{"version": 3, "file": "primeng-inplace.mjs", "sources": ["../../src/app/components/inplace/inplace.ts", "../../src/app/components/inplace/primeng-inplace.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewEncapsulation, booleanAttribute } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { TimesIcon } from 'primeng/icons/times';\n\n@Component({\n    selector: 'p-inplaceDisplay',\n    template: '<ng-content></ng-content>',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class InplaceDisplay {}\n\n@Component({\n    selector: 'p-inplaceContent',\n    template: '<ng-content></ng-content>',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class InplaceContent {}\n/**\n * Inplace provides an easy to do editing and display at the same time where clicking the output displays the actual content.\n * @group Components\n */\n@Component({\n    selector: 'p-inplace',\n    template: `\n        <div [ngClass]=\"{ 'p-inplace p-component': true, 'p-inplace-closable': closable }\" [ngStyle]=\"style\" [class]=\"styleClass\" [attr.aria-live]=\"'polite'\">\n            <div class=\"p-inplace-display\" (click)=\"onActivateClick($event)\" tabindex=\"0\" role=\"button\" (keydown)=\"onKeydown($event)\" [ngClass]=\"{ 'p-disabled': disabled }\" *ngIf=\"!active\">\n                <ng-content select=\"[pInplaceDisplay]\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"displayTemplate\"></ng-container>\n            </div>\n            <div class=\"p-inplace-content\" *ngIf=\"active\">\n                <ng-content select=\"[pInplaceContent]\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n\n                <ng-container *ngIf=\"closable\">\n                    <button *ngIf=\"closeIcon\" type=\"button\" [icon]=\"closeIcon\" pButton (click)=\"onDeactivateClick($event)\" [attr.aria-label]=\"closeAriaLabel\"></button>\n                    <button *ngIf=\"!closeIcon\" type=\"button\" pButton [ngClass]=\"'p-button-icon-only'\" (click)=\"onDeactivateClick($event)\" [attr.aria-label]=\"closeAriaLabel\">\n                        <TimesIcon *ngIf=\"!closeIconTemplate\" />\n                        <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                    </button>\n                </ng-container>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./inplace.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Inplace implements AfterContentInit {\n    /**\n     * Whether the content is displayed or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) active: boolean | undefined = false;\n    /**\n     * Displays a button to switch back to display mode.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) closable: boolean | undefined = false;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined = false;\n    /**\n     * Allows to prevent clicking.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) preventClick: boolean | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Icon to display in the close button.\n     * @group Props\n     */\n    @Input() closeIcon: string | undefined;\n    /**\n     * Establishes a string value that labels the close button.\n     * @group Props\n     */\n    @Input() closeAriaLabel: string | undefined;\n    /**\n     * Callback to invoke when inplace is opened.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onActivate: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when inplace is closed.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onDeactivate: EventEmitter<Event> = new EventEmitter<Event>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    hover!: boolean;\n\n    displayTemplate: TemplateRef<any> | undefined;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    closeIconTemplate: TemplateRef<any> | undefined;\n\n    constructor(public cd: ChangeDetectorRef) {}\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'display':\n                    this.displayTemplate = item.template;\n                    break;\n\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    onActivateClick(event: MouseEvent) {\n        if (!this.preventClick) this.activate(event);\n    }\n\n    onDeactivateClick(event: MouseEvent) {\n        if (!this.preventClick) this.deactivate(event);\n    }\n    /**\n     * Activates the content.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    activate(event?: Event) {\n        if (!this.disabled) {\n            this.active = true;\n            this.onActivate.emit(event);\n            this.cd.markForCheck();\n        }\n    }\n    /**\n     * Deactivates the content.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    deactivate(event?: Event) {\n        if (!this.disabled) {\n            this.active = false;\n            this.hover = false;\n            this.onDeactivate.emit(event);\n            this.cd.markForCheck();\n        }\n    }\n\n    onKeydown(event: KeyboardEvent) {\n        if (event.code === 'Enter') {\n            this.activate(event);\n            event.preventDefault();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, ButtonModule, SharedModule, TimesIcon],\n    exports: [Inplace, InplaceDisplay, InplaceContent, ButtonModule, SharedModule],\n    declarations: [Inplace, InplaceDisplay, InplaceContent]\n})\nexport class InplaceModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;MAaa,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,+FALb,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;2FAK5B,cAAc,EAAA,UAAA,EAAA,CAAA;kBAP1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;MAUY,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,+FALb,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;2FAK5B,cAAc,EAAA,UAAA,EAAA,CAAA;kBAP1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;AAED;;;AAGG;MA8BU,OAAO,CAAA;AAgEG,IAAA,EAAA,CAAA;AA/DnB;;;AAGG;IACqC,MAAM,GAAwB,KAAK,CAAC;AAC5E;;;AAGG;IACqC,QAAQ,GAAwB,KAAK,CAAC;AAC9E;;;AAGG;IACqC,QAAQ,GAAwB,KAAK,CAAC;AAC9E;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;;AAIG;AACO,IAAA,UAAU,GAAwB,IAAI,YAAY,EAAS,CAAC;AACtE;;;;AAIG;AACO,IAAA,YAAY,GAAwB,IAAI,YAAY,EAAS,CAAC;AAExC,IAAA,SAAS,CAAuC;AAEhF,IAAA,KAAK,CAAW;AAEhB,IAAA,eAAe,CAA+B;AAE9C,IAAA,eAAe,CAA+B;AAE9C,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,WAAA,CAAmB,EAAqB,EAAA;QAArB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;IAE5C,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,eAAe,CAAC,KAAiB,EAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,YAAY;AAAE,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KAChD;AAED,IAAA,iBAAiB,CAAC,KAAiB,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,YAAY;AAAE,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAClD;AACD;;;;AAIG;AACH,IAAA,QAAQ,CAAC,KAAa,EAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;KACJ;AACD;;;;AAIG;AACH,IAAA,UAAU,CAAC,KAAa,EAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC1B,QAAA,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;AACxB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;uGA1HQ,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAP,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAKI,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAKhB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAkCnB,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAjFpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;AAmBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,kSAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAsImD,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA9HpD,OAAO,EAAA,UAAA,EAAA,CAAA;kBA7BnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;AAmBT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,kSAAA,CAAA,EAAA,CAAA;sFAOuC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMI,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA4ErB,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAb,aAAa,EAAA,YAAA,EAAA,CAlIb,OAAO,EA3CP,cAAc,EASd,cAAc,CAAA,EAAA,OAAA,EAAA,CAgKb,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,CA9HpD,EAAA,OAAA,EAAA,CAAA,OAAO,EA3CP,cAAc,EASd,cAAc,EAiK4B,YAAY,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGpE,aAAa,EAAA,OAAA,EAAA,CAJZ,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EACV,YAAY,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGpE,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,CAAC;oBAC9D,OAAO,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC;AAC9E,oBAAA,YAAY,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,cAAc,CAAC;AAC1D,iBAAA,CAAA;;;ACzLD;;AAEG;;;;"}