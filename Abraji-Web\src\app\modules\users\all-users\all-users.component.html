<section class="bg-gray-50 dark:bg-gray-900 p-3">
  <div *ngIf="isLoading">
    <app-table-skeleton></app-table-skeleton>
  </div>
  <div appFlowbiteInit *ngIf="!isLoading" class="mx-auto max-w-screen-xl">
    <!-- Start coding here -->
    <div class="bg-white dark:bg-gray-800 relative shadow-md overflow-x-hidden sm:rounded-lg">
      <div class="flex items-center m-4 mb-0">
        <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
          width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
            d="M4.5 17H4a1 1 0 0 1-1-1 3 3 0 0 1 3-3h1m0-3.05A2.5 2.5 0 1 1 9 5.5M19.5 17h.5a1 1 0 0 0 1-1 3 3 0 0 0-3-3h-1m0-3.05a2.5 2.5 0 1 0-2-4.45m.5 13.5h-7a1 1 0 0 1-1-1 3 3 0 0 1 3-3h3a3 3 0 0 1 3 3 1 1 0 0 1-1 1Zm-1-9.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Z" />
        </svg>
        <label for="">
          {{'table.foundRecord' | transloco}} {{tableResponse.total}}</label>
      </div>
      <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
        <div class="w-full md:w-1/2">
          <div class="flex items-center">
            <label for="simple-search" class="sr-only">{{'table.search' | transloco}}</label>
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 flex items-center p-3 pointer-events-none">
                <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor"
                  viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <input type="text" id="simple-search"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full ps-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                placeholder="{{'table.search' | transloco}}..." required="" [value]="userForm.search"
                (change)="searchChange($event)">
            </div>
          </div>
        </div>
        <div
          class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
          <div class="flex items-center space-x-3 w-full md:w-auto">
            <button id="actionsDropdownButton" data-dropdown-toggle="actionsDropdown"
              class="w-full mx-2 md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              type="button">
              {{'table.actions' | transloco}}
              <svg class="-me-1 ms-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path clip-rule="evenodd" fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
              </svg>
            </button>
            <div id="actionsDropdown"
              class="hidden z-30 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
              <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="actionsDropdownButton">
                <li>
                  <a routerLink="/users/create" routerLinkActive="/users/create"
                    class="flex items-center justify-start py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 12h14m-7 7V5" />
                    </svg>
                    {{ 'user.table.newAccount'| transloco }}
                  </a>
                </li>
                <li *ngIf="topSelectedUserId">
                  <!--  -->
                  <a [routerLink]="['/users/', topSelectedUserId ,'edit']"
                    class="flex items-center justify-start py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z" />
                    </svg>
                    {{ 'user.table.edit'| transloco }}
                  </a>
                </li>
                <li *ngIf="topSelectedUserId">
                  <a [routerLink]="['/users/activate/', topSelectedUserId]"
                    class="flex items-center justify-start py-2 px-4 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 16.881V7.119a1 1 0 0 1 1.636-.772l5.927 4.881a1 1 0 0 1 0 1.544l-5.927 4.88A1 1 0 0 1 8 16.882Z" />
                    </svg>
                    {{ 'user.table.activate'| transloco }}
                  </a>
                </li>
                <li *ngIf="topSelectedUserId">
                  <a href="#"
                    class="flex items-center justify-start py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path fill="currentColor"
                        d="M4 9.05H3v2h1v-2Zm16 2h1v-2h-1v2ZM10 14a1 1 0 1 0 0 2v-2Zm4 2a1 1 0 1 0 0-2v2Zm-3 1a1 1 0 1 0 2 0h-2Zm2-4a1 1 0 1 0-2 0h2Zm-2-5.95a1 1 0 1 0 2 0h-2Zm2-3a1 1 0 1 0-2 0h2Zm-7 3a1 1 0 0 0 2 0H6Zm2-3a1 1 0 1 0-2 0h2Zm8 3a1 1 0 1 0 2 0h-2Zm2-3a1 1 0 1 0-2 0h2Zm-13 3h14v-2H5v2Zm14 0v12h2v-12h-2Zm0 12H5v2h14v-2Zm-14 0v-12H3v12h2Zm0 0H3a2 2 0 0 0 2 2v-2Zm14 0v2a2 2 0 0 0 2-2h-2Zm0-12h2a2 2 0 0 0-2-2v2Zm-14-2a2 2 0 0 0-2 2h2v-2Zm-1 6h16v-2H4v2ZM10 16h4v-2h-4v2Zm3 1v-4h-2v4h2Zm0-9.95v-3h-2v3h2Zm-5 0v-3H6v3h2Zm10 0v-3h-2v3h2Z" />
                    </svg>

                    {{ 'user.table.extendQuota'| transloco }}
                  </a>
                </li>
                <li *ngIf="topSelectedUserId">
                  <a [routerLink]="['/users/profile/', topSelectedUserId]"
                    class="flex items-center justify-start py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="square" stroke-linejoin="round" stroke-width="2"
                        d="M7 19H5a1 1 0 0 1-1-1v-1a3 3 0 0 1 3-3h1m4-6a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm7.441 1.559a1.907 1.907 0 0 1 0 2.698l-6.069 6.069L10 19l.674-3.372 6.07-6.07a1.907 1.907 0 0 1 2.697 0Z" />
                    </svg>
                    {{ 'user.table.changeQuota'| transloco }}
                  </a>
                </li>
                <li *ngIf="topSelectedUserId">
                  <a [routerLink]="['/users/', topSelectedUserId ,'debts']"
                    class="flex items-center justify-start py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                        d="M8 7V6a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1M3 18v-7a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" />
                    </svg>
                    {{ 'user.table.payDebt'| transloco }}
                  </a>
                </li>
                <li *ngIf="!(exportIsLoading || tableResponse.total === 0)">
                  <a (click)="exportToExcel()"
                    class="cursor-pointer flex items-center justify-start py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-5 h-5 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 10V4a1 1 0 0 0-1-1H9.914a1 1 0 0 0-.707.293L5.293 7.207A1 1 0 0 0 5 7.914V20a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2M10 3v4a1 1 0 0 1-1 1H5m5 6h9m0 0-2-2m2 2-2 2" />
                    </svg>
                    {{ 'user.table.export'| transloco }}
                  </a>
                </li>
              </ul>
            </div>
            <button id="coulmnsDropdownButton" data-dropdown-toggle="coulmnsDropdown"
              class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              type="button">
              <svg class="h-4 w-4 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 5v14M9 5v14M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z" />
              </svg>

              {{'table.columns' | transloco}}
              <svg class="-me-1 ms-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path clip-rule="evenodd" fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
              </svg>
            </button>
            <div id="coulmnsDropdown"
              class="z-10 hidden w-48 h-64 overflow-y-scroll p-3 bg-white rounded-lg shadow dark:bg-gray-700">
              <h6 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">{{'table.showColumns' | transloco}}
              </h6>
              <ul class="space-y-2 text-sm" aria-labelledby="filterDropdownButton">
                <li *ngFor="let column of userColumnsState" class="flex items-center">
                  <input title="select" type="checkbox" [id]="column.key" [checked]="!column.hidden"
                    (change)="toggleColumnSelection(column.key)"
                    class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                  <label [for]="column.key" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ ('user.overview.' + column.key) | transloco | titlecase }}
                  </label>
                </li>
              </ul>
            </div>
            <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" (click)="filterOpened()"
              class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              type="button">
              <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 me-2 text-gray-400"
                viewbox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                  d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                  clip-rule="evenodd" />
              </svg>
              {{'table.filter' | transloco}}
              <svg class="-me-1 ms-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path clip-rule="evenodd" fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
              </svg>
            </button>
            <div id="filterDropdown"
              class="z-10 hidden w-60 h-64 overflow-y-scroll p-3 bg-white rounded-lg shadow-2xl dark:bg-gray-700">
              <h6 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">{{'table.advancedFilter' | transloco}}
              </h6>
              <div class="max-w-sm mx-auto">
                <div>
                  <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                    'user.table.filter.status' | transloco }}</label>
                  <select id="status" (change)="onStatusChange($event)"
                    class="block w-full p-2 mb-6 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option [selected]="userForm.status == -1" value="-1">{{'common.all' | transloco}}</option>
                    <option [selected]="userForm.status == 1" value="1">{{ 'user.table.filter.active' | transloco }}
                    </option>
                    <option [selected]="userForm.status == 2" value="2">{{ 'user.table.filter.expired' | transloco }}
                    </option>
                    <option [selected]="userForm.status == 3" value="3">{{ 'user.table.filter.disabled' | transloco }}
                    </option>
                    <option [selected]="userForm.status == 4" value="4">{{ 'user.table.filter.expiringSoon' | transloco
                      }}</option>
                    <option [selected]="userForm.status == 5" value="5">{{ 'user.table.filter.expiringToday' | transloco
                      }}</option>
                  </select>
                </div>
                <div>
                  <label for="connection" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                    'user.table.filter.connection' | transloco }}</label>
                  <select id="connection" (change)="onConnectionChange($event)"
                    class="block w-full p-2 mb-6 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option [selected]="userForm.connection == -1" value="-1">{{'common.all' | transloco}}</option>
                    <option [selected]="userForm.connection == 1" value="1">{{ 'user.table.filter.offline' | transloco
                      }}</option>
                    <option [selected]="userForm.connection == 2" value="2">{{ 'user.table.filter.online' | transloco }}
                    </option>
                  </select>
                </div>
                <div>
                  <label for="profile" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                    'user.table.filter.quota' | transloco }}</label>
                  <select id="profile" (change)="onQuotaChange($event)"
                    class="block w-full p-2 mb-6 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option [selected]="userForm.profile_id == -1" value="-1">{{'common.all' | transloco}}</option>
                    <ng-container *ngFor="let quota of quotas">
                      <option [selected]="userForm.profile_id === quota.id" value="{{quota.id}}">{{quota.name}}</option>
                    </ng-container>
                  </select>
                </div>
                <div>
                  <label for="parent_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                    {{'user.overview.parent_username' | transloco }}</label>
                  <select id="parent_id" (change)="onManagerChange($event)"
                    class="block w-full p-2 mb-6 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option [selected]="userForm.parent_id == -1" value="-1">{{'common.all' | transloco}}</option>
                    <ng-container *ngFor="let manager of managers">
                      <option [selected]="userForm.parent_id === manager.id" value="{{manager.id}}">{{manager.username}}</option>
                    </ng-container>
                  </select>
                </div>
                <!-- <div>
                  <label for="parent"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">parent</label>
                  <select id="parent"
                    class="block w-full p-2 mb-6 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option selected value="-1">{{'common.all' | transloco}}</option>
                    <option value="0">loop</option>
                  </select>
                </div> -->

                <!-- <div>
                  <label for="Group" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Group</label>
                  <select id="Group"
                    class="block w-full p-2 mb-6 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option selected value="-1">{{'common.all' | transloco}}</option>
                    <option value="0">loop</option>
                  </select>
                </div> -->
                <div class="my-2">
                  <label class="inline-flex items-center mb-5 cursor-pointer">
                    <input type="checkbox" (change)="onSubUsersChange($event)" [checked]="userForm.sub_users"
                      class="sr-only peer">
                    <div
                      class="relative w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
                    </div>
                    <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">{{
                      'user.table.filter.subUser' | transloco }}</span>
                  </label>
                </div>
                <button type="button" (click)="fetchUsers()"
                  class="w-full px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                  {{ 'common.search' | transloco }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="overflow-x-auto relative">
        <!-- data table -->
        <table class="w-full text-sm text-start text-gray-500 dark:text-gray-400">
          <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
              <th scope="col" class="px-4 py-3 flex justify-center items-center whitespace-nowrap">
                <div class="flex items-center">
                  <input title="select" type="checkbox" (change)="selectAll($event)"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                </div>
              </th>
              <th scope="col" class="px-2 py-3">#</th>
              <ng-container *ngFor="let column of userColumnsState">
                <th scope="col" class="px-4 py-3 whitespace-nowrap" *ngIf="!column.hidden">
                  <!-- provide sorting column -->
                  <div class="cursor-pointer flex" (click)="sortByColumn(column.key)">
                    {{ ('user.overview.' + column.key) | transloco | titlecase }}
                    <svg title="sort" _ngcontent-ng-c3067077598="" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                      fill="currentColor" viewBox="0 0 24 24" class="w-3 h-3 ms-1.5">
                      <path _ngcontent-ng-c3067077598=""
                        d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z">
                      </path>
                    </svg>
                  </div>
                </th>
              </ng-container>
              <th scope="col" class="px-4 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of tableResponse.data; let i = index" class="border-b dark:border-gray-700">
              <td class="px-4 py-3">
                <div class="flex items-center">
                  <input title="select" type="checkbox" [checked]="(selectedUsers.indexOf(user.id) !== -1)"
                    (change)="toggleSelection(user.id)"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                </div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">{{ tableResponse.from + i }}</td>
              <ng-container *ngFor="let column of userColumnsState">
                <td class="px-4 py-3 whitespace-nowrap" *ngIf="!column.hidden">
                  <ng-container *ngIf="column.key !== 'status'; else statusColumn">
                    <ng-container *ngIf="column.key === 'username'; else defaultColumn">
                      <a [routerLink]="getPropertyValue(user, column.key).link"
                        [ngClass]="{'text-red-500': !getPropertyValue(user, column.key).isEnabled, 'text-blue-500': getPropertyValue(user, column.key).isEnabled}">
                        {{ getPropertyValue(user, column.key).value }}
                      </a>
                    </ng-container>
                    <ng-template #defaultColumn>
                      <!-- Handle different keys specifically -->
                      <ng-container *ngIf="column.key === 'acctstarttime'">
                        {{ getPropertyValue(user, column.key) | timeAgo }}
                      </ng-container>
                      <ng-container *ngIf="column.key === 'acctinputoctets' || column.key === 'acctoutputoctets'">
                        {{ getPropertyValue(user, column.key) | bytesToSize }}
                      </ng-container>
                      <ng-container *ngIf="column.key === 'daily_traffic'">
                        {{ getPropertyValue(user, column.key) | bytesToSize }}
                      </ng-container>
                      <ng-container *ngIf="column.key === 'framedipaddress'">
                        <a [href]="'http://' + getPropertyValue(user, column.key)" target="_blank"
                          class="text-blue-500 ">
                          {{ getPropertyValue(user, column.key) }}
                        </a>
                      </ng-container>
                      <ng-container
                        *ngIf="column.key !== 'acctstarttime' && column.key !== 'acctinputoctets' && column.key !== 'acctoutputoctets' && column.key !== 'daily_traffic' && column.key !== 'framedipaddress'">
                        {{ getPropertyValue(user, column.key) }}
                      </ng-container>
                    </ng-template>
                  </ng-container>
                  <ng-template #statusColumn>
                    <ng-container *ngIf="!user.status.expiration; else expiredStatus">
                      <span [ngClass]="getStatusClass('Expired')">{{ 'user.table.expired' | transloco }}</span>
                    </ng-container>
                    <ng-template #expiredStatus>
                      <span [ngClass]="getStatusClass('Active')">{{ 'user.table.active' | transloco }}</span>
                    </ng-template>
                  </ng-template>
                </td>
              </ng-container>
            </tr>
          </tbody>
        </table>
      </div>
      <nav class="flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4"
        aria-label="Table navigation">
        <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
          {{'table.showing' | transloco}}
          <span class="font-semibold text-gray-900 dark:text-white">{{(tableResponse.from != null? tableResponse.from
            :'0') + '-' + (tableResponse.to != null? tableResponse.to : '0')}}</span>
          {{'table.of' | transloco}}
          <span class="font-semibold text-gray-900 dark:text-white">{{tableResponse.total}}</span>
        </span>
        <!-- Pagination controls -->
        <ul dir="ltr" class="inline-flex items-stretch -space-x-px">
          <li>
            <button (click)="changePage((tableResponse.current_page - 1).toString())"
              [disabled]="tableResponse.current_page === 1"
              class="flex items-center justify-center h-full py-1.5 px-3 ms-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
              <span class="sr-only">Previous</span>
              <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>
            </button>
          </li>
          <ng-container *ngFor="let page of getPagesToDisplay()">
            <li *ngIf="page === '...'">
              <span
                class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400">...</span>
            </li>
            <li *ngIf="page !== '...'">
              <button (click)="changePage(page)" [class.bg-primary-50]="tableResponse.current_page === page"
                [class.text-primary-600]="tableResponse.current_page === page"
                [class.z-10]="tableResponse.current_page === page"
                class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                {{ page }}
              </button>
            </li>
          </ng-container>
          <li>
            <button (click)="changePage((tableResponse.current_page + 1).toString())"
              [disabled]="tableResponse.current_page === tableResponse.last_page"
              class="flex items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
              <span class="sr-only">Next</span>
              <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M7.293 14.707a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 00-1.414 1.414L10.586 10l-3.293 3.293a1 1 0 000 1.414z"
                  clip-rule="evenodd" />
              </svg>

            </button>
          </li>
        </ul>


      </nav>
    </div>
  </div>
</section>
