{"version": 3, "file": "primeng-multiselect.mjs", "sources": ["../../src/app/components/multiselect/multiselect.ts", "../../src/app/components/multiselect/primeng-multiselect.ts"], "sourcesContent": ["import { AnimationEvent } from '@angular/animations';\nimport { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewChecked,\n    AfterViewInit,\n    booleanAttribute,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    computed,\n    ContentChild,\n    ContentChildren,\n    effect,\n    ElementRef,\n    EventEmitter,\n    forwardRef,\n    Input,\n    NgModule,\n    NgZone,\n    numberAttribute,\n    OnInit,\n    Output,\n    QueryList,\n    Renderer2,\n    Signal,\n    signal,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { FilterService, Footer, Header, OverlayOptions, OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule, TranslationKeys } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { Overlay, OverlayModule } from 'primeng/overlay';\nimport { RippleModule } from 'primeng/ripple';\nimport { <PERSON><PERSON><PERSON>, ScrollerModule } from 'primeng/scroller';\nimport { ScrollerOptions } from 'primeng/api';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { SearchIcon } from 'primeng/icons/search';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { MultiSelectRemoveEvent, MultiSelectFilterOptions, MultiSelectFilterEvent, MultiSelectBlurEvent, MultiSelectChangeEvent, MultiSelectFocusEvent, MultiSelectLazyLoadEvent, MultiSelectSelectAllChangeEvent } from './multiselect.interface';\nimport { MinusIcon } from 'primeng/icons/minus';\n\nexport const MULTISELECT_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MultiSelect),\n    multi: true\n};\n\n@Component({\n    selector: 'p-multiSelectItem',\n    template: `\n        <li\n            pRipple\n            role=\"option\"\n            [ngStyle]=\"{ height: itemSize + 'px' }\"\n            class=\"p-multiselect-item\"\n            [ngClass]=\"{ 'p-multiselect-item': true, 'p-disabled': disabled, 'p-focus': focused }\"\n            [id]=\"id\"\n            [attr.aria-label]=\"label\"\n            [attr.aria-setsize]=\"ariaSetSize\"\n            [attr.aria-posinset]=\"ariaPosInset\"\n            [attr.aria-selected]=\"selected\"\n            [attr.data-p-focused]=\"focused\"\n            [attr.data-p-highlight]=\"selected\"\n            [attr.data-p-disabled]=\"disabled\"\n            [attr.aria-checked]=\"selected\"\n            (click)=\"onOptionClick($event)\"\n            (mouseenter)=\"onOptionMouseEnter($event)\"\n        >\n            <div class=\"p-checkbox p-component\" [ngClass]=\"{ 'p-variant-filled': config.inputStyle() === 'filled' }\">\n                <div class=\"p-checkbox-box\" [ngClass]=\"{ 'p-highlight': selected }\">\n                    <ng-container *ngIf=\"selected\">\n                        <CheckIcon *ngIf=\"!checkIconTemplate && !itemCheckboxIconTemplate\" [styleClass]=\"'p-checkbox-icon'\" [attr.aria-hidden]=\"true\" />\n                        <span *ngIf=\"checkIconTemplate\" class=\"p-checkbox-icon\" [attr.aria-hidden]=\"true\">\n                            <ng-template *ngTemplateOutlet=\"checkIconTemplate\"></ng-template>\n                        </span>\n                        <span *ngIf=\"itemCheckboxIconTemplate\" class=\"p-checkbox-icon\" [attr.aria-hidden]=\"true\">\n                            <ng-template *ngTemplateOutlet=\"itemCheckboxIconTemplate; context: { $implicit: selected }\"></ng-template>\n                        </span>\n                    </ng-container>\n                </div>\n            </div>\n            <span *ngIf=\"!template\">{{ label ?? 'empty' }}</span>\n            <ng-container *ngTemplateOutlet=\"template; context: { $implicit: option }\"></ng-container>\n        </li>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class MultiSelectItem {\n    @Input() id: string | undefined;\n\n    @Input() option: any;\n\n    @Input({ transform: booleanAttribute }) selected: boolean | undefined;\n\n    @Input() label: string | undefined;\n\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n\n    @Input({ transform: numberAttribute }) itemSize: number | undefined;\n\n    @Input({ transform: booleanAttribute }) focused: boolean | undefined;\n\n    @Input() ariaPosInset: string | undefined;\n\n    @Input() ariaSetSize: string | undefined;\n\n    @Input() template: TemplateRef<any> | undefined;\n\n    @Input() checkIconTemplate: TemplateRef<any> | undefined;\n\n    @Input() itemCheckboxIconTemplate: TemplateRef<any> | undefined;\n\n    @Output() onClick: EventEmitter<any> = new EventEmitter();\n\n    @Output() onMouseEnter: EventEmitter<any> = new EventEmitter();\n\n    constructor(public config: PrimeNGConfig) {}\n\n    onOptionClick(event: Event) {\n        this.onClick.emit({\n            originalEvent: event,\n            option: this.option,\n            selected: this.selected\n        });\n        event.stopPropagation();\n    }\n\n    onOptionMouseEnter(event: Event) {\n        this.onMouseEnter.emit({\n            originalEvent: event,\n            option: this.option,\n            selected: this.selected\n        });\n    }\n}\n/**\n * MultiSelect is used to select multiple items from a collection.\n * @group Components\n */\n@Component({\n    selector: 'p-multiSelect',\n    template: `\n        <div #container [attr.id]=\"id\" [ngClass]=\"containerClass\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onContainerClick($event)\">\n            <div class=\"p-hidden-accessible\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #focusInput\n                    [pTooltip]=\"tooltip\"\n                    [tooltipPosition]=\"tooltipPosition\"\n                    [positionStyle]=\"tooltipPositionStyle\"\n                    [tooltipStyleClass]=\"tooltipStyleClass\"\n                    [attr.aria-disabled]=\"disabled\"\n                    [attr.id]=\"inputId\"\n                    role=\"combobox\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-haspopup]=\"'listbox'\"\n                    [attr.aria-expanded]=\"overlayVisible ?? false\"\n                    [attr.aria-controls]=\"overlayVisible ? id + '_list' : null\"\n                    [attr.tabindex]=\"!disabled ? tabindex : -1\"\n                    [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                    (focus)=\"onInputFocus($event)\"\n                    (blur)=\"onInputBlur($event)\"\n                    (keydown)=\"onKeyDown($event)\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                    [attr.value]=\"label() || 'empty'\"\n                />\n            </div>\n            <div\n                class=\"p-multiselect-label-container\"\n                [pTooltip]=\"tooltip\"\n                (mouseleave)=\"labelContainerMouseLeave()\"\n                [tooltipDisabled]=\"_disableTooltip\"\n                [tooltipPosition]=\"tooltipPosition\"\n                [positionStyle]=\"tooltipPositionStyle\"\n                [tooltipStyleClass]=\"tooltipStyleClass\"\n            >\n                <div [ngClass]=\"labelClass\">\n                    <ng-container *ngIf=\"!selectedItemsTemplate\">\n                        <ng-container *ngIf=\"display === 'comma'\">{{ label() || 'empty' }}</ng-container>\n                        <ng-container *ngIf=\"display === 'chip'\">\n                            <div #token *ngFor=\"let item of chipSelectedItems(); let i = index\" class=\"p-multiselect-token\">\n                                <span class=\"p-multiselect-token-label\">{{ getLabelByValue(item) }}</span>\n                                <ng-container *ngIf=\"!disabled\">\n                                    <TimesCircleIcon\n                                        *ngIf=\"!removeTokenIconTemplate\"\n                                        [ngClass]=\"{ 'p-disabled': isOptionDisabled(item) }\"\n                                        [styleClass]=\"'p-multiselect-token-icon'\"\n                                        (click)=\"removeOption(item, event)\"\n                                        [attr.data-pc-section]=\"'clearicon'\"\n                                        [attr.aria-hidden]=\"true\"\n                                    />\n                                    <span *ngIf=\"removeTokenIconTemplate\" class=\"p-multiselect-token-icon\" (click)=\"removeOption(item, event)\" [attr.data-pc-section]=\"'clearicon'\" [attr.aria-hidden]=\"true\">\n                                        <ng-container *ngTemplateOutlet=\"removeTokenIconTemplate\"></ng-container>\n                                    </span>\n                                </ng-container>\n                            </div>\n                            <ng-container *ngIf=\"!modelValue() || modelValue().length === 0\">{{ placeholder() || defaultLabel || 'empty' }}</ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-container *ngTemplateOutlet=\"selectedItemsTemplate; context: { $implicit: selectedOptions, removeChip: removeOption.bind(this) }\"></ng-container>\n                </div>\n                <ng-container *ngIf=\"isVisibleClearIcon\">\n                    <TimesIcon *ngIf=\"!clearIconTemplate\" [styleClass]=\"'p-multiselect-clear-icon'\" (click)=\"clear($event)\" [attr.data-pc-section]=\"'clearicon'\" [attr.aria-hidden]=\"true\" />\n                    <span *ngIf=\"clearIconTemplate\" class=\"p-multiselect-clear-icon\" (click)=\"clear($event)\" [attr.data-pc-section]=\"'clearicon'\" [attr.aria-hidden]=\"true\">\n                        <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                    </span>\n                </ng-container>\n            </div>\n            <div class=\"p-multiselect-trigger\">\n                <ng-container *ngIf=\"loading; else elseBlock\">\n                    <ng-container *ngIf=\"loadingIconTemplate\">\n                        <ng-container *ngTemplateOutlet=\"loadingIconTemplate\"></ng-container>\n                    </ng-container>\n                    <ng-container *ngIf=\"!loadingIconTemplate\">\n                        <span *ngIf=\"loadingIcon\" [ngClass]=\"'p-multiselect-trigger-icon pi-spin ' + loadingIcon\" aria-hidden=\"true\"></span>\n                        <span *ngIf=\"!loadingIcon\" [class]=\"'p-multiselect-trigger-icon pi pi-spinner pi-spin'\" aria-hidden=\"true\"></span>\n                    </ng-container>\n                </ng-container>\n                <ng-template #elseBlock>\n                    <ng-container *ngIf=\"!dropdownIconTemplate\">\n                        <span *ngIf=\"dropdownIcon\" class=\"p-multiselect-trigger-icon\" [ngClass]=\"dropdownIcon\" [attr.data-pc-section]=\"'triggericon'\" [attr.aria-hidden]=\"true\"></span>\n                        <ChevronDownIcon *ngIf=\"!dropdownIcon\" [styleClass]=\"'p-multiselect-trigger-icon'\" [attr.data-pc-section]=\"'triggericon'\" [attr.aria-hidden]=\"true\" />\n                    </ng-container>\n                    <span *ngIf=\"dropdownIconTemplate\" class=\"p-multiselect-trigger-icon\" [attr.data-pc-section]=\"'triggericon'\" [attr.aria-hidden]=\"true\">\n                        <ng-template *ngTemplateOutlet=\"dropdownIconTemplate\"></ng-template>\n                    </span>\n                </ng-template>\n            </div>\n            <p-overlay\n                #overlay\n                [(visible)]=\"overlayVisible\"\n                [options]=\"overlayOptions\"\n                [target]=\"'@parent'\"\n                [appendTo]=\"appendTo\"\n                [autoZIndex]=\"autoZIndex\"\n                [baseZIndex]=\"baseZIndex\"\n                [showTransitionOptions]=\"showTransitionOptions\"\n                [hideTransitionOptions]=\"hideTransitionOptions\"\n                (onAnimationStart)=\"onOverlayAnimationStart($event)\"\n                (onHide)=\"hide()\"\n            >\n                <ng-template pTemplate=\"content\">\n                    <div [attr.id]=\"id + '_list'\" [ngClass]=\"'p-multiselect-panel p-component'\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\">\n                        <span\n                            #firstHiddenFocusableEl\n                            role=\"presentation\"\n                            class=\"p-hidden-accessible p-hidden-focusable\"\n                            [attr.tabindex]=\"0\"\n                            (focus)=\"onFirstHiddenFocus($event)\"\n                            [attr.data-p-hidden-accessible]=\"true\"\n                            [attr.data-p-hidden-focusable]=\"true\"\n                        >\n                        </span>\n                        <div class=\"p-multiselect-header\" *ngIf=\"showHeader\">\n                            <ng-content select=\"p-header\"></ng-content>\n                            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                            <ng-container *ngIf=\"filterTemplate; else builtInFilterElement\">\n                                <ng-container *ngTemplateOutlet=\"filterTemplate; context: { options: filterOptions }\"></ng-container>\n                            </ng-container>\n                            <ng-template #builtInFilterElement>\n                                <div\n                                    class=\"p-checkbox p-component\"\n                                    *ngIf=\"isSelectionAllDisabled()\"\n                                    [ngClass]=\"{ 'p-variant-filled': variant === 'filled' || config.inputStyle() === 'filled', 'p-checkbox-disabled': disabled || toggleAllDisabled }\"\n                                    (click)=\"onToggleAll($event)\"\n                                    (keydown)=\"onHeaderCheckboxKeyDown($event)\"\n                                >\n                                    <div class=\"p-hidden-accessible\" [attr.data-p-hidden-accessible]=\"true\">\n                                        <input\n                                            #headerCheckbox\n                                            type=\"checkbox\"\n                                            [readonly]=\"readonly\"\n                                            [attr.checked]=\"allSelected()\"\n                                            (focus)=\"onHeaderCheckboxFocus()\"\n                                            (blur)=\"onHeaderCheckboxBlur()\"\n                                            [disabled]=\"disabled || toggleAllDisabled\"\n                                            [attr.aria-label]=\"toggleAllAriaLabel\"\n                                        />\n                                    </div>\n                                    <div\n                                        class=\"p-checkbox-box\"\n                                        role=\"checkbox\"\n                                        [attr.aria-label]=\"toggleAllAriaLabel\"\n                                        [attr.aria-checked]=\"allSelected()\"\n                                        [ngClass]=\"{ 'p-highlight': allSelected(), 'p-focus': headerCheckboxFocus, 'p-disabled': disabled || toggleAllDisabled }\"\n                                    >\n                                        <ng-container *ngIf=\"allSelected() || partialSelected()\">\n                                            <ng-container *ngIf=\"!checkIconTemplate && !headerCheckboxIconTemplate\">\n                                                <CheckIcon [styleClass]=\"'p-checkbox-icon'\" *ngIf=\"allSelected()\" [attr.aria-hidden]=\"true\" />\n                                            </ng-container>\n\n                                            <span *ngIf=\"checkIconTemplate\" class=\"p-checkbox-icon\" [attr.aria-hidden]=\"true\">\n                                                <ng-template *ngTemplateOutlet=\"checkIconTemplate; context: { $implicit: allSelected() }\"></ng-template>\n                                            </span>\n                                            <span *ngIf=\"headerCheckboxIconTemplate\" class=\"p-checkbox-icon\" [attr.aria-hidden]=\"true\">\n                                                <ng-template *ngTemplateOutlet=\"headerCheckboxIconTemplate; context: { $implicit: allSelected(), partialSelected: partialSelected() }\"></ng-template>\n                                            </span>\n                                        </ng-container>\n                                    </div>\n                                </div>\n                                <div class=\"p-multiselect-filter-container\" *ngIf=\"filter\">\n                                    <input\n                                        #filterInput\n                                        type=\"text\"\n                                        role=\"searchbox\"\n                                        [attr.autocomplete]=\"autocomplete\"\n                                        [attr.placeholder]=\"filterPlaceHolder\"\n                                        role=\"searchbox\"\n                                        [attr.aria-owns]=\"id + '_list'\"\n                                        [attr.aria-activedescendant]=\"focusedOptionId\"\n                                        [value]=\"_filterValue() || ''\"\n                                        (input)=\"onFilterInputChange($event)\"\n                                        (keydown)=\"onFilterKeyDown($event)\"\n                                        (click)=\"onInputClick($event)\"\n                                        (blur)=\"onFilterBlur($event)\"\n                                        class=\"p-multiselect-filter p-inputtext p-component\"\n                                        [disabled]=\"disabled\"\n                                        [attr.placeholder]=\"filterPlaceHolder\"\n                                        [attr.aria-label]=\"ariaFilterLabel\"\n                                    />\n                                    <SearchIcon [styleClass]=\"'p-multiselect-filter-icon'\" *ngIf=\"!filterIconTemplate\" />\n                                    <span *ngIf=\"filterIconTemplate\" class=\"p-multiselect-filter-icon\">\n                                        <ng-template *ngTemplateOutlet=\"filterIconTemplate\"></ng-template>\n                                    </span>\n                                </div>\n\n                                <button class=\"p-multiselect-close p-link p-button-icon-only\" type=\"button\" (click)=\"close($event)\" pRipple [attr.aria-label]=\"closeAriaLabel\">\n                                    <TimesIcon [styleClass]=\"'p-multiselect-close-icon'\" *ngIf=\"!closeIconTemplate\" />\n                                    <span *ngIf=\"closeIconTemplate\" class=\"p-multiselect-close-icon\">\n                                        <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                                    </span>\n                                </button>\n                            </ng-template>\n                        </div>\n                        <div class=\"p-multiselect-items-wrapper\" [ngStyle]=\"{ 'max-height': virtualScroll ? 'auto' : scrollHeight || 'auto' }\">\n                            <p-scroller\n                                *ngIf=\"virtualScroll\"\n                                #scroller\n                                [items]=\"visibleOptions()\"\n                                [style]=\"{ height: scrollHeight }\"\n                                [itemSize]=\"virtualScrollItemSize || _itemSize\"\n                                [autoSize]=\"true\"\n                                [tabindex]=\"-1\"\n                                [lazy]=\"lazy\"\n                                (onLazyLoad)=\"onLazyLoad.emit($event)\"\n                                [options]=\"virtualScrollOptions\"\n                            >\n                                <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                                    <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: items, options: scrollerOptions }\"></ng-container>\n                                </ng-template>\n                                <ng-container *ngIf=\"loaderTemplate\">\n                                    <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                                        <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: scrollerOptions }\"></ng-container>\n                                    </ng-template>\n                                </ng-container>\n                            </p-scroller>\n                            <ng-container *ngIf=\"!virtualScroll\">\n                                <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: visibleOptions(), options: {} }\"></ng-container>\n                            </ng-container>\n\n                            <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                                <ul #items class=\"p-multiselect-items p-component\" [ngClass]=\"scrollerOptions.contentStyleClass\" [ngStyle]=\"scrollerOptions.contentStyle\" role=\"listbox\" aria-multiselectable=\"true\" [attr.aria-label]=\"listLabel\">\n                                    <ng-template ngFor let-option [ngForOf]=\"items\" let-i=\"index\">\n                                        <ng-container *ngIf=\"isOptionGroup(option)\">\n                                            <li [attr.id]=\"id + '_' + getOptionIndex(i, scrollerOptions)\" class=\"p-multiselect-item-group\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                                <span *ngIf=\"!groupTemplate\">{{ getOptionGroupLabel(option.optionGroup) }}</span>\n                                                <ng-container *ngTemplateOutlet=\"groupTemplate; context: { $implicit: option.optionGroup }\"></ng-container>\n                                            </li>\n                                        </ng-container>\n                                        <ng-container *ngIf=\"!isOptionGroup(option)\">\n                                            <p-multiSelectItem\n                                                [id]=\"id + '_' + getOptionIndex(i, scrollerOptions)\"\n                                                [option]=\"option\"\n                                                [selected]=\"isSelected(option)\"\n                                                [label]=\"getOptionLabel(option)\"\n                                                [disabled]=\"isOptionDisabled(option)\"\n                                                [template]=\"itemTemplate\"\n                                                [checkIconTemplate]=\"checkIconTemplate\"\n                                                [itemCheckboxIconTemplate]=\"itemCheckboxIconTemplate\"\n                                                [itemSize]=\"scrollerOptions.itemSize\"\n                                                [focused]=\"focusedOptionIndex() === getOptionIndex(i, scrollerOptions)\"\n                                                [ariaPosInset]=\"getAriaPosInset(getOptionIndex(i, scrollerOptions))\"\n                                                [ariaSetSize]=\"ariaSetSize\"\n                                                (onClick)=\"onOptionSelect($event, false, getOptionIndex(i, scrollerOptions))\"\n                                                (onMouseEnter)=\"onOptionMouseEnter($event, getOptionIndex(i, scrollerOptions))\"\n                                            ></p-multiSelectItem>\n                                        </ng-container>\n                                    </ng-template>\n\n                                    <li *ngIf=\"hasFilter() && isEmpty()\" class=\"p-multiselect-empty-message\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                        <ng-container *ngIf=\"!emptyFilterTemplate && !emptyTemplate; else emptyFilter\">\n                                            {{ emptyFilterMessageLabel }}\n                                        </ng-container>\n                                        <ng-container #emptyFilter *ngTemplateOutlet=\"emptyFilterTemplate || emptyTemplate\"></ng-container>\n                                    </li>\n                                    <li *ngIf=\"!hasFilter() && isEmpty()\" class=\"p-multiselect-empty-message\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                        <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                            {{ emptyMessageLabel }}\n                                        </ng-container>\n                                        <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                                    </li>\n                                </ul>\n                            </ng-template>\n                        </div>\n                        <div class=\"p-multiselect-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                            <ng-content select=\"p-footer\"></ng-content>\n                            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n                        </div>\n\n                        <span\n                            #lastHiddenFocusableEl\n                            role=\"presentation\"\n                            class=\"p-hidden-accessible p-hidden-focusable\"\n                            [attr.tabindex]=\"0\"\n                            (focus)=\"onLastHiddenFocus($event)\"\n                            [attr.data-p-hidden-accessible]=\"true\"\n                            [attr.data-p-hidden-focusable]=\"true\"\n                        ></span>\n                    </div>\n                </ng-template>\n            </p-overlay>\n        </div>\n    `,\n    host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-focus]': 'focused || overlayVisible',\n        '[class.p-inputwrapper-filled]': 'filled'\n    },\n    providers: [MULTISELECT_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./multiselect.css']\n})\nexport class MultiSelect implements OnInit, AfterViewInit, AfterContentInit, AfterViewChecked, ControlValueAccessor {\n    /**\n     * Unique identifier of the component\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the overlay panel.\n     * @group Props\n     */\n    @Input() panelStyle: any;\n    /**\n     * Style class of the overlay panel element.\n     * @group Props\n     */\n    @Input() panelStyleClass: string | undefined;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean | undefined;\n    /**\n     * Whether to display options as grouped when nested options are provided.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) group: boolean | undefined;\n    /**\n     * When specified, displays an input field to filter the items on keyup.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) filter: boolean = true;\n    /**\n     * Defines placeholder of the filter input.\n     * @group Props\n     */\n    @Input() filterPlaceHolder: string | undefined;\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() filterLocale: string | undefined;\n    /**\n     * Specifies the visibility of the options panel.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) overlayVisible: boolean | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined = 0;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    @Input() dataKey: string | undefined;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Whether to show labels of selected item labels or use default label.\n     * @group Props\n     * @defaultValue true\n     */\n    @Input() set displaySelectedLabel(val: boolean) {\n        this._displaySelectedLabel = val;\n    }\n    get displaySelectedLabel(): boolean {\n        return this._displaySelectedLabel;\n    }\n    /**\n     * Decides how many selected item labels to show at most.\n     * @group Props\n     * @defaultValue 3\n     */\n    @Input() set maxSelectedLabels(val: number | null | undefined) {\n        this._maxSelectedLabels = val;\n    }\n    get maxSelectedLabels(): number | null | undefined {\n        return this._maxSelectedLabels;\n    }\n    /**\n     * Decides how many selected item labels to show at most.\n     * @group Props\n     */\n    @Input({ transform: (value: unknown) => numberAttribute(value, null) }) selectionLimit: number | null | undefined;\n    /**\n     * Label to display after exceeding max selected labels e.g. ({0} items selected), defaults \"ellipsis\" keyword to indicate a text-overflow.\n     * @group Props\n     */\n    @Input() selectedItemsLabel: string | undefined;\n    /**\n     * Whether to show the checkbox at header to toggle all items at once.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showToggleAll: boolean = true;\n    /**\n     * Text to display when filtering does not return any results.\n     * @group Props\n     */\n    @Input() emptyFilterMessage: string = '';\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    @Input() emptyMessage: string = '';\n    /**\n     * Clears the filter value when hiding the dropdown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) resetFilterOnHide: boolean = false;\n    /**\n     * Icon class of the dropdown icon.\n     * @group Props\n     */\n    @Input() dropdownIcon: string | undefined;\n    /**\n     * Name of the label field of an option.\n     * @group Props\n     */\n    @Input() optionLabel: string | undefined;\n    /**\n     * Name of the value field of an option.\n     * @group Props\n     */\n    @Input() optionValue: string | undefined;\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    @Input() optionDisabled: string | undefined;\n    /**\n     * Name of the label field of an option group.\n     * @group Props\n     */\n    @Input() optionGroupLabel: string | undefined = 'label';\n    /**\n     * Name of the options field of an option group.\n     * @group Props\n     */\n    @Input() optionGroupChildren: string = 'items';\n    /**\n     * Whether to show the header.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showHeader: boolean = true;\n    /**\n     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n     * @group Props\n     */\n    @Input() filterBy: string | undefined;\n    /**\n     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    @Input() scrollHeight: string = '200px';\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazy: boolean = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) virtualScroll: boolean | undefined;\n    /**\n     * Whether the multiselect is in loading state.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) loading: boolean | undefined = false;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) virtualScrollItemSize: number | undefined;\n    /**\n     * Icon to display in loading state.\n     * @group Props\n     */\n    @Input() loadingIcon: string | undefined;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() virtualScrollOptions: ScrollerOptions | undefined;\n    /**\n     * Whether to use overlay API feature. The properties of overlay API can be used like an object in it.\n     * @group Props\n     */\n    @Input() overlayOptions: OverlayOptions | undefined;\n    /**\n     * Defines a string that labels the filter input.\n     * @group Props\n     */\n    @Input() ariaFilterLabel: string | undefined;\n    /**\n     * Defines how the items are filtered.\n     * @group Props\n     */\n    @Input() filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte' = 'contains';\n    /**\n     * Advisory information to display in a tooltip on hover.\n     * @group Props\n     */\n    @Input() tooltip: string = '';\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    @Input() tooltipPosition: 'top' | 'left' | 'right' | 'bottom' = 'right';\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    @Input() tooltipPositionStyle: string = 'absolute';\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    @Input() tooltipStyleClass: string | undefined;\n    /**\n     * Applies focus to the filter element when the overlay is shown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocusFilter: boolean = true;\n    /**\n     * Defines how the selected items are displayed.\n     * @group Props\n     */\n    @Input() display: string | 'comma' | 'chip' = 'comma';\n    /**\n     * Defines the autocomplete is active.\n     * @group Props\n     */\n    @Input() autocomplete: string = 'off';\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean = false;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input() get autoZIndex(): boolean | undefined {\n        return this._autoZIndex;\n    }\n    set autoZIndex(val: boolean | undefined) {\n        this._autoZIndex = val;\n        console.warn('The autoZIndex property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input() get baseZIndex(): number | undefined {\n        return this._baseZIndex;\n    }\n    set baseZIndex(val: number | undefined) {\n        this._baseZIndex = val;\n        console.warn('The baseZIndex property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    @Input() get showTransitionOptions(): string | undefined {\n        return this._showTransitionOptions;\n    }\n    set showTransitionOptions(val: string | undefined) {\n        this._showTransitionOptions = val;\n        console.warn('The showTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    @Input() get hideTransitionOptions(): string | undefined {\n        return this._hideTransitionOptions;\n    }\n    set hideTransitionOptions(val: string | undefined) {\n        this._hideTransitionOptions = val;\n        console.warn('The hideTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Label to display when there are no selections.\n     * @group Props\n     * @deprecated Use placeholder instead.\n     */\n    @Input() set defaultLabel(val: string | undefined) {\n        this._defaultLabel = val;\n        console.warn('defaultLabel property is deprecated since 16.6.0, use placeholder instead');\n    }\n    get defaultLabel(): string | undefined {\n        return this._defaultLabel;\n    }\n    /**\n     * Label to display when there are no selections.\n     * @group Props\n     */\n    @Input() set placeholder(val: string | undefined) {\n        this._placeholder.set(val);\n    }\n    get placeholder(): Signal<string | undefined> {\n        return this._placeholder.asReadonly();\n    }\n    /**\n     * An array of objects to display as the available options.\n     * @group Props\n     */\n    @Input() get options(): any[] | undefined {\n        const options = this._options();\n        return options;\n    }\n    set options(val: any[] | undefined) {\n        if (!ObjectUtils.deepEquals(this._options(), val)) {\n            this._options.set(val);\n        }\n    }\n    /**\n     * When specified, filter displays with this value.\n     * @group Props\n     */\n    @Input() get filterValue(): string | undefined | null {\n        return this._filterValue();\n    }\n    set filterValue(val: string | undefined | null) {\n        this._filterValue.set(val);\n    }\n    /**\n     * Item size of item to be virtual scrolled.\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    @Input() get itemSize(): number | undefined {\n        return this._itemSize;\n    }\n    set itemSize(val: number | undefined) {\n        this._itemSize = val;\n        console.warn('The itemSize property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    /**\n     * Whether all data is selected.\n     * @group Props\n     */\n    @Input() get selectAll(): boolean | undefined | null {\n        return this._selectAll;\n    }\n    set selectAll(value: boolean | undefined | null) {\n        this._selectAll = value;\n    }\n    /**\n     * Indicates whether to focus on options when hovering over them, defaults to optionLabel.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) focusOnHover: boolean = false;\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    @Input() filterFields: any[] | undefined;\n    /**\n     * Determines if the option will be selected on focus.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) selectOnFocus: boolean = false;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoOptionFocus: boolean = true;\n    /**\n     * Callback to invoke when value changes.\n     * @param {MultiSelectChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<MultiSelectChangeEvent> = new EventEmitter<MultiSelectChangeEvent>();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {MultiSelectFilterEvent} event - Custom filter event.\n     * @group Emits\n     */\n    @Output() onFilter: EventEmitter<MultiSelectFilterEvent> = new EventEmitter<MultiSelectFilterEvent>();\n    /**\n     * Callback to invoke when multiselect receives focus.\n     * @param {MultiSelectFocusEvent} event - Custom focus event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<MultiSelectFocusEvent> = new EventEmitter<MultiSelectFocusEvent>();\n    /**\n     * Callback to invoke when multiselect loses focus.\n     * @param {MultiSelectBlurEvent} event - Custom blur event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<MultiSelectBlurEvent> = new EventEmitter<MultiSelectBlurEvent>();\n    /**\n     * Callback to invoke when component is clicked.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onClick: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when input field is cleared.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<void> = new EventEmitter<void>();\n    /**\n     * Callback to invoke when overlay panel becomes visible.\n     * @group Emits\n     */\n    @Output() onPanelShow: EventEmitter<void> = new EventEmitter<void>();\n    /**\n     * Callback to invoke when overlay panel becomes hidden.\n     * @group Emits\n     */\n    @Output() onPanelHide: EventEmitter<void> = new EventEmitter<void>();\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {MultiSelectLazyLoadEvent} event - Lazy load event.\n     * @group Emits\n     */\n    @Output() onLazyLoad: EventEmitter<MultiSelectLazyLoadEvent> = new EventEmitter<MultiSelectLazyLoadEvent>();\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {MultiSelectRemoveEvent} event - Remove event.\n     * @group Emits\n     */\n    @Output() onRemove: EventEmitter<MultiSelectRemoveEvent> = new EventEmitter<MultiSelectRemoveEvent>();\n    /**\n     * Callback to invoke when all data is selected.\n     * @param {MultiSelectSelectAllChangeEvent} event - Custom select event.\n     * @group Emits\n     */\n    @Output() onSelectAllChange: EventEmitter<MultiSelectSelectAllChangeEvent> = new EventEmitter<MultiSelectSelectAllChangeEvent>();\n\n    @ViewChild('container') containerViewChild: Nullable<ElementRef>;\n\n    @ViewChild('overlay') overlayViewChild: Nullable<Overlay>;\n\n    @ViewChild('filterInput') filterInputChild: Nullable<ElementRef>;\n\n    @ViewChild('focusInput') focusInputViewChild: Nullable<ElementRef>;\n\n    @ViewChild('items') itemsViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scroller') scroller: Nullable<Scroller>;\n\n    @ViewChild('lastHiddenFocusableEl') lastHiddenFocusableElementOnOverlay: Nullable<ElementRef>;\n\n    @ViewChild('firstHiddenFocusableEl') firstHiddenFocusableElementOnOverlay: Nullable<ElementRef>;\n\n    @ViewChild('headerCheckbox') headerCheckboxViewChild: Nullable<ElementRef>;\n\n    @ContentChild(Footer) footerFacet: any;\n\n    @ContentChild(Header) headerFacet: any;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    searchValue: Nullable<string>;\n\n    searchTimeout: any;\n\n    _selectAll: boolean | undefined | null = null;\n\n    _autoZIndex: boolean | undefined;\n\n    _baseZIndex: number | undefined;\n\n    _showTransitionOptions: string | undefined;\n\n    _hideTransitionOptions: string | undefined;\n\n    _defaultLabel: string | undefined;\n\n    _placeholder = signal<string | undefined>(undefined);\n\n    _itemSize: number | undefined;\n\n    _selectionLimit: number | undefined;\n\n    _disableTooltip = false;\n\n    value: any[];\n\n    public _filteredOptions: any[] | undefined | null;\n\n    public onModelChange: Function = () => {};\n\n    public onModelTouched: Function = () => {};\n\n    public valuesAsString: string | undefined;\n\n    public focus: boolean | undefined;\n\n    public filtered: boolean | undefined;\n\n    public itemTemplate: TemplateRef<any> | undefined;\n\n    public groupTemplate: TemplateRef<any> | undefined;\n\n    public loaderTemplate: TemplateRef<any> | undefined;\n\n    public headerTemplate: TemplateRef<any> | undefined;\n\n    public filterTemplate: TemplateRef<any> | undefined;\n\n    public footerTemplate: TemplateRef<any> | undefined;\n\n    public emptyFilterTemplate: TemplateRef<any> | undefined;\n\n    public emptyTemplate: TemplateRef<any> | undefined;\n\n    public selectedItemsTemplate: TemplateRef<any> | undefined;\n\n    checkIconTemplate: TemplateRef<any> | undefined;\n\n    loadingIconTemplate: TemplateRef<any> | undefined;\n\n    filterIconTemplate: TemplateRef<any> | undefined;\n\n    removeTokenIconTemplate: TemplateRef<any> | undefined;\n\n    closeIconTemplate: TemplateRef<any> | undefined;\n\n    clearIconTemplate: TemplateRef<any> | undefined;\n\n    dropdownIconTemplate: TemplateRef<any> | undefined;\n\n    itemCheckboxIconTemplate: TemplateRef<any> | undefined;\n\n    headerCheckboxIconTemplate: TemplateRef<any> | undefined;\n\n    public headerCheckboxFocus: boolean | undefined;\n\n    filterOptions: MultiSelectFilterOptions | undefined;\n\n    preventModelTouched: boolean | undefined;\n\n    preventDocumentDefault: boolean | undefined;\n\n    focused: boolean = false;\n\n    itemsWrapper: any;\n\n    _displaySelectedLabel: boolean = true;\n\n    _maxSelectedLabels: number = 3;\n\n    modelValue = signal<any>(null);\n\n    _filterValue = signal<any>(null);\n\n    _options = signal<any[]>(null);\n\n    startRangeIndex = signal<number>(-1);\n\n    focusedOptionIndex = signal<number>(-1);\n\n    selectedOptions: any;\n\n    clickInProgress: boolean = false;\n\n    get containerClass() {\n        return {\n            'p-multiselect p-component p-inputwrapper': true,\n            'p-disabled': this.disabled,\n            'p-multiselect-clearable': this.showClear && !this.disabled,\n            'p-multiselect-chip': this.display === 'chip',\n            'p-focus': this.focused,\n            'p-variant-filled': this.variant === 'filled' || this.config.inputStyle() === 'filled'\n        };\n    }\n\n    get inputClass() {\n        return {\n            'p-multiselect-label p-inputtext': true,\n            'p-placeholder': (this.placeholder() || this.defaultLabel) && (this.label() === this.placeholder() || this.label() === this.defaultLabel),\n            'p-multiselect-label-empty': !this.selectedItemsTemplate && (this.label() === 'p-emptylabel' || this.label().length === 0)\n        };\n    }\n\n    get panelClass() {\n        return {\n            'p-multiselect-panel p-component': true,\n            'p-input-filled': this.config.inputStyle() === 'filled',\n            'p-ripple-disabled': this.config.ripple === false\n        };\n    }\n\n    get labelClass() {\n        return {\n            'p-multiselect-label': true,\n            'p-placeholder': this.label() === this.placeholder() || this.label() === this.defaultLabel,\n            'p-multiselect-label-empty': !this.placeholder() && !this.defaultLabel && (!this.modelValue() || this.modelValue().length === 0)\n        };\n    }\n\n    get emptyMessageLabel(): string {\n        return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n\n    get emptyFilterMessageLabel(): string {\n        return this.emptyFilterMessage || this.config.getTranslation(TranslationKeys.EMPTY_FILTER_MESSAGE);\n    }\n\n    get filled(): boolean {\n        if (typeof this.modelValue() === 'string') return !!this.modelValue();\n\n        return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n\n    get isVisibleClearIcon(): boolean | undefined {\n        return this.modelValue() != null && this.modelValue() !== '' && ObjectUtils.isNotEmpty(this.modelValue()) && this.showClear && !this.disabled && this.filled;\n    }\n\n    get toggleAllAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria[this.allSelected() ? 'selectAll' : 'unselectAll'] : undefined;\n    }\n\n    get closeAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n    }\n\n    get listLabel(): string {\n        return this.config.getTranslation(TranslationKeys.ARIA)['listLabel'];\n    }\n\n    private getAllVisibleAndNonVisibleOptions() {\n        return this.group ? this.flatOptions(this.options) : this.options || [];\n    }\n\n    visibleOptions = computed(() => {\n        const options = this.getAllVisibleAndNonVisibleOptions();\n        const isArrayOfObjects = ObjectUtils.isArray(options) && ObjectUtils.isObject(options[0]);\n\n        if (this._filterValue()) {\n            let filteredOptions;\n\n            if (isArrayOfObjects) {\n                filteredOptions = this.filterService.filter(options, this.searchFields(), this._filterValue(), this.filterMatchMode, this.filterLocale);\n            } else {\n                filteredOptions = options.filter((option) => option.toString().toLocaleLowerCase().includes(this._filterValue().toLocaleLowerCase()));\n            }\n\n            if (this.group) {\n                const optionGroups = this.options || [];\n                const filtered = [];\n\n                optionGroups.forEach((group) => {\n                    const groupChildren = this.getOptionGroupChildren(group);\n                    const filteredItems = groupChildren.filter((item) => filteredOptions.includes(item));\n\n                    if (filteredItems.length > 0) filtered.push({ ...group, [typeof this.optionGroupChildren === 'string' ? this.optionGroupChildren : 'items']: [...filteredItems] });\n                });\n\n                return this.flatOptions(filtered);\n            }\n\n            return filteredOptions;\n        }\n        return options;\n    });\n\n    label = computed(() => {\n        let label;\n        const modelValue = this.modelValue();\n\n        if (modelValue && modelValue.length && this.displaySelectedLabel) {\n            if (ObjectUtils.isNotEmpty(this.maxSelectedLabels) && modelValue.length > this.maxSelectedLabels) {\n                return this.getSelectedItemsLabel();\n            } else {\n                label = '';\n\n                for (let i = 0; i < modelValue.length; i++) {\n                    if (i !== 0) {\n                        label += ', ';\n                    }\n\n                    label += this.getLabelByValue(modelValue[i]);\n                }\n            }\n        } else {\n            label = this.placeholder() || this.defaultLabel || '';\n        }\n        return label;\n    });\n\n    chipSelectedItems = computed(() => {\n        return ObjectUtils.isNotEmpty(this.maxSelectedLabels) && this.modelValue() && this.modelValue().length > this.maxSelectedLabels ? this.modelValue().slice(0, this.maxSelectedLabels) : this.modelValue();\n    });\n\n    constructor(public el: ElementRef, public renderer: Renderer2, public cd: ChangeDetectorRef, public zone: NgZone, public filterService: FilterService, public config: PrimeNGConfig, public overlayService: OverlayService) {\n        effect(() => {\n            const modelValue = this.modelValue();\n\n            const visibleOptions = this.visibleOptions();\n            if (visibleOptions && ObjectUtils.isNotEmpty(visibleOptions)) {\n                if (this.optionValue && this.optionLabel && modelValue) {\n                    this.selectedOptions = visibleOptions.filter((option) => modelValue.includes(option[this.optionLabel]) || modelValue.includes(option[this.optionValue]));\n                } else {\n                    this.selectedOptions = modelValue;\n                }\n                this.cd.markForCheck();\n            }\n        });\n    }\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n        this.autoUpdateModel();\n\n        if (this.filterBy) {\n            this.filterOptions = {\n                filter: (value) => this.onFilterInputChange(value),\n                reset: () => this.resetFilter()\n            };\n        }\n    }\n\n    maxSelectionLimitReached() {\n        return ObjectUtils.isNotEmpty(this.selectionLimit) && this.modelValue() && this.modelValue().length === this.selectionLimit;\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'group':\n                    this.groupTemplate = item.template;\n                    break;\n\n                case 'selectedItems':\n                    this.selectedItemsTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'filter':\n                    this.filterTemplate = item.template;\n                    break;\n\n                case 'emptyfilter':\n                    this.emptyFilterTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n\n                case 'checkicon':\n                    this.checkIconTemplate = item.template;\n                    console.warn('checkicon is deprecated and will removed in v18. Use itemcheckboxicon or headercheckboxicon templates instead.');\n                    break;\n\n                case 'headercheckboxicon':\n                    this.headerCheckboxIconTemplate = item.template;\n                    break;\n\n                case 'loadingicon':\n                    this.loadingIconTemplate = item.template;\n                    break;\n\n                case 'filtericon':\n                    this.filterIconTemplate = item.template;\n                    break;\n\n                case 'removetokenicon':\n                    this.removeTokenIconTemplate = item.template;\n                    break;\n\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n\n                case 'dropdownicon':\n                    this.dropdownIconTemplate = item.template;\n                    break;\n\n                case 'itemcheckboxicon':\n                    this.itemCheckboxIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngAfterViewInit() {\n        if (this.overlayVisible) {\n            this.show();\n        }\n    }\n\n    ngAfterViewChecked() {\n        if (this.filtered) {\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    this.overlayViewChild?.alignOverlay();\n                }, 1);\n            });\n            this.filtered = false;\n        }\n    }\n\n    flatOptions(options) {\n        return (options || []).reduce((result, option, index) => {\n            result.push({ optionGroup: option, group: true, index });\n\n            const optionGroupChildren = this.getOptionGroupChildren(option);\n\n            optionGroupChildren && optionGroupChildren.forEach((o) => result.push(o));\n\n            return result;\n        }, []);\n    }\n\n    autoUpdateModel() {\n        if (this.selectOnFocus && this.autoOptionFocus && !this.hasSelectedOption()) {\n            this.focusedOptionIndex.set(this.findFirstFocusedOptionIndex());\n            const value = this.getOptionValue(this.visibleOptions()[this.focusedOptionIndex()]);\n            this.onOptionSelect({ originalEvent: null, option: [value] });\n        }\n    }\n\n    /**\n     * Updates the model value.\n     * @group Method\n     */\n    public updateModel(value, event?) {\n        this.value = value;\n        this.onModelChange(value);\n        this.modelValue.set(value);\n    }\n\n    onInputClick(event) {\n        event.stopPropagation();\n        event.preventDefault();\n        this.focusedOptionIndex.set(-1);\n    }\n\n    onOptionSelect(event, isFocus = false, index = -1) {\n        const { originalEvent, option } = event;\n        if (this.disabled || this.isOptionDisabled(option)) {\n            return;\n        }\n\n        let selected = this.isSelected(option);\n        let value = null;\n        if (selected) {\n            value = this.modelValue().filter((val) => !ObjectUtils.equals(val, this.getOptionValue(option), this.equalityKey()));\n            this.onRemove.emit({ newValue: this.value, removed: this.getOptionValue(option) });\n        } else {\n            value = [...(this.modelValue() || []), this.getOptionValue(option)];\n        }\n\n        this.updateModel(value, originalEvent);\n        index !== -1 && this.focusedOptionIndex.set(index);\n\n        isFocus && DomHandler.focus(this.focusInputViewChild?.nativeElement);\n\n        this.onChange.emit({\n            originalEvent: { ...event, selected: !event.selected },\n            value: value,\n            itemValue: option\n        });\n    }\n\n    findSelectedOptionIndex() {\n        return this.hasSelectedOption() ? this.visibleOptions().findIndex((option) => this.isValidSelectedOption(option)) : -1;\n    }\n\n    onOptionSelectRange(event, start = -1, end = -1) {\n        start === -1 && (start = this.findNearestSelectedOptionIndex(end, true));\n        end === -1 && (end = this.findNearestSelectedOptionIndex(start));\n\n        if (start !== -1 && end !== -1) {\n            const rangeStart = Math.min(start, end);\n            const rangeEnd = Math.max(start, end);\n            const value = this.visibleOptions()\n                .slice(rangeStart, rangeEnd + 1)\n                .filter((option) => this.isValidOption(option))\n                .map((option) => this.getOptionValue(option));\n\n            this.updateModel(value, event);\n        }\n    }\n\n    searchFields() {\n        return (this.filterBy || this.optionLabel || 'label').split(',');\n    }\n\n    findNearestSelectedOptionIndex(index, firstCheckUp = false) {\n        let matchedOptionIndex = -1;\n\n        if (this.hasSelectedOption()) {\n            if (firstCheckUp) {\n                matchedOptionIndex = this.findPrevSelectedOptionIndex(index);\n                matchedOptionIndex = matchedOptionIndex === -1 ? this.findNextSelectedOptionIndex(index) : matchedOptionIndex;\n            } else {\n                matchedOptionIndex = this.findNextSelectedOptionIndex(index);\n                matchedOptionIndex = matchedOptionIndex === -1 ? this.findPrevSelectedOptionIndex(index) : matchedOptionIndex;\n            }\n        }\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n\n    findPrevSelectedOptionIndex(index) {\n        const matchedOptionIndex = this.hasSelectedOption() && index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), (option) => this.isValidSelectedOption(option)) : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : -1;\n    }\n\n    findFirstFocusedOptionIndex() {\n        const selectedIndex = this.findFirstSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n\n    findFirstOptionIndex() {\n        return this.visibleOptions().findIndex((option) => this.isValidOption(option));\n    }\n\n    findFirstSelectedOptionIndex() {\n        return this.hasSelectedOption() ? this.visibleOptions().findIndex((option) => this.isValidSelectedOption(option)) : -1;\n    }\n\n    findNextSelectedOptionIndex(index) {\n        const matchedOptionIndex =\n            this.hasSelectedOption() && index < this.visibleOptions().length - 1\n                ? this.visibleOptions()\n                      .slice(index + 1)\n                      .findIndex((option) => this.isValidSelectedOption(option))\n                : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : -1;\n    }\n\n    equalityKey() {\n        return this.optionValue ? null : this.dataKey;\n    }\n\n    hasSelectedOption() {\n        return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n\n    isSelectionAllDisabled() {\n        return this.showToggleAll && ObjectUtils.isEmpty(this.selectionLimit);\n    }\n\n    isValidSelectedOption(option) {\n        return this.isValidOption(option) && this.isSelected(option);\n    }\n\n    isOptionGroup(option) {\n        return (this.group || this.optionGroupLabel) && option.optionGroup && option.group;\n    }\n\n    isValidOption(option) {\n        return option && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n    }\n\n    isOptionDisabled(option: any) {\n        if (this.maxSelectionLimitReached() && !this.isSelected(option)) {\n            return true;\n        }\n        return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : option && option.disabled !== undefined ? option.disabled : false;\n    }\n\n    isSelected(option) {\n        const optionValue = this.getOptionValue(option);\n        return (this.modelValue() || []).some((value) => ObjectUtils.equals(value, optionValue, this.equalityKey()));\n    }\n\n    isOptionMatched(option) {\n        return this.isValidOption(option) && this.getOptionLabel(option).toString().toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale));\n    }\n\n    isEmpty() {\n        return !this._options() || (this.visibleOptions() && this.visibleOptions().length === 0);\n    }\n\n    getOptionIndex(index, scrollerOptions) {\n        return this.virtualScrollerDisabled ? index : scrollerOptions && scrollerOptions.getItemOptions(index)['index'];\n    }\n\n    getAriaPosInset(index) {\n        return (\n            (this.optionGroupLabel\n                ? index -\n                  this.visibleOptions()\n                      .slice(0, index)\n                      .filter((option) => this.isOptionGroup(option)).length\n                : index) + 1\n        );\n    }\n\n    get ariaSetSize() {\n        return this.visibleOptions().filter((option) => !this.isOptionGroup(option)).length;\n    }\n\n    getLabelByValue(value) {\n        const options = this.group ? this.flatOptions(this._options()) : this._options() || [];\n        const matchedOption = options.find((option) => !this.isOptionGroup(option) && ObjectUtils.equals(this.getOptionValue(option), value, this.equalityKey()));\n        return matchedOption ? this.getOptionLabel(matchedOption) : null;\n    }\n\n    getSelectedItemsLabel() {\n        let pattern = /{(.*?)}/;\n        let message = this.selectedItemsLabel ? this.selectedItemsLabel : this.config.getTranslation(TranslationKeys.SELECTION_MESSAGE);\n\n        if (pattern.test(message)) {\n            return message.replace(message.match(pattern)[0], this.modelValue().length + '');\n        }\n\n        return message;\n    }\n\n    getOptionLabel(option: any) {\n        return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option && option.label != undefined ? option.label : option;\n    }\n\n    getOptionValue(option: any) {\n        return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : !this.optionLabel && option && option.value !== undefined ? option.value : option;\n    }\n\n    getOptionGroupLabel(optionGroup: any) {\n        return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label != undefined ? optionGroup.label : optionGroup;\n    }\n\n    getOptionGroupChildren(optionGroup: any) {\n        return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        if (this.disabled) {\n            event.preventDefault();\n            return;\n        }\n\n        const metaKey = event.metaKey || event.ctrlKey;\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'PageDown':\n                this.onPageDownKey(event);\n                break;\n\n            case 'PageUp':\n                this.onPageUpKey(event);\n                break;\n\n            case 'Enter':\n            case 'Space':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                this.onShiftKey();\n                break;\n\n            default:\n                if (event.code === 'KeyA' && metaKey) {\n                    const value = this.visibleOptions()\n                        .filter((option) => this.isValidOption(option))\n                        .map((option) => this.getOptionValue(option));\n\n                    this.updateModel(value, event);\n\n                    event.preventDefault();\n                    break;\n                }\n\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    !this.overlayVisible && this.show();\n                    this.searchOptions(event, event.key);\n                    event.preventDefault();\n                }\n\n                break;\n        }\n    }\n\n    onFilterKeyDown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event, true);\n                break;\n\n            case 'ArrowLeft':\n            case 'ArrowRight':\n                this.onArrowLeftKey(event, true);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event, true);\n                break;\n\n            case 'End':\n                this.onEndKey(event, true);\n                break;\n\n            case 'Enter':\n            case 'NumpadEnter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event, true);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onArrowLeftKey(event: KeyboardEvent, pressedInInputText: boolean = false) {\n        pressedInInputText && this.focusedOptionIndex.set(-1);\n    }\n\n    onArrowDownKey(event) {\n        const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.findFirstFocusedOptionIndex();\n\n        if (event.shiftKey) {\n            this.onOptionSelectRange(event, this.startRangeIndex(), optionIndex);\n        }\n\n        this.changeFocusedOptionIndex(event, optionIndex);\n        !this.overlayVisible && this.show();\n        event.preventDefault();\n        event.stopPropagation();\n    }\n\n    onArrowUpKey(event, pressedInInputText = false) {\n        if (event.altKey && !pressedInInputText) {\n            if (this.focusedOptionIndex() !== -1) {\n                this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n            }\n\n            this.overlayVisible && this.hide();\n            event.preventDefault();\n        } else {\n            const optionIndex = this.focusedOptionIndex() !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex()) : this.findLastFocusedOptionIndex();\n\n            if (event.shiftKey) {\n                this.onOptionSelectRange(event, optionIndex, this.startRangeIndex());\n            }\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n\n            !this.overlayVisible && this.show();\n            event.preventDefault();\n        }\n        event.stopPropagation();\n    }\n\n    onHomeKey(event, pressedInInputText = false) {\n        const { currentTarget } = event;\n\n        if (pressedInInputText) {\n            const len = currentTarget.value.length;\n\n            currentTarget.setSelectionRange(0, event.shiftKey ? len : 0);\n            this.focusedOptionIndex.set(-1);\n        } else {\n            let metaKey = event.metaKey || event.ctrlKey;\n            let optionIndex = this.findFirstOptionIndex();\n\n            if (event.shiftKey && metaKey) {\n                this.onOptionSelectRange(event, optionIndex, this.startRangeIndex());\n            }\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n\n            !this.overlayVisible && this.show();\n        }\n\n        event.preventDefault();\n    }\n\n    onEndKey(event, pressedInInputText = false) {\n        const { currentTarget } = event;\n\n        if (pressedInInputText) {\n            const len = currentTarget.value.length;\n            currentTarget.setSelectionRange(event.shiftKey ? 0 : len, len);\n            this.focusedOptionIndex.set(-1);\n        } else {\n            let metaKey = event.metaKey || event.ctrlKey;\n            let optionIndex = this.findLastFocusedOptionIndex();\n\n            if (event.shiftKey && metaKey) {\n                this.onOptionSelectRange(event, this.startRangeIndex(), optionIndex);\n            }\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n\n            !this.overlayVisible && this.show();\n        }\n\n        event.preventDefault();\n    }\n\n    onPageDownKey(event) {\n        this.scrollInView(this.visibleOptions().length - 1);\n        event.preventDefault();\n    }\n\n    onPageUpKey(event) {\n        this.scrollInView(0);\n        event.preventDefault();\n    }\n\n    onEnterKey(event) {\n        if (!this.overlayVisible) {\n            this.onArrowDownKey(event);\n        } else {\n            if (this.focusedOptionIndex() !== -1) {\n                if (event.shiftKey) {\n                    this.onOptionSelectRange(event, this.focusedOptionIndex());\n                } else {\n                    this.onOptionSelect({ originalEvent: event, option: this.visibleOptions()[this.focusedOptionIndex()] });\n                }\n            }\n        }\n\n        event.preventDefault();\n    }\n\n    onEscapeKey(event) {\n        this.overlayVisible && this.hide(true);\n        event.preventDefault();\n    }\n\n    onDeleteKey(event: KeyboardEvent) {\n        if (this.showClear) {\n            this.clear(event);\n            event.preventDefault();\n        }\n    }\n\n    onTabKey(event, pressedInInputText = false) {\n        if (!pressedInInputText) {\n            if (this.overlayVisible && this.hasFocusableElements()) {\n                DomHandler.focus(event.shiftKey ? this.lastHiddenFocusableElementOnOverlay.nativeElement : this.firstHiddenFocusableElementOnOverlay.nativeElement);\n\n                event.preventDefault();\n            } else {\n                if (this.focusedOptionIndex() !== -1) {\n                    this.onOptionSelect({ originalEvent: event, option: this.visibleOptions()[this.focusedOptionIndex()] });\n                }\n\n                this.overlayVisible && this.hide(this.filter);\n            }\n        }\n    }\n\n    onShiftKey() {\n        this.startRangeIndex.set(this.focusedOptionIndex());\n    }\n\n    onContainerClick(event: any) {\n        if (this.disabled || this.loading || this.readonly || (event.target as Node).isSameNode(this.focusInputViewChild?.nativeElement)) {\n            return;\n        }\n\n        if (event.target.tagName === 'INPUT' || event.target.getAttribute('data-pc-section') === 'clearicon' || event.target.closest('[data-pc-section=\"clearicon\"]')) {\n            event.preventDefault();\n            return;\n        } else if (!this.overlayViewChild || !this.overlayViewChild.el.nativeElement.contains(event.target)) {\n            if (this.clickInProgress) {\n                return;\n            }\n\n            this.clickInProgress = true;\n\n            setTimeout(() => {\n                this.clickInProgress = false;\n            }, 150);\n\n            this.overlayVisible ? this.hide(true) : this.show(true);\n        }\n        this.focusInputViewChild?.nativeElement.focus({ preventScroll: true });\n        this.onClick.emit(event);\n        this.cd.detectChanges();\n    }\n\n    onFirstHiddenFocus(event) {\n        const focusableEl =\n            event.relatedTarget === this.focusInputViewChild?.nativeElement\n                ? DomHandler.getFirstFocusableElement(this.overlayViewChild?.overlayViewChild?.nativeElement, ':not([data-p-hidden-focusable=\"true\"])')\n                : this.focusInputViewChild?.nativeElement;\n\n        DomHandler.focus(focusableEl);\n    }\n\n    onInputFocus(event: Event) {\n        this.focused = true;\n        const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n        this.focusedOptionIndex.set(focusedOptionIndex);\n        this.overlayVisible && this.scrollInView(this.focusedOptionIndex());\n        this.onFocus.emit({ originalEvent: event });\n    }\n\n    onInputBlur(event: Event) {\n        this.focused = false;\n        this.onBlur.emit({ originalEvent: event });\n\n        if (!this.preventModelTouched) {\n            this.onModelTouched();\n        }\n        this.preventModelTouched = false;\n    }\n\n    onFilterInputChange(event: KeyboardEvent) {\n        let value: string = (event.target as HTMLInputElement).value;\n        this._filterValue.set(value);\n        this.focusedOptionIndex.set(-1);\n        this.onFilter.emit({ originalEvent: event, filter: this._filterValue() });\n\n        !this.virtualScrollerDisabled && this.scroller.scrollToIndex(0);\n        setTimeout(() => {\n            this.overlayViewChild.alignOverlay();\n        });\n    }\n\n    onLastHiddenFocus(event) {\n        const focusableEl =\n            event.relatedTarget === this.focusInputViewChild?.nativeElement\n                ? DomHandler.getLastFocusableElement(this.overlayViewChild?.overlayViewChild?.nativeElement, ':not([data-p-hidden-focusable=\"true\"])')\n                : this.focusInputViewChild?.nativeElement;\n\n        DomHandler.focus(focusableEl);\n    }\n\n    onOptionMouseEnter(event, index) {\n        if (this.focusOnHover) {\n            this.changeFocusedOptionIndex(event, index);\n        }\n    }\n\n    onHeaderCheckboxKeyDown(event) {\n        if (this.disabled) {\n            event.preventDefault();\n\n            return;\n        }\n\n        switch (event.code) {\n            case 'Space':\n                this.onToggleAll(event);\n                break;\n            case 'Enter':\n                this.onToggleAll(event);\n                break;\n            default:\n                break;\n        }\n    }\n\n    onFilterBlur(event) {\n        this.focusedOptionIndex.set(-1);\n    }\n\n    onHeaderCheckboxFocus() {\n        this.headerCheckboxFocus = true;\n    }\n\n    onHeaderCheckboxBlur() {\n        this.headerCheckboxFocus = false;\n    }\n\n    onToggleAll(event) {\n        if (this.disabled || this.readonly) {\n            return;\n        }\n\n        if (this.selectAll != null) {\n            this.onSelectAllChange.emit({\n                originalEvent: event,\n                checked: !this.allSelected()\n            });\n        } else {\n            // pre-selected disabled options should always be selected.\n            const selectedDisabledOptions = this.getAllVisibleAndNonVisibleOptions().filter(\n                (option) => this.isSelected(option) && (this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : option && option.disabled !== undefined ? option.disabled : false)\n            );\n\n            const visibleOptions = this.allSelected()\n                ? this.visibleOptions().filter((option) => !this.isValidOption(option) && this.isSelected(option))\n                : this.visibleOptions().filter((option) => this.isSelected(option) || this.isValidOption(option));\n\n            const optionValues = [...selectedDisabledOptions, ...visibleOptions].map((option) => this.getOptionValue(option));\n            const value = [...new Set(optionValues)];\n\n            this.updateModel(value, event);\n\n            // because onToggleAll could have been called during filtering, this additional test needs to be performed before calling onSelectAllChange.emit\n            if (!value.length || value.length === this.getAllVisibleAndNonVisibleOptions().length) {\n                this.onSelectAllChange.emit({\n                    originalEvent: event,\n                    checked: !!value.length\n                });\n            }\n        }\n\n        if (this.partialSelected()) {\n            this.selectedOptions = null;\n            this.cd.markForCheck();\n        }\n\n        this.onChange.emit({ originalEvent: event, value: this.value });\n        DomHandler.focus(this.headerCheckboxViewChild?.nativeElement);\n        this.headerCheckboxFocus = true;\n\n        event.preventDefault();\n        event.stopPropagation();\n    }\n\n    changeFocusedOptionIndex(event, index) {\n        if (this.focusedOptionIndex() !== index) {\n            this.focusedOptionIndex.set(index);\n            this.scrollInView();\n        }\n    }\n\n    get virtualScrollerDisabled() {\n        return !this.virtualScroll;\n    }\n\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n        if (this.itemsViewChild && this.itemsViewChild.nativeElement) {\n            const element = DomHandler.findSingle(this.itemsViewChild.nativeElement, `li[id=\"${id}\"]`);\n            if (element) {\n                element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n            } else if (!this.virtualScrollerDisabled) {\n                setTimeout(() => {\n                    this.virtualScroll && this.scroller?.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex());\n                }, 0);\n            }\n        }\n    }\n\n    get focusedOptionId() {\n        return this.focusedOptionIndex() !== -1 ? `${this.id}_${this.focusedOptionIndex()}` : null;\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        if (!ObjectUtils.isEmpty(this.selectionLimit) && ObjectUtils.isEmpty(this.value)) {\n            this.modelValue.set([]);\n        } else {\n            this.modelValue.set(this.value);\n        }\n\n        this.cd.markForCheck();\n    }\n\n    public registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    public registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    allSelected() {\n        return this.selectAll !== null ? this.selectAll : ObjectUtils.isNotEmpty(this.visibleOptions()) && this.visibleOptions().every((option) => this.isOptionGroup(option) || this.isOptionDisabled(option) || this.isSelected(option));\n    }\n\n    partialSelected() {\n        return this.selectedOptions && this.selectedOptions.length > 0 && this.selectedOptions.length < this.options.length;\n    }\n\n    /**\n     * Displays the panel.\n     * @group Method\n     */\n    public show(isFocus?) {\n        this.overlayVisible = true;\n        const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n        this.focusedOptionIndex.set(focusedOptionIndex);\n\n        if (isFocus) {\n            DomHandler.focus(this.focusInputViewChild?.nativeElement);\n        }\n\n        this.cd.markForCheck();\n    }\n\n    /**\n     * Hides the panel.\n     * @group Method\n     */\n    public hide(isFocus?) {\n        this.overlayVisible = false;\n        this.focusedOptionIndex.set(-1);\n\n        if (this.filter && this.resetFilterOnHide) {\n            this.resetFilter();\n        }\n        if (this.overlayOptions?.mode === 'modal') {\n            DomHandler.unblockBodyScroll();\n        }\n\n        isFocus && DomHandler.focus(this.focusInputViewChild?.nativeElement);\n        this.onPanelHide.emit();\n        this.cd.markForCheck();\n    }\n\n    onOverlayAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                this.itemsWrapper = DomHandler.findSingle(this.overlayViewChild?.overlayViewChild?.nativeElement, this.virtualScroll ? '.p-scroller' : '.p-multiselect-items-wrapper');\n                this.virtualScroll && this.scroller?.setContentEl(this.itemsViewChild?.nativeElement);\n\n                if (this._options() && this._options().length) {\n                    if (this.virtualScroll) {\n                        const selectedIndex = ObjectUtils.isNotEmpty(this.modelValue()) ? this.focusedOptionIndex() : -1;\n                        if (selectedIndex !== -1) {\n                            this.scroller?.scrollToIndex(selectedIndex);\n                        }\n                    } else {\n                        let selectedListItem = DomHandler.findSingle(this.itemsWrapper, '[data-p-highlight=\"true\"]');\n\n                        if (selectedListItem) {\n                            selectedListItem.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n                        }\n                    }\n                }\n\n                if (this.filterInputChild && this.filterInputChild.nativeElement) {\n                    this.preventModelTouched = true;\n\n                    if (this.autofocusFilter) {\n                        this.filterInputChild.nativeElement.focus();\n                    }\n                }\n\n                this.onPanelShow.emit();\n            case 'void':\n                this.itemsWrapper = null;\n                this.onModelTouched();\n                break;\n        }\n    }\n\n    resetFilter() {\n        if (this.filterInputChild && this.filterInputChild.nativeElement) {\n            this.filterInputChild.nativeElement.value = '';\n        }\n\n        this._filterValue.set(null);\n        this._filteredOptions = null;\n    }\n\n    close(event: Event) {\n        this.hide();\n        event.preventDefault();\n        event.stopPropagation();\n    }\n\n    clear(event: Event) {\n        this.value = null;\n        this.updateModel(null, event);\n        this.selectedOptions = null;\n        this.onClear.emit();\n        this._disableTooltip = true;\n\n        event.stopPropagation();\n    }\n\n    labelContainerMouseLeave() {\n        if (this._disableTooltip) this._disableTooltip = false;\n    }\n\n    removeOption(optionValue, event) {\n        let value = this.modelValue().filter((val) => !ObjectUtils.equals(val, optionValue, this.equalityKey()));\n\n        this.updateModel(value, event);\n        this.onChange.emit({\n            originalEvent: event,\n            value: value,\n            itemValue: optionValue\n        });\n\n        event && event.stopPropagation();\n    }\n\n    findNextItem(item: any): HTMLElement | null {\n        let nextItem = item.nextElementSibling;\n\n        if (nextItem) return DomHandler.hasClass(nextItem.children[0], 'p-disabled') || DomHandler.isHidden(nextItem.children[0]) || DomHandler.hasClass(nextItem, 'p-multiselect-item-group') ? this.findNextItem(nextItem) : nextItem.children[0];\n        else return null;\n    }\n\n    findPrevItem(item: any): HTMLElement | null {\n        let prevItem = item.previousElementSibling;\n\n        if (prevItem) return DomHandler.hasClass(prevItem.children[0], 'p-disabled') || DomHandler.isHidden(prevItem.children[0]) || DomHandler.hasClass(prevItem, 'p-multiselect-item-group') ? this.findPrevItem(prevItem) : prevItem.children[0];\n        else return null;\n    }\n\n    findNextOptionIndex(index) {\n        const matchedOptionIndex =\n            index < this.visibleOptions().length - 1\n                ? this.visibleOptions()\n                      .slice(index + 1)\n                      .findIndex((option) => this.isValidOption(option))\n                : -1;\n        return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n\n    findPrevOptionIndex(index) {\n        const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), (option) => this.isValidOption(option)) : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n\n    findLastSelectedOptionIndex() {\n        return this.hasSelectedOption() ? ObjectUtils.findLastIndex(this.visibleOptions(), (option) => this.isValidSelectedOption(option)) : -1;\n    }\n\n    findLastFocusedOptionIndex() {\n        const selectedIndex = this.findLastSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n\n    findLastOptionIndex() {\n        return ObjectUtils.findLastIndex(this.visibleOptions(), (option) => this.isValidOption(option));\n    }\n\n    searchOptions(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let optionIndex = -1;\n        let matched = false;\n\n        if (this.focusedOptionIndex() !== -1) {\n            optionIndex = this.visibleOptions()\n                .slice(this.focusedOptionIndex())\n                .findIndex((option) => this.isOptionMatched(option));\n            optionIndex =\n                optionIndex === -1\n                    ? this.visibleOptions()\n                          .slice(0, this.focusedOptionIndex())\n                          .findIndex((option) => this.isOptionMatched(option))\n                    : optionIndex + this.focusedOptionIndex();\n        } else {\n            optionIndex = this.visibleOptions().findIndex((option) => this.isOptionMatched(option));\n        }\n\n        if (optionIndex !== -1) {\n            matched = true;\n        }\n\n        if (optionIndex === -1 && this.focusedOptionIndex() === -1) {\n            optionIndex = this.findFirstFocusedOptionIndex();\n        }\n\n        if (optionIndex !== -1) {\n            this.changeFocusedOptionIndex(event, optionIndex);\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n\n    activateFilter() {\n        if (this.hasFilter() && this._options) {\n            if (this.group) {\n                let filteredGroups = [];\n                for (let optgroup of this.options as any[]) {\n                    let filteredSubOptions = this.filterService.filter(this.getOptionGroupChildren(optgroup), this.searchFields(), this.filterValue, this.filterMatchMode, this.filterLocale);\n                    if (filteredSubOptions && filteredSubOptions.length) {\n                        filteredGroups.push({ ...optgroup, ...{ [this.optionGroupChildren]: filteredSubOptions } });\n                    }\n                }\n\n                this._filteredOptions = filteredGroups;\n            } else {\n                this._filteredOptions = this.filterService.filter(this.options as any[], this.searchFields(), this.filterValue, this.filterMatchMode, this.filterLocale);\n            }\n        } else {\n            this._filteredOptions = null;\n        }\n    }\n\n    hasFocusableElements() {\n        return DomHandler.getFocusableElements(this.overlayViewChild.overlayViewChild.nativeElement, ':not([data-p-hidden-focusable=\"true\"])').length > 0;\n    }\n\n    hasFilter() {\n        return this._filterValue() && this._filterValue().trim().length > 0;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, OverlayModule, SharedModule, TooltipModule, RippleModule, ScrollerModule, AutoFocusModule, CheckIcon, SearchIcon, TimesCircleIcon, TimesIcon, ChevronDownIcon, CheckIcon, MinusIcon],\n    exports: [MultiSelect, OverlayModule, SharedModule, ScrollerModule],\n    declarations: [MultiSelect, MultiSelectItem]\n})\nexport class MultiSelectModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAkDa,MAAA,0BAA0B,GAAQ;AAC3C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,WAAW,CAAC;AAC1C,IAAA,KAAK,EAAE,IAAI;EACb;MA6CW,eAAe,CAAA;AA6BL,IAAA,MAAA,CAAA;AA5BV,IAAA,EAAE,CAAqB;AAEvB,IAAA,MAAM,CAAM;AAEmB,IAAA,QAAQ,CAAsB;AAE7D,IAAA,KAAK,CAAqB;AAEK,IAAA,QAAQ,CAAsB;AAE/B,IAAA,QAAQ,CAAqB;AAE5B,IAAA,OAAO,CAAsB;AAE5D,IAAA,YAAY,CAAqB;AAEjC,IAAA,WAAW,CAAqB;AAEhC,IAAA,QAAQ,CAA+B;AAEvC,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,wBAAwB,CAA+B;AAEtD,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEhD,IAAA,YAAY,GAAsB,IAAI,YAAY,EAAE,CAAC;AAE/D,IAAA,WAAA,CAAmB,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;AAE5C,IAAA,aAAa,CAAC,KAAY,EAAA;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AACd,YAAA,aAAa,EAAE,KAAK;YACpB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC1B,SAAA,CAAC,CAAC;QACH,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;AAED,IAAA,kBAAkB,CAAC,KAAY,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACnB,YAAA,aAAa,EAAE,KAAK;YACpB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC1B,SAAA,CAAC,CAAC;KACN;uGA9CQ,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAf,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKJ,gBAAgB,CAIhB,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,sCAEhB,eAAe,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAEf,gBAAgB,CAtD1B,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA0nEsL,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FApnEvL,eAAe,EAAA,UAAA,EAAA,CAAA;kBA3C3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;kFAEY,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAEG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAEG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAEG,wBAAwB,EAAA,CAAA;sBAAhC,KAAK;gBAEI,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAEG,YAAY,EAAA,CAAA;sBAArB,MAAM;;AAqBX;;;AAGG;MAwSU,WAAW,CAAA;AAwuBD,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAA8B,IAAA,IAAA,CAAA;AAAqB,IAAA,aAAA,CAAA;AAAqC,IAAA,MAAA,CAAA;AAA8B,IAAA,cAAA,CAAA;AAvuB5L;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAAM;AACzB;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,KAAK,CAAsB;AACnE;;;AAGG;IACqC,MAAM,GAAY,IAAI,CAAC;AAC/D;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACqC,IAAA,cAAc,CAAsB;AAC5E;;;AAGG;IACoC,QAAQ,GAAuB,CAAC,CAAC;AACxE;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;;AAIG;IACH,IAAa,oBAAoB,CAAC,GAAY,EAAA;AAC1C,QAAA,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC;KACpC;AACD,IAAA,IAAI,oBAAoB,GAAA;QACpB,OAAO,IAAI,CAAC,qBAAqB,CAAC;KACrC;AACD;;;;AAIG;IACH,IAAa,iBAAiB,CAAC,GAA8B,EAAA;AACzD,QAAA,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;KACjC;AACD,IAAA,IAAI,iBAAiB,GAAA;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAClC;AACD;;;AAGG;AACqE,IAAA,cAAc,CAA4B;AAClH;;;AAGG;AACM,IAAA,kBAAkB,CAAqB;AAChD;;;AAGG;IACqC,aAAa,GAAY,IAAI,CAAC;AACtE;;;AAGG;IACM,kBAAkB,GAAW,EAAE,CAAC;AACzC;;;AAGG;IACM,YAAY,GAAW,EAAE,CAAC;AACnC;;;AAGG;IACqC,iBAAiB,GAAY,KAAK,CAAC;AAC3E;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACM,gBAAgB,GAAuB,OAAO,CAAC;AACxD;;;AAGG;IACM,mBAAmB,GAAW,OAAO,CAAC;AAC/C;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;IACM,YAAY,GAAW,OAAO,CAAC;AACxC;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;IACqC,OAAO,GAAwB,KAAK,CAAC;AAC7E;;;AAGG;AACoC,IAAA,qBAAqB,CAAqB;AACjF;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,oBAAoB,CAA8B;AAC3D;;;AAGG;AACM,IAAA,cAAc,CAA6B;AACpD;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;IACM,eAAe,GAAyG,UAAU,CAAC;AAC5I;;;AAGG;IACM,OAAO,GAAW,EAAE,CAAC;AAC9B;;;AAGG;IACM,eAAe,GAAwC,OAAO,CAAC;AACxE;;;AAGG;IACM,oBAAoB,GAAW,UAAU,CAAC;AACnD;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;IACqC,eAAe,GAAY,IAAI,CAAC;AACxE;;;AAGG;IACM,OAAO,GAA8B,OAAO,CAAC;AACtD;;;AAGG;IACM,YAAY,GAAW,KAAK,CAAC;AACtC;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAwB,EAAA;AACnC,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;KAC7G;AACD;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAuB,EAAA;AAClC,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;KAC7G;AACD;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACtC;IACD,IAAI,qBAAqB,CAAC,GAAuB,EAAA;AAC7C,QAAA,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;KACxH;AACD;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACtC;IACD,IAAI,qBAAqB,CAAC,GAAuB,EAAA;AAC7C,QAAA,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;KACxH;AACD;;;;AAIG;IACH,IAAa,YAAY,CAAC,GAAuB,EAAA;AAC7C,QAAA,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;AACzB,QAAA,OAAO,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAC;KAC7F;AACD,IAAA,IAAI,YAAY,GAAA;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;KAC7B;AACD;;;AAGG;IACH,IAAa,WAAW,CAAC,GAAuB,EAAA;AAC5C,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9B;AACD,IAAA,IAAI,WAAW,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;KACzC;AACD;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;AAChB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChC,QAAA,OAAO,OAAO,CAAC;KAClB;IACD,IAAI,OAAO,CAAC,GAAsB,EAAA;AAC9B,QAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,EAAE;AAC/C,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1B,SAAA;KACJ;AACD;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;KAC9B;IACD,IAAI,WAAW,CAAC,GAA8B,EAAA;AAC1C,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9B;AACD;;;;AAIG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,GAAuB,EAAA;AAChC,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACrB,QAAA,OAAO,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;KACpG;AACD;;;AAGG;AACH,IAAA,IAAa,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;IACD,IAAI,SAAS,CAAC,KAAiC,EAAA;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;KAC3B;AACD;;;AAGG;IACqC,YAAY,GAAY,KAAK,CAAC;AACtE;;;AAGG;AACM,IAAA,YAAY,CAAoB;AACzC;;;AAGG;IACqC,aAAa,GAAY,KAAK,CAAC;AACvE;;;AAGG;IACqC,eAAe,GAAY,IAAI,CAAC;AACxE;;;;AAIG;AACO,IAAA,QAAQ,GAAyC,IAAI,YAAY,EAA0B,CAAC;AACtG;;;;AAIG;AACO,IAAA,QAAQ,GAAyC,IAAI,YAAY,EAA0B,CAAC;AACtG;;;;AAIG;AACO,IAAA,OAAO,GAAwC,IAAI,YAAY,EAAyB,CAAC;AACnG;;;;AAIG;AACO,IAAA,MAAM,GAAuC,IAAI,YAAY,EAAwB,CAAC;AAChG;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;AAGG;AACO,IAAA,OAAO,GAAuB,IAAI,YAAY,EAAQ,CAAC;AACjE;;;AAGG;AACO,IAAA,WAAW,GAAuB,IAAI,YAAY,EAAQ,CAAC;AACrE;;;AAGG;AACO,IAAA,WAAW,GAAuB,IAAI,YAAY,EAAQ,CAAC;AACrE;;;;AAIG;AACO,IAAA,UAAU,GAA2C,IAAI,YAAY,EAA4B,CAAC;AAC5G;;;;AAIG;AACO,IAAA,QAAQ,GAAyC,IAAI,YAAY,EAA0B,CAAC;AACtG;;;;AAIG;AACO,IAAA,iBAAiB,GAAkD,IAAI,YAAY,EAAmC,CAAC;AAEzG,IAAA,kBAAkB,CAAuB;AAE3C,IAAA,gBAAgB,CAAoB;AAEhC,IAAA,gBAAgB,CAAuB;AAExC,IAAA,mBAAmB,CAAuB;AAE/C,IAAA,cAAc,CAAuB;AAElC,IAAA,QAAQ,CAAqB;AAEhB,IAAA,mCAAmC,CAAuB;AAEzD,IAAA,oCAAoC,CAAuB;AAEnE,IAAA,uBAAuB,CAAuB;AAErD,IAAA,WAAW,CAAM;AAEjB,IAAA,WAAW,CAAM;AAEP,IAAA,SAAS,CAAqC;AAE9E,IAAA,WAAW,CAAmB;AAE9B,IAAA,aAAa,CAAM;IAEnB,UAAU,GAA+B,IAAI,CAAC;AAE9C,IAAA,WAAW,CAAsB;AAEjC,IAAA,WAAW,CAAqB;AAEhC,IAAA,sBAAsB,CAAqB;AAE3C,IAAA,sBAAsB,CAAqB;AAE3C,IAAA,aAAa,CAAqB;AAElC,IAAA,YAAY,GAAG,MAAM,CAAqB,SAAS,CAAC,CAAC;AAErD,IAAA,SAAS,CAAqB;AAE9B,IAAA,eAAe,CAAqB;IAEpC,eAAe,GAAG,KAAK,CAAC;AAExB,IAAA,KAAK,CAAQ;AAEN,IAAA,gBAAgB,CAA2B;AAE3C,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,cAAc,CAAqB;AAEnC,IAAA,KAAK,CAAsB;AAE3B,IAAA,QAAQ,CAAsB;AAE9B,IAAA,YAAY,CAA+B;AAE3C,IAAA,aAAa,CAA+B;AAE5C,IAAA,cAAc,CAA+B;AAE7C,IAAA,cAAc,CAA+B;AAE7C,IAAA,cAAc,CAA+B;AAE7C,IAAA,cAAc,CAA+B;AAE7C,IAAA,mBAAmB,CAA+B;AAElD,IAAA,aAAa,CAA+B;AAE5C,IAAA,qBAAqB,CAA+B;AAE3D,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,mBAAmB,CAA+B;AAElD,IAAA,kBAAkB,CAA+B;AAEjD,IAAA,uBAAuB,CAA+B;AAEtD,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,oBAAoB,CAA+B;AAEnD,IAAA,wBAAwB,CAA+B;AAEvD,IAAA,0BAA0B,CAA+B;AAElD,IAAA,mBAAmB,CAAsB;AAEhD,IAAA,aAAa,CAAuC;AAEpD,IAAA,mBAAmB,CAAsB;AAEzC,IAAA,sBAAsB,CAAsB;IAE5C,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,YAAY,CAAM;IAElB,qBAAqB,GAAY,IAAI,CAAC;IAEtC,kBAAkB,GAAW,CAAC,CAAC;AAE/B,IAAA,UAAU,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAE/B,IAAA,YAAY,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAEjC,IAAA,QAAQ,GAAG,MAAM,CAAQ,IAAI,CAAC,CAAC;AAE/B,IAAA,eAAe,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC,CAAC;AAErC,IAAA,kBAAkB,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC,CAAC;AAExC,IAAA,eAAe,CAAM;IAErB,eAAe,GAAY,KAAK,CAAC;AAEjC,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;AACH,YAAA,0CAA0C,EAAE,IAAI;YAChD,YAAY,EAAE,IAAI,CAAC,QAAQ;YAC3B,yBAAyB,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;AAC3D,YAAA,oBAAoB,EAAE,IAAI,CAAC,OAAO,KAAK,MAAM;YAC7C,SAAS,EAAE,IAAI,CAAC,OAAO;AACvB,YAAA,kBAAkB,EAAE,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ;SACzF,CAAC;KACL;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,iCAAiC,EAAE,IAAI;AACvC,YAAA,eAAe,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;YACzI,2BAA2B,EAAE,CAAC,IAAI,CAAC,qBAAqB,KAAK,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC;SAC7H,CAAC;KACL;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,iCAAiC,EAAE,IAAI;YACvC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ;AACvD,YAAA,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,KAAK;SACpD,CAAC;KACL;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,qBAAqB,EAAE,IAAI;AAC3B,YAAA,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,YAAY;YAC1F,2BAA2B,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC;SACnI,CAAC;KACL;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;KACzF;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;KACtG;AAED,IAAA,IAAI,MAAM,GAAA;AACN,QAAA,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,QAAQ;AAAE,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QAEtE,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KACpD;AAED,IAAA,IAAI,kBAAkB,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC;KAChK;AAED,IAAA,IAAI,kBAAkB,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,WAAW,GAAG,aAAa,CAAC,GAAG,SAAS,CAAC;KACpI;AAED,IAAA,IAAI,cAAc,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;KACxF;AAED,IAAA,IAAI,SAAS,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC;KACxE;IAEO,iCAAiC,GAAA;QACrC,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;KAC3E;AAED,IAAA,cAAc,GAAG,QAAQ,CAAC,MAAK;AAC3B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iCAAiC,EAAE,CAAC;AACzD,QAAA,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAE1F,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;AACrB,YAAA,IAAI,eAAe,CAAC;AAEpB,YAAA,IAAI,gBAAgB,EAAE;gBAClB,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC3I,aAAA;AAAM,iBAAA;AACH,gBAAA,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;AACzI,aAAA;YAED,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;gBACxC,MAAM,QAAQ,GAAG,EAAE,CAAC;AAEpB,gBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;oBAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AACzD,oBAAA,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAErF,oBAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC;AAAE,wBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,OAAO,IAAI,CAAC,mBAAmB,KAAK,QAAQ,GAAG,IAAI,CAAC,mBAAmB,GAAG,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;AACvK,iBAAC,CAAC,CAAC;AAEH,gBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACrC,aAAA;AAED,YAAA,OAAO,eAAe,CAAC;AAC1B,SAAA;AACD,QAAA,OAAO,OAAO,CAAC;AACnB,KAAC,CAAC,CAAC;AAEH,IAAA,KAAK,GAAG,QAAQ,CAAC,MAAK;AAClB,QAAA,IAAI,KAAK,CAAC;AACV,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAErC,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC9D,YAAA,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE;AAC9F,gBAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACvC,aAAA;AAAM,iBAAA;gBACH,KAAK,GAAG,EAAE,CAAC;AAEX,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACxC,IAAI,CAAC,KAAK,CAAC,EAAE;wBACT,KAAK,IAAI,IAAI,CAAC;AACjB,qBAAA;oBAED,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,iBAAA;AACJ,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC;AACzD,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;AACjB,KAAC,CAAC,CAAC;AAEH,IAAA,iBAAiB,GAAG,QAAQ,CAAC,MAAK;QAC9B,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAC7M,KAAC,CAAC,CAAC;AAEH,IAAA,WAAA,CAAmB,EAAc,EAAS,QAAmB,EAAS,EAAqB,EAAS,IAAY,EAAS,aAA4B,EAAS,MAAqB,EAAS,cAA8B,EAAA;QAAvM,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAS,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAS,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QACtN,MAAM,CAAC,MAAK;AACR,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAErC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC7C,IAAI,cAAc,IAAI,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;gBAC1D,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,IAAI,UAAU,EAAE;AACpD,oBAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC5J,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;AACrC,iBAAA;AACD,gBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;QACzC,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,aAAa,GAAG;gBACjB,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;AAClD,gBAAA,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;aAClC,CAAC;AACL,SAAA;KACJ;IAED,wBAAwB,GAAA;QACpB,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC;KAC/H;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,eAAe;AAChB,oBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC3C,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvC,oBAAA,OAAO,CAAC,IAAI,CAAC,gHAAgH,CAAC,CAAC;oBAC/H,MAAM;AAEV,gBAAA,KAAK,oBAAoB;AACrB,oBAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAChD,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,iBAAiB;AAClB,oBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC7C,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,kBAAkB;AACnB,oBAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC9C,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,UAAU,CAAC,MAAK;AACZ,oBAAA,IAAI,CAAC,gBAAgB,EAAE,YAAY,EAAE,CAAC;iBACzC,EAAE,CAAC,CAAC,CAAC;AACV,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACzB,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,OAAO,EAAA;AACf,QAAA,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,KAAI;AACpD,YAAA,MAAM,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAEzD,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;AAEhE,YAAA,mBAAmB,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAE1E,YAAA,OAAO,MAAM,CAAC;SACjB,EAAE,EAAE,CAAC,CAAC;KACV;IAED,eAAe,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;YACzE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAC;AAChE,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AACpF,YAAA,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACjE,SAAA;KACJ;AAED;;;AAGG;IACI,WAAW,CAAC,KAAK,EAAE,KAAM,EAAA;AAC5B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KAC9B;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;QACd,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACnC;IAED,cAAc,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAA;AAC7C,QAAA,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QACxC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAChD,OAAO;AACV,SAAA;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YACrH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACtF,SAAA;AAAM,aAAA;AACH,YAAA,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AACvE,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AACvC,QAAA,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAEnD,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;AAErE,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACf,aAAa,EAAE,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE;AACtD,YAAA,KAAK,EAAE,KAAK;AACZ,YAAA,SAAS,EAAE,MAAM;AACpB,SAAA,CAAC,CAAC;KACN;IAED,uBAAuB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1H;IAED,mBAAmB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAA;AAC3C,QAAA,KAAK,KAAK,CAAC,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,8BAA8B,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACzE,QAAA,GAAG,KAAK,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,CAAC;QAEjE,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACtC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE;AAC9B,iBAAA,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG,CAAC,CAAC;AAC/B,iBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC9C,iBAAA,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAElD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;KACJ;IAED,YAAY,GAAA;AACR,QAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;KACpE;AAED,IAAA,8BAA8B,CAAC,KAAK,EAAE,YAAY,GAAG,KAAK,EAAA;AACtD,QAAA,IAAI,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAE5B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC1B,YAAA,IAAI,YAAY,EAAE;AACd,gBAAA,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;AAC7D,gBAAA,kBAAkB,GAAG,kBAAkB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC;AACjH,aAAA;AAAM,iBAAA;AACH,gBAAA,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;AAC7D,gBAAA,kBAAkB,GAAG,kBAAkB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC;AACjH,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,CAAC;KAC/D;AAED,IAAA,2BAA2B,CAAC,KAAK,EAAA;QAC7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,IAAI,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEzL,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,CAAC,CAAC;KAC5D;IAED,2BAA2B,GAAA;AACvB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;AAE1D,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,aAAa,CAAC;KAC1E;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KAClF;IAED,4BAA4B,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1H;AAED,IAAA,2BAA2B,CAAC,KAAK,EAAA;AAC7B,QAAA,MAAM,kBAAkB,GACpB,IAAI,CAAC,iBAAiB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC;AAChE,cAAE,IAAI,CAAC,cAAc,EAAE;AAChB,iBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChB,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;cAC9D,CAAC,CAAC,CAAC;AAEb,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KACxE;IAED,WAAW,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;KACjD;IAED,iBAAiB,GAAA;QACb,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KACpD;IAED,sBAAsB,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACzE;AAED,IAAA,qBAAqB,CAAC,MAAM,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;KAChE;AAED,IAAA,aAAa,CAAC,MAAM,EAAA;AAChB,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,CAAC;KACtF;AAED,IAAA,aAAa,CAAC,MAAM,EAAA;AAChB,QAAA,OAAO,MAAM,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KACnF;AAED,IAAA,gBAAgB,CAAC,MAAW,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AAC7D,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,GAAG,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;KAC9J;AAED,IAAA,UAAU,CAAC,MAAM,EAAA;QACb,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChD,QAAA,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KAChH;AAED,IAAA,eAAe,CAAC,MAAM,EAAA;AAClB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KACtL;IAED,OAAO,GAAA;QACH,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;KAC5F;IAED,cAAc,CAAC,KAAK,EAAE,eAAe,EAAA;QACjC,OAAO,IAAI,CAAC,uBAAuB,GAAG,KAAK,GAAG,eAAe,IAAI,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;KACnH;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;AACjB,QAAA,QACI,CAAC,IAAI,CAAC,gBAAgB;AAClB,cAAE,KAAK;gBACL,IAAI,CAAC,cAAc,EAAE;AAChB,qBAAA,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AACf,qBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;AAC5D,cAAE,KAAK,IAAI,CAAC,EAClB;KACL;AAED,IAAA,IAAI,WAAW,GAAA;QACX,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;KACvF;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;AACvF,QAAA,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AAC1J,QAAA,OAAO,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;KACpE;IAED,qBAAqB,GAAA;QACjB,IAAI,OAAO,GAAG,SAAS,CAAC;QACxB,IAAI,OAAO,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;AAEhI,QAAA,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACvB,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;AACpF,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,cAAc,CAAC,MAAW,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;KAClJ;AAED,IAAA,cAAc,CAAC,MAAW,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;KACxK;AAED,IAAA,mBAAmB,CAAC,WAAgB,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,WAAW,IAAI,WAAW,CAAC,KAAK,IAAI,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC;KACrL;AAED,IAAA,sBAAsB,CAAC,WAAgB,EAAA;QACnC,OAAO,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;KAC7H;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;AACV,SAAA;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QAE/C,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,UAAU;AACX,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;gBACb,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM;AAEV,YAAA;AACI,gBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,OAAO,EAAE;AAClC,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE;AAC9B,yBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC9C,yBAAA,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAElD,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;oBAE/B,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM;AACT,iBAAA;gBAED,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBACpC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;oBACrC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;QAChC,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC/B,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAE,kBAAA,GAA8B,KAAK,EAAA;QACpE,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACzD;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;AAChB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEhJ,IAAI,KAAK,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,WAAW,CAAC,CAAC;AACxE,SAAA;AAED,QAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAClD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACpC,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;AAED,IAAA,YAAY,CAAC,KAAK,EAAE,kBAAkB,GAAG,KAAK,EAAA;AAC1C,QAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,kBAAkB,EAAE;AACrC,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AAChF,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACnC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAE/I,IAAI,KAAK,CAAC,QAAQ,EAAE;AAChB,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;AACxE,aAAA;AAED,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAElD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACpC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;QACD,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;AAED,IAAA,SAAS,CAAC,KAAK,EAAE,kBAAkB,GAAG,KAAK,EAAA;AACvC,QAAA,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAEhC,QAAA,IAAI,kBAAkB,EAAE;AACpB,YAAA,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;AAEvC,YAAA,aAAa,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;YAC7D,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,SAAA;AAAM,aAAA;YACH,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;AAC7C,YAAA,IAAI,WAAW,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAE9C,YAAA,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,EAAE;AAC3B,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;AACxE,aAAA;AAED,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAElD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACvC,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAE,kBAAkB,GAAG,KAAK,EAAA;AACtC,QAAA,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAEhC,QAAA,IAAI,kBAAkB,EAAE;AACpB,YAAA,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;AACvC,YAAA,aAAa,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YAC/D,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,SAAA;AAAM,aAAA;YACH,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;AAC7C,YAAA,IAAI,WAAW,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAEpD,YAAA,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,EAAE;AAC3B,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,WAAW,CAAC,CAAC;AACxE,aAAA;AAED,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAElD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACvC,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;AACf,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;gBAClC,IAAI,KAAK,CAAC,QAAQ,EAAE;oBAChB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAC9D,iBAAA;AAAM,qBAAA;oBACH,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3G,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;QACb,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;QAC5B,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAClB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAK,EAAE,kBAAkB,GAAG,KAAK,EAAA;QACtC,IAAI,CAAC,kBAAkB,EAAE;YACrB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBACpD,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,mCAAmC,CAAC,aAAa,GAAG,IAAI,CAAC,oCAAoC,CAAC,aAAa,CAAC,CAAC;gBAEpJ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;oBAClC,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3G,iBAAA;gBAED,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;KACJ;IAED,UAAU,GAAA;QACN,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;KACvD;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAK,KAAK,CAAC,MAAe,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,EAAE;YAC9H,OAAO;AACV,SAAA;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,KAAK,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,EAAE;YAC3J,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;AACV,SAAA;aAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACjG,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAE5B,UAAU,CAAC,MAAK;AACZ,gBAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;aAChC,EAAE,GAAG,CAAC,CAAC;YAER,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3D,SAAA;AACD,QAAA,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;AAED,IAAA,kBAAkB,CAAC,KAAK,EAAA;QACpB,MAAM,WAAW,GACb,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,mBAAmB,EAAE,aAAa;AAC3D,cAAE,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,wCAAwC,CAAC;AACvI,cAAE,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC;AAElD,QAAA,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACjC;AAED,IAAA,YAAY,CAAC,KAAY,EAAA;AACrB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,CAAC;AAChL,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;KAC/C;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;AAE3C,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,SAAA;AACD,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;KACpC;AAED,IAAA,mBAAmB,CAAC,KAAoB,EAAA;AACpC,QAAA,IAAI,KAAK,GAAY,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;AAC7D,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAE1E,QAAA,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAChE,UAAU,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;KACN;AAED,IAAA,iBAAiB,CAAC,KAAK,EAAA;QACnB,MAAM,WAAW,GACb,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,mBAAmB,EAAE,aAAa;AAC3D,cAAE,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,wCAAwC,CAAC;AACtI,cAAE,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC;AAElD,QAAA,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACjC;IAED,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAA;QAC3B,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/C,SAAA;KACJ;AAED,IAAA,uBAAuB,CAAC,KAAK,EAAA;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,OAAO;AACV,SAAA;QAED,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AACV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AACV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;QACd,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACnC;IAED,qBAAqB,GAAA;AACjB,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;KACnC;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;KACpC;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,OAAO;AACV,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;AACxB,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AACxB,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE;AAC/B,aAAA,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;;AAEH,YAAA,MAAM,uBAAuB,GAAG,IAAI,CAAC,iCAAiC,EAAE,CAAC,MAAM,CAC3E,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,GAAG,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC,CAC/L,CAAC;AAEF,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,EAAE;kBACnC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;kBAChG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;YAEtG,MAAM,YAAY,GAAG,CAAC,GAAG,uBAAuB,EAAE,GAAG,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAClH,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;AAEzC,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;AAG/B,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,iCAAiC,EAAE,CAAC,MAAM,EAAE;AACnF,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AACxB,oBAAA,aAAa,EAAE,KAAK;AACpB,oBAAA,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM;AAC1B,iBAAA,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;AACxB,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAChE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;AAC9D,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;IAED,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;KACJ;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;KAC9B;AAED,IAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;QACnB,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,eAAe,CAAC;QACvE,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;AAC1D,YAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAA,OAAA,EAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAC3F,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,aAAA;AAAM,iBAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACtC,UAAU,CAAC,MAAK;oBACZ,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;iBACxG,EAAE,CAAC,CAAC,CAAC;AACT,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,GAAG,IAAI,CAAC;KAC9F;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC9E,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC3B,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnC,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAEM,IAAA,gBAAgB,CAAC,EAAY,EAAA;AAChC,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAEM,IAAA,iBAAiB,CAAC,EAAY,EAAA;AACjC,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,WAAW,GAAA;QACP,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;KACtO;IAED,eAAe,GAAA;QACX,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;KACvH;AAED;;;AAGG;AACI,IAAA,IAAI,CAAC,OAAQ,EAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,CAAC;AACzJ,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAEhD,QAAA,IAAI,OAAO,EAAE;YACT,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;AAC7D,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED;;;AAGG;AACI,IAAA,IAAI,CAAC,OAAQ,EAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAEhC,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACvC,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,KAAK,OAAO,EAAE;YACvC,UAAU,CAAC,iBAAiB,EAAE,CAAC;AAClC,SAAA;QAED,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,uBAAuB,CAAC,KAAqB,EAAA;QACzC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;gBACV,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,GAAG,aAAa,GAAG,8BAA8B,CAAC,CAAC;AACvK,gBAAA,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;gBAEtF,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE;oBAC3C,IAAI,IAAI,CAAC,aAAa,EAAE;wBACpB,MAAM,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC;AACjG,wBAAA,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;AACtB,4BAAA,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AAC/C,yBAAA;AACJ,qBAAA;AAAM,yBAAA;AACH,wBAAA,IAAI,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;AAE7F,wBAAA,IAAI,gBAAgB,EAAE;AAClB,4BAAA,gBAAgB,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC5E,yBAAA;AACJ,qBAAA;AACJ,iBAAA;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;AAC9D,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;oBAEhC,IAAI,IAAI,CAAC,eAAe,EAAE;AACtB,wBAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC/C,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AAC5B,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM;AACb,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;YAC9D,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AAClD,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;KAChC;AAED,IAAA,KAAK,CAAC,KAAY,EAAA;QACd,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;AAED,IAAA,KAAK,CAAC,KAAY,EAAA;AACd,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC9B,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;IAED,wBAAwB,GAAA;QACpB,IAAI,IAAI,CAAC,eAAe;AAAE,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;KAC1D;IAED,YAAY,CAAC,WAAW,EAAE,KAAK,EAAA;AAC3B,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AAEzG,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,KAAK,EAAE,KAAK;AACZ,YAAA,SAAS,EAAE,WAAW;AACzB,SAAA,CAAC,CAAC;AAEH,QAAA,KAAK,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;KACpC;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;AAClB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAEvC,QAAA,IAAI,QAAQ;YAAE,OAAO,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,0BAA0B,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;AACvO,YAAA,OAAO,IAAI,CAAC;KACpB;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;AAClB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAE3C,QAAA,IAAI,QAAQ;YAAE,OAAO,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,0BAA0B,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;AACvO,YAAA,OAAO,IAAI,CAAC;KACpB;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;QACrB,MAAM,kBAAkB,GACpB,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC;AACpC,cAAE,IAAI,CAAC,cAAc,EAAE;AAChB,iBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChB,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC;AACb,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KAC3E;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAErJ,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,CAAC;KAC/D;IAED,2BAA2B,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC3I;IAED,0BAA0B,GAAA;AACtB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAEzD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,aAAa,CAAC;KACzE;IAED,mBAAmB,GAAA;QACf,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KACnG;IAED,aAAa,CAAC,KAAK,EAAE,IAAI,EAAA;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;AAEnD,QAAA,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;QACrB,IAAI,OAAO,GAAG,KAAK,CAAC;AAEpB,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,YAAA,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE;AAC9B,iBAAA,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAChC,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;YACzD,WAAW;gBACP,WAAW,KAAK,CAAC,CAAC;AACd,sBAAE,IAAI,CAAC,cAAc,EAAE;AAChB,yBAAA,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACnC,yBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC1D,sBAAE,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACrD,SAAA;AAAM,aAAA;YACH,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3F,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;YACpB,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AACxD,YAAA,WAAW,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACpD,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;AACpB,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;IAED,cAAc,GAAA;QACV,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACnC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,cAAc,GAAG,EAAE,CAAC;AACxB,gBAAA,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,OAAgB,EAAE;AACxC,oBAAA,IAAI,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC1K,oBAAA,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,EAAE;AACjD,wBAAA,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,EAAE,EAAE,CAAC,CAAC;AAC/F,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC;AAC1C,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,OAAgB,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC5J,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAChC,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,EAAE,wCAAwC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACrJ;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;KACvE;uGArxDQ,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAwCA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKhB,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,gBAAgB,CAKhB,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CAehB,EAAA,iBAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAgB,CAKhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,CAoDf,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAUhD,EAAA,kBAAA,EAAA,oBAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAehB,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAAA,gBAAgB,CAmChB,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAehB,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,CAKhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAKhB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAKhB,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAAA,eAAe,CAkDf,EAAA,WAAA,EAAA,aAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,CAehB,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAKhB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAwHhB,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAUhB,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAKhB,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,CA3azB,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,4BAAA,EAAA,2BAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,SAAA,EAAA,CAAC,0BAA0B,CAAC,EA8fzB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,MAAM,EAEN,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,MAAM,EAEH,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAlyBpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qCAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,uBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sCAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0RT,EAoyDsL,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,2+CAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,cAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,SAAS,CAAlE,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,UAAU,CAAE,EAAA,QAAA,EAAA,YAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,iFAAE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MApnE5K,eAAe,CAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,SAAA,EAAA,cAAA,EAAA,aAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,0BAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,cAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA2Vf,WAAW,EAAA,UAAA,EAAA,CAAA;kBAvSvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0RT,EACK,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACjC,wBAAA,8BAA8B,EAAE,2BAA2B;AAC3D,wBAAA,+BAA+B,EAAE,QAAQ;qBAC5C,EACU,SAAA,EAAA,CAAC,0BAA0B,CAAC,EACtB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,2+CAAA,CAAA,EAAA,CAAA;iPAQ5B,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMO,oBAAoB,EAAA,CAAA;sBAAhC,KAAK;gBAWO,iBAAiB,EAAA,CAAA;sBAA7B,KAAK;gBAUkE,cAAc,EAAA,CAAA;sBAArF,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,SAAS,EAAE,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAA;gBAK7D,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,qBAAqB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKkC,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAMzB,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAYO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAYO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAYO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAYO,YAAY,EAAA,CAAA;sBAAxB,KAAK;gBAWO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAUO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAaO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAWO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAWO,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAUkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAKG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAKG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAKG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAEiB,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEA,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEM,gBAAgB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,aAAa,CAAA;gBAEC,mBAAmB,EAAA,CAAA;sBAA3C,SAAS;uBAAC,YAAY,CAAA;gBAEH,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBAEK,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;gBAEe,mCAAmC,EAAA,CAAA;sBAAtE,SAAS;uBAAC,uBAAuB,CAAA;gBAEG,oCAAoC,EAAA,CAAA;sBAAxE,SAAS;uBAAC,wBAAwB,CAAA;gBAEN,uBAAuB,EAAA,CAAA;sBAAnD,SAAS;uBAAC,gBAAgB,CAAA;gBAEL,WAAW,EAAA,CAAA;sBAAhC,YAAY;uBAAC,MAAM,CAAA;gBAEE,WAAW,EAAA,CAAA;sBAAhC,YAAY;uBAAC,MAAM,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAgyCrB,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,iBA7xDjB,WAAW,EA3VX,eAAe,CAAA,EAAA,OAAA,EAAA,CAonEd,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,aAzxDlM,WAAW,EA0xDG,aAAa,EAAE,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;AAGzD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAJhB,OAAA,EAAA,CAAA,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EACpL,aAAa,EAAE,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;;2FAGzD,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,CAAC;oBAC5M,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,CAAC;AACnE,oBAAA,YAAY,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC;AAC/C,iBAAA,CAAA;;;AC1tED;;AAEG;;;;"}