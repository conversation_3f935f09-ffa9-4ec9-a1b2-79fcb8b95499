import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UserDetails } from '../../../core/user-services/api/users';
import { UsersService } from '../../../core/user-services/services/users.service';
import { ProfileService } from '../../../core/service-profile/services/profile.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Service } from '../../../core/service-profile/api/profile';
import { ToastService } from '../../shared/toast/toast.service'; // Adjusted path

@Component({
  selector: 'app-user-edit',
  templateUrl: './user-edit.component.html',
  styleUrls: ['./user-edit.component.scss'],
})
export class UserEditComponent implements OnInit {
  private userId!: string | null;
  userDetails: UserDetails | null = null;
  services!: Service[];
  profile_editable = false;
  username_editable = false;
  isLoading = false;
  userForm!: FormGroup;

  constructor(
    private route: ActivatedRoute,
    private usersService: UsersService,
    private profileService: ProfileService,
    private fb: FormBuilder,
    private toastService: ToastService,
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Access parent route to get the id parameter
    this.route.parent?.paramMap.subscribe((params) => {
      this.userId = params.get('id');
      console.log('User ID:', this.userId); // Check if the ID is correctly retrieved

      if (this.userId) {
        this.getUserDetails();
        this.getProfiles(Number(this.userId));
      }
    });
  }

  initForm() {
    this.userForm = this.fb.group({
      username: [this.userDetails?.data.username, Validators.required],
      enabled: [this.userDetails?.data.enabled],
      password: [null],
      confirm_password: [null],
      profile_id: [this.userDetails?.overview.profile_id, Validators.required],
      parent_id: [this.userDetails?.data.parent_id],
      use_separate_portal_password: [false],
      portal_password: [null],
      firstname: [this.userDetails?.data.firstname],
      lastname: [this.userDetails?.data.lastname],
      company: [this.userDetails?.data.company],
      email: [this.userDetails?.data.email],
      phone: [this.userDetails?.data.phone],
      city: [this.userDetails?.data.city],
      address: [this.userDetails?.data.address],
      apartment: [null],
      street: [this.userDetails?.data.street],
      contract_id: [this.userDetails?.data.contract_id],
      national_id: [this.userDetails?.data.national_id],
      notes: [this.userDetails?.data.notes],
    });
  }

  getUserDetails() {
    // fetch the data
    if (this.userId) {
      this.isLoading = true;
      this.usersService.getUser(this.userId).subscribe({
        next: (response) => {
          this.userDetails = response;
          this.userForm.patchValue(this.userDetails.data);
          console.log('User:', this.userDetails); // Check if the user data is correctly retrieved
        },
        complete: () => {
          this.isLoading = false;
        },
        error: (error) => {
          this.isLoading = false;
          console.error(error);
          this.toastService.addToast('error', 'Fetch Error', 'Failed to fetch user details.'); // Show error toast
        }
      });
    }
  }

  getProfiles(userId: number): void {
    this.profileService.getServices(userId).subscribe({
      next: (response) => {
        console.log(response);
        this.services = response;
      }
    });
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      const editedUserData = this.userForm.value;
      // Call the service to save the edited user data
      console.log('Edited User Data:', editedUserData);
      // Implement the service call here
      this.isLoading = true;
      this.usersService.editUser(this.userId, editedUserData).subscribe({
        next: (response) => {
          console.log(response);
          this.isLoading = false;
          this.toastService.addToast('success', 'User Updated', 'The user has been updated successfully!'); // Show success toast
        },
        error: (error) => {
          this.isLoading = false;
          console.error(error);
          this.toastService.addToast('error', 'Update Failed', 'Failed to update the user.'); // Show error toast
        }
      });
    } else {
      this.toastService.addToast('error', 'Invalid Form', 'Please fill out the form correctly.'); // Show form invalid toast
      console.log(this.userForm);
    }
  }

  // Getters
  get username(): any {
    return this.userForm.get('username');
  }

  get password(): any {
    return this.userForm.get('password');
  }

  get confirmPassword(): any {
    return this.userForm.get('confirm_password');
  }

  get profile_id(): any {
    return this.userForm.get('profile_id');
  }
}
