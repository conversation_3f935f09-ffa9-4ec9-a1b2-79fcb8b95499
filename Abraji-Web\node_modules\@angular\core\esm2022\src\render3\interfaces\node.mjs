/**
 * Converts `TNodeType` into human readable text.
 * Make sure this matches with `TNodeType`
 */
export function toTNodeTypeAsString(tNodeType) {
    let text = '';
    tNodeType & 1 /* TNodeType.Text */ && (text += '|Text');
    tNodeType & 2 /* TNodeType.Element */ && (text += '|Element');
    tNodeType & 4 /* TNodeType.Container */ && (text += '|Container');
    tNodeType & 8 /* TNodeType.ElementContainer */ && (text += '|ElementContainer');
    tNodeType & 16 /* TNodeType.Projection */ && (text += '|Projection');
    tNodeType & 32 /* TNodeType.Icu */ && (text += '|IcuContainer');
    tNodeType & 64 /* TNodeType.Placeholder */ && (text += '|Placeholder');
    tNodeType & 128 /* TNodeType.LetDeclaration */ && (text += '|LetDeclaration');
    return text.length > 0 ? text.substring(1) : text;
}
/**
 * Helper function to detect if a given value matches a `TNode` shape.
 *
 * The logic uses the `insertBeforeIndex` and its possible values as
 * a way to differentiate a TNode shape from other types of objects
 * within the `TView.data`. This is not a perfect check, but it can
 * be a reasonable differentiator, since we control the shapes of objects
 * within `TView.data`.
 */
export function isTNodeShape(value) {
    return (value != null &&
        typeof value === 'object' &&
        (value.insertBeforeIndex === null ||
            typeof value.insertBeforeIndex === 'number' ||
            Array.isArray(value.insertBeforeIndex)));
}
/**
 * Returns `true` if the `TNode` has a directive which has `@Input()` for `class` binding.
 *
 * ```
 * <div my-dir [class]="exp"></div>
 * ```
 * and
 * ```
 * @Directive({
 * })
 * class MyDirective {
 *   @Input()
 *   class: string;
 * }
 * ```
 *
 * In the above case it is necessary to write the reconciled styling information into the
 * directive's input.
 *
 * @param tNode
 */
export function hasClassInput(tNode) {
    return (tNode.flags & 8 /* TNodeFlags.hasClassInput */) !== 0;
}
/**
 * Returns `true` if the `TNode` has a directive which has `@Input()` for `style` binding.
 *
 * ```
 * <div my-dir [style]="exp"></div>
 * ```
 * and
 * ```
 * @Directive({
 * })
 * class MyDirective {
 *   @Input()
 *   class: string;
 * }
 * ```
 *
 * In the above case it is necessary to write the reconciled styling information into the
 * directive's input.
 *
 * @param tNode
 */
export function hasStyleInput(tNode) {
    return (tNode.flags & 16 /* TNodeFlags.hasStyleInput */) !== 0;
}
//# sourceMappingURL=data:application/json;base64,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