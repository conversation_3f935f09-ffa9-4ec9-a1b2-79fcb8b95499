{"version": 3, "file": "primeng-spinner.mjs", "sources": ["../../src/app/components/spinner/spinner.ts", "../../src/app/components/spinner/primeng-spinner.ts"], "sourcesContent": ["import { NgModule, Component, ElementRef, OnInit, Input, Output, EventEmitter, forwardRef, ViewChild, ChangeDetectorRef, ChangeDetectionStrategy, ViewEncapsulation, booleanAttribute, numberAttribute } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';\n\nexport const SPINNER_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Spinner),\n    multi: true\n};\n\n@Component({\n    selector: 'p-spinner',\n    template: `\n        <span class=\"ui-spinner ui-widget ui-corner-all\">\n            <input\n                #inputfield\n                type=\"text\"\n                [attr.id]=\"inputId\"\n                [value]=\"formattedValue || null\"\n                [attr.name]=\"name\"\n                [attr.aria-valumin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"value\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.size]=\"size\"\n                [attr.maxlength]=\"maxlength\"\n                [attr.tabindex]=\"tabindex\"\n                [attr.placeholder]=\"placeholder\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.required]=\"required\"\n                (keydown)=\"onInputKeydown($event)\"\n                (blur)=\"onInputBlur($event)\"\n                (input)=\"onInput($event)\"\n                (change)=\"onInputChange($event)\"\n                (focus)=\"onInputFocus($event)\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [ngClass]=\"'ui-spinner-input ui-inputtext ui-widget ui-state-default ui-corner-all'\"\n            />\n            <button\n                type=\"button\"\n                [ngClass]=\"{ 'ui-spinner-button ui-spinner-up ui-corner-tr ui-button ui-widget ui-state-default': true, 'ui-state-disabled': disabled }\"\n                [disabled]=\"disabled || readonly\"\n                tabindex=\"-1\"\n                [attr.readonly]=\"readonly\"\n                (mouseleave)=\"onUpButtonMouseleave($event)\"\n                (mousedown)=\"onUpButtonMousedown($event)\"\n                (mouseup)=\"onUpButtonMouseup($event)\"\n            >\n                <span class=\"ui-spinner-button-icon pi pi-caret-up ui-clickable\"></span>\n            </button>\n            <button\n                type=\"button\"\n                [ngClass]=\"{ 'ui-spinner-button ui-spinner-down ui-corner-br ui-button ui-widget ui-state-default': true, 'ui-state-disabled': disabled }\"\n                [disabled]=\"disabled || readonly\"\n                tabindex=\"-1\"\n                [attr.readonly]=\"readonly\"\n                (mouseleave)=\"onDownButtonMouseleave($event)\"\n                (mousedown)=\"onDownButtonMousedown($event)\"\n                (mouseup)=\"onDownButtonMouseup($event)\"\n            >\n                <span class=\"ui-spinner-button-icon pi pi-caret-down ui-clickable\"></span>\n            </button>\n        </span>\n    `,\n    host: {\n        class: 'p-element',\n        '[class.ui-inputwrapper-filled]': 'filled',\n        '[class.ui-inputwrapper-focus]': 'focus'\n    },\n    providers: [SPINNER_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./spinner.css']\n})\nexport class Spinner implements OnInit, ControlValueAccessor {\n    @Output() onChange: EventEmitter<any> = new EventEmitter();\n\n    @Output() onFocus: EventEmitter<any> = new EventEmitter();\n\n    @Output() onBlur: EventEmitter<any> = new EventEmitter();\n\n    @Input({ transform: numberAttribute }) min: number;\n\n    @Input({ transform: numberAttribute }) max: number;\n\n    @Input({ transform: numberAttribute }) maxlength: number;\n\n    @Input({ transform: numberAttribute }) size: number;\n\n    @Input() placeholder: string;\n\n    @Input() inputId: string;\n\n    @Input({ transform: booleanAttribute }) disabled: boolean;\n\n    @Input({ transform: booleanAttribute }) readonly: boolean;\n\n    @Input({ transform: numberAttribute }) tabindex: number;\n\n    @Input({ transform: booleanAttribute }) required: boolean;\n\n    @Input() name: string;\n\n    @Input() ariaLabelledBy: string;\n\n    @Input() inputStyle: any;\n\n    @Input() inputStyleClass: string;\n\n    @Input({ transform: booleanAttribute }) formatInput: boolean;\n\n    @Input() decimalSeparator: string;\n\n    @Input() thousandSeparator: string;\n\n    @Input({ transform: numberAttribute }) precision: number;\n\n    value: any;\n\n    _step: number = 1;\n\n    formattedValue: string;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    keyPattern: RegExp = /[0-9\\+\\-]/;\n\n    public timer: any;\n\n    public focus: boolean;\n\n    public filled: boolean;\n\n    public negativeSeparator = '-';\n\n    localeDecimalSeparator: string;\n\n    localeThousandSeparator: string;\n\n    thousandRegExp: RegExp;\n\n    calculatedPrecision: number;\n\n    @ViewChild('inputfield') inputfieldViewChild: ElementRef;\n\n    @Input() get step(): number {\n        return this._step;\n    }\n    set step(val: number) {\n        this._step = val;\n\n        if (this._step != null) {\n            let tokens = this.step.toString().split(/[,]|[.]/);\n            this.calculatedPrecision = tokens[1] ? tokens[1].length : undefined;\n        }\n    }\n\n    constructor(public el: ElementRef, public cd: ChangeDetectorRef) {}\n\n    ngOnInit() {\n        if (this.formatInput) {\n            this.localeDecimalSeparator = (1.1).toLocaleString().substring(1, 2);\n            this.localeThousandSeparator = (1000).toLocaleString().substring(1, 2);\n            this.thousandRegExp = new RegExp(`[${this.thousandSeparator || this.localeThousandSeparator}]`, 'gim');\n\n            if (this.decimalSeparator && this.thousandSeparator && this.decimalSeparator === this.thousandSeparator) {\n                console.warn('thousandSeparator and decimalSeparator cannot have the same value.');\n            }\n        }\n    }\n\n    repeat(event: Event, interval: number, dir: number) {\n        let i = interval || 500;\n\n        this.clearTimer();\n        this.timer = setTimeout(() => {\n            this.repeat(event, 40, dir);\n        }, i);\n\n        this.spin(event, dir);\n    }\n\n    spin(event: Event, dir: number) {\n        let step = this.step * dir;\n        let currentValue: number;\n        let precision = this.getPrecision();\n\n        if (this.value) currentValue = typeof this.value === 'string' ? this.parseValue(this.value) : this.value;\n        else currentValue = 0;\n\n        if (precision) this.value = parseFloat(this.toFixed(currentValue + step, precision));\n        else this.value = currentValue + step;\n\n        if (this.maxlength !== undefined && this.value.toString().length > this.maxlength) {\n            this.value = currentValue;\n        }\n\n        if (this.min !== undefined && this.value < this.min) {\n            this.value = this.min;\n        }\n\n        if (this.max !== undefined && this.value > this.max) {\n            this.value = this.max;\n        }\n\n        this.formatValue();\n        this.onModelChange(this.value);\n        this.onChange.emit(event);\n    }\n\n    getPrecision() {\n        return this.precision === undefined ? this.calculatedPrecision : this.precision;\n    }\n\n    toFixed(value: number, precision: number) {\n        let power = Math.pow(10, precision || 0);\n        return String(Math.round(value * power) / power);\n    }\n\n    onUpButtonMousedown(event: Event) {\n        if (!this.disabled) {\n            this.inputfieldViewChild.nativeElement.focus();\n            this.repeat(event, null, 1);\n            this.updateFilledState();\n            event.preventDefault();\n        }\n    }\n\n    onUpButtonMouseup(event: Event) {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onUpButtonMouseleave(event: Event) {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onDownButtonMousedown(event: Event) {\n        if (!this.disabled) {\n            this.inputfieldViewChild.nativeElement.focus();\n            this.repeat(event, null, -1);\n            this.updateFilledState();\n            event.preventDefault();\n        }\n    }\n\n    onDownButtonMouseup(event: Event) {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onDownButtonMouseleave(event: Event) {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onInputKeydown(event: KeyboardEvent) {\n        if (event.which == 38) {\n            this.spin(event, 1);\n            event.preventDefault();\n        } else if (event.which == 40) {\n            this.spin(event, -1);\n            event.preventDefault();\n        }\n    }\n\n    onInputChange(event: Event) {\n        this.onChange.emit(event);\n    }\n\n    onInput(event: KeyboardEvent) {\n        this.value = this.parseValue((<HTMLInputElement>event.target).value);\n        this.onModelChange(this.value);\n        this.updateFilledState();\n    }\n\n    onInputBlur(event) {\n        this.focus = false;\n        this.formatValue();\n        this.onModelTouched();\n        this.onBlur.emit(event);\n    }\n\n    onInputFocus(event) {\n        this.focus = true;\n        this.onFocus.emit(event);\n    }\n\n    parseValue(val: string): number {\n        let value: number;\n        let precision = this.getPrecision();\n\n        if (val.trim() === '') {\n            value = null;\n        } else {\n            if (this.formatInput) {\n                val = val.replace(this.thousandRegExp, '');\n            }\n\n            if (precision) {\n                val = this.formatInput ? val.replace(this.decimalSeparator || this.localeDecimalSeparator, '.') : val.replace(',', '.');\n                value = parseFloat(val);\n            } else {\n                value = parseInt(val, 10);\n            }\n\n            if (!isNaN(value)) {\n                if (this.max !== null && value > this.max) {\n                    value = this.max;\n                }\n\n                if (this.min !== null && value < this.min) {\n                    value = this.min;\n                }\n            } else {\n                value = null;\n            }\n        }\n\n        return value;\n    }\n\n    formatValue() {\n        let value = this.value;\n        let precision = this.getPrecision();\n\n        if (value != null) {\n            if (this.formatInput) {\n                value = value.toLocaleString(undefined, { maximumFractionDigits: 20 });\n\n                if (this.decimalSeparator && this.thousandSeparator) {\n                    value = value.split(this.localeDecimalSeparator);\n\n                    if (precision && value[1]) {\n                        value[1] = (this.decimalSeparator || this.localeDecimalSeparator) + value[1];\n                    }\n\n                    if (this.thousandSeparator && value[0].length > 3) {\n                        value[0] = value[0].replace(new RegExp(`[${this.localeThousandSeparator}]`, 'gim'), this.thousandSeparator);\n                    }\n\n                    value = value.join('');\n                }\n            }\n\n            this.formattedValue = value.toString();\n        } else {\n            this.formattedValue = null;\n        }\n\n        if (this.inputfieldViewChild && this.inputfieldViewChild.nativeElement) {\n            this.inputfieldViewChild.nativeElement.value = this.formattedValue;\n        }\n    }\n\n    clearTimer() {\n        if (this.timer) {\n            clearInterval(this.timer);\n        }\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.formatValue();\n        this.updateFilledState();\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    updateFilledState() {\n        this.filled = this.value !== undefined && this.value != null;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, InputTextModule],\n    exports: [Spinner],\n    declarations: [Spinner]\n})\nexport class SpinnerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;AAKa,MAAA,sBAAsB,GAAQ;AACvC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,OAAO,CAAC;AACtC,IAAA,KAAK,EAAE,IAAI;EACb;MAoEW,OAAO,CAAA;AAqFG,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AApFhC,IAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEjD,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEhD,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElB,IAAA,GAAG,CAAS;AAEZ,IAAA,GAAG,CAAS;AAEZ,IAAA,SAAS,CAAS;AAElB,IAAA,IAAI,CAAS;AAE3C,IAAA,WAAW,CAAS;AAEpB,IAAA,OAAO,CAAS;AAEe,IAAA,QAAQ,CAAU;AAElB,IAAA,QAAQ,CAAU;AAEnB,IAAA,QAAQ,CAAS;AAEhB,IAAA,QAAQ,CAAU;AAEjD,IAAA,IAAI,CAAS;AAEb,IAAA,cAAc,CAAS;AAEvB,IAAA,UAAU,CAAM;AAEhB,IAAA,eAAe,CAAS;AAEO,IAAA,WAAW,CAAU;AAEpD,IAAA,gBAAgB,CAAS;AAEzB,IAAA,iBAAiB,CAAS;AAEI,IAAA,SAAS,CAAS;AAEzD,IAAA,KAAK,CAAM;IAEX,KAAK,GAAW,CAAC,CAAC;AAElB,IAAA,cAAc,CAAS;AAEvB,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;IAEpC,UAAU,GAAW,WAAW,CAAC;AAE1B,IAAA,KAAK,CAAM;AAEX,IAAA,KAAK,CAAU;AAEf,IAAA,MAAM,CAAU;IAEhB,iBAAiB,GAAG,GAAG,CAAC;AAE/B,IAAA,sBAAsB,CAAS;AAE/B,IAAA,uBAAuB,CAAS;AAEhC,IAAA,cAAc,CAAS;AAEvB,IAAA,mBAAmB,CAAS;AAEH,IAAA,mBAAmB,CAAa;AAEzD,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;IACD,IAAI,IAAI,CAAC,GAAW,EAAA;AAChB,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AAEjB,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;AACpB,YAAA,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,SAAS,CAAC;AACvE,SAAA;KACJ;IAED,WAAmB,CAAA,EAAc,EAAS,EAAqB,EAAA;QAA5C,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;IAEnE,QAAQ,GAAA;QACJ,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,IAAI,CAAC,sBAAsB,GAAG,CAAC,GAAG,EAAE,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrE,YAAA,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvE,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,uBAAuB,GAAG,EAAE,KAAK,CAAC,CAAC;AAEvG,YAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,iBAAiB,EAAE;AACrG,gBAAA,OAAO,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;AACtF,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAY,EAAE,QAAgB,EAAE,GAAW,EAAA;AAC9C,QAAA,IAAI,CAAC,GAAG,QAAQ,IAAI,GAAG,CAAC;QAExB,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,MAAK;YACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;SAC/B,EAAE,CAAC,CAAC,CAAC;AAEN,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACzB;IAED,IAAI,CAAC,KAAY,EAAE,GAAW,EAAA;AAC1B,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AAC3B,QAAA,IAAI,YAAoB,CAAC;AACzB,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,KAAK;YAAE,YAAY,GAAG,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;;YACpG,YAAY,GAAG,CAAC,CAAC;AAEtB,QAAA,IAAI,SAAS;AAAE,YAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;;AAChF,YAAA,IAAI,CAAC,KAAK,GAAG,YAAY,GAAG,IAAI,CAAC;AAEtC,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;AAC/E,YAAA,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;AAC7B,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE;AACjD,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE;AACjD,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,SAAA;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC7B;IAED,YAAY,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC;KACnF;IAED,OAAO,CAAC,KAAa,EAAE,SAAiB,EAAA;AACpC,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;AACzC,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;KACpD;AAED,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,KAAY,EAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,qBAAqB,CAAC,KAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,sBAAsB,CAAC,KAAY,EAAA;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE;AACnB,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACpB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,KAAY,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC7B;AAED,IAAA,OAAO,CAAC,KAAoB,EAAA;AACxB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAoB,KAAK,CAAC,MAAO,CAAC,KAAK,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,UAAU,CAAC,GAAW,EAAA;AAClB,QAAA,IAAI,KAAa,CAAC;AAClB,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAEpC,QAAA,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnB,KAAK,GAAG,IAAI,CAAC;AAChB,SAAA;AAAM,aAAA;YACH,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;AAC9C,aAAA;AAED,YAAA,IAAI,SAAS,EAAE;AACX,gBAAA,GAAG,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,sBAAsB,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxH,gBAAA,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AAC3B,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAC7B,aAAA;AAED,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACf,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE;AACvC,oBAAA,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;AACpB,iBAAA;gBAED,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE;AACvC,oBAAA,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;AACpB,iBAAA;AACJ,aAAA;AAAM,iBAAA;gBACH,KAAK,GAAG,IAAI,CAAC;AAChB,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,WAAW,GAAA;AACP,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvB,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpC,IAAI,KAAK,IAAI,IAAI,EAAE;YACf,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,gBAAA,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,qBAAqB,EAAE,EAAE,EAAE,CAAC,CAAC;AAEvE,gBAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACjD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AAEjD,oBAAA,IAAI,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AACvB,wBAAA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,sBAAsB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AAChF,qBAAA;AAED,oBAAA,IAAI,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;wBAC/C,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,CAAA,CAAA,EAAI,IAAI,CAAC,uBAAuB,CAAA,CAAA,CAAG,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC/G,qBAAA;AAED,oBAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1B,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC1C,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;QAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE;YACpE,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;AACtE,SAAA;KACJ;IAED,UAAU,GAAA;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7B,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;KAChE;uGA5TQ,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAP,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAOI,eAAe,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAEf,eAAe,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAEf,eAAe,CAEf,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,CAMf,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAEhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,sCAEhB,eAAe,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAEf,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAUhB,gBAAgB,CAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAMhB,eAAe,CA9CxB,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8BAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,sBAAsB,CAAC,EA3DzB,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,yuBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAWQ,OAAO,EAAA,UAAA,EAAA,CAAA;kBAlEnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqDT,EACK,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,gCAAgC,EAAE,QAAQ;AAC1C,wBAAA,+BAA+B,EAAE,OAAO;qBAC3C,EACU,SAAA,EAAA,CAAC,sBAAsB,CAAC,EAClB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,yuBAAA,CAAA,EAAA,CAAA;+GAI3B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAEG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEgC,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEE,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEE,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEE,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAEkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAEG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAEkC,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAEG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAEiC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBA8BZ,mBAAmB,EAAA,CAAA;sBAA3C,SAAS;uBAAC,YAAY,CAAA;gBAEV,IAAI,EAAA,CAAA;sBAAhB,KAAK;;MA2PG,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,iBApUb,OAAO,CAAA,EAAA,OAAA,EAAA,CAgUN,YAAY,EAAE,eAAe,aAhU9B,OAAO,CAAA,EAAA,CAAA,CAAA;wGAoUP,aAAa,EAAA,OAAA,EAAA,CAJZ,YAAY,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA;;2FAI9B,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC;oBACxC,OAAO,EAAE,CAAC,OAAO,CAAC;oBAClB,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;AChZD;;AAEG;;;;"}