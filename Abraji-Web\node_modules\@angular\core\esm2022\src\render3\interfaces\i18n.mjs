/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Marks that the next string is an element name.
 *
 * See `I18nMutateOpCodes` documentation.
 */
export const ELEMENT_MARKER = {
    marker: 'element',
};
/**
 * Marks that the next string is comment text need for ICU.
 *
 * See `I18nMutateOpCodes` documentation.
 */
export const ICU_MARKER = {
    marker: 'ICU',
};
/**
 * See `I18nCreateOpCodes`
 */
export var I18nCreateOpCode;
(function (I18nCreateOpCode) {
    /**
     * Number of bits to shift index so that it can be combined with the `APPEND_EAGERLY` and
     * `COMMENT`.
     */
    I18nCreateOpCode[I18nCreateOpCode["SHIFT"] = 2] = "SHIFT";
    /**
     * Should the node be appended to parent immediately after creation.
     */
    I18nCreateOpCode[I18nCreateOpCode["APPEND_EAGERLY"] = 1] = "APPEND_EAGERLY";
    /**
     * If set the node should be comment (rather than a text) node.
     */
    I18nCreateOpCode[I18nCreateOpCode["COMMENT"] = 2] = "COMMENT";
})(I18nCreateOpCode || (I18nCreateOpCode = {}));
//# sourceMappingURL=data:application/json;base64,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