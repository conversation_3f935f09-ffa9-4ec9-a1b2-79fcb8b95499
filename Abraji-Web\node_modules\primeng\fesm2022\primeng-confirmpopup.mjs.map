{"version": 3, "file": "primeng-confirmpopup.mjs", "sources": ["../../src/app/components/confirmpopup/confirmpopup.ts", "../../src/app/components/confirmpopup/primeng-confirmpopup.ts"], "sourcesContent": ["import { AnimationEvent, animate, state, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    HostListener,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { Confirmation, ConfirmationService, OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule, TranslationKeys } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { ConnectedOverlayScrollHandler, DomHandler } from 'primeng/dom';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { Subscription } from 'rxjs';\n/**\n * ConfirmPopup displays a confirmation overlay displayed relatively to its target.\n * @group Components\n */\n@Component({\n    selector: 'p-confirmPopup',\n    template: `\n        <div\n            *ngIf=\"visible\"\n            [ngClass]=\"'p-confirm-popup p-component'\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            role=\"alertdialog\"\n            (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{ value: 'open', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            (@animation.start)=\"onAnimationStart($event)\"\n            (@animation.done)=\"onAnimationEnd($event)\"\n        >\n            <ng-container *ngIf=\"headlessTemplate; else notHeadless\">\n                <ng-container *ngTemplateOutlet=\"headlessTemplate; context: { $implicit: confirmation }\"></ng-container>\n            </ng-container>\n            <ng-template #notHeadless>\n                <div #content class=\"p-confirm-popup-content\">\n                    <ng-container *ngIf=\"contentTemplate; else withoutContentTemplate\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: confirmation }\"></ng-container>\n                    </ng-container>\n                    <ng-template #withoutContentTemplate>\n                        <i [ngClass]=\"'p-confirm-popup-icon'\" [class]=\"confirmation?.icon\" *ngIf=\"confirmation?.icon\"></i>\n                        <span class=\"p-confirm-popup-message\">{{ confirmation?.message }}</span>\n                    </ng-template>\n                </div>\n                <div class=\"p-confirm-popup-footer\">\n                    <button\n                        type=\"button\"\n                        pButton\n                        [label]=\"rejectButtonLabel\"\n                        (click)=\"reject()\"\n                        [ngClass]=\"'p-confirm-popup-reject p-button-sm'\"\n                        [class]=\"confirmation?.rejectButtonStyleClass || 'p-button-text'\"\n                        *ngIf=\"confirmation?.rejectVisible !== false\"\n                        [attr.aria-label]=\"rejectButtonLabel\"\n                    >\n                        <i [class]=\"confirmation?.rejectIcon\" *ngIf=\"confirmation?.rejectIcon; else rejecticon\"></i>\n                        <ng-template #rejecticon *ngTemplateOutlet=\"rejectIconTemplate\"></ng-template>\n                    </button>\n                    <button\n                        type=\"button\"\n                        pButton\n                        [label]=\"acceptButtonLabel\"\n                        (click)=\"accept()\"\n                        [ngClass]=\"'p-confirm-popup-accept p-button-sm'\"\n                        [class]=\"confirmation?.acceptButtonStyleClass\"\n                        *ngIf=\"confirmation?.acceptVisible !== false\"\n                        [attr.aria-label]=\"acceptButtonLabel\"\n                    >\n                        <i [class]=\"confirmation?.acceptIcon\" *ngIf=\"confirmation?.acceptIcon; else accepticon\"></i>\n                        <ng-template #accepticon *ngTemplateOutlet=\"acceptIconTemplate\"></ng-template>\n                    </button>\n                </div>\n            </ng-template>\n        </div>\n    `,\n    animations: [\n        trigger('animation', [\n            state(\n                'void',\n                style({\n                    transform: 'scaleY(0.8)',\n                    opacity: 0\n                })\n            ),\n            state(\n                'open',\n                style({\n                    transform: 'translateY(0)',\n                    opacity: 1\n                })\n            ),\n            transition('void => open', animate('{{showTransitionParams}}')),\n            transition('open => void', animate('{{hideTransitionParams}}'))\n        ])\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./confirmpopup.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ConfirmPopup implements AfterContentInit, OnDestroy {\n    /**\n     * Optional key to match the key of confirm object, necessary to use when component tree has multiple confirm dialogs.\n     * @group Props\n     */\n    @Input() key: string | undefined;\n    /**\n     * Element to receive the focus when the popup gets visible, valid values are \"accept\", \"reject\", and \"none\".\n     * @group Props\n     */\n    @Input() defaultFocus: string = 'accept';\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.1s linear';\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Defines if the component is visible.\n     * @group Props\n     */\n    @Input() get visible(): any {\n        return this._visible;\n    }\n    set visible(value: any) {\n        this._visible = value;\n        this.cd.markForCheck();\n    }\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    container: Nullable<HTMLDivElement>;\n\n    subscription: Subscription;\n\n    confirmation: Nullable<Confirmation>;\n\n    contentTemplate: Nullable<TemplateRef<any>>;\n\n    acceptIconTemplate: Nullable<TemplateRef<any>>;\n\n    rejectIconTemplate: Nullable<TemplateRef<any>>;\n\n    headlessTemplate: Nullable<TemplateRef<any>>;\n\n    _visible: boolean | undefined;\n\n    documentClickListener: VoidListener;\n\n    documentResizeListener: VoidListener;\n\n    scrollHandler: Nullable<ConnectedOverlayScrollHandler>;\n\n    private window: Window;\n\n    constructor(\n        public el: ElementRef,\n        private confirmationService: ConfirmationService,\n        public renderer: Renderer2,\n        private cd: ChangeDetectorRef,\n        public config: PrimeNGConfig,\n        public overlayService: OverlayService,\n        @Inject(DOCUMENT) private document: Document\n    ) {\n        this.window = this.document.defaultView as Window;\n        this.subscription = this.confirmationService.requireConfirmation$.subscribe((confirmation) => {\n            if (!confirmation) {\n                this.hide();\n                return;\n            }\n\n            if (confirmation.key === this.key) {\n                this.confirmation = confirmation;\n                if (this.confirmation.accept) {\n                    this.confirmation.acceptEvent = new EventEmitter();\n                    this.confirmation.acceptEvent.subscribe(this.confirmation.accept);\n                }\n\n                if (this.confirmation.reject) {\n                    this.confirmation.rejectEvent = new EventEmitter();\n                    this.confirmation.rejectEvent.subscribe(this.confirmation.reject);\n                }\n\n                this.visible = true;\n            }\n        });\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'rejecticon':\n                    this.rejectIconTemplate = item.template;\n                    break;\n\n                case 'accepticon':\n                    this.acceptIconTemplate = item.template;\n                    break;\n\n                case 'headless':\n                    this.headlessTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    @HostListener('document:keydown.escape', ['$event'])\n    onEscapeKeydown(event: KeyboardEvent) {\n        if (this.confirmation && this.confirmation.closeOnEscape) {\n            this.reject();\n        }\n    }\n\n    onAnimationStart(event: AnimationEvent) {\n        if (event.toState === 'open') {\n            this.container = event.element;\n            this.renderer.appendChild(this.document.body, this.container);\n            this.align();\n            this.bindListeners();\n\n            const element = this.getElementToFocus();\n            if (element) {\n                element.focus();\n            }\n        }\n    }\n\n    onAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                this.onContainerDestroy();\n                break;\n        }\n    }\n\n    getElementToFocus() {\n        switch (this.defaultFocus) {\n            case 'accept':\n                return DomHandler.findSingle(this.container, '.p-confirm-popup-accept');\n\n            case 'reject':\n                return DomHandler.findSingle(this.container, '.p-confirm-popup-reject');\n\n            case 'none':\n                return null;\n        }\n    }\n\n    align() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('overlay', this.container, this.config.zIndex.overlay);\n        }\n\n        if (!this.confirmation) {\n            return;\n        }\n        DomHandler.absolutePosition(this.container, this.confirmation?.target, false);\n\n        const containerOffset = DomHandler.getOffset(this.container);\n        const targetOffset = DomHandler.getOffset(this.confirmation?.target);\n        let arrowLeft = 0;\n\n        if (containerOffset.left < targetOffset.left) {\n            arrowLeft = targetOffset.left - containerOffset.left;\n        }\n        (this.container as HTMLDivElement).style.setProperty('--overlayArrowLeft', `${arrowLeft}px`);\n\n        if (containerOffset.top < targetOffset.top) {\n            DomHandler.addClass(this.container, 'p-confirm-popup-flipped');\n        }\n    }\n\n    hide() {\n        this.visible = false;\n    }\n\n    accept() {\n        if (this.confirmation?.acceptEvent) {\n            this.confirmation.acceptEvent.emit();\n        }\n\n        this.hide();\n    }\n\n    reject() {\n        if (this.confirmation?.rejectEvent) {\n            this.confirmation.rejectEvent.emit();\n        }\n\n        this.hide();\n    }\n\n    onOverlayClick(event: MouseEvent) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n    }\n\n    bindListeners(): void {\n        /*\n         * Called inside `setTimeout` to avoid listening to the click event that appears when `confirm` is first called(bubbling).\n         * Need wait when bubbling event up and hang the handler on the next tick.\n         * This is the case when eventTarget and confirmation.target do not match when the `confirm` method is called.\n         */\n        setTimeout(() => {\n            this.bindDocumentClickListener();\n            this.bindDocumentResizeListener();\n            this.bindScrollListener();\n        });\n    }\n\n    unbindListeners() {\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n    }\n\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            let documentEvent = DomHandler.isIOS() ? 'touchstart' : 'click';\n            const documentTarget: any = this.el ? this.el.nativeElement.ownerDocument : this.document;\n\n            this.documentClickListener = this.renderer.listen(documentTarget, documentEvent, (event) => {\n                if (this.confirmation && this.confirmation.dismissableMask !== false) {\n                    let targetElement = <HTMLElement>this.confirmation.target;\n                    if (this.container !== event.target && !this.container?.contains(event.target) && targetElement !== event.target && !targetElement.contains(event.target)) {\n                        this.hide();\n                    }\n                }\n            });\n        }\n    }\n\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n\n    onWindowResize() {\n        if (this.visible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n\n    bindDocumentResizeListener() {\n        if (!this.documentResizeListener) {\n            this.documentResizeListener = this.renderer.listen(this.window, 'resize', this.onWindowResize.bind(this));\n        }\n    }\n\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.confirmation?.target, () => {\n                if (this.visible) {\n                    this.hide();\n                }\n            });\n        }\n\n        this.scrollHandler.bindScrollListener();\n    }\n\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n\n    unsubscribeConfirmationSubscriptions() {\n        if (this.confirmation) {\n            if (this.confirmation.acceptEvent) {\n                this.confirmation.acceptEvent.unsubscribe();\n            }\n\n            if (this.confirmation.rejectEvent) {\n                this.confirmation.rejectEvent.unsubscribe();\n            }\n        }\n    }\n\n    onContainerDestroy() {\n        this.unbindListeners();\n        this.unsubscribeConfirmationSubscriptions();\n\n        if (this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n\n        this.confirmation = null;\n        this.container = null;\n    }\n\n    restoreAppend() {\n        if (this.container) {\n            this.renderer.removeChild(this.document.body, this.container);\n        }\n\n        this.onContainerDestroy();\n    }\n\n    get acceptButtonLabel(): string {\n        return this.confirmation?.acceptLabel || this.config.getTranslation(TranslationKeys.ACCEPT);\n    }\n\n    get rejectButtonLabel(): string {\n        return this.confirmation?.rejectLabel || this.config.getTranslation(TranslationKeys.REJECT);\n    }\n\n    ngOnDestroy() {\n        this.restoreAppend();\n\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, ButtonModule, SharedModule],\n    exports: [ConfirmPopup, SharedModule],\n    declarations: [ConfirmPopup]\n})\nexport class ConfirmPopupModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;AA4BA;;;AAGG;MAsFU,YAAY,CAAA;AAgFV,IAAA,EAAA,CAAA;AACC,IAAA,mBAAA,CAAA;AACD,IAAA,QAAA,CAAA;AACC,IAAA,EAAA,CAAA;AACD,IAAA,MAAA,CAAA;AACA,IAAA,cAAA,CAAA;AACmB,IAAA,QAAA,CAAA;AArF9B;;;AAGG;AACM,IAAA,GAAG,CAAqB;AACjC;;;AAGG;IACM,YAAY,GAAW,QAAQ,CAAC;AACzC;;;AAGG;IACM,qBAAqB,GAAW,iCAAiC,CAAC;AAC3E;;;AAGG;IACM,qBAAqB,GAAW,YAAY,CAAC;AACtD;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAE+B,IAAA,SAAS,CAAuC;AAEhF,IAAA,SAAS,CAA2B;AAEpC,IAAA,YAAY,CAAe;AAE3B,IAAA,YAAY,CAAyB;AAErC,IAAA,eAAe,CAA6B;AAE5C,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,gBAAgB,CAA6B;AAE7C,IAAA,QAAQ,CAAsB;AAE9B,IAAA,qBAAqB,CAAe;AAEpC,IAAA,sBAAsB,CAAe;AAErC,IAAA,aAAa,CAA0C;AAE/C,IAAA,MAAM,CAAS;AAEvB,IAAA,WAAA,CACW,EAAc,EACb,mBAAwC,EACzC,QAAmB,EAClB,EAAqB,EACtB,MAAqB,EACrB,cAA8B,EACX,QAAkB,EAAA;QANrC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACb,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB,CAAqB;QACzC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAClB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACtB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACrB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QACX,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAE5C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;AAClD,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,YAAY,KAAI;YACzF,IAAI,CAAC,YAAY,EAAE;gBACf,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,OAAO;AACV,aAAA;AAED,YAAA,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE;AAC/B,gBAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,gBAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;oBAC1B,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,YAAY,EAAE,CAAC;AACnD,oBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACrE,iBAAA;AAED,gBAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;oBAC1B,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,YAAY,EAAE,CAAC;AACnD,oBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACrE,iBAAA;AAED,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACvB,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAGD,IAAA,eAAe,CAAC,KAAoB,EAAA;QAChC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;YACtD,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAqB,EAAA;AAClC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;AAC1B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;AAC/B,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9D,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,EAAE,CAAC;AAErB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzC,YAAA,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,KAAK,EAAE,CAAC;AACnB,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAqB,EAAA;QAChC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;AACb,SAAA;KACJ;IAED,iBAAiB,GAAA;QACb,QAAQ,IAAI,CAAC,YAAY;AACrB,YAAA,KAAK,QAAQ;gBACT,OAAO,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC;AAE5E,YAAA,KAAK,QAAQ;gBACT,OAAO,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC;AAE5E,YAAA,KAAK,MAAM;AACP,gBAAA,OAAO,IAAI,CAAC;AACnB,SAAA;KACJ;IAED,KAAK,GAAA;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO;AACV,SAAA;AACD,QAAA,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAE9E,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7D,QAAA,MAAM,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QACrE,IAAI,SAAS,GAAG,CAAC,CAAC;AAElB,QAAA,IAAI,eAAe,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,EAAE;YAC1C,SAAS,GAAG,YAAY,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC;AACxD,SAAA;AACA,QAAA,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAA,EAAG,SAAS,CAAA,EAAA,CAAI,CAAC,CAAC;AAE7F,QAAA,IAAI,eAAe,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE;YACxC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC;AAClE,SAAA;KACJ;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB;IAED,MAAM,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE;AAChC,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AACxC,SAAA;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;KACf;IAED,MAAM,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE;AAChC,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AACxC,SAAA;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;KACf;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa;AAChC,SAAA,CAAC,CAAC;KACN;IAED,aAAa,GAAA;AACT;;;;AAIG;QACH,UAAU,CAAC,MAAK;YACZ,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC9B,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;QACX,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;IAED,yBAAyB,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AAC7B,YAAA,IAAI,aAAa,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,GAAG,OAAO,CAAC;YAChE,MAAM,cAAc,GAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;AAE1F,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,aAAa,EAAE,CAAC,KAAK,KAAI;gBACvF,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,KAAK,KAAK,EAAE;AAClE,oBAAA,IAAI,aAAa,GAAgB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;AAC1D,oBAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,aAAa,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;wBACvJ,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AACJ,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,2BAA2B,GAAA;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;KACJ;IAED,cAAc,GAAA;QACV,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;YAC7C,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;KACJ;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7G,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,MAAK;gBACnF,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;KAC3C;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;AAC7C,SAAA;KACJ;IAED,oCAAoC,GAAA;QAChC,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;AAC/B,gBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC/C,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;AAC/B,gBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;AAC/C,aAAA;AACJ,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,oCAAoC,EAAE,CAAC;QAE5C,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB;IAED,aAAa,GAAA;QACT,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACjE,SAAA;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KAC/F;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KAC/F;IAED,WAAW,GAAA;QACP,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;AAhWQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,oMAsFT,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAtFX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,2NAyBD,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,eAAe,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,yBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAuBlB,aAAa,EAxIpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuDT,EACW,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,+uBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,WAAW,EAAE;AACjB,gBAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,oBAAA,SAAS,EAAE,aAAa;AACxB,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,oBAAA,SAAS,EAAE,eAAe;AAC1B,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA,CAAC,CACL;AACD,gBAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC/D,gBAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;aAClE,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,YAAY,EAAA,UAAA,EAAA,CAAA;kBArFxB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,gBAAgB,EAChB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuDT,EACW,UAAA,EAAA;wBACR,OAAO,CAAC,WAAW,EAAE;AACjB,4BAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,gCAAA,SAAS,EAAE,aAAa;AACxB,gCAAA,OAAO,EAAE,CAAC;AACb,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,gCAAA,SAAS,EAAE,eAAe;AAC1B,gCAAA,OAAO,EAAE,CAAC;AACb,6BAAA,CAAC,CACL;AACD,4BAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC/D,4BAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;yBAClE,CAAC;AACL,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,+uBAAA,CAAA,EAAA,CAAA;;0BAwFI,MAAM;2BAAC,QAAQ,CAAA;yCAjFX,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAQ0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAkF9B,eAAe,EAAA,CAAA;sBADd,YAAY;uBAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAkO1C,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAlB,kBAAkB,EAAA,YAAA,EAAA,CAxWlB,YAAY,CAAA,EAAA,OAAA,EAAA,CAoWX,YAAY,EAAE,YAAY,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CApWzC,YAAY,EAqWG,YAAY,CAAA,EAAA,CAAA,CAAA;AAG3B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAJjB,YAAY,EAAE,YAAY,EAAE,YAAY,EAC1B,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG3B,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;AACnD,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;oBACrC,YAAY,EAAE,CAAC,YAAY,CAAC;AAC/B,iBAAA,CAAA;;;AC5dD;;AAEG;;;;"}