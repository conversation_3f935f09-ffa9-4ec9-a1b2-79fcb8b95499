{"version": 3, "file": "primeng-button.mjs", "sources": ["../../src/app/components/button/button.ts", "../../src/app/components/button/primeng-button.ts"], "sourcesContent": ["import { DOCUMENT, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgT<PERSON>plateOutlet } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    Component,\n    ContentChildren,\n    Directive,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    QueryList,\n    TemplateRef,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { AutoFocus } from 'primeng/autofocus';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { Ripple } from 'primeng/ripple';\nimport { ObjectUtils } from 'primeng/utils';\n\ntype ButtonIconPosition = 'left' | 'right' | 'top' | 'bottom';\n\nconst INTERNAL_BUTTON_CLASSES = {\n    button: 'p-button',\n    component: 'p-component',\n    iconOnly: 'p-button-icon-only',\n    disabled: 'p-disabled',\n    loading: 'p-button-loading',\n    labelOnly: 'p-button-loading-label-only'\n} as const;\n\n/**\n * Button directive is an extension to button component.\n * @group Components\n */\n@Directive({\n    selector: '[pButton]',\n    standalone: true,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ButtonDirective implements AfterViewInit, OnDestroy {\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    @Input() iconPos: ButtonIconPosition = 'left';\n    /**\n     * Uses to pass attributes to the loading icon's DOM element.\n     * @group Props\n     */\n    @Input() loadingIcon: string | undefined;\n    /**\n     * Text of the button.\n     * @group Props\n     */\n    @Input() get label(): string | undefined {\n        return this._label as string;\n    }\n    set label(val: string) {\n        this._label = val;\n\n        if (this.initialized) {\n            this.updateLabel();\n            this.updateIcon();\n            this.setStyleClass();\n        }\n    }\n    /**\n     * Name of the icon.\n     * @group Props\n     */\n    @Input() get icon(): string {\n        return this._icon as string;\n    }\n    set icon(val: string) {\n        this._icon = val;\n\n        if (this.initialized) {\n            this.updateIcon();\n            this.setStyleClass();\n        }\n    }\n    /**\n     * Whether the button is in loading state.\n     * @group Props\n     */\n    @Input() get loading(): boolean {\n        return this._loading;\n    }\n    set loading(val: boolean) {\n        this._loading = val;\n\n        if (this.initialized) {\n            this.updateIcon();\n            this.setStyleClass();\n        }\n    }\n    /**\n     * Defines the style of the button.\n     * @group Props\n     */\n    @Input() severity: 'success' | 'info' | 'warning' | 'danger' | 'help' | 'primary' | 'secondary' | 'contrast' | null | undefined;\n    /**\n     * Add a shadow to indicate elevation.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) raised: boolean = false;\n    /**\n     * Add a circular border radius to the button.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) rounded: boolean = false;\n    /**\n     * Add a textual class to the button without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) text: boolean = false;\n    /**\n     * Add a border class without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) outlined: boolean = false;\n    /**\n     * Defines the size of the button.\n     * @group Props\n     */\n    @Input() size: 'small' | 'large' | undefined | null = null;\n    /**\n     * Add a plain textual class to the button without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) plain: boolean = false;\n\n    public _label: string | undefined;\n\n    public _icon: string | undefined;\n\n    public _loading: boolean = false;\n\n    public initialized: boolean | undefined;\n\n    private get htmlElement(): HTMLElement {\n        return this.el.nativeElement as HTMLElement;\n    }\n\n    private _internalClasses: string[] = Object.values(INTERNAL_BUTTON_CLASSES);\n\n    constructor(public el: ElementRef, @Inject(DOCUMENT) private document: Document) {}\n\n    ngAfterViewInit() {\n        DomHandler.addMultipleClasses(this.htmlElement, this.getStyleClass().join(' '));\n\n        this.createIcon();\n        this.createLabel();\n\n        this.initialized = true;\n    }\n\n    getStyleClass(): string[] {\n        const styleClass: string[] = [INTERNAL_BUTTON_CLASSES.button, INTERNAL_BUTTON_CLASSES.component];\n\n        if (this.icon && !this.label && ObjectUtils.isEmpty(this.htmlElement.textContent)) {\n            styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n        }\n\n        if (this.loading) {\n            styleClass.push(INTERNAL_BUTTON_CLASSES.disabled, INTERNAL_BUTTON_CLASSES.loading);\n\n            if (!this.icon && this.label) {\n                styleClass.push(INTERNAL_BUTTON_CLASSES.labelOnly);\n            }\n\n            if (this.icon && !this.label && !ObjectUtils.isEmpty(this.htmlElement.textContent)) {\n                styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n            }\n        }\n\n        if (this.text) {\n            styleClass.push('p-button-text');\n        }\n\n        if (this.severity) {\n            styleClass.push(`p-button-${this.severity}`);\n        }\n\n        if (this.plain) {\n            styleClass.push('p-button-plain');\n        }\n\n        if (this.raised) {\n            styleClass.push('p-button-raised');\n        }\n\n        if (this.size) {\n            styleClass.push(`p-button-${this.size}`);\n        }\n\n        if (this.outlined) {\n            styleClass.push('p-button-outlined');\n        }\n\n        if (this.rounded) {\n            styleClass.push('p-button-rounded');\n        }\n\n        if (this.size === 'small') {\n            styleClass.push('p-button-sm');\n        }\n\n        if (this.size === 'large') {\n            styleClass.push('p-button-lg');\n        }\n\n        return styleClass;\n    }\n\n    setStyleClass() {\n        const styleClass = this.getStyleClass();\n        this.htmlElement.classList.remove(...this._internalClasses);\n        this.htmlElement.classList.add(...styleClass);\n    }\n\n    createLabel() {\n        const created = DomHandler.findSingle(this.htmlElement, '.p-button-label');\n        if (!created && this.label) {\n            let labelElement = this.document.createElement('span');\n            if (this.icon && !this.label) {\n                labelElement.setAttribute('aria-hidden', 'true');\n            }\n\n            labelElement.className = 'p-button-label';\n            labelElement.appendChild(this.document.createTextNode(this.label));\n\n            this.htmlElement.appendChild(labelElement);\n        }\n    }\n\n    createIcon() {\n        const created = DomHandler.findSingle(this.htmlElement, '.p-button-icon');\n        if (!created && (this.icon || this.loading)) {\n            let iconElement = this.document.createElement('span');\n            iconElement.className = 'p-button-icon';\n            iconElement.setAttribute('aria-hidden', 'true');\n            let iconPosClass = this.label ? 'p-button-icon-' + this.iconPos : null;\n\n            if (iconPosClass) {\n                DomHandler.addClass(iconElement, iconPosClass);\n            }\n\n            let iconClass = this.getIconClass();\n\n            if (iconClass) {\n                DomHandler.addMultipleClasses(iconElement, iconClass);\n            }\n\n            this.htmlElement.insertBefore(iconElement, this.htmlElement.firstChild);\n        }\n    }\n\n    updateLabel() {\n        let labelElement = DomHandler.findSingle(this.htmlElement, '.p-button-label');\n\n        if (!this.label) {\n            labelElement && this.htmlElement.removeChild(labelElement);\n            return;\n        }\n\n        labelElement ? (labelElement.textContent = this.label) : this.createLabel();\n    }\n\n    updateIcon() {\n        let iconElement = DomHandler.findSingle(this.htmlElement, '.p-button-icon');\n        let labelElement = DomHandler.findSingle(this.htmlElement, '.p-button-label');\n\n        if (iconElement) {\n            if (this.iconPos) {\n                iconElement.className = 'p-button-icon ' + (labelElement ? 'p-button-icon-' + this.iconPos : '') + ' ' + this.getIconClass();\n            } else {\n                iconElement.className = 'p-button-icon ' + this.getIconClass();\n            }\n        } else {\n            this.createIcon();\n        }\n    }\n\n    getIconClass() {\n        return this.loading ? 'p-button-loading-icon pi-spin ' + (this.loadingIcon ?? 'pi pi-spinner') : this.icon || 'p-hidden';\n    }\n\n    ngOnDestroy() {\n        this.initialized = false;\n    }\n}\n/**\n * Button is an extension to standard button element with icons and theming.\n * @group Components\n */\n@Component({\n    selector: 'p-button',\n    standalone: true,\n    imports: [NgIf, NgTemplateOutlet, NgStyle, NgClass, Ripple, AutoFocus, SpinnerIcon],\n    template: `\n        <button\n            [attr.type]=\"type\"\n            [attr.aria-label]=\"ariaLabel\"\n            [ngStyle]=\"style\"\n            [disabled]=\"disabled || loading\"\n            [ngClass]=\"buttonClass\"\n            (click)=\"onClick.emit($event)\"\n            (focus)=\"onFocus.emit($event)\"\n            (blur)=\"onBlur.emit($event)\"\n            pRipple\n            [attr.data-pc-name]=\"'button'\"\n            [attr.data-pc-section]=\"'root'\"\n            [attr.tabindex]=\"tabindex\"\n            pAutoFocus\n            [autofocus]=\"autofocus\"\n        >\n            <ng-content></ng-content>\n            <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            <ng-container *ngIf=\"loading\">\n                <ng-container *ngIf=\"!loadingIconTemplate\">\n                    <span *ngIf=\"loadingIcon\" [ngClass]=\"iconClass()\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'loadingicon'\"></span>\n                    <SpinnerIcon *ngIf=\"!loadingIcon\" [styleClass]=\"spinnerIconClass()\" [spin]=\"true\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'loadingicon'\" />\n                </ng-container>\n                <ng-template [ngIf]=\"loadingIconTemplate\" *ngTemplateOutlet=\"loadingIconTemplate; context: { class: iconClass() }\"></ng-template>\n            </ng-container>\n            <ng-container *ngIf=\"!loading\">\n                <span *ngIf=\"icon && !iconTemplate\" [ngClass]=\"iconClass()\" [attr.data-pc-section]=\"'icon'\"></span>\n                <ng-template [ngIf]=\"!icon && iconTemplate\" *ngTemplateOutlet=\"iconTemplate; context: { class: iconClass() }\"></ng-template>\n            </ng-container>\n            <span class=\"p-button-label\" [attr.aria-hidden]=\"icon && !label\" *ngIf=\"!contentTemplate && label\" [attr.data-pc-section]=\"'label'\">{{ label }}</span>\n            <span [ngClass]=\"badgeStyleClass()\" [class]=\"badgeClass\" *ngIf=\"!contentTemplate && badge\" [attr.data-pc-section]=\"'badge'\">{{ badge }}</span>\n        </button>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element',\n        '[class.p-disabled]': 'disabled' || 'loading'\n    }\n})\nexport class Button implements AfterContentInit {\n    /**\n     * Type of the button.\n     * @group Props\n     */\n    @Input() type: string = 'button';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    @Input() iconPos: ButtonIconPosition = 'left';\n    /**\n     * Name of the icon.\n     * @group Props\n     */\n    @Input() icon: string | undefined;\n    /**\n     * Value of the badge.\n     * @group Props\n     */\n    @Input() badge: string | undefined;\n    /**\n     * Uses to pass attributes to the label's DOM element.\n     * @group Props\n     */\n    @Input() label: string | undefined;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Whether the button is in loading state.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) loading: boolean = false;\n    /**\n     * Icon to display in loading state.\n     * @group Props\n     */\n    @Input() loadingIcon: string | undefined;\n    /**\n     * Add a shadow to indicate elevation.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) raised: boolean = false;\n    /**\n     * Add a circular border radius to the button.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) rounded: boolean = false;\n    /**\n     * Add a textual class to the button without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) text: boolean = false;\n    /**\n     * Add a plain textual class to the button without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) plain: boolean = false;\n    /**\n     * Defines the style of the button.\n     * @group Props\n     */\n    @Input() severity: 'success' | 'info' | 'warning' | 'danger' | 'help' | 'primary' | 'secondary' | 'contrast' | null | undefined;\n    /**\n     * Add a border class without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) outlined: boolean = false;\n    /**\n     * Add a link style to the button.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) link: boolean = false;\n    /**\n     * Add a tabindex to the button.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n    /**\n     * Defines the size of the button.\n     * @group Props\n     */\n    @Input() size: 'small' | 'large' | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Style class of the badge.\n     * @group Props\n     */\n    @Input() badgeClass: string | undefined;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Callback to execute when button is clicked.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (click).\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    @Output() onClick: EventEmitter<MouseEvent> = new EventEmitter();\n    /**\n     * Callback to execute when button is focused.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (focus).\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n    /**\n     * Callback to execute when button loses focus.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (blur).\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    loadingIconTemplate: TemplateRef<any> | undefined;\n\n    iconTemplate: TemplateRef<any> | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    constructor(public el: ElementRef) {}\n\n    spinnerIconClass(): string {\n        return Object.entries(this.iconClass())\n            .filter(([, value]) => !!value)\n            .reduce((acc, [key]) => acc + ` ${key}`, 'p-button-loading-icon');\n    }\n\n    iconClass() {\n        const iconClasses = {\n            'p-button-icon': true,\n            'p-button-icon-left': this.iconPos === 'left' && this.label,\n            'p-button-icon-right': this.iconPos === 'right' && this.label,\n            'p-button-icon-top': this.iconPos === 'top' && this.label,\n            'p-button-icon-bottom': this.iconPos === 'bottom' && this.label\n        };\n\n        if (this.loading) {\n            iconClasses[`p-button-loading-icon pi-spin ${this.loadingIcon ?? ''}`] = true;\n        } else if (this.icon) {\n            iconClasses[this.icon] = true;\n        }\n\n        return iconClasses;\n    }\n\n    get buttonClass() {\n        return {\n            'p-button p-component': true,\n            'p-button-icon-only': (this.icon || this.iconTemplate || this.loadingIcon || this.loadingIconTemplate) && !this.label,\n            'p-button-vertical': (this.iconPos === 'top' || this.iconPos === 'bottom') && this.label,\n            'p-button-loading': this.loading,\n            'p-button-loading-label-only': this.loading && !this.icon && this.label && !this.loadingIcon && this.iconPos === 'left',\n            'p-button-link': this.link,\n            [`p-button-${this.severity}`]: this.severity,\n            'p-button-raised': this.raised,\n            'p-button-rounded': this.rounded,\n            'p-button-text': this.text,\n            'p-button-outlined': this.outlined,\n            'p-button-sm': this.size === 'small',\n            'p-button-lg': this.size === 'large',\n            'p-button-plain': this.plain,\n            [`${this.styleClass}`]: this.styleClass\n        };\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n\n                case 'loadingicon':\n                    this.loadingIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    badgeStyleClass() {\n        return {\n            'p-badge p-component': true,\n            'p-badge-no-gutter': this.badge && String(this.badge).length === 1\n        };\n    }\n\n    /**\n     * Applies focus.\n     * @group Method\n     */\n    public focus() {\n        this.el.nativeElement.firstChild.focus();\n    }\n}\n\n@NgModule({\n    imports: [ButtonDirective, Button],\n    exports: [ButtonDirective, Button, SharedModule]\n})\nexport class ButtonModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;AA8BA,MAAM,uBAAuB,GAAG;AAC5B,IAAA,MAAM,EAAE,UAAU;AAClB,IAAA,SAAS,EAAE,aAAa;AACxB,IAAA,QAAQ,EAAE,oBAAoB;AAC9B,IAAA,QAAQ,EAAE,YAAY;AACtB,IAAA,OAAO,EAAE,kBAAkB;AAC3B,IAAA,SAAS,EAAE,6BAA6B;CAClC,CAAC;AAEX;;;AAGG;MAQU,eAAe,CAAA;AA2GL,IAAA,EAAA,CAAA;AAA0C,IAAA,QAAA,CAAA;AA1G7D;;;AAGG;IACM,OAAO,GAAuB,MAAM,CAAC;AAC9C;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACH,IAAA,IAAa,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAgB,CAAC;KAChC;IACD,IAAI,KAAK,CAAC,GAAW,EAAA;AACjB,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;QAElB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,SAAA;KACJ;AACD;;;AAGG;AACH,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAe,CAAC;KAC/B;IACD,IAAI,IAAI,CAAC,GAAW,EAAA;AAChB,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QAEjB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,SAAA;KACJ;AACD;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,GAAY,EAAA;AACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QAEpB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,SAAA;KACJ;AACD;;;AAGG;AACM,IAAA,QAAQ,CAA+G;AAChI;;;AAGG;IACqC,MAAM,GAAY,KAAK,CAAC;AAChE;;;AAGG;IACqC,OAAO,GAAY,KAAK,CAAC;AACjE;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACM,IAAI,GAAyC,IAAI,CAAC;AAC3D;;;AAGG;IACqC,KAAK,GAAY,KAAK,CAAC;AAExD,IAAA,MAAM,CAAqB;AAE3B,IAAA,KAAK,CAAqB;IAE1B,QAAQ,GAAY,KAAK,CAAC;AAE1B,IAAA,WAAW,CAAsB;AAExC,IAAA,IAAY,WAAW,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,aAA4B,CAAC;KAC/C;AAEO,IAAA,gBAAgB,GAAa,MAAM,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;IAE5E,WAAmB,CAAA,EAAc,EAA4B,QAAkB,EAAA;QAA5D,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAA4B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;KAAI;IAEnF,eAAe,GAAA;AACX,QAAA,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,WAAW,EAAE,CAAC;AAEnB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;IAED,aAAa,GAAA;QACT,MAAM,UAAU,GAAa,CAAC,uBAAuB,CAAC,MAAM,EAAE,uBAAuB,CAAC,SAAS,CAAC,CAAC;AAEjG,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;AAC/E,YAAA,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AACrD,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAEnF,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AAC1B,gBAAA,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;AACtD,aAAA;YAED,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;AAChF,gBAAA,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AACrD,aAAA;AACJ,SAAA;QAED,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,YAAA,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACpC,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,UAAU,CAAC,IAAI,CAAC,CAAA,SAAA,EAAY,IAAI,CAAC,QAAQ,CAAE,CAAA,CAAC,CAAC;AAChD,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,YAAA,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACtC,SAAA;QAED,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,UAAU,CAAC,IAAI,CAAC,CAAA,SAAA,EAAY,IAAI,CAAC,IAAI,CAAE,CAAA,CAAC,CAAC;AAC5C,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACxC,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvB,YAAA,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAClC,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvB,YAAA,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAClC,SAAA;AAED,QAAA,OAAO,UAAU,CAAC;KACrB;IAED,aAAa,GAAA;AACT,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AACxC,QAAA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;KACjD;IAED,WAAW,GAAA;AACP,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;YACxB,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACvD,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AAC1B,gBAAA,YAAY,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACpD,aAAA;AAED,YAAA,YAAY,CAAC,SAAS,GAAG,gBAAgB,CAAC;AAC1C,YAAA,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAEnE,YAAA,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AAC9C,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AAC1E,QAAA,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;YACzC,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACtD,YAAA,WAAW,CAAC,SAAS,GAAG,eAAe,CAAC;AACxC,YAAA,WAAW,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAChD,YAAA,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,gBAAgB,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAEvE,YAAA,IAAI,YAAY,EAAE;AACd,gBAAA,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAClD,aAAA;AAED,YAAA,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAEpC,YAAA,IAAI,SAAS,EAAE;AACX,gBAAA,UAAU,CAAC,kBAAkB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AACzD,aAAA;AAED,YAAA,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC3E,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAE9E,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAC3D,OAAO;AACV,SAAA;AAED,QAAA,YAAY,IAAI,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;KAC/E;IAED,UAAU,GAAA;AACN,QAAA,IAAI,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AAC5E,QAAA,IAAI,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAE9E,QAAA,IAAI,WAAW,EAAE;YACb,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,WAAW,CAAC,SAAS,GAAG,gBAAgB,IAAI,YAAY,GAAG,gBAAgB,GAAG,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAChI,aAAA;AAAM,iBAAA;gBACH,WAAW,CAAC,SAAS,GAAG,gBAAgB,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAClE,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;IAED,YAAY,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,GAAG,gCAAgC,IAAI,IAAI,CAAC,WAAW,IAAI,eAAe,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;KAC5H;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC5B;AA3PQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,4CA2GmB,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA3G1C,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAkEJ,gBAAgB,CAKhB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,0BAKhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAUhB,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FA3F3B,eAAe,EAAA,UAAA,EAAA,CAAA;kBAP3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BA4GuC,MAAM;2BAAC,QAAQ,CAAA;yCAtG1C,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAgBO,IAAI,EAAA,CAAA;sBAAhB,KAAK;gBAeO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAeG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;;AAkK1C;;;AAGG;MA8CU,MAAM,CAAA;AA6II,IAAA,EAAA,CAAA;AA5InB;;;AAGG;IACM,IAAI,GAAW,QAAQ,CAAC;AACjC;;;AAGG;IACM,OAAO,GAAuB,MAAM,CAAC;AAC9C;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACqC,OAAO,GAAY,KAAK,CAAC;AACjE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;IACqC,MAAM,GAAY,KAAK,CAAC;AAChE;;;AAGG;IACqC,OAAO,GAAY,KAAK,CAAC;AACjE;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;IACqC,KAAK,GAAY,KAAK,CAAC;AAC/D;;;AAGG;AACM,IAAA,QAAQ,CAA+G;AAChI;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;AACM,IAAA,IAAI,CAAgC;AAC7C;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;;AAKG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAE,CAAC;AACjE;;;;;AAKG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC7E;;;;;AAKG;AACO,IAAA,MAAM,GAA6B,IAAI,YAAY,EAAc,CAAC;AAE5E,IAAA,eAAe,CAA+B;AAE9C,IAAA,mBAAmB,CAA+B;AAElD,IAAA,YAAY,CAA+B;AAEX,IAAA,SAAS,CAAuC;AAEhF,IAAA,WAAA,CAAmB,EAAc,EAAA;QAAd,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;IAErC,gBAAgB,GAAA;QACZ,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AAClC,aAAA,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAC9B,aAAA,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAI,CAAA,EAAA,GAAG,EAAE,EAAE,uBAAuB,CAAC,CAAC;KACzE;IAED,SAAS,GAAA;AACL,QAAA,MAAM,WAAW,GAAG;AAChB,YAAA,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI,CAAC,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK;YAC3D,qBAAqB,EAAE,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK;YAC7D,mBAAmB,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK;YACzD,sBAAsB,EAAE,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,KAAK;SAClE,CAAC;QAEF,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,WAAW,CAAC,CAAiC,8BAAA,EAAA,IAAI,CAAC,WAAW,IAAI,EAAE,CAAE,CAAA,CAAC,GAAG,IAAI,CAAC;AACjF,SAAA;aAAM,IAAI,IAAI,CAAC,IAAI,EAAE;AAClB,YAAA,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACjC,SAAA;AAED,QAAA,OAAO,WAAW,CAAC;KACtB;AAED,IAAA,IAAI,WAAW,GAAA;QACX,OAAO;AACH,YAAA,sBAAsB,EAAE,IAAI;YAC5B,oBAAoB,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,IAAI,CAAC,KAAK;AACrH,YAAA,mBAAmB,EAAE,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC,KAAK;YACxF,kBAAkB,EAAE,IAAI,CAAC,OAAO;YAChC,6BAA6B,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACvH,eAAe,EAAE,IAAI,CAAC,IAAI;YAC1B,CAAC,CAAA,SAAA,EAAY,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ;YAC5C,iBAAiB,EAAE,IAAI,CAAC,MAAM;YAC9B,kBAAkB,EAAE,IAAI,CAAC,OAAO;YAChC,eAAe,EAAE,IAAI,CAAC,IAAI;YAC1B,mBAAmB,EAAE,IAAI,CAAC,QAAQ;AAClC,YAAA,aAAa,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;AACpC,YAAA,aAAa,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;YACpC,gBAAgB,EAAE,IAAI,CAAC,KAAK;YAC5B,CAAC,CAAA,EAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU;SAC1C,CAAC;KACL;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;QACX,OAAO;AACH,YAAA,qBAAqB,EAAE,IAAI;AAC3B,YAAA,mBAAmB,EAAE,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC;SACrE,CAAC;KACL;AAED;;;AAGG;IACI,KAAK,GAAA;QACR,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;KAC5C;uGA9NQ,MAAM,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAN,MAAM,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EA8BK,gBAAgB,CAKhB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,4DAUhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAKhB,gBAAgB,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAKhB,gBAAgB,CAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAKhB,gBAAgB,CAUhB,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,0BAKhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,eAAe,CA8Bf,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CA6BnB,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,kBAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EApLpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAlCS,IAAI,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,MAAM,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,SAAS,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,WAAW,EAAA,QAAA,EAAA,aAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA0CzE,MAAM,EAAA,UAAA,EAAA,CAAA;kBA7ClB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,OAAO,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,CAAC;AACnF,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;wBAClB,oBAAoB,EAAE,UAAU,IAAI,SAAS;AAChD,qBAAA;AACJ,iBAAA,CAAA;+EAMY,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAO5B,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAOG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAOG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAQyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA0FrB,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAZ,YAAY,EAAA,OAAA,EAAA,CAnhBZ,eAAe,EA8Sf,MAAM,aA9SN,eAAe,EA8Sf,MAAM,EAmOoB,YAAY,CAAA,EAAA,CAAA,CAAA;wGAEtC,YAAY,EAAA,OAAA,EAAA,CAHM,MAAM,EACE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAEtC,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,CAAC;AAClC,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,YAAY,CAAC;AACnD,iBAAA,CAAA;;;ACpkBD;;AAEG;;;;"}