{"version": 3, "file": "primeng-selectbutton.mjs", "sources": ["../../src/app/components/selectbutton/selectbutton.ts", "../../src/app/components/selectbutton/primeng-selectbutton.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChild, ElementRef, EventEmitter, Input, NgModule, Output, TemplateRef, ViewChild, ViewEncapsulation, booleanAttribute, forwardRef, numberAttribute } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { RippleModule } from 'primeng/ripple';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { ObjectUtils } from 'primeng/utils';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { SelectButtonChangeEvent, SelectButtonOptionClickEvent } from './selectbutton.interface';\n\nexport const SELECTBUTTON_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => SelectButton),\n    multi: true\n};\n/**\n * SelectButton is used to choose single or multiple items from a list using buttons.\n * @group Components\n */\n@Component({\n    selector: 'p-selectButton',\n    template: `\n        <div #container [ngClass]=\"'p-selectbutton p-buttonset p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"group\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.data-pc-name]=\"'selectbutton'\" [attr.data-pc-section]=\"'root'\">\n            <div\n                *ngFor=\"let option of options; let i = index\"\n                pRipple\n                [attr.tabindex]=\"i === focusedIndex ? '0' : '-1'\"\n                [attr.aria-label]=\"option.label\"\n                [role]=\"multiple ? 'checkbox' : 'radio'\"\n                [attr.aria-checked]=\"isSelected(option)\"\n                [attr.aria-disabled]=\"optionDisabled\"\n                class=\"p-button p-component\"\n                [class]=\"option.styleClass\"\n                [ngClass]=\"{ 'p-highlight': isSelected(option), 'p-disabled': disabled || isOptionDisabled(option), 'p-button-icon-only': option.icon && !getOptionLabel(option) }\"\n                (click)=\"onOptionSelect($event, option, i)\"\n                (keydown)=\"onKeyDown($event, option, i)\"\n                [attr.title]=\"option.title\"\n                (focus)=\"onFocus($event, i)\"\n                (blur)=\"onBlur()\"\n                [attr.aria-labelledby]=\"this.getOptionLabel(option)\"\n                [attr.data-pc-section]=\"'button'\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n            >\n                <ng-container *ngIf=\"!itemTemplate; else customcontent\">\n                    <span [ngClass]=\"'p-button-icon p-button-icon-left'\" [class]=\"option.icon\" *ngIf=\"option.icon\" [attr.data-pc-section]=\"'icon'\"></span>\n                    <span class=\"p-button-label\" [attr.data-pc-section]=\"'label'\">{{ getOptionLabel(option) }}</span>\n                </ng-container>\n                <ng-template #customcontent>\n                    <ng-container *ngTemplateOutlet=\"selectButtonTemplate; context: { $implicit: option, index: i }\"></ng-container>\n                </ng-template>\n            </div>\n        </div>\n    `,\n    providers: [SELECTBUTTON_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['../button/button.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class SelectButton implements ControlValueAccessor {\n    /**\n     * An array of selectitems to display as the available options.\n     * @group Props\n     */\n    @Input() options: any[] | undefined;\n    /**\n     * Name of the label field of an option.\n     * @group Props\n     */\n    @Input() optionLabel: string | undefined;\n    /**\n     * Name of the value field of an option.\n     * @group Props\n     */\n    @Input() optionValue: string | undefined;\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    @Input() optionDisabled: string | undefined;\n    /**\n     * Whether selection can be cleared.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) unselectable: boolean = false;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n    /**\n     * When specified, allows selecting multiple values.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) multiple: boolean | undefined;\n    /**\n     * Whether selection can not be cleared.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) allowEmpty: boolean = true;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: any;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    @Input() dataKey: string | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Callback to invoke on input click.\n     * @param {SelectButtonOptionClickEvent} event - Custom click event.\n     * @group Emits\n     */\n    @Output() onOptionClick: EventEmitter<SelectButtonOptionClickEvent> = new EventEmitter<SelectButtonOptionClickEvent>();\n    /**\n     * Callback to invoke on selection change.\n     * @param {SelectButtonChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<SelectButtonChangeEvent> = new EventEmitter<SelectButtonChangeEvent>();\n\n    @ViewChild('container') container: Nullable<ElementRef>;\n\n    @ContentChild(PrimeTemplate) itemTemplate!: PrimeTemplate;\n\n    public get selectButtonTemplate(): TemplateRef<any> {\n        return this.itemTemplate?.template;\n    }\n\n    get equalityKey() {\n        return this.optionValue ? null : this.dataKey;\n    }\n\n    value: any;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    focusedIndex: number = 0;\n\n    constructor(public cd: ChangeDetectorRef) {}\n\n    getOptionLabel(option: any) {\n        return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option.label != undefined ? option.label : option;\n    }\n\n    getOptionValue(option: any) {\n        return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : this.optionLabel || option.value === undefined ? option : option.value;\n    }\n\n    isOptionDisabled(option: any) {\n        return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : option.disabled !== undefined ? option.disabled : false;\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    onOptionSelect(event, option, index) {\n        if (this.disabled || this.isOptionDisabled(option)) {\n            return;\n        }\n\n        let selected = this.isSelected(option);\n\n        if (selected && this.unselectable) {\n            return;\n        }\n\n        let optionValue = this.getOptionValue(option);\n        let newValue;\n\n        if (this.multiple) {\n            if (selected) newValue = this.value.filter((val) => !ObjectUtils.equals(val, optionValue, this.equalityKey));\n            else newValue = this.value ? [...this.value, optionValue] : [optionValue];\n        } else {\n            if (selected && !this.allowEmpty) {\n                return;\n            }\n            newValue = selected ? null : optionValue;\n        }\n\n        this.focusedIndex = index;\n        this.value = newValue;\n        this.onModelChange(this.value);\n\n        this.onChange.emit({\n            originalEvent: event,\n            value: this.value\n        });\n\n        this.onOptionClick.emit({\n            originalEvent: event,\n            option: option,\n            index: index\n        });\n    }\n\n    onKeyDown(event, option, index) {\n        switch (event.code) {\n            case 'Space': {\n                this.onOptionSelect(event, option, index);\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowDown':\n\n            case 'ArrowRight': {\n                this.changeTabIndexes(event, 'next');\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowUp':\n\n            case 'ArrowLeft': {\n                this.changeTabIndexes(event, 'prev');\n                event.preventDefault();\n                break;\n            }\n\n            default:\n                //no op\n                break;\n        }\n    }\n\n    changeTabIndexes(event, direction) {\n        let firstTabableChild, index;\n\n        for (let i = 0; i <= this.container.nativeElement.children.length - 1; i++) {\n            if (this.container.nativeElement.children[i].getAttribute('tabindex') === '0') firstTabableChild = { elem: this.container.nativeElement.children[i], index: i };\n        }\n\n        if (direction === 'prev') {\n            if (firstTabableChild.index === 0) index = this.container.nativeElement.children.length - 1;\n            else index = firstTabableChild.index - 1;\n        } else {\n            if (firstTabableChild.index === this.container.nativeElement.children.length - 1) index = 0;\n            else index = firstTabableChild.index + 1;\n        }\n\n        this.focusedIndex = index;\n        this.container.nativeElement.children[index].focus();\n    }\n\n    onFocus(event: Event, index: number) {\n        this.focusedIndex = index;\n    }\n\n    onBlur() {\n        this.onModelTouched();\n    }\n\n    removeOption(option: any): void {\n        this.value = this.value.filter((val: any) => !ObjectUtils.equals(val, this.getOptionValue(option), this.dataKey));\n    }\n\n    isSelected(option: any) {\n        let selected = false;\n        const optionValue = this.getOptionValue(option);\n\n        if (this.multiple) {\n            if (this.value && Array.isArray(this.value)) {\n                for (let val of this.value) {\n                    if (ObjectUtils.equals(val, optionValue, this.dataKey)) {\n                        selected = true;\n                        break;\n                    }\n                }\n            }\n        } else {\n            selected = ObjectUtils.equals(this.getOptionValue(option), this.value, this.equalityKey);\n        }\n\n        return selected;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RippleModule, SharedModule, AutoFocusModule],\n    exports: [SelectButton, SharedModule],\n    declarations: [SelectButton]\n})\nexport class SelectButtonModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;AAUa,MAAA,2BAA2B,GAAQ;AAC5C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,YAAY,CAAC;AAC3C,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MA4CU,YAAY,CAAA;AAwGF,IAAA,EAAA,CAAA;AAvGnB;;;AAGG;AACM,IAAA,OAAO,CAAoB;AACpC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACqC,YAAY,GAAY,KAAK,CAAC;AACtE;;;AAGG;IACoC,QAAQ,GAAW,CAAC,CAAC;AAC5D;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;AACM,IAAA,KAAK,CAAM;AACpB;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;AACO,IAAA,aAAa,GAA+C,IAAI,YAAY,EAAgC,CAAC;AACvH;;;;AAIG;AACO,IAAA,QAAQ,GAA0C,IAAI,YAAY,EAA2B,CAAC;AAEhF,IAAA,SAAS,CAAuB;AAE3B,IAAA,YAAY,CAAiB;AAE1D,IAAA,IAAW,oBAAoB,GAAA;AAC3B,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC;KACtC;AAED,IAAA,IAAI,WAAW,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;KACjD;AAED,IAAA,KAAK,CAAM;AAEX,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;IAEpC,YAAY,GAAW,CAAC,CAAC;AAEzB,IAAA,WAAA,CAAmB,EAAqB,EAAA;QAArB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;AAE5C,IAAA,cAAc,CAAC,MAAW,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,KAAK,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;KACxI;AAED,IAAA,cAAc,CAAC,MAAW,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;KAC7J;AAED,IAAA,gBAAgB,CAAC,MAAW,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,GAAG,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;KACpJ;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAA;QAC/B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE;YAChD,OAAO;AACV,SAAA;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAEvC,QAAA,IAAI,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE;YAC/B,OAAO;AACV,SAAA;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAC9C,QAAA,IAAI,QAAQ,CAAC;QAEb,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,QAAQ;gBAAE,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;;gBACxG,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC7E,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC9B,OAAO;AACV,aAAA;YACD,QAAQ,GAAG,QAAQ,GAAG,IAAI,GAAG,WAAW,CAAC;AAC5C,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAE/B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,YAAA,aAAa,EAAE,KAAK;YACpB,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;AACpB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,KAAK,EAAE,KAAK;AACf,SAAA,CAAC,CAAC;KACN;AAED,IAAA,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAA;QAC1B,QAAQ,KAAK,CAAC,IAAI;YACd,KAAK,OAAO,EAAE;gBACV,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;gBAC1C,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;AAED,YAAA,KAAK,WAAW,CAAC;YAEjB,KAAK,YAAY,EAAE;AACf,gBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACrC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;AAED,YAAA,KAAK,SAAS,CAAC;YAEf,KAAK,WAAW,EAAE;AACd,gBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACrC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;AAED,YAAA;;gBAEI,MAAM;AACb,SAAA;KACJ;IAED,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAA;QAC7B,IAAI,iBAAiB,EAAE,KAAK,CAAC;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACxE,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,GAAG;AAAE,gBAAA,iBAAiB,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AACnK,SAAA;QAED,IAAI,SAAS,KAAK,MAAM,EAAE;AACtB,YAAA,IAAI,iBAAiB,CAAC,KAAK,KAAK,CAAC;AAAE,gBAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;;AACvF,gBAAA,KAAK,GAAG,iBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5C,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,iBAAiB,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;gBAAE,KAAK,GAAG,CAAC,CAAC;;AACvF,gBAAA,KAAK,GAAG,iBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5C,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;KACxD;IAED,OAAO,CAAC,KAAY,EAAE,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;KAC7B;IAED,MAAM,GAAA;QACF,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB;AAED,IAAA,YAAY,CAAC,MAAW,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAQ,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;KACrH;AAED,IAAA,UAAU,CAAC,MAAW,EAAA;QAClB,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACzC,gBAAA,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE;AACxB,oBAAA,IAAI,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE;wBACpD,QAAQ,GAAG,IAAI,CAAC;wBAChB,MAAM;AACT,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC5F,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB;uGA/PQ,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,qMAyBD,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,eAAe,CAKf,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,4CAKhB,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAoBhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAUhB,gBAAgB,CA9EzB,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,2BAA2B,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EA8F1B,aAAa,EA/HjB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,ohEAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FASQ,YAAY,EAAA,UAAA,EAAA,CAAA;kBA3CxB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,gBAAgB,EAChB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgCT,EACU,SAAA,EAAA,CAAC,2BAA2B,CAAC,EACvB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,ohEAAA,CAAA,EAAA,CAAA;sFAOQ,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEiB,SAAS,EAAA,CAAA;sBAAhC,SAAS;uBAAC,WAAW,CAAA;gBAEO,YAAY,EAAA,CAAA;sBAAxC,YAAY;uBAAC,aAAa,CAAA;;MAiLlB,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,EAvQlB,YAAA,EAAA,CAAA,YAAY,CAmQX,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,CAnQ1D,EAAA,OAAA,EAAA,CAAA,YAAY,EAoQG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAG3B,kBAAkB,EAAA,OAAA,EAAA,CAJjB,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAC3C,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG3B,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,CAAC;AACpE,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;oBACrC,YAAY,EAAE,CAAC,YAAY,CAAC;AAC/B,iBAAA,CAAA;;;ACpUD;;AAEG;;;;"}