{"version": 3, "file": "primeng-dom.mjs", "sources": ["../../src/app/components/dom/domhandler.ts", "../../src/app/components/dom/connectedoverlayscrollhandler.ts", "../../src/app/components/dom/primeng-dom.ts"], "sourcesContent": ["/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nexport class DomHandler {\n    public static zindex: number = 1000;\n\n    private static calculatedScrollbarWidth: number = null;\n\n    private static calculatedScrollbarHeight: number = null;\n\n    private static browser: any;\n\n    public static addClass(element: any, className: string): void {\n        if (element && className) {\n            if (element.classList) element.classList.add(className);\n            else element.className += ' ' + className;\n        }\n    }\n\n    public static addMultipleClasses(element: any, className: string): void {\n        if (element && className) {\n            if (element.classList) {\n                let styles: string[] = className.trim().split(' ');\n                for (let i = 0; i < styles.length; i++) {\n                    element.classList.add(styles[i]);\n                }\n            } else {\n                let styles: string[] = className.split(' ');\n                for (let i = 0; i < styles.length; i++) {\n                    element.className += ' ' + styles[i];\n                }\n            }\n        }\n    }\n\n    public static removeClass(element: any, className: string): void {\n        if (element && className) {\n            if (element.classList) element.classList.remove(className);\n            else element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n        }\n    }\n\n    public static removeMultipleClasses(element, classNames) {\n        if (element && classNames) {\n            [classNames]\n                .flat()\n                .filter(Boolean)\n                .forEach((cNames) => cNames.split(' ').forEach((className) => this.removeClass(element, className)));\n        }\n    }\n\n    public static hasClass(element: any, className: string): boolean {\n        if (element && className) {\n            if (element.classList) return element.classList.contains(className);\n            else return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n        }\n\n        return false;\n    }\n\n    public static siblings(element: any): any {\n        return Array.prototype.filter.call(element.parentNode.children, function (child) {\n            return child !== element;\n        });\n    }\n\n    public static find(element: any, selector: string): any[] {\n        return Array.from(element.querySelectorAll(selector));\n    }\n\n    public static findSingle(element: any, selector: string): any {\n        return this.isElement(element) ? element.querySelector(selector) : null;\n    }\n\n    public static index(element: any): number {\n        let children = element.parentNode.childNodes;\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element) return num;\n            if (children[i].nodeType == 1) num++;\n        }\n        return -1;\n    }\n\n    public static indexWithinGroup(element: any, attributeName: string): number {\n        let children = element.parentNode ? element.parentNode.childNodes : [];\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element) return num;\n            if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1) num++;\n        }\n        return -1;\n    }\n\n    public static appendOverlay(overlay: any, target: any, appendTo: any = 'self') {\n        if (appendTo !== 'self' && overlay && target) {\n            this.appendChild(overlay, target);\n        }\n    }\n\n    public static alignOverlay(overlay: any, target: any, appendTo: any = 'self', calculateMinWidth: boolean = true) {\n        if (overlay && target) {\n            if (calculateMinWidth) {\n                overlay.style.minWidth = `${DomHandler.getOuterWidth(target)}px`;\n            }\n\n            if (appendTo === 'self') {\n                this.relativePosition(overlay, target);\n            } else {\n                this.absolutePosition(overlay, target);\n            }\n        }\n    }\n\n    public static relativePosition(element: any, target: any, gutter: boolean = true): void {\n        const getClosestRelativeElement = (el) => {\n            if (!el) return;\n\n            return getComputedStyle(el).getPropertyValue('position') === 'relative' ? el : getClosestRelativeElement(el.parentElement);\n        };\n\n        const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const targetHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n        const targetOffset = target.getBoundingClientRect();\n        const windowScrollTop = this.getWindowScrollTop();\n        const windowScrollLeft = this.getWindowScrollLeft();\n        const viewport = this.getViewport();\n        const relativeElement = getClosestRelativeElement(element);\n        const relativeElementOffset = relativeElement?.getBoundingClientRect() || { top: -1 * windowScrollTop, left: -1 * windowScrollLeft };\n        let top: number, left: number;\n\n        if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n            top = targetOffset.top - relativeElementOffset.top - elementDimensions.height;\n            element.style.transformOrigin = 'bottom';\n            if (targetOffset.top + top < 0) {\n                top = -1 * targetOffset.top;\n            }\n        } else {\n            top = targetHeight + targetOffset.top - relativeElementOffset.top;\n            element.style.transformOrigin = 'top';\n        }\n\n        const horizontalOverflow = targetOffset.left + elementDimensions.width - viewport.width;\n        const targetLeftOffsetInSpaceOfRelativeElement = targetOffset.left - relativeElementOffset.left;\n        if (elementDimensions.width > viewport.width) {\n            // element wider then viewport and cannot fit on screen (align at left side of viewport)\n            left = (targetOffset.left - relativeElementOffset.left) * -1;\n        } else if (horizontalOverflow > 0) {\n            // element wider then viewport but can be fit on screen (align at right side of viewport)\n            left = targetLeftOffsetInSpaceOfRelativeElement - horizontalOverflow;\n        } else {\n            // element fits on screen (align with target)\n            left = targetOffset.left - relativeElementOffset.left;\n        }\n\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n        gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n\n    public static absolutePosition(element: any, target: any, gutter: boolean = true): void {\n        const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const elementOuterHeight = elementDimensions.height;\n        const elementOuterWidth = elementDimensions.width;\n        const targetOuterHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n        const targetOuterWidth = target.offsetWidth ?? target.getBoundingClientRect().width;\n        const targetOffset = target.getBoundingClientRect();\n        const windowScrollTop = this.getWindowScrollTop();\n        const windowScrollLeft = this.getWindowScrollLeft();\n        const viewport = this.getViewport();\n        let top: number, left: number;\n\n        if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n            top = targetOffset.top + windowScrollTop - elementOuterHeight;\n            element.style.transformOrigin = 'bottom';\n\n            if (top < 0) {\n                top = windowScrollTop;\n            }\n        } else {\n            top = targetOuterHeight + targetOffset.top + windowScrollTop;\n            element.style.transformOrigin = 'top';\n        }\n\n        if (targetOffset.left + elementOuterWidth > viewport.width) left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);\n        else left = targetOffset.left + windowScrollLeft;\n\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n        gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n\n    static getParents(element: any, parents: any = []): any {\n        return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n    }\n\n    static getScrollableParents(element: any) {\n        let scrollableParents = [];\n\n        if (element) {\n            let parents = this.getParents(element);\n            const overflowRegex = /(auto|scroll)/;\n            const overflowCheck = (node: any) => {\n                let styleDeclaration = window['getComputedStyle'](node, null);\n                return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n            };\n\n            for (let parent of parents) {\n                let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n                if (scrollSelectors) {\n                    let selectors = scrollSelectors.split(',');\n                    for (let selector of selectors) {\n                        let el = this.findSingle(parent, selector);\n                        if (el && overflowCheck(el)) {\n                            scrollableParents.push(el);\n                        }\n                    }\n                }\n\n                if (parent.nodeType !== 9 && overflowCheck(parent)) {\n                    scrollableParents.push(parent);\n                }\n            }\n        }\n\n        return scrollableParents;\n    }\n\n    public static getHiddenElementOuterHeight(element: any): number {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementHeight = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n\n        return elementHeight;\n    }\n\n    public static getHiddenElementOuterWidth(element: any): number {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementWidth = element.offsetWidth;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n\n        return elementWidth;\n    }\n\n    public static getHiddenElementDimensions(element: any): any {\n        let dimensions: any = {};\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        dimensions.width = element.offsetWidth;\n        dimensions.height = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n\n        return dimensions;\n    }\n\n    public static scrollInView(container, item) {\n        let borderTopValue: string = getComputedStyle(container).getPropertyValue('borderTopWidth');\n        let borderTop: number = borderTopValue ? parseFloat(borderTopValue) : 0;\n        let paddingTopValue: string = getComputedStyle(container).getPropertyValue('paddingTop');\n        let paddingTop: number = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n        let containerRect = container.getBoundingClientRect();\n        let itemRect = item.getBoundingClientRect();\n        let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n        let scroll = container.scrollTop;\n        let elementHeight = container.clientHeight;\n        let itemHeight = this.getOuterHeight(item);\n\n        if (offset < 0) {\n            container.scrollTop = scroll + offset;\n        } else if (offset + itemHeight > elementHeight) {\n            container.scrollTop = scroll + offset - elementHeight + itemHeight;\n        }\n    }\n\n    public static fadeIn(element, duration: number): void {\n        element.style.opacity = 0;\n\n        let last = +new Date();\n        let opacity = 0;\n        let tick = function () {\n            opacity = +element.style.opacity.replace(',', '.') + (new Date().getTime() - last) / duration;\n            element.style.opacity = opacity;\n            last = +new Date();\n\n            if (+opacity < 1) {\n                (window.requestAnimationFrame && requestAnimationFrame(tick)) || setTimeout(tick, 16);\n            }\n        };\n\n        tick();\n    }\n\n    public static fadeOut(element, ms) {\n        var opacity = 1,\n            interval = 50,\n            duration = ms,\n            gap = interval / duration;\n\n        let fading = setInterval(() => {\n            opacity = opacity - gap;\n\n            if (opacity <= 0) {\n                opacity = 0;\n                clearInterval(fading);\n            }\n\n            element.style.opacity = opacity;\n        }, interval);\n    }\n\n    public static getWindowScrollTop(): number {\n        let doc = document.documentElement;\n        return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n    }\n\n    public static getWindowScrollLeft(): number {\n        let doc = document.documentElement;\n        return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n    }\n\n    public static matches(element, selector: string): boolean {\n        var p = Element.prototype;\n        var f =\n            p['matches'] ||\n            p.webkitMatchesSelector ||\n            p['mozMatchesSelector'] ||\n            p['msMatchesSelector'] ||\n            function (s) {\n                return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n            };\n        return f.call(element, selector);\n    }\n\n    public static getOuterWidth(el, margin?) {\n        let width = el.offsetWidth;\n\n        if (margin) {\n            let style = getComputedStyle(el);\n            width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n        }\n\n        return width;\n    }\n\n    public static getHorizontalPadding(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    }\n\n    public static getHorizontalMargin(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n\n    public static innerWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n\n        width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n\n    public static width(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n\n    public static getInnerHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n\n        height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n        return height;\n    }\n\n    public static getOuterHeight(el, margin?) {\n        let height = el.offsetHeight;\n\n        if (margin) {\n            let style = getComputedStyle(el);\n            height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n        }\n\n        return height;\n    }\n\n    public static getHeight(el): number {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n\n        height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n\n        return height;\n    }\n\n    public static getWidth(el): number {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n\n        return width;\n    }\n\n    public static getViewport(): any {\n        let win = window,\n            d = document,\n            e = d.documentElement,\n            g = d.getElementsByTagName('body')[0],\n            w = win.innerWidth || e.clientWidth || g.clientWidth,\n            h = win.innerHeight || e.clientHeight || g.clientHeight;\n\n        return { width: w, height: h };\n    }\n\n    public static getOffset(el) {\n        var rect = el.getBoundingClientRect();\n\n        return {\n            top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n            left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n        };\n    }\n\n    public static replaceElementWith(element: any, replacementElement: any): any {\n        let parentNode = element.parentNode;\n        if (!parentNode) throw `Can't replace element`;\n        return parentNode.replaceChild(replacementElement, element);\n    }\n\n    public static getUserAgent(): string {\n        if (navigator && this.isClient()) {\n            return navigator.userAgent;\n        }\n    }\n\n    public static isIE() {\n        var ua = window.navigator.userAgent;\n\n        var msie = ua.indexOf('MSIE ');\n        if (msie > 0) {\n            // IE 10 or older => return version number\n            return true;\n        }\n\n        var trident = ua.indexOf('Trident/');\n        if (trident > 0) {\n            // IE 11 => return version number\n            var rv = ua.indexOf('rv:');\n            return true;\n        }\n\n        var edge = ua.indexOf('Edge/');\n        if (edge > 0) {\n            // Edge (IE 12+) => return version number\n            return true;\n        }\n\n        // other browser\n        return false;\n    }\n\n    public static isIOS() {\n        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n    }\n\n    public static isAndroid() {\n        return /(android)/i.test(navigator.userAgent);\n    }\n\n    public static isTouchDevice() {\n        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n\n    public static appendChild(element: any, target: any) {\n        if (this.isElement(target)) target.appendChild(element);\n        else if (target && target.el && target.el.nativeElement) target.el.nativeElement.appendChild(element);\n        else throw 'Cannot append ' + target + ' to ' + element;\n    }\n\n    public static removeChild(element: any, target: any) {\n        if (this.isElement(target)) target.removeChild(element);\n        else if (target.el && target.el.nativeElement) target.el.nativeElement.removeChild(element);\n        else throw 'Cannot remove ' + element + ' from ' + target;\n    }\n\n    public static removeElement(element: Element) {\n        if (!('remove' in Element.prototype)) element.parentNode.removeChild(element);\n        else element.remove();\n    }\n\n    public static isElement(obj: any) {\n        return typeof HTMLElement === 'object' ? obj instanceof HTMLElement : obj && typeof obj === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';\n    }\n\n    public static calculateScrollbarWidth(el?: HTMLElement): number {\n        if (el) {\n            let style = getComputedStyle(el);\n            return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n        } else {\n            if (this.calculatedScrollbarWidth !== null) return this.calculatedScrollbarWidth;\n\n            let scrollDiv = document.createElement('div');\n            scrollDiv.className = 'p-scrollbar-measure';\n            document.body.appendChild(scrollDiv);\n\n            let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n            document.body.removeChild(scrollDiv);\n\n            this.calculatedScrollbarWidth = scrollbarWidth;\n\n            return scrollbarWidth;\n        }\n    }\n\n    public static calculateScrollbarHeight(): number {\n        if (this.calculatedScrollbarHeight !== null) return this.calculatedScrollbarHeight;\n\n        let scrollDiv = document.createElement('div');\n        scrollDiv.className = 'p-scrollbar-measure';\n        document.body.appendChild(scrollDiv);\n\n        let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n        document.body.removeChild(scrollDiv);\n\n        this.calculatedScrollbarWidth = scrollbarHeight;\n\n        return scrollbarHeight;\n    }\n\n    public static invokeElementMethod(element: any, methodName: string, args?: any[]): void {\n        (element as any)[methodName].apply(element, args);\n    }\n\n    public static clearSelection(): void {\n        if (window.getSelection) {\n            if (window.getSelection().empty) {\n                window.getSelection().empty();\n            } else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n                window.getSelection().removeAllRanges();\n            }\n        } else if (document['selection'] && document['selection'].empty) {\n            try {\n                document['selection'].empty();\n            } catch (error) {\n                //ignore IE bug\n            }\n        }\n    }\n\n    public static getBrowser() {\n        if (!this.browser) {\n            let matched = this.resolveUserAgent();\n            this.browser = {};\n\n            if (matched.browser) {\n                this.browser[matched.browser] = true;\n                this.browser['version'] = matched.version;\n            }\n\n            if (this.browser['chrome']) {\n                this.browser['webkit'] = true;\n            } else if (this.browser['webkit']) {\n                this.browser['safari'] = true;\n            }\n        }\n\n        return this.browser;\n    }\n\n    public static resolveUserAgent() {\n        let ua = navigator.userAgent.toLowerCase();\n        let match =\n            /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || (ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua)) || [];\n\n        return {\n            browser: match[1] || '',\n            version: match[2] || '0'\n        };\n    }\n\n    public static isInteger(value): boolean {\n        if (Number.isInteger) {\n            return Number.isInteger(value);\n        } else {\n            return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n        }\n    }\n\n    public static isHidden(element: HTMLElement): boolean {\n        return !element || element.offsetParent === null;\n    }\n\n    public static isVisible(element: HTMLElement) {\n        return element && element.offsetParent != null;\n    }\n\n    public static isExist(element: HTMLElement) {\n        return element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode;\n    }\n\n    public static focus(element: HTMLElement, options?: FocusOptions): void {\n        element && document.activeElement !== element && element.focus(options);\n    }\n\n    public static getFocusableSelectorString(selector = ''): string {\n        return `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-inputtext:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`;\n    }\n\n    public static getFocusableElements(element, selector = ''): any[] {\n        let focusableElements = this.find(element, this.getFocusableSelectorString(selector));\n\n        let visibleFocusableElements = [];\n\n        for (let focusableElement of focusableElements) {\n            const computedStyle = getComputedStyle(focusableElement);\n            if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden') visibleFocusableElements.push(focusableElement);\n        }\n\n        return visibleFocusableElements;\n    }\n\n    public static getFocusableElement(element, selector = ''): any | null {\n        let focusableElement = this.findSingle(element, this.getFocusableSelectorString(selector));\n\n        if (focusableElement) {\n            const computedStyle = getComputedStyle(focusableElement);\n            if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden') return focusableElement;\n        }\n\n        return null;\n    }\n\n    public static getFirstFocusableElement(element, selector = '') {\n        const focusableElements = this.getFocusableElements(element, selector);\n\n        return focusableElements.length > 0 ? focusableElements[0] : null;\n    }\n\n    public static getLastFocusableElement(element, selector) {\n        const focusableElements = this.getFocusableElements(element, selector);\n\n        return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n    }\n\n    public static getNextFocusableElement(element: HTMLElement, reverse = false) {\n        const focusableElements = DomHandler.getFocusableElements(element);\n        let index = 0;\n        if (focusableElements && focusableElements.length > 0) {\n            const focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n\n            if (reverse) {\n                if (focusedIndex == -1 || focusedIndex === 0) {\n                    index = focusableElements.length - 1;\n                } else {\n                    index = focusedIndex - 1;\n                }\n            } else if (focusedIndex != -1 && focusedIndex !== focusableElements.length - 1) {\n                index = focusedIndex + 1;\n            }\n        }\n\n        return focusableElements[index];\n    }\n\n    static generateZIndex() {\n        this.zindex = this.zindex || 999;\n        return ++this.zindex;\n    }\n\n    public static getSelection() {\n        if (window.getSelection) return window.getSelection().toString();\n        else if (document.getSelection) return document.getSelection().toString();\n        else if (document['selection']) return document['selection'].createRange().text;\n\n        return null;\n    }\n\n    public static getTargetElement(target: any, el?: HTMLElement) {\n        if (!target) return null;\n\n        switch (target) {\n            case 'document':\n                return document;\n            case 'window':\n                return window;\n            case '@next':\n                return el?.nextElementSibling;\n            case '@prev':\n                return el?.previousElementSibling;\n            case '@parent':\n                return el?.parentElement;\n            case '@grandparent':\n                return el?.parentElement.parentElement;\n            default:\n                const type = typeof target;\n\n                if (type === 'string') {\n                    return document.querySelector(target);\n                } else if (type === 'object' && target.hasOwnProperty('nativeElement')) {\n                    return this.isExist(target.nativeElement) ? target.nativeElement : undefined;\n                }\n\n                const isFunction = (obj: any) => !!(obj && obj.constructor && obj.call && obj.apply);\n                const element = isFunction(target) ? target() : target;\n\n                return (element && element.nodeType === 9) || this.isExist(element) ? element : null;\n        }\n    }\n\n    public static isClient() {\n        return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n    }\n\n    public static getAttribute(element, name) {\n        if (element) {\n            const value = element.getAttribute(name);\n\n            if (!isNaN(value)) {\n                return +value;\n            }\n\n            if (value === 'true' || value === 'false') {\n                return value === 'true';\n            }\n\n            return value;\n        }\n\n        return undefined;\n    }\n\n    public static calculateBodyScrollbarWidth() {\n        return window.innerWidth - document.documentElement.offsetWidth;\n    }\n\n    public static blockBodyScroll(className = 'p-overflow-hidden') {\n        document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');\n        this.addClass(document.body, className);\n    }\n\n    public static unblockBodyScroll(className = 'p-overflow-hidden') {\n        document.body.style.removeProperty('--scrollbar-width');\n        this.removeClass(document.body, className);\n    }\n\n    public static createElement(type, attributes = {}, ...children) {\n        if (type) {\n            const element = document.createElement(type);\n\n            this.setAttributes(element, attributes);\n            element.append(...children);\n\n            return element;\n        }\n\n        return undefined;\n    }\n\n    public static setAttribute(element, attribute = '', value) {\n        if (this.isElement(element) && value !== null && value !== undefined) {\n            element.setAttribute(attribute, value);\n        }\n    }\n\n    public static setAttributes(element, attributes = {}) {\n        if (this.isElement(element)) {\n            const computedStyles = (rule, value) => {\n                const styles = element?.$attrs?.[rule] ? [element?.$attrs?.[rule]] : [];\n\n                return [value].flat().reduce((cv, v) => {\n                    if (v !== null && v !== undefined) {\n                        const type = typeof v;\n\n                        if (type === 'string' || type === 'number') {\n                            cv.push(v);\n                        } else if (type === 'object') {\n                            const _cv = Array.isArray(v)\n                                ? computedStyles(rule, v)\n                                : Object.entries(v).map(([_k, _v]) => (rule === 'style' && (!!_v || _v === 0) ? `${_k.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()}:${_v}` : !!_v ? _k : undefined));\n\n                            cv = _cv.length ? cv.concat(_cv.filter((c) => !!c)) : cv;\n                        }\n                    }\n\n                    return cv;\n                }, styles);\n            };\n\n            Object.entries(attributes).forEach(([key, value]) => {\n                if (value !== undefined && value !== null) {\n                    const matchedEvent = key.match(/^on(.+)/);\n\n                    if (matchedEvent) {\n                        element.addEventListener(matchedEvent[1].toLowerCase(), value);\n                    } else if (key === 'pBind') {\n                        this.setAttributes(element, value);\n                    } else {\n                        value = key === 'class' ? [...new Set(computedStyles('class', value))].join(' ').trim() : key === 'style' ? computedStyles('style', value).join(';').trim() : value;\n                        (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);\n                        element.setAttribute(key, value);\n                    }\n                }\n            });\n        }\n    }\n\n    public static isFocusableElement(element, selector = '') {\n        return this.isElement(element)\n            ? element.matches(`button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`)\n            : false;\n    }\n}\n", "import { <PERSON><PERSON><PERSON><PERSON> } from './domhandler';\n\nexport class ConnectedOverlayScrollHandler {\n    element: any;\n\n    listener: any;\n\n    scrollableParents: any;\n\n    constructor(element: any, listener: any = () => {}) {\n        this.element = element;\n        this.listener = listener;\n    }\n\n    bindScrollListener() {\n        this.scrollableParents = DomHandler.getScrollableParents(this.element);\n        for (let i = 0; i < this.scrollableParents.length; i++) {\n            this.scrollableParents[i].addEventListener('scroll', this.listener);\n        }\n    }\n\n    unbindScrollListener() {\n        if (this.scrollableParents) {\n            for (let i = 0; i < this.scrollableParents.length; i++) {\n                this.scrollableParents[i].removeEventListener('scroll', this.listener);\n            }\n        }\n    }\n\n    destroy() {\n        this.unbindScrollListener();\n        this.element = null;\n        this.listener = null;\n        this.scrollableParents = null;\n    }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": "AAAA;;;;;;;AAOG;AACH;MACa,UAAU,CAAA;AACZ,IAAA,OAAO,MAAM,GAAW,IAAI,CAAC;AAE5B,IAAA,OAAO,wBAAwB,GAAW,IAAI,CAAC;AAE/C,IAAA,OAAO,yBAAyB,GAAW,IAAI,CAAC;IAEhD,OAAO,OAAO,CAAM;AAErB,IAAA,OAAO,QAAQ,CAAC,OAAY,EAAE,SAAiB,EAAA;QAClD,IAAI,OAAO,IAAI,SAAS,EAAE;YACtB,IAAI,OAAO,CAAC,SAAS;AAAE,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;;AACnD,gBAAA,OAAO,CAAC,SAAS,IAAI,GAAG,GAAG,SAAS,CAAC;AAC7C,SAAA;KACJ;AAEM,IAAA,OAAO,kBAAkB,CAAC,OAAY,EAAE,SAAiB,EAAA;QAC5D,IAAI,OAAO,IAAI,SAAS,EAAE;YACtB,IAAI,OAAO,CAAC,SAAS,EAAE;gBACnB,IAAI,MAAM,GAAa,SAAS,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,iBAAA;AACJ,aAAA;AAAM,iBAAA;gBACH,IAAI,MAAM,GAAa,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5C,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpC,OAAO,CAAC,SAAS,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxC,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;AAEM,IAAA,OAAO,WAAW,CAAC,OAAY,EAAE,SAAiB,EAAA;QACrD,IAAI,OAAO,IAAI,SAAS,EAAE;YACtB,IAAI,OAAO,CAAC,SAAS;AAAE,gBAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;;AACtD,gBAAA,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACrI,SAAA;KACJ;AAEM,IAAA,OAAO,qBAAqB,CAAC,OAAO,EAAE,UAAU,EAAA;QACnD,IAAI,OAAO,IAAI,UAAU,EAAE;AACvB,YAAA,CAAC,UAAU,CAAC;AACP,iBAAA,IAAI,EAAE;iBACN,MAAM,CAAC,OAAO,CAAC;AACf,iBAAA,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC5G,SAAA;KACJ;AAEM,IAAA,OAAO,QAAQ,CAAC,OAAY,EAAE,SAAiB,EAAA;QAClD,IAAI,OAAO,IAAI,SAAS,EAAE;YACtB,IAAI,OAAO,CAAC,SAAS;gBAAE,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;;AAC/D,gBAAA,OAAO,IAAI,MAAM,CAAC,OAAO,GAAG,SAAS,GAAG,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACvF,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;IAEM,OAAO,QAAQ,CAAC,OAAY,EAAA;AAC/B,QAAA,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,KAAK,EAAA;YAC3E,OAAO,KAAK,KAAK,OAAO,CAAC;AAC7B,SAAC,CAAC,CAAC;KACN;AAEM,IAAA,OAAO,IAAI,CAAC,OAAY,EAAE,QAAgB,EAAA;QAC7C,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;KACzD;AAEM,IAAA,OAAO,UAAU,CAAC,OAAY,EAAE,QAAgB,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;KAC3E;IAEM,OAAO,KAAK,CAAC,OAAY,EAAA;AAC5B,QAAA,IAAI,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC;QAC7C,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,OAAO;AAAE,gBAAA,OAAO,GAAG,CAAC;AACvC,YAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC;AAAE,gBAAA,GAAG,EAAE,CAAC;AACxC,SAAA;QACD,OAAO,CAAC,CAAC,CAAC;KACb;AAEM,IAAA,OAAO,gBAAgB,CAAC,OAAY,EAAE,aAAqB,EAAA;AAC9D,QAAA,IAAI,QAAQ,GAAG,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;QACvE,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,OAAO;AAAE,gBAAA,OAAO,GAAG,CAAC;YACvC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC;AAAE,gBAAA,GAAG,EAAE,CAAC;AAC3G,SAAA;QACD,OAAO,CAAC,CAAC,CAAC;KACb;IAEM,OAAO,aAAa,CAAC,OAAY,EAAE,MAAW,EAAE,WAAgB,MAAM,EAAA;AACzE,QAAA,IAAI,QAAQ,KAAK,MAAM,IAAI,OAAO,IAAI,MAAM,EAAE;AAC1C,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACrC,SAAA;KACJ;IAEM,OAAO,YAAY,CAAC,OAAY,EAAE,MAAW,EAAE,QAAgB,GAAA,MAAM,EAAE,iBAAA,GAA6B,IAAI,EAAA;QAC3G,IAAI,OAAO,IAAI,MAAM,EAAE;AACnB,YAAA,IAAI,iBAAiB,EAAE;AACnB,gBAAA,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAA,EAAG,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;AACpE,aAAA;YAED,IAAI,QAAQ,KAAK,MAAM,EAAE;AACrB,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC1C,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC1C,aAAA;AACJ,SAAA;KACJ;IAEM,OAAO,gBAAgB,CAAC,OAAY,EAAE,MAAW,EAAE,SAAkB,IAAI,EAAA;AAC5E,QAAA,MAAM,yBAAyB,GAAG,CAAC,EAAE,KAAI;AACrC,YAAA,IAAI,CAAC,EAAE;gBAAE,OAAO;YAEhB,OAAO,gBAAgB,CAAC,EAAE,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU,GAAG,EAAE,GAAG,yBAAyB,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;AAC/H,SAAC,CAAC;AAEF,QAAA,MAAM,iBAAiB,GAAG,OAAO,CAAC,YAAY,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACzJ,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,qBAAqB,EAAE,CAAC,MAAM,CAAC;AAClF,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,qBAAqB,EAAE,CAAC;AACpD,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAClD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACpD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACpC,QAAA,MAAM,eAAe,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAC3D,MAAM,qBAAqB,GAAG,eAAe,EAAE,qBAAqB,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,gBAAgB,EAAE,CAAC;QACrI,IAAI,GAAW,EAAE,IAAY,CAAC;AAE9B,QAAA,IAAI,YAAY,CAAC,GAAG,GAAG,YAAY,GAAG,iBAAiB,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE;AAC9E,YAAA,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,qBAAqB,CAAC,GAAG,GAAG,iBAAiB,CAAC,MAAM,CAAC;AAC9E,YAAA,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC;AACzC,YAAA,IAAI,YAAY,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE;AAC5B,gBAAA,GAAG,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC;AAC/B,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,GAAG,GAAG,YAAY,GAAG,YAAY,CAAC,GAAG,GAAG,qBAAqB,CAAC,GAAG,CAAC;AAClE,YAAA,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;AACzC,SAAA;AAED,QAAA,MAAM,kBAAkB,GAAG,YAAY,CAAC,IAAI,GAAG,iBAAiB,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QACxF,MAAM,wCAAwC,GAAG,YAAY,CAAC,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC;AAChG,QAAA,IAAI,iBAAiB,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE;;AAE1C,YAAA,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,GAAG,qBAAqB,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;AAChE,SAAA;aAAM,IAAI,kBAAkB,GAAG,CAAC,EAAE;;AAE/B,YAAA,IAAI,GAAG,wCAAwC,GAAG,kBAAkB,CAAC;AACxE,SAAA;AAAM,aAAA;;YAEH,IAAI,GAAG,YAAY,CAAC,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC;AACzD,SAAA;QAED,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;QAC/B,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACjC,MAAM,KAAK,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,KAAK,QAAQ,GAAG,mCAAmC,GAAG,8BAA8B,CAAC,CAAC;KACpI;IAEM,OAAO,gBAAgB,CAAC,OAAY,EAAE,MAAW,EAAE,SAAkB,IAAI,EAAA;AAC5E,QAAA,MAAM,iBAAiB,GAAG,OAAO,CAAC,YAAY,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACzJ,QAAA,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,MAAM,CAAC;AACpD,QAAA,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,CAAC;AAClD,QAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,qBAAqB,EAAE,CAAC,MAAM,CAAC;AACvF,QAAA,MAAM,gBAAgB,GAAG,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AACpF,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,qBAAqB,EAAE,CAAC;AACpD,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAClD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AACpD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,GAAW,EAAE,IAAY,CAAC;QAE9B,IAAI,YAAY,CAAC,GAAG,GAAG,iBAAiB,GAAG,kBAAkB,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC7E,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,eAAe,GAAG,kBAAkB,CAAC;AAC9D,YAAA,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC;YAEzC,IAAI,GAAG,GAAG,CAAC,EAAE;gBACT,GAAG,GAAG,eAAe,CAAC;AACzB,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,GAAG,GAAG,iBAAiB,GAAG,YAAY,CAAC,GAAG,GAAG,eAAe,CAAC;AAC7D,YAAA,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;AACzC,SAAA;QAED,IAAI,YAAY,CAAC,IAAI,GAAG,iBAAiB,GAAG,QAAQ,CAAC,KAAK;AAAE,YAAA,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,IAAI,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,iBAAiB,CAAC,CAAC;;AACvJ,YAAA,IAAI,GAAG,YAAY,CAAC,IAAI,GAAG,gBAAgB,CAAC;QAEjD,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;QAC/B,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACjC,MAAM,KAAK,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,KAAK,QAAQ,GAAG,mCAAmC,GAAG,8BAA8B,CAAC,CAAC;KACpI;AAED,IAAA,OAAO,UAAU,CAAC,OAAY,EAAE,UAAe,EAAE,EAAA;AAC7C,QAAA,OAAO,OAAO,CAAC,YAAY,CAAC,KAAK,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KAC/H;IAED,OAAO,oBAAoB,CAAC,OAAY,EAAA;QACpC,IAAI,iBAAiB,GAAG,EAAE,CAAC;AAE3B,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,aAAa,GAAG,eAAe,CAAC;AACtC,YAAA,MAAM,aAAa,GAAG,CAAC,IAAS,KAAI;gBAChC,IAAI,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC9D,gBAAA,OAAO,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;AACzN,aAAC,CAAC;AAEF,YAAA,KAAK,IAAI,MAAM,IAAI,OAAO,EAAE;AACxB,gBAAA,IAAI,eAAe,GAAG,MAAM,CAAC,QAAQ,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AACjF,gBAAA,IAAI,eAAe,EAAE;oBACjB,IAAI,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC3C,oBAAA,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;wBAC5B,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3C,wBAAA,IAAI,EAAE,IAAI,aAAa,CAAC,EAAE,CAAC,EAAE;AACzB,4BAAA,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B,yBAAA;AACJ,qBAAA;AACJ,iBAAA;gBAED,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;AAChD,oBAAA,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,iBAAiB,CAAC;KAC5B;IAEM,OAAO,2BAA2B,CAAC,OAAY,EAAA;AAClD,QAAA,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AACpC,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC,QAAA,IAAI,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;AACzC,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC/B,QAAA,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;AAErC,QAAA,OAAO,aAAa,CAAC;KACxB;IAEM,OAAO,0BAA0B,CAAC,OAAY,EAAA;AACjD,QAAA,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AACpC,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC,QAAA,IAAI,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC;AACvC,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC/B,QAAA,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;AAErC,QAAA,OAAO,YAAY,CAAC;KACvB;IAEM,OAAO,0BAA0B,CAAC,OAAY,EAAA;QACjD,IAAI,UAAU,GAAQ,EAAE,CAAC;AACzB,QAAA,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AACpC,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC,QAAA,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC;AACvC,QAAA,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;AACzC,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC/B,QAAA,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;AAErC,QAAA,OAAO,UAAU,CAAC;KACrB;AAEM,IAAA,OAAO,YAAY,CAAC,SAAS,EAAE,IAAI,EAAA;QACtC,IAAI,cAAc,GAAW,gBAAgB,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAC5F,QAAA,IAAI,SAAS,GAAW,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACxE,IAAI,eAAe,GAAW,gBAAgB,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACzF,QAAA,IAAI,UAAU,GAAW,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AAC3E,QAAA,IAAI,aAAa,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;AACtD,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC5C,IAAI,MAAM,GAAG,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,UAAU,CAAC;AAC7H,QAAA,IAAI,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC;AACjC,QAAA,IAAI,aAAa,GAAG,SAAS,CAAC,YAAY,CAAC;QAC3C,IAAI,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,MAAM,GAAG,CAAC,EAAE;AACZ,YAAA,SAAS,CAAC,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC;AACzC,SAAA;AAAM,aAAA,IAAI,MAAM,GAAG,UAAU,GAAG,aAAa,EAAE;YAC5C,SAAS,CAAC,SAAS,GAAG,MAAM,GAAG,MAAM,GAAG,aAAa,GAAG,UAAU,CAAC;AACtE,SAAA;KACJ;AAEM,IAAA,OAAO,MAAM,CAAC,OAAO,EAAE,QAAgB,EAAA;AAC1C,QAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;AAE1B,QAAA,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,OAAO,GAAG,CAAC,CAAC;AAChB,QAAA,IAAI,IAAI,GAAG,YAAA;YACP,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,QAAQ,CAAC;AAC9F,YAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC,YAAA,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;AAEnB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;AACd,gBAAA,CAAC,MAAM,CAAC,qBAAqB,IAAI,qBAAqB,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACzF,aAAA;AACL,SAAC,CAAC;AAEF,QAAA,IAAI,EAAE,CAAC;KACV;AAEM,IAAA,OAAO,OAAO,CAAC,OAAO,EAAE,EAAE,EAAA;AAC7B,QAAA,IAAI,OAAO,GAAG,CAAC,EACX,QAAQ,GAAG,EAAE,EACb,QAAQ,GAAG,EAAE,EACb,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAE9B,QAAA,IAAI,MAAM,GAAG,WAAW,CAAC,MAAK;AAC1B,YAAA,OAAO,GAAG,OAAO,GAAG,GAAG,CAAC;YAExB,IAAI,OAAO,IAAI,CAAC,EAAE;gBACd,OAAO,GAAG,CAAC,CAAC;gBACZ,aAAa,CAAC,MAAM,CAAC,CAAC;AACzB,aAAA;AAED,YAAA,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;SACnC,EAAE,QAAQ,CAAC,CAAC;KAChB;AAEM,IAAA,OAAO,kBAAkB,GAAA;AAC5B,QAAA,IAAI,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC;AACnC,QAAA,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;KACvE;AAEM,IAAA,OAAO,mBAAmB,GAAA;AAC7B,QAAA,IAAI,GAAG,GAAG,QAAQ,CAAC,eAAe,CAAC;AACnC,QAAA,OAAO,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;KACzE;AAEM,IAAA,OAAO,OAAO,CAAC,OAAO,EAAE,QAAgB,EAAA;AAC3C,QAAA,IAAI,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC;AAC1B,QAAA,IAAI,CAAC,GACD,CAAC,CAAC,SAAS,CAAC;AACZ,YAAA,CAAC,CAAC,qBAAqB;YACvB,CAAC,CAAC,oBAAoB,CAAC;YACvB,CAAC,CAAC,mBAAmB,CAAC;AACtB,YAAA,UAAU,CAAC,EAAA;AACP,gBAAA,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACtE,aAAC,CAAC;QACN,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KACpC;AAEM,IAAA,OAAO,aAAa,CAAC,EAAE,EAAE,MAAO,EAAA;AACnC,QAAA,IAAI,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC;AAE3B,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AACjC,YAAA,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AACzE,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;IAEM,OAAO,oBAAoB,CAAC,EAAE,EAAA;AACjC,QAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AACjC,QAAA,OAAO,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;KACzE;IAEM,OAAO,mBAAmB,CAAC,EAAE,EAAA;AAChC,QAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AACjC,QAAA,OAAO,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACvE;IAEM,OAAO,UAAU,CAAC,EAAE,EAAA;AACvB,QAAA,IAAI,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC;AAC3B,QAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAEjC,QAAA,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACxE,QAAA,OAAO,KAAK,CAAC;KAChB;IAEM,OAAO,KAAK,CAAC,EAAE,EAAA;AAClB,QAAA,IAAI,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC;AAC3B,QAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAEjC,QAAA,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACxE,QAAA,OAAO,KAAK,CAAC;KAChB;IAEM,OAAO,cAAc,CAAC,EAAE,EAAA;AAC3B,QAAA,IAAI,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC;AAC7B,QAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAEjC,QAAA,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACzE,QAAA,OAAO,MAAM,CAAC;KACjB;AAEM,IAAA,OAAO,cAAc,CAAC,EAAE,EAAE,MAAO,EAAA;AACpC,QAAA,IAAI,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC;AAE7B,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AACjC,YAAA,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;IAEM,OAAO,SAAS,CAAC,EAAE,EAAA;AACtB,QAAA,IAAI,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC;AAC7B,QAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAEjC,QAAA,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAElJ,QAAA,OAAO,MAAM,CAAC;KACjB;IAEM,OAAO,QAAQ,CAAC,EAAE,EAAA;AACrB,QAAA,IAAI,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC;AAC3B,QAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAEjC,QAAA,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAEjJ,QAAA,OAAO,KAAK,CAAC;KAChB;AAEM,IAAA,OAAO,WAAW,GAAA;QACrB,IAAI,GAAG,GAAG,MAAM,EACZ,CAAC,GAAG,QAAQ,EACZ,CAAC,GAAG,CAAC,CAAC,eAAe,EACrB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EACrC,CAAC,GAAG,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,EACpD,CAAC,GAAG,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,YAAY,CAAC;QAE5D,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;KAClC;IAEM,OAAO,SAAS,CAAC,EAAE,EAAA;AACtB,QAAA,IAAI,IAAI,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;QAEtC,OAAO;YACH,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,CAAC,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;YAC1G,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,CAAC,UAAU,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;SACjH,CAAC;KACL;AAEM,IAAA,OAAO,kBAAkB,CAAC,OAAY,EAAE,kBAAuB,EAAA;AAClE,QAAA,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AACpC,QAAA,IAAI,CAAC,UAAU;AAAE,YAAA,MAAM,uBAAuB,CAAC;QAC/C,OAAO,UAAU,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;KAC/D;AAEM,IAAA,OAAO,YAAY,GAAA;AACtB,QAAA,IAAI,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YAC9B,OAAO,SAAS,CAAC,SAAS,CAAC;AAC9B,SAAA;KACJ;AAEM,IAAA,OAAO,IAAI,GAAA;AACd,QAAA,IAAI,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;QAEpC,IAAI,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,CAAC,EAAE;;AAEV,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAI,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,IAAI,OAAO,GAAG,CAAC,EAAE;;YAEb,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3B,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAI,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,CAAC,EAAE;;AAEV,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;AAGD,QAAA,OAAO,KAAK,CAAC;KAChB;AAEM,IAAA,OAAO,KAAK,GAAA;AACf,QAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KAC9E;AAEM,IAAA,OAAO,SAAS,GAAA;QACnB,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;KACjD;AAEM,IAAA,OAAO,aAAa,GAAA;QACvB,OAAO,cAAc,IAAI,MAAM,IAAI,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;KACnE;AAEM,IAAA,OAAO,WAAW,CAAC,OAAY,EAAE,MAAW,EAAA;AAC/C,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAAE,YAAA,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;aACnD,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,aAAa;YAAE,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;;AACjG,YAAA,MAAM,gBAAgB,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;KAC3D;AAEM,IAAA,OAAO,WAAW,CAAC,OAAY,EAAE,MAAW,EAAA;AAC/C,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAAE,YAAA,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;aACnD,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,aAAa;YAAE,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;;AACvF,YAAA,MAAM,gBAAgB,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC;KAC7D;IAEM,OAAO,aAAa,CAAC,OAAgB,EAAA;AACxC,QAAA,IAAI,EAAE,QAAQ,IAAI,OAAO,CAAC,SAAS,CAAC;AAAE,YAAA,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;;YACzE,OAAO,CAAC,MAAM,EAAE,CAAC;KACzB;IAEM,OAAO,SAAS,CAAC,GAAQ,EAAA;AAC5B,QAAA,OAAO,OAAO,WAAW,KAAK,QAAQ,GAAG,GAAG,YAAY,WAAW,GAAG,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;KAClL;IAEM,OAAO,uBAAuB,CAAC,EAAgB,EAAA;AAClD,QAAA,IAAI,EAAE,EAAE;AACJ,YAAA,IAAI,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;YACjC,OAAO,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;AACnH,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,wBAAwB,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAC,wBAAwB,CAAC;YAEjF,IAAI,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,YAAA,SAAS,CAAC,SAAS,GAAG,qBAAqB,CAAC;AAC5C,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAErC,IAAI,cAAc,GAAG,SAAS,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;AACnE,YAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAErC,YAAA,IAAI,CAAC,wBAAwB,GAAG,cAAc,CAAC;AAE/C,YAAA,OAAO,cAAc,CAAC;AACzB,SAAA;KACJ;AAEM,IAAA,OAAO,wBAAwB,GAAA;AAClC,QAAA,IAAI,IAAI,CAAC,yBAAyB,KAAK,IAAI;YAAE,OAAO,IAAI,CAAC,yBAAyB,CAAC;QAEnF,IAAI,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,QAAA,SAAS,CAAC,SAAS,GAAG,qBAAqB,CAAC;AAC5C,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAErC,IAAI,eAAe,GAAG,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;AACtE,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAErC,QAAA,IAAI,CAAC,wBAAwB,GAAG,eAAe,CAAC;AAEhD,QAAA,OAAO,eAAe,CAAC;KAC1B;AAEM,IAAA,OAAO,mBAAmB,CAAC,OAAY,EAAE,UAAkB,EAAE,IAAY,EAAA;QAC3E,OAAe,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KACrD;AAEM,IAAA,OAAO,cAAc,GAAA;QACxB,IAAI,MAAM,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,KAAK,EAAE;AAC7B,gBAAA,MAAM,CAAC,YAAY,EAAE,CAAC,KAAK,EAAE,CAAC;AACjC,aAAA;AAAM,iBAAA,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,UAAU,GAAG,CAAC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AACzJ,gBAAA,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,EAAE,CAAC;AAC3C,aAAA;AACJ,SAAA;aAAM,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE;YAC7D,IAAI;AACA,gBAAA,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,CAAC;AACjC,aAAA;AAAC,YAAA,OAAO,KAAK,EAAE;;AAEf,aAAA;AACJ,SAAA;KACJ;AAEM,IAAA,OAAO,UAAU,GAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACf,YAAA,IAAI,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACtC,YAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;YAElB,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;AAC7C,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACxB,gBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AACjC,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC/B,gBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AACjC,aAAA;AACJ,SAAA;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;AAEM,IAAA,OAAO,gBAAgB,GAAA;QAC1B,IAAI,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,KAAK,GACL,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,oCAAoC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,+BAA+B,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAE5O,OAAO;AACH,YAAA,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;AACvB,YAAA,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;SAC3B,CAAC;KACL;IAEM,OAAO,SAAS,CAAC,KAAK,EAAA;QACzB,IAAI,MAAM,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAClC,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;AACtF,SAAA;KACJ;IAEM,OAAO,QAAQ,CAAC,OAAoB,EAAA;QACvC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,YAAY,KAAK,IAAI,CAAC;KACpD;IAEM,OAAO,SAAS,CAAC,OAAoB,EAAA;AACxC,QAAA,OAAO,OAAO,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC;KAClD;IAEM,OAAO,OAAO,CAAC,OAAoB,EAAA;AACtC,QAAA,OAAO,OAAO,KAAK,IAAI,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU,CAAC;KACvG;AAEM,IAAA,OAAO,KAAK,CAAC,OAAoB,EAAE,OAAsB,EAAA;AAC5D,QAAA,OAAO,IAAI,QAAQ,CAAC,aAAa,KAAK,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC3E;AAEM,IAAA,OAAO,0BAA0B,CAAC,QAAQ,GAAG,EAAE,EAAA;AAClD,QAAA,OAAO,2FAA2F,QAAQ,CAAA;6HACW,QAAQ,CAAA;iGACpC,QAAQ,CAAA;kGACP,QAAQ,CAAA;oGACN,QAAQ,CAAA;sGACN,QAAQ,CAAA;6GACD,QAAQ,CAAA;wGACb,QAAQ,CAAA;AACX,mGAAA,EAAA,QAAQ,EAAE,CAAC;KAC3G;AAEM,IAAA,OAAO,oBAAoB,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,EAAA;AACrD,QAAA,IAAI,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEtF,IAAI,wBAAwB,GAAG,EAAE,CAAC;AAElC,QAAA,KAAK,IAAI,gBAAgB,IAAI,iBAAiB,EAAE;AAC5C,YAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AACzD,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,aAAa,CAAC,OAAO,IAAI,MAAM,IAAI,aAAa,CAAC,UAAU,IAAI,QAAQ;AAAE,gBAAA,wBAAwB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpK,SAAA;AAED,QAAA,OAAO,wBAAwB,CAAC;KACnC;AAEM,IAAA,OAAO,mBAAmB,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,EAAA;AACpD,QAAA,IAAI,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE3F,QAAA,IAAI,gBAAgB,EAAE;AAClB,YAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AACzD,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,aAAa,CAAC,OAAO,IAAI,MAAM,IAAI,aAAa,CAAC,UAAU,IAAI,QAAQ;AAAE,gBAAA,OAAO,gBAAgB,CAAC;AAC5I,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAEM,IAAA,OAAO,wBAAwB,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,EAAA;QACzD,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAEvE,QAAA,OAAO,iBAAiB,CAAC,MAAM,GAAG,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACrE;AAEM,IAAA,OAAO,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAA;QACnD,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAEvE,OAAO,iBAAiB,CAAC,MAAM,GAAG,CAAC,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;KAChG;AAEM,IAAA,OAAO,uBAAuB,CAAC,OAAoB,EAAE,OAAO,GAAG,KAAK,EAAA;QACvE,MAAM,iBAAiB,GAAG,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACnE,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,QAAA,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AACnD,YAAA,MAAM,YAAY,GAAG,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAEjG,YAAA,IAAI,OAAO,EAAE;gBACT,IAAI,YAAY,IAAI,CAAC,CAAC,IAAI,YAAY,KAAK,CAAC,EAAE;AAC1C,oBAAA,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,iBAAA;AAAM,qBAAA;AACH,oBAAA,KAAK,GAAG,YAAY,GAAG,CAAC,CAAC;AAC5B,iBAAA;AACJ,aAAA;AAAM,iBAAA,IAAI,YAAY,IAAI,CAAC,CAAC,IAAI,YAAY,KAAK,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5E,gBAAA,KAAK,GAAG,YAAY,GAAG,CAAC,CAAC;AAC5B,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;KACnC;AAED,IAAA,OAAO,cAAc,GAAA;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC;AACjC,QAAA,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC;KACxB;AAEM,IAAA,OAAO,YAAY,GAAA;QACtB,IAAI,MAAM,CAAC,YAAY;AAAE,YAAA,OAAO,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,CAAC;aAC5D,IAAI,QAAQ,CAAC,YAAY;AAAE,YAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,CAAC;aACrE,IAAI,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC;AAEhF,QAAA,OAAO,IAAI,CAAC;KACf;AAEM,IAAA,OAAO,gBAAgB,CAAC,MAAW,EAAE,EAAgB,EAAA;AACxD,QAAA,IAAI,CAAC,MAAM;AAAE,YAAA,OAAO,IAAI,CAAC;AAEzB,QAAA,QAAQ,MAAM;AACV,YAAA,KAAK,UAAU;AACX,gBAAA,OAAO,QAAQ,CAAC;AACpB,YAAA,KAAK,QAAQ;AACT,gBAAA,OAAO,MAAM,CAAC;AAClB,YAAA,KAAK,OAAO;gBACR,OAAO,EAAE,EAAE,kBAAkB,CAAC;AAClC,YAAA,KAAK,OAAO;gBACR,OAAO,EAAE,EAAE,sBAAsB,CAAC;AACtC,YAAA,KAAK,SAAS;gBACV,OAAO,EAAE,EAAE,aAAa,CAAC;AAC7B,YAAA,KAAK,cAAc;AACf,gBAAA,OAAO,EAAE,EAAE,aAAa,CAAC,aAAa,CAAC;AAC3C,YAAA;AACI,gBAAA,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;gBAE3B,IAAI,IAAI,KAAK,QAAQ,EAAE;AACnB,oBAAA,OAAO,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACzC,iBAAA;qBAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE;AACpE,oBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;AAChF,iBAAA;gBAED,MAAM,UAAU,GAAG,CAAC,GAAQ,KAAK,CAAC,EAAE,GAAG,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AACrF,gBAAA,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,CAAC;gBAEvD,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC;AAC5F,SAAA;KACJ;AAEM,IAAA,OAAO,QAAQ,GAAA;AAClB,QAAA,OAAO,CAAC,EAAE,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;KAChG;AAEM,IAAA,OAAO,YAAY,CAAC,OAAO,EAAE,IAAI,EAAA;AACpC,QAAA,IAAI,OAAO,EAAE;YACT,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAEzC,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACf,OAAO,CAAC,KAAK,CAAC;AACjB,aAAA;AAED,YAAA,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO,EAAE;gBACvC,OAAO,KAAK,KAAK,MAAM,CAAC;AAC3B,aAAA;AAED,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KACpB;AAEM,IAAA,OAAO,2BAA2B,GAAA;QACrC,OAAO,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC;KACnE;AAEM,IAAA,OAAO,eAAe,CAAC,SAAS,GAAG,mBAAmB,EAAA;AACzD,QAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAC,2BAA2B,EAAE,GAAG,IAAI,CAAC,CAAC;QAChG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAC3C;AAEM,IAAA,OAAO,iBAAiB,CAAC,SAAS,GAAG,mBAAmB,EAAA;QAC3D,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;QACxD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAC9C;IAEM,OAAO,aAAa,CAAC,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,QAAQ,EAAA;AAC1D,QAAA,IAAI,IAAI,EAAE;YACN,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAE7C,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACxC,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC;AAE5B,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KACpB;IAEM,OAAO,YAAY,CAAC,OAAO,EAAE,SAAS,GAAG,EAAE,EAAE,KAAK,EAAA;AACrD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAClE,YAAA,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAC1C,SAAA;KACJ;AAEM,IAAA,OAAO,aAAa,CAAC,OAAO,EAAE,UAAU,GAAG,EAAE,EAAA;AAChD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;AACzB,YAAA,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,KAAK,KAAI;gBACnC,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAExE,gBAAA,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAI;AACnC,oBAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE;AAC/B,wBAAA,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC;AAEtB,wBAAA,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,QAAQ,EAAE;AACxC,4BAAA,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACd,yBAAA;6BAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAC1B,4BAAA,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACxB,kCAAE,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;AACzB,kCAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,MAAM,IAAI,KAAK,OAAO,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,GAAG,CAAA,EAAG,EAAE,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,CAAA,CAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC;AAE9K,4BAAA,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC5D,yBAAA;AACJ,qBAAA;AAED,oBAAA,OAAO,EAAE,CAAC;iBACb,EAAE,MAAM,CAAC,CAAC;AACf,aAAC,CAAC;AAEF,YAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;AAChD,gBAAA,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;oBACvC,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAE1C,oBAAA,IAAI,YAAY,EAAE;AACd,wBAAA,OAAO,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;AAClE,qBAAA;yBAAM,IAAI,GAAG,KAAK,OAAO,EAAE;AACxB,wBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACtC,qBAAA;AAAM,yBAAA;wBACH,KAAK,GAAG,GAAG,KAAK,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,GAAG,KAAK,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC;wBACpK,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AACzE,wBAAA,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACpC,qBAAA;AACJ,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAEM,IAAA,OAAO,kBAAkB,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC1B,cAAE,OAAO,CAAC,OAAO,CAAC,2FAA2F,QAAQ,CAAA;qIACI,QAAQ,CAAA;yGACpC,QAAQ,CAAA;0GACP,QAAQ,CAAA;4GACN,QAAQ,CAAA;8GACN,QAAQ,CAAA;AACD,mHAAA,EAAA,QAAQ,EAAE,CAAC;cAClH,KAAK,CAAC;KACf;;;MCr0BQ,6BAA6B,CAAA;AACtC,IAAA,OAAO,CAAM;AAEb,IAAA,QAAQ,CAAM;AAEd,IAAA,iBAAiB,CAAM;AAEvB,IAAA,WAAA,CAAY,OAAY,EAAE,QAAA,GAAgB,SAAQ,EAAA;AAC9C,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC5B;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpD,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvE,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACxB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpD,gBAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1E,aAAA;AACJ,SAAA;KACJ;IAED,OAAO,GAAA;QACH,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;KACjC;AACJ;;ACnCD;;AAEG;;;;"}