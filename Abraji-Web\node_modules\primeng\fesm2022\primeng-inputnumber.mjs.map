{"version": 3, "file": "primeng-inputnumber.mjs", "sources": ["../../src/app/components/inputnumber/inputnumber.ts", "../../src/app/components/inputnumber/primeng-inputnumber.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Injector,\n    Input,\n    NgModule,\n    OnChanges,\n    OnInit,\n    Output,\n    QueryList,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    forwardRef,\n    numberAttribute\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport { PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleUpIcon } from 'primeng/icons/angleup';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { InputNumberInputEvent } from './inputnumber.interface';\n\nexport const INPUTNUMBER_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputNumber),\n    multi: true\n};\n/**\n * InputNumber is an input component to provide numerical input.\n * @group Components\n */\n@Component({\n    selector: 'p-inputNumber',\n    template: `\n        <span\n            [ngClass]=\"{\n                'p-inputnumber p-component': true,\n                'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal',\n                'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'\n            }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'inputnumber'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <input\n                pInputText\n                #input\n                [attr.id]=\"inputId\"\n                role=\"spinbutton\"\n                [ngClass]=\"'p-inputnumber-input'\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [value]=\"formattedValue()\"\n                [attr.variant]=\"variant\"\n                [attr.aria-valuemin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"value\"\n                [disabled]=\"disabled\"\n                [readonly]=\"readonly\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.title]=\"title\"\n                [attr.size]=\"size\"\n                [attr.name]=\"name\"\n                [attr.autocomplete]=\"autocomplete\"\n                [attr.maxlength]=\"maxlength\"\n                [attr.tabindex]=\"tabindex\"\n                [attr.aria-required]=\"ariaRequired\"\n                [attr.required]=\"required\"\n                [attr.min]=\"min\"\n                [attr.max]=\"max\"\n                inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\"\n                (keydown)=\"onInputKeyDown($event)\"\n                (keypress)=\"onInputKeyPress($event)\"\n                (paste)=\"onPaste($event)\"\n                (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                [attr.data-pc-section]=\"'input'\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n                [class.p-variant-filled]=\"variant === 'filled' || config.inputStyle() === 'filled'\"\n            />\n            <ng-container *ngIf=\"buttonLayout != 'vertical' && showClear && value\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [ngClass]=\"'p-inputnumber-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n                <span *ngIf=\"clearIconTemplate\" (click)=\"clear()\" class=\"p-inputnumber-clear-icon\" [attr.data-pc-section]=\"'clearIcon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\" [attr.data-pc-section]=\"'buttonGroup'\">\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"incrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onUpButtonMouseDown($event)\"\n                    (mouseup)=\"onUpButtonMouseUp()\"\n                    (mouseleave)=\"onUpButtonMouseLeave()\"\n                    (keydown)=\"onUpButtonKeyDown($event)\"\n                    (keyup)=\"onUpButtonKeyUp()\"\n                    [attr.aria-hidden]=\"true\"\n                    [attr.data-pc-section]=\"'incrementbutton'\"\n                >\n                    <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!incrementButtonIcon\">\n                        <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n                <button\n                    type=\"button\"\n                    pButton\n                    [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                    class=\"p-button-icon-only\"\n                    [class]=\"decrementButtonClass\"\n                    [disabled]=\"disabled\"\n                    tabindex=\"-1\"\n                    [attr.aria-hidden]=\"true\"\n                    (mousedown)=\"onDownButtonMouseDown($event)\"\n                    (mouseup)=\"onDownButtonMouseUp()\"\n                    (mouseleave)=\"onDownButtonMouseLeave()\"\n                    (keydown)=\"onDownButtonKeyDown($event)\"\n                    (keyup)=\"onDownButtonKeyUp()\"\n                    [attr.data-pc-section]=\"decrementbutton\"\n                >\n                    <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                    <ng-container *ngIf=\"!decrementButtonIcon\">\n                        <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                        <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                    </ng-container>\n                </button>\n            </span>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-up': true }\"\n                [class]=\"incrementButtonClass\"\n                class=\"p-button-icon-only\"\n                [disabled]=\"disabled\"\n                tabindex=\"-1\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onUpButtonMouseDown($event)\"\n                (mouseup)=\"onUpButtonMouseUp()\"\n                (mouseleave)=\"onUpButtonMouseLeave()\"\n                (keydown)=\"onUpButtonKeyDown($event)\"\n                (keyup)=\"onUpButtonKeyUp()\"\n                [attr.data-pc-section]=\"'incrementbutton'\"\n            >\n                <span *ngIf=\"incrementButtonIcon\" [ngClass]=\"incrementButtonIcon\" [attr.data-pc-section]=\"'incrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!incrementButtonIcon\">\n                    <AngleUpIcon *ngIf=\"!incrementButtonIconTemplate\" [attr.data-pc-section]=\"'incrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"incrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n            <button\n                *ngIf=\"showButtons && buttonLayout !== 'stacked'\"\n                type=\"button\"\n                pButton\n                [ngClass]=\"{ 'p-inputnumber-button p-inputnumber-button-down': true }\"\n                class=\"p-button-icon-only\"\n                [class]=\"decrementButtonClass\"\n                [disabled]=\"disabled\"\n                tabindex=\"-1\"\n                [attr.aria-hidden]=\"true\"\n                (mousedown)=\"onDownButtonMouseDown($event)\"\n                (mouseup)=\"onDownButtonMouseUp()\"\n                (mouseleave)=\"onDownButtonMouseLeave()\"\n                (keydown)=\"onDownButtonKeyDown($event)\"\n                (keyup)=\"onDownButtonKeyUp()\"\n                [attr.data-pc-section]=\"'decrementbutton'\"\n            >\n                <span *ngIf=\"decrementButtonIcon\" [ngClass]=\"decrementButtonIcon\" [attr.data-pc-section]=\"'decrementbuttonicon'\"></span>\n                <ng-container *ngIf=\"!decrementButtonIcon\">\n                    <AngleDownIcon *ngIf=\"!decrementButtonIconTemplate\" [attr.data-pc-section]=\"'decrementbuttonicon'\" />\n                    <ng-template *ngTemplateOutlet=\"decrementButtonIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n        </span>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    providers: [INPUTNUMBER_VALUE_ACCESSOR],\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./inputnumber.css'],\n    host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': 'focused',\n        '[class.p-inputnumber-clearable]': 'showClear && buttonLayout != \"vertical\"'\n    }\n})\nexport class InputNumber implements OnInit, AfterContentInit, OnChanges, ControlValueAccessor {\n    /**\n     * Displays spinner buttons.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showButtons: boolean = false;\n    /**\n     * Whether to format the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) format: boolean = true;\n    /**\n     * Layout of the buttons, valid values are \"stacked\" (default), \"horizontal\" and \"vertical\".\n     * @group Props\n     */\n    @Input() buttonLayout: string = 'stacked';\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    @Input() placeholder: string | undefined;\n    /**\n     * Size of the input field.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) size: number | undefined;\n    /**\n     * Maximum number of character allows in the input field.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) maxlength: number | undefined;\n    /**\n     * Specifies tab order of the element.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n    /**\n     * Title text of the input text.\n     * @group Props\n     */\n    @Input() title: string | undefined;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Used to indicate that user input is required on an element before a form can be submitted.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) ariaRequired: boolean | undefined;\n    /**\n     * Name of the input field.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * Indicates that whether the input field is required.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) required: boolean | undefined;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    @Input() autocomplete: string | undefined;\n    /**\n     * Mininum boundary value.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) min: number | undefined;\n    /**\n     * Maximum boundary value.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) max: number | undefined;\n    /**\n     * Style class of the increment button.\n     * @group Props\n     */\n    @Input() incrementButtonClass: string | undefined;\n    /**\n     * Style class of the decrement button.\n     * @group Props\n     */\n    @Input() decrementButtonClass: string | undefined;\n    /**\n     * Style class of the increment button.\n     * @group Props\n     */\n    @Input() incrementButtonIcon: string | undefined;\n    /**\n     * Style class of the decrement button.\n     * @group Props\n     */\n    @Input() decrementButtonIcon: string | undefined;\n    /**\n     * When present, it specifies that an input field is read-only.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean = false;\n    /**\n     * Step factor to increment/decrement the value.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) step: number = 1;\n    /**\n     * Determines whether the input field is empty.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) allowEmpty: boolean = true;\n    /**\n     * Locale to be used in formatting.\n     * @group Props\n     */\n    @Input() locale: string | undefined;\n    /**\n     * The locale matching algorithm to use. Possible values are \"lookup\" and \"best fit\"; the default is \"best fit\". See Locale Negotiation for details.\n     * @group Props\n     */\n    @Input() localeMatcher: string | undefined;\n    /**\n     * Defines the behavior of the component, valid values are \"decimal\" and \"currency\".\n     * @group Props\n     */\n    @Input() mode: string = 'decimal';\n    /**\n     * The currency to use in currency formatting. Possible values are the ISO 4217 currency codes, such as \"USD\" for the US dollar, \"EUR\" for the euro, or \"CNY\" for the Chinese RMB. There is no default value; if the style is \"currency\", the currency property must be provided.\n     * @group Props\n     */\n    @Input() currency: string | undefined;\n    /**\n     * How to display the currency in currency formatting. Possible values are \"symbol\" to use a localized currency symbol such as €, ü\"code\" to use the ISO currency code, \"name\" to use a localized currency name such as \"dollar\"; the default is \"symbol\".\n     * @group Props\n     */\n    @Input() currencyDisplay: string | undefined;\n    /**\n     * Whether to use grouping separators, such as thousands separators or thousand/lakh/crore separators.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) useGrouping: boolean = true;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * The minimum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number and percent formatting is 0; the default for currency formatting is the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n     * @group Props\n     */\n    @Input({ transform: (value: unknown) => numberAttribute(value, null) }) minFractionDigits: number | undefined;\n    /**\n     * The maximum number of fraction digits to use. Possible values are from 0 to 20; the default for plain number formatting is the larger of minimumFractionDigits and 3; the default for currency formatting is the larger of minimumFractionDigits and the number of minor unit digits provided by the ISO 4217 currency code list (2 if the list doesn't provide that information).\n     * @group Props\n     */\n    @Input({ transform: (value: unknown) => numberAttribute(value, null) }) maxFractionDigits: number | undefined;\n    /**\n     * Text to display before the value.\n     * @group Props\n     */\n    @Input() prefix: string | undefined;\n    /**\n     * Text to display after the value.\n     * @group Props\n     */\n    @Input() suffix: string | undefined;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    @Input() inputStyle: any;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    @Input() inputStyleClass: string | undefined;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean = false;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input() get disabled(): boolean | undefined {\n        return this._disabled;\n    }\n    set disabled(disabled: boolean | undefined) {\n        if (disabled) this.focused = false;\n\n        this._disabled = disabled;\n\n        if (this.timer) this.clearTimer();\n    }\n    /**\n     * Callback to invoke on input.\n     * @param {InputNumberInputEvent} event - Custom input event.\n     * @group Emits\n     */\n    @Output() onInput: EventEmitter<InputNumberInputEvent> = new EventEmitter<InputNumberInputEvent>();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke on input key press.\n     * @param {KeyboardEvent} event - Keyboard event.\n     * @group Emits\n     */\n    @Output() onKeyDown: EventEmitter<KeyboardEvent> = new EventEmitter<KeyboardEvent>();\n    /**\n     * Callback to invoke when clear token is clicked.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<void> = new EventEmitter<void>();\n\n    @ViewChild('input') input!: ElementRef<HTMLInputElement>;\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    clearIconTemplate: Nullable<TemplateRef<any>>;\n\n    incrementButtonIconTemplate: Nullable<TemplateRef<any>>;\n\n    decrementButtonIconTemplate: Nullable<TemplateRef<any>>;\n\n    value: Nullable<number>;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    focused: Nullable<boolean>;\n\n    initialized: Nullable<boolean>;\n\n    groupChar: string = '';\n\n    prefixChar: string = '';\n\n    suffixChar: string = '';\n\n    isSpecialChar: Nullable<boolean>;\n\n    timer: any;\n\n    lastValue: Nullable<string>;\n\n    _numeral: any;\n\n    numberFormat: any;\n\n    _decimal: any;\n\n    _decimalChar: string;\n\n    _group: any;\n\n    _minusSign: any;\n\n    _currency: Nullable<RegExp | string>;\n\n    _prefix: Nullable<RegExp>;\n\n    _suffix: Nullable<RegExp>;\n\n    _index: number | any;\n\n    _disabled: boolean | undefined;\n\n    private ngControl: NgControl | null = null;\n\n    constructor(@Inject(DOCUMENT) private document: Document, public el: ElementRef, private cd: ChangeDetectorRef, private readonly injector: Injector, public config: PrimeNGConfig) {}\n\n    ngOnChanges(simpleChange: SimpleChanges) {\n        const props = ['locale', 'localeMatcher', 'mode', 'currency', 'currencyDisplay', 'useGrouping', 'minFractionDigits', 'maxFractionDigits', 'prefix', 'suffix'];\n        if (props.some((p) => !!simpleChange[p])) {\n            this.updateConstructParser();\n        }\n    }\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n\n                case 'incrementbuttonicon':\n                    this.incrementButtonIconTemplate = item.template;\n                    break;\n\n                case 'decrementbuttonicon':\n                    this.decrementButtonIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngOnInit() {\n        this.ngControl = this.injector.get(NgControl, null, { optional: true });\n\n        this.constructParser();\n\n        this.initialized = true;\n    }\n\n    getOptions() {\n        return {\n            localeMatcher: this.localeMatcher,\n            style: this.mode,\n            currency: this.currency,\n            currencyDisplay: this.currencyDisplay,\n            useGrouping: this.useGrouping,\n            minimumFractionDigits: this.minFractionDigits ?? undefined,\n            maximumFractionDigits: this.maxFractionDigits ?? undefined\n        };\n    }\n\n    constructParser() {\n        this.numberFormat = new Intl.NumberFormat(this.locale, this.getOptions());\n        const numerals = [...new Intl.NumberFormat(this.locale, { useGrouping: false }).format(9876543210)].reverse();\n        const index = new Map(numerals.map((d, i) => [d, i]));\n        this._numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n        this._group = this.getGroupingExpression();\n        this._minusSign = this.getMinusSignExpression();\n        this._currency = this.getCurrencyExpression();\n        this._decimal = this.getDecimalExpression();\n        this._decimalChar = this.getDecimalChar();\n        this._suffix = this.getSuffixExpression();\n        this._prefix = this.getPrefixExpression();\n        this._index = (d: any) => index.get(d);\n    }\n\n    updateConstructParser() {\n        if (this.initialized) {\n            this.constructParser();\n        }\n    }\n\n    escapeRegExp(text: string): string {\n        return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n    }\n\n    getDecimalExpression(): RegExp {\n        const decimalChar = this.getDecimalChar();\n        return new RegExp(`[${decimalChar}]`, 'g');\n    }\n\n    getDecimalChar(): string {\n        const formatter = new Intl.NumberFormat(this.locale, { ...this.getOptions(), useGrouping: false });\n        return formatter\n            .format(1.1)\n            .replace(this._currency as RegExp | string, '')\n            .trim()\n            .replace(this._numeral, '');\n    }\n\n    getGroupingExpression(): RegExp {\n        const formatter = new Intl.NumberFormat(this.locale, { useGrouping: true });\n        this.groupChar = formatter.format(1000000).trim().replace(this._numeral, '').charAt(0);\n        return new RegExp(`[${this.groupChar}]`, 'g');\n    }\n\n    getMinusSignExpression(): RegExp {\n        const formatter = new Intl.NumberFormat(this.locale, { useGrouping: false });\n        return new RegExp(`[${formatter.format(-1).trim().replace(this._numeral, '')}]`, 'g');\n    }\n\n    getCurrencyExpression(): RegExp {\n        if (this.currency) {\n            const formatter = new Intl.NumberFormat(this.locale, { style: 'currency', currency: this.currency, currencyDisplay: this.currencyDisplay, minimumFractionDigits: 0, maximumFractionDigits: 0 });\n            return new RegExp(`[${formatter.format(1).replace(/\\s/g, '').replace(this._numeral, '').replace(this._group, '')}]`, 'g');\n        }\n\n        return new RegExp(`[]`, 'g');\n    }\n\n    getPrefixExpression(): RegExp {\n        if (this.prefix) {\n            this.prefixChar = this.prefix;\n        } else {\n            const formatter = new Intl.NumberFormat(this.locale, { style: this.mode, currency: this.currency, currencyDisplay: this.currencyDisplay });\n            this.prefixChar = formatter.format(1).split('1')[0];\n        }\n\n        return new RegExp(`${this.escapeRegExp(this.prefixChar || '')}`, 'g');\n    }\n\n    getSuffixExpression(): RegExp {\n        if (this.suffix) {\n            this.suffixChar = this.suffix;\n        } else {\n            const formatter = new Intl.NumberFormat(this.locale, { style: this.mode, currency: this.currency, currencyDisplay: this.currencyDisplay, minimumFractionDigits: 0, maximumFractionDigits: 0 });\n            this.suffixChar = formatter.format(1).split('1')[1];\n        }\n\n        return new RegExp(`${this.escapeRegExp(this.suffixChar || '')}`, 'g');\n    }\n\n    get isBlurUpdateOnMode() {\n        return this.ngControl?.control?.updateOn === 'blur';\n    }\n\n    formatValue(value: any) {\n        if (value != null) {\n            if (value === '-') {\n                // Minus sign\n                return value;\n            }\n\n            if (this.format) {\n                let formatter = new Intl.NumberFormat(this.locale, this.getOptions());\n                let formattedValue = formatter.format(value);\n\n                if (this.prefix && value != this.prefix) {\n                    formattedValue = this.prefix + formattedValue;\n                }\n\n                if (this.suffix && value != this.suffix) {\n                    formattedValue = formattedValue + this.suffix;\n                }\n\n                return formattedValue;\n            }\n\n            return value.toString();\n        }\n\n        return '';\n    }\n\n    parseValue(text: any) {\n        const suffixRegex = new RegExp(this._suffix, '');\n        const prefixRegex = new RegExp(this._prefix, '');\n        const currencyRegex = new RegExp(this._currency, '');\n\n        let filteredText = text\n            .replace(suffixRegex, '')\n            .replace(prefixRegex, '')\n            .trim()\n            .replace(/\\s/g, '')\n            .replace(currencyRegex, '')\n            .replace(this._group, '')\n            .replace(this._minusSign, '-')\n            .replace(this._decimal, '.')\n            .replace(this._numeral, this._index);\n\n        if (filteredText) {\n            if (filteredText === '-')\n                // Minus sign\n                return filteredText;\n\n            let parsedValue = +filteredText;\n            return isNaN(parsedValue) ? null : parsedValue;\n        }\n\n        return null;\n    }\n\n    repeat(event: Event, interval: number | null, dir: number) {\n        if (this.readonly) {\n            return;\n        }\n\n        let i = interval || 500;\n\n        this.clearTimer();\n        this.timer = setTimeout(() => {\n            this.repeat(event, 40, dir);\n        }, i);\n\n        this.spin(event, dir);\n    }\n\n    spin(event: Event, dir: number) {\n        let step = this.step * dir;\n        let currentValue = this.parseValue(this.input?.nativeElement.value) || 0;\n        let newValue = this.validateValue((currentValue as number) + step);\n        if (this.maxlength && this.maxlength < this.formatValue(newValue).length) {\n            return;\n        }\n        this.updateInput(newValue, null, 'spin', null);\n        this.updateModel(event, newValue);\n\n        this.handleOnInput(event, currentValue, newValue);\n    }\n\n    clear() {\n        this.value = null;\n        this.onModelChange(this.value);\n        this.onClear.emit();\n    }\n\n    onUpButtonMouseDown(event: MouseEvent) {\n        if (event.button === 2) {\n            this.clearTimer();\n            return;\n        }\n\n        if (!this.disabled) {\n            this.input?.nativeElement.focus();\n            this.repeat(event, null, 1);\n            event.preventDefault();\n        }\n    }\n\n    onUpButtonMouseUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onUpButtonMouseLeave() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onUpButtonKeyDown(event: KeyboardEvent) {\n        if (event.keyCode === 32 || event.keyCode === 13) {\n            this.repeat(event, null, 1);\n        }\n    }\n\n    onUpButtonKeyUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onDownButtonMouseDown(event: MouseEvent) {\n        if (event.button === 2) {\n            this.clearTimer();\n            return;\n        }\n        if (!this.disabled) {\n            this.input?.nativeElement.focus();\n            this.repeat(event, null, -1);\n            event.preventDefault();\n        }\n    }\n\n    onDownButtonMouseUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onDownButtonMouseLeave() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onDownButtonKeyUp() {\n        if (!this.disabled) {\n            this.clearTimer();\n        }\n    }\n\n    onDownButtonKeyDown(event: KeyboardEvent) {\n        if (event.keyCode === 32 || event.keyCode === 13) {\n            this.repeat(event, null, -1);\n        }\n    }\n\n    onUserInput(event: Event) {\n        if (this.readonly) {\n            return;\n        }\n\n        if (this.isSpecialChar) {\n            (event.target as HTMLInputElement).value = this.lastValue as string;\n        }\n        this.isSpecialChar = false;\n    }\n\n    onInputKeyDown(event: KeyboardEvent) {\n        if (this.readonly) {\n            return;\n        }\n\n        this.lastValue = (event.target as HTMLInputElement).value;\n        if ((event as KeyboardEvent).shiftKey || (event as KeyboardEvent).altKey) {\n            this.isSpecialChar = true;\n            return;\n        }\n\n        let selectionStart = (event.target as HTMLInputElement).selectionStart as number;\n        let selectionEnd = (event.target as HTMLInputElement).selectionEnd as number;\n        let inputValue = (event.target as HTMLInputElement).value as string;\n        let newValueStr = null;\n\n        if (event.altKey) {\n            event.preventDefault();\n        }\n\n        switch (event.key) {\n            case 'ArrowUp':\n                this.spin(event, 1);\n                event.preventDefault();\n                break;\n\n            case 'ArrowDown':\n                this.spin(event, -1);\n                event.preventDefault();\n                break;\n\n            case 'ArrowLeft':\n                for (let index = selectionStart; index <= inputValue.length; index++) {\n                    const previousCharIndex = index === 0 ? 0 : index - 1;\n                    if (this.isNumeralChar(inputValue.charAt(previousCharIndex))) {\n                        this.input.nativeElement.setSelectionRange(index, index);\n                        break;\n                    }\n                }\n                break;\n\n            case 'ArrowRight':\n                for (let index = selectionEnd; index >= 0; index--) {\n                    if (this.isNumeralChar(inputValue.charAt(index))) {\n                        this.input.nativeElement.setSelectionRange(index, index);\n                        break;\n                    }\n                }\n                break;\n\n            case 'Tab':\n            case 'Enter':\n                newValueStr = this.validateValue(this.parseValue(this.input.nativeElement.value));\n                this.input.nativeElement.value = this.formatValue(newValueStr);\n                this.input.nativeElement.setAttribute('aria-valuenow', newValueStr);\n                this.updateModel(event, newValueStr);\n                break;\n\n            case 'Backspace': {\n                event.preventDefault();\n\n                if (selectionStart === selectionEnd) {\n                    if ((selectionStart == 1 && this.prefix) || (selectionStart == inputValue.length && this.suffix)) {\n                        break;\n                    }\n\n                    const deleteChar = inputValue.charAt(selectionStart - 1);\n                    const { decimalCharIndex, decimalCharIndexWithoutPrefix } = this.getDecimalCharIndexes(inputValue);\n\n                    if (this.isNumeralChar(deleteChar)) {\n                        const decimalLength = this.getDecimalLength(inputValue);\n\n                        if (this._group.test(deleteChar)) {\n                            this._group.lastIndex = 0;\n                            newValueStr = inputValue.slice(0, selectionStart - 2) + inputValue.slice(selectionStart - 1);\n                        } else if (this._decimal.test(deleteChar)) {\n                            this._decimal.lastIndex = 0;\n\n                            if (decimalLength) {\n                                this.input?.nativeElement.setSelectionRange(selectionStart - 1, selectionStart - 1);\n                            } else {\n                                newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                            }\n                        } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                            const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + insertedText + inputValue.slice(selectionStart);\n                        } else if (decimalCharIndexWithoutPrefix === 1) {\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + '0' + inputValue.slice(selectionStart);\n                            newValueStr = (this.parseValue(newValueStr) as number) > 0 ? newValueStr : '';\n                        } else {\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                        }\n                    } else if (this.mode === 'currency' && deleteChar.search(this._currency) != -1) {\n                        newValueStr = inputValue.slice(1);\n                    }\n\n                    this.updateValue(event, newValueStr, null, 'delete-single');\n                } else {\n                    newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n                    this.updateValue(event, newValueStr, null, 'delete-range');\n                }\n\n                break;\n            }\n\n            case 'Delete':\n                event.preventDefault();\n\n                if (selectionStart === selectionEnd) {\n                    if ((selectionStart == 0 && this.prefix) || (selectionStart == inputValue.length - 1 && this.suffix)) {\n                        break;\n                    }\n                    const deleteChar = inputValue.charAt(selectionStart);\n                    const { decimalCharIndex, decimalCharIndexWithoutPrefix } = this.getDecimalCharIndexes(inputValue);\n\n                    if (this.isNumeralChar(deleteChar)) {\n                        const decimalLength = this.getDecimalLength(inputValue);\n\n                        if (this._group.test(deleteChar)) {\n                            this._group.lastIndex = 0;\n                            newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 2);\n                        } else if (this._decimal.test(deleteChar)) {\n                            this._decimal.lastIndex = 0;\n\n                            if (decimalLength) {\n                                this.input?.nativeElement.setSelectionRange(selectionStart + 1, selectionStart + 1);\n                            } else {\n                                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                            }\n                        } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                            const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                            newValueStr = inputValue.slice(0, selectionStart) + insertedText + inputValue.slice(selectionStart + 1);\n                        } else if (decimalCharIndexWithoutPrefix === 1) {\n                            newValueStr = inputValue.slice(0, selectionStart) + '0' + inputValue.slice(selectionStart + 1);\n                            newValueStr = (this.parseValue(newValueStr) as number) > 0 ? newValueStr : '';\n                        } else {\n                            newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                        }\n                    }\n\n                    this.updateValue(event, newValueStr as string, null, 'delete-back-single');\n                } else {\n                    newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n                    this.updateValue(event, newValueStr, null, 'delete-range');\n                }\n                break;\n\n            case 'Home':\n                if (this.min) {\n                    this.updateModel(event, this.min);\n                    event.preventDefault();\n                }\n                break;\n\n            case 'End':\n                if (this.max) {\n                    this.updateModel(event, this.max);\n                    event.preventDefault();\n                }\n                break;\n\n            default:\n                break;\n        }\n\n        this.onKeyDown.emit(event);\n    }\n\n    onInputKeyPress(event: KeyboardEvent) {\n        if (this.readonly) {\n            return;\n        }\n\n        let code = event.which || event.keyCode;\n        let char = String.fromCharCode(code);\n        let isDecimalSign = this.isDecimalSign(char);\n        const isMinusSign = this.isMinusSign(char);\n\n        if (code != 13) {\n            event.preventDefault();\n        }\n        if (!isDecimalSign && event.code === 'NumpadDecimal') {\n            isDecimalSign = true;\n            char = this._decimalChar;\n            code = char.charCodeAt(0);\n        }\n        const newValue = this.parseValue(this.input.nativeElement.value + char);\n        const newValueStr = newValue != null ? newValue.toString() : '';\n\n        if (this.maxlength && this.getSelectedText()?.length == this.maxlength) {\n            this.insert(event, char, { isDecimalSign, isMinusSign });\n            return;\n        }\n\n        if (this.maxlength && newValueStr.length > this.maxlength) {\n            return;\n        }\n\n        if ((48 <= code && code <= 57) || isMinusSign || isDecimalSign) {\n            this.insert(event, char, { isDecimalSign, isMinusSign });\n        }\n    }\n\n    private getSelectedText() {\n        return (\n            window\n                ?.getSelection()\n                ?.toString()\n                .replaceAll(/[^0-9']/g, '') || ''\n        );\n    }\n\n    onPaste(event: ClipboardEvent) {\n        if (!this.disabled && !this.readonly) {\n            event.preventDefault();\n            let data = (event.clipboardData || (this.document as any).defaultView['clipboardData']).getData('Text');\n            if (data) {\n                if (this.maxlength) {\n                    data = data.toString().substring(0, this.maxlength);\n                }\n\n                let filteredData = this.parseValue(data);\n                if (filteredData != null) {\n                    this.insert(event, filteredData.toString());\n                }\n            }\n        }\n    }\n\n    allowMinusSign() {\n        return this.min == null || this.min < 0;\n    }\n\n    isMinusSign(char: string) {\n        if (this._minusSign.test(char) || char === '-') {\n            this._minusSign.lastIndex = 0;\n            return true;\n        }\n\n        return false;\n    }\n\n    isDecimalSign(char: string) {\n        if (this._decimal.test(char)) {\n            this._decimal.lastIndex = 0;\n            return true;\n        }\n\n        return false;\n    }\n\n    isDecimalMode() {\n        return this.mode === 'decimal';\n    }\n\n    getDecimalCharIndexes(val: string) {\n        let decimalCharIndex = val.search(this._decimal);\n        this._decimal.lastIndex = 0;\n\n        const filteredVal = val\n            .replace(this._prefix as RegExp, '')\n            .trim()\n            .replace(/\\s/g, '')\n            .replace(this._currency as RegExp, '');\n        const decimalCharIndexWithoutPrefix = filteredVal.search(this._decimal);\n        this._decimal.lastIndex = 0;\n\n        return { decimalCharIndex, decimalCharIndexWithoutPrefix };\n    }\n\n    getCharIndexes(val: string) {\n        const decimalCharIndex = val.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        const minusCharIndex = val.search(this._minusSign);\n        this._minusSign.lastIndex = 0;\n        const suffixCharIndex = val.search(this._suffix as RegExp);\n        (this._suffix as RegExp).lastIndex = 0;\n        const currencyCharIndex = val.search(this._currency as RegExp);\n        (this._currency as RegExp).lastIndex = 0;\n\n        return { decimalCharIndex, minusCharIndex, suffixCharIndex, currencyCharIndex };\n    }\n\n    insert(event: Event, text: string, sign = { isDecimalSign: false, isMinusSign: false }) {\n        const minusCharIndexOnText = text.search(this._minusSign);\n        this._minusSign.lastIndex = 0;\n        if (!this.allowMinusSign() && minusCharIndexOnText !== -1) {\n            return;\n        }\n\n        let selectionStart = this.input?.nativeElement.selectionStart;\n        let selectionEnd = this.input?.nativeElement.selectionEnd;\n        let inputValue = this.input?.nativeElement.value.trim();\n        const { decimalCharIndex, minusCharIndex, suffixCharIndex, currencyCharIndex } = this.getCharIndexes(inputValue);\n        let newValueStr;\n\n        if (sign.isMinusSign) {\n            if (selectionStart === 0) {\n                newValueStr = inputValue;\n                if (minusCharIndex === -1 || selectionEnd !== 0) {\n                    newValueStr = this.insertText(inputValue, text, 0, selectionEnd);\n                }\n\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n        } else if (sign.isDecimalSign) {\n            if (decimalCharIndex > 0 && selectionStart === decimalCharIndex) {\n                this.updateValue(event, inputValue, text, 'insert');\n            } else if (decimalCharIndex > selectionStart && decimalCharIndex < selectionEnd) {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, 'insert');\n            } else if (decimalCharIndex === -1 && this.maxFractionDigits) {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n        } else {\n            const maxFractionDigits = this.numberFormat.resolvedOptions().maximumFractionDigits;\n            const operation = selectionStart !== selectionEnd ? 'range-insert' : 'insert';\n\n            if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                if (selectionStart + text.length - (decimalCharIndex + 1) <= maxFractionDigits) {\n                    const charIndex = currencyCharIndex >= selectionStart ? currencyCharIndex - 1 : suffixCharIndex >= selectionStart ? suffixCharIndex : inputValue.length;\n\n                    newValueStr = inputValue.slice(0, selectionStart) + text + inputValue.slice(selectionStart + text.length, charIndex) + inputValue.slice(charIndex);\n                    this.updateValue(event, newValueStr, text, operation);\n                }\n            } else {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, operation);\n            }\n        }\n    }\n\n    insertText(value: string, text: string, start: number, end: number) {\n        let textSplit = text === '.' ? text : text.split('.');\n\n        if (textSplit.length === 2) {\n            const decimalCharIndex = value.slice(start, end).search(this._decimal);\n            this._decimal.lastIndex = 0;\n            return decimalCharIndex > 0 ? value.slice(0, start) + this.formatValue(text) + value.slice(end) : value || this.formatValue(text);\n        } else if (end - start === value.length) {\n            return this.formatValue(text);\n        } else if (start === 0) {\n            return text + value.slice(end);\n        } else if (end === value.length) {\n            return value.slice(0, start) + text;\n        } else {\n            return value.slice(0, start) + text + value.slice(end);\n        }\n    }\n\n    deleteRange(value: string, start: number, end: number) {\n        let newValueStr;\n\n        if (end - start === value.length) newValueStr = '';\n        else if (start === 0) newValueStr = value.slice(end);\n        else if (end === value.length) newValueStr = value.slice(0, start);\n        else newValueStr = value.slice(0, start) + value.slice(end);\n\n        return newValueStr;\n    }\n\n    initCursor() {\n        let selectionStart = this.input?.nativeElement.selectionStart;\n        let selectionEnd = this.input?.nativeElement.selectionEnd;\n        let inputValue = this.input?.nativeElement.value;\n        let valueLength = inputValue.length;\n        let index = null;\n\n        // remove prefix\n        let prefixLength = (this.prefixChar || '').length;\n        inputValue = inputValue.replace(this._prefix, '');\n\n        // Will allow selecting whole prefix. But not a part of it.\n        // Negative values will trigger clauses after this to fix the cursor position.\n        if (selectionStart === selectionEnd || selectionStart !== 0 || selectionEnd < prefixLength) {\n            selectionStart -= prefixLength;\n        }\n\n        let char = inputValue.charAt(selectionStart);\n        if (this.isNumeralChar(char)) {\n            return selectionStart + prefixLength;\n        }\n\n        //left\n        let i = selectionStart - 1;\n        while (i >= 0) {\n            char = inputValue.charAt(i);\n            if (this.isNumeralChar(char)) {\n                index = i + prefixLength;\n                break;\n            } else {\n                i--;\n            }\n        }\n\n        if (index !== null) {\n            this.input?.nativeElement.setSelectionRange(index + 1, index + 1);\n        } else {\n            i = selectionStart;\n            while (i < valueLength) {\n                char = inputValue.charAt(i);\n                if (this.isNumeralChar(char)) {\n                    index = i + prefixLength;\n                    break;\n                } else {\n                    i++;\n                }\n            }\n\n            if (index !== null) {\n                this.input?.nativeElement.setSelectionRange(index, index);\n            }\n        }\n\n        return index || 0;\n    }\n\n    onInputClick() {\n        const currentValue = this.input?.nativeElement.value;\n\n        if (!this.readonly && currentValue !== DomHandler.getSelection()) {\n            this.initCursor();\n        }\n    }\n\n    isNumeralChar(char: string) {\n        if (char.length === 1 && (this._numeral.test(char) || this._decimal.test(char) || this._group.test(char) || this._minusSign.test(char))) {\n            this.resetRegex();\n            return true;\n        }\n\n        return false;\n    }\n\n    resetRegex() {\n        this._numeral.lastIndex = 0;\n        this._decimal.lastIndex = 0;\n        this._group.lastIndex = 0;\n        this._minusSign.lastIndex = 0;\n    }\n\n    updateValue(event: Event, valueStr: Nullable<string>, insertedValueStr: Nullable<string>, operation: Nullable<string>) {\n        let currentValue = this.input?.nativeElement.value;\n        let newValue = null;\n\n        if (valueStr != null) {\n            newValue = this.parseValue(valueStr);\n            newValue = !newValue && !this.allowEmpty ? 0 : newValue;\n            this.updateInput(newValue, insertedValueStr, operation, valueStr);\n\n            this.handleOnInput(event, currentValue, newValue);\n        }\n    }\n\n    handleOnInput(event: Event, currentValue: string, newValue: any) {\n        if (this.isValueChanged(currentValue, newValue)) {\n            (this.input as ElementRef).nativeElement.value = this.formatValue(newValue);\n            this.input?.nativeElement.setAttribute('aria-valuenow', newValue);\n            !this.isBlurUpdateOnMode && this.updateModel(event, newValue);\n            this.onInput.emit({ originalEvent: event, value: newValue, formattedValue: currentValue });\n        }\n    }\n\n    isValueChanged(currentValue: string, newValue: string) {\n        if (newValue === null && currentValue !== null) {\n            return true;\n        }\n\n        if (newValue != null) {\n            let parsedCurrentValue = typeof currentValue === 'string' ? this.parseValue(currentValue) : currentValue;\n            return newValue !== parsedCurrentValue;\n        }\n\n        return false;\n    }\n\n    validateValue(value: number | string) {\n        if (value === '-' || value == null) {\n            return null;\n        }\n\n        if (this.min != null && (value as number) < this.min) {\n            return this.min;\n        }\n\n        if (this.max != null && (value as number) > this.max) {\n            return this.max;\n        }\n\n        return value;\n    }\n\n    updateInput(value: any, insertedValueStr: Nullable<string>, operation: Nullable<string>, valueStr: Nullable<string>) {\n        insertedValueStr = insertedValueStr || '';\n\n        let inputValue = this.input?.nativeElement.value;\n        let newValue = this.formatValue(value);\n        let currentLength = inputValue.length;\n\n        if (newValue !== valueStr) {\n            newValue = this.concatValues(newValue, valueStr as string);\n        }\n\n        if (currentLength === 0) {\n            this.input.nativeElement.value = newValue;\n            this.input.nativeElement.setSelectionRange(0, 0);\n            const index = this.initCursor();\n            const selectionEnd = index + insertedValueStr.length;\n            this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        } else {\n            let selectionStart = this.input.nativeElement.selectionStart;\n            let selectionEnd = this.input.nativeElement.selectionEnd;\n\n            if (this.maxlength && newValue.length > this.maxlength) {\n                newValue = newValue.slice(0, this.maxlength);\n                selectionStart = Math.min(selectionStart, this.maxlength);\n                selectionEnd = Math.min(selectionEnd, this.maxlength);\n            }\n\n            if (this.maxlength && this.maxlength < newValue.length) {\n                return;\n            }\n\n            this.input.nativeElement.value = newValue;\n            let newLength = newValue.length;\n\n            if (operation === 'range-insert') {\n                const startValue = this.parseValue((inputValue || '').slice(0, selectionStart));\n                const startValueStr = startValue !== null ? startValue.toString() : '';\n                const startExpr = startValueStr.split('').join(`(${this.groupChar})?`);\n                const sRegex = new RegExp(startExpr, 'g');\n                sRegex.test(newValue);\n\n                const tExpr = insertedValueStr.split('').join(`(${this.groupChar})?`);\n                const tRegex = new RegExp(tExpr, 'g');\n                tRegex.test(newValue.slice(sRegex.lastIndex));\n\n                selectionEnd = sRegex.lastIndex + tRegex.lastIndex;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            } else if (newLength === currentLength) {\n                if (operation === 'insert' || operation === 'delete-back-single') this.input.nativeElement.setSelectionRange(selectionEnd + 1, selectionEnd + 1);\n                else if (operation === 'delete-single') this.input.nativeElement.setSelectionRange(selectionEnd - 1, selectionEnd - 1);\n                else if (operation === 'delete-range' || operation === 'spin') this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            } else if (operation === 'delete-back-single') {\n                let prevChar = inputValue.charAt(selectionEnd - 1);\n                let nextChar = inputValue.charAt(selectionEnd);\n                let diff = currentLength - newLength;\n                let isGroupChar = this._group.test(nextChar);\n\n                if (isGroupChar && diff === 1) {\n                    selectionEnd += 1;\n                } else if (!isGroupChar && this.isNumeralChar(prevChar)) {\n                    selectionEnd += -1 * diff + 1;\n                }\n\n                this._group.lastIndex = 0;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            } else if (inputValue === '-' && operation === 'insert') {\n                this.input.nativeElement.setSelectionRange(0, 0);\n                const index = this.initCursor();\n                const selectionEnd = index + insertedValueStr.length + 1;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            } else {\n                selectionEnd = selectionEnd + (newLength - currentLength);\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n        }\n\n        this.input.nativeElement.setAttribute('aria-valuenow', value);\n    }\n\n    concatValues(val1: string, val2: string) {\n        if (val1 && val2) {\n            let decimalCharIndex = val2.search(this._decimal);\n            this._decimal.lastIndex = 0;\n\n            if (this.suffixChar) {\n                return decimalCharIndex !== -1 ? val1 : val1.replace(this.suffixChar, '').split(this._decimal)[0] + val2.replace(this.suffixChar, '').slice(decimalCharIndex) + this.suffixChar;\n            } else {\n                return decimalCharIndex !== -1 ? val1.split(this._decimal)[0] + val2.slice(decimalCharIndex) : val1;\n            }\n        }\n        return val1;\n    }\n\n    getDecimalLength(value: string) {\n        if (value) {\n            const valueSplit = value.split(this._decimal);\n\n            if (valueSplit.length === 2) {\n                return valueSplit[1]\n                    .replace(this._suffix as RegExp, '')\n                    .trim()\n                    .replace(/\\s/g, '')\n                    .replace(this._currency as RegExp, '').length;\n            }\n        }\n\n        return 0;\n    }\n\n    onInputFocus(event: Event) {\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event: Event) {\n        this.focused = false;\n\n        const newValueNumber = this.validateValue(this.parseValue(this.input.nativeElement.value));\n        const newValueString = newValueNumber?.toString();\n        this.input.nativeElement.value = this.formatValue(newValueString);\n        this.input.nativeElement.setAttribute('aria-valuenow', newValueString);\n        this.updateModel(event, newValueNumber);\n        this.onBlur.emit(event);\n    }\n\n    formattedValue() {\n        const val = !this.value && !this.allowEmpty ? 0 : this.value;\n        return this.formatValue(val);\n    }\n\n    updateModel(event: Event, value: any) {\n        if (this.value !== value) {\n            this.value = value;\n\n            if (!(this.isBlurUpdateOnMode && this.focused)) {\n                this.onModelChange(value);\n            } else if (this.isBlurUpdateOnMode) {\n                this.onModelChange(value);\n            }\n        }\n        this.onModelTouched();\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    get filled() {\n        return this.value != null && this.value.toString().length > 0;\n    }\n\n    clearTimer() {\n        if (this.timer) {\n            clearInterval(this.timer);\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, InputTextModule, ButtonModule, AutoFocusModule, TimesIcon, AngleUpIcon, AngleDownIcon],\n    exports: [InputNumber, SharedModule],\n    declarations: [InputNumber]\n})\nexport class InputNumberModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAqCa,MAAA,0BAA0B,GAAQ;AAC3C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,WAAW,CAAC;AAC1C,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MAwKU,WAAW,CAAA;AAkTkB,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAAwC,IAAA,QAAA,CAAA;AAA2B,IAAA,MAAA,CAAA;AAjT5J;;;AAGG;IACqC,WAAW,GAAY,KAAK,CAAC;AACrE;;;AAGG;IACqC,MAAM,GAAY,IAAI,CAAC;AAC/D;;;AAGG;IACM,YAAY,GAAW,SAAS,CAAC;AAC1C;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACoC,IAAA,IAAI,CAAqB;AAChE;;;AAGG;AACoC,IAAA,SAAS,CAAqB;AACrE;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACoC,IAAA,GAAG,CAAqB;AAC/D;;;AAGG;AACoC,IAAA,GAAG,CAAqB;AAC/D;;;AAGG;AACM,IAAA,oBAAoB,CAAqB;AAClD;;;AAGG;AACM,IAAA,oBAAoB,CAAqB;AAClD;;;AAGG;AACM,IAAA,mBAAmB,CAAqB;AACjD;;;AAGG;AACM,IAAA,mBAAmB,CAAqB;AACjD;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACoC,IAAI,GAAW,CAAC,CAAC;AACxD;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;AAGG;IACM,IAAI,GAAW,SAAS,CAAC;AAClC;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;IACqC,WAAW,GAAY,IAAI,CAAC;AACpE;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;AAGG;AACqE,IAAA,iBAAiB,CAAqB;AAC9G;;;AAGG;AACqE,IAAA,iBAAiB,CAAqB;AAC9G;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACM,IAAA,UAAU,CAAM;AACzB;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,QAA6B,EAAA;AACtC,QAAA,IAAI,QAAQ;AAAE,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAEnC,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,IAAI,IAAI,CAAC,KAAK;YAAE,IAAI,CAAC,UAAU,EAAE,CAAC;KACrC;AACD;;;;AAIG;AACO,IAAA,OAAO,GAAwC,IAAI,YAAY,EAAyB,CAAC;AACnG;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,SAAS,GAAgC,IAAI,YAAY,EAAiB,CAAC;AACrF;;;AAGG;AACO,IAAA,OAAO,GAAuB,IAAI,YAAY,EAAQ,CAAC;AAE7C,IAAA,KAAK,CAAgC;AAEzB,IAAA,SAAS,CAA4B;AAErE,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,2BAA2B,CAA6B;AAExD,IAAA,2BAA2B,CAA6B;AAExD,IAAA,KAAK,CAAmB;AAExB,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,OAAO,CAAoB;AAE3B,IAAA,WAAW,CAAoB;IAE/B,SAAS,GAAW,EAAE,CAAC;IAEvB,UAAU,GAAW,EAAE,CAAC;IAExB,UAAU,GAAW,EAAE,CAAC;AAExB,IAAA,aAAa,CAAoB;AAEjC,IAAA,KAAK,CAAM;AAEX,IAAA,SAAS,CAAmB;AAE5B,IAAA,QAAQ,CAAM;AAEd,IAAA,YAAY,CAAM;AAElB,IAAA,QAAQ,CAAM;AAEd,IAAA,YAAY,CAAS;AAErB,IAAA,MAAM,CAAM;AAEZ,IAAA,UAAU,CAAM;AAEhB,IAAA,SAAS,CAA4B;AAErC,IAAA,OAAO,CAAmB;AAE1B,IAAA,OAAO,CAAmB;AAE1B,IAAA,MAAM,CAAe;AAErB,IAAA,SAAS,CAAsB;IAEvB,SAAS,GAAqB,IAAI,CAAC;IAE3C,WAAsC,CAAA,QAAkB,EAAS,EAAc,EAAU,EAAqB,EAAmB,QAAkB,EAAS,MAAqB,EAAA;QAA3I,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAmB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;AAErL,IAAA,WAAW,CAAC,YAA2B,EAAA;QACnC,MAAM,KAAK,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC9J,QAAA,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;YACtC,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAChC,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,qBAAqB;AACtB,oBAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjD,MAAM;AAEV,gBAAA,KAAK,qBAAqB;AACtB,oBAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjD,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QAExE,IAAI,CAAC,eAAe,EAAE,CAAC;AAEvB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;IAED,UAAU,GAAA;QACN,OAAO;YACH,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,KAAK,EAAE,IAAI,CAAC,IAAI;YAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;AAC7B,YAAA,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,IAAI,SAAS;AAC1D,YAAA,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,IAAI,SAAS;SAC7D,CAAC;KACL;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AAC1E,QAAA,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC9G,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,CAAI,CAAA,EAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAChD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC9C,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5C,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAM,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC1C;IAED,qBAAqB,GAAA;QACjB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,IAAY,EAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC;KAC3D;IAED,oBAAoB,GAAA;AAChB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,OAAO,IAAI,MAAM,CAAC,CAAA,CAAA,EAAI,WAAW,CAAG,CAAA,CAAA,EAAE,GAAG,CAAC,CAAC;KAC9C;IAED,cAAc,GAAA;QACV,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;AACnG,QAAA,OAAO,SAAS;aACX,MAAM,CAAC,GAAG,CAAC;AACX,aAAA,OAAO,CAAC,IAAI,CAAC,SAA4B,EAAE,EAAE,CAAC;AAC9C,aAAA,IAAI,EAAE;AACN,aAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;KACnC;IAED,qBAAqB,GAAA;AACjB,QAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvF,OAAO,IAAI,MAAM,CAAC,CAAI,CAAA,EAAA,IAAI,CAAC,SAAS,CAAG,CAAA,CAAA,EAAE,GAAG,CAAC,CAAC;KACjD;IAED,sBAAsB,GAAA;AAClB,QAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;QAC7E,OAAO,IAAI,MAAM,CAAC,CAAI,CAAA,EAAA,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAG,CAAA,CAAA,EAAE,GAAG,CAAC,CAAC;KACzF;IAED,qBAAqB,GAAA;QACjB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,qBAAqB,EAAE,CAAC,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC,CAAC;AAChM,YAAA,OAAO,IAAI,MAAM,CAAC,CAAI,CAAA,EAAA,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC7H,SAAA;AAED,QAAA,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAChC;IAED,mBAAmB,GAAA;QACf,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;AACjC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;AAC3I,YAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,SAAA;AAED,QAAA,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;KACzE;IAED,mBAAmB,GAAA;QACf,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;AACjC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,qBAAqB,EAAE,CAAC,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/L,YAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,SAAA;AAED,QAAA,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;KACzE;AAED,IAAA,IAAI,kBAAkB,GAAA;QAClB,OAAO,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,KAAK,MAAM,CAAC;KACvD;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;QAClB,IAAI,KAAK,IAAI,IAAI,EAAE;YACf,IAAI,KAAK,KAAK,GAAG,EAAE;;AAEf,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;YAED,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,gBAAA,IAAI,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;gBACtE,IAAI,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAE7C,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;AACrC,oBAAA,cAAc,GAAG,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;AACjD,iBAAA;gBAED,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;AACrC,oBAAA,cAAc,GAAG,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;AACjD,iBAAA;AAED,gBAAA,OAAO,cAAc,CAAC;AACzB,aAAA;AAED,YAAA,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC3B,SAAA;AAED,QAAA,OAAO,EAAE,CAAC;KACb;AAED,IAAA,UAAU,CAAC,IAAS,EAAA;QAChB,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAErD,IAAI,YAAY,GAAG,IAAI;AAClB,aAAA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;AACxB,aAAA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;AACxB,aAAA,IAAI,EAAE;AACN,aAAA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAClB,aAAA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;AAC1B,aAAA,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;AACxB,aAAA,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC;AAC7B,aAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC;aAC3B,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAEzC,QAAA,IAAI,YAAY,EAAE;YACd,IAAI,YAAY,KAAK,GAAG;;AAEpB,gBAAA,OAAO,YAAY,CAAC;AAExB,YAAA,IAAI,WAAW,GAAG,CAAC,YAAY,CAAC;AAChC,YAAA,OAAO,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,GAAG,WAAW,CAAC;AAClD,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED,IAAA,MAAM,CAAC,KAAY,EAAE,QAAuB,EAAE,GAAW,EAAA;QACrD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,GAAG,QAAQ,IAAI,GAAG,CAAC;QAExB,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,MAAK;YACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;SAC/B,EAAE,CAAC,CAAC,CAAC;AAEN,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACzB;IAED,IAAI,CAAC,KAAY,EAAE,GAAW,EAAA;AAC1B,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AAC3B,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzE,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAE,YAAuB,GAAG,IAAI,CAAC,CAAC;AACnE,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;YACtE,OAAO;AACV,SAAA;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAElC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;KACrD;IAED,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACvB;AAED,IAAA,mBAAmB,CAAC,KAAiB,EAAA;AACjC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAC5B,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAoB,EAAA;QAClC,IAAI,KAAK,CAAC,OAAO,KAAK,EAAE,IAAI,KAAK,CAAC,OAAO,KAAK,EAAE,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC/B,SAAA;KACJ;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,qBAAqB,CAAC,KAAiB,EAAA;AACnC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO;AACV,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7B,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,mBAAmB,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;IAED,sBAAsB,GAAA;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,mBAAmB,CAAC,KAAoB,EAAA;QACpC,IAAI,KAAK,CAAC,OAAO,KAAK,EAAE,IAAI,KAAK,CAAC,OAAO,KAAK,EAAE,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;QACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACnB,KAAK,CAAC,MAA2B,CAAC,KAAK,GAAG,IAAI,CAAC,SAAmB,CAAC;AACvE,SAAA;AACD,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;KAC9B;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;QAC/B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;QAED,IAAI,CAAC,SAAS,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;AAC1D,QAAA,IAAK,KAAuB,CAAC,QAAQ,IAAK,KAAuB,CAAC,MAAM,EAAE;AACtE,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO;AACV,SAAA;AAED,QAAA,IAAI,cAAc,GAAI,KAAK,CAAC,MAA2B,CAAC,cAAwB,CAAC;AACjF,QAAA,IAAI,YAAY,GAAI,KAAK,CAAC,MAA2B,CAAC,YAAsB,CAAC;AAC7E,QAAA,IAAI,UAAU,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAe,CAAC;QACpE,IAAI,WAAW,GAAG,IAAI,CAAC;QAEvB,IAAI,KAAK,CAAC,MAAM,EAAE;YACd,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;QAED,QAAQ,KAAK,CAAC,GAAG;AACb,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,WAAW;gBACZ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,KAAK,IAAI,KAAK,GAAG,cAAc,EAAE,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AAClE,oBAAA,MAAM,iBAAiB,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;oBACtD,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE;wBAC1D,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;wBACzD,MAAM;AACT,qBAAA;AACJ,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,YAAY;gBACb,KAAK,IAAI,KAAK,GAAG,YAAY,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE;oBAChD,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC9C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;wBACzD,MAAM;AACT,qBAAA;AACJ,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,KAAK,CAAC;AACX,YAAA,KAAK,OAAO;AACR,gBAAA,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AAClF,gBAAA,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAC/D,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;AACpE,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;gBACrC,MAAM;YAEV,KAAK,WAAW,EAAE;gBACd,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,IAAI,cAAc,KAAK,YAAY,EAAE;oBACjC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,MAAM,cAAc,IAAI,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;wBAC9F,MAAM;AACT,qBAAA;oBAED,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AACzD,oBAAA,MAAM,EAAE,gBAAgB,EAAE,6BAA6B,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAEnG,oBAAA,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;wBAChC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;wBAExD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAC9B,4BAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;4BAC1B,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AAChG,yBAAA;6BAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACvC,4BAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAE5B,4BAAA,IAAI,aAAa,EAAE;AACf,gCAAA,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,iBAAiB,CAAC,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;AACvF,6BAAA;AAAM,iCAAA;AACH,gCAAA,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC5F,6BAAA;AACJ,yBAAA;AAAM,6BAAA,IAAI,gBAAgB,GAAG,CAAC,IAAI,cAAc,GAAG,gBAAgB,EAAE;4BAClE,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,aAAa,GAAG,EAAE,GAAG,GAAG,CAAC;4BACtG,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,GAAG,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC3G,yBAAA;6BAAM,IAAI,6BAA6B,KAAK,CAAC,EAAE;4BAC5C,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC/F,4BAAA,WAAW,GAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAY,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC;AACjF,yBAAA;AAAM,6BAAA;AACH,4BAAA,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC5F,yBAAA;AACJ,qBAAA;AAAM,yBAAA,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;AAC5E,wBAAA,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrC,qBAAA;oBAED,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;AAC/D,iBAAA;AAAM,qBAAA;oBACH,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;oBACzE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;AAC9D,iBAAA;gBAED,MAAM;AACT,aAAA;AAED,YAAA,KAAK,QAAQ;gBACT,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,IAAI,cAAc,KAAK,YAAY,EAAE;oBACjC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,MAAM,cAAc,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;wBAClG,MAAM;AACT,qBAAA;oBACD,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AACrD,oBAAA,MAAM,EAAE,gBAAgB,EAAE,6BAA6B,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAEnG,oBAAA,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;wBAChC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;wBAExD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAC9B,4BAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;AAC1B,4BAAA,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AAC5F,yBAAA;6BAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACvC,4BAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAE5B,4BAAA,IAAI,aAAa,EAAE;AACf,gCAAA,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,iBAAiB,CAAC,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;AACvF,6BAAA;AAAM,iCAAA;AACH,gCAAA,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AAC5F,6BAAA;AACJ,yBAAA;AAAM,6BAAA,IAAI,gBAAgB,GAAG,CAAC,IAAI,cAAc,GAAG,gBAAgB,EAAE;4BAClE,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,aAAa,GAAG,EAAE,GAAG,GAAG,CAAC;4BACtG,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AAC3G,yBAAA;6BAAM,IAAI,6BAA6B,KAAK,CAAC,EAAE;4BAC5C,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AAC/F,4BAAA,WAAW,GAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAY,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC;AACjF,yBAAA;AAAM,6BAAA;AACH,4BAAA,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AAC5F,yBAAA;AACJ,qBAAA;oBAED,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAqB,EAAE,IAAI,EAAE,oBAAoB,CAAC,CAAC;AAC9E,iBAAA;AAAM,qBAAA;oBACH,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;oBACzE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;AAC9D,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,IAAI,CAAC,GAAG,EAAE;oBACV,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;oBAClC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,KAAK;gBACN,IAAI,IAAI,CAAC,GAAG,EAAE;oBACV,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;oBAClC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBACD,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC9B;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;QAChC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;QAED,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC;QACxC,IAAI,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,IAAI,IAAI,EAAE,EAAE;YACZ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;QACD,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE;YAClD,aAAa,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;AACzB,YAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7B,SAAA;AACD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AACxE,QAAA,MAAM,WAAW,GAAG,QAAQ,IAAI,IAAI,GAAG,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC;AAEhE,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;AACpE,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC;YACzD,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;YACvD,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,KAAK,WAAW,IAAI,aAAa,EAAE;AAC5D,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC;AAC5D,SAAA;KACJ;IAEO,eAAe,GAAA;AACnB,QAAA,QACI,MAAM;AACF,cAAE,YAAY,EAAE;AAChB,cAAE,QAAQ,EAAE;aACX,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,EAAE,EACvC;KACL;AAED,IAAA,OAAO,CAAC,KAAqB,EAAA;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,aAAa,IAAK,IAAI,CAAC,QAAgB,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACxG,YAAA,IAAI,IAAI,EAAE;gBACN,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,oBAAA,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACvD,iBAAA;gBAED,IAAI,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAI,YAAY,IAAI,IAAI,EAAE;oBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC/C,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,cAAc,GAAA;QACV,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;KAC3C;AAED,IAAA,WAAW,CAAC,IAAY,EAAA;AACpB,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,EAAE;AAC5C,YAAA,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC;AAC9B,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,aAAa,CAAC,IAAY,EAAA;QACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC1B,YAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAC5B,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,aAAa,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC;KAClC;AAED,IAAA,qBAAqB,CAAC,GAAW,EAAA;QAC7B,IAAI,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;QAE5B,MAAM,WAAW,GAAG,GAAG;AAClB,aAAA,OAAO,CAAC,IAAI,CAAC,OAAiB,EAAE,EAAE,CAAC;AACnC,aAAA,IAAI,EAAE;AACN,aAAA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAClB,aAAA,OAAO,CAAC,IAAI,CAAC,SAAmB,EAAE,EAAE,CAAC,CAAC;QAC3C,MAAM,6BAA6B,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxE,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAE5B,QAAA,OAAO,EAAE,gBAAgB,EAAE,6BAA6B,EAAE,CAAC;KAC9D;AAED,IAAA,cAAc,CAAC,GAAW,EAAA;QACtB,MAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;QAC5B,MAAM,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC;QAC9B,MAAM,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,OAAiB,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,OAAkB,CAAC,SAAS,GAAG,CAAC,CAAC;QACvC,MAAM,iBAAiB,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAmB,CAAC,CAAC;AAC9D,QAAA,IAAI,CAAC,SAAoB,CAAC,SAAS,GAAG,CAAC,CAAC;QAEzC,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,CAAC;KACnF;AAED,IAAA,MAAM,CAAC,KAAY,EAAE,IAAY,EAAE,IAAI,GAAG,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,EAAA;QAClF,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,oBAAoB,KAAK,CAAC,CAAC,EAAE;YACvD,OAAO;AACV,SAAA;QAED,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,cAAc,CAAC;QAC9D,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,YAAY,CAAC;AAC1D,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACxD,QAAA,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACjH,QAAA,IAAI,WAAW,CAAC;QAEhB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,cAAc,KAAK,CAAC,EAAE;gBACtB,WAAW,GAAG,UAAU,CAAC;gBACzB,IAAI,cAAc,KAAK,CAAC,CAAC,IAAI,YAAY,KAAK,CAAC,EAAE;AAC7C,oBAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;AACpE,iBAAA;gBAED,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACxD,aAAA;AACJ,SAAA;aAAM,IAAI,IAAI,CAAC,aAAa,EAAE;AAC3B,YAAA,IAAI,gBAAgB,GAAG,CAAC,IAAI,cAAc,KAAK,gBAAgB,EAAE;gBAC7D,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACvD,aAAA;AAAM,iBAAA,IAAI,gBAAgB,GAAG,cAAc,IAAI,gBAAgB,GAAG,YAAY,EAAE;AAC7E,gBAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;gBAC9E,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACxD,aAAA;iBAAM,IAAI,gBAAgB,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC1D,gBAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;gBAC9E,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACxD,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,qBAAqB,CAAC;AACpF,YAAA,MAAM,SAAS,GAAG,cAAc,KAAK,YAAY,GAAG,cAAc,GAAG,QAAQ,CAAC;AAE9E,YAAA,IAAI,gBAAgB,GAAG,CAAC,IAAI,cAAc,GAAG,gBAAgB,EAAE;AAC3D,gBAAA,IAAI,cAAc,GAAG,IAAI,CAAC,MAAM,IAAI,gBAAgB,GAAG,CAAC,CAAC,IAAI,iBAAiB,EAAE;oBAC5E,MAAM,SAAS,GAAG,iBAAiB,IAAI,cAAc,GAAG,iBAAiB,GAAG,CAAC,GAAG,eAAe,IAAI,cAAc,GAAG,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC;AAExJ,oBAAA,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBACnJ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACzD,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;gBAC9E,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACzD,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,GAAW,EAAA;AAC9D,QAAA,IAAI,SAAS,GAAG,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAEtD,QAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AACxB,YAAA,MAAM,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvE,YAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAC5B,YAAA,OAAO,gBAAgB,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACrI,SAAA;AAAM,aAAA,IAAI,GAAG,GAAG,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACjC,SAAA;aAAM,IAAI,KAAK,KAAK,CAAC,EAAE;YACpB,OAAO,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,SAAA;AAAM,aAAA,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM,EAAE;YAC7B,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;AACvC,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1D,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAa,EAAE,KAAa,EAAE,GAAW,EAAA;AACjD,QAAA,IAAI,WAAW,CAAC;AAEhB,QAAA,IAAI,GAAG,GAAG,KAAK,KAAK,KAAK,CAAC,MAAM;YAAE,WAAW,GAAG,EAAE,CAAC;aAC9C,IAAI,KAAK,KAAK,CAAC;AAAE,YAAA,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAChD,aAAA,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM;YAAE,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;;AAC9D,YAAA,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAE5D,QAAA,OAAO,WAAW,CAAC;KACtB;IAED,UAAU,GAAA;QACN,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,cAAc,CAAC;QAC9D,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,YAAY,CAAC;QAC1D,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC;AACjD,QAAA,IAAI,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC;QACpC,IAAI,KAAK,GAAG,IAAI,CAAC;;QAGjB,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC;QAClD,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;;;QAIlD,IAAI,cAAc,KAAK,YAAY,IAAI,cAAc,KAAK,CAAC,IAAI,YAAY,GAAG,YAAY,EAAE;YACxF,cAAc,IAAI,YAAY,CAAC;AAClC,SAAA;QAED,IAAI,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAC7C,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,cAAc,GAAG,YAAY,CAAC;AACxC,SAAA;;AAGD,QAAA,IAAI,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,EAAE;AACX,YAAA,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,YAAA,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AAC1B,gBAAA,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC;gBACzB,MAAM;AACT,aAAA;AAAM,iBAAA;AACH,gBAAA,CAAC,EAAE,CAAC;AACP,aAAA;AACJ,SAAA;QAED,IAAI,KAAK,KAAK,IAAI,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,iBAAiB,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AACrE,SAAA;AAAM,aAAA;YACH,CAAC,GAAG,cAAc,CAAC;YACnB,OAAO,CAAC,GAAG,WAAW,EAAE;AACpB,gBAAA,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,gBAAA,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AAC1B,oBAAA,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC;oBACzB,MAAM;AACT,iBAAA;AAAM,qBAAA;AACH,oBAAA,CAAC,EAAE,CAAC;AACP,iBAAA;AACJ,aAAA;YAED,IAAI,KAAK,KAAK,IAAI,EAAE;gBAChB,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC7D,aAAA;AACJ,SAAA;QAED,OAAO,KAAK,IAAI,CAAC,CAAC;KACrB;IAED,YAAY,GAAA;QACR,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC;QAErD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,YAAY,KAAK,UAAU,CAAC,YAAY,EAAE,EAAE;YAC9D,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,IAAY,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YACrI,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,UAAU,GAAA;AACN,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC;KACjC;AAED,IAAA,WAAW,CAAC,KAAY,EAAE,QAA0B,EAAE,gBAAkC,EAAE,SAA2B,EAAA;QACjH,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC;QACnD,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,IAAI,QAAQ,IAAI,IAAI,EAAE;AAClB,YAAA,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AACrC,YAAA,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,QAAQ,CAAC;YACxD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,gBAAgB,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAElE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AACrD,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,KAAY,EAAE,YAAoB,EAAE,QAAa,EAAA;QAC3D,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE;AAC5C,YAAA,IAAI,CAAC,KAAoB,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC5E,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;AAClE,YAAA,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC9D,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,CAAC;AAC9F,SAAA;KACJ;IAED,cAAc,CAAC,YAAoB,EAAE,QAAgB,EAAA;AACjD,QAAA,IAAI,QAAQ,KAAK,IAAI,IAAI,YAAY,KAAK,IAAI,EAAE;AAC5C,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAI,QAAQ,IAAI,IAAI,EAAE;AAClB,YAAA,IAAI,kBAAkB,GAAG,OAAO,YAAY,KAAK,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;YACzG,OAAO,QAAQ,KAAK,kBAAkB,CAAC;AAC1C,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,aAAa,CAAC,KAAsB,EAAA;AAChC,QAAA,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,IAAI,EAAE;AAChC,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,IAAK,KAAgB,GAAG,IAAI,CAAC,GAAG,EAAE;YAClD,OAAO,IAAI,CAAC,GAAG,CAAC;AACnB,SAAA;QAED,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,IAAK,KAAgB,GAAG,IAAI,CAAC,GAAG,EAAE;YAClD,OAAO,IAAI,CAAC,GAAG,CAAC;AACnB,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,WAAW,CAAC,KAAU,EAAE,gBAAkC,EAAE,SAA2B,EAAE,QAA0B,EAAA;AAC/G,QAAA,gBAAgB,GAAG,gBAAgB,IAAI,EAAE,CAAC;QAE1C,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC;QACjD,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACvC,QAAA,IAAI,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;QAEtC,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACvB,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAkB,CAAC,CAAC;AAC9D,SAAA;QAED,IAAI,aAAa,KAAK,CAAC,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC1C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAChC,YAAA,MAAM,YAAY,GAAG,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC;YACrD,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AAC1E,SAAA;AAAM,aAAA;YACH,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC;YAC7D,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC;YAEzD,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;gBACpD,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7C,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC1D,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACzD,aAAA;YAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAE;gBACpD,OAAO;AACV,aAAA;YAED,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC;AAC1C,YAAA,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;YAEhC,IAAI,SAAS,KAAK,cAAc,EAAE;AAC9B,gBAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,UAAU,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;AAChF,gBAAA,MAAM,aAAa,GAAG,UAAU,KAAK,IAAI,GAAG,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC;AACvE,gBAAA,MAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAI,CAAA,EAAA,IAAI,CAAC,SAAS,CAAA,EAAA,CAAI,CAAC,CAAC;gBACvE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAC1C,gBAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAEtB,gBAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAI,CAAA,EAAA,IAAI,CAAC,SAAS,CAAA,EAAA,CAAI,CAAC,CAAC;gBACtE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACtC,gBAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;gBAE9C,YAAY,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;gBACnD,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AAC1E,aAAA;iBAAM,IAAI,SAAS,KAAK,aAAa,EAAE;AACpC,gBAAA,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,oBAAoB;AAAE,oBAAA,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;qBAC5I,IAAI,SAAS,KAAK,eAAe;AAAE,oBAAA,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;AAClH,qBAAA,IAAI,SAAS,KAAK,cAAc,IAAI,SAAS,KAAK,MAAM;oBAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AACzI,aAAA;iBAAM,IAAI,SAAS,KAAK,oBAAoB,EAAE;gBAC3C,IAAI,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;gBACnD,IAAI,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAC/C,gBAAA,IAAI,IAAI,GAAG,aAAa,GAAG,SAAS,CAAC;gBACrC,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAE7C,gBAAA,IAAI,WAAW,IAAI,IAAI,KAAK,CAAC,EAAE;oBAC3B,YAAY,IAAI,CAAC,CAAC;AACrB,iBAAA;qBAAM,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;AACrD,oBAAA,YAAY,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACjC,iBAAA;AAED,gBAAA,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;gBAC1B,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AAC1E,aAAA;AAAM,iBAAA,IAAI,UAAU,KAAK,GAAG,IAAI,SAAS,KAAK,QAAQ,EAAE;gBACrD,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChC,MAAM,YAAY,GAAG,KAAK,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;gBACzD,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AAC1E,aAAA;AAAM,iBAAA;gBACH,YAAY,GAAG,YAAY,IAAI,SAAS,GAAG,aAAa,CAAC,CAAC;gBAC1D,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AAC1E,aAAA;AACJ,SAAA;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;KACjE;IAED,YAAY,CAAC,IAAY,EAAE,IAAY,EAAA;QACnC,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,IAAI,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClD,YAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;YAE5B,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,OAAO,gBAAgB,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AACnL,aAAA;AAAM,iBAAA;AACH,gBAAA,OAAO,gBAAgB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;AACvG,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED,IAAA,gBAAgB,CAAC,KAAa,EAAA;AAC1B,QAAA,IAAI,KAAK,EAAE;YACP,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAE9C,YAAA,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,OAAO,UAAU,CAAC,CAAC,CAAC;AACf,qBAAA,OAAO,CAAC,IAAI,CAAC,OAAiB,EAAE,EAAE,CAAC;AACnC,qBAAA,IAAI,EAAE;AACN,qBAAA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;qBAClB,OAAO,CAAC,IAAI,CAAC,SAAmB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;AACrD,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,CAAC,CAAC;KACZ;AAED,IAAA,YAAY,CAAC,KAAY,EAAA;AACrB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAErB,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3F,QAAA,MAAM,cAAc,GAAG,cAAc,EAAE,QAAQ,EAAE,CAAC;AAClD,QAAA,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QAClE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;AACvE,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;IAED,cAAc,GAAA;QACV,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7D,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;KAChC;IAED,WAAW,CAAC,KAAY,EAAE,KAAU,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AACtB,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YAEnB,IAAI,EAAE,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;AAC5C,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7B,aAAA;iBAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAChC,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7B,aAAA;AACJ,SAAA;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,IAAI,MAAM,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;KACjE;IAED,UAAU,GAAA;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7B,SAAA;KACJ;AA1vCQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,kBAkTA,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAlTnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAKA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAKhB,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CA8BhB,EAAA,YAAA,EAAA,cAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,CAKf,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,eAAe,CAKf,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,CAoBf,EAAA,KAAA,EAAA,OAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAUhB,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAUhB,EAAA,YAAA,EAAA,cAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAAA,eAAe,CAKf,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAAA,eAAe,CAyBf,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKhB,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,CAKf,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CA8BhB,EAAA,MAAA,EAAA,QAAA,EAAA,aAAA,EAAA,eAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAUhB,EAAA,OAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAAA,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAKhD,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAAA,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAyBhD,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAKhB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAvNzB,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,SAAA,EAAA,+BAAA,EAAA,2CAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,SAAA,EAAA,CAAC,0BAA0B,CAAC,EAsQtB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAjapB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyJT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,ohEAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA0wCuE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,WAAW,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,aAAa,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA9vCpG,WAAW,EAAA,UAAA,EAAA,CAAA;kBAvKvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyJT,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA,CAAC,0BAA0B,CAAC,EACxB,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACjC,wBAAA,+BAA+B,EAAE,QAAQ;AACzC,wBAAA,8BAA8B,EAAE,SAAS;AACzC,wBAAA,iCAAiC,EAAE,yCAAyC;AAC/E,qBAAA,EAAA,MAAA,EAAA,CAAA,ohEAAA,CAAA,EAAA,CAAA;;0BAoTY,MAAM;2BAAC,QAAQ,CAAA;qJA7SY,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKiC,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKiC,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKkC,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkE,iBAAiB,EAAA,CAAA;sBAAxF,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,SAAS,EAAE,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAA;gBAKE,iBAAiB,EAAA,CAAA;sBAAxF,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,SAAS,EAAE,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAA;gBAK7D,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAeI,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAKG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAEa,KAAK,EAAA,CAAA;sBAAxB,SAAS;uBAAC,OAAO,CAAA;gBAEc,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAsgCrB,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,iBAlwCjB,WAAW,CAAA,EAAA,OAAA,EAAA,CA8vCV,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,CA9vCpG,EAAA,OAAA,EAAA,CAAA,WAAW,EA+vCG,YAAY,CAAA,EAAA,CAAA,CAAA;AAG1B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAJhB,OAAA,EAAA,CAAA,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EACtF,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG1B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC;AAC9G,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;oBACpC,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;ACt9CD;;AAEG;;;;"}