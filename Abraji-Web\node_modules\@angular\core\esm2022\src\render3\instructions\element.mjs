/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { invalidSkipHydrationHost, validateMatchingNode, validateNodeExists, } from '../../hydration/error_handling';
import { locateNextRNode } from '../../hydration/node_lookup_utils';
import { hasSkipHydrationAttrOnRElement, hasSkipHydrationAttrOnTNode, } from '../../hydration/skip_hydration';
import { getSerializedContainerViews, isDisconnectedNode, markRNodeAsClaimedByHydration, markRNodeAsSkippedByHydration, setSegmentHead, } from '../../hydration/utils';
import { isDetachedByI18n } from '../../i18n/utils';
import { assertDefined, assertEqual, assertIndexInRange } from '../../util/assert';
import { assertFirstCreatePass, assertHasParent } from '../assert';
import { attachPatchData } from '../context_discovery';
import { registerPostOrderHooks } from '../hooks';
import { hasClassInput, hasStyleInput, } from '../interfaces/node';
import { isComponentHost, isContentQueryHost, isDirectiveHost } from '../interfaces/type_checks';
import { HEADER_OFFSET, HYDRATION, RENDERER } from '../interfaces/view';
import { assertTNodeType } from '../node_assert';
import { appendChild, clearElementContents, createElementNode, setupStaticAttributes, } from '../node_manipulation';
import { decreaseElementDepthCount, enterSkipHydrationBlock, getBindingIndex, getCurrentTNode, getElementDepthCount, getLView, getNamespace, getTView, increaseElementDepthCount, isCurrentTNodeParent, isInSkipHydrationBlock, isSkipHydrationRootTNode, lastNodeWasCreated, leaveSkipHydrationBlock, setCurrentTNode, setCurrentTNodeAsNotParent, wasLastNodeCreated, } from '../state';
import { computeStaticStyling } from '../styling/static_styling';
import { getConstant } from '../util/view_utils';
import { validateElementIsKnown } from './element_validation';
import { setDirectiveInputsWhichShadowsStyling } from './property';
import { createDirectivesInstances, executeContentQueries, getOrCreateTNode, resolveDirectives, saveResolvedLocalsInData, } from './shared';
function elementStartFirstCreatePass(index, tView, lView, name, attrsIndex, localRefsIndex) {
    ngDevMode && assertFirstCreatePass(tView);
    ngDevMode && ngDevMode.firstCreatePass++;
    const tViewConsts = tView.consts;
    const attrs = getConstant(tViewConsts, attrsIndex);
    const tNode = getOrCreateTNode(tView, index, 2 /* TNodeType.Element */, name, attrs);
    resolveDirectives(tView, lView, tNode, getConstant(tViewConsts, localRefsIndex));
    if (tNode.attrs !== null) {
        computeStaticStyling(tNode, tNode.attrs, false);
    }
    if (tNode.mergedAttrs !== null) {
        computeStaticStyling(tNode, tNode.mergedAttrs, true);
    }
    if (tView.queries !== null) {
        tView.queries.elementStart(tView, tNode);
    }
    return tNode;
}
/**
 * Create DOM element. The instruction must later be followed by `elementEnd()` call.
 *
 * @param index Index of the element in the LView array
 * @param name Name of the DOM Node
 * @param attrsIndex Index of the element's attributes in the `consts` array.
 * @param localRefsIndex Index of the element's local references in the `consts` array.
 * @returns This function returns itself so that it may be chained.
 *
 * Attributes and localRefs are passed as an array of strings where elements with an even index
 * hold an attribute name and elements with an odd index hold an attribute value, ex.:
 * ['id', 'warning5', 'class', 'alert']
 *
 * @codeGenApi
 */
export function ɵɵelementStart(index, name, attrsIndex, localRefsIndex) {
    const lView = getLView();
    const tView = getTView();
    const adjustedIndex = HEADER_OFFSET + index;
    ngDevMode &&
        assertEqual(getBindingIndex(), tView.bindingStartIndex, 'elements should be created before any bindings');
    ngDevMode && assertIndexInRange(lView, adjustedIndex);
    const renderer = lView[RENDERER];
    const tNode = tView.firstCreatePass
        ? elementStartFirstCreatePass(adjustedIndex, tView, lView, name, attrsIndex, localRefsIndex)
        : tView.data[adjustedIndex];
    const native = _locateOrCreateElementNode(tView, lView, tNode, renderer, name, index);
    lView[adjustedIndex] = native;
    const hasDirectives = isDirectiveHost(tNode);
    if (ngDevMode && tView.firstCreatePass) {
        validateElementIsKnown(native, lView, tNode.value, tView.schemas, hasDirectives);
    }
    setCurrentTNode(tNode, true);
    setupStaticAttributes(renderer, native, tNode);
    if (!isDetachedByI18n(tNode) && wasLastNodeCreated()) {
        // In the i18n case, the translation may have removed this element, so only add it if it is not
        // detached. See `TNodeType.Placeholder` and `LFrame.inI18n` for more context.
        appendChild(tView, lView, native, tNode);
    }
    // any immediate children of a component or template container must be pre-emptively
    // monkey-patched with the component view data so that the element can be inspected
    // later on using any element discovery utility methods (see `element_discovery.ts`)
    if (getElementDepthCount() === 0) {
        attachPatchData(native, lView);
    }
    increaseElementDepthCount();
    if (hasDirectives) {
        createDirectivesInstances(tView, lView, tNode);
        executeContentQueries(tView, tNode, lView);
    }
    if (localRefsIndex !== null) {
        saveResolvedLocalsInData(lView, tNode);
    }
    return ɵɵelementStart;
}
/**
 * Mark the end of the element.
 * @returns This function returns itself so that it may be chained.
 *
 * @codeGenApi
 */
export function ɵɵelementEnd() {
    let currentTNode = getCurrentTNode();
    ngDevMode && assertDefined(currentTNode, 'No parent node to close.');
    if (isCurrentTNodeParent()) {
        setCurrentTNodeAsNotParent();
    }
    else {
        ngDevMode && assertHasParent(getCurrentTNode());
        currentTNode = currentTNode.parent;
        setCurrentTNode(currentTNode, false);
    }
    const tNode = currentTNode;
    ngDevMode && assertTNodeType(tNode, 3 /* TNodeType.AnyRNode */);
    if (isSkipHydrationRootTNode(tNode)) {
        leaveSkipHydrationBlock();
    }
    decreaseElementDepthCount();
    const tView = getTView();
    if (tView.firstCreatePass) {
        registerPostOrderHooks(tView, currentTNode);
        if (isContentQueryHost(currentTNode)) {
            tView.queries.elementEnd(currentTNode);
        }
    }
    if (tNode.classesWithoutHost != null && hasClassInput(tNode)) {
        setDirectiveInputsWhichShadowsStyling(tView, tNode, getLView(), tNode.classesWithoutHost, true);
    }
    if (tNode.stylesWithoutHost != null && hasStyleInput(tNode)) {
        setDirectiveInputsWhichShadowsStyling(tView, tNode, getLView(), tNode.stylesWithoutHost, false);
    }
    return ɵɵelementEnd;
}
/**
 * Creates an empty element using {@link elementStart} and {@link elementEnd}
 *
 * @param index Index of the element in the data array
 * @param name Name of the DOM Node
 * @param attrsIndex Index of the element's attributes in the `consts` array.
 * @param localRefsIndex Index of the element's local references in the `consts` array.
 * @returns This function returns itself so that it may be chained.
 *
 * @codeGenApi
 */
export function ɵɵelement(index, name, attrsIndex, localRefsIndex) {
    ɵɵelementStart(index, name, attrsIndex, localRefsIndex);
    ɵɵelementEnd();
    return ɵɵelement;
}
let _locateOrCreateElementNode = (tView, lView, tNode, renderer, name, index) => {
    lastNodeWasCreated(true);
    return createElementNode(renderer, name, getNamespace());
};
/**
 * Enables hydration code path (to lookup existing elements in DOM)
 * in addition to the regular creation mode of element nodes.
 */
function locateOrCreateElementNodeImpl(tView, lView, tNode, renderer, name, index) {
    const hydrationInfo = lView[HYDRATION];
    const isNodeCreationMode = !hydrationInfo ||
        isInSkipHydrationBlock() ||
        isDetachedByI18n(tNode) ||
        isDisconnectedNode(hydrationInfo, index);
    lastNodeWasCreated(isNodeCreationMode);
    // Regular creation mode.
    if (isNodeCreationMode) {
        return createElementNode(renderer, name, getNamespace());
    }
    // Hydration mode, looking up an existing element in DOM.
    const native = locateNextRNode(hydrationInfo, tView, lView, tNode);
    ngDevMode && validateMatchingNode(native, Node.ELEMENT_NODE, name, lView, tNode);
    ngDevMode && markRNodeAsClaimedByHydration(native);
    // This element might also be an anchor of a view container.
    if (getSerializedContainerViews(hydrationInfo, index)) {
        // Important note: this element acts as an anchor, but it's **not** a part
        // of the embedded view, so we start the segment **after** this element, taking
        // a reference to the next sibling. For example, the following template:
        // `<div #vcrTarget>` is represented in the DOM as `<div></div>...<!--container-->`,
        // so while processing a `<div>` instruction, point to the next sibling as a
        // start of a segment.
        ngDevMode && validateNodeExists(native.nextSibling, lView, tNode);
        setSegmentHead(hydrationInfo, index, native.nextSibling);
    }
    // Checks if the skip hydration attribute is present during hydration so we know to
    // skip attempting to hydrate this block. We check both TNode and RElement for an
    // attribute: the RElement case is needed for i18n cases, when we add it to host
    // elements during the annotation phase (after all internal data structures are setup).
    if (hydrationInfo &&
        (hasSkipHydrationAttrOnTNode(tNode) || hasSkipHydrationAttrOnRElement(native))) {
        if (isComponentHost(tNode)) {
            enterSkipHydrationBlock(tNode);
            // Since this isn't hydratable, we need to empty the node
            // so there's no duplicate content after render
            clearElementContents(native);
            ngDevMode && markRNodeAsSkippedByHydration(native);
        }
        else if (ngDevMode) {
            // If this is not a component host, throw an error.
            // Hydration can be skipped on per-component basis only.
            throw invalidSkipHydrationHost(native);
        }
    }
    return native;
}
export function enableLocateOrCreateElementNodeImpl() {
    _locateOrCreateElementNode = locateOrCreateElementNodeImpl;
}
//# sourceMappingURL=data:application/json;base64,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