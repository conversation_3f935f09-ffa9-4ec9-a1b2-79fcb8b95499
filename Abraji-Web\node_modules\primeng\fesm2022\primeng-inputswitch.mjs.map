{"version": 3, "file": "primeng-inputswitch.mjs", "sources": ["../../src/app/components/inputswitch/inputswitch.ts", "../../src/app/components/inputswitch/primeng-inputswitch.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, NgModule, Output, ViewChild, ViewEncapsulation, booleanAttribute, forwardRef, numberAttribute } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { InputSwitchChangeEvent } from './inputswitch.interface';\n\nexport const INPUTSWITCH_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputSwitch),\n    multi: true\n};\n/**\n * InputSwitch is used to select a boolean value.\n * @group Components\n */\n@Component({\n    selector: 'p-inputSwitch',\n    template: `\n        <div\n            [ngClass]=\"{ 'p-inputswitch p-component': true, 'p-inputswitch-checked': checked(), 'p-disabled': disabled, 'p-focus': focused }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onClick($event)\"\n            [attr.data-pc-name]=\"'inputswitch'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"checkbox\"\n                    role=\"switch\"\n                    [checked]=\"checked()\"\n                    [disabled]=\"disabled\"\n                    [attr.aria-checked]=\"checked()\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.name]=\"name\"\n                    [attr.tabindex]=\"tabindex\"\n                    (focus)=\"onFocus()\"\n                    (blur)=\"onBlur()\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                />\n            </div>\n            <span class=\"p-inputswitch-slider\" [attr.data-pc-section]=\"'slider'\"></span>\n        </div>\n    `,\n    providers: [INPUTSWITCH_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./inputswitch.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class InputSwitch {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n    /**\n     * Identifier of the input element.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean | undefined;\n    /**\n     * Value in checked state.\n     * @group Props\n     */\n    @Input() trueValue: any = true;\n    /**\n     * Value in unchecked state.\n     * @group Props\n     */\n    @Input() falseValue: any = false;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Callback to invoke when the on value change.\n     * @param {InputSwitchChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<InputSwitchChangeEvent> = new EventEmitter<InputSwitchChangeEvent>();\n\n    @ViewChild('input') input!: ElementRef;\n\n    modelValue: any = false;\n\n    focused: boolean = false;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    constructor(private cd: ChangeDetectorRef) {}\n\n    onClick(event: Event) {\n        if (!this.disabled && !this.readonly) {\n            this.modelValue = this.checked() ? this.falseValue : this.trueValue;\n\n            this.onModelChange(this.modelValue);\n            this.onChange.emit({\n                originalEvent: event,\n                checked: this.modelValue\n            });\n\n            this.input.nativeElement.focus();\n        }\n    }\n\n    onFocus() {\n        this.focused = true;\n    }\n\n    onBlur() {\n        this.focused = false;\n        this.onModelTouched();\n    }\n\n    writeValue(value: any): void {\n        this.modelValue = value;\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    checked() {\n        return this.modelValue === this.trueValue;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, AutoFocusModule],\n    exports: [InputSwitch],\n    declarations: [InputSwitch]\n})\nexport class InputSwitchModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;AAMa,MAAA,0BAA0B,GAAQ;AAC3C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,WAAW,CAAC;AAC1C,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MA2CU,WAAW,CAAA;AA8EA,IAAA,EAAA,CAAA;AA7EpB;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACM,SAAS,GAAQ,IAAI,CAAC;AAC/B;;;AAGG;IACM,UAAU,GAAQ,KAAK,CAAC;AACjC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;AACO,IAAA,QAAQ,GAAyC,IAAI,YAAY,EAA0B,CAAC;AAElF,IAAA,KAAK,CAAc;IAEvC,UAAU,GAAQ,KAAK,CAAC;IAExB,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,WAAA,CAAoB,EAAqB,EAAA;QAArB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;AAE7C,IAAA,OAAO,CAAC,KAAY,EAAA;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;AAEpE,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,gBAAA,aAAa,EAAE,KAAK;gBACpB,OAAO,EAAE,IAAI,CAAC,UAAU;AAC3B,aAAA,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACpC,SAAA;KACJ;IAED,OAAO,GAAA;AACH,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,OAAO,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,SAAS,CAAC;KAC7C;uGA3HQ,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAeA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,CAef,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAyBhB,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CApEzB,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,0BAA0B,CAAC,EAhC7B,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,oRAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FASQ,WAAW,EAAA,UAAA,EAAA,CAAA;kBA1CvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+BT,EACU,SAAA,EAAA,CAAC,0BAA0B,CAAC,EACtB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,oRAAA,CAAA,EAAA,CAAA;sFAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEa,KAAK,EAAA,CAAA;sBAAxB,SAAS;uBAAC,OAAO,CAAA;;MA+DT,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,iBAnIjB,WAAW,CAAA,EAAA,OAAA,EAAA,CA+HV,YAAY,EAAE,eAAe,aA/H9B,WAAW,CAAA,EAAA,CAAA,CAAA;wGAmIX,iBAAiB,EAAA,OAAA,EAAA,CAJhB,YAAY,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA;;2FAI9B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC;oBACxC,OAAO,EAAE,CAAC,WAAW,CAAC;oBACtB,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;AC3LD;;AAEG;;;;"}