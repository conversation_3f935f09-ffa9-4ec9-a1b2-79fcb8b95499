/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { SimpleChange } from '../../interface/simple_change';
import { assertString } from '../../util/assert';
import { EMPTY_OBJ } from '../../util/empty';
import { applyValueToInputField } from '../apply_value_input_field';
/**
 * The NgOnChangesFeature decorates a component with support for the ngOnChanges
 * lifecycle hook, so it should be included in any component that implements
 * that hook.
 *
 * If the component or directive uses inheritance, the NgOnChangesFeature MUST
 * be included as a feature AFTER {@link InheritDefinitionFeature}, otherwise
 * inherited properties will not be propagated to the ngOnChanges lifecycle
 * hook.
 *
 * Example usage:
 *
 * ```
 * static ɵcmp = defineComponent({
 *   ...
 *   inputs: {name: 'publicName'},
 *   features: [NgOnChangesFeature]
 * });
 * ```
 *
 * @codeGenApi
 */
export function ɵɵNgOnChangesFeature() {
    return NgOnChangesFeatureImpl;
}
export function NgOnChangesFeatureImpl(definition) {
    if (definition.type.prototype.ngOnChanges) {
        definition.setInput = ngOnChangesSetInput;
    }
    return rememberChangeHistoryAndInvokeOnChangesHook;
}
// This option ensures that the ngOnChanges lifecycle hook will be inherited
// from superclasses (in InheritDefinitionFeature).
/** @nocollapse */
// tslint:disable-next-line:no-toplevel-property-access
ɵɵNgOnChangesFeature.ngInherit = true;
/**
 * This is a synthetic lifecycle hook which gets inserted into `TView.preOrderHooks` to simulate
 * `ngOnChanges`.
 *
 * The hook reads the `NgSimpleChangesStore` data from the component instance and if changes are
 * found it invokes `ngOnChanges` on the component instance.
 *
 * @param this Component instance. Because this function gets inserted into `TView.preOrderHooks`,
 *     it is guaranteed to be called with component instance.
 */
function rememberChangeHistoryAndInvokeOnChangesHook() {
    const simpleChangesStore = getSimpleChangesStore(this);
    const current = simpleChangesStore?.current;
    if (current) {
        const previous = simpleChangesStore.previous;
        if (previous === EMPTY_OBJ) {
            simpleChangesStore.previous = current;
        }
        else {
            // New changes are copied to the previous store, so that we don't lose history for inputs
            // which were not changed this time
            for (let key in current) {
                previous[key] = current[key];
            }
        }
        simpleChangesStore.current = null;
        this.ngOnChanges(current);
    }
}
function ngOnChangesSetInput(instance, inputSignalNode, value, publicName, privateName) {
    const declaredName = this.declaredInputs[publicName];
    ngDevMode && assertString(declaredName, 'Name of input in ngOnChanges has to be a string');
    const simpleChangesStore = getSimpleChangesStore(instance) ||
        setSimpleChangesStore(instance, { previous: EMPTY_OBJ, current: null });
    const current = simpleChangesStore.current || (simpleChangesStore.current = {});
    const previous = simpleChangesStore.previous;
    const previousChange = previous[declaredName];
    current[declaredName] = new SimpleChange(previousChange && previousChange.currentValue, value, previous === EMPTY_OBJ);
    applyValueToInputField(instance, inputSignalNode, privateName, value);
}
const SIMPLE_CHANGES_STORE = '__ngSimpleChanges__';
function getSimpleChangesStore(instance) {
    return instance[SIMPLE_CHANGES_STORE] || null;
}
function setSimpleChangesStore(instance, store) {
    return (instance[SIMPLE_CHANGES_STORE] = store);
}
//# sourceMappingURL=data:application/json;base64,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