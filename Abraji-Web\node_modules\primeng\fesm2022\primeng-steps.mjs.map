{"version": 3, "file": "primeng-steps.mjs", "sources": ["../../src/app/components/steps/steps.ts", "../../src/app/components/steps/primeng-steps.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, NgModule, OnDestroy, OnInit, Output, ViewChild, ViewEncapsulation, booleanAttribute, numberAttribute } from '@angular/core';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { DomHandler } from 'primeng/dom';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { MenuItem } from 'primeng/api';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { Subscription } from 'rxjs';\n/**\n * Steps components is an indicator for the steps in a wizard workflow.\n * @group Components\n */\n@Component({\n    selector: 'p-steps',\n    template: `\n        <nav [ngClass]=\"{ 'p-steps p-component': true, 'p-readonly': readonly }\" [ngStyle]=\"style\" [class]=\"styleClass\" [attr.data-pc-name]=\"'steps'\">\n            <ul #list [attr.data-pc-section]=\"'menu'\">\n                <li\n                    *ngFor=\"let item of model; let i = index\"\n                    class=\"p-steps-item\"\n                    #menuitem\n                    [ngStyle]=\"item.style\"\n                    [class]=\"item.styleClass\"\n                    [attr.aria-current]=\"isActive(item, i) ? 'step' : undefined\"\n                    [attr.id]=\"item.id\"\n                    pTooltip\n                    [tooltipOptions]=\"item.tooltipOptions\"\n                    [ngClass]=\"{ 'p-highlight p-steps-current': isActive(item, i), 'p-disabled': item.disabled || (readonly && !isActive(item, i)) }\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                >\n                    <a\n                        role=\"link\"\n                        *ngIf=\"isClickableRouterLink(item); else elseBlock\"\n                        [routerLink]=\"item.routerLink\"\n                        [queryParams]=\"item.queryParams\"\n                        [routerLinkActive]=\"'p-menuitem-link-active'\"\n                        [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                        class=\"p-menuitem-link\"\n                        (click)=\"onItemClick($event, item, i)\"\n                        (keydown)=\"onItemKeydown($event, item, i)\"\n                        [target]=\"item.target\"\n                        [attr.tabindex]=\"getItemTabIndex(item, i)\"\n                        [attr.aria-expanded]=\"i === activeIndex\"\n                        [attr.aria-disabled]=\"item.disabled || (readonly && i !== activeIndex)\"\n                        [fragment]=\"item.fragment\"\n                        [queryParamsHandling]=\"item.queryParamsHandling\"\n                        [preserveFragment]=\"item.preserveFragment\"\n                        [skipLocationChange]=\"item.skipLocationChange\"\n                        [replaceUrl]=\"item.replaceUrl\"\n                        [state]=\"item.state\"\n                        [attr.ariaCurrentWhenActive]=\"exact ? 'step' : undefined\"\n                    >\n                        <span class=\"p-steps-number\">{{ i + 1 }}</span>\n                        <span class=\"p-steps-title\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ item.label }}</span>\n                        <ng-template #htmlLabel><span class=\"p-steps-title\" [innerHTML]=\"item.label\"></span></ng-template>\n                    </a>\n                    <ng-template #elseBlock>\n                        <a\n                            role=\"link\"\n                            [attr.href]=\"item.url\"\n                            class=\"p-menuitem-link\"\n                            (click)=\"onItemClick($event, item, i)\"\n                            (keydown)=\"onItemKeydown($event, item, i)\"\n                            [target]=\"item.target\"\n                            [attr.tabindex]=\"getItemTabIndex(item, i)\"\n                            [attr.aria-expanded]=\"i === activeIndex\"\n                            [attr.aria-disabled]=\"item.disabled || (readonly && i !== activeIndex)\"\n                            [attr.ariaCurrentWhenActive]=\"exact && (!item.disabled || readonly) ? 'step' : undefined\"\n                        >\n                            <span class=\"p-steps-number\">{{ i + 1 }}</span>\n                            <span class=\"p-steps-title\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ item.label }}</span>\n                            <ng-template #htmlRouteLabel><span class=\"p-steps-title\" [innerHTML]=\"item.label\"></span></ng-template>\n                        </a>\n                    </ng-template>\n                </li>\n            </ul>\n        </nav>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./steps.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Steps implements OnInit, OnDestroy {\n    /**\n     * Index of the active item.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) activeIndex: number = 0;\n    /**\n     * An array of menu items.\n     * @group Props\n     */\n    @Input() model: MenuItem[] | undefined;\n    /**\n     * Whether the items are clickable or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean = true;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Whether to apply 'router-link-active-exact' class if route exactly matches the item path.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) exact: boolean = true;\n    /**\n     * Callback to invoke when the new step is selected.\n     * @param {number} number - current index.\n     * @group Emits\n     */\n    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter<number>();\n\n    @ViewChild('list', { static: false }) listViewChild: Nullable<ElementRef>;\n\n    constructor(private router: Router, private route: ActivatedRoute, private cd: ChangeDetectorRef) {}\n\n    subscription: Subscription | undefined;\n\n    ngOnInit() {\n        this.subscription = this.router.events.subscribe(() => this.cd.markForCheck());\n    }\n\n    onItemClick(event: Event, item: MenuItem, i: number) {\n        if (this.readonly || item.disabled) {\n            event.preventDefault();\n            return;\n        }\n\n        this.activeIndexChange.emit(i);\n\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item,\n                index: i\n            });\n        }\n    }\n\n    onItemKeydown(event: KeyboardEvent, item: MenuItem, i: number) {\n        switch (event.code) {\n            case 'ArrowRight': {\n                this.navigateToNextItem(event.target);\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowLeft': {\n                this.navigateToPrevItem(event.target);\n                event.preventDefault();\n                break;\n            }\n\n            case 'Home': {\n                this.navigateToFirstItem(event.target);\n                event.preventDefault();\n                break;\n            }\n\n            case 'End': {\n                this.navigateToLastItem(event.target);\n                event.preventDefault();\n                break;\n            }\n\n            case 'Tab':\n                if (i !== this.activeIndex) {\n                    const siblings = DomHandler.find(this.listViewChild.nativeElement, '[data-pc-section=\"menuitem\"]');\n                    siblings[i].children[0].tabIndex = '-1';\n                    siblings[this.activeIndex].children[0].tabIndex = '0';\n                }\n                break;\n\n            case 'Enter':\n            case 'Space': {\n                this.onItemClick(event, item, i);\n                event.preventDefault();\n                break;\n            }\n\n            default:\n                break;\n        }\n    }\n\n    navigateToNextItem(target) {\n        const nextItem = this.findNextItem(target);\n\n        nextItem && this.setFocusToMenuitem(target, nextItem);\n    }\n    navigateToPrevItem(target) {\n        const prevItem = this.findPrevItem(target);\n\n        prevItem && this.setFocusToMenuitem(target, prevItem);\n    }\n    navigateToFirstItem(target) {\n        const firstItem = this.findFirstItem();\n\n        firstItem && this.setFocusToMenuitem(target, firstItem);\n    }\n    navigateToLastItem(target) {\n        const lastItem = this.findLastItem();\n\n        lastItem && this.setFocusToMenuitem(target, lastItem);\n    }\n    findNextItem(item) {\n        const nextItem = item.parentElement.nextElementSibling;\n\n        return nextItem ? nextItem.children[0] : null;\n    }\n    findPrevItem(item) {\n        const prevItem = item.parentElement.previousElementSibling;\n\n        return prevItem ? prevItem.children[0] : null;\n    }\n    findFirstItem() {\n        const firstSibling = DomHandler.findSingle(this.listViewChild.nativeElement, '[data-pc-section=\"menuitem\"]');\n\n        return firstSibling ? firstSibling.children[0] : null;\n    }\n    findLastItem() {\n        const siblings = DomHandler.find(this.listViewChild.nativeElement, '[data-pc-section=\"menuitem\"]');\n\n        return siblings ? siblings[siblings.length - 1].children[0] : null;\n    }\n    setFocusToMenuitem(target, focusableItem) {\n        target.tabIndex = '-1';\n        focusableItem.tabIndex = '0';\n        focusableItem.focus();\n    }\n\n    isClickableRouterLink(item: MenuItem) {\n        return item.routerLink && !this.readonly && !item.disabled;\n    }\n\n    isActive(item: MenuItem, index: number) {\n        if (item.routerLink) {\n            let routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n\n            return this.router.isActive(this.router.createUrlTree(routerLink, { relativeTo: this.route }).toString(), false);\n        }\n\n        return index === this.activeIndex;\n    }\n\n    getItemTabIndex(item: MenuItem, index: number): string {\n        if (item.disabled) {\n            return '-1';\n        }\n\n        if (!item.disabled && this.activeIndex === index) {\n            return item.tabindex || '0';\n        }\n\n        return item.tabindex ?? '-1';\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, TooltipModule],\n    exports: [Steps, RouterModule, TooltipModule],\n    declarations: [Steps]\n})\nexport class StepsModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;AAQA;;;AAGG;MA0EU,KAAK,CAAA;AAwCM,IAAA,MAAA,CAAA;AAAwB,IAAA,KAAA,CAAA;AAA+B,IAAA,EAAA,CAAA;AAvC3E;;;AAGG;IACoC,WAAW,GAAW,CAAC,CAAC;AAC/D;;;AAGG;AACM,IAAA,KAAK,CAAyB;AACvC;;;AAGG;IACqC,QAAQ,GAAY,IAAI,CAAC;AACjE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACqC,KAAK,GAAY,IAAI,CAAC;AAC9D;;;;AAIG;AACO,IAAA,iBAAiB,GAAyB,IAAI,YAAY,EAAU,CAAC;AAEzC,IAAA,aAAa,CAAuB;AAE1E,IAAA,WAAA,CAAoB,MAAc,EAAU,KAAqB,EAAU,EAAqB,EAAA;QAA5E,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QAAU,IAAK,CAAA,KAAA,GAAL,KAAK,CAAgB;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;AAEpG,IAAA,YAAY,CAA2B;IAEvC,QAAQ,GAAA;QACJ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC;KAClF;AAED,IAAA,WAAW,CAAC,KAAY,EAAE,IAAc,EAAE,CAAS,EAAA;AAC/C,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC/B,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC;AACT,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,IAAI,EAAE,IAAI;AACV,gBAAA,KAAK,EAAE,CAAC;AACX,aAAA,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,KAAoB,EAAE,IAAc,EAAE,CAAS,EAAA;QACzD,QAAQ,KAAK,CAAC,IAAI;YACd,KAAK,YAAY,EAAE;AACf,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACtC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,WAAW,EAAE;AACd,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACtC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,MAAM,EAAE;AACT,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACvC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,KAAK,EAAE;AACR,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACtC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;AAED,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,EAAE;AACxB,oBAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;AACnG,oBAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AACxC,oBAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;AACzD,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;YACb,KAAK,OAAO,EAAE;gBACV,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBACjC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;AAED,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,kBAAkB,CAAC,MAAM,EAAA;QACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE3C,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACzD;AACD,IAAA,kBAAkB,CAAC,MAAM,EAAA;QACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE3C,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACzD;AACD,IAAA,mBAAmB,CAAC,MAAM,EAAA;AACtB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAEvC,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;KAC3D;AACD,IAAA,kBAAkB,CAAC,MAAM,EAAA;AACrB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAErC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KACzD;AACD,IAAA,YAAY,CAAC,IAAI,EAAA;AACb,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;AAEvD,QAAA,OAAO,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACjD;AACD,IAAA,YAAY,CAAC,IAAI,EAAA;AACb,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;AAE3D,QAAA,OAAO,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACjD;IACD,aAAa,GAAA;AACT,QAAA,MAAM,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;AAE7G,QAAA,OAAO,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACzD;IACD,YAAY,GAAA;AACR,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;QAEnG,OAAO,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACtE;IACD,kBAAkB,CAAC,MAAM,EAAE,aAAa,EAAA;AACpC,QAAA,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;AACvB,QAAA,aAAa,CAAC,QAAQ,GAAG,GAAG,CAAC;QAC7B,aAAa,CAAC,KAAK,EAAE,CAAC;KACzB;AAED,IAAA,qBAAqB,CAAC,IAAc,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC9D;IAED,QAAQ,CAAC,IAAc,EAAE,KAAa,EAAA;QAClC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAEtF,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;AACpH,SAAA;AAED,QAAA,OAAO,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC;KACrC;IAED,eAAe,CAAC,IAAc,EAAE,KAAa,EAAA;QACzC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;AAC9C,YAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAC;AAC/B,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;KAChC;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGA/LQ,KAAK,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAL,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,KAAK,6EAKM,eAAe,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAUf,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAehB,gBAAgB,CArG1B,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,2jBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,KAAK,EAAA,UAAA,EAAA,CAAA;kBAzEjB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,SAAS,EACT,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,2jBAAA,CAAA,EAAA,CAAA;wIAOsC,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKkC,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAE+B,aAAa,EAAA,CAAA;sBAAlD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,MAAM,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;;MAiK3B,WAAW,CAAA;uGAAX,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,EAvMX,YAAA,EAAA,CAAA,KAAK,CAmMJ,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,aAAa,CAnM1C,EAAA,OAAA,EAAA,CAAA,KAAK,EAoMG,YAAY,EAAE,aAAa,CAAA,EAAA,CAAA,CAAA;wGAGnC,WAAW,EAAA,OAAA,EAAA,CAJV,YAAY,EAAE,YAAY,EAAE,aAAa,EAClC,YAAY,EAAE,aAAa,CAAA,EAAA,CAAA,CAAA;;2FAGnC,WAAW,EAAA,UAAA,EAAA,CAAA;kBALvB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;AACpD,oBAAA,OAAO,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,CAAC;oBAC7C,YAAY,EAAE,CAAC,KAAK,CAAC;AACxB,iBAAA,CAAA;;;AC3RD;;AAEG;;;;"}