import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DebtsRoutingModule } from './debts-routing.module';
import { AllDebtsComponent } from './all-debts/all-debts.component';
import { SharedModule } from '../shared/shared.module';
import { TranslocoModule } from '@jsverse/transloco';
import { TableSkeletonComponent } from "../shared/skeletons/table/table.component";
import { ReactiveFormsModule } from '@angular/forms';
import { UserDebtsComponent } from './user-debts/user-debts.component';
import { SmallCardSkeletonComponent } from "../shared/skeletons/small-card-skeleton/small-card-skeleton.component";
import { DebtHistoryComponent } from './debt-history/debt-history.component';


@NgModule({
  declarations: [
    AllDebtsComponent,
    UserDebtsComponent,
    DebtHistoryComponent,
  ],
  imports: [
    CommonModule,
    DebtsRoutingModule,
    SharedModule,
    TranslocoModule,
    TableSkeletonComponent,
    ReactiveFormsModule,
    SmallCardSkeletonComponent
]
})
export class DebtsModule { }
