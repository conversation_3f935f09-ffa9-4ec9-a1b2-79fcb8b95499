<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="LaravelPint">
    <laravel_pint_settings>
      <LaravelPintConfiguration tool_path="$PROJECT_DIR$/Abraji-APIs/vendor/bin/pint" />
    </laravel_pint_settings>
  </component>
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psr/container" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psr/log" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/cviebrock/eloquent-sluggable" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/spatie/laravel-permission" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/wikimedia/composer-merge-plugin" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/nwidart/laravel-modules" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/composer" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/mcamara/laravel-localization" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/league/config" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/cocur/slugify" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/brick/math" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/Abraji-APIs/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psr/log" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psr/container" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/brick/math" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/cocur/slugify" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/league/config" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/spatie/laravel-permission" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/mcamara/laravel-localization" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/nwidart/laravel-modules" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/composer" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/cviebrock/eloquent-sluggable" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/wikimedia/composer-merge-plugin" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/AbrajiAPIs/vendor/graham-campbell/result-type" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.2" />
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings configuration_file_path="$PROJECT_DIR$/AbrajiAPIs/phpunit.xml" custom_loader_path="$PROJECT_DIR$/AbrajiAPIs/vendor/autoload.php" use_configuration_file="true" />
    </phpunit_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>