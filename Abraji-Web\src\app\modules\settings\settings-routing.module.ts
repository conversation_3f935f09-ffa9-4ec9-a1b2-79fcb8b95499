import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainSettingsComponent } from './main-settings/main-settings.component';
import { TowersComponent } from './towers/towers.component';
import { PrinterSettingsComponent } from './printer-settings/printer-settings.component';

//Main After Port
const routes: Routes = [
  {
    path: '',
    component: MainSettingsComponent,
  },
  {
    path: 'tower',
    component: TowersComponent,
  },
  {
    path: 'printer',
    component: PrinterSettingsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SettingsRoutingModule {}
