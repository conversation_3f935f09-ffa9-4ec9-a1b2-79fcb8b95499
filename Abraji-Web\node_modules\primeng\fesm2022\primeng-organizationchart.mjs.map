{"version": 3, "file": "primeng-organizationchart.mjs", "sources": ["../../src/app/components/organizationchart/organizationchart.ts", "../../src/app/components/organizationchart/primeng-organizationchart.ts"], "sourcesContent": ["import { animate, state, style, transition, trigger } from '@angular/animations';\nimport { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    QueryList,\n    TemplateRef,\n    ViewEncapsulation,\n    booleanAttribute,\n    forwardRef\n} from '@angular/core';\nimport { PrimeTemplate, SharedModule, TreeNode } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronUpIcon } from 'primeng/icons/chevronup';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { Subject, Subscription } from 'rxjs';\nimport { OrganizationChartNodeCollapseEvent, OrganizationChartNodeExpandEvent, OrganizationChartNodeSelectEvent, OrganizationChartNodeUnSelectEvent } from './organizationchart.interface';\n@Component({\n    selector: '[pOrganizationChartNode]',\n    template: `\n        <tbody *ngIf=\"node\" [attr.data-pc-section]=\"'body'\">\n            <tr [attr.data-pc-section]=\"'row'\">\n                <td [attr.colspan]=\"colspan\" [attr.data-pc-section]=\"'cell'\">\n                    <div\n                        [class]=\"node.styleClass\"\n                        [ngClass]=\"{ 'p-organizationchart-node-content': true, 'p-organizationchart-selectable-node': chart.selectionMode && node.selectable !== false, 'p-highlight': isSelected() }\"\n                        (click)=\"onNodeClick($event, node)\"\n                        [attr.data-pc-section]=\"'node'\"\n                    >\n                        <div *ngIf=\"!chart.getTemplateForNode(node)\">{{ node.label }}</div>\n                        <div *ngIf=\"chart.getTemplateForNode(node)\">\n                            <ng-container *ngTemplateOutlet=\"chart.getTemplateForNode(node); context: { $implicit: node }\"></ng-container>\n                        </div>\n                        <ng-container *ngIf=\"collapsible\">\n                            <a *ngIf=\"!leaf\" tabindex=\"0\" class=\"p-node-toggler\" (click)=\"toggleNode($event, node)\" (keydown.enter)=\"toggleNode($event, node)\" (keydown.space)=\"toggleNode($event, node)\" [attr.data-pc-section]=\"'nodeToggler'\">\n                                <ng-container *ngIf=\"!chart.togglerIconTemplate\">\n                                    <ChevronDownIcon *ngIf=\"node.expanded\" [styleClass]=\"'p-node-toggler-icon'\" [attr.data-pc-section]=\"'nodeTogglerIcon'\" />\n                                    <ChevronUpIcon *ngIf=\"!node.expanded\" [styleClass]=\"'p-node-toggler-icon'\" [attr.data-pc-section]=\"'nodeTogglerIcon'\" />\n                                </ng-container>\n                                <span class=\"p-node-toggler-icon\" *ngIf=\"chart.togglerIconTemplate\" [attr.data-pc-section]=\"'nodeTogglerIcon'\">\n                                    <ng-template *ngTemplateOutlet=\"chart.togglerIconTemplate; context: { $implicit: node.expanded }\"></ng-template>\n                                </span>\n                            </a>\n                        </ng-container>\n                    </div>\n                </td>\n            </tr>\n            <tr [ngClass]=\"!leaf && node.expanded ? 'p-organizationchart-node-visible' : 'p-organizationchart-node-hidden'\" class=\"p-organizationchart-lines\" [@childState]=\"'in'\" [attr.data-pc-section]=\"'lines'\">\n                <td [attr.data-pc-section]=\"'lineCell'\" [attr.colspan]=\"colspan\">\n                    <div [attr.data-pc-section]=\"'lineDown'\" class=\"p-organizationchart-line-down\"></div>\n                </td>\n            </tr>\n            <tr [ngClass]=\"!leaf && node.expanded ? 'p-organizationchart-node-visible' : 'p-organizationchart-node-hidden'\" class=\"p-organizationchart-lines\" [@childState]=\"'in'\" [attr.data-pc-section]=\"'lines'\">\n                <ng-container *ngIf=\"node.children && node.children.length === 1\">\n                    <td [attr.data-pc-section]=\"'lineCell'\" [attr.colspan]=\"colspan\">\n                        <div [attr.data-pc-section]=\"'lineDown'\" class=\"p-organizationchart-line-down\"></div>\n                    </td>\n                </ng-container>\n                <ng-container *ngIf=\"node.children && node.children.length > 1\">\n                    <ng-template ngFor let-child [ngForOf]=\"node.children\" let-first=\"first\" let-last=\"last\">\n                        <td [attr.data-pc-section]=\"'lineLeft'\" class=\"p-organizationchart-line-left\" [ngClass]=\"{ 'p-organizationchart-line-top': !first }\">&nbsp;</td>\n                        <td [attr.data-pc-section]=\"'lineRight'\" class=\"p-organizationchart-line-right\" [ngClass]=\"{ 'p-organizationchart-line-top': !last }\">&nbsp;</td>\n                    </ng-template>\n                </ng-container>\n            </tr>\n            <tr [ngClass]=\"!leaf && node.expanded ? 'p-organizationchart-node-visible' : 'p-organizationchart-node-hidden'\" class=\"p-organizationchart-nodes\" [@childState]=\"'in'\" [attr.data-pc-section]=\"'nodes'\">\n                <td *ngFor=\"let child of node.children\" colspan=\"2\" [attr.data-pc-section]=\"'nodeCell'\">\n                    <table class=\"p-organizationchart-table\" pOrganizationChartNode [node]=\"child\" [collapsible]=\"node.children && node.children.length > 0\"></table>\n                </td>\n            </tr>\n        </tbody>\n    `,\n    animations: [trigger('childState', [state('in', style({ opacity: 1 })), transition('void => *', [style({ opacity: 0 }), animate(150)]), transition('* => void', [animate(150, style({ opacity: 0 }))])])],\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.Default,\n    styleUrls: ['./organizationchart.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class OrganizationChartNode implements OnDestroy {\n    @Input() node: TreeNode<any> | undefined;\n\n    @Input({ transform: booleanAttribute }) root: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) first: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) last: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) collapsible: boolean | undefined;\n\n    chart: OrganizationChart;\n\n    subscription: Subscription;\n\n    constructor(@Inject(forwardRef(() => OrganizationChart)) chart: OrganizationChart, public cd: ChangeDetectorRef) {\n        this.chart = chart as OrganizationChart;\n        this.subscription = this.chart.selectionSource$.subscribe(() => {\n            this.cd.markForCheck();\n        });\n    }\n\n    get leaf(): boolean | undefined {\n        if (this.node) {\n            return this.node.leaf == false ? false : !(this.node.children && this.node.children.length);\n        }\n    }\n\n    get colspan() {\n        if (this.node) {\n            return this.node.children && this.node.children.length ? this.node.children.length * 2 : null;\n        }\n    }\n\n    onNodeClick(event: Event, node: TreeNode) {\n        this.chart.onNodeClick(event, node);\n    }\n\n    toggleNode(event: Event, node: TreeNode) {\n        node.expanded = !node.expanded;\n        if (node.expanded) this.chart.onNodeExpand.emit({ originalEvent: event, node: <TreeNode>this.node });\n        else this.chart.onNodeCollapse.emit({ originalEvent: event, node: <TreeNode>this.node });\n\n        event.preventDefault();\n    }\n\n    isSelected() {\n        return this.chart.isSelected(this.node as TreeNode);\n    }\n\n    ngOnDestroy() {\n        this.subscription.unsubscribe();\n    }\n}\n/**\n * OrganizationChart visualizes hierarchical organization data.\n * @group Components\n */\n@Component({\n    selector: 'p-organizationChart',\n    template: `\n        <div [ngStyle]=\"style\" [class]=\"styleClass\" [ngClass]=\"{ 'p-organizationchart p-component': true, 'p-organizationchart-preservespace': preserveSpace }\" [attr.data-pc-section]=\"'root'\">\n            <table class=\"p-organizationchart-table\" [collapsible]=\"collapsible\" pOrganizationChartNode [node]=\"root\" *ngIf=\"root\"></table>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.Default,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class OrganizationChart implements AfterContentInit {\n    /**\n     * An array of nested TreeNodes.\n     * @group Props\n     */\n    @Input() value: TreeNode[] | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Defines the selection mode.\n     * @group Props\n     */\n    @Input() selectionMode: 'single' | 'multiple' | null | undefined;\n    /**\n     * Whether the nodes can be expanded or toggled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) collapsible: boolean | undefined;\n    /**\n     * Whether the space allocated by a node is preserved when hidden.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) preserveSpace: boolean = true;\n    /**\n     * A single treenode instance or an array to refer to the selections.\n     * @group Props\n     */\n    @Input() get selection(): any {\n        return this._selection;\n    }\n    set selection(val: any) {\n        this._selection = val;\n\n        if (this.initialized) this.selectionSource.next(null);\n    }\n    /**\n     * Callback to invoke on selection change.\n     * @param {*} any - selected value.\n     * @group Emits\n     */\n    @Output() selectionChange: EventEmitter<any> = new EventEmitter();\n    /**\n     * Callback to invoke when a node is selected.\n     * @param {OrganizationChartNodeSelectEvent} event - custom node select event.\n     * @group Emits\n     */\n    @Output() onNodeSelect: EventEmitter<OrganizationChartNodeSelectEvent> = new EventEmitter<OrganizationChartNodeSelectEvent>();\n    /**\n     * Callback to invoke when a node is unselected.\n     * @param {OrganizationChartNodeUnSelectEvent} event - custom node unselect event.\n     * @group Emits\n     */\n    @Output() onNodeUnselect: EventEmitter<OrganizationChartNodeUnSelectEvent> = new EventEmitter<OrganizationChartNodeUnSelectEvent>();\n    /**\n     * Callback to invoke when a node is expanded.\n     * @param {OrganizationChartNodeExpandEvent} event - custom node expand event.\n     * @group Emits\n     */\n    @Output() onNodeExpand: EventEmitter<OrganizationChartNodeExpandEvent> = new EventEmitter<OrganizationChartNodeExpandEvent>();\n    /**\n     * Callback to invoke when a node is collapsed.\n     * @param {OrganizationChartNodeCollapseEvent} event - custom node collapse event.\n     * @group Emits\n     */\n    @Output() onNodeCollapse: EventEmitter<OrganizationChartNodeCollapseEvent> = new EventEmitter<OrganizationChartNodeCollapseEvent>();\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    public templateMap: any;\n\n    togglerIconTemplate: Nullable<TemplateRef<any>>;\n\n    private selectionSource = new Subject<any>();\n\n    _selection: any;\n\n    initialized: Nullable<boolean>;\n\n    selectionSource$ = this.selectionSource.asObservable();\n\n    constructor(public el: ElementRef, public cd: ChangeDetectorRef) {}\n\n    get root(): TreeNode<any> | null {\n        return this.value && this.value.length ? this.value[0] : null;\n    }\n\n    ngAfterContentInit() {\n        if ((this.templates as QueryList<PrimeTemplate>).length) {\n            this.templateMap = {};\n        }\n\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            if (item.getType() === 'togglericon') {\n                this.togglerIconTemplate = item.template;\n            } else {\n                this.templateMap[item.getType()] = item.template;\n            }\n        });\n\n        this.initialized = true;\n    }\n\n    getTemplateForNode(node: TreeNode): TemplateRef<any> | null {\n        if (this.templateMap) return node.type ? this.templateMap[node.type] : this.templateMap['default'];\n        else return null;\n    }\n\n    onNodeClick(event: Event, node: TreeNode) {\n        let eventTarget = <Element>event.target;\n\n        if (eventTarget.className && (DomHandler.hasClass(eventTarget, 'p-node-toggler') || DomHandler.hasClass(eventTarget, 'p-node-toggler-icon'))) {\n            return;\n        } else if (this.selectionMode) {\n            if (node.selectable === false) {\n                return;\n            }\n\n            let index = this.findIndexInSelection(node);\n            let selected = index >= 0;\n\n            if (this.selectionMode === 'single') {\n                if (selected) {\n                    this.selection = null;\n                    this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                } else {\n                    this.selection = node;\n                    this.onNodeSelect.emit({ originalEvent: event, node: node });\n                }\n            } else if (this.selectionMode === 'multiple') {\n                if (selected) {\n                    this.selection = this.selection.filter((val: any, i: number) => i != index);\n                    this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                } else {\n                    this.selection = [...(this.selection || []), node];\n                    this.onNodeSelect.emit({ originalEvent: event, node: node });\n                }\n            }\n\n            this.selectionChange.emit(this.selection);\n            this.selectionSource.next(null);\n        }\n    }\n\n    findIndexInSelection(node: TreeNode) {\n        let index: number = -1;\n\n        if (this.selectionMode && this.selection) {\n            if (this.selectionMode === 'single') {\n                index = this.selection == node ? 0 : -1;\n            } else if (this.selectionMode === 'multiple') {\n                for (let i = 0; i < this.selection.length; i++) {\n                    if (this.selection[i] == node) {\n                        index = i;\n                        break;\n                    }\n                }\n            }\n        }\n\n        return index;\n    }\n\n    isSelected(node: TreeNode) {\n        return this.findIndexInSelection(node) != -1;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, ChevronDownIcon, ChevronUpIcon, SharedModule],\n    exports: [OrganizationChart, SharedModule],\n    declarations: [OrganizationChart, OrganizationChartNode]\n})\nexport class OrganizationChartModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;MA2Fa,qBAAqB,CAAA;AAe4D,IAAA,EAAA,CAAA;AAdjF,IAAA,IAAI,CAA4B;AAED,IAAA,IAAI,CAAsB;AAE1B,IAAA,KAAK,CAAsB;AAE3B,IAAA,IAAI,CAAsB;AAE1B,IAAA,WAAW,CAAsB;AAEzE,IAAA,KAAK,CAAoB;AAEzB,IAAA,YAAY,CAAe;IAE3B,WAAyD,CAAA,KAAwB,EAAS,EAAqB,EAAA;QAArB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;AAC3G,QAAA,IAAI,CAAC,KAAK,GAAG,KAA0B,CAAC;AACxC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;AAC3D,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;AAED,IAAA,IAAI,IAAI,GAAA;QACJ,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC/F,SAAA;KACJ;AAED,IAAA,IAAI,OAAO,GAAA;QACP,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC;AACjG,SAAA;KACJ;IAED,WAAW,CAAC,KAAY,EAAE,IAAc,EAAA;QACpC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;KACvC;IAED,UAAU,CAAC,KAAY,EAAE,IAAc,EAAA;AACnC,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAY,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;AAChG,YAAA,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAY,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzF,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,UAAU,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAgB,CAAC,CAAC;KACvD;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;KACnC;AApDQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,kBAeV,UAAU,CAAC,MAAM,iBAAiB,CAAC,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAf9C,qBAAqB,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAGV,gBAAgB,CAEhB,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,gBAAgB,0BAEhB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAEhB,gBAAgB,CAtE1B,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,86BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA+PuB,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,aAAa,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAtP7C,qBAAqB,CARlB,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQhM,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBA/DjC,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,0BAA0B,EAC1B,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDT,IAAA,CAAA,EAAA,UAAA,EACW,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1L,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,OAAO,EAE1C,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,86BAAA,CAAA,EAAA,CAAA;;0BAiBY,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,iBAAiB,CAAC,CAAA;yEAd9C,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAEkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;;AA6C1C;;;AAGG;MAaU,iBAAiB,CAAA;AAwFP,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAvF1C;;;AAGG;AACM,IAAA,KAAK,CAAyB;AACvC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,aAAa,CAA2C;AACjE;;;AAGG;AACqC,IAAA,WAAW,CAAsB;AACzE;;;AAGG;IACqC,aAAa,GAAY,IAAI,CAAC;AACtE;;;AAGG;AACH,IAAA,IAAa,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;IACD,IAAI,SAAS,CAAC,GAAQ,EAAA;AAClB,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QAEtB,IAAI,IAAI,CAAC,WAAW;AAAE,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACzD;AACD;;;;AAIG;AACO,IAAA,eAAe,GAAsB,IAAI,YAAY,EAAE,CAAC;AAClE;;;;AAIG;AACO,IAAA,YAAY,GAAmD,IAAI,YAAY,EAAoC,CAAC;AAC9H;;;;AAIG;AACO,IAAA,cAAc,GAAqD,IAAI,YAAY,EAAsC,CAAC;AACpI;;;;AAIG;AACO,IAAA,YAAY,GAAmD,IAAI,YAAY,EAAoC,CAAC;AAC9H;;;;AAIG;AACO,IAAA,cAAc,GAAqD,IAAI,YAAY,EAAsC,CAAC;AAEpG,IAAA,SAAS,CAAqC;AAEvE,IAAA,WAAW,CAAM;AAExB,IAAA,mBAAmB,CAA6B;AAExC,IAAA,eAAe,GAAG,IAAI,OAAO,EAAO,CAAC;AAE7C,IAAA,UAAU,CAAM;AAEhB,IAAA,WAAW,CAAoB;AAE/B,IAAA,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;IAEvD,WAAmB,CAAA,EAAc,EAAS,EAAqB,EAAA;QAA5C,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;AAEnE,IAAA,IAAI,IAAI,GAAA;QACJ,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACjE;IAED,kBAAkB,GAAA;AACd,QAAA,IAAK,IAAI,CAAC,SAAsC,CAAC,MAAM,EAAE;AACrD,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACzB,SAAA;QAEA,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,aAAa,EAAE;AAClC,gBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpD,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;AAED,IAAA,kBAAkB,CAAC,IAAc,EAAA;QAC7B,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;;AAC9F,YAAA,OAAO,IAAI,CAAC;KACpB;IAED,WAAW,CAAC,KAAY,EAAE,IAAc,EAAA;AACpC,QAAA,IAAI,WAAW,GAAY,KAAK,CAAC,MAAM,CAAC;QAExC,IAAI,WAAW,CAAC,SAAS,KAAK,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,gBAAgB,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC,EAAE;YAC1I,OAAO;AACV,SAAA;aAAM,IAAI,IAAI,CAAC,aAAa,EAAE;AAC3B,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;gBAC3B,OAAO;AACV,aAAA;YAED,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAC5C,YAAA,IAAI,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC;AAE1B,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;AACjC,gBAAA,IAAI,QAAQ,EAAE;AACV,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAClE,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAChE,iBAAA;AACJ,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,EAAE;AAC1C,gBAAA,IAAI,QAAQ,EAAE;oBACV,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AAC5E,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAClE,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACnD,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAChE,iBAAA;AACJ,aAAA;YAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,IAAc,EAAA;AAC/B,QAAA,IAAI,KAAK,GAAW,CAAC,CAAC,CAAC;AAEvB,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE;AACtC,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;AACjC,gBAAA,KAAK,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3C,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,EAAE;AAC1C,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;wBAC3B,KAAK,GAAG,CAAC,CAAC;wBACV,MAAM;AACT,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,UAAU,CAAC,IAAc,EAAA;QACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KAChD;uGA5KQ,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,mLAyBN,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAKhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA4CnB,aAAa,EApFpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAhEQ,qBAAqB,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,CAAA,CAAA;;2FAsErB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAZ7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,CAAA;;;;AAIT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,OAAO;AAChD,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;+GAMY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKkC,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAaI,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA0GrB,uBAAuB,CAAA;uGAAvB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,EApLvB,YAAA,EAAA,CAAA,iBAAiB,EAtEjB,qBAAqB,aAsPpB,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,CAhL3D,EAAA,OAAA,EAAA,CAAA,iBAAiB,EAiLG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGhC,uBAAuB,EAAA,OAAA,EAAA,CAJtB,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,EACvC,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGhC,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBALnC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,CAAC;AACrE,oBAAA,OAAO,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC;AAC1C,oBAAA,YAAY,EAAE,CAAC,iBAAiB,EAAE,qBAAqB,CAAC;AAC3D,iBAAA,CAAA;;;ACpVD;;AAEG;;;;"}