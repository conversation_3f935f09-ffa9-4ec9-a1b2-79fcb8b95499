import { inject, Injectable } from '@angular/core';
import { ColumnState, User } from '../../user-services/api/users';
import { LocalStorageService } from '../../auth-services/services/local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class TableElementsService {
  constructor(private localStorageService: LocalStorageService) { }

  // Get pages that shown in pagination
  getPagesToDisplay(lastPage: number, currentPage: number): (number | string)[] {
    const pages: (number | string)[] = [];

    if (lastPage <= 5) {
      for (let i = 1; i <= lastPage; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        pages.push(1, 2, 3, '...', lastPage);
      } else if (currentPage > lastPage - 3) {
        pages.push(1, '...', lastPage - 2, lastPage - 1, lastPage);
      } else {
        pages.push(1, '...', currentPage - 1, currentPage, currentPage + 1, '...', lastPage);
      }
    }

    return pages;
  }

  getUserPropertyValue(user: User, key: string): any {
    switch (key) {
      case 'profile':
        return user.user_profile_name? user.user_profile_name : user.profile_details?.name;
      case 'daily_traffic':
        return user.daily_traffic_details? user.daily_traffic_details.traffic : '';
      case 'parent':
        return user.parent_username? user.parent_username: '';
      case 'group':
        return user.group_details ? user.group_details.group_name : '';
      case 'username':
        return {
          value: user.username,
          link: `/users/${user.user_details? user.user_details.id : user.id}`,
          isEnabled: user.enabled == null? true : user.enabled,
        };
      case 'firstname':
        return user.firstname ? user.firstname : user.user_details?.firstname;
      case 'lastname':
        return user.lastname ? user.lastname : user.user_details?.lastname;
      case 'expiration':
        return user.expiration ? user.expiration : user.user_details?.expiration;
      case 'contract_id':
        return user.contract_id ? user.contract_id : user.user_details?.contract_id;
      case 'acctstarttime':
        return user.acctstarttime ? user.acctstarttime : "";
      default:
        return user[key as keyof User];
    }
  }

  mapUserPropertyName(key: string): string {
    switch (key) {
        case 'loan_balance':
            return 'Debts';
        case 'profile_username':
            return 'Profile';
        case 'mikrotik_ipv6_prefix':
            return 'IPv6 Prefix';
        case 'acctinputoctets':
            return 'Upload';
        case 'acctoutputoctets':
            return 'Download';
        case 'nasipaddress':
            return 'NAS';
        case 'parent_username':
            return 'Parent';
        case 'framedipaddress':
            return 'IP';
        case 'callingstationid':
            return 'MAC';
        case 'daily_usage_percentage':
            return 'Daily Quota';
        case 'oui':
            return 'Device';
        case 'calledstationid':
            return 'Service';
        case 'contract_id':
            return 'Contract ID';
        case 'acctstarttime':
          return 'uptime';
        default:
            return key;
    }
  }

  // Sort by column
  sortByColumn(key: string, requestForm: any): void {
    requestForm.sortBy = key;
    if (requestForm.direction !== 'asc')
      requestForm.direction = 'asc';
    else
    requestForm.direction = "desc";
  }

  toggleColumnSelection(columnKey: string, columnsState: ColumnState[], storageKey: string){
    const column = columnsState.find(col => col.key === columnKey);
    if (column) {
      column.hidden = !column.hidden;
      this.localStorageService.saveColumnsState(columnsState, storageKey);
    }
    // Re-render the page
    //this.fetchUsers();
  }

  getVisibleColumns(columnsState: ColumnState[]) : string[]{
    const visibleColumns = columnsState
    .filter(column => !column.hidden)
    .map(column => column.key);
    return visibleColumns;
  }

}
