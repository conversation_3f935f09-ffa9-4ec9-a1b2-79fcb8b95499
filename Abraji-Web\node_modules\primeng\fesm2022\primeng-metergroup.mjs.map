{"version": 3, "file": "primeng-metergroup.mjs", "sources": ["../../src/app/components/metergroup/metergroup.ts", "../../src/app/components/metergroup/primeng-metergroup.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, ElementRef, Input, NgModule, QueryList, TemplateRef, ViewEncapsulation, effect, forwardRef, inject, ViewChild } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { MeterItem } from './metergroup.interface';\n\n@Component({\n    selector: 'p-meterGroupLabel',\n    template: `\n        <ol [ngClass]=\"labelClass\">\n            <li *ngFor=\"let labelItem of value; let index = index; trackBy: parentInstance.trackByFn\" class=\"p-metergroup-label\">\n                <ng-container *ngIf=\"!iconTemplate\">\n                    <i *ngIf=\"labelItem.icon\" [class]=\"labelItem.icon\" [ngClass]=\"{ 'p-metergroup-label-icon': true }\" [ngStyle]=\"{ color: labelItem.color }\"></i>\n                    <span *ngIf=\"!labelItem.icon\" class=\"p-metergroup-label-marker\" [ngStyle]=\"{ backgroundColor: labelItem.color }\"></span>\n                </ng-container>\n                <ng-container *ngTemplateOutlet=\"iconTemplate; context: { $implicit: labelItem, icon: labelItem.icon }\"></ng-container>\n                <span class=\"p-metergroup-label-text\">{{ labelItem.label }} ({{ parentInstance?.percentValue(labelItem.value) }})</span>\n            </li>\n        </ol>\n    `\n})\nexport class MeterGroupLabel {\n    @Input() value: any[] = null;\n\n    @Input() labelPosition: 'start' | 'end' = 'end';\n\n    @Input() labelOrientation: 'horizontal' | 'vertical' = 'horizontal';\n\n    @Input() min: number;\n\n    @Input() max: number;\n\n    @Input() iconTemplate: TemplateRef<any> | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    get labelClass(): { [key: string]: boolean } {\n        return {\n            'p-metergroup-labels p-component': true,\n            'p-metergroup-labels-vertical': this.labelOrientation === 'vertical',\n            'p-metergroup-labels-horizontal': this.labelOrientation === 'horizontal'\n        };\n    }\n\n    parentInstance: MeterGroup = inject(forwardRef(() => MeterGroup));\n}\n/**\n * MeterGroup displays scalar measurements within a known range.\n * @group Components\n */\n@Component({\n    selector: 'p-meterGroup',\n    template: `\n        <div #container [ngClass]=\"containerClass\" role=\"meter\" [attr.aria-valuemin]=\"min\" [attr.aria-valuemax]=\"max\" [attr.aria-valuenow]=\"totalPercent()\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            @if (labelPosition === 'start') {\n            <p-meterGroupLabel *ngIf=\"!labelTemplate\" [value]=\"value\" [labelPosition]=\"labelPosition\" [labelOrientation]=\"labelOrientation\" [min]=\"min\" [max]=\"max\" [iconTemplate]=\"iconTemplate\" />\n            <ng-container *ngTemplateOutlet=\"labelTemplate; context: { $implicit: value, totalPercent: totalPercent(), percentages: percentages() }\"></ng-container>\n            }\n            <ng-container *ngTemplateOutlet=\"startTemplate; context: { $implicit: value, totalPercent: totalPercent(), percentages: percentages() }\"></ng-container>\n            <div class=\"p-metergroup-meters\">\n                <ng-container *ngFor=\"let meterItem of value; let index = index; trackBy: trackByFn\">\n                    <ng-container *ngTemplateOutlet=\"meterTemplate; context: { $implicit: meterItem, index: index, orientation: this.orientation, class: 'p-metergroup-meter', size: percentValue(meterItem.value), totalPercent: totalPercent() }\">\n                    </ng-container>\n                    <ng-container *ngIf=\"!meterTemplate\">\n                        <span class=\"p-metergroup-meter\" [ngStyle]=\"meterStyle(meterItem)\"></span>\n                    </ng-container>\n                </ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"endTemplate; context: { $implicit: value, totalPercent: totalPercent(), percentages: percentages() }\"></ng-container>\n            @if (labelPosition === 'end') {\n            <p-meterGroupLabel *ngIf=\"!labelTemplate\" [value]=\"value\" [labelPosition]=\"labelPosition\" [labelOrientation]=\"labelOrientation\" [min]=\"min\" [max]=\"max\" [iconTemplate]=\"iconTemplate\" />\n            <ng-container *ngTemplateOutlet=\"labelTemplate; context: { $implicit: value, totalPercent: totalPercent(), percentages: percentages() }\"></ng-container>\n            }\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None\n})\nexport class MeterGroup implements AfterContentInit {\n    /**\n     * Current value of the metergroup.\n     * @group Props\n     */\n    @Input() value: MeterItem[] | undefined;\n    /**\n     * Mininum boundary value.\n     * @group Props\n     */\n    @Input() min: number = 0;\n    /**\n     * Maximum boundary value.\n     * @group Props\n     */\n    @Input() max: number = 100;\n    /**\n     * Specifies the layout of the component, valid values are 'horizontal' and 'vertical'.\n     * @group Props\n     */\n    @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';\n    /**\n     * Specifies the label position of the component, valid values are 'start' and 'end'.\n     * @group Props\n     */\n    @Input() labelPosition: 'start' | 'end' = 'end';\n    /**\n     * Specifies the label orientation of the component, valid values are 'horizontal' and 'vertical'.\n     * @group Props\n     */\n    @Input() labelOrientation: string = 'horizontal';\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    get vertical(): boolean {\n        return this.orientation === 'vertical';\n    }\n\n    get containerClass() {\n        return {\n            'p-metergroup p-component': true,\n            'p-metergroup-horizontal': this.orientation === 'horizontal',\n            'p-metergroup-vertical': this.orientation === 'vertical'\n        };\n    }\n\n    labelTemplate: TemplateRef<any> | undefined;\n\n    meterTemplate: TemplateRef<any> | undefined;\n\n    endTemplate: TemplateRef<any> | undefined;\n\n    startTemplate: TemplateRef<any> | undefined;\n\n    iconTemplate: TemplateRef<any> | undefined;\n\n    @ViewChild('container', { read: ElementRef }) container: ElementRef;\n\n    ngAfterViewInit() {\n        const _container = this.container.nativeElement;\n        const height = DomHandler.getOuterHeight(_container);\n        this.vertical && (_container.style.height = height + 'px');\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'label':\n                    this.labelTemplate = item.template;\n                    break;\n                case 'meter':\n                    this.meterTemplate = item.template;\n                    break;\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n                default:\n                    break;\n            }\n        });\n    }\n\n    percent(meter = 0) {\n        const percentOfItem = ((meter - this.min) / (this.max - this.min)) * 100;\n\n        return Math.round(Math.max(0, Math.min(100, percentOfItem)));\n    }\n\n    percentValue(meter) {\n        return this.percent(meter) + '%';\n    }\n\n    meterStyle(val) {\n        return {\n            backgroundColor: val.color,\n            width: this.orientation === 'horizontal' && this.percentValue(val.value),\n            height: this.orientation === 'vertical' && this.percentValue(val.value)\n        };\n    }\n\n    totalPercent() {\n        return this.percent(this.value.reduce((total, val) => total + val.value, 0));\n    }\n\n    percentages() {\n        let sum = 0;\n        const sumsArray = [];\n\n        this.value.forEach((item) => {\n            sum += item.value;\n            sumsArray.push(sum);\n        });\n\n        return sumsArray;\n    }\n\n    trackByFn(index: number): number {\n        return index;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule],\n    exports: [MeterGroup, SharedModule],\n    declarations: [MeterGroup, MeterGroupLabel]\n})\nexport class MeterGroupModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;MAqBa,eAAe,CAAA;IACf,KAAK,GAAU,IAAI,CAAC;IAEpB,aAAa,GAAoB,KAAK,CAAC;IAEvC,gBAAgB,GAA8B,YAAY,CAAC;AAE3D,IAAA,GAAG,CAAS;AAEZ,IAAA,GAAG,CAAS;AAEZ,IAAA,YAAY,CAA+B;AAEpB,IAAA,SAAS,CAAuC;AAEhF,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,iCAAiC,EAAE,IAAI;AACvC,YAAA,8BAA8B,EAAE,IAAI,CAAC,gBAAgB,KAAK,UAAU;AACpE,YAAA,gCAAgC,EAAE,IAAI,CAAC,gBAAgB,KAAK,YAAY;SAC3E,CAAC;KACL;IAED,cAAc,GAAe,MAAM,CAAC,UAAU,CAAC,MAAM,UAAU,CAAC,CAAC,CAAC;uGAvBzD,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAf,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,GAAA,EAAA,KAAA,EAAA,GAAA,EAAA,KAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAaP,aAAa,EA1BpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;AAWT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;2FAEQ,eAAe,EAAA,UAAA,EAAA,CAAA;kBAf3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;AAWT,IAAA,CAAA;AACJ,iBAAA,CAAA;8BAEY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAEG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAEG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAEG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAE0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;AAYlC;;;AAGG;MA6BU,UAAU,CAAA;AACnB;;;AAGG;AACM,IAAA,KAAK,CAA0B;AACxC;;;AAGG;IACM,GAAG,GAAW,CAAC,CAAC;AACzB;;;AAGG;IACM,GAAG,GAAW,GAAG,CAAC;AAC3B;;;AAGG;IACM,WAAW,GAA8B,YAAY,CAAC;AAC/D;;;AAGG;IACM,aAAa,GAAoB,KAAK,CAAC;AAChD;;;AAGG;IACM,gBAAgB,GAAW,YAAY,CAAC;AACjD;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AAER,IAAA,SAAS,CAAuC;AAEhF,IAAA,IAAI,QAAQ,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC;KAC1C;AAED,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;AACH,YAAA,0BAA0B,EAAE,IAAI;AAChC,YAAA,yBAAyB,EAAE,IAAI,CAAC,WAAW,KAAK,YAAY;AAC5D,YAAA,uBAAuB,EAAE,IAAI,CAAC,WAAW,KAAK,UAAU;SAC3D,CAAC;KACL;AAED,IAAA,aAAa,CAA+B;AAE5C,IAAA,aAAa,CAA+B;AAE5C,IAAA,WAAW,CAA+B;AAE1C,IAAA,aAAa,CAA+B;AAE5C,IAAA,YAAY,CAA+B;AAEG,IAAA,SAAS,CAAa;IAEpE,eAAe,GAAA;AACX,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;QAChD,MAAM,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;KAC9D;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AACV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AACV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AACV,gBAAA,KAAK,KAAK;AACN,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,MAAM;AACV,gBAAA;oBACI,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,OAAO,CAAC,KAAK,GAAG,CAAC,EAAA;QACb,MAAM,aAAa,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;QAEzE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;KAChE;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;KACpC;AAED,IAAA,UAAU,CAAC,GAAG,EAAA;QACV,OAAO;YACH,eAAe,EAAE,GAAG,CAAC,KAAK;AAC1B,YAAA,KAAK,EAAE,IAAI,CAAC,WAAW,KAAK,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC;AACxE,YAAA,MAAM,EAAE,IAAI,CAAC,WAAW,KAAK,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC;SAC1E,CAAC;KACL;IAED,YAAY,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK,KAAK,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;KAChF;IAED,WAAW,GAAA;QACP,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,MAAM,SAAS,GAAG,EAAE,CAAC;QAErB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACxB,YAAA,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC;AAClB,YAAA,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,SAAS,CAAC;KACpB;AAED,IAAA,SAAS,CAAC,KAAa,EAAA;AACnB,QAAA,OAAO,KAAK,CAAC;KAChB;uGAtIQ,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EA0CF,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,GAAA,EAAA,KAAA,EAAA,GAAA,EAAA,KAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAwBE,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,IAAA,EAAA,UAAU,EA5FhC,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;AAsBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EArDQ,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,KAAA,EAAA,cAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAyDf,UAAU,EAAA,UAAA,EAAA,CAAA;kBA5BtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;AAsBT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACxC,iBAAA,CAAA;8BAMY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAKG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAE0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAwBgB,SAAS,EAAA,CAAA;sBAAtD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAA;;MA4EnC,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAhB,gBAAgB,EAAA,YAAA,EAAA,CA9IhB,UAAU,EAzDV,eAAe,CAAA,EAAA,OAAA,EAAA,CAmMd,YAAY,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CA1I3B,UAAU,EA2IG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGzB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EAJf,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EACd,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGzB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACrC,oBAAA,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;AACnC,oBAAA,YAAY,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC;AAC9C,iBAAA,CAAA;;;AC3ND;;AAEG;;;;"}