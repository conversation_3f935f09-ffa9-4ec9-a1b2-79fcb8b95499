{"version": 3, "file": "primeng-colorpicker.mjs", "sources": ["../../src/app/components/colorpicker/colorpicker.ts", "../../src/app/components/colorpicker/primeng-colorpicker.ts"], "sourcesContent": ["import { AnimationEvent, animate, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    PLATFORM_ID,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    forwardRef,\n    numberAttribute\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { OverlayService, PrimeNGConfig, TranslationKeys } from 'primeng/api';\nimport { ConnectedOverlayScrollHandler, DomHandler } from 'primeng/dom';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { ColorPickerChangeEvent } from './colorpicker.interface';\n\nexport const COLORPICKER_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => ColorPicker),\n    multi: true\n};\n/**\n * ColorPicker groups a collection of contents in tabs.\n * @group Components\n */\n@Component({\n    selector: 'p-colorPicker',\n    template: `\n        <div\n            #container\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [ngClass]=\"{ 'p-colorpicker p-component': true, 'p-colorpicker-overlay': !inline, 'p-colorpicker-dragging': colorDragging || hueDragging }\"\n            [attr.data-pc-name]=\"'colorpicker'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <input\n                *ngIf=\"!inline\"\n                #input\n                type=\"text\"\n                class=\"p-colorpicker-preview p-inputtext\"\n                [ngClass]=\"{ 'p-disabled': disabled }\"\n                readonly=\"readonly\"\n                [attr.tabindex]=\"tabindex\"\n                [disabled]=\"disabled\"\n                (click)=\"onInputClick()\"\n                (keydown)=\"onInputKeydown($event)\"\n                (focus)=\"onInputFocus()\"\n                [attr.id]=\"inputId\"\n                [style.backgroundColor]=\"inputBgColor\"\n                [attr.data-pc-section]=\"'input'\"\n                [attr.aria-label]=\"ariaLabel\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n            />\n            <div\n                *ngIf=\"inline || overlayVisible\"\n                [ngClass]=\"{ 'p-colorpicker-panel': true, 'p-colorpicker-overlay-panel': !inline, 'p-disabled': disabled }\"\n                (click)=\"onOverlayClick($event)\"\n                [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n                [@.disabled]=\"inline === true\"\n                (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n                (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n                [attr.data-pc-section]=\"'panel'\"\n            >\n                <div class=\"p-colorpicker-content\" [attr.data-pc-section]=\"'content'\">\n                    <div #colorSelector class=\"p-colorpicker-color-selector\" (touchstart)=\"onColorDragStart($event)\" (touchmove)=\"onDrag($event)\" (touchend)=\"onDragEnd()\" (mousedown)=\"onColorMousedown($event)\" [attr.data-pc-section]=\"'selector'\">\n                        <div class=\"p-colorpicker-color\" [attr.data-pc-section]=\"'color'\">\n                            <div #colorHandle class=\"p-colorpicker-color-handle\" [attr.data-pc-section]=\"'colorHandle'\"></div>\n                        </div>\n                    </div>\n                    <div #hue class=\"p-colorpicker-hue\" (mousedown)=\"onHueMousedown($event)\" (touchstart)=\"onHueDragStart($event)\" (touchmove)=\"onDrag($event)\" (touchend)=\"onDragEnd()\" [attr.data-pc-section]=\"'hue'\">\n                        <div #hueHandle class=\"p-colorpicker-hue-handle\" [attr.data-pc-section]=\"'hueHandle'\"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `,\n    animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0, transform: 'scaleY(0.8)' }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({ opacity: 0 }))])])],\n    providers: [COLORPICKER_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./colorpicker.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ColorPicker implements ControlValueAccessor, OnDestroy {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Whether to display as an overlay or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) inline: boolean | undefined;\n    /**\n     * Format to use in value binding.\n     * @group Props\n     */\n    @Input() format: 'hex' | 'rgb' | 'hsb' = 'hex';\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input() tabindex: string | undefined;\n    /**\n     * Identifier of the focus input to match a label defined for the dropdown.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.1s linear';\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Callback to invoke on value change.\n     * @param {ColorPickerChangeEvent} event - Custom value change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<ColorPickerChangeEvent> = new EventEmitter<ColorPickerChangeEvent>();\n    /**\n     * Callback to invoke on panel is shown.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke on panel is hidden.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<any> = new EventEmitter<any>();\n\n    @ViewChild('container') containerViewChild: Nullable<ElementRef>;\n\n    @ViewChild('input') inputViewChild: Nullable<ElementRef>;\n\n    value: any = { h: 0, s: 100, b: 100 };\n\n    inputBgColor: string | undefined;\n\n    shown: Nullable<boolean>;\n\n    overlayVisible: Nullable<boolean>;\n\n    defaultColor: string = 'ff0000';\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    documentClickListener: VoidListener;\n\n    documentResizeListener: VoidListener;\n\n    documentMousemoveListener: VoidListener;\n\n    documentMouseupListener: VoidListener;\n\n    documentHueMoveListener: VoidListener;\n\n    scrollHandler: Nullable<ConnectedOverlayScrollHandler>;\n\n    selfClick: Nullable<boolean>;\n\n    colorDragging: Nullable<boolean>;\n\n    hueDragging: Nullable<boolean>;\n\n    overlay: Nullable<HTMLDivElement>;\n\n    colorSelectorViewChild: Nullable<ElementRef>;\n\n    colorHandleViewChild: Nullable<ElementRef>;\n\n    hueViewChild: Nullable<ElementRef>;\n\n    hueHandleViewChild: Nullable<ElementRef>;\n\n    window: Window;\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        public el: ElementRef,\n        public renderer: Renderer2,\n        public cd: ChangeDetectorRef,\n        public config: PrimeNGConfig,\n        public overlayService: OverlayService\n    ) {\n        this.window = this.document.defaultView as Window;\n    }\n\n    @ViewChild('colorSelector') set colorSelector(element: ElementRef) {\n        this.colorSelectorViewChild = element;\n    }\n\n    @ViewChild('colorHandle') set colorHandle(element: ElementRef) {\n        this.colorHandleViewChild = element;\n    }\n\n    @ViewChild('hue') set hue(element: ElementRef) {\n        this.hueViewChild = element;\n    }\n\n    @ViewChild('hueHandle') set hueHandle(element: ElementRef) {\n        this.hueHandleViewChild = element;\n    }\n\n    get ariaLabel() {\n        return this.config?.getTranslation(TranslationKeys.ARIA)[TranslationKeys.SELECT_COLOR];\n    }\n\n    onHueMousedown(event: MouseEvent) {\n        if (this.disabled) {\n            return;\n        }\n\n        this.bindDocumentMousemoveListener();\n        this.bindDocumentMouseupListener();\n\n        this.hueDragging = true;\n        this.pickHue(event);\n    }\n\n    onHueDragStart(event: TouchEvent) {\n        if (this.disabled) {\n            return;\n        }\n\n        this.hueDragging = true;\n        this.pickHue(event, (event as TouchEvent).changedTouches[0]);\n    }\n\n    onColorDragStart(event: TouchEvent) {\n        if (this.disabled) {\n            return;\n        }\n\n        this.colorDragging = true;\n        this.pickColor(event, (event as TouchEvent).changedTouches[0]);\n    }\n\n    pickHue(event: MouseEvent | TouchEvent, position?: any) {\n        let pageY = position ? position.pageY : (event as MouseEvent).pageY;\n        let top: number = this.hueViewChild?.nativeElement.getBoundingClientRect().top + ((this.document as any).defaultView.pageYOffset || this.document.documentElement.scrollTop || this.document.body.scrollTop || 0);\n        this.value = this.validateHSB({\n            h: Math.floor((360 * (150 - Math.max(0, Math.min(150, pageY - top)))) / 150),\n            s: this.value.s,\n            b: this.value.b\n        });\n\n        this.updateColorSelector();\n        this.updateUI();\n        this.updateModel();\n        this.onChange.emit({ originalEvent: event, value: this.getValueToUpdate() });\n    }\n\n    onColorMousedown(event: MouseEvent) {\n        if (this.disabled) {\n            return;\n        }\n\n        this.bindDocumentMousemoveListener();\n        this.bindDocumentMouseupListener();\n\n        this.colorDragging = true;\n        this.pickColor(event);\n    }\n\n    onDrag(event: TouchEvent) {\n        if (this.colorDragging) {\n            this.pickColor(event, event.changedTouches[0]);\n            event.preventDefault();\n        }\n\n        if (this.hueDragging) {\n            this.pickHue(event, event.changedTouches[0]);\n            event.preventDefault();\n        }\n    }\n\n    onDragEnd() {\n        this.colorDragging = false;\n        this.hueDragging = false;\n\n        this.unbindDocumentMousemoveListener();\n        this.unbindDocumentMouseupListener();\n    }\n\n    pickColor(event: MouseEvent | TouchEvent, position?: any) {\n        let pageX = position ? position.pageX : (event as MouseEvent).pageX;\n        let pageY = position ? position.pageY : (event as MouseEvent).pageY;\n        let rect = this.colorSelectorViewChild?.nativeElement.getBoundingClientRect();\n        let top = rect.top + ((this.document as any).defaultView.pageYOffset || this.document.documentElement.scrollTop || this.document.body.scrollTop || 0);\n        let left = rect.left + this.document.body.scrollLeft;\n        let saturation = Math.floor((100 * Math.max(0, Math.min(150, pageX - left))) / 150);\n        let brightness = Math.floor((100 * (150 - Math.max(0, Math.min(150, pageY - top)))) / 150);\n        this.value = this.validateHSB({\n            h: this.value.h,\n            s: saturation,\n            b: brightness\n        });\n\n        this.updateUI();\n        this.updateModel();\n        this.onChange.emit({ originalEvent: event, value: this.getValueToUpdate() });\n    }\n\n    getValueToUpdate() {\n        let val: any;\n        switch (this.format) {\n            case 'hex':\n                val = '#' + this.HSBtoHEX(this.value);\n                break;\n\n            case 'rgb':\n                val = this.HSBtoRGB(this.value);\n                break;\n\n            case 'hsb':\n                val = this.value;\n                break;\n        }\n\n        return val;\n    }\n\n    updateModel(): void {\n        this.onModelChange(this.getValueToUpdate());\n        this.cd.markForCheck();\n    }\n\n    writeValue(value: any): void {\n        if (value) {\n            switch (this.format) {\n                case 'hex':\n                    this.value = this.HEXtoHSB(value);\n                    break;\n\n                case 'rgb':\n                    this.value = this.RGBtoHSB(value);\n                    break;\n\n                case 'hsb':\n                    this.value = value;\n                    break;\n            }\n        } else {\n            this.value = this.HEXtoHSB(this.defaultColor);\n        }\n\n        this.updateColorSelector();\n        this.updateUI();\n        this.cd.markForCheck();\n    }\n\n    updateColorSelector() {\n        if (this.colorSelectorViewChild) {\n            const hsb: any = {};\n            hsb.s = 100;\n            hsb.b = 100;\n            hsb.h = this.value.h;\n\n            this.colorSelectorViewChild.nativeElement.style.backgroundColor = '#' + this.HSBtoHEX(hsb);\n        }\n    }\n\n    updateUI() {\n        if (this.colorHandleViewChild && this.hueHandleViewChild?.nativeElement) {\n            this.colorHandleViewChild.nativeElement.style.left = Math.floor((150 * this.value.s) / 100) + 'px';\n            this.colorHandleViewChild.nativeElement.style.top = Math.floor((150 * (100 - this.value.b)) / 100) + 'px';\n            this.hueHandleViewChild.nativeElement.style.top = Math.floor(150 - (150 * this.value.h) / 360) + 'px';\n        }\n\n        this.inputBgColor = '#' + this.HSBtoHEX(this.value);\n    }\n\n    onInputFocus() {\n        this.onModelTouched();\n    }\n\n    show() {\n        this.overlayVisible = true;\n        this.cd.markForCheck();\n    }\n\n    onOverlayAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                if (!this.inline) {\n                    this.overlay = event.element;\n                    this.appendOverlay();\n\n                    if (this.autoZIndex) {\n                        ZIndexUtils.set('overlay', this.overlay, this.config.zIndex.overlay);\n                    }\n\n                    this.alignOverlay();\n                    this.bindDocumentClickListener();\n                    this.bindDocumentResizeListener();\n                    this.bindScrollListener();\n\n                    this.updateColorSelector();\n                    this.updateUI();\n                }\n                break;\n\n            case 'void':\n                this.onOverlayHide();\n                break;\n        }\n    }\n\n    onOverlayAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                if (!this.inline) {\n                    this.onShow.emit({});\n                }\n                break;\n\n            case 'void':\n                if (this.autoZIndex) {\n                    ZIndexUtils.clear(event.element);\n                }\n\n                this.onHide.emit({});\n                break;\n        }\n    }\n\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.overlay);\n            else DomHandler.appendChild(this.overlay, this.appendTo);\n        }\n    }\n\n    restoreOverlayAppend() {\n        if (this.overlay && this.appendTo) {\n            this.renderer.appendChild(this.el.nativeElement, this.overlay);\n        }\n    }\n\n    alignOverlay() {\n        if (this.appendTo) DomHandler.absolutePosition(this.overlay, this.inputViewChild?.nativeElement);\n        else DomHandler.relativePosition(this.overlay, this.inputViewChild?.nativeElement);\n    }\n\n    hide() {\n        this.overlayVisible = false;\n        this.cd.markForCheck();\n    }\n\n    onInputClick() {\n        this.selfClick = true;\n        this.togglePanel();\n    }\n\n    togglePanel() {\n        if (!this.overlayVisible) this.show();\n        else this.hide();\n    }\n\n    onInputKeydown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'Space':\n                this.togglePanel();\n                event.preventDefault();\n                break;\n\n            case 'Escape':\n            case 'Tab':\n                this.hide();\n                break;\n\n            default:\n                //NoOp\n                break;\n        }\n    }\n\n    onOverlayClick(event: MouseEvent) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n\n        this.selfClick = true;\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            const documentTarget: any = this.el ? this.el.nativeElement.ownerDocument : 'document';\n\n            this.documentClickListener = this.renderer.listen(documentTarget, 'click', () => {\n                if (!this.selfClick) {\n                    this.overlayVisible = false;\n                    this.unbindDocumentClickListener();\n                }\n\n                this.selfClick = false;\n                this.cd.markForCheck();\n            });\n        }\n    }\n\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n\n    bindDocumentMousemoveListener() {\n        if (!this.documentMousemoveListener) {\n            const documentTarget: any = this.el ? this.el.nativeElement.ownerDocument : 'document';\n\n            this.documentMousemoveListener = this.renderer.listen(documentTarget, 'mousemove', (event: MouseEvent) => {\n                if (this.colorDragging) {\n                    this.pickColor(event);\n                }\n\n                if (this.hueDragging) {\n                    this.pickHue(event);\n                }\n            });\n        }\n    }\n\n    unbindDocumentMousemoveListener() {\n        if (this.documentMousemoveListener) {\n            this.documentMousemoveListener();\n            this.documentMousemoveListener = null;\n        }\n    }\n\n    bindDocumentMouseupListener() {\n        if (!this.documentMouseupListener) {\n            const documentTarget: any = this.el ? this.el.nativeElement.ownerDocument : 'document';\n\n            this.documentMouseupListener = this.renderer.listen(documentTarget, 'mouseup', () => {\n                this.colorDragging = false;\n                this.hueDragging = false;\n                this.unbindDocumentMousemoveListener();\n                this.unbindDocumentMouseupListener();\n            });\n        }\n    }\n\n    unbindDocumentMouseupListener() {\n        if (this.documentMouseupListener) {\n            this.documentMouseupListener();\n            this.documentMouseupListener = null;\n        }\n    }\n\n    bindDocumentResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.documentResizeListener = this.renderer.listen(this.window, 'resize', this.onWindowResize.bind(this));\n        }\n    }\n\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n\n    onWindowResize() {\n        if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.containerViewChild?.nativeElement, () => {\n                if (this.overlayVisible) {\n                    this.hide();\n                }\n            });\n        }\n\n        this.scrollHandler.bindScrollListener();\n    }\n\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n\n    validateHSB(hsb: { h: number; s: number; b: number }) {\n        return {\n            h: Math.min(360, Math.max(0, hsb.h)),\n            s: Math.min(100, Math.max(0, hsb.s)),\n            b: Math.min(100, Math.max(0, hsb.b))\n        };\n    }\n\n    validateRGB(rgb: { r: number; g: number; b: number }) {\n        return {\n            r: Math.min(255, Math.max(0, rgb.r)),\n            g: Math.min(255, Math.max(0, rgb.g)),\n            b: Math.min(255, Math.max(0, rgb.b))\n        };\n    }\n\n    validateHEX(hex: string) {\n        var len = 6 - hex.length;\n        if (len > 0) {\n            var o = [];\n            for (var i = 0; i < len; i++) {\n                o.push('0');\n            }\n            o.push(hex);\n            hex = o.join('');\n        }\n        return hex;\n    }\n\n    HEXtoRGB(hex: string) {\n        let hexValue = parseInt(hex.indexOf('#') > -1 ? hex.substring(1) : hex, 16);\n        return { r: hexValue >> 16, g: (hexValue & 0x00ff00) >> 8, b: hexValue & 0x0000ff };\n    }\n\n    HEXtoHSB(hex: string) {\n        return this.RGBtoHSB(this.HEXtoRGB(hex));\n    }\n\n    RGBtoHSB(rgb: { r: number; g: number; b: number }) {\n        var hsb = {\n            h: 0,\n            s: 0,\n            b: 0\n        };\n        var min = Math.min(rgb.r, rgb.g, rgb.b);\n        var max = Math.max(rgb.r, rgb.g, rgb.b);\n        var delta = max - min;\n        hsb.b = max;\n        hsb.s = max != 0 ? (255 * delta) / max : 0;\n        if (hsb.s != 0) {\n            if (rgb.r == max) {\n                hsb.h = (rgb.g - rgb.b) / delta;\n            } else if (rgb.g == max) {\n                hsb.h = 2 + (rgb.b - rgb.r) / delta;\n            } else {\n                hsb.h = 4 + (rgb.r - rgb.g) / delta;\n            }\n        } else {\n            hsb.h = -1;\n        }\n        hsb.h *= 60;\n        if (hsb.h < 0) {\n            hsb.h += 360;\n        }\n        hsb.s *= 100 / 255;\n        hsb.b *= 100 / 255;\n        return hsb;\n    }\n\n    HSBtoRGB(hsb: { h: number; s: number; b: number }) {\n        var rgb = {\n            r: 0,\n            g: 0,\n            b: 0\n        };\n        let h: number = hsb.h;\n        let s: number = (hsb.s * 255) / 100;\n        let v: number = (hsb.b * 255) / 100;\n        if (s == 0) {\n            rgb = {\n                r: v,\n                g: v,\n                b: v\n            };\n        } else {\n            let t1: number = v;\n            let t2: number = ((255 - s) * v) / 255;\n            let t3: number = ((t1 - t2) * (h % 60)) / 60;\n            if (h == 360) h = 0;\n            if (h < 60) {\n                rgb.r = t1;\n                rgb.b = t2;\n                rgb.g = t2 + t3;\n            } else if (h < 120) {\n                rgb.g = t1;\n                rgb.b = t2;\n                rgb.r = t1 - t3;\n            } else if (h < 180) {\n                rgb.g = t1;\n                rgb.r = t2;\n                rgb.b = t2 + t3;\n            } else if (h < 240) {\n                rgb.b = t1;\n                rgb.r = t2;\n                rgb.g = t1 - t3;\n            } else if (h < 300) {\n                rgb.b = t1;\n                rgb.g = t2;\n                rgb.r = t2 + t3;\n            } else if (h < 360) {\n                rgb.r = t1;\n                rgb.g = t2;\n                rgb.b = t1 - t3;\n            } else {\n                rgb.r = 0;\n                rgb.g = 0;\n                rgb.b = 0;\n            }\n        }\n        return { r: Math.round(rgb.r), g: Math.round(rgb.g), b: Math.round(rgb.b) };\n    }\n\n    RGBtoHEX(rgb: { r: number; g: number; b: number }) {\n        var hex = [rgb.r.toString(16), rgb.g.toString(16), rgb.b.toString(16)];\n\n        for (var key in hex) {\n            if (hex[key].length == 1) {\n                hex[key] = '0' + hex[key];\n            }\n        }\n\n        return hex.join('');\n    }\n\n    HSBtoHEX(hsb: { h: number; s: number; b: number }) {\n        return this.RGBtoHEX(this.HSBtoRGB(hsb));\n    }\n\n    onOverlayHide() {\n        this.unbindScrollListener();\n        this.unbindDocumentResizeListener();\n        this.unbindDocumentClickListener();\n        this.overlay = null;\n    }\n\n    ngOnDestroy() {\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.overlay && this.autoZIndex) {\n            ZIndexUtils.clear(this.overlay);\n        }\n\n        this.restoreOverlayAppend();\n        this.onOverlayHide();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, AutoFocusModule],\n    exports: [ColorPicker],\n    declarations: [ColorPicker]\n})\nexport class ColorPickerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA8Ba,MAAA,0BAA0B,GAAQ;AAC3C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,WAAW,CAAC;AAC1C,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MA+DU,WAAW,CAAA;AAoIU,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACtB,IAAA,EAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACA,IAAA,MAAA,CAAA;AACA,IAAA,cAAA,CAAA;AAzIX;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACqC,IAAA,MAAM,CAAsB;AACpE;;;AAGG;IACM,MAAM,GAA0B,KAAK,CAAC;AAC/C;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;IACM,qBAAqB,GAAW,iCAAiC,CAAC;AAC3E;;;AAGG;IACM,qBAAqB,GAAW,YAAY,CAAC;AACtD;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;AACO,IAAA,QAAQ,GAAyC,IAAI,YAAY,EAA0B,CAAC;AACtG;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAEtC,IAAA,kBAAkB,CAAuB;AAE7C,IAAA,cAAc,CAAuB;AAEzD,IAAA,KAAK,GAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AAEtC,IAAA,YAAY,CAAqB;AAEjC,IAAA,KAAK,CAAoB;AAEzB,IAAA,cAAc,CAAoB;IAElC,YAAY,GAAW,QAAQ,CAAC;AAEhC,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,qBAAqB,CAAe;AAEpC,IAAA,sBAAsB,CAAe;AAErC,IAAA,yBAAyB,CAAe;AAExC,IAAA,uBAAuB,CAAe;AAEtC,IAAA,uBAAuB,CAAe;AAEtC,IAAA,aAAa,CAA0C;AAEvD,IAAA,SAAS,CAAoB;AAE7B,IAAA,aAAa,CAAoB;AAEjC,IAAA,WAAW,CAAoB;AAE/B,IAAA,OAAO,CAA2B;AAElC,IAAA,sBAAsB,CAAuB;AAE7C,IAAA,oBAAoB,CAAuB;AAE3C,IAAA,YAAY,CAAuB;AAEnC,IAAA,kBAAkB,CAAuB;AAEzC,IAAA,MAAM,CAAS;AAEf,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACrC,EAAc,EACd,QAAmB,EACnB,EAAqB,EACrB,MAAqB,EACrB,cAA8B,EAAA;QANX,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACrC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACrB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACrB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QAErC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;KACrD;IAED,IAAgC,aAAa,CAAC,OAAmB,EAAA;AAC7D,QAAA,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC;KACzC;IAED,IAA8B,WAAW,CAAC,OAAmB,EAAA;AACzD,QAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;KACvC;IAED,IAAsB,GAAG,CAAC,OAAmB,EAAA;AACzC,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;KAC/B;IAED,IAA4B,SAAS,CAAC,OAAmB,EAAA;AACrD,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;KACrC;AAED,IAAA,IAAI,SAAS,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;KAC1F;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;QAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;QAED,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACrC,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAEnC,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACvB;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;QAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAG,KAAoB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;KAChE;AAED,IAAA,gBAAgB,CAAC,KAAiB,EAAA;QAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAG,KAAoB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;KAClE;IAED,OAAO,CAAC,KAA8B,EAAE,QAAc,EAAA;AAClD,QAAA,IAAI,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAI,KAAoB,CAAC,KAAK,CAAC;AACpE,QAAA,IAAI,GAAG,GAAW,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,qBAAqB,EAAE,CAAC,GAAG,IAAK,IAAI,CAAC,QAAgB,CAAC,WAAW,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;AAClN,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;AAC1B,YAAA,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;AAC5E,YAAA,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACf,YAAA,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,SAAA,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;KAChF;AAED,IAAA,gBAAgB,CAAC,KAAiB,EAAA;QAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;QAED,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACrC,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAEnC,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACzB;AAED,IAAA,MAAM,CAAC,KAAiB,EAAA;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACvC,IAAI,CAAC,6BAA6B,EAAE,CAAC;KACxC;IAED,SAAS,CAAC,KAA8B,EAAE,QAAc,EAAA;AACpD,QAAA,IAAI,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAI,KAAoB,CAAC,KAAK,CAAC;AACpE,QAAA,IAAI,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAI,KAAoB,CAAC,KAAK,CAAC;QACpE,IAAI,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,qBAAqB,EAAE,CAAC;AAC9E,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,IAAK,IAAI,CAAC,QAAgB,CAAC,WAAW,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;AACtJ,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;AACrD,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AACpF,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AAC3F,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;AAC1B,YAAA,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACf,YAAA,CAAC,EAAE,UAAU;AACb,YAAA,CAAC,EAAE,UAAU;AAChB,SAAA,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;KAChF;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,GAAQ,CAAC;QACb,QAAQ,IAAI,CAAC,MAAM;AACf,YAAA,KAAK,KAAK;gBACN,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM;AAEV,YAAA,KAAK,KAAK;gBACN,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAChC,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;gBACjB,MAAM;AACb,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACd;IAED,WAAW,GAAA;QACP,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,KAAK,EAAE;YACP,QAAQ,IAAI,CAAC,MAAM;AACf,gBAAA,KAAK,KAAK;oBACN,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,KAAK;oBACN,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,KAAK;AACN,oBAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,MAAM;AACb,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACjD,SAAA;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,mBAAmB,GAAA;QACf,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,MAAM,GAAG,GAAQ,EAAE,CAAC;AACpB,YAAA,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACZ,YAAA,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;YACZ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAErB,YAAA,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC9F,SAAA;KACJ;IAED,QAAQ,GAAA;QACJ,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE;YACrE,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AACnG,YAAA,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AAC1G,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC;AACzG,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACvD;IAED,YAAY,GAAA;QACR,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,uBAAuB,CAAC,KAAqB,EAAA;QACzC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,oBAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;oBAC7B,IAAI,CAAC,aAAa,EAAE,CAAC;oBAErB,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,wBAAA,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACxE,qBAAA;oBAED,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACjC,IAAI,CAAC,0BAA0B,EAAE,CAAC;oBAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAE1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,MAAM;AACb,SAAA;KACJ;AAED,IAAA,qBAAqB,CAAC,KAAqB,EAAA;QACvC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,oBAAA,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACpC,iBAAA;AAED,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrB,MAAM;AACb,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;AAAE,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;;gBACrF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5D,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/B,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAClE,SAAA;KACJ;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;;AAC5F,YAAA,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;KACtF;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,YAAY,GAAA;AACR,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;KACtB;IAED,WAAW,GAAA;QACP,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,IAAI,EAAE,CAAC;;YACjC,IAAI,CAAC,IAAI,EAAE,CAAC;KACpB;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;QAC/B,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO;gBACR,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,KAAK;gBACN,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,MAAM;AAEV,YAAA;;gBAEI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa;AAChC,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,yBAAyB,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AAC7B,YAAA,MAAM,cAAc,GAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,GAAG,UAAU,CAAC;AAEvF,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,EAAE,MAAK;AAC5E,gBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACjB,oBAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC5B,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,iBAAA;AAED,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,gBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,2BAA2B,GAAA;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;KACJ;IAED,6BAA6B,GAAA;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;AACjC,YAAA,MAAM,cAAc,GAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,GAAG,UAAU,CAAC;AAEvF,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC,KAAiB,KAAI;gBACrG,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACzB,iBAAA;gBAED,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,oBAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvB,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,+BAA+B,GAAA;QAC3B,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjC,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;AACzC,SAAA;KACJ;IAED,2BAA2B,GAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AAC/B,YAAA,MAAM,cAAc,GAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,GAAG,UAAU,CAAC;AAEvF,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,SAAS,EAAE,MAAK;AAChF,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,gBAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,+BAA+B,EAAE,CAAC;gBACvC,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACzC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,6BAA6B,GAAA;QACzB,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACvC,SAAA;KACJ;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7G,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,cAAc,GAAA;QACV,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;YACpD,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE,MAAK;gBAChG,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;KAC3C;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;AAC7C,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,GAAwC,EAAA;QAChD,OAAO;AACH,YAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,YAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,YAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;SACvC,CAAC;KACL;AAED,IAAA,WAAW,CAAC,GAAwC,EAAA;QAChD,OAAO;AACH,YAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,YAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,YAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;SACvC,CAAC;KACL;AAED,IAAA,WAAW,CAAC,GAAW,EAAA;AACnB,QAAA,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;QACzB,IAAI,GAAG,GAAG,CAAC,EAAE;YACT,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC1B,gBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,aAAA;AACD,YAAA,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,YAAA,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACd;AAED,IAAA,QAAQ,CAAC,GAAW,EAAA;AAChB,QAAA,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;QAC5E,OAAO,EAAE,CAAC,EAAE,QAAQ,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,KAAK,CAAC,EAAE,CAAC,EAAE,QAAQ,GAAG,QAAQ,EAAE,CAAC;KACvF;AAED,IAAA,QAAQ,CAAC,GAAW,EAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5C;AAED,IAAA,QAAQ,CAAC,GAAwC,EAAA;AAC7C,QAAA,IAAI,GAAG,GAAG;AACN,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;SACP,CAAC;AACF,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,QAAA,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;AACtB,QAAA,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QACZ,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC;AAC3C,QAAA,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE;AACZ,YAAA,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;AACd,gBAAA,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;AACnC,aAAA;AAAM,iBAAA,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;AACrB,gBAAA,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;AACvC,aAAA;AAAM,iBAAA;AACH,gBAAA,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;AACvC,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACd,SAAA;AACD,QAAA,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;AACX,YAAA,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;AAChB,SAAA;AACD,QAAA,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC;AACnB,QAAA,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC;AACnB,QAAA,OAAO,GAAG,CAAC;KACd;AAED,IAAA,QAAQ,CAAC,GAAwC,EAAA;AAC7C,QAAA,IAAI,GAAG,GAAG;AACN,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;AACJ,YAAA,CAAC,EAAE,CAAC;SACP,CAAC;AACF,QAAA,IAAI,CAAC,GAAW,GAAG,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,GAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;QACpC,IAAI,CAAC,GAAW,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,EAAE;AACR,YAAA,GAAG,GAAG;AACF,gBAAA,CAAC,EAAE,CAAC;AACJ,gBAAA,CAAC,EAAE,CAAC;AACJ,gBAAA,CAAC,EAAE,CAAC;aACP,CAAC;AACL,SAAA;AAAM,aAAA;YACH,IAAI,EAAE,GAAW,CAAC,CAAC;AACnB,YAAA,IAAI,EAAE,GAAW,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;AACvC,YAAA,IAAI,EAAE,GAAW,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,GAAG;gBAAE,CAAC,GAAG,CAAC,CAAC;YACpB,IAAI,CAAC,GAAG,EAAE,EAAE;AACR,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,aAAA;iBAAM,IAAI,CAAC,GAAG,GAAG,EAAE;AAChB,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,aAAA;iBAAM,IAAI,CAAC,GAAG,GAAG,EAAE;AAChB,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,aAAA;iBAAM,IAAI,CAAC,GAAG,GAAG,EAAE;AAChB,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,aAAA;iBAAM,IAAI,CAAC,GAAG,GAAG,EAAE;AAChB,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,aAAA;iBAAM,IAAI,CAAC,GAAG,GAAG,EAAE;AAChB,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACnB,aAAA;AAAM,iBAAA;AACH,gBAAA,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACV,gBAAA,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACV,gBAAA,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;KAC/E;AAED,IAAA,QAAQ,CAAC,GAAwC,EAAA;AAC7C,QAAA,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAEvE,QAAA,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;YACjB,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;gBACtB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7B,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACvB;AAED,IAAA,QAAQ,CAAC,GAAwC,EAAA;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5C;IAED,aAAa,GAAA;QACT,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;AACjC,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACnC,SAAA;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB;uGA3sBQ,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAoIR,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AArId,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAeA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CAehB,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,sFAehB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,eAAe,CAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAef,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAzEzB,CAAC,0BAA0B,CAAC,EApD7B,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,KAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,KAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkDT,EACW,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,siCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FASpO,WAAW,EAAA,UAAA,EAAA,CAAA;kBA9DvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDT,IAAA,CAAA,EAAA,UAAA,EACW,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAClO,SAAA,EAAA,CAAC,0BAA0B,CAAC,EAAA,eAAA,EACtB,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,siCAAA,CAAA,EAAA,CAAA;;0BAsII,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;mLAhId,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEiB,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEF,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBA0Dc,aAAa,EAAA,CAAA;sBAA5C,SAAS;uBAAC,eAAe,CAAA;gBAII,WAAW,EAAA,CAAA;sBAAxC,SAAS;uBAAC,aAAa,CAAA;gBAIF,GAAG,EAAA,CAAA;sBAAxB,SAAS;uBAAC,KAAK,CAAA;gBAIY,SAAS,EAAA,CAAA;sBAApC,SAAS;uBAAC,WAAW,CAAA;;MAwjBb,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,iBAntBjB,WAAW,CAAA,EAAA,OAAA,EAAA,CA+sBV,YAAY,EAAE,eAAe,aA/sB9B,WAAW,CAAA,EAAA,CAAA,CAAA;wGAmtBX,iBAAiB,EAAA,OAAA,EAAA,CAJhB,YAAY,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA;;2FAI9B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC;oBACxC,OAAO,EAAE,CAAC,WAAW,CAAC;oBACtB,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;ACvzBD;;AAEG;;;;"}