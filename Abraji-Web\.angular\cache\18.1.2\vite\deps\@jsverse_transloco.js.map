{"version": 3, "sources": ["../../../../../node_modules/@angular/core/fesm2022/rxjs-interop.mjs", "../../../../../node_modules/flat/index.js", "../../../../../node_modules/@jsverse/transloco/fesm2022/jsverse-transloco.mjs"], "sourcesContent": ["/**\n * @license Angular v18.1.2\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError, ɵgetOutputDestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  constructor(source) {\n    this.source = source;\n    this.destroyed = false;\n    this.destroyRef = inject(DestroyRef);\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new ɵRuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @developerPreview\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @developerPreview\n */\nfunction outputToObservable(ref) {\n  const destroyRef = ɵgetOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n  ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  const equal = makeToSignalEqual(options?.equal);\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    }, {\n      equal\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    }, {\n      equal\n    });\n  }\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  }, {\n    equal: options?.equal\n  });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n  return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { outputFromObservable, outputToObservable, takeUntilDestroyed, toObservable, toSignal };\n", "function isBuffer(obj) {\n  return obj && obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj);\n}\nfunction keyIdentity(key) {\n  return key;\n}\nexport function flatten(target, opts) {\n  opts = opts || {};\n  const delimiter = opts.delimiter || '.';\n  const maxDepth = opts.maxDepth;\n  const transformKey = opts.transformKey || keyIdentity;\n  const output = {};\n  function step(object, prev, currentDepth) {\n    currentDepth = currentDepth || 1;\n    Object.keys(object).forEach(function (key) {\n      const value = object[key];\n      const isarray = opts.safe && Array.isArray(value);\n      const type = Object.prototype.toString.call(value);\n      const isbuffer = isBuffer(value);\n      const isobject = type === '[object Object]' || type === '[object Array]';\n      const newKey = prev ? prev + delimiter + transformKey(key) : transformKey(key);\n      if (!isarray && !isbuffer && isobject && Object.keys(value).length && (!opts.maxDepth || currentDepth < maxDepth)) {\n        return step(value, newKey, currentDepth + 1);\n      }\n      output[newKey] = value;\n    });\n  }\n  step(target);\n  return output;\n}\nexport function unflatten(target, opts) {\n  opts = opts || {};\n  const delimiter = opts.delimiter || '.';\n  const overwrite = opts.overwrite || false;\n  const transformKey = opts.transformKey || keyIdentity;\n  const result = {};\n  const isbuffer = isBuffer(target);\n  if (isbuffer || Object.prototype.toString.call(target) !== '[object Object]') {\n    return target;\n  }\n\n  // safely ensure that the key is\n  // an integer.\n  function getkey(key) {\n    const parsedKey = Number(key);\n    return isNaN(parsedKey) || key.indexOf('.') !== -1 || opts.object ? key : parsedKey;\n  }\n  function addKeys(keyPrefix, recipient, target) {\n    return Object.keys(target).reduce(function (result, key) {\n      result[keyPrefix + delimiter + key] = target[key];\n      return result;\n    }, recipient);\n  }\n  function isEmpty(val) {\n    const type = Object.prototype.toString.call(val);\n    const isArray = type === '[object Array]';\n    const isObject = type === '[object Object]';\n    if (!val) {\n      return true;\n    } else if (isArray) {\n      return !val.length;\n    } else if (isObject) {\n      return !Object.keys(val).length;\n    }\n  }\n  target = Object.keys(target).reduce(function (result, key) {\n    const type = Object.prototype.toString.call(target[key]);\n    const isObject = type === '[object Object]' || type === '[object Array]';\n    if (!isObject || isEmpty(target[key])) {\n      result[key] = target[key];\n      return result;\n    } else {\n      return addKeys(key, result, flatten(target[key], opts));\n    }\n  }, {});\n  Object.keys(target).forEach(function (key) {\n    const split = key.split(delimiter).map(transformKey);\n    let key1 = getkey(split.shift());\n    let key2 = getkey(split[0]);\n    let recipient = result;\n    while (key2 !== undefined) {\n      if (key1 === '__proto__') {\n        return;\n      }\n      const type = Object.prototype.toString.call(recipient[key1]);\n      const isobject = type === '[object Object]' || type === '[object Array]';\n\n      // do not write over falsey, non-undefined values if overwrite is false\n      if (!overwrite && !isobject && typeof recipient[key1] !== 'undefined') {\n        return;\n      }\n      if (overwrite && !isobject || !overwrite && recipient[key1] == null) {\n        recipient[key1] = typeof key2 === 'number' && !opts.object ? [] : {};\n      }\n      recipient = recipient[key1];\n      if (split.length > 0) {\n        key1 = getkey(split.shift());\n        key2 = getkey(split[0]);\n      }\n    }\n\n    // unflatten again for 'messy objects'\n    recipient[key1] = unflatten(target[key], opts);\n  });\n  return result;\n}", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injectable, Injector, Inject, Optional, Component, Input, TemplateRef, DestroyRef, ChangeDetectorRef, ElementRef, ViewContainerRef, Renderer2, Directive, Pipe, NgModule, makeEnvironmentProviders, APP_INITIALIZER } from '@angular/core';\nimport { of, take, from, map, Subject, BehaviorSubject, forkJoin, retry, tap, catchError, shareReplay, switchMap, combineLatest, EMPTY } from 'rxjs';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { unflatten as unflatten$1, flatten as flatten$1 } from 'flat';\nclass DefaultLoader {\n  translations;\n  constructor(translations) {\n    this.translations = translations;\n  }\n  getTranslation(lang) {\n    return of(this.translations.get(lang) || {});\n  }\n}\nconst TRANSLOCO_LOADER = new InjectionToken('TRANSLOCO_LOADER');\nfunction getValue(obj, path) {\n  if (!obj) {\n    return obj;\n  }\n  /* For cases where the key is like: 'general.something.thing' */\n  if (Object.prototype.hasOwnProperty.call(obj, path)) {\n    return obj[path];\n  }\n  return path.split('.').reduce((p, c) => p?.[c], obj);\n}\nfunction setValue(obj, prop, val) {\n  obj = {\n    ...obj\n  };\n  const split = prop.split('.');\n  const lastIndex = split.length - 1;\n  split.reduce((acc, part, index) => {\n    if (index === lastIndex) {\n      acc[part] = val;\n    } else {\n      acc[part] = Array.isArray(acc[part]) ? acc[part].slice() : {\n        ...acc[part]\n      };\n    }\n    return acc && acc[part];\n  }, obj);\n  return obj;\n}\nfunction size(collection) {\n  if (!collection) {\n    return 0;\n  }\n  if (Array.isArray(collection)) {\n    return collection.length;\n  }\n  if (isObject(collection)) {\n    return Object.keys(collection).length;\n  }\n  return collection ? collection.length : 0;\n}\nfunction isEmpty(collection) {\n  return size(collection) === 0;\n}\nfunction isFunction(val) {\n  return typeof val === 'function';\n}\nfunction isString(val) {\n  return typeof val === 'string';\n}\nfunction isNumber(val) {\n  return typeof val === 'number';\n}\nfunction isObject(item) {\n  return !!item && typeof item === 'object' && !Array.isArray(item);\n}\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n/*\n * @example\n *\n * given: path-to-happiness => pathToHappiness\n * given: path_to_happiness => pathToHappiness\n * given: path-to_happiness => pathToHappiness\n *\n */\nfunction toCamelCase(str) {\n  return str.replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => index == 0 ? word.toLowerCase() : word.toUpperCase()).replace(/\\s+|_|-|\\//g, '');\n}\nfunction isBrowser() {\n  return typeof window !== 'undefined';\n}\nfunction isNil(value) {\n  return value === null || value === undefined;\n}\nfunction isDefined(value) {\n  return isNil(value) === false;\n}\nfunction toNumber(value) {\n  if (isNumber(value)) return value;\n  if (isString(value) && !isNaN(Number(value) - parseFloat(value))) {\n    return Number(value);\n  }\n  return null;\n}\nfunction isScopeObject(item) {\n  return item && typeof item.scope === 'string';\n}\nfunction hasInlineLoader(item) {\n  return item && isObject(item.loader);\n}\nfunction unflatten(obj) {\n  return unflatten$1(obj);\n}\nfunction flatten(obj) {\n  return flatten$1(obj, {\n    safe: true\n  });\n}\nconst TRANSLOCO_CONFIG = new InjectionToken('TRANSLOCO_CONFIG', {\n  providedIn: 'root',\n  factory: () => defaultConfig\n});\nconst defaultConfig = {\n  defaultLang: 'en',\n  reRenderOnLangChange: false,\n  prodMode: false,\n  failedRetries: 2,\n  fallbackLang: [],\n  availableLangs: [],\n  missingHandler: {\n    logMissingKey: true,\n    useFallbackTranslation: false,\n    allowEmpty: false\n  },\n  flatten: {\n    aot: false\n  },\n  interpolation: ['{{', '}}']\n};\nfunction translocoConfig(config = {}) {\n  return {\n    ...defaultConfig,\n    ...config,\n    missingHandler: {\n      ...defaultConfig.missingHandler,\n      ...config.missingHandler\n    },\n    flatten: {\n      ...defaultConfig.flatten,\n      ...config.flatten\n    }\n  };\n}\nconst TRANSLOCO_TRANSPILER = new InjectionToken('TRANSLOCO_TRANSPILER');\nclass DefaultTranspiler {\n  config = inject(TRANSLOCO_CONFIG, {\n    optional: true\n  }) ?? defaultConfig;\n  get interpolationMatcher() {\n    return resolveMatcher(this.config);\n  }\n  transpile({\n    value,\n    params = {},\n    translation,\n    key\n  }) {\n    if (isString(value)) {\n      let paramMatch;\n      let parsedValue = value;\n      while ((paramMatch = this.interpolationMatcher.exec(parsedValue)) !== null) {\n        const [match, paramValue] = paramMatch;\n        parsedValue = parsedValue.replace(match, () => {\n          const match = paramValue.trim();\n          const param = getValue(params, match);\n          if (isDefined(param)) {\n            return param;\n          }\n          return isDefined(translation[match]) ? this.transpile({\n            params,\n            translation,\n            key,\n            value: translation[match]\n          }) : '';\n        });\n      }\n      return parsedValue;\n    } else if (params) {\n      if (isObject(value)) {\n        value = this.handleObject({\n          value: value,\n          params,\n          translation,\n          key\n        });\n      } else if (Array.isArray(value)) {\n        value = this.handleArray({\n          value,\n          params,\n          translation,\n          key\n        });\n      }\n    }\n    return value;\n  }\n  /**\n   *\n   * @example\n   *\n   * const en = {\n   *  a: {\n   *    b: {\n   *      c: \"Hello {{ value }}\"\n   *    }\n   *  }\n   * }\n   *\n   * const params =  {\n   *  \"b.c\": { value: \"Transloco \"}\n   * }\n   *\n   * service.selectTranslate('a', params);\n   *\n   * // the first param will be the result of `en.a`.\n   * // the second param will be `params`.\n   * parser.transpile(value, params, {});\n   *\n   *\n   */\n  handleObject({\n    value,\n    params = {},\n    translation,\n    key\n  }) {\n    let result = value;\n    Object.keys(params).forEach(p => {\n      // transpile the value => \"Hello Transloco\"\n      const transpiled = this.transpile({\n        // get the value of \"b.c\" inside \"a\" => \"Hello {{ value }}\"\n        value: getValue(result, p),\n        // get the params of \"b.c\" => { value: \"Transloco\" }\n        params: getValue(params, p),\n        translation,\n        key\n      });\n      // set \"b.c\" to `transpiled`\n      result = setValue(result, p, transpiled);\n    });\n    return result;\n  }\n  handleArray({\n    value,\n    ...rest\n  }) {\n    return value.map(v => this.transpile({\n      value: v,\n      ...rest\n    }));\n  }\n  static ɵfac = function DefaultTranspiler_Factory(t) {\n    return new (t || DefaultTranspiler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultTranspiler,\n    factory: DefaultTranspiler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultTranspiler, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction resolveMatcher(config) {\n  const [start, end] = config.interpolation;\n  return new RegExp(`${start}([^${start}${end}]*?)${end}`, 'g');\n}\nfunction getFunctionArgs(argsString) {\n  const splitted = argsString ? argsString.split(',') : [];\n  const args = [];\n  for (let i = 0; i < splitted.length; i++) {\n    let value = splitted[i].trim();\n    while (value[value.length - 1] === '\\\\') {\n      i++;\n      value = value.replace('\\\\', ',') + splitted[i];\n    }\n    args.push(value);\n  }\n  return args;\n}\nclass FunctionalTranspiler extends DefaultTranspiler {\n  injector = inject(Injector);\n  transpile({\n    value,\n    ...rest\n  }) {\n    let transpiled = value;\n    if (isString(value)) {\n      transpiled = value.replace(/\\[\\[\\s*(\\w+)\\((.*?)\\)\\s*]]/g, (match, functionName, args) => {\n        try {\n          const func = this.injector.get(functionName);\n          return func.transpile(...getFunctionArgs(args));\n        } catch (e) {\n          let message = `There is an error in: '${value}'. \n                          Check that the you used the right syntax in your translation and that the implementation of ${functionName} is correct.`;\n          if (e.message.includes('NullInjectorError')) {\n            message = `You are using the '${functionName}' function in your translation but no provider was found!`;\n          }\n          throw new Error(message);\n        }\n      });\n    }\n    return super.transpile({\n      value: transpiled,\n      ...rest\n    });\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFunctionalTranspiler_BaseFactory;\n    return function FunctionalTranspiler_Factory(t) {\n      return (ɵFunctionalTranspiler_BaseFactory || (ɵFunctionalTranspiler_BaseFactory = i0.ɵɵgetInheritedFactory(FunctionalTranspiler)))(t || FunctionalTranspiler);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FunctionalTranspiler,\n    factory: FunctionalTranspiler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FunctionalTranspiler, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_MISSING_HANDLER = new InjectionToken('TRANSLOCO_MISSING_HANDLER');\nclass DefaultMissingHandler {\n  handle(key, config) {\n    if (config.missingHandler.logMissingKey && !config.prodMode) {\n      const msg = `Missing translation for '${key}'`;\n      console.warn(`%c ${msg}`, 'font-size: 12px; color: red');\n    }\n    return key;\n  }\n  static ɵfac = function DefaultMissingHandler_Factory(t) {\n    return new (t || DefaultMissingHandler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultMissingHandler,\n    factory: DefaultMissingHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultMissingHandler, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_INTERCEPTOR = new InjectionToken('TRANSLOCO_INTERCEPTOR');\nclass DefaultInterceptor {\n  preSaveTranslation(translation) {\n    return translation;\n  }\n  preSaveTranslationKey(_, value) {\n    return value;\n  }\n  static ɵfac = function DefaultInterceptor_Factory(t) {\n    return new (t || DefaultInterceptor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultInterceptor,\n    factory: DefaultInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultInterceptor, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_FALLBACK_STRATEGY = new InjectionToken('TRANSLOCO_FALLBACK_STRATEGY');\nclass DefaultFallbackStrategy {\n  userConfig;\n  constructor(userConfig) {\n    this.userConfig = userConfig;\n  }\n  getNextLangs() {\n    const fallbackLang = this.userConfig.fallbackLang;\n    if (!fallbackLang) {\n      throw new Error('When using the default fallback, a fallback language must be provided in the config!');\n    }\n    return Array.isArray(fallbackLang) ? fallbackLang : [fallbackLang];\n  }\n  static ɵfac = function DefaultFallbackStrategy_Factory(t) {\n    return new (t || DefaultFallbackStrategy)(i0.ɵɵinject(TRANSLOCO_CONFIG));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultFallbackStrategy,\n    factory: DefaultFallbackStrategy.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultFallbackStrategy, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_CONFIG]\n    }]\n  }], null);\n})();\n\n/*\n * @example\n *\n * given: lazy-page/en => lazy-page\n *\n */\nfunction getScopeFromLang(lang) {\n  if (!lang) {\n    return '';\n  }\n  const split = lang.split('/');\n  split.pop();\n  return split.join('/');\n}\n/*\n * @example\n *\n * given: lazy-page/en => en\n *\n */\nfunction getLangFromScope(lang) {\n  if (!lang) {\n    return '';\n  }\n  return lang.split('/').pop();\n}\n/**\n * @example\n *\n * getPipeValue('todos|scoped', 'scoped') [true, 'todos']\n * getPipeValue('en|static', 'static') [true, 'en']\n * getPipeValue('en', 'static') [false, 'en']\n */\nfunction getPipeValue(str, value, char = '|') {\n  if (isString(str)) {\n    const splitted = str.split(char);\n    const lastItem = splitted.pop();\n    return lastItem === value ? [true, splitted.toString()] : [false, lastItem];\n  }\n  return [false, ''];\n}\nfunction shouldListenToLangChanges(service, lang) {\n  const [hasStatic] = getPipeValue(lang, 'static');\n  if (!hasStatic) {\n    // If we didn't get 'lang|static' check if it's set in the global level\n    return !!service.config.reRenderOnLangChange;\n  }\n  // We have 'lang|static' so don't listen to lang changes\n  return false;\n}\nfunction listenOrNotOperator(listenToLangChange) {\n  return listenToLangChange ? source => source : take(1);\n}\nfunction prependScope(inlineLoader, scope) {\n  return Object.keys(inlineLoader).reduce((acc, lang) => {\n    acc[`${scope}/${lang}`] = inlineLoader[lang];\n    return acc;\n  }, {});\n}\nfunction resolveInlineLoader(providerScope, scope) {\n  return hasInlineLoader(providerScope) ? prependScope(providerScope.loader, scope) : undefined;\n}\nfunction getEventPayload(lang) {\n  return {\n    scope: getScopeFromLang(lang) || null,\n    langName: getLangFromScope(lang)\n  };\n}\nfunction resolveLoader(options) {\n  const {\n    path,\n    inlineLoader,\n    mainLoader,\n    data\n  } = options;\n  if (inlineLoader) {\n    const pathLoader = inlineLoader[path];\n    if (isFunction(pathLoader) === false) {\n      throw `You're using an inline loader but didn't provide a loader for ${path}`;\n    }\n    return inlineLoader[path]().then(res => res.default ? res.default : res);\n  }\n  return mainLoader.getTranslation(path, data);\n}\nfunction getFallbacksLoaders({\n  mainLoader,\n  path,\n  data,\n  fallbackPath,\n  inlineLoader\n}) {\n  const paths = fallbackPath ? [path, fallbackPath] : [path];\n  return paths.map(path => {\n    const loader = resolveLoader({\n      path,\n      mainLoader,\n      inlineLoader,\n      data\n    });\n    return from(loader).pipe(map(translation => ({\n      translation,\n      lang: path\n    })));\n  });\n}\nlet service;\nfunction translate(key, params = {}, lang) {\n  return service.translate(key, params, lang);\n}\nfunction translateObject(key, params = {}, lang) {\n  return service.translateObject(key, params, lang);\n}\nclass TranslocoService {\n  loader;\n  parser;\n  missingHandler;\n  interceptor;\n  fallbackStrategy;\n  langChanges$;\n  translations = new Map();\n  cache = new Map();\n  firstFallbackLang;\n  defaultLang = '';\n  availableLangs = [];\n  isResolvedMissingOnce = false;\n  lang;\n  failedLangs = new Set();\n  events = new Subject();\n  events$ = this.events.asObservable();\n  config;\n  constructor(loader, parser, missingHandler, interceptor, userConfig, fallbackStrategy) {\n    this.loader = loader;\n    this.parser = parser;\n    this.missingHandler = missingHandler;\n    this.interceptor = interceptor;\n    this.fallbackStrategy = fallbackStrategy;\n    if (!this.loader) {\n      this.loader = new DefaultLoader(this.translations);\n    }\n    service = this;\n    this.config = JSON.parse(JSON.stringify(userConfig));\n    this.setAvailableLangs(this.config.availableLangs || []);\n    this.setFallbackLangForMissingTranslation(this.config);\n    this.setDefaultLang(this.config.defaultLang);\n    this.lang = new BehaviorSubject(this.getDefaultLang());\n    // Don't use distinctUntilChanged as we need the ability to update\n    // the value when using setTranslation or setTranslationKeys\n    this.langChanges$ = this.lang.asObservable();\n    /**\n     * When we have a failure, we want to define the next language that succeeded as the active\n     */\n    this.events$.pipe(takeUntilDestroyed()).subscribe(e => {\n      if (e.type === 'translationLoadSuccess' && e.wasFailure) {\n        this.setActiveLang(e.payload.langName);\n      }\n    });\n  }\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n  setDefaultLang(lang) {\n    this.defaultLang = lang;\n  }\n  getActiveLang() {\n    return this.lang.getValue();\n  }\n  setActiveLang(lang) {\n    this.parser.onLangChanged?.(lang);\n    this.lang.next(lang);\n    this.events.next({\n      type: 'langChanged',\n      payload: getEventPayload(lang)\n    });\n    return this;\n  }\n  setAvailableLangs(langs) {\n    this.availableLangs = langs;\n  }\n  /**\n   * Gets the available languages.\n   *\n   * @returns\n   * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`\n   * depending on how the available languages are set in your module.\n   */\n  getAvailableLangs() {\n    return this.availableLangs;\n  }\n  load(path, options = {}) {\n    const cached = this.cache.get(path);\n    if (cached) {\n      return cached;\n    }\n    let loadTranslation;\n    const isScope = this._isLangScoped(path);\n    let scope;\n    if (isScope) {\n      scope = getScopeFromLang(path);\n    }\n    const loadersOptions = {\n      path,\n      mainLoader: this.loader,\n      inlineLoader: options.inlineLoader,\n      data: isScope ? {\n        scope: scope\n      } : undefined\n    };\n    if (this.useFallbackTranslation(path)) {\n      // if the path is scope the fallback should be `scope/fallbackLang`;\n      const fallback = isScope ? `${scope}/${this.firstFallbackLang}` : this.firstFallbackLang;\n      const loaders = getFallbacksLoaders({\n        ...loadersOptions,\n        fallbackPath: fallback\n      });\n      loadTranslation = forkJoin(loaders);\n    } else {\n      const loader = resolveLoader(loadersOptions);\n      loadTranslation = from(loader);\n    }\n    const load$ = loadTranslation.pipe(retry(this.config.failedRetries), tap(translation => {\n      if (Array.isArray(translation)) {\n        translation.forEach(t => {\n          this.handleSuccess(t.lang, t.translation);\n          // Save the fallback in cache so we'll not create a redundant request\n          if (t.lang !== path) {\n            this.cache.set(t.lang, of({}));\n          }\n        });\n        return;\n      }\n      this.handleSuccess(path, translation);\n    }), catchError(error => {\n      if (!this.config.prodMode) {\n        console.error(`Error while trying to load \"${path}\"`, error);\n      }\n      return this.handleFailure(path, options);\n    }), shareReplay(1));\n    this.cache.set(path, load$);\n    return load$;\n  }\n  /**\n   * Gets the instant translated value of a key\n   *\n   * @example\n   *\n   * translate<string>('hello')\n   * translate('hello', { value: 'value' })\n   * translate<string[]>(['hello', 'key'])\n   * translate('hello', { }, 'en')\n   * translate('scope.someKey', { }, 'en')\n   */\n  translate(key, params = {}, lang = this.getActiveLang()) {\n    if (!key) return key;\n    const {\n      scope,\n      resolveLang\n    } = this.resolveLangAndScope(lang);\n    if (Array.isArray(key)) {\n      return key.map(k => this.translate(scope ? `${scope}.${k}` : k, params, resolveLang));\n    }\n    key = scope ? `${scope}.${key}` : key;\n    const translation = this.getTranslation(resolveLang);\n    const value = translation[key];\n    if (!value) {\n      return this._handleMissingKey(key, value, params);\n    }\n    return this.parser.transpile({\n      value,\n      params,\n      translation,\n      key\n    });\n  }\n  /**\n   * Gets the translated value of a key as observable\n   *\n   * @example\n   *\n   * selectTranslate<string>('hello').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)\n   *\n   */\n  selectTranslate(key, params, lang, _isObject = false) {\n    let inlineLoader;\n    const load = (lang, options) => this.load(lang, options).pipe(map(() => _isObject ? this.translateObject(key, params, lang) : this.translate(key, params, lang)));\n    if (isNil(lang)) {\n      return this.langChanges$.pipe(switchMap(lang => load(lang)));\n    }\n    lang = Array.isArray(lang) ? lang[0] : lang;\n    if (isScopeObject(lang)) {\n      // it's a scope object.\n      const providerScope = lang;\n      lang = providerScope.scope;\n      inlineLoader = resolveInlineLoader(providerScope, providerScope.scope);\n    }\n    lang = lang;\n    if (this.isLang(lang) || this.isScopeWithLang(lang)) {\n      return load(lang);\n    }\n    // it's a scope\n    const scope = lang;\n    return this.langChanges$.pipe(switchMap(lang => load(`${scope}/${lang}`, {\n      inlineLoader\n    })));\n  }\n  /**\n   * Whether the scope with lang\n   *\n   * @example\n   *\n   * todos/en => true\n   * todos => false\n   */\n  isScopeWithLang(lang) {\n    return this.isLang(getLangFromScope(lang));\n  }\n  translateObject(key, params = {}, lang = this.getActiveLang()) {\n    if (isString(key) || Array.isArray(key)) {\n      const {\n        resolveLang,\n        scope\n      } = this.resolveLangAndScope(lang);\n      if (Array.isArray(key)) {\n        return key.map(k => this.translateObject(scope ? `${scope}.${k}` : k, params, resolveLang));\n      }\n      const translation = this.getTranslation(resolveLang);\n      key = scope ? `${scope}.${key}` : key;\n      const value = unflatten(this.getObjectByKey(translation, key));\n      /* If an empty object was returned we want to try and translate the key as a string and not an object */\n      return isEmpty(value) ? this.translate(key, params, lang) : this.parser.transpile({\n        value,\n        params: params,\n        translation,\n        key\n      });\n    }\n    const translations = [];\n    for (const [_key, _params] of this.getEntries(key)) {\n      translations.push(this.translateObject(_key, _params, lang));\n    }\n    return translations;\n  }\n  selectTranslateObject(key, params, lang) {\n    if (isString(key) || Array.isArray(key)) {\n      return this.selectTranslate(key, params, lang, true);\n    }\n    const [[firstKey, firstParams], ...rest] = this.getEntries(key);\n    /* In order to avoid subscribing multiple times to the load language event by calling selectTranslateObject for each pair,\n     * we listen to when the first key has been translated (the language is loaded) and translate the rest synchronously */\n    return this.selectTranslateObject(firstKey, firstParams, lang).pipe(map(value => {\n      const translations = [value];\n      for (const [_key, _params] of rest) {\n        translations.push(this.translateObject(_key, _params, lang));\n      }\n      return translations;\n    }));\n  }\n  getTranslation(langOrScope) {\n    if (langOrScope) {\n      if (this.isLang(langOrScope)) {\n        return this.translations.get(langOrScope) || {};\n      } else {\n        // This is a scope, build the scope value from the translation object\n        const {\n          scope,\n          resolveLang\n        } = this.resolveLangAndScope(langOrScope);\n        const translation = this.translations.get(resolveLang) || {};\n        return this.getObjectByKey(translation, scope);\n      }\n    }\n    return this.translations;\n  }\n  /**\n   * Gets an object of translations for a given language\n   *\n   * @example\n   *\n   * selectTranslation().subscribe() - will return the current lang translation\n   * selectTranslation('es').subscribe()\n   * selectTranslation('admin-page').subscribe() - will return the current lang scope translation\n   * selectTranslation('admin-page/es').subscribe()\n   */\n  selectTranslation(lang) {\n    let language$ = this.langChanges$;\n    if (lang) {\n      const scopeLangSpecified = getLangFromScope(lang) !== lang;\n      if (this.isLang(lang) || scopeLangSpecified) {\n        language$ = of(lang);\n      } else {\n        language$ = this.langChanges$.pipe(map(currentLang => `${lang}/${currentLang}`));\n      }\n    }\n    return language$.pipe(switchMap(language => this.load(language).pipe(map(() => this.getTranslation(language)))));\n  }\n  /**\n   * Sets or merge a given translation object to current lang\n   *\n   * @example\n   *\n   * setTranslation({ ... })\n   * setTranslation({ ... }, 'en')\n   * setTranslation({ ... }, 'es', { merge: false } )\n   * setTranslation({ ... }, 'todos/en', { merge: false } )\n   */\n  setTranslation(translation, lang = this.getActiveLang(), options = {}) {\n    const defaults = {\n      merge: true,\n      emitChange: true\n    };\n    const mergedOptions = {\n      ...defaults,\n      ...options\n    };\n    const scope = getScopeFromLang(lang);\n    /**\n     * If this isn't a scope we use the whole translation as is\n     * otherwise we need to flat the scope and use it\n     */\n    let flattenScopeOrTranslation = translation;\n    // Merged the scoped language into the active language\n    if (scope) {\n      const key = this.getMappedScope(scope);\n      flattenScopeOrTranslation = flatten({\n        [key]: translation\n      });\n    }\n    const currentLang = scope ? getLangFromScope(lang) : lang;\n    const mergedTranslation = {\n      ...(mergedOptions.merge && this.getTranslation(currentLang)),\n      ...flattenScopeOrTranslation\n    };\n    const flattenTranslation = this.config.flatten.aot ? mergedTranslation : flatten(mergedTranslation);\n    const withHook = this.interceptor.preSaveTranslation(flattenTranslation, currentLang);\n    this.translations.set(currentLang, withHook);\n    mergedOptions.emitChange && this.setActiveLang(this.getActiveLang());\n  }\n  /**\n   * Sets translation key with given value\n   *\n   * @example\n   *\n   * setTranslationKey('key', 'value')\n   * setTranslationKey('key.nested', 'value')\n   * setTranslationKey('key.nested', 'value', 'en')\n   * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )\n   */\n  setTranslationKey(key, value, options = {}) {\n    const lang = options.lang || this.getActiveLang();\n    const withHook = this.interceptor.preSaveTranslationKey(key, value, lang);\n    const newValue = {\n      [key]: withHook\n    };\n    this.setTranslation(newValue, lang, {\n      ...options,\n      merge: true\n    });\n  }\n  /**\n   * Sets the fallback lang for the currently active language\n   * @param fallbackLang\n   */\n  setFallbackLangForMissingTranslation({\n    fallbackLang\n  }) {\n    const lang = Array.isArray(fallbackLang) ? fallbackLang[0] : fallbackLang;\n    if (fallbackLang && this.useFallbackTranslation(lang)) {\n      this.firstFallbackLang = lang;\n    }\n  }\n  /**\n   * @internal\n   */\n  _handleMissingKey(key, value, params) {\n    if (this.config.missingHandler.allowEmpty && value === '') {\n      return '';\n    }\n    if (!this.isResolvedMissingOnce && this.useFallbackTranslation()) {\n      // We need to set it to true to prevent a loop\n      this.isResolvedMissingOnce = true;\n      const fallbackValue = this.translate(key, params, this.firstFallbackLang);\n      this.isResolvedMissingOnce = false;\n      return fallbackValue;\n    }\n    return this.missingHandler.handle(key, this.getMissingHandlerData(), params);\n  }\n  /**\n   * @internal\n   */\n  _isLangScoped(lang) {\n    return this.getAvailableLangsIds().indexOf(lang) === -1;\n  }\n  /**\n   * Checks if a given string is one of the specified available languages.\n   * @returns\n   * True if the given string is an available language.\n   * False if the given string is not an available language.\n   */\n  isLang(lang) {\n    return this.getAvailableLangsIds().indexOf(lang) !== -1;\n  }\n  /**\n   * @internal\n   *\n   * We always want to make sure the global lang is loaded\n   * before loading the scope since you can access both via the pipe/directive.\n   */\n  _loadDependencies(path, inlineLoader) {\n    const mainLang = getLangFromScope(path);\n    if (this._isLangScoped(path) && !this.isLoadedTranslation(mainLang)) {\n      return combineLatest([this.load(mainLang), this.load(path, {\n        inlineLoader\n      })]);\n    }\n    return this.load(path, {\n      inlineLoader\n    });\n  }\n  /**\n   * @internal\n   */\n  _completeScopeWithLang(langOrScope) {\n    if (this._isLangScoped(langOrScope) && !this.isLang(getLangFromScope(langOrScope))) {\n      return `${langOrScope}/${this.getActiveLang()}`;\n    }\n    return langOrScope;\n  }\n  /**\n   * @internal\n   */\n  _setScopeAlias(scope, alias) {\n    if (!this.config.scopeMapping) {\n      this.config.scopeMapping = {};\n    }\n    this.config.scopeMapping[scope] = alias;\n  }\n  ngOnDestroy() {\n    // Caretaker note: since this is the root provider, it'll be destroyed when the `NgModuleRef.destroy()` is run.\n    // Cached values capture `this`, thus leading to a circular reference and preventing the `TranslocoService` from\n    // being GC'd. This would lead to a memory leak when server-side rendering is used since the service is created\n    // and destroyed per each HTTP request, but any service is not getting GC'd.\n    this.cache.clear();\n  }\n  isLoadedTranslation(lang) {\n    return size(this.getTranslation(lang));\n  }\n  getAvailableLangsIds() {\n    const first = this.getAvailableLangs()[0];\n    if (isString(first)) {\n      return this.getAvailableLangs();\n    }\n    return this.getAvailableLangs().map(l => l.id);\n  }\n  getMissingHandlerData() {\n    return {\n      ...this.config,\n      activeLang: this.getActiveLang(),\n      availableLangs: this.availableLangs,\n      defaultLang: this.defaultLang\n    };\n  }\n  /**\n   * Use a fallback translation set for missing keys of the primary language\n   * This is unrelated to the fallback language (which changes the active language)\n   */\n  useFallbackTranslation(lang) {\n    return this.config.missingHandler.useFallbackTranslation && lang !== this.firstFallbackLang;\n  }\n  handleSuccess(lang, translation) {\n    this.setTranslation(translation, lang, {\n      emitChange: false\n    });\n    this.events.next({\n      wasFailure: !!this.failedLangs.size,\n      type: 'translationLoadSuccess',\n      payload: getEventPayload(lang)\n    });\n    this.failedLangs.forEach(l => this.cache.delete(l));\n    this.failedLangs.clear();\n  }\n  handleFailure(lang, loadOptions) {\n    // When starting to load a first choice language, initialize\n    // the failed counter and resolve the fallback langs.\n    if (isNil(loadOptions.failedCounter)) {\n      loadOptions.failedCounter = 0;\n      if (!loadOptions.fallbackLangs) {\n        loadOptions.fallbackLangs = this.fallbackStrategy.getNextLangs(lang);\n      }\n    }\n    const splitted = lang.split('/');\n    const fallbacks = loadOptions.fallbackLangs;\n    const nextLang = fallbacks[loadOptions.failedCounter];\n    this.failedLangs.add(lang);\n    // This handles the case where a loaded fallback language is requested again\n    if (this.cache.has(nextLang)) {\n      this.handleSuccess(nextLang, this.getTranslation(nextLang));\n      return EMPTY;\n    }\n    const isFallbackLang = nextLang === splitted[splitted.length - 1];\n    if (!nextLang || isFallbackLang) {\n      let msg = `Unable to load translation and all the fallback languages`;\n      if (splitted.length > 1) {\n        msg += `, did you misspelled the scope name?`;\n      }\n      throw new Error(msg);\n    }\n    let resolveLang = nextLang;\n    // if it's scoped lang\n    if (splitted.length > 1) {\n      // We need to resolve it to:\n      // todos/langNotExists => todos/nextLang\n      splitted[splitted.length - 1] = nextLang;\n      resolveLang = splitted.join('/');\n    }\n    loadOptions.failedCounter++;\n    this.events.next({\n      type: 'translationLoadFailure',\n      payload: getEventPayload(lang)\n    });\n    return this.load(resolveLang, loadOptions);\n  }\n  getMappedScope(scope) {\n    const {\n      scopeMapping = {}\n    } = this.config;\n    return scopeMapping[scope] || toCamelCase(scope);\n  }\n  /**\n   * If lang is scope we need to check the following cases:\n   * todos/es => in this case we should take `es` as lang\n   * todos => in this case we should set the active lang as lang\n   */\n  resolveLangAndScope(lang) {\n    let resolveLang = lang;\n    let scope;\n    if (this._isLangScoped(lang)) {\n      // en for example\n      const langFromScope = getLangFromScope(lang);\n      // en is lang\n      const hasLang = this.isLang(langFromScope);\n      // take en\n      resolveLang = hasLang ? langFromScope : this.getActiveLang();\n      // find the scope\n      scope = this.getMappedScope(hasLang ? getScopeFromLang(lang) : lang);\n    }\n    return {\n      scope,\n      resolveLang\n    };\n  }\n  getObjectByKey(translation, key) {\n    const result = {};\n    const prefix = `${key}.`;\n    for (const currentKey in translation) {\n      if (currentKey.startsWith(prefix)) {\n        result[currentKey.replace(prefix, '')] = translation[currentKey];\n      }\n    }\n    return result;\n  }\n  getEntries(key) {\n    return key instanceof Map ? key.entries() : Object.entries(key);\n  }\n  static ɵfac = function TranslocoService_Factory(t) {\n    return new (t || TranslocoService)(i0.ɵɵinject(TRANSLOCO_LOADER, 8), i0.ɵɵinject(TRANSLOCO_TRANSPILER), i0.ɵɵinject(TRANSLOCO_MISSING_HANDLER), i0.ɵɵinject(TRANSLOCO_INTERCEPTOR), i0.ɵɵinject(TRANSLOCO_CONFIG), i0.ɵɵinject(TRANSLOCO_FALLBACK_STRATEGY));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslocoService,\n    factory: TranslocoService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_LOADER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_TRANSPILER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_MISSING_HANDLER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_INTERCEPTOR]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_CONFIG]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_FALLBACK_STRATEGY]\n    }]\n  }], null);\n})();\nclass TranslocoLoaderComponent {\n  html;\n  static ɵfac = function TranslocoLoaderComponent_Factory(t) {\n    return new (t || TranslocoLoaderComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TranslocoLoaderComponent,\n    selectors: [[\"ng-component\"]],\n    inputs: {\n      html: \"html\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[1, \"transloco-loader-template\", 3, \"innerHTML\"]],\n    template: function TranslocoLoaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"innerHTML\", ctx.html, i0.ɵɵsanitizeHtml);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoLoaderComponent, [{\n    type: Component,\n    args: [{\n      template: `\n    <div class=\"transloco-loader-template\" [innerHTML]=\"html\"></div>\n  `,\n      standalone: true\n    }]\n  }], null, {\n    html: [{\n      type: Input\n    }]\n  });\n})();\nclass TemplateHandler {\n  view;\n  vcr;\n  constructor(view, vcr) {\n    this.view = view;\n    this.vcr = vcr;\n  }\n  attachView() {\n    if (this.view instanceof TemplateRef) {\n      this.vcr.createEmbeddedView(this.view);\n    } else if (isString(this.view)) {\n      const componentRef = this.vcr.createComponent(TranslocoLoaderComponent);\n      componentRef.instance.html = this.view;\n      componentRef.hostView.detectChanges();\n    } else {\n      this.vcr.createComponent(this.view);\n    }\n  }\n  detachView() {\n    this.vcr.clear();\n  }\n}\nconst TRANSLOCO_LANG = new InjectionToken('TRANSLOCO_LANG');\nconst TRANSLOCO_LOADING_TEMPLATE = new InjectionToken('TRANSLOCO_LOADING_TEMPLATE');\nconst TRANSLOCO_SCOPE = new InjectionToken('TRANSLOCO_SCOPE');\nclass LangResolver {\n  initialized = false;\n  // inline => provider => active\n  resolve({\n    inline,\n    provider,\n    active\n  }) {\n    let lang = active;\n    /**\n     * When the user changes the lang we need to update\n     * the view. Otherwise, the lang will remain the inline/provided lang\n     */\n    if (this.initialized) {\n      lang = active;\n      return lang;\n    }\n    if (provider) {\n      const [, extracted] = getPipeValue(provider, 'static');\n      lang = extracted;\n    }\n    if (inline) {\n      const [, extracted] = getPipeValue(inline, 'static');\n      lang = extracted;\n    }\n    this.initialized = true;\n    return lang;\n  }\n  /**\n   *\n   * Resolve the lang\n   *\n   * @example\n   *\n   * resolveLangBasedOnScope('todos/en') => en\n   * resolveLangBasedOnScope('en') => en\n   *\n   */\n  resolveLangBasedOnScope(lang) {\n    const scope = getScopeFromLang(lang);\n    return scope ? getLangFromScope(lang) : lang;\n  }\n  /**\n   *\n   * Resolve the lang path for loading\n   *\n   * @example\n   *\n   * resolveLangPath('todos', 'en') => todos/en\n   * resolveLangPath('en') => en\n   *\n   */\n  resolveLangPath(lang, scope) {\n    return scope ? `${scope}/${lang}` : lang;\n  }\n}\nclass ScopeResolver {\n  service;\n  constructor(service) {\n    this.service = service;\n  }\n  // inline => provider\n  resolve(params) {\n    const {\n      inline,\n      provider\n    } = params;\n    if (inline) {\n      return inline;\n    }\n    if (provider) {\n      if (isScopeObject(provider)) {\n        const {\n          scope,\n          alias = toCamelCase(scope)\n        } = provider;\n        this.service._setScopeAlias(scope, alias);\n        return scope;\n      }\n      return provider;\n    }\n    return undefined;\n  }\n}\nclass TranslocoDirective {\n  destroyRef = inject(DestroyRef);\n  service = inject(TranslocoService);\n  tpl = inject(TemplateRef, {\n    optional: true\n  });\n  providerLang = inject(TRANSLOCO_LANG, {\n    optional: true\n  });\n  providerScope = inject(TRANSLOCO_SCOPE, {\n    optional: true\n  });\n  providedLoadingTpl = inject(TRANSLOCO_LOADING_TEMPLATE, {\n    optional: true\n  });\n  cdr = inject(ChangeDetectorRef);\n  host = inject(ElementRef);\n  vcr = inject(ViewContainerRef);\n  renderer = inject(Renderer2);\n  view;\n  memo = new Map();\n  key;\n  params = {};\n  inlineScope;\n  /** @deprecated use prefix instead, will be removed in Transloco v8 */\n  inlineRead;\n  prefix;\n  inlineLang;\n  inlineTpl;\n  currentLang;\n  loaderTplHandler;\n  // Whether we already rendered the view once\n  initialized = false;\n  path;\n  langResolver = new LangResolver();\n  scopeResolver = new ScopeResolver(this.service);\n  strategy = this.tpl === null ? 'attribute' : 'structural';\n  static ngTemplateContextGuard(dir, ctx) {\n    return true;\n  }\n  ngOnInit() {\n    const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || this.inlineLang);\n    this.service.langChanges$.pipe(switchMap(activeLang => {\n      const lang = this.langResolver.resolve({\n        inline: this.inlineLang,\n        provider: this.providerLang,\n        active: activeLang\n      });\n      return Array.isArray(this.providerScope) ? forkJoin(this.providerScope.map(providerScope => this.resolveScope(lang, providerScope))) : this.resolveScope(lang, this.providerScope);\n    }), listenOrNotOperator(listenToLangChange), takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n      this.currentLang = this.langResolver.resolveLangBasedOnScope(this.path);\n      this.strategy === 'attribute' ? this.attributeStrategy() : this.structuralStrategy(this.currentLang, this.prefix || this.inlineRead);\n      this.cdr.markForCheck();\n      this.initialized = true;\n    });\n    if (!this.initialized) {\n      const loadingContent = this.resolveLoadingContent();\n      if (loadingContent) {\n        this.loaderTplHandler = new TemplateHandler(loadingContent, this.vcr);\n        this.loaderTplHandler.attachView();\n      }\n    }\n  }\n  ngOnChanges(changes) {\n    // We need to support dynamic keys/params, so if this is not the first change CD cycle\n    // we need to run the function again in order to update the value\n    if (this.strategy === 'attribute') {\n      const notInit = Object.keys(changes).some(v => !changes[v].firstChange);\n      notInit && this.attributeStrategy();\n    }\n  }\n  attributeStrategy() {\n    this.detachLoader();\n    this.renderer.setProperty(this.host.nativeElement, 'innerText', this.service.translate(this.key, this.params, this.currentLang));\n  }\n  structuralStrategy(lang, prefix) {\n    this.memo.clear();\n    const translateFn = this.getTranslateFn(lang, prefix);\n    if (this.view) {\n      // when the lang changes we need to change the reference so Angular will update the view\n      this.view.context['$implicit'] = translateFn;\n      this.view.context['currentLang'] = this.currentLang;\n    } else {\n      this.detachLoader();\n      this.view = this.vcr.createEmbeddedView(this.tpl, {\n        $implicit: translateFn,\n        currentLang: this.currentLang\n      });\n    }\n  }\n  getTranslateFn(lang, prefix) {\n    return (key, params) => {\n      const withPrefix = prefix ? `${prefix}.${key}` : key;\n      const memoKey = params ? `${withPrefix}${JSON.stringify(params)}` : withPrefix;\n      if (!this.memo.has(memoKey)) {\n        this.memo.set(memoKey, this.service.translate(withPrefix, params, lang));\n      }\n      return this.memo.get(memoKey);\n    };\n  }\n  resolveLoadingContent() {\n    return this.inlineTpl || this.providedLoadingTpl;\n  }\n  ngOnDestroy() {\n    this.memo.clear();\n  }\n  detachLoader() {\n    this.loaderTplHandler?.detachView();\n  }\n  resolveScope(lang, providerScope) {\n    const resolvedScope = this.scopeResolver.resolve({\n      inline: this.inlineScope,\n      provider: providerScope\n    });\n    this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n    const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n    return this.service._loadDependencies(this.path, inlineLoader);\n  }\n  static ɵfac = function TranslocoDirective_Factory(t) {\n    return new (t || TranslocoDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TranslocoDirective,\n    selectors: [[\"\", \"transloco\", \"\"]],\n    inputs: {\n      key: [0, \"transloco\", \"key\"],\n      params: [0, \"translocoParams\", \"params\"],\n      inlineScope: [0, \"translocoScope\", \"inlineScope\"],\n      inlineRead: [0, \"translocoRead\", \"inlineRead\"],\n      prefix: [0, \"translocoPrefix\", \"prefix\"],\n      inlineLang: [0, \"translocoLang\", \"inlineLang\"],\n      inlineTpl: [0, \"translocoLoadingTpl\", \"inlineTpl\"]\n    },\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[transloco]',\n      standalone: true\n    }]\n  }], null, {\n    key: [{\n      type: Input,\n      args: ['transloco']\n    }],\n    params: [{\n      type: Input,\n      args: ['translocoParams']\n    }],\n    inlineScope: [{\n      type: Input,\n      args: ['translocoScope']\n    }],\n    inlineRead: [{\n      type: Input,\n      args: ['translocoRead']\n    }],\n    prefix: [{\n      type: Input,\n      args: ['translocoPrefix']\n    }],\n    inlineLang: [{\n      type: Input,\n      args: ['translocoLang']\n    }],\n    inlineTpl: [{\n      type: Input,\n      args: ['translocoLoadingTpl']\n    }]\n  });\n})();\nclass TranslocoPipe {\n  service;\n  providerScope;\n  providerLang;\n  cdr;\n  subscription = null;\n  lastValue = '';\n  lastKey;\n  path;\n  langResolver = new LangResolver();\n  scopeResolver;\n  constructor(service, providerScope, providerLang, cdr) {\n    this.service = service;\n    this.providerScope = providerScope;\n    this.providerLang = providerLang;\n    this.cdr = cdr;\n    this.scopeResolver = new ScopeResolver(this.service);\n  }\n  // null is for handling strict mode + async pipe types https://github.com/jsverse/transloco/issues/311\n  // null is for handling strict mode + optional chaining types https://github.com/jsverse/transloco/issues/488\n  transform(key, params, inlineLang) {\n    if (!key) {\n      return key;\n    }\n    const keyName = params ? `${key}${JSON.stringify(params)}` : key;\n    if (keyName === this.lastKey) {\n      return this.lastValue;\n    }\n    this.lastKey = keyName;\n    this.subscription?.unsubscribe();\n    const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || inlineLang);\n    this.subscription = this.service.langChanges$.pipe(switchMap(activeLang => {\n      const lang = this.langResolver.resolve({\n        inline: inlineLang,\n        provider: this.providerLang,\n        active: activeLang\n      });\n      return Array.isArray(this.providerScope) ? forkJoin(this.providerScope.map(providerScope => this.resolveScope(lang, providerScope))) : this.resolveScope(lang, this.providerScope);\n    }), listenOrNotOperator(listenToLangChange)).subscribe(() => this.updateValue(key, params));\n    return this.lastValue;\n  }\n  ngOnDestroy() {\n    this.subscription?.unsubscribe();\n    // Caretaker note: it's important to clean up references to subscriptions since they save the `next`\n    // callback within its `destination` property, preventing classes from being GC'd.\n    this.subscription = null;\n  }\n  updateValue(key, params) {\n    const lang = this.langResolver.resolveLangBasedOnScope(this.path);\n    this.lastValue = this.service.translate(key, params, lang);\n    this.cdr.markForCheck();\n  }\n  resolveScope(lang, providerScope) {\n    const resolvedScope = this.scopeResolver.resolve({\n      inline: undefined,\n      provider: providerScope\n    });\n    this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n    const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n    return this.service._loadDependencies(this.path, inlineLoader);\n  }\n  static ɵfac = function TranslocoPipe_Factory(t) {\n    return new (t || TranslocoPipe)(i0.ɵɵdirectiveInject(TranslocoService, 16), i0.ɵɵdirectiveInject(TRANSLOCO_SCOPE, 24), i0.ɵɵdirectiveInject(TRANSLOCO_LANG, 24), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"transloco\",\n    type: TranslocoPipe,\n    pure: false,\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'transloco',\n      pure: false,\n      standalone: true\n    }]\n  }], () => [{\n    type: TranslocoService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_SCOPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_LANG]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nconst decl = [TranslocoDirective, TranslocoPipe];\nclass TranslocoModule {\n  static ɵfac = function TranslocoModule_Factory(t) {\n    return new (t || TranslocoModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TranslocoModule,\n    imports: [TranslocoDirective, TranslocoPipe],\n    exports: [TranslocoDirective, TranslocoPipe]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoModule, [{\n    type: NgModule,\n    args: [{\n      imports: decl,\n      exports: decl\n    }]\n  }], null, null);\n})();\nfunction provideTransloco(options) {\n  const providers = [provideTranslocoTranspiler(DefaultTranspiler), provideTranslocoMissingHandler(DefaultMissingHandler), provideTranslocoInterceptor(DefaultInterceptor), provideTranslocoFallbackStrategy(DefaultFallbackStrategy)];\n  if (options.config) {\n    providers.push(provideTranslocoConfig(options.config));\n  }\n  if (options.loader) {\n    providers.push(provideTranslocoLoader(options.loader));\n  }\n  return providers;\n}\nfunction provideTranslocoConfig(config) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_CONFIG,\n    useValue: translocoConfig(config)\n  }]);\n}\nfunction provideTranslocoLoader(loader) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_LOADER,\n    useClass: loader\n  }]);\n}\nfunction provideTranslocoScope(...scopes) {\n  return scopes.map(scope => ({\n    provide: TRANSLOCO_SCOPE,\n    useValue: scope,\n    multi: true\n  }));\n}\nfunction provideTranslocoLoadingTpl(content) {\n  return {\n    provide: TRANSLOCO_LOADING_TEMPLATE,\n    useValue: content\n  };\n}\nfunction provideTranslocoTranspiler(transpiler) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_TRANSPILER,\n    useClass: transpiler,\n    deps: [TRANSLOCO_CONFIG]\n  }]);\n}\nfunction provideTranslocoFallbackStrategy(strategy) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_FALLBACK_STRATEGY,\n    useClass: strategy,\n    deps: [TRANSLOCO_CONFIG]\n  }]);\n}\nfunction provideTranslocoMissingHandler(handler) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_MISSING_HANDLER,\n    useClass: handler\n  }]);\n}\nfunction provideTranslocoInterceptor(interceptor) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_INTERCEPTOR,\n    useClass: interceptor\n  }]);\n}\nfunction provideTranslocoLang(lang) {\n  return {\n    provide: TRANSLOCO_LANG,\n    useValue: lang\n  };\n}\nconst TRANSLOCO_TEST_LANGS = new InjectionToken('TRANSLOCO_TEST_LANGS - Available testing languages');\nconst TRANSLOCO_TEST_OPTIONS = new InjectionToken('TRANSLOCO_TEST_OPTIONS - Testing options');\nclass TestingLoader {\n  langs;\n  constructor(langs) {\n    this.langs = langs;\n  }\n  getTranslation(lang) {\n    return of(this.langs[lang]);\n  }\n  static ɵfac = function TestingLoader_Factory(t) {\n    return new (t || TestingLoader)(i0.ɵɵinject(TRANSLOCO_TEST_LANGS));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TestingLoader,\n    factory: TestingLoader.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestingLoader, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_TEST_LANGS]\n    }]\n  }], null);\n})();\nfunction initTranslocoService(service, langs = {}, options) {\n  const preloadAllLangs = () => options.preloadLangs ? Promise.all(Object.keys(langs).map(lang => service.load(lang).toPromise())) : Promise.resolve();\n  return preloadAllLangs;\n}\nclass TranslocoTestingModule {\n  static forRoot(options) {\n    return {\n      ngModule: TranslocoTestingModule,\n      providers: [provideTransloco({\n        loader: TestingLoader,\n        config: {\n          prodMode: true,\n          missingHandler: {\n            logMissingKey: false\n          },\n          ...options.translocoConfig\n        }\n      }), {\n        provide: TRANSLOCO_TEST_LANGS,\n        useValue: options.langs\n      }, {\n        provide: TRANSLOCO_TEST_OPTIONS,\n        useValue: options\n      }, {\n        provide: APP_INITIALIZER,\n        useFactory: initTranslocoService,\n        deps: [TranslocoService, TRANSLOCO_TEST_LANGS, TRANSLOCO_TEST_OPTIONS],\n        multi: true\n      }]\n    };\n  }\n  static ɵfac = function TranslocoTestingModule_Factory(t) {\n    return new (t || TranslocoTestingModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TranslocoTestingModule,\n    exports: [TranslocoModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [TranslocoModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [TranslocoModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Returns the language code name from the browser, e.g. \"en\"\n */\nfunction getBrowserLang() {\n  let browserLang = getBrowserCultureLang();\n  if (!browserLang || !isBrowser()) {\n    return undefined;\n  }\n  if (browserLang.indexOf('-') !== -1) {\n    browserLang = browserLang.split('-')[0];\n  }\n  if (browserLang.indexOf('_') !== -1) {\n    browserLang = browserLang.split('_')[0];\n  }\n  return browserLang;\n}\n/**\n * Returns the culture language code name from the browser, e.g. \"en-US\"\n */\nfunction getBrowserCultureLang() {\n  if (!isBrowser()) {\n    return '';\n  }\n  const navigator = window.navigator;\n  return navigator.languages?.[0] ?? navigator.language;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultFallbackStrategy, DefaultInterceptor, DefaultMissingHandler, DefaultTranspiler, FunctionalTranspiler, TRANSLOCO_CONFIG, TRANSLOCO_FALLBACK_STRATEGY, TRANSLOCO_INTERCEPTOR, TRANSLOCO_LANG, TRANSLOCO_LOADER, TRANSLOCO_LOADING_TEMPLATE, TRANSLOCO_MISSING_HANDLER, TRANSLOCO_SCOPE, TRANSLOCO_TRANSPILER, TestingLoader, TranslocoDirective, TranslocoModule, TranslocoPipe, TranslocoService, TranslocoTestingModule, coerceArray, defaultConfig, flatten, getBrowserCultureLang, getBrowserLang, getFunctionArgs, getLangFromScope, getPipeValue, getScopeFromLang, getValue, hasInlineLoader, isBrowser, isDefined, isEmpty, isFunction, isNil, isNumber, isObject, isScopeObject, isString, provideTransloco, provideTranslocoConfig, provideTranslocoFallbackStrategy, provideTranslocoInterceptor, provideTranslocoLang, provideTranslocoLoader, provideTranslocoLoadingTpl, provideTranslocoMissingHandler, provideTranslocoScope, provideTranslocoTranspiler, setValue, size, toCamelCase, toNumber, translate, translateObject, translocoConfig, unflatten };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,SAAS,mBAAmB,YAAY;AACtC,MAAI,CAAC,YAAY;AACf,6BAAyB,kBAAkB;AAC3C,iBAAa,OAAO,UAAU;AAAA,EAChC;AACA,QAAM,aAAa,IAAI,WAAW,cAAY;AAC5C,UAAM,eAAe,WAAW,UAAU,SAAS,KAAK,KAAK,QAAQ,CAAC;AACtE,WAAO;AAAA,EACT,CAAC;AACD,SAAO,YAAU;AACf,WAAO,OAAO,KAAK,UAAU,UAAU,CAAC;AAAA,EAC1C;AACF;;;AChCA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,IAAI,eAAe,OAAO,IAAI,YAAY,aAAa,cAAc,IAAI,YAAY,SAAS,GAAG;AACjH;AACA,SAAS,YAAY,KAAK;AACxB,SAAO;AACT;AACO,SAAS,QAAQ,QAAQ,MAAM;AACpC,SAAO,QAAQ,CAAC;AAChB,QAAM,YAAY,KAAK,aAAa;AACpC,QAAM,WAAW,KAAK;AACtB,QAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAM,SAAS,CAAC;AAChB,WAAS,KAAK,QAAQ,MAAM,cAAc;AACxC,mBAAe,gBAAgB;AAC/B,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,YAAM,QAAQ,OAAO,GAAG;AACxB,YAAM,UAAU,KAAK,QAAQ,MAAM,QAAQ,KAAK;AAChD,YAAM,OAAO,OAAO,UAAU,SAAS,KAAK,KAAK;AACjD,YAAM,WAAW,SAAS,KAAK;AAC/B,YAAM,WAAW,SAAS,qBAAqB,SAAS;AACxD,YAAM,SAAS,OAAO,OAAO,YAAY,aAAa,GAAG,IAAI,aAAa,GAAG;AAC7E,UAAI,CAAC,WAAW,CAAC,YAAY,YAAY,OAAO,KAAK,KAAK,EAAE,WAAW,CAAC,KAAK,YAAY,eAAe,WAAW;AACjH,eAAO,KAAK,OAAO,QAAQ,eAAe,CAAC;AAAA,MAC7C;AACA,aAAO,MAAM,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AACA,OAAK,MAAM;AACX,SAAO;AACT;AACO,SAAS,UAAU,QAAQ,MAAM;AACtC,SAAO,QAAQ,CAAC;AAChB,QAAM,YAAY,KAAK,aAAa;AACpC,QAAM,YAAY,KAAK,aAAa;AACpC,QAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAM,SAAS,CAAC;AAChB,QAAM,WAAW,SAAS,MAAM;AAChC,MAAI,YAAY,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,mBAAmB;AAC5E,WAAO;AAAA,EACT;AAIA,WAAS,OAAO,KAAK;AACnB,UAAM,YAAY,OAAO,GAAG;AAC5B,WAAO,MAAM,SAAS,KAAK,IAAI,QAAQ,GAAG,MAAM,MAAM,KAAK,SAAS,MAAM;AAAA,EAC5E;AACA,WAAS,QAAQ,WAAW,WAAWA,SAAQ;AAC7C,WAAO,OAAO,KAAKA,OAAM,EAAE,OAAO,SAAUC,SAAQ,KAAK;AACvD,MAAAA,QAAO,YAAY,YAAY,GAAG,IAAID,QAAO,GAAG;AAChD,aAAOC;AAAA,IACT,GAAG,SAAS;AAAA,EACd;AACA,WAASC,SAAQ,KAAK;AACpB,UAAM,OAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAC/C,UAAM,UAAU,SAAS;AACzB,UAAMC,YAAW,SAAS;AAC1B,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT,WAAW,SAAS;AAClB,aAAO,CAAC,IAAI;AAAA,IACd,WAAWA,WAAU;AACnB,aAAO,CAAC,OAAO,KAAK,GAAG,EAAE;AAAA,IAC3B;AAAA,EACF;AACA,WAAS,OAAO,KAAK,MAAM,EAAE,OAAO,SAAUF,SAAQ,KAAK;AACzD,UAAM,OAAO,OAAO,UAAU,SAAS,KAAK,OAAO,GAAG,CAAC;AACvD,UAAME,YAAW,SAAS,qBAAqB,SAAS;AACxD,QAAI,CAACA,aAAYD,SAAQ,OAAO,GAAG,CAAC,GAAG;AACrC,MAAAD,QAAO,GAAG,IAAI,OAAO,GAAG;AACxB,aAAOA;AAAA,IACT,OAAO;AACL,aAAO,QAAQ,KAAKA,SAAQ,QAAQ,OAAO,GAAG,GAAG,IAAI,CAAC;AAAA,IACxD;AAAA,EACF,GAAG,CAAC,CAAC;AACL,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,UAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,IAAI,YAAY;AACnD,QAAI,OAAO,OAAO,MAAM,MAAM,CAAC;AAC/B,QAAI,OAAO,OAAO,MAAM,CAAC,CAAC;AAC1B,QAAI,YAAY;AAChB,WAAO,SAAS,QAAW;AACzB,UAAI,SAAS,aAAa;AACxB;AAAA,MACF;AACA,YAAM,OAAO,OAAO,UAAU,SAAS,KAAK,UAAU,IAAI,CAAC;AAC3D,YAAM,WAAW,SAAS,qBAAqB,SAAS;AAGxD,UAAI,CAAC,aAAa,CAAC,YAAY,OAAO,UAAU,IAAI,MAAM,aAAa;AACrE;AAAA,MACF;AACA,UAAI,aAAa,CAAC,YAAY,CAAC,aAAa,UAAU,IAAI,KAAK,MAAM;AACnE,kBAAU,IAAI,IAAI,OAAO,SAAS,YAAY,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC;AAAA,MACrE;AACA,kBAAY,UAAU,IAAI;AAC1B,UAAI,MAAM,SAAS,GAAG;AACpB,eAAO,OAAO,MAAM,MAAM,CAAC;AAC3B,eAAO,OAAO,MAAM,CAAC,CAAC;AAAA,MACxB;AAAA,IACF;AAGA,cAAU,IAAI,IAAI,UAAU,OAAO,GAAG,GAAG,IAAI;AAAA,EAC/C,CAAC;AACD,SAAO;AACT;;;ACpGA,IAAM,gBAAN,MAAoB;AAAA,EAClB;AAAA,EACA,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,GAAG,KAAK,aAAa,IAAI,IAAI,KAAK,CAAC,CAAC;AAAA,EAC7C;AACF;AACA,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAC9D,SAAS,SAAS,KAAK,MAAM;AAC3B,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,GAAG;AACnD,WAAO,IAAI,IAAI;AAAA,EACjB;AACA,SAAO,KAAK,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,GAAG,GAAG;AACrD;AACA,SAAS,SAAS,KAAK,MAAM,KAAK;AAChC,QAAM,mBACD;AAEL,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,QAAM,YAAY,MAAM,SAAS;AACjC,QAAM,OAAO,CAAC,KAAK,MAAM,UAAU;AACjC,QAAI,UAAU,WAAW;AACvB,UAAI,IAAI,IAAI;AAAA,IACd,OAAO;AACL,UAAI,IAAI,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,MAAM,IAAI,mBACtD,IAAI,IAAI;AAAA,IAEf;AACA,WAAO,OAAO,IAAI,IAAI;AAAA,EACxB,GAAG,GAAG;AACN,SAAO;AACT;AACA,SAAS,KAAK,YAAY;AACxB,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,SAAS,UAAU,GAAG;AACxB,WAAO,OAAO,KAAK,UAAU,EAAE;AAAA,EACjC;AACA,SAAO,aAAa,WAAW,SAAS;AAC1C;AACA,SAAS,QAAQ,YAAY;AAC3B,SAAO,KAAK,UAAU,MAAM;AAC9B;AACA,SAAS,WAAW,KAAK;AACvB,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,SAAS,MAAM;AACtB,SAAO,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM,QAAQ,IAAI;AAClE;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;AASA,SAAS,YAAY,KAAK;AACxB,SAAO,IAAI,QAAQ,uBAAuB,CAAC,MAAM,UAAU,SAAS,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,EAAE,QAAQ,eAAe,EAAE;AAC5I;AACA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,MAAM,OAAO;AACpB,SAAO,UAAU,QAAQ,UAAU;AACrC;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,MAAM,KAAK,MAAM;AAC1B;AACA,SAAS,SAAS,OAAO;AACvB,MAAI,SAAS,KAAK,EAAG,QAAO;AAC5B,MAAI,SAAS,KAAK,KAAK,CAAC,MAAM,OAAO,KAAK,IAAI,WAAW,KAAK,CAAC,GAAG;AAChE,WAAO,OAAO,KAAK;AAAA,EACrB;AACA,SAAO;AACT;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,QAAQ,OAAO,KAAK,UAAU;AACvC;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,QAAQ,SAAS,KAAK,MAAM;AACrC;AACA,SAASG,WAAU,KAAK;AACtB,SAAO,UAAY,GAAG;AACxB;AACA,SAASC,SAAQ,KAAK;AACpB,SAAO,QAAU,KAAK;AAAA,IACpB,MAAM;AAAA,EACR,CAAC;AACH;AACA,IAAM,mBAAmB,IAAI,eAAe,oBAAoB;AAAA,EAC9D,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AACD,IAAM,gBAAgB;AAAA,EACpB,aAAa;AAAA,EACb,sBAAsB;AAAA,EACtB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,cAAc,CAAC;AAAA,EACf,gBAAgB,CAAC;AAAA,EACjB,gBAAgB;AAAA,IACd,eAAe;AAAA,IACf,wBAAwB;AAAA,IACxB,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA,eAAe,CAAC,MAAM,IAAI;AAC5B;AACA,SAAS,gBAAgB,SAAS,CAAC,GAAG;AACpC,SAAO,gDACF,gBACA,SAFE;AAAA,IAGL,gBAAgB,kCACX,cAAc,iBACd,OAAO;AAAA,IAEZ,SAAS,kCACJ,cAAc,UACd,OAAO;AAAA,EAEd;AACF;AACA,IAAM,uBAAuB,IAAI,eAAe,sBAAsB;AACtE,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,SAAS,OAAO,kBAAkB;AAAA,IAChC,UAAU;AAAA,EACZ,CAAC,KAAK;AAAA,EACN,IAAI,uBAAuB;AACzB,WAAO,eAAe,KAAK,MAAM;AAAA,EACnC;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA,SAAS,CAAC;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,SAAS,KAAK,GAAG;AACnB,UAAI;AACJ,UAAI,cAAc;AAClB,cAAQ,aAAa,KAAK,qBAAqB,KAAK,WAAW,OAAO,MAAM;AAC1E,cAAM,CAAC,OAAO,UAAU,IAAI;AAC5B,sBAAc,YAAY,QAAQ,OAAO,MAAM;AAC7C,gBAAMC,SAAQ,WAAW,KAAK;AAC9B,gBAAM,QAAQ,SAAS,QAAQA,MAAK;AACpC,cAAI,UAAU,KAAK,GAAG;AACpB,mBAAO;AAAA,UACT;AACA,iBAAO,UAAU,YAAYA,MAAK,CAAC,IAAI,KAAK,UAAU;AAAA,YACpD;AAAA,YACA;AAAA,YACA;AAAA,YACA,OAAO,YAAYA,MAAK;AAAA,UAC1B,CAAC,IAAI;AAAA,QACP,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,WAAW,QAAQ;AACjB,UAAI,SAAS,KAAK,GAAG;AACnB,gBAAQ,KAAK,aAAa;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,gBAAQ,KAAK,YAAY;AAAA,UACvB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,aAAa;AAAA,IACX;AAAA,IACA,SAAS,CAAC;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,SAAS;AACb,WAAO,KAAK,MAAM,EAAE,QAAQ,OAAK;AAE/B,YAAM,aAAa,KAAK,UAAU;AAAA;AAAA,QAEhC,OAAO,SAAS,QAAQ,CAAC;AAAA;AAAA,QAEzB,QAAQ,SAAS,QAAQ,CAAC;AAAA,QAC1B;AAAA,QACA;AAAA,MACF,CAAC;AAED,eAAS,SAAS,QAAQ,GAAG,UAAU;AAAA,IACzC,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,YAAY,IAGT;AAHS,iBACV;AAAA;AAAA,IAzPJ,IAwPc,IAEP,iBAFO,IAEP;AAAA,MADH;AAAA;AAGA,WAAO,MAAM,IAAI,OAAK,KAAK,UAAU;AAAA,MACnC,OAAO;AAAA,OACJ,KACJ,CAAC;AAAA,EACJ;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,GAAG;AAClD,WAAO,KAAK,KAAK,oBAAmB;AAAA,EACtC;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,mBAAkB;AAAA,EAC7B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,eAAe,QAAQ;AAC9B,QAAM,CAAC,OAAO,GAAG,IAAI,OAAO;AAC5B,SAAO,IAAI,OAAO,GAAG,KAAK,MAAM,KAAK,GAAG,GAAG,OAAO,GAAG,IAAI,GAAG;AAC9D;AACA,SAAS,gBAAgB,YAAY;AACnC,QAAM,WAAW,aAAa,WAAW,MAAM,GAAG,IAAI,CAAC;AACvD,QAAM,OAAO,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,QAAQ,SAAS,CAAC,EAAE,KAAK;AAC7B,WAAO,MAAM,MAAM,SAAS,CAAC,MAAM,MAAM;AACvC;AACA,cAAQ,MAAM,QAAQ,MAAM,GAAG,IAAI,SAAS,CAAC;AAAA,IAC/C;AACA,SAAK,KAAK,KAAK;AAAA,EACjB;AACA,SAAO;AACT;AACA,IAAM,uBAAN,MAAM,8BAA6B,kBAAkB;AAAA,EACnD,WAAW,OAAO,QAAQ;AAAA,EAC1B,UAAU,IAGP;AAHO,iBACR;AAAA;AAAA,IAlSJ,IAiSY,IAEL,iBAFK,IAEL;AAAA,MADH;AAAA;AAGA,QAAI,aAAa;AACjB,QAAI,SAAS,KAAK,GAAG;AACnB,mBAAa,MAAM,QAAQ,+BAA+B,CAAC,OAAO,cAAc,SAAS;AACvF,YAAI;AACF,gBAAM,OAAO,KAAK,SAAS,IAAI,YAAY;AAC3C,iBAAO,KAAK,UAAU,GAAG,gBAAgB,IAAI,CAAC;AAAA,QAChD,SAAS,GAAG;AACV,cAAI,UAAU,0BAA0B,KAAK;AAAA,wHACiE,YAAY;AAC1H,cAAI,EAAE,QAAQ,SAAS,mBAAmB,GAAG;AAC3C,sBAAU,sBAAsB,YAAY;AAAA,UAC9C;AACA,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,MAAM,UAAU;AAAA,MACrB,OAAO;AAAA,OACJ,KACJ;AAAA,EACH;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,6BAA6B,GAAG;AAC9C,cAAQ,sCAAsC,oCAAuC,sBAAsB,qBAAoB,IAAI,KAAK,qBAAoB;AAAA,IAC9J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAChF,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,KAAK,QAAQ;AAClB,QAAI,OAAO,eAAe,iBAAiB,CAAC,OAAO,UAAU;AAC3D,YAAM,MAAM,4BAA4B,GAAG;AAC3C,cAAQ,KAAK,MAAM,GAAG,IAAI,6BAA6B;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,GAAG;AACtD,WAAO,KAAK,KAAK,wBAAuB;AAAA,EAC1C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,EACjC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAwB,IAAI,eAAe,uBAAuB;AACxE,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,mBAAmB,aAAa;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,GAAG,OAAO;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,GAAG;AACnD,WAAO,KAAK,KAAK,qBAAoB;AAAA,EACvC;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,8BAA8B,IAAI,eAAe,6BAA6B;AACpF,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B;AAAA,EACA,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe;AACb,UAAM,eAAe,KAAK,WAAW;AACrC,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,MAAM,sFAAsF;AAAA,IACxG;AACA,WAAO,MAAM,QAAQ,YAAY,IAAI,eAAe,CAAC,YAAY;AAAA,EACnE;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,GAAG;AACxD,WAAO,KAAK,KAAK,0BAA4B,SAAS,gBAAgB,CAAC;AAAA,EACzE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,yBAAwB;AAAA,EACnC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAQH,SAAS,iBAAiB,MAAM;AAC9B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,QAAM,IAAI;AACV,SAAO,MAAM,KAAK,GAAG;AACvB;AAOA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,KAAK,MAAM,GAAG,EAAE,IAAI;AAC7B;AAQA,SAAS,aAAa,KAAK,OAAO,OAAO,KAAK;AAC5C,MAAI,SAAS,GAAG,GAAG;AACjB,UAAM,WAAW,IAAI,MAAM,IAAI;AAC/B,UAAM,WAAW,SAAS,IAAI;AAC9B,WAAO,aAAa,QAAQ,CAAC,MAAM,SAAS,SAAS,CAAC,IAAI,CAAC,OAAO,QAAQ;AAAA,EAC5E;AACA,SAAO,CAAC,OAAO,EAAE;AACnB;AACA,SAAS,0BAA0BC,UAAS,MAAM;AAChD,QAAM,CAAC,SAAS,IAAI,aAAa,MAAM,QAAQ;AAC/C,MAAI,CAAC,WAAW;AAEd,WAAO,CAAC,CAACA,SAAQ,OAAO;AAAA,EAC1B;AAEA,SAAO;AACT;AACA,SAAS,oBAAoB,oBAAoB;AAC/C,SAAO,qBAAqB,YAAU,SAAS,KAAK,CAAC;AACvD;AACA,SAAS,aAAa,cAAc,OAAO;AACzC,SAAO,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,KAAK,SAAS;AACrD,QAAI,GAAG,KAAK,IAAI,IAAI,EAAE,IAAI,aAAa,IAAI;AAC3C,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,oBAAoB,eAAe,OAAO;AACjD,SAAO,gBAAgB,aAAa,IAAI,aAAa,cAAc,QAAQ,KAAK,IAAI;AACtF;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO;AAAA,IACL,OAAO,iBAAiB,IAAI,KAAK;AAAA,IACjC,UAAU,iBAAiB,IAAI;AAAA,EACjC;AACF;AACA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,cAAc;AAChB,UAAM,aAAa,aAAa,IAAI;AACpC,QAAI,WAAW,UAAU,MAAM,OAAO;AACpC,YAAM,iEAAiE,IAAI;AAAA,IAC7E;AACA,WAAO,aAAa,IAAI,EAAE,EAAE,KAAK,SAAO,IAAI,UAAU,IAAI,UAAU,GAAG;AAAA,EACzE;AACA,SAAO,WAAW,eAAe,MAAM,IAAI;AAC7C;AACA,SAAS,oBAAoB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,QAAQ,eAAe,CAAC,MAAM,YAAY,IAAI,CAAC,IAAI;AACzD,SAAO,MAAM,IAAI,CAAAC,UAAQ;AACvB,UAAM,SAAS,cAAc;AAAA,MAC3B,MAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO,KAAK,MAAM,EAAE,KAAK,IAAI,kBAAgB;AAAA,MAC3C;AAAA,MACA,MAAMA;AAAA,IACR,EAAE,CAAC;AAAA,EACL,CAAC;AACH;AACA,IAAI;AACJ,SAAS,UAAU,KAAK,SAAS,CAAC,GAAG,MAAM;AACzC,SAAO,QAAQ,UAAU,KAAK,QAAQ,IAAI;AAC5C;AACA,SAAS,gBAAgB,KAAK,SAAS,CAAC,GAAG,MAAM;AAC/C,SAAO,QAAQ,gBAAgB,KAAK,QAAQ,IAAI;AAClD;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe,oBAAI,IAAI;AAAA,EACvB,QAAQ,oBAAI,IAAI;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,EACd,iBAAiB,CAAC;AAAA,EAClB,wBAAwB;AAAA,EACxB;AAAA,EACA,cAAc,oBAAI,IAAI;AAAA,EACtB,SAAS,IAAI,QAAQ;AAAA,EACrB,UAAU,KAAK,OAAO,aAAa;AAAA,EACnC;AAAA,EACA,YAAY,QAAQ,QAAQ,gBAAgB,aAAa,YAAY,kBAAkB;AACrF,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,IAAI,cAAc,KAAK,YAAY;AAAA,IACnD;AACA,cAAU;AACV,SAAK,SAAS,KAAK,MAAM,KAAK,UAAU,UAAU,CAAC;AACnD,SAAK,kBAAkB,KAAK,OAAO,kBAAkB,CAAC,CAAC;AACvD,SAAK,qCAAqC,KAAK,MAAM;AACrD,SAAK,eAAe,KAAK,OAAO,WAAW;AAC3C,SAAK,OAAO,IAAI,gBAAgB,KAAK,eAAe,CAAC;AAGrD,SAAK,eAAe,KAAK,KAAK,aAAa;AAI3C,SAAK,QAAQ,KAAK,mBAAmB,CAAC,EAAE,UAAU,OAAK;AACrD,UAAI,EAAE,SAAS,4BAA4B,EAAE,YAAY;AACvD,aAAK,cAAc,EAAE,QAAQ,QAAQ;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,MAAM;AACnB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,KAAK,SAAS;AAAA,EAC5B;AAAA,EACA,cAAc,MAAM;AAClB,SAAK,OAAO,gBAAgB,IAAI;AAChC,SAAK,KAAK,KAAK,IAAI;AACnB,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,SAAS,gBAAgB,IAAI;AAAA,IAC/B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,KAAK,MAAM,UAAU,CAAC,GAAG;AACvB,UAAM,SAAS,KAAK,MAAM,IAAI,IAAI;AAClC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,QAAI;AACJ,UAAM,UAAU,KAAK,cAAc,IAAI;AACvC,QAAI;AACJ,QAAI,SAAS;AACX,cAAQ,iBAAiB,IAAI;AAAA,IAC/B;AACA,UAAM,iBAAiB;AAAA,MACrB;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,cAAc,QAAQ;AAAA,MACtB,MAAM,UAAU;AAAA,QACd;AAAA,MACF,IAAI;AAAA,IACN;AACA,QAAI,KAAK,uBAAuB,IAAI,GAAG;AAErC,YAAM,WAAW,UAAU,GAAG,KAAK,IAAI,KAAK,iBAAiB,KAAK,KAAK;AACvE,YAAM,UAAU,oBAAoB,iCAC/B,iBAD+B;AAAA,QAElC,cAAc;AAAA,MAChB,EAAC;AACD,wBAAkB,SAAS,OAAO;AAAA,IACpC,OAAO;AACL,YAAM,SAAS,cAAc,cAAc;AAC3C,wBAAkB,KAAK,MAAM;AAAA,IAC/B;AACA,UAAM,QAAQ,gBAAgB,KAAK,MAAM,KAAK,OAAO,aAAa,GAAG,IAAI,iBAAe;AACtF,UAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,oBAAY,QAAQ,OAAK;AACvB,eAAK,cAAc,EAAE,MAAM,EAAE,WAAW;AAExC,cAAI,EAAE,SAAS,MAAM;AACnB,iBAAK,MAAM,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAAA,UAC/B;AAAA,QACF,CAAC;AACD;AAAA,MACF;AACA,WAAK,cAAc,MAAM,WAAW;AAAA,IACtC,CAAC,GAAG,WAAW,WAAS;AACtB,UAAI,CAAC,KAAK,OAAO,UAAU;AACzB,gBAAQ,MAAM,+BAA+B,IAAI,KAAK,KAAK;AAAA,MAC7D;AACA,aAAO,KAAK,cAAc,MAAM,OAAO;AAAA,IACzC,CAAC,GAAG,YAAY,CAAC,CAAC;AAClB,SAAK,MAAM,IAAI,MAAM,KAAK;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,UAAU,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,cAAc,GAAG;AACvD,QAAI,CAAC,IAAK,QAAO;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,oBAAoB,IAAI;AACjC,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAO,IAAI,IAAI,OAAK,KAAK,UAAU,QAAQ,GAAG,KAAK,IAAI,CAAC,KAAK,GAAG,QAAQ,WAAW,CAAC;AAAA,IACtF;AACA,UAAM,QAAQ,GAAG,KAAK,IAAI,GAAG,KAAK;AAClC,UAAM,cAAc,KAAK,eAAe,WAAW;AACnD,UAAM,QAAQ,YAAY,GAAG;AAC7B,QAAI,CAAC,OAAO;AACV,aAAO,KAAK,kBAAkB,KAAK,OAAO,MAAM;AAAA,IAClD;AACA,WAAO,KAAK,OAAO,UAAU;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,gBAAgB,KAAK,QAAQ,MAAM,YAAY,OAAO;AACpD,QAAI;AACJ,UAAM,OAAO,CAACC,OAAM,YAAY,KAAK,KAAKA,OAAM,OAAO,EAAE,KAAK,IAAI,MAAM,YAAY,KAAK,gBAAgB,KAAK,QAAQA,KAAI,IAAI,KAAK,UAAU,KAAK,QAAQA,KAAI,CAAC,CAAC;AAChK,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,KAAK,aAAa,KAAK,UAAU,CAAAA,UAAQ,KAAKA,KAAI,CAAC,CAAC;AAAA,IAC7D;AACA,WAAO,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,IAAI;AACvC,QAAI,cAAc,IAAI,GAAG;AAEvB,YAAM,gBAAgB;AACtB,aAAO,cAAc;AACrB,qBAAe,oBAAoB,eAAe,cAAc,KAAK;AAAA,IACvE;AACA,WAAO;AACP,QAAI,KAAK,OAAO,IAAI,KAAK,KAAK,gBAAgB,IAAI,GAAG;AACnD,aAAO,KAAK,IAAI;AAAA,IAClB;AAEA,UAAM,QAAQ;AACd,WAAO,KAAK,aAAa,KAAK,UAAU,CAAAA,UAAQ,KAAK,GAAG,KAAK,IAAIA,KAAI,IAAI;AAAA,MACvE;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,MAAM;AACpB,WAAO,KAAK,OAAO,iBAAiB,IAAI,CAAC;AAAA,EAC3C;AAAA,EACA,gBAAgB,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,cAAc,GAAG;AAC7D,QAAI,SAAS,GAAG,KAAK,MAAM,QAAQ,GAAG,GAAG;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,oBAAoB,IAAI;AACjC,UAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,eAAO,IAAI,IAAI,OAAK,KAAK,gBAAgB,QAAQ,GAAG,KAAK,IAAI,CAAC,KAAK,GAAG,QAAQ,WAAW,CAAC;AAAA,MAC5F;AACA,YAAM,cAAc,KAAK,eAAe,WAAW;AACnD,YAAM,QAAQ,GAAG,KAAK,IAAI,GAAG,KAAK;AAClC,YAAM,QAAQL,WAAU,KAAK,eAAe,aAAa,GAAG,CAAC;AAE7D,aAAO,QAAQ,KAAK,IAAI,KAAK,UAAU,KAAK,QAAQ,IAAI,IAAI,KAAK,OAAO,UAAU;AAAA,QAChF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,eAAe,CAAC;AACtB,eAAW,CAAC,MAAM,OAAO,KAAK,KAAK,WAAW,GAAG,GAAG;AAClD,mBAAa,KAAK,KAAK,gBAAgB,MAAM,SAAS,IAAI,CAAC;AAAA,IAC7D;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,KAAK,QAAQ,MAAM;AACvC,QAAI,SAAS,GAAG,KAAK,MAAM,QAAQ,GAAG,GAAG;AACvC,aAAO,KAAK,gBAAgB,KAAK,QAAQ,MAAM,IAAI;AAAA,IACrD;AACA,UAAM,CAAC,CAAC,UAAU,WAAW,GAAG,GAAG,IAAI,IAAI,KAAK,WAAW,GAAG;AAG9D,WAAO,KAAK,sBAAsB,UAAU,aAAa,IAAI,EAAE,KAAK,IAAI,WAAS;AAC/E,YAAM,eAAe,CAAC,KAAK;AAC3B,iBAAW,CAAC,MAAM,OAAO,KAAK,MAAM;AAClC,qBAAa,KAAK,KAAK,gBAAgB,MAAM,SAAS,IAAI,CAAC;AAAA,MAC7D;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,eAAe,aAAa;AAC1B,QAAI,aAAa;AACf,UAAI,KAAK,OAAO,WAAW,GAAG;AAC5B,eAAO,KAAK,aAAa,IAAI,WAAW,KAAK,CAAC;AAAA,MAChD,OAAO;AAEL,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,KAAK,oBAAoB,WAAW;AACxC,cAAM,cAAc,KAAK,aAAa,IAAI,WAAW,KAAK,CAAC;AAC3D,eAAO,KAAK,eAAe,aAAa,KAAK;AAAA,MAC/C;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,MAAM;AACtB,QAAI,YAAY,KAAK;AACrB,QAAI,MAAM;AACR,YAAM,qBAAqB,iBAAiB,IAAI,MAAM;AACtD,UAAI,KAAK,OAAO,IAAI,KAAK,oBAAoB;AAC3C,oBAAY,GAAG,IAAI;AAAA,MACrB,OAAO;AACL,oBAAY,KAAK,aAAa,KAAK,IAAI,iBAAe,GAAG,IAAI,IAAI,WAAW,EAAE,CAAC;AAAA,MACjF;AAAA,IACF;AACA,WAAO,UAAU,KAAK,UAAU,cAAY,KAAK,KAAK,QAAQ,EAAE,KAAK,IAAI,MAAM,KAAK,eAAe,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,EACjH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,aAAa,OAAO,KAAK,cAAc,GAAG,UAAU,CAAC,GAAG;AACrE,UAAM,WAAW;AAAA,MACf,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AACA,UAAM,gBAAgB,kCACjB,WACA;AAEL,UAAM,QAAQ,iBAAiB,IAAI;AAKnC,QAAI,4BAA4B;AAEhC,QAAI,OAAO;AACT,YAAM,MAAM,KAAK,eAAe,KAAK;AACrC,kCAA4BC,SAAQ;AAAA,QAClC,CAAC,GAAG,GAAG;AAAA,MACT,CAAC;AAAA,IACH;AACA,UAAM,cAAc,QAAQ,iBAAiB,IAAI,IAAI;AACrD,UAAM,oBAAoB,kCACpB,cAAc,SAAS,KAAK,eAAe,WAAW,IACvD;AAEL,UAAM,qBAAqB,KAAK,OAAO,QAAQ,MAAM,oBAAoBA,SAAQ,iBAAiB;AAClG,UAAM,WAAW,KAAK,YAAY,mBAAmB,oBAAoB,WAAW;AACpF,SAAK,aAAa,IAAI,aAAa,QAAQ;AAC3C,kBAAc,cAAc,KAAK,cAAc,KAAK,cAAc,CAAC;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,KAAK,OAAO,UAAU,CAAC,GAAG;AAC1C,UAAM,OAAO,QAAQ,QAAQ,KAAK,cAAc;AAChD,UAAM,WAAW,KAAK,YAAY,sBAAsB,KAAK,OAAO,IAAI;AACxE,UAAM,WAAW;AAAA,MACf,CAAC,GAAG,GAAG;AAAA,IACT;AACA,SAAK,eAAe,UAAU,MAAM,iCAC/B,UAD+B;AAAA,MAElC,OAAO;AAAA,IACT,EAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qCAAqC;AAAA,IACnC;AAAA,EACF,GAAG;AACD,UAAM,OAAO,MAAM,QAAQ,YAAY,IAAI,aAAa,CAAC,IAAI;AAC7D,QAAI,gBAAgB,KAAK,uBAAuB,IAAI,GAAG;AACrD,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,KAAK,OAAO,QAAQ;AACpC,QAAI,KAAK,OAAO,eAAe,cAAc,UAAU,IAAI;AACzD,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,yBAAyB,KAAK,uBAAuB,GAAG;AAEhE,WAAK,wBAAwB;AAC7B,YAAM,gBAAgB,KAAK,UAAU,KAAK,QAAQ,KAAK,iBAAiB;AACxE,WAAK,wBAAwB;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,eAAe,OAAO,KAAK,KAAK,sBAAsB,GAAG,MAAM;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,MAAM;AAClB,WAAO,KAAK,qBAAqB,EAAE,QAAQ,IAAI,MAAM;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,MAAM;AACX,WAAO,KAAK,qBAAqB,EAAE,QAAQ,IAAI,MAAM;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,MAAM,cAAc;AACpC,UAAM,WAAW,iBAAiB,IAAI;AACtC,QAAI,KAAK,cAAc,IAAI,KAAK,CAAC,KAAK,oBAAoB,QAAQ,GAAG;AACnE,aAAO,cAAc,CAAC,KAAK,KAAK,QAAQ,GAAG,KAAK,KAAK,MAAM;AAAA,QACzD;AAAA,MACF,CAAC,CAAC,CAAC;AAAA,IACL;AACA,WAAO,KAAK,KAAK,MAAM;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB,aAAa;AAClC,QAAI,KAAK,cAAc,WAAW,KAAK,CAAC,KAAK,OAAO,iBAAiB,WAAW,CAAC,GAAG;AAClF,aAAO,GAAG,WAAW,IAAI,KAAK,cAAc,CAAC;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,OAAO,OAAO;AAC3B,QAAI,CAAC,KAAK,OAAO,cAAc;AAC7B,WAAK,OAAO,eAAe,CAAC;AAAA,IAC9B;AACA,SAAK,OAAO,aAAa,KAAK,IAAI;AAAA,EACpC;AAAA,EACA,cAAc;AAKZ,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA,EACA,oBAAoB,MAAM;AACxB,WAAO,KAAK,KAAK,eAAe,IAAI,CAAC;AAAA,EACvC;AAAA,EACA,uBAAuB;AACrB,UAAM,QAAQ,KAAK,kBAAkB,EAAE,CAAC;AACxC,QAAI,SAAS,KAAK,GAAG;AACnB,aAAO,KAAK,kBAAkB;AAAA,IAChC;AACA,WAAO,KAAK,kBAAkB,EAAE,IAAI,OAAK,EAAE,EAAE;AAAA,EAC/C;AAAA,EACA,wBAAwB;AACtB,WAAO,iCACF,KAAK,SADH;AAAA,MAEL,YAAY,KAAK,cAAc;AAAA,MAC/B,gBAAgB,KAAK;AAAA,MACrB,aAAa,KAAK;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,MAAM;AAC3B,WAAO,KAAK,OAAO,eAAe,0BAA0B,SAAS,KAAK;AAAA,EAC5E;AAAA,EACA,cAAc,MAAM,aAAa;AAC/B,SAAK,eAAe,aAAa,MAAM;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AACD,SAAK,OAAO,KAAK;AAAA,MACf,YAAY,CAAC,CAAC,KAAK,YAAY;AAAA,MAC/B,MAAM;AAAA,MACN,SAAS,gBAAgB,IAAI;AAAA,IAC/B,CAAC;AACD,SAAK,YAAY,QAAQ,OAAK,KAAK,MAAM,OAAO,CAAC,CAAC;AAClD,SAAK,YAAY,MAAM;AAAA,EACzB;AAAA,EACA,cAAc,MAAM,aAAa;AAG/B,QAAI,MAAM,YAAY,aAAa,GAAG;AACpC,kBAAY,gBAAgB;AAC5B,UAAI,CAAC,YAAY,eAAe;AAC9B,oBAAY,gBAAgB,KAAK,iBAAiB,aAAa,IAAI;AAAA,MACrE;AAAA,IACF;AACA,UAAM,WAAW,KAAK,MAAM,GAAG;AAC/B,UAAM,YAAY,YAAY;AAC9B,UAAM,WAAW,UAAU,YAAY,aAAa;AACpD,SAAK,YAAY,IAAI,IAAI;AAEzB,QAAI,KAAK,MAAM,IAAI,QAAQ,GAAG;AAC5B,WAAK,cAAc,UAAU,KAAK,eAAe,QAAQ,CAAC;AAC1D,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,aAAa,SAAS,SAAS,SAAS,CAAC;AAChE,QAAI,CAAC,YAAY,gBAAgB;AAC/B,UAAI,MAAM;AACV,UAAI,SAAS,SAAS,GAAG;AACvB,eAAO;AAAA,MACT;AACA,YAAM,IAAI,MAAM,GAAG;AAAA,IACrB;AACA,QAAI,cAAc;AAElB,QAAI,SAAS,SAAS,GAAG;AAGvB,eAAS,SAAS,SAAS,CAAC,IAAI;AAChC,oBAAc,SAAS,KAAK,GAAG;AAAA,IACjC;AACA,gBAAY;AACZ,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,SAAS,gBAAgB,IAAI;AAAA,IAC/B,CAAC;AACD,WAAO,KAAK,KAAK,aAAa,WAAW;AAAA,EAC3C;AAAA,EACA,eAAe,OAAO;AACpB,UAAM;AAAA,MACJ,eAAe,CAAC;AAAA,IAClB,IAAI,KAAK;AACT,WAAO,aAAa,KAAK,KAAK,YAAY,KAAK;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,MAAM;AACxB,QAAI,cAAc;AAClB,QAAI;AACJ,QAAI,KAAK,cAAc,IAAI,GAAG;AAE5B,YAAM,gBAAgB,iBAAiB,IAAI;AAE3C,YAAM,UAAU,KAAK,OAAO,aAAa;AAEzC,oBAAc,UAAU,gBAAgB,KAAK,cAAc;AAE3D,cAAQ,KAAK,eAAe,UAAU,iBAAiB,IAAI,IAAI,IAAI;AAAA,IACrE;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,aAAa,KAAK;AAC/B,UAAM,SAAS,CAAC;AAChB,UAAM,SAAS,GAAG,GAAG;AACrB,eAAW,cAAc,aAAa;AACpC,UAAI,WAAW,WAAW,MAAM,GAAG;AACjC,eAAO,WAAW,QAAQ,QAAQ,EAAE,CAAC,IAAI,YAAY,UAAU;AAAA,MACjE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,KAAK;AACd,WAAO,eAAe,MAAM,IAAI,QAAQ,IAAI,OAAO,QAAQ,GAAG;AAAA,EAChE;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,GAAG;AACjD,WAAO,KAAK,KAAK,mBAAqB,SAAS,kBAAkB,CAAC,GAAM,SAAS,oBAAoB,GAAM,SAAS,yBAAyB,GAAM,SAAS,qBAAqB,GAAM,SAAS,gBAAgB,GAAM,SAAS,2BAA2B,CAAC;AAAA,EAC7P;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,GAAG;AACzD,WAAO,KAAK,KAAK,2BAA0B;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,QAAQ;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB;AAAA,IACjC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,6BAA6B,GAAG,WAAW,CAAC;AAAA,IACzD,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,GAAG,OAAO,CAAC;AAAA,MAC1B;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,aAAa,IAAI,MAAS,cAAc;AAAA,MACxD;AAAA,IACF;AAAA,IACA,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA;AAAA,MAGV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,YAAY,MAAM,KAAK;AACrB,SAAK,OAAO;AACZ,SAAK,MAAM;AAAA,EACb;AAAA,EACA,aAAa;AACX,QAAI,KAAK,gBAAgB,aAAa;AACpC,WAAK,IAAI,mBAAmB,KAAK,IAAI;AAAA,IACvC,WAAW,SAAS,KAAK,IAAI,GAAG;AAC9B,YAAM,eAAe,KAAK,IAAI,gBAAgB,wBAAwB;AACtE,mBAAa,SAAS,OAAO,KAAK;AAClC,mBAAa,SAAS,cAAc;AAAA,IACtC,OAAO;AACL,WAAK,IAAI,gBAAgB,KAAK,IAAI;AAAA,IACpC;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,IAAI,MAAM;AAAA,EACjB;AACF;AACA,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAC1D,IAAM,6BAA6B,IAAI,eAAe,4BAA4B;AAClF,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAC5D,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AAAA;AAAA,EAEd,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,QAAI,OAAO;AAKX,QAAI,KAAK,aAAa;AACpB,aAAO;AACP,aAAO;AAAA,IACT;AACA,QAAI,UAAU;AACZ,YAAM,CAAC,EAAE,SAAS,IAAI,aAAa,UAAU,QAAQ;AACrD,aAAO;AAAA,IACT;AACA,QAAI,QAAQ;AACV,YAAM,CAAC,EAAE,SAAS,IAAI,aAAa,QAAQ,QAAQ;AACnD,aAAO;AAAA,IACT;AACA,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,wBAAwB,MAAM;AAC5B,UAAM,QAAQ,iBAAiB,IAAI;AACnC,WAAO,QAAQ,iBAAiB,IAAI,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,gBAAgB,MAAM,OAAO;AAC3B,WAAO,QAAQ,GAAG,KAAK,IAAI,IAAI,KAAK;AAAA,EACtC;AACF;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB;AAAA,EACA,YAAYE,UAAS;AACnB,SAAK,UAAUA;AAAA,EACjB;AAAA;AAAA,EAEA,QAAQ,QAAQ;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,QAAI,UAAU;AACZ,UAAI,cAAc,QAAQ,GAAG;AAC3B,cAAM;AAAA,UACJ;AAAA,UACA,QAAQ,YAAY,KAAK;AAAA,QAC3B,IAAI;AACJ,aAAK,QAAQ,eAAe,OAAO,KAAK;AACxC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,aAAa,OAAO,UAAU;AAAA,EAC9B,UAAU,OAAO,gBAAgB;AAAA,EACjC,MAAM,OAAO,aAAa;AAAA,IACxB,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,gBAAgB;AAAA,IACpC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,gBAAgB,OAAO,iBAAiB;AAAA,IACtC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,qBAAqB,OAAO,4BAA4B;AAAA,IACtD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,MAAM,OAAO,iBAAiB;AAAA,EAC9B,OAAO,OAAO,UAAU;AAAA,EACxB,MAAM,OAAO,gBAAgB;AAAA,EAC7B,WAAW,OAAO,SAAS;AAAA,EAC3B;AAAA,EACA,OAAO,oBAAI,IAAI;AAAA,EACf;AAAA,EACA,SAAS,CAAC;AAAA,EACV;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,cAAc;AAAA,EACd;AAAA,EACA,eAAe,IAAI,aAAa;AAAA,EAChC,gBAAgB,IAAI,cAAc,KAAK,OAAO;AAAA,EAC9C,WAAW,KAAK,QAAQ,OAAO,cAAc;AAAA,EAC7C,OAAO,uBAAuB,KAAK,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,UAAM,qBAAqB,0BAA0B,KAAK,SAAS,KAAK,gBAAgB,KAAK,UAAU;AACvG,SAAK,QAAQ,aAAa,KAAK,UAAU,gBAAc;AACrD,YAAM,OAAO,KAAK,aAAa,QAAQ;AAAA,QACrC,QAAQ,KAAK;AAAA,QACb,UAAU,KAAK;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,MAAM,QAAQ,KAAK,aAAa,IAAI,SAAS,KAAK,cAAc,IAAI,mBAAiB,KAAK,aAAa,MAAM,aAAa,CAAC,CAAC,IAAI,KAAK,aAAa,MAAM,KAAK,aAAa;AAAA,IACnL,CAAC,GAAG,oBAAoB,kBAAkB,GAAG,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAChG,WAAK,cAAc,KAAK,aAAa,wBAAwB,KAAK,IAAI;AACtE,WAAK,aAAa,cAAc,KAAK,kBAAkB,IAAI,KAAK,mBAAmB,KAAK,aAAa,KAAK,UAAU,KAAK,UAAU;AACnI,WAAK,IAAI,aAAa;AACtB,WAAK,cAAc;AAAA,IACrB,CAAC;AACD,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,iBAAiB,KAAK,sBAAsB;AAClD,UAAI,gBAAgB;AAClB,aAAK,mBAAmB,IAAI,gBAAgB,gBAAgB,KAAK,GAAG;AACpE,aAAK,iBAAiB,WAAW;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AAGnB,QAAI,KAAK,aAAa,aAAa;AACjC,YAAM,UAAU,OAAO,KAAK,OAAO,EAAE,KAAK,OAAK,CAAC,QAAQ,CAAC,EAAE,WAAW;AACtE,iBAAW,KAAK,kBAAkB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,aAAa;AAClB,SAAK,SAAS,YAAY,KAAK,KAAK,eAAe,aAAa,KAAK,QAAQ,UAAU,KAAK,KAAK,KAAK,QAAQ,KAAK,WAAW,CAAC;AAAA,EACjI;AAAA,EACA,mBAAmB,MAAM,QAAQ;AAC/B,SAAK,KAAK,MAAM;AAChB,UAAM,cAAc,KAAK,eAAe,MAAM,MAAM;AACpD,QAAI,KAAK,MAAM;AAEb,WAAK,KAAK,QAAQ,WAAW,IAAI;AACjC,WAAK,KAAK,QAAQ,aAAa,IAAI,KAAK;AAAA,IAC1C,OAAO;AACL,WAAK,aAAa;AAClB,WAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,KAAK;AAAA,QAChD,WAAW;AAAA,QACX,aAAa,KAAK;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,MAAM,QAAQ;AAC3B,WAAO,CAAC,KAAK,WAAW;AACtB,YAAM,aAAa,SAAS,GAAG,MAAM,IAAI,GAAG,KAAK;AACjD,YAAM,UAAU,SAAS,GAAG,UAAU,GAAG,KAAK,UAAU,MAAM,CAAC,KAAK;AACpE,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,GAAG;AAC3B,aAAK,KAAK,IAAI,SAAS,KAAK,QAAQ,UAAU,YAAY,QAAQ,IAAI,CAAC;AAAA,MACzE;AACA,aAAO,KAAK,KAAK,IAAI,OAAO;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,SAAK,KAAK,MAAM;AAAA,EAClB;AAAA,EACA,eAAe;AACb,SAAK,kBAAkB,WAAW;AAAA,EACpC;AAAA,EACA,aAAa,MAAM,eAAe;AAChC,UAAM,gBAAgB,KAAK,cAAc,QAAQ;AAAA,MAC/C,QAAQ,KAAK;AAAA,MACb,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,OAAO,KAAK,aAAa,gBAAgB,MAAM,aAAa;AACjE,UAAM,eAAe,oBAAoB,eAAe,aAAa;AACrE,WAAO,KAAK,QAAQ,kBAAkB,KAAK,MAAM,YAAY;AAAA,EAC/D;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,GAAG;AACnD,WAAO,KAAK,KAAK,qBAAoB;AAAA,EACvC;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACjC,QAAQ;AAAA,MACN,KAAK,CAAC,GAAG,aAAa,KAAK;AAAA,MAC3B,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,MACvC,aAAa,CAAC,GAAG,kBAAkB,aAAa;AAAA,MAChD,YAAY,CAAC,GAAG,iBAAiB,YAAY;AAAA,MAC7C,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,MACvC,YAAY,CAAC,GAAG,iBAAiB,YAAY;AAAA,MAC7C,WAAW,CAAC,GAAG,uBAAuB,WAAW;AAAA,IACnD;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,eAAe,IAAI,aAAa;AAAA,EAChC;AAAA,EACA,YAAYA,UAAS,eAAe,cAAc,KAAK;AACrD,SAAK,UAAUA;AACf,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,MAAM;AACX,SAAK,gBAAgB,IAAI,cAAc,KAAK,OAAO;AAAA,EACrD;AAAA;AAAA;AAAA,EAGA,UAAU,KAAK,QAAQ,YAAY;AACjC,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,UAAM,UAAU,SAAS,GAAG,GAAG,GAAG,KAAK,UAAU,MAAM,CAAC,KAAK;AAC7D,QAAI,YAAY,KAAK,SAAS;AAC5B,aAAO,KAAK;AAAA,IACd;AACA,SAAK,UAAU;AACf,SAAK,cAAc,YAAY;AAC/B,UAAM,qBAAqB,0BAA0B,KAAK,SAAS,KAAK,gBAAgB,UAAU;AAClG,SAAK,eAAe,KAAK,QAAQ,aAAa,KAAK,UAAU,gBAAc;AACzE,YAAM,OAAO,KAAK,aAAa,QAAQ;AAAA,QACrC,QAAQ;AAAA,QACR,UAAU,KAAK;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AACD,aAAO,MAAM,QAAQ,KAAK,aAAa,IAAI,SAAS,KAAK,cAAc,IAAI,mBAAiB,KAAK,aAAa,MAAM,aAAa,CAAC,CAAC,IAAI,KAAK,aAAa,MAAM,KAAK,aAAa;AAAA,IACnL,CAAC,GAAG,oBAAoB,kBAAkB,CAAC,EAAE,UAAU,MAAM,KAAK,YAAY,KAAK,MAAM,CAAC;AAC1F,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,YAAY;AAG/B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,YAAY,KAAK,QAAQ;AACvB,UAAM,OAAO,KAAK,aAAa,wBAAwB,KAAK,IAAI;AAChE,SAAK,YAAY,KAAK,QAAQ,UAAU,KAAK,QAAQ,IAAI;AACzD,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,aAAa,MAAM,eAAe;AAChC,UAAM,gBAAgB,KAAK,cAAc,QAAQ;AAAA,MAC/C,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,OAAO,KAAK,aAAa,gBAAgB,MAAM,aAAa;AACjE,UAAM,eAAe,oBAAoB,eAAe,aAAa;AACrE,WAAO,KAAK,QAAQ,kBAAkB,KAAK,MAAM,YAAY;AAAA,EAC/D;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,GAAG;AAC9C,WAAO,KAAK,KAAK,gBAAkB,kBAAkB,kBAAkB,EAAE,GAAM,kBAAkB,iBAAiB,EAAE,GAAM,kBAAkB,gBAAgB,EAAE,GAAM,kBAAqB,mBAAmB,EAAE,CAAC;AAAA,EACjN;AAAA,EACA,OAAO,QAA0B,aAAa;AAAA,IAC5C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,OAAO,CAAC,oBAAoB,aAAa;AAC/C,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,GAAG;AAChD,WAAO,KAAK,KAAK,kBAAiB;AAAA,EACpC;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,oBAAoB,aAAa;AAAA,IAC3C,SAAS,CAAC,oBAAoB,aAAa;AAAA,EAC7C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,iBAAiB,SAAS;AACjC,QAAM,YAAY,CAAC,2BAA2B,iBAAiB,GAAG,+BAA+B,qBAAqB,GAAG,4BAA4B,kBAAkB,GAAG,iCAAiC,uBAAuB,CAAC;AACnO,MAAI,QAAQ,QAAQ;AAClB,cAAU,KAAK,uBAAuB,QAAQ,MAAM,CAAC;AAAA,EACvD;AACA,MAAI,QAAQ,QAAQ;AAClB,cAAU,KAAK,uBAAuB,QAAQ,MAAM,CAAC;AAAA,EACvD;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,QAAQ;AACtC,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU,gBAAgB,MAAM;AAAA,EAClC,CAAC,CAAC;AACJ;AACA,SAAS,uBAAuB,QAAQ;AACtC,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AACA,SAAS,yBAAyB,QAAQ;AACxC,SAAO,OAAO,IAAI,YAAU;AAAA,IAC1B,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACT,EAAE;AACJ;AACA,SAAS,2BAA2B,SAAS;AAC3C,SAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACF;AACA,SAAS,2BAA2B,YAAY;AAC9C,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM,CAAC,gBAAgB;AAAA,EACzB,CAAC,CAAC;AACJ;AACA,SAAS,iCAAiC,UAAU;AAClD,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM,CAAC,gBAAgB;AAAA,EACzB,CAAC,CAAC;AACJ;AACA,SAAS,+BAA+B,SAAS;AAC/C,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AACA,SAAS,4BAA4B,aAAa;AAChD,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AACA,SAAS,qBAAqB,MAAM;AAClC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACF;AACA,IAAM,uBAAuB,IAAI,eAAe,oDAAoD;AACpG,IAAM,yBAAyB,IAAI,eAAe,0CAA0C;AAC5F,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,eAAe,MAAM;AACnB,WAAO,GAAG,KAAK,MAAM,IAAI,CAAC;AAAA,EAC5B;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,GAAG;AAC9C,WAAO,KAAK,KAAK,gBAAkB,SAAS,oBAAoB,CAAC;AAAA,EACnE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,qBAAqBA,UAAS,QAAQ,CAAC,GAAG,SAAS;AAC1D,QAAM,kBAAkB,MAAM,QAAQ,eAAe,QAAQ,IAAI,OAAO,KAAK,KAAK,EAAE,IAAI,UAAQA,SAAQ,KAAK,IAAI,EAAE,UAAU,CAAC,CAAC,IAAI,QAAQ,QAAQ;AACnJ,SAAO;AACT;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,QAAQ,SAAS;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,iBAAiB;AAAA,QAC3B,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,UAAU;AAAA,UACV,gBAAgB;AAAA,YACd,eAAe;AAAA,UACjB;AAAA,WACG,QAAQ;AAAA,MAEf,CAAC,GAAG;AAAA,QACF,SAAS;AAAA,QACT,UAAU,QAAQ;AAAA,MACpB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,MAAM,CAAC,kBAAkB,sBAAsB,sBAAsB;AAAA,QACrE,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,GAAG;AACvD,WAAO,KAAK,KAAK,yBAAwB;AAAA,EAC3C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,SAAS,iBAAiB;AACxB,MAAI,cAAc,sBAAsB;AACxC,MAAI,CAAC,eAAe,CAAC,UAAU,GAAG;AAChC,WAAO;AAAA,EACT;AACA,MAAI,YAAY,QAAQ,GAAG,MAAM,IAAI;AACnC,kBAAc,YAAY,MAAM,GAAG,EAAE,CAAC;AAAA,EACxC;AACA,MAAI,YAAY,QAAQ,GAAG,MAAM,IAAI;AACnC,kBAAc,YAAY,MAAM,GAAG,EAAE,CAAC;AAAA,EACxC;AACA,SAAO;AACT;AAIA,SAAS,wBAAwB;AAC/B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,OAAO;AACzB,SAAO,UAAU,YAAY,CAAC,KAAK,UAAU;AAC/C;", "names": ["target", "result", "isEmpty", "isObject", "unflatten", "flatten", "match", "service", "path", "lang"]}