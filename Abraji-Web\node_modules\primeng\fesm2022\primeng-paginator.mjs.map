{"version": 3, "file": "primeng-paginator.mjs", "sources": ["../../src/app/components/paginator/paginator.ts", "../../src/app/components/paginator/primeng-paginator.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Input,\n    NgModule,\n    OnChanges,\n    OnInit,\n    Output,\n    QueryList,\n    SimpleChanges,\n    TemplateRef,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { Aria, PrimeNGConfig, PrimeTemplate, SelectItem, SharedModule } from 'primeng/api';\nimport { DropdownChangeEvent, DropdownModule } from 'primeng/dropdown';\nimport { AngleDoubleLeftIcon } from 'primeng/icons/angledoubleleft';\nimport { AngleDoubleRightIcon } from 'primeng/icons/angledoubleright';\nimport { AngleLeftIcon } from 'primeng/icons/angleleft';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { RippleModule } from 'primeng/ripple';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { PaginatorState } from './paginator.interface';\n/**\n * Paginator is a generic component to display content in paged format.\n * @group Components\n */\n@Component({\n    selector: 'p-paginator',\n    template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-paginator p-component'\" *ngIf=\"alwaysShow ? true : pageLinks && pageLinks.length > 1\" [attr.data-pc-section]=\"'paginator'\" [attr.data-pc-section]=\"'root'\">\n            <div class=\"p-paginator-left-content\" *ngIf=\"templateLeft\" [attr.data-pc-section]=\"'start'\">\n                <ng-container *ngTemplateOutlet=\"templateLeft; context: { $implicit: paginatorState }\"></ng-container>\n            </div>\n            <span class=\"p-paginator-current\" *ngIf=\"showCurrentPageReport\">{{ currentPageReport }}</span>\n            <button\n                *ngIf=\"showFirstLastIcon\"\n                type=\"button\"\n                [disabled]=\"isFirstPage() || empty()\"\n                (click)=\"changePageToFirst($event)\"\n                pRipple\n                class=\"p-paginator-first p-paginator-element p-link\"\n                [ngClass]=\"{ 'p-disabled': isFirstPage() || empty() }\"\n                [attr.aria-label]=\"getAriaLabel('firstPageLabel')\"\n            >\n                <AngleDoubleLeftIcon *ngIf=\"!firstPageLinkIconTemplate\" [styleClass]=\"'p-paginator-icon'\" />\n                <span class=\"p-paginator-icon\" *ngIf=\"firstPageLinkIconTemplate\">\n                    <ng-template *ngTemplateOutlet=\"firstPageLinkIconTemplate\"></ng-template>\n                </span>\n            </button>\n            <button\n                type=\"button\"\n                [disabled]=\"isFirstPage() || empty()\"\n                (click)=\"changePageToPrev($event)\"\n                pRipple\n                class=\"p-paginator-prev p-paginator-element p-link\"\n                [ngClass]=\"{ 'p-disabled': isFirstPage() || empty() }\"\n                [attr.aria-label]=\"getAriaLabel('prevPageLabel')\"\n            >\n                <AngleLeftIcon *ngIf=\"!previousPageLinkIconTemplate\" [styleClass]=\"'p-paginator-icon'\" />\n                <span class=\"p-paginator-icon\" *ngIf=\"previousPageLinkIconTemplate\">\n                    <ng-template *ngTemplateOutlet=\"previousPageLinkIconTemplate\"></ng-template>\n                </span>\n            </button>\n            <span class=\"p-paginator-pages\" *ngIf=\"showPageLinks\">\n                <button\n                    type=\"button\"\n                    *ngFor=\"let pageLink of pageLinks\"\n                    class=\"p-paginator-page p-paginator-element p-link\"\n                    [ngClass]=\"{ 'p-highlight': pageLink - 1 == getPage() }\"\n                    [attr.aria-label]=\"getPageAriaLabel(pageLink)\"\n                    [attr.aria-current]=\"pageLink - 1 == getPage() ? 'page' : undefined\"\n                    (click)=\"onPageLinkClick($event, pageLink - 1)\"\n                    pRipple\n                >\n                    {{ getLocalization(pageLink) }}\n                </button>\n            </span>\n            <p-dropdown\n                [options]=\"pageItems\"\n                [ngModel]=\"getPage()\"\n                *ngIf=\"showJumpToPageDropdown\"\n                [disabled]=\"empty()\"\n                [attr.aria-label]=\"getAriaLabel('jumpToPageDropdownLabel')\"\n                styleClass=\"p-paginator-page-options\"\n                (onChange)=\"onPageDropdownChange($event)\"\n                [appendTo]=\"dropdownAppendTo\"\n                [scrollHeight]=\"dropdownScrollHeight\"\n            >\n                <ng-template pTemplate=\"selectedItem\">{{ currentPageReport }}</ng-template>\n                <ng-container *ngIf=\"jumpToPageItemTemplate\">\n                    <ng-template let-item pTemplate=\"item\">\n                        <ng-container *ngTemplateOutlet=\"jumpToPageItemTemplate; context: { $implicit: item }\"> </ng-container>\n                    </ng-template>\n                </ng-container>\n                <ng-template pTemplate=\"dropdownicon\" *ngIf=\"dropdownIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"dropdownIconTemplate\"></ng-container>\n                </ng-template>\n            </p-dropdown>\n            <button\n                type=\"button\"\n                [disabled]=\"isLastPage() || empty()\"\n                (click)=\"changePageToNext($event)\"\n                pRipple\n                class=\"p-paginator-next p-paginator-element p-link\"\n                [ngClass]=\"{ 'p-disabled': isLastPage() || empty() }\"\n                [attr.aria-label]=\"getAriaLabel('nextPageLabel')\"\n            >\n                <AngleRightIcon *ngIf=\"!nextPageLinkIconTemplate\" [styleClass]=\"'p-paginator-icon'\" />\n                <span class=\"p-paginator-icon\" *ngIf=\"nextPageLinkIconTemplate\">\n                    <ng-template *ngTemplateOutlet=\"nextPageLinkIconTemplate\"></ng-template>\n                </span>\n            </button>\n            <button\n                *ngIf=\"showFirstLastIcon\"\n                type=\"button\"\n                [disabled]=\"isLastPage() || empty()\"\n                (click)=\"changePageToLast($event)\"\n                pRipple\n                class=\"p-paginator-last p-paginator-element p-link\"\n                [ngClass]=\"{ 'p-disabled': isLastPage() || empty() }\"\n                [attr.aria-label]=\"getAriaLabel('lastPageLabel')\"\n            >\n                <AngleDoubleRightIcon *ngIf=\"!lastPageLinkIconTemplate\" [styleClass]=\"'p-paginator-icon'\" />\n                <span class=\"p-paginator-icon\" *ngIf=\"lastPageLinkIconTemplate\">\n                    <ng-template *ngTemplateOutlet=\"lastPageLinkIconTemplate\"></ng-template>\n                </span>\n            </button>\n            <p-inputNumber *ngIf=\"showJumpToPageInput\" [ngModel]=\"currentPage()\" class=\"p-paginator-page-input\" [disabled]=\"empty()\" (ngModelChange)=\"changePage($event - 1)\"></p-inputNumber>\n            <p-dropdown\n                [options]=\"rowsPerPageItems\"\n                [(ngModel)]=\"rows\"\n                *ngIf=\"rowsPerPageOptions\"\n                styleClass=\"p-paginator-rpp-options\"\n                [disabled]=\"empty()\"\n                (onChange)=\"onRppChange($event)\"\n                [appendTo]=\"dropdownAppendTo\"\n                [scrollHeight]=\"dropdownScrollHeight\"\n                [ariaLabel]=\"getAriaLabel('rowsPerPageLabel')\"\n            >\n                <ng-container *ngIf=\"dropdownItemTemplate\">\n                    <ng-template let-item pTemplate=\"item\">\n                        <ng-container *ngTemplateOutlet=\"dropdownItemTemplate; context: { $implicit: item }\"> </ng-container>\n                    </ng-template>\n                </ng-container>\n                <ng-template pTemplate=\"dropdownicon\" *ngIf=\"dropdownIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"dropdownIconTemplate\"></ng-container>\n                </ng-template>\n            </p-dropdown>\n            <div class=\"p-paginator-right-content\" *ngIf=\"templateRight\" [attr.data-pc-section]=\"'end'\">\n                <ng-container *ngTemplateOutlet=\"templateRight; context: { $implicit: paginatorState }\"></ng-container>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./paginator.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Paginator implements OnInit, AfterContentInit, OnChanges {\n    /**\n     * Number of page links to display.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) pageLinkSize: number = 5;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Whether to show it even there is only one page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) alwaysShow: boolean = true;\n    /**\n     * Target element to attach the dropdown overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() dropdownAppendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Template instance to inject into the left side of the paginator.\n     * @param {PaginatorState} context - Paginator state.\n     * @group Props\n     */\n    @Input() templateLeft: TemplateRef<PaginatorState> | undefined;\n    /**\n     * Template instance to inject into the right side of the paginator.\n     * @param {PaginatorState} context - Paginator state.\n     * @group Props\n     */\n    @Input() templateRight: TemplateRef<PaginatorState> | undefined;\n    /**\n     * Target element to attach the dropdown overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Dropdown height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    @Input() dropdownScrollHeight: string = '200px';\n    /**\n     * Template of the current page report element. Available placeholders are {currentPage},{totalPages},{rows},{first},{last} and {totalRecords}\n     * @group Props\n     */\n    @Input() currentPageReportTemplate: string = '{currentPage} of {totalPages}';\n    /**\n     * Whether to display current page report.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showCurrentPageReport: boolean | undefined;\n    /**\n     * When enabled, icons are displayed on paginator to go first and last page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showFirstLastIcon: boolean = true;\n    /**\n     * Number of total records.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) totalRecords: number = 0;\n    /**\n     * Data count to display per page.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) rows: number = 0;\n    /**\n     * Array of integer/object values to display inside rows per page dropdown. A object that have 'showAll' key can be added to it to show all data. Exp; [10,20,30,{showAll:'All'}]\n     * @group Props\n     */\n    @Input() rowsPerPageOptions: any[] | undefined;\n    /**\n     * Whether to display a dropdown to navigate to any page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showJumpToPageDropdown: boolean | undefined;\n    /**\n     * Whether to display a input to navigate to any page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showJumpToPageInput: boolean | undefined;\n    /**\n     * Template instance to inject into the jump to page dropdown item inside in the paginator.\n     * @param {Object} context - item instance.\n     * @group Props\n     */\n    @Input() jumpToPageItemTemplate: TemplateRef<{ $implicit: any }> | undefined;\n    /**\n     * Whether to show page links.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showPageLinks: boolean = true;\n    /**\n     * Locale to be used in formatting.\n     * @group Props\n     */\n    @Input() locale: string | undefined;\n    /**\n     * Template instance to inject into the rows per page dropdown item inside in the paginator.\n     * @param {Object} context - item instance.\n     * @group Props\n     */\n    @Input() dropdownItemTemplate: TemplateRef<{ $implicit: any }> | undefined;\n    /**\n     * Zero-relative number of the first row to be displayed.\n     * @group Props\n     */\n    @Input() get first(): number {\n        return this._first;\n    }\n    set first(val: number) {\n        this._first = val;\n    }\n    /**\n     * Callback to invoke when page changes, the event object contains information about the new state.\n     * @param {PaginatorState} event - Paginator state.\n     * @group Emits\n     */\n    @Output() onPageChange: EventEmitter<PaginatorState> = new EventEmitter<PaginatorState>();\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<any>>;\n\n    dropdownIconTemplate: Nullable<TemplateRef<any>>;\n\n    firstPageLinkIconTemplate: Nullable<TemplateRef<any>>;\n\n    previousPageLinkIconTemplate: Nullable<TemplateRef<any>>;\n\n    lastPageLinkIconTemplate: Nullable<TemplateRef<any>>;\n\n    nextPageLinkIconTemplate: Nullable<TemplateRef<any>>;\n\n    pageLinks: number[] | undefined;\n\n    pageItems: SelectItem[] | undefined;\n\n    rowsPerPageItems: SelectItem[] | undefined;\n\n    paginatorState: any;\n\n    _first: number = 0;\n\n    _page: number = 0;\n\n    constructor(private cd: ChangeDetectorRef, private config: PrimeNGConfig) {}\n\n    ngOnInit() {\n        this.updatePaginatorState();\n    }\n\n    getAriaLabel(labelType: keyof Aria): string | undefined {\n        return this.config.translation.aria ? this.config.translation.aria[labelType] : undefined;\n    }\n\n    getPageAriaLabel(value: number): string | undefined {\n        return this.config.translation.aria ? this.config.translation.aria.pageLabel.replace(/{page}/g, `${value}`) : undefined;\n    }\n\n    getLocalization(digit: number): string {\n        const numerals = [...new Intl.NumberFormat(this.locale, { useGrouping: false }).format(9876543210)].reverse();\n        const index = new Map(numerals.map((d, i) => [i, d]));\n        if (digit > 9) {\n            const numbers = String(digit).split('');\n            return numbers.map((number) => index.get(Number(number))).join('');\n        } else {\n            return index.get(digit);\n        }\n    }\n\n    ngAfterContentInit(): void {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'dropdownicon':\n                    this.dropdownIconTemplate = item.template;\n                    break;\n\n                case 'firstpagelinkicon':\n                    this.firstPageLinkIconTemplate = item.template;\n                    break;\n\n                case 'previouspagelinkicon':\n                    this.previousPageLinkIconTemplate = item.template;\n                    break;\n\n                case 'lastpagelinkicon':\n                    this.lastPageLinkIconTemplate = item.template;\n                    break;\n\n                case 'nextpagelinkicon':\n                    this.nextPageLinkIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngOnChanges(simpleChange: SimpleChanges): void {\n        if (simpleChange.totalRecords) {\n            this.updatePageLinks();\n            this.updatePaginatorState();\n            this.updateFirst();\n            this.updateRowsPerPageOptions();\n        }\n\n        if (simpleChange.first) {\n            this._first = simpleChange.first.currentValue;\n            this.updatePageLinks();\n            this.updatePaginatorState();\n        }\n\n        if (simpleChange.rows) {\n            this.updatePageLinks();\n            this.updatePaginatorState();\n        }\n\n        if (simpleChange.rowsPerPageOptions) {\n            this.updateRowsPerPageOptions();\n        }\n\n        if (simpleChange.pageLinkSize) {\n            this.updatePageLinks();\n        }\n    }\n\n    updateRowsPerPageOptions(): void {\n        if (this.rowsPerPageOptions) {\n            this.rowsPerPageItems = [];\n            for (let opt of this.rowsPerPageOptions) {\n                if (typeof opt == 'object' && opt['showAll']) {\n                    this.rowsPerPageItems.unshift({ label: opt['showAll'], value: this.totalRecords });\n                } else {\n                    this.rowsPerPageItems.push({ label: String(this.getLocalization(opt)), value: opt });\n                }\n            }\n        }\n    }\n\n    isFirstPage(): boolean {\n        return this.getPage() === 0;\n    }\n\n    isLastPage(): boolean {\n        return this.getPage() === this.getPageCount() - 1;\n    }\n\n    getPageCount(): number {\n        return Math.ceil(this.totalRecords / this.rows);\n    }\n\n    calculatePageLinkBoundaries(): [number, number] {\n        let numberOfPages = this.getPageCount(),\n            visiblePages = Math.min(this.pageLinkSize, numberOfPages);\n\n        //calculate range, keep current in middle if necessary\n        let start = Math.max(0, Math.ceil(this.getPage() - visiblePages / 2)),\n            end = Math.min(numberOfPages - 1, start + visiblePages - 1);\n\n        //check when approaching to last page\n        var delta = this.pageLinkSize - (end - start + 1);\n        start = Math.max(0, start - delta);\n\n        return [start, end];\n    }\n\n    updatePageLinks(): void {\n        this.pageLinks = [];\n        let boundaries = this.calculatePageLinkBoundaries(),\n            start = boundaries[0],\n            end = boundaries[1];\n\n        for (let i = start; i <= end; i++) {\n            this.pageLinks.push(i + 1);\n        }\n\n        if (this.showJumpToPageDropdown) {\n            this.pageItems = [];\n            for (let i = 0; i < this.getPageCount(); i++) {\n                this.pageItems.push({ label: String(i + 1), value: i });\n            }\n        }\n    }\n\n    changePage(p: number): void {\n        var pc = this.getPageCount();\n\n        if (p >= 0 && p < pc) {\n            this._first = this.rows * p;\n            var state = {\n                page: p,\n                first: this.first,\n                rows: this.rows,\n                pageCount: pc\n            };\n            this.updatePageLinks();\n\n            this.onPageChange.emit(state);\n            this.updatePaginatorState();\n        }\n    }\n\n    updateFirst(): void {\n        const page = this.getPage();\n        if (page > 0 && this.totalRecords && this.first >= this.totalRecords) {\n            Promise.resolve(null).then(() => this.changePage(page - 1));\n        }\n    }\n\n    getPage(): number {\n        return Math.floor(this.first / this.rows);\n    }\n\n    changePageToFirst(event: Event): void {\n        if (!this.isFirstPage()) {\n            this.changePage(0);\n        }\n\n        event.preventDefault();\n    }\n\n    changePageToPrev(event: Event): void {\n        this.changePage(this.getPage() - 1);\n        event.preventDefault();\n    }\n\n    changePageToNext(event: Event): void {\n        this.changePage(this.getPage() + 1);\n        event.preventDefault();\n    }\n\n    changePageToLast(event: Event): void {\n        if (!this.isLastPage()) {\n            this.changePage(this.getPageCount() - 1);\n        }\n\n        event.preventDefault();\n    }\n\n    onPageLinkClick(event: Event, page: number): void {\n        this.changePage(page);\n        event.preventDefault();\n    }\n\n    onRppChange(event: Event): void {\n        this.changePage(this.getPage());\n    }\n\n    onPageDropdownChange(event: DropdownChangeEvent): void {\n        this.changePage(event.value);\n    }\n\n    updatePaginatorState(): void {\n        this.paginatorState = {\n            page: this.getPage(),\n            pageCount: this.getPageCount(),\n            rows: this.rows,\n            first: this.first,\n            totalRecords: this.totalRecords\n        };\n    }\n\n    empty(): boolean {\n        return this.getPageCount() === 0;\n    }\n\n    currentPage(): number {\n        return this.getPageCount() > 0 ? this.getPage() + 1 : 0;\n    }\n\n    get currentPageReport(): string {\n        return this.currentPageReportTemplate\n            .replace('{currentPage}', String(this.currentPage()))\n            .replace('{totalPages}', String(this.getPageCount()))\n            .replace('{first}', String(this.totalRecords > 0 ? this._first + 1 : 0))\n            .replace('{last}', String(Math.min(this._first + this.rows, this.totalRecords)))\n            .replace('{rows}', String(this.rows))\n            .replace('{totalRecords}', String(this.totalRecords));\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, DropdownModule, InputNumberModule, FormsModule, SharedModule, RippleModule, AngleDoubleLeftIcon, AngleDoubleRightIcon, AngleLeftIcon, AngleRightIcon],\n    exports: [Paginator, DropdownModule, InputNumberModule, FormsModule, SharedModule],\n    declarations: [Paginator]\n})\nexport class PaginatorModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAgCA;;;AAGG;MAuIU,SAAS,CAAA;AAuJE,IAAA,EAAA,CAAA;AAA+B,IAAA,MAAA,CAAA;AAtJnD;;;AAGG;IACoC,YAAY,GAAW,CAAC,CAAC;AAChE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;AACM,IAAA,gBAAgB,CAAgF;AACzG;;;;AAIG;AACM,IAAA,YAAY,CAA0C;AAC/D;;;;AAIG;AACM,IAAA,aAAa,CAA0C;AAChE;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;IACM,oBAAoB,GAAW,OAAO,CAAC;AAChD;;;AAGG;IACM,yBAAyB,GAAW,+BAA+B,CAAC;AAC7E;;;AAGG;AACqC,IAAA,qBAAqB,CAAsB;AACnF;;;AAGG;IACqC,iBAAiB,GAAY,IAAI,CAAC;AAC1E;;;AAGG;IACoC,YAAY,GAAW,CAAC,CAAC;AAChE;;;AAGG;IACoC,IAAI,GAAW,CAAC,CAAC;AACxD;;;AAGG;AACM,IAAA,kBAAkB,CAAoB;AAC/C;;;AAGG;AACqC,IAAA,sBAAsB,CAAsB;AACpF;;;AAGG;AACqC,IAAA,mBAAmB,CAAsB;AACjF;;;;AAIG;AACM,IAAA,sBAAsB,CAA8C;AAC7E;;;AAGG;IACqC,aAAa,GAAY,IAAI,CAAC;AACtE;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;;AAIG;AACM,IAAA,oBAAoB,CAA8C;AAC3E;;;AAGG;AACH,IAAA,IAAa,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;IACD,IAAI,KAAK,CAAC,GAAW,EAAA;AACjB,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;KACrB;AACD;;;;AAIG;AACO,IAAA,YAAY,GAAiC,IAAI,YAAY,EAAkB,CAAC;AAE1D,IAAA,SAAS,CAA2B;AAEpE,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,yBAAyB,CAA6B;AAEtD,IAAA,4BAA4B,CAA6B;AAEzD,IAAA,wBAAwB,CAA6B;AAErD,IAAA,wBAAwB,CAA6B;AAErD,IAAA,SAAS,CAAuB;AAEhC,IAAA,SAAS,CAA2B;AAEpC,IAAA,gBAAgB,CAA2B;AAE3C,IAAA,cAAc,CAAM;IAEpB,MAAM,GAAW,CAAC,CAAC;IAEnB,KAAK,GAAW,CAAC,CAAC;IAElB,WAAoB,CAAA,EAAqB,EAAU,MAAqB,EAAA;QAApD,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAE5E,QAAQ,GAAA;QACJ,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;AAED,IAAA,YAAY,CAAC,SAAqB,EAAA;QAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;KAC7F;AAED,IAAA,gBAAgB,CAAC,KAAa,EAAA;AAC1B,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,CAAA,EAAG,KAAK,CAAA,CAAE,CAAC,GAAG,SAAS,CAAC;KAC3H;AAED,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC9G,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACxC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtE,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;KACJ;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,mBAAmB;AACpB,oBAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC/C,MAAM;AAEV,gBAAA,KAAK,sBAAsB;AACvB,oBAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClD,MAAM;AAEV,gBAAA,KAAK,kBAAkB;AACnB,oBAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC9C,MAAM;AAEV,gBAAA,KAAK,kBAAkB;AACnB,oBAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC9C,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,WAAW,CAAC,YAA2B,EAAA;QACnC,IAAI,YAAY,CAAC,YAAY,EAAE;YAC3B,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACnC,SAAA;QAED,IAAI,YAAY,CAAC,KAAK,EAAE;YACpB,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC;YAC9C,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,SAAA;QAED,IAAI,YAAY,CAAC,IAAI,EAAE;YACnB,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,SAAA;QAED,IAAI,YAAY,CAAC,kBAAkB,EAAE;YACjC,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACnC,SAAA;QAED,IAAI,YAAY,CAAC,YAAY,EAAE;YAC3B,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,wBAAwB,GAAA;QACpB,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAC3B,YAAA,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACrC,IAAI,OAAO,GAAG,IAAI,QAAQ,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE;oBAC1C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;AACtF,iBAAA;AAAM,qBAAA;oBACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AACxF,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAC/B;IAED,UAAU,GAAA;QACN,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;KACrD;IAED,YAAY,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;KACnD;IAED,2BAA2B,GAAA;AACvB,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,EAAE,EACnC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;;AAG9D,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,EACjE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC;;AAGhE,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;QAClD,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,CAAC;AAEnC,QAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACvB;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,UAAU,GAAG,IAAI,CAAC,2BAA2B,EAAE,EAC/C,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,EACrB,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAExB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9B,SAAA;QAED,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACpB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3D,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,CAAS,EAAA;AAChB,QAAA,IAAI,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAE7B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAC5B,YAAA,IAAI,KAAK,GAAG;AACR,gBAAA,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,gBAAA,SAAS,EAAE,EAAE;aAChB,CAAC;YACF,IAAI,CAAC,eAAe,EAAE,CAAC;AAEvB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AAC5B,QAAA,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;YAClE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D,SAAA;KACJ;IAED,OAAO,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7C;AAED,IAAA,iBAAiB,CAAC,KAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACtB,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAY,EAAA;QACzB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACpC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAY,EAAA;QACzB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACpC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAY,EAAA;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC;AAC5C,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,eAAe,CAAC,KAAY,EAAE,IAAY,EAAA;AACtC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;QACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;KACnC;AAED,IAAA,oBAAoB,CAAC,KAA0B,EAAA;AAC3C,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAChC;IAED,oBAAoB,GAAA;QAChB,IAAI,CAAC,cAAc,GAAG;AAClB,YAAA,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE;AACpB,YAAA,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,YAAY,EAAE,IAAI,CAAC,YAAY;SAClC,CAAC;KACL;IAED,KAAK,GAAA;AACD,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;KACpC;IAED,WAAW,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;KAC3D;AAED,IAAA,IAAI,iBAAiB,GAAA;QACjB,OAAO,IAAI,CAAC,yBAAyB;aAChC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;aACpD,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;aACpD,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aACvE,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;aAC/E,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACpC,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KAC7D;uGA9XQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAT,SAAS,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAKE,eAAe,CAef,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,6SAqChB,gBAAgB,CAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAKhB,gBAAgB,CAKhB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,eAAe,0BAKf,eAAe,CAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAUf,gBAAgB,CAKhB,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAAA,gBAAgB,uGAWhB,gBAAgB,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA6BnB,aAAa,EAnQpB,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4HT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,6gBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,cAAA,EAAA,QAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,aAAA,EAAA,aAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,SAAA,EAAA,SAAA,EAAA,SAAA,EAAA,UAAA,EAAA,cAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,WAAA,EAAA,cAAA,EAAA,SAAA,EAAA,aAAA,EAAA,aAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,qBAAA,EAAA,kBAAA,EAAA,OAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,cAAA,EAAA,MAAA,EAAA,eAAA,EAAA,uBAAA,EAAA,sBAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,sBAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,uBAAA,EAAA,aAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,SAAA,EAAA,QAAA,EAAA,SAAA,EAAA,QAAA,EAAA,QAAA,EAAA,SAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,QAAA,EAAA,cAAA,EAAA,SAAA,EAAA,YAAA,EAAA,OAAA,EAAA,aAAA,EAAA,MAAA,EAAA,WAAA,EAAA,UAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,cAAA,EAAA,MAAA,EAAA,UAAA,EAAA,cAAA,EAAA,KAAA,EAAA,KAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,qBAAA,EAAA,UAAA,EAAA,MAAA,EAAA,YAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,QAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,WAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,QAAA,EAAA,WAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,qDAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,SAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA0YmG,mBAAmB,CAAE,EAAA,QAAA,EAAA,qBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,oBAAoB,CAAE,EAAA,QAAA,EAAA,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,aAAa,+EAAE,cAAc,CAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAlYnK,SAAS,EAAA,UAAA,EAAA,CAAA;kBAtIrB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACb,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4HT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,6gBAAA,CAAA,EAAA,CAAA;kHAOsC,YAAY,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAMG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAMG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,yBAAyB,EAAA,CAAA;sBAAjC,KAAK;gBAKkC,qBAAqB,EAAA,CAAA;sBAA5D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,YAAY,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKkC,sBAAsB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,mBAAmB,EAAA,CAAA;sBAA1D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM7B,sBAAsB,EAAA,CAAA;sBAA9B,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAMG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAWI,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAuQrB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAtYf,YAAA,EAAA,CAAA,SAAS,CAkYR,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,aAAa,EAAE,cAAc,CAlYnK,EAAA,OAAA,EAAA,CAAA,SAAS,EAmYG,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGxE,eAAe,EAAA,OAAA,EAAA,CAJd,YAAY,EAAE,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,aAAa,EAAE,cAAc,EACvJ,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGxE,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,aAAa,EAAE,cAAc,CAAC;oBAC7K,OAAO,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,CAAC;oBAClF,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;AC/iBD;;AAEG;;;;"}