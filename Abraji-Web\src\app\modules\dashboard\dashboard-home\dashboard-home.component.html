<!-- API Connection Test -->
<div class="rounded-lg border border-gray-300 dark:border-gray-600 mb-4 p-4 bg-white dark:bg-gray-800">
  <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">🔗 API Connection Test</h3>

  <div *ngIf="isLoading" class="flex items-center space-x-2">
    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
    <span class="text-gray-600 dark:text-gray-300">Testing API connection...</span>
  </div>

  <div *ngIf="!isLoading && apiTestResult" class="space-y-2">
    <div class="flex items-center space-x-2">
      <span [class]="apiTestResult.success ? 'text-green-600' : 'text-red-600'">
        {{ apiTestResult.success ? '✅' : '❌' }}
      </span>
      <span class="font-medium">{{ apiTestResult.type }} Test:</span>
      <span [class]="apiTestResult.success ? 'text-green-600' : 'text-red-600'">
        {{ apiTestResult.success ? 'Success' : 'Failed' }}
      </span>
    </div>

    <div *ngIf="apiTestResult.success" class="bg-green-50 dark:bg-green-900/20 p-3 rounded border border-green-200 dark:border-green-800">
      <pre class="text-sm text-green-800 dark:text-green-200">{{ apiTestResult.data | json }}</pre>
    </div>

    <div *ngIf="!apiTestResult.success" class="bg-red-50 dark:bg-red-900/20 p-3 rounded border border-red-200 dark:border-red-800">
      <pre class="text-sm text-red-800 dark:text-red-200">{{ apiTestResult.error | json }}</pre>
    </div>
  </div>
</div>

<div class="rounded-lg  border-gray-300 dark:border-gray-600 mb-4">
  <app-stats></app-stats>
</div>
