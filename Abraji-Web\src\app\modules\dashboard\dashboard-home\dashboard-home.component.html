<!-- API Connection Test -->
<div class="rounded-lg border border-gray-300 dark:border-gray-600 mb-4 p-4 bg-white dark:bg-gray-800">
  <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">🔗 API Connection Test</h3>

  <div *ngIf="isLoading" class="flex items-center space-x-2">
    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
    <span class="text-gray-600 dark:text-gray-300">Testing API connection...</span>
  </div>

  <div *ngIf="!isLoading && apiTestResult" class="space-y-2">
    <div class="flex items-center space-x-2">
      <span [class]="apiTestResult.success ? 'text-green-600' : 'text-red-600'">
        {{ apiTestResult.success ? '✅' : '❌' }}
      </span>
      <span class="font-medium">{{ apiTestResult.type }} Test:</span>
      <span [class]="apiTestResult.success ? 'text-green-600' : 'text-red-600'">
        {{ apiTestResult.success ? 'Success' : 'Failed' }}
      </span>
    </div>

    <div *ngIf="apiTestResult.success" class="bg-green-50 dark:bg-green-900/20 p-3 rounded border border-green-200 dark:border-green-800">
      <pre class="text-sm text-green-800 dark:text-green-200">{{ apiTestResult.data | json }}</pre>
    </div>

    <div *ngIf="!apiTestResult.success" class="bg-red-50 dark:bg-red-900/20 p-3 rounded border border-red-200 dark:border-red-800">
      <pre class="text-sm text-red-800 dark:text-red-200">{{ apiTestResult.error | json }}</pre>
    </div>
  </div>

  <!-- Test Buttons -->
  <div class="mt-4 space-y-2">
    <div class="space-x-2">
      <button
        (click)="testApiConnection()"
        [disabled]="isLoading"
        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50">
        🔄 Test API Connection
      </button>

      <button
        (click)="testLoginEndpoint()"
        [disabled]="isLoading"
        class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50">
        🔐 Test Real Login
      </button>
    </div>

    <div class="space-x-2">
      <button
        (click)="testDashboardEndpoint()"
        [disabled]="isLoading"
        class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50">
        📊 Test Dashboard
      </button>

      <button
        (click)="testCardsEndpoint()"
        [disabled]="isLoading"
        class="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50">
        💳 Test Cards
      </button>
    </div>
  </div>

  <!-- Dashboard Data Display -->
  <div *ngIf="dashboardData" class="mt-6 bg-gray-50 dark:bg-gray-900/20 p-4 rounded border">
    <h4 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">📊 Dashboard Statistics</h4>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="bg-white dark:bg-gray-800 p-3 rounded shadow">
        <div class="text-sm text-gray-600 dark:text-gray-400">Total Users</div>
        <div class="text-xl font-bold text-blue-600">{{ dashboardData.data?.users_count || 0 }}</div>
      </div>
      <div class="bg-white dark:bg-gray-800 p-3 rounded shadow">
        <div class="text-sm text-gray-600 dark:text-gray-400">Active Users</div>
        <div class="text-xl font-bold text-green-600">{{ dashboardData.data?.active_users || 0 }}</div>
      </div>
      <div class="bg-white dark:bg-gray-800 p-3 rounded shadow">
        <div class="text-sm text-gray-600 dark:text-gray-400">Online Users</div>
        <div class="text-xl font-bold text-purple-600">{{ dashboardData.data?.online_users || 0 }}</div>
      </div>
      <div class="bg-white dark:bg-gray-800 p-3 rounded shadow">
        <div class="text-sm text-gray-600 dark:text-gray-400">Balance</div>
        <div class="text-xl font-bold text-orange-600">${{ dashboardData.data?.balance || '0.00' }}</div>
      </div>
    </div>
  </div>
</div>

<!-- Stats component temporarily disabled for testing -->
<!-- <div class="rounded-lg  border-gray-300 dark:border-gray-600 mb-4">
  <app-stats></app-stats>
</div> -->
