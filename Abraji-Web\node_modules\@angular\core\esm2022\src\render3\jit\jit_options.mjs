let jitOptions = null;
export function setJitOptions(options) {
    if (jitOptions !== null) {
        if (options.defaultEncapsulation !== jitOptions.defaultEncapsulation) {
            ngDevMode &&
                console.error('Provided value for `defaultEncapsulation` can not be changed once it has been set.');
            return;
        }
        if (options.preserveWhitespaces !== jitOptions.preserveWhitespaces) {
            ngDevMode &&
                console.error('Provided value for `preserveWhitespaces` can not be changed once it has been set.');
            return;
        }
    }
    jitOptions = options;
}
export function getJitOptions() {
    return jitOptions;
}
export function resetJitOptions() {
    jitOptions = null;
}
//# sourceMappingURL=data:application/json;base64,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