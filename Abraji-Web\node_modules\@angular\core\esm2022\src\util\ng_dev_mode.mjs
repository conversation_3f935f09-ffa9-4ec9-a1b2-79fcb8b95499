/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { global } from './global';
export function ngDevModeResetPerfCounters() {
    const locationString = typeof location !== 'undefined' ? location.toString() : '';
    const newCounters = {
        namedConstructors: locationString.indexOf('ngDevMode=namedConstructors') != -1,
        firstCreatePass: 0,
        tNode: 0,
        tView: 0,
        rendererCreateTextNode: 0,
        rendererSetText: 0,
        rendererCreateElement: 0,
        rendererAddEventListener: 0,
        rendererSetAttribute: 0,
        rendererRemoveAttribute: 0,
        rendererSetProperty: 0,
        rendererSetClassName: 0,
        rendererAddClass: 0,
        rendererRemoveClass: 0,
        rendererSetStyle: 0,
        rendererRemoveStyle: 0,
        rendererDestroy: 0,
        rendererDestroyNode: 0,
        rendererMoveNode: 0,
        rendererRemoveNode: 0,
        rendererAppendChild: 0,
        rendererInsertBefore: 0,
        rendererCreateComment: 0,
        hydratedNodes: 0,
        hydratedComponents: 0,
        dehydratedViewsRemoved: 0,
        dehydratedViewsCleanupRuns: 0,
        componentsSkippedHydration: 0,
    };
    // Make sure to refer to ngDevMode as ['ngDevMode'] for closure.
    const allowNgDevModeTrue = locationString.indexOf('ngDevMode=false') === -1;
    if (!allowNgDevModeTrue) {
        global['ngDevMode'] = false;
    }
    else {
        if (typeof global['ngDevMode'] !== 'object') {
            global['ngDevMode'] = {};
        }
        Object.assign(global['ngDevMode'], newCounters);
    }
    return newCounters;
}
/**
 * This function checks to see if the `ngDevMode` has been set. If yes,
 * then we honor it, otherwise we default to dev mode with additional checks.
 *
 * The idea is that unless we are doing production build where we explicitly
 * set `ngDevMode == false` we should be helping the developer by providing
 * as much early warning and errors as possible.
 *
 * `ɵɵdefineComponent` is guaranteed to have been called before any component template functions
 * (and thus Ivy instructions), so a single initialization there is sufficient to ensure ngDevMode
 * is defined for the entire instruction set.
 *
 * When checking `ngDevMode` on toplevel, always init it before referencing it
 * (e.g. `((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode())`), otherwise you can
 *  get a `ReferenceError` like in https://github.com/angular/angular/issues/31595.
 *
 * Details on possible values for `ngDevMode` can be found on its docstring.
 *
 * NOTE:
 * - changes to the `ngDevMode` name must be synced with `compiler-cli/src/tooling.ts`.
 */
export function initNgDevMode() {
    // The below checks are to ensure that calling `initNgDevMode` multiple times does not
    // reset the counters.
    // If the `ngDevMode` is not an object, then it means we have not created the perf counters
    // yet.
    if (typeof ngDevMode === 'undefined' || ngDevMode) {
        if (typeof ngDevMode !== 'object' || Object.keys(ngDevMode).length === 0) {
            ngDevModeResetPerfCounters();
        }
        return typeof ngDevMode !== 'undefined' && !!ngDevMode;
    }
    return false;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibmdfZGV2X21vZGUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy91dGlsL25nX2Rldl9tb2RlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sRUFBQyxNQUFNLEVBQUMsTUFBTSxVQUFVLENBQUM7QUFtRGhDLE1BQU0sVUFBVSwwQkFBMEI7SUFDeEMsTUFBTSxjQUFjLEdBQUcsT0FBTyxRQUFRLEtBQUssV0FBVyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztJQUNsRixNQUFNLFdBQVcsR0FBMEI7UUFDekMsaUJBQWlCLEVBQUUsY0FBYyxDQUFDLE9BQU8sQ0FBQyw2QkFBNkIsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUM5RSxlQUFlLEVBQUUsQ0FBQztRQUNsQixLQUFLLEVBQUUsQ0FBQztRQUNSLEtBQUssRUFBRSxDQUFDO1FBQ1Isc0JBQXNCLEVBQUUsQ0FBQztRQUN6QixlQUFlLEVBQUUsQ0FBQztRQUNsQixxQkFBcUIsRUFBRSxDQUFDO1FBQ3hCLHdCQUF3QixFQUFFLENBQUM7UUFDM0Isb0JBQW9CLEVBQUUsQ0FBQztRQUN2Qix1QkFBdUIsRUFBRSxDQUFDO1FBQzFCLG1CQUFtQixFQUFFLENBQUM7UUFDdEIsb0JBQW9CLEVBQUUsQ0FBQztRQUN2QixnQkFBZ0IsRUFBRSxDQUFDO1FBQ25CLG1CQUFtQixFQUFFLENBQUM7UUFDdEIsZ0JBQWdCLEVBQUUsQ0FBQztRQUNuQixtQkFBbUIsRUFBRSxDQUFDO1FBQ3RCLGVBQWUsRUFBRSxDQUFDO1FBQ2xCLG1CQUFtQixFQUFFLENBQUM7UUFDdEIsZ0JBQWdCLEVBQUUsQ0FBQztRQUNuQixrQkFBa0IsRUFBRSxDQUFDO1FBQ3JCLG1CQUFtQixFQUFFLENBQUM7UUFDdEIsb0JBQW9CLEVBQUUsQ0FBQztRQUN2QixxQkFBcUIsRUFBRSxDQUFDO1FBQ3hCLGFBQWEsRUFBRSxDQUFDO1FBQ2hCLGtCQUFrQixFQUFFLENBQUM7UUFDckIsc0JBQXNCLEVBQUUsQ0FBQztRQUN6QiwwQkFBMEIsRUFBRSxDQUFDO1FBQzdCLDBCQUEwQixFQUFFLENBQUM7S0FDOUIsQ0FBQztJQUVGLGdFQUFnRTtJQUNoRSxNQUFNLGtCQUFrQixHQUFHLGNBQWMsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztJQUM1RSxJQUFJLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztRQUN4QixNQUFNLENBQUMsV0FBVyxDQUFDLEdBQUcsS0FBSyxDQUFDO0lBQzlCLENBQUM7U0FBTSxDQUFDO1FBQ04sSUFBSSxPQUFPLE1BQU0sQ0FBQyxXQUFXLENBQUMsS0FBSyxRQUFRLEVBQUUsQ0FBQztZQUM1QyxNQUFNLENBQUMsV0FBVyxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQzNCLENBQUM7UUFDRCxNQUFNLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsRUFBRSxXQUFXLENBQUMsQ0FBQztJQUNsRCxDQUFDO0lBQ0QsT0FBTyxXQUFXLENBQUM7QUFDckIsQ0FBQztBQUVEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztHQW9CRztBQUNILE1BQU0sVUFBVSxhQUFhO0lBQzNCLHNGQUFzRjtJQUN0RixzQkFBc0I7SUFDdEIsMkZBQTJGO0lBQzNGLE9BQU87SUFDUCxJQUFJLE9BQU8sU0FBUyxLQUFLLFdBQVcsSUFBSSxTQUFTLEVBQUUsQ0FBQztRQUNsRCxJQUFJLE9BQU8sU0FBUyxLQUFLLFFBQVEsSUFBSSxNQUFNLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztZQUN6RSwwQkFBMEIsRUFBRSxDQUFDO1FBQy9CLENBQUM7UUFDRCxPQUFPLE9BQU8sU0FBUyxLQUFLLFdBQVcsSUFBSSxDQUFDLENBQUMsU0FBUyxDQUFDO0lBQ3pELENBQUM7SUFDRCxPQUFPLEtBQUssQ0FBQztBQUNmLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHtnbG9iYWx9IGZyb20gJy4vZ2xvYmFsJztcblxuZGVjbGFyZSBnbG9iYWwge1xuICAvKipcbiAgICogVmFsdWVzIG9mIG5nRGV2TW9kZVxuICAgKiBEZXBlbmRpbmcgb24gdGhlIGN1cnJlbnQgc3RhdGUgb2YgdGhlIGFwcGxpY2F0aW9uLCBuZ0Rldk1vZGUgbWF5IGhhdmUgb25lIG9mIHNldmVyYWwgdmFsdWVzLlxuICAgKlxuICAgKiBGb3IgY29udmVuaWVuY2UsIHRoZSDigJx0cnV0aHnigJ0gdmFsdWUgd2hpY2ggZW5hYmxlcyBkZXYgbW9kZSBpcyBhbHNvIGFuIG9iamVjdCB3aGljaCBjb250YWluc1xuICAgKiBBbmd1bGFy4oCZcyBwZXJmb3JtYW5jZSBjb3VudGVycy4gVGhpcyBpcyBub3QgbmVjZXNzYXJ5LCBidXQgY3V0cyBkb3duIG9uIGJvaWxlcnBsYXRlIGZvciB0aGVcbiAgICogcGVyZiBjb3VudGVycy5cbiAgICpcbiAgICogbmdEZXZNb2RlIG1heSBhbHNvIGJlIHNldCB0byBmYWxzZS4gVGhpcyBjYW4gaGFwcGVuIGluIG9uZSBvZiBhIGZldyB3YXlzOlxuICAgKiAtIFRoZSB1c2VyIGV4cGxpY2l0bHkgc2V0cyBgd2luZG93Lm5nRGV2TW9kZSA9IGZhbHNlYCBzb21ld2hlcmUgaW4gdGhlaXIgYXBwLlxuICAgKiAtIFRoZSB1c2VyIGNhbGxzIGBlbmFibGVQcm9kTW9kZSgpYC5cbiAgICogLSBUaGUgVVJMIGNvbnRhaW5zIGEgYG5nRGV2TW9kZT1mYWxzZWAgdGV4dC5cbiAgICogRmluYWxseSwgbmdEZXZNb2RlIG1heSBub3QgaGF2ZSBiZWVuIGRlZmluZWQgYXQgYWxsLlxuICAgKi9cbiAgY29uc3QgbmdEZXZNb2RlOiBudWxsIHwgTmdEZXZNb2RlUGVyZkNvdW50ZXJzO1xuXG4gIGludGVyZmFjZSBOZ0Rldk1vZGVQZXJmQ291bnRlcnMge1xuICAgIG5hbWVkQ29uc3RydWN0b3JzOiBib29sZWFuO1xuICAgIGZpcnN0Q3JlYXRlUGFzczogbnVtYmVyO1xuICAgIHROb2RlOiBudW1iZXI7XG4gICAgdFZpZXc6IG51bWJlcjtcbiAgICByZW5kZXJlckNyZWF0ZVRleHROb2RlOiBudW1iZXI7XG4gICAgcmVuZGVyZXJTZXRUZXh0OiBudW1iZXI7XG4gICAgcmVuZGVyZXJDcmVhdGVFbGVtZW50OiBudW1iZXI7XG4gICAgcmVuZGVyZXJBZGRFdmVudExpc3RlbmVyOiBudW1iZXI7XG4gICAgcmVuZGVyZXJTZXRBdHRyaWJ1dGU6IG51bWJlcjtcbiAgICByZW5kZXJlclJlbW92ZUF0dHJpYnV0ZTogbnVtYmVyO1xuICAgIHJlbmRlcmVyU2V0UHJvcGVydHk6IG51bWJlcjtcbiAgICByZW5kZXJlclNldENsYXNzTmFtZTogbnVtYmVyO1xuICAgIHJlbmRlcmVyQWRkQ2xhc3M6IG51bWJlcjtcbiAgICByZW5kZXJlclJlbW92ZUNsYXNzOiBudW1iZXI7XG4gICAgcmVuZGVyZXJTZXRTdHlsZTogbnVtYmVyO1xuICAgIHJlbmRlcmVyUmVtb3ZlU3R5bGU6IG51bWJlcjtcbiAgICByZW5kZXJlckRlc3Ryb3k6IG51bWJlcjtcbiAgICByZW5kZXJlckRlc3Ryb3lOb2RlOiBudW1iZXI7XG4gICAgcmVuZGVyZXJNb3ZlTm9kZTogbnVtYmVyO1xuICAgIHJlbmRlcmVyUmVtb3ZlTm9kZTogbnVtYmVyO1xuICAgIHJlbmRlcmVyQXBwZW5kQ2hpbGQ6IG51bWJlcjtcbiAgICByZW5kZXJlckluc2VydEJlZm9yZTogbnVtYmVyO1xuICAgIHJlbmRlcmVyQ3JlYXRlQ29tbWVudDogbnVtYmVyO1xuICAgIGh5ZHJhdGVkTm9kZXM6IG51bWJlcjtcbiAgICBoeWRyYXRlZENvbXBvbmVudHM6IG51bWJlcjtcbiAgICBkZWh5ZHJhdGVkVmlld3NSZW1vdmVkOiBudW1iZXI7XG4gICAgZGVoeWRyYXRlZFZpZXdzQ2xlYW51cFJ1bnM6IG51bWJlcjtcbiAgICBjb21wb25lbnRzU2tpcHBlZEh5ZHJhdGlvbjogbnVtYmVyO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBuZ0Rldk1vZGVSZXNldFBlcmZDb3VudGVycygpOiBOZ0Rldk1vZGVQZXJmQ291bnRlcnMge1xuICBjb25zdCBsb2NhdGlvblN0cmluZyA9IHR5cGVvZiBsb2NhdGlvbiAhPT0gJ3VuZGVmaW5lZCcgPyBsb2NhdGlvbi50b1N0cmluZygpIDogJyc7XG4gIGNvbnN0IG5ld0NvdW50ZXJzOiBOZ0Rldk1vZGVQZXJmQ291bnRlcnMgPSB7XG4gICAgbmFtZWRDb25zdHJ1Y3RvcnM6IGxvY2F0aW9uU3RyaW5nLmluZGV4T2YoJ25nRGV2TW9kZT1uYW1lZENvbnN0cnVjdG9ycycpICE9IC0xLFxuICAgIGZpcnN0Q3JlYXRlUGFzczogMCxcbiAgICB0Tm9kZTogMCxcbiAgICB0VmlldzogMCxcbiAgICByZW5kZXJlckNyZWF0ZVRleHROb2RlOiAwLFxuICAgIHJlbmRlcmVyU2V0VGV4dDogMCxcbiAgICByZW5kZXJlckNyZWF0ZUVsZW1lbnQ6IDAsXG4gICAgcmVuZGVyZXJBZGRFdmVudExpc3RlbmVyOiAwLFxuICAgIHJlbmRlcmVyU2V0QXR0cmlidXRlOiAwLFxuICAgIHJlbmRlcmVyUmVtb3ZlQXR0cmlidXRlOiAwLFxuICAgIHJlbmRlcmVyU2V0UHJvcGVydHk6IDAsXG4gICAgcmVuZGVyZXJTZXRDbGFzc05hbWU6IDAsXG4gICAgcmVuZGVyZXJBZGRDbGFzczogMCxcbiAgICByZW5kZXJlclJlbW92ZUNsYXNzOiAwLFxuICAgIHJlbmRlcmVyU2V0U3R5bGU6IDAsXG4gICAgcmVuZGVyZXJSZW1vdmVTdHlsZTogMCxcbiAgICByZW5kZXJlckRlc3Ryb3k6IDAsXG4gICAgcmVuZGVyZXJEZXN0cm95Tm9kZTogMCxcbiAgICByZW5kZXJlck1vdmVOb2RlOiAwLFxuICAgIHJlbmRlcmVyUmVtb3ZlTm9kZTogMCxcbiAgICByZW5kZXJlckFwcGVuZENoaWxkOiAwLFxuICAgIHJlbmRlcmVySW5zZXJ0QmVmb3JlOiAwLFxuICAgIHJlbmRlcmVyQ3JlYXRlQ29tbWVudDogMCxcbiAgICBoeWRyYXRlZE5vZGVzOiAwLFxuICAgIGh5ZHJhdGVkQ29tcG9uZW50czogMCxcbiAgICBkZWh5ZHJhdGVkVmlld3NSZW1vdmVkOiAwLFxuICAgIGRlaHlkcmF0ZWRWaWV3c0NsZWFudXBSdW5zOiAwLFxuICAgIGNvbXBvbmVudHNTa2lwcGVkSHlkcmF0aW9uOiAwLFxuICB9O1xuXG4gIC8vIE1ha2Ugc3VyZSB0byByZWZlciB0byBuZ0Rldk1vZGUgYXMgWyduZ0Rldk1vZGUnXSBmb3IgY2xvc3VyZS5cbiAgY29uc3QgYWxsb3dOZ0Rldk1vZGVUcnVlID0gbG9jYXRpb25TdHJpbmcuaW5kZXhPZignbmdEZXZNb2RlPWZhbHNlJykgPT09IC0xO1xuICBpZiAoIWFsbG93TmdEZXZNb2RlVHJ1ZSkge1xuICAgIGdsb2JhbFsnbmdEZXZNb2RlJ10gPSBmYWxzZTtcbiAgfSBlbHNlIHtcbiAgICBpZiAodHlwZW9mIGdsb2JhbFsnbmdEZXZNb2RlJ10gIT09ICdvYmplY3QnKSB7XG4gICAgICBnbG9iYWxbJ25nRGV2TW9kZSddID0ge307XG4gICAgfVxuICAgIE9iamVjdC5hc3NpZ24oZ2xvYmFsWyduZ0Rldk1vZGUnXSwgbmV3Q291bnRlcnMpO1xuICB9XG4gIHJldHVybiBuZXdDb3VudGVycztcbn1cblxuLyoqXG4gKiBUaGlzIGZ1bmN0aW9uIGNoZWNrcyB0byBzZWUgaWYgdGhlIGBuZ0Rldk1vZGVgIGhhcyBiZWVuIHNldC4gSWYgeWVzLFxuICogdGhlbiB3ZSBob25vciBpdCwgb3RoZXJ3aXNlIHdlIGRlZmF1bHQgdG8gZGV2IG1vZGUgd2l0aCBhZGRpdGlvbmFsIGNoZWNrcy5cbiAqXG4gKiBUaGUgaWRlYSBpcyB0aGF0IHVubGVzcyB3ZSBhcmUgZG9pbmcgcHJvZHVjdGlvbiBidWlsZCB3aGVyZSB3ZSBleHBsaWNpdGx5XG4gKiBzZXQgYG5nRGV2TW9kZSA9PSBmYWxzZWAgd2Ugc2hvdWxkIGJlIGhlbHBpbmcgdGhlIGRldmVsb3BlciBieSBwcm92aWRpbmdcbiAqIGFzIG11Y2ggZWFybHkgd2FybmluZyBhbmQgZXJyb3JzIGFzIHBvc3NpYmxlLlxuICpcbiAqIGDJtcm1ZGVmaW5lQ29tcG9uZW50YCBpcyBndWFyYW50ZWVkIHRvIGhhdmUgYmVlbiBjYWxsZWQgYmVmb3JlIGFueSBjb21wb25lbnQgdGVtcGxhdGUgZnVuY3Rpb25zXG4gKiAoYW5kIHRodXMgSXZ5IGluc3RydWN0aW9ucyksIHNvIGEgc2luZ2xlIGluaXRpYWxpemF0aW9uIHRoZXJlIGlzIHN1ZmZpY2llbnQgdG8gZW5zdXJlIG5nRGV2TW9kZVxuICogaXMgZGVmaW5lZCBmb3IgdGhlIGVudGlyZSBpbnN0cnVjdGlvbiBzZXQuXG4gKlxuICogV2hlbiBjaGVja2luZyBgbmdEZXZNb2RlYCBvbiB0b3BsZXZlbCwgYWx3YXlzIGluaXQgaXQgYmVmb3JlIHJlZmVyZW5jaW5nIGl0XG4gKiAoZS5nLiBgKCh0eXBlb2YgbmdEZXZNb2RlID09PSAndW5kZWZpbmVkJyB8fCBuZ0Rldk1vZGUpICYmIGluaXROZ0Rldk1vZGUoKSlgKSwgb3RoZXJ3aXNlIHlvdSBjYW5cbiAqICBnZXQgYSBgUmVmZXJlbmNlRXJyb3JgIGxpa2UgaW4gaHR0cHM6Ly9naXRodWIuY29tL2FuZ3VsYXIvYW5ndWxhci9pc3N1ZXMvMzE1OTUuXG4gKlxuICogRGV0YWlscyBvbiBwb3NzaWJsZSB2YWx1ZXMgZm9yIGBuZ0Rldk1vZGVgIGNhbiBiZSBmb3VuZCBvbiBpdHMgZG9jc3RyaW5nLlxuICpcbiAqIE5PVEU6XG4gKiAtIGNoYW5nZXMgdG8gdGhlIGBuZ0Rldk1vZGVgIG5hbWUgbXVzdCBiZSBzeW5jZWQgd2l0aCBgY29tcGlsZXItY2xpL3NyYy90b29saW5nLnRzYC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGluaXROZ0Rldk1vZGUoKTogYm9vbGVhbiB7XG4gIC8vIFRoZSBiZWxvdyBjaGVja3MgYXJlIHRvIGVuc3VyZSB0aGF0IGNhbGxpbmcgYGluaXROZ0Rldk1vZGVgIG11bHRpcGxlIHRpbWVzIGRvZXMgbm90XG4gIC8vIHJlc2V0IHRoZSBjb3VudGVycy5cbiAgLy8gSWYgdGhlIGBuZ0Rldk1vZGVgIGlzIG5vdCBhbiBvYmplY3QsIHRoZW4gaXQgbWVhbnMgd2UgaGF2ZSBub3QgY3JlYXRlZCB0aGUgcGVyZiBjb3VudGVyc1xuICAvLyB5ZXQuXG4gIGlmICh0eXBlb2YgbmdEZXZNb2RlID09PSAndW5kZWZpbmVkJyB8fCBuZ0Rldk1vZGUpIHtcbiAgICBpZiAodHlwZW9mIG5nRGV2TW9kZSAhPT0gJ29iamVjdCcgfHwgT2JqZWN0LmtleXMobmdEZXZNb2RlKS5sZW5ndGggPT09IDApIHtcbiAgICAgIG5nRGV2TW9kZVJlc2V0UGVyZkNvdW50ZXJzKCk7XG4gICAgfVxuICAgIHJldHVybiB0eXBlb2YgbmdEZXZNb2RlICE9PSAndW5kZWZpbmVkJyAmJiAhIW5nRGV2TW9kZTtcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG4iXX0=