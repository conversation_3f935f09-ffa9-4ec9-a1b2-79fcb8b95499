import { Injectable } from '@angular/core';
import * as CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root',
})
export class EncryptionService {
  private encryptionKey = 'abcdefghijuklmno0123456789012345';
  private encryptionQRKey = 'abc';

  constructor() {}
  private encrypt(form: object): string {
    // Encrypt the form data
    const cypData: string = CryptoJS.AES.encrypt(
      JSON.stringify(form),
      this.encryptionKey
    ).toString();
    return cypData;
  }
  private decrypt(encryptedData: string): object | null {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
      const decryptedData = bytes.toString(CryptoJS.enc.Utf8);

      if (!decryptedData) {
        console.error('Decryption resulted in an empty string.');
        return null;
      }

      return JSON.parse(decryptedData);
    } catch (error) {
      console.error('Error during decryption or parsing JSON:', error);
      return null;
    }
  }
  generatePayload(form: object): string {
    const payload = this.encrypt(form);
    return payload;
  }

  getDecryptedPayload(encryptedPayload: string): object | null {
    return this.decrypt(encryptedPayload);
  }

  encryptQR(qrId: string): string {
    // Encrypt the form data
    const cypData: string = CryptoJS.AES.encrypt(
      JSON.stringify(qrId),
      this.encryptionQRKey
    ).toString();
    return cypData;
  }
  decryptQR(encryptedQrId: string): string | null {
    try {
      const bytes = CryptoJS.AES.decrypt(
        decodeURIComponent(encryptedQrId),
        this.encryptionQRKey
      );
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Error during decryption or parsing JSON:', error);
      return null;
    }
  }
}
