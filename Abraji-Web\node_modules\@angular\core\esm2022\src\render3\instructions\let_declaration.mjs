/*!
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { RuntimeError } from '../../errors';
import { performanceMarkFeature } from '../../util/performance';
import { HEADER_OFFSET } from '../interfaces/view';
import { getContextLView, getLView, getSelectedIndex, getTView, setCurrentTNode } from '../state';
import { load } from '../util/view_utils';
import { getOrCreateTNode } from './shared';
import { store } from './storage';
/** Object that indicates the value of a `@let` declaration that hasn't been initialized yet. */
const UNINITIALIZED_LET = {};
/**
 * Declares an `@let` at a specific data slot. Returns itself to allow chaining.
 *
 * @param index Index at which to declare the `@let`.
 *
 * @codeGenApi
 */
export function ɵɵdeclareLet(index) {
    const tView = getTView();
    const lView = getLView();
    const adjustedIndex = index + HEADER_OFFSET;
    const tNode = getOrCreateTNode(tView, adjustedIndex, 128 /* TNodeType.LetDeclaration */, null, null);
    setCurrentTNode(tNode, false);
    store(tView, lView, adjustedIndex, UNINITIALIZED_LET);
    return ɵɵdeclareLet;
}
/**
 * Instruction that stores the value of a `@let` declaration on the current view.
 * Returns the value to allow usage inside variable initializers.
 *
 * @codeGenApi
 */
export function ɵɵstoreLet(value) {
    performanceMarkFeature('NgLet');
    const tView = getTView();
    const lView = getLView();
    const index = getSelectedIndex();
    store(tView, lView, index, value);
    return value;
}
/**
 * Retrieves the value of a `@let` declaration defined in a parent view.
 *
 * @param index Index of the declaration within the view.
 *
 * @codeGenApi
 */
export function ɵɵreadContextLet(index) {
    const contextLView = getContextLView();
    const value = load(contextLView, HEADER_OFFSET + index);
    if (value === UNINITIALIZED_LET) {
        throw new RuntimeError(314 /* RuntimeErrorCode.UNINITIALIZED_LET_ACCESS */, ngDevMode && 'Attempting to access a @let declaration whose value is not available yet');
    }
    return value;
}
//# sourceMappingURL=data:application/json;base64,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