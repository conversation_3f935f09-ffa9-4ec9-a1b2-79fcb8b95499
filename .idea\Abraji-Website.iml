<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Authentication/app" isTestSource="false" packagePrefix="Modules\Authentication\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Authentication/database/factories" isTestSource="false" packagePrefix="Modules\Authentication\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Authentication/database/seeders" isTestSource="false" packagePrefix="Modules\Authentication\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Authentication/tests" isTestSource="true" packagePrefix="Modules\Authentication\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/tests/Feature" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/tests/Unit" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Invoices/app" isTestSource="false" packagePrefix="Modules\Invoices\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Invoices/database/factories" isTestSource="false" packagePrefix="Modules\Invoices\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Invoices/database/seeders" isTestSource="false" packagePrefix="Modules\Invoices\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Invoices/tests" isTestSource="true" packagePrefix="Modules\Invoices\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Users/<USER>" isTestSource="false" packagePrefix="Modules\Users\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Users/<USER>/factories" isTestSource="false" packagePrefix="Modules\Users\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Users/<USER>/seeders" isTestSource="false" packagePrefix="Modules\Users\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Users/<USER>" isTestSource="true" packagePrefix="Modules\Users\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Dashboard/app" isTestSource="false" packagePrefix="Modules\Dashboard\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Dashboard/database/factories" isTestSource="false" packagePrefix="Modules\Dashboard\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Dashboard/database/seeders" isTestSource="false" packagePrefix="Modules\Dashboard\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Dashboard/tests" isTestSource="true" packagePrefix="Modules\Dashboard\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Manager/app" isTestSource="false" packagePrefix="Modules\Manager\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Manager/database/factories" isTestSource="false" packagePrefix="Modules\Manager\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Manager/database/seeders" isTestSource="false" packagePrefix="Modules\Manager\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/Abraji-APIs/Modules/Manager/tests" isTestSource="true" packagePrefix="Modules\Manager\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Authentication/app" isTestSource="false" packagePrefix="Modules\Authentication\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Authentication/database/factories" isTestSource="false" packagePrefix="Modules\Authentication\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Authentication/database/seeders" isTestSource="false" packagePrefix="Modules\Authentication\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Authentication/tests" isTestSource="true" packagePrefix="Modules\Authentication\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Card/app" isTestSource="false" packagePrefix="Modules\Card\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Card/database/factories" isTestSource="false" packagePrefix="Modules\Card\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Card/database/seeders" isTestSource="false" packagePrefix="Modules\Card\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Card/tests" isTestSource="true" packagePrefix="Modules\Card\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Dashboard/app" isTestSource="false" packagePrefix="Modules\Dashboard\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Dashboard/database/factories" isTestSource="false" packagePrefix="Modules\Dashboard\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Dashboard/database/seeders" isTestSource="false" packagePrefix="Modules\Dashboard\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Dashboard/tests" isTestSource="true" packagePrefix="Modules\Dashboard\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Debts/app" isTestSource="false" packagePrefix="Modules\Debts\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Debts/database/factories" isTestSource="false" packagePrefix="Modules\Debts\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Debts/database/seeders" isTestSource="false" packagePrefix="Modules\Debts\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Debts/tests" isTestSource="true" packagePrefix="Modules\Debts\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Invoice/app" isTestSource="false" packagePrefix="Modules\Invoice\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Invoice/database/factories" isTestSource="false" packagePrefix="Modules\Invoice\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Invoice/database/seeders" isTestSource="false" packagePrefix="Modules\Invoice\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Invoice/tests" isTestSource="true" packagePrefix="Modules\Invoice\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Manager/app" isTestSource="false" packagePrefix="Modules\Manager\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Manager/database/factories" isTestSource="false" packagePrefix="Modules\Manager\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Manager/database/seeders" isTestSource="false" packagePrefix="Modules\Manager\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Manager/tests" isTestSource="true" packagePrefix="Modules\Manager\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Transaction/app" isTestSource="false" packagePrefix="Modules\Transaction\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Transaction/database/factories" isTestSource="false" packagePrefix="Modules\Transaction\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Transaction/database/seeders" isTestSource="false" packagePrefix="Modules\Transaction\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Transaction/tests" isTestSource="true" packagePrefix="Modules\Transaction\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Users/<USER>" isTestSource="false" packagePrefix="Modules\Users\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Users/<USER>/factories" isTestSource="false" packagePrefix="Modules\Users\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Users/<USER>/seeders" isTestSource="false" packagePrefix="Modules\Users\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Users/<USER>" isTestSource="true" packagePrefix="Modules\Users\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Wallet/app" isTestSource="false" packagePrefix="Modules\Wallet\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Wallet/database/factories" isTestSource="false" packagePrefix="Modules\Wallet\Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Wallet/database/seeders" isTestSource="false" packagePrefix="Modules\Wallet\Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/Modules/Wallet/tests" isTestSource="true" packagePrefix="Modules\Wallet\Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/tests/Feature" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/AbrajiAPIs/tests/Unit" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/cocur/slugify" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/cviebrock/eloquent-sluggable" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/laravel/pint" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/mcamara/laravel-localization" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/nwidart/laravel-modules" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/paragonie/constant_time_encoding" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phpseclib/phpseclib" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/spatie/laravel-permission" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/clock" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/Abraji-APIs/vendor/wikimedia/composer-merge-plugin" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/cocur/slugify" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/cviebrock/eloquent-sluggable" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/laravel/pint" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/mcamara/laravel-localization" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/nwidart/laravel-modules" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/paragonie/constant_time_encoding" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phpseclib/phpseclib" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/spatie/laravel-permission" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/clock" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/AbrajiAPIs/vendor/wikimedia/composer-merge-plugin" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>