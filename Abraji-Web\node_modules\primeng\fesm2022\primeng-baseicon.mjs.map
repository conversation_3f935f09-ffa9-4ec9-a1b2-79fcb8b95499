{"version": 3, "file": "primeng-baseicon.mjs", "sources": ["../../src/app/components/baseicon/baseicon.ts", "../../src/app/components/baseicon/primeng-baseicon.ts"], "sourcesContent": ["import { Component, Input, ChangeDetectionStrategy, ViewEncapsulation, ElementRef, HostBinding, booleanAttribute } from '@angular/core';\nimport { ObjectUtils } from 'primeng/utils';\n\n@Component({\n    template: ` <ng-content></ng-content> `,\n    standalone: true,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element p-icon-wrapper'\n    }\n})\nexport class BaseIcon {\n    @Input() label: string;\n\n    @Input({ transform: booleanAttribute }) spin: boolean = false;\n\n    @Input() styleClass: string;\n\n    role: string;\n\n    ariaLabel: string;\n\n    ariaHidden: boolean;\n\n    ngOnInit() {\n        this.getAttributes();\n    }\n\n    getAttributes() {\n        const isLabelEmpty = ObjectUtils.isEmpty(this.label);\n        this.role = !isLabelEmpty ? 'img' : undefined;\n        this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n        this.ariaHidden = isLabelEmpty;\n    }\n\n    getClassNames() {\n        return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n    }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;MAYa,QAAQ,CAAA;AACR,IAAA,KAAK,CAAS;IAEiB,IAAI,GAAY,KAAK,CAAC;AAErD,IAAA,UAAU,CAAS;AAE5B,IAAA,IAAI,CAAS;AAEb,IAAA,SAAS,CAAS;AAElB,IAAA,UAAU,CAAU;IAEpB,QAAQ,GAAA;QACJ,IAAI,CAAC,aAAa,EAAE,CAAC;KACxB;IAED,aAAa,GAAA;QACT,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,YAAY,GAAG,KAAK,GAAG,SAAS,CAAC;AAC9C,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AACxD,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;KAClC;IAED,aAAa,GAAA;AACT,QAAA,OAAO,CAAU,OAAA,EAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,EAAE,CAAG,EAAA,IAAI,CAAC,IAAI,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;KACpG;uGA1BQ,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAR,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAGG,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAX1B,CAA6B,2BAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQ9B,QAAQ,EAAA,UAAA,EAAA,CAAA;kBATpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,CAA6B,2BAAA,CAAA;AACvC,oBAAA,UAAU,EAAE,IAAI;oBAChB,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACpC,qBAAA;AACJ,iBAAA,CAAA;8BAEY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,UAAU,EAAA,CAAA;sBAAlB,KAAK;;;ACjBV;;AAEG;;;;"}