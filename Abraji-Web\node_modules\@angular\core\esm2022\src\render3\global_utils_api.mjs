/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @fileoverview
 * This file is the index file collecting all of the symbols published on the global.ng namespace.
 *
 * The reason why this file/module is separate global_utils.ts file is that we use this file
 * to generate a d.ts file containing all the published symbols that is then compared to the golden
 * file in the public_api_guard test.
 */
export { applyChanges } from './util/change_detection_utils';
export { getComponent, getContext, getDirectiveMetadata, getDirectives, getHostElement, getInjector, getListeners, getOwningComponent, getRootComponents, } from './util/discovery_utils';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZ2xvYmFsX3V0aWxzX2FwaS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL3JlbmRlcjMvZ2xvYmFsX3V0aWxzX2FwaS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSDs7Ozs7OztHQU9HO0FBRUgsT0FBTyxFQUFDLFlBQVksRUFBQyxNQUFNLCtCQUErQixDQUFDO0FBQzNELE9BQU8sRUFHTCxZQUFZLEVBQ1osVUFBVSxFQUNWLG9CQUFvQixFQUNwQixhQUFhLEVBQ2IsY0FBYyxFQUNkLFdBQVcsRUFDWCxZQUFZLEVBQ1osa0JBQWtCLEVBQ2xCLGlCQUFpQixHQUVsQixNQUFNLHdCQUF3QixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8qKlxuICogQGZpbGVvdmVydmlld1xuICogVGhpcyBmaWxlIGlzIHRoZSBpbmRleCBmaWxlIGNvbGxlY3RpbmcgYWxsIG9mIHRoZSBzeW1ib2xzIHB1Ymxpc2hlZCBvbiB0aGUgZ2xvYmFsLm5nIG5hbWVzcGFjZS5cbiAqXG4gKiBUaGUgcmVhc29uIHdoeSB0aGlzIGZpbGUvbW9kdWxlIGlzIHNlcGFyYXRlIGdsb2JhbF91dGlscy50cyBmaWxlIGlzIHRoYXQgd2UgdXNlIHRoaXMgZmlsZVxuICogdG8gZ2VuZXJhdGUgYSBkLnRzIGZpbGUgY29udGFpbmluZyBhbGwgdGhlIHB1Ymxpc2hlZCBzeW1ib2xzIHRoYXQgaXMgdGhlbiBjb21wYXJlZCB0byB0aGUgZ29sZGVuXG4gKiBmaWxlIGluIHRoZSBwdWJsaWNfYXBpX2d1YXJkIHRlc3QuXG4gKi9cblxuZXhwb3J0IHthcHBseUNoYW5nZXN9IGZyb20gJy4vdXRpbC9jaGFuZ2VfZGV0ZWN0aW9uX3V0aWxzJztcbmV4cG9ydCB7XG4gIENvbXBvbmVudERlYnVnTWV0YWRhdGEsXG4gIERpcmVjdGl2ZURlYnVnTWV0YWRhdGEsXG4gIGdldENvbXBvbmVudCxcbiAgZ2V0Q29udGV4dCxcbiAgZ2V0RGlyZWN0aXZlTWV0YWRhdGEsXG4gIGdldERpcmVjdGl2ZXMsXG4gIGdldEhvc3RFbGVtZW50LFxuICBnZXRJbmplY3RvcixcbiAgZ2V0TGlzdGVuZXJzLFxuICBnZXRPd25pbmdDb21wb25lbnQsXG4gIGdldFJvb3RDb21wb25lbnRzLFxuICBMaXN0ZW5lcixcbn0gZnJvbSAnLi91dGlsL2Rpc2NvdmVyeV91dGlscyc7XG4iXX0=