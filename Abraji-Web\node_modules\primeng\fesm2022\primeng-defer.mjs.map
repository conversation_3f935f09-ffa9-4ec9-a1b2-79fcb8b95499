{"version": 3, "file": "primeng-defer.mjs", "sources": ["../../src/app/components/defer/defer.ts", "../../src/app/components/defer/primeng-defer.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { AfterViewInit, ChangeDetectorRef, ContentChild, Directive, ElementRef, EmbeddedViewRef, EventEmitter, Inject, NgModule, OnDestroy, Output, PLATFORM_ID, Renderer2, TemplateRef, ViewContainerRef } from '@angular/core';\nimport { Nullable } from 'primeng/ts-helpers';\n/**\n * Defer postpones the loading the content that is initially not in the viewport until it becomes visible on scroll.\n * @group Components\n */\n@Directive({\n    selector: '[pDefer]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class DeferredLoader implements AfterViewInit, OnDestroy {\n    /**\n     * Callback to invoke when deferred content is loaded.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onLoad: EventEmitter<Event> = new EventEmitter<Event>();\n\n    @ContentChild(TemplateRef) template: TemplateRef<any> | undefined;\n\n    documentScrollListener: Nullable<Function>;\n\n    view: Nullable<EmbeddedViewRef<any>>;\n\n    window: Window;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, public el: ElementRef, public renderer: Renderer2, public viewContainer: ViewContainerRef, private cd: ChangeDetectorRef) {\n        this.window = this.document.defaultView as Window;\n    }\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.shouldLoad()) {\n                this.load();\n            }\n\n            if (!this.isLoaded()) {\n                this.documentScrollListener = this.renderer.listen(this.window, 'scroll', () => {\n                    if (this.shouldLoad()) {\n                        this.load();\n                        this.documentScrollListener && this.documentScrollListener();\n                        this.documentScrollListener = null;\n                    }\n                });\n            }\n        }\n    }\n\n    shouldLoad(): boolean {\n        if (this.isLoaded()) {\n            return false;\n        } else {\n            let rect = this.el.nativeElement.getBoundingClientRect();\n            let docElement = this.document.documentElement;\n            let winHeight = docElement.clientHeight;\n\n            return winHeight >= rect.top;\n        }\n    }\n\n    load(): void {\n        this.view = this.viewContainer.createEmbeddedView(this.template as TemplateRef<any>);\n        this.onLoad.emit();\n        this.cd.detectChanges();\n    }\n\n    isLoaded() {\n        return this.view != null && isPlatformBrowser(this.platformId);\n    }\n\n    ngOnDestroy() {\n        this.view = null;\n\n        if (this.documentScrollListener) {\n            this.documentScrollListener();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [DeferredLoader],\n    declarations: [DeferredLoader]\n})\nexport class DeferModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;AAGA;;;AAGG;MAOU,cAAc,CAAA;AAgBe,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA4B,IAAA,aAAA,CAAA;AAAyC,IAAA,EAAA,CAAA;AAf3M;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAEvC,IAAA,QAAQ,CAA+B;AAElE,IAAA,sBAAsB,CAAqB;AAE3C,IAAA,IAAI,CAAiC;AAErC,IAAA,MAAM,CAAS;IAEf,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAS,EAAc,EAAS,QAAmB,EAAS,aAA+B,EAAU,EAAqB,EAAA;QAA1L,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAa,CAAA,aAAA,GAAb,aAAa,CAAkB;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAC5N,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;KACrD;IAED,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACnB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,aAAA;AAED,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;AAClB,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAK;AAC3E,oBAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;wBACnB,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,wBAAA,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC7D,wBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;AACjB,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAAM,aAAA;YACH,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;AACzD,YAAA,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;AAC/C,YAAA,IAAI,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC;AAExC,YAAA,OAAO,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC;AAChC,SAAA;KACJ;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAA4B,CAAC,CAAC;AACrF,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,QAAQ,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAClE;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AACjC,SAAA;KACJ;uGAlEQ,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAgBH,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAhBpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,4JAQT,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FARhB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAN1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAiBgB,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;yJAVnE,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEoB,QAAQ,EAAA,CAAA;sBAAlC,YAAY;uBAAC,WAAW,CAAA;;MAkEhB,WAAW,CAAA;uGAAX,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,EA1EX,YAAA,EAAA,CAAA,cAAc,CAsEb,EAAA,OAAA,EAAA,CAAA,YAAY,aAtEb,cAAc,CAAA,EAAA,CAAA,CAAA;AA0Ed,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,YAJV,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,WAAW,EAAA,UAAA,EAAA,CAAA;kBALvB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,cAAc,CAAC;oBACzB,YAAY,EAAE,CAAC,cAAc,CAAC;AACjC,iBAAA,CAAA;;;ACtFD;;AAEG;;;;"}