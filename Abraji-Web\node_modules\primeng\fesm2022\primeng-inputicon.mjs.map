{"version": 3, "file": "primeng-inputicon.mjs", "sources": ["../../src/app/components/inputicon/inputicon.ts", "../../src/app/components/inputicon/primeng-inputicon.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, Input, NgModule, ViewEncapsulation } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n\n/**\n * InputIcon displays an icon.\n * @group Components\n */\n@Component({\n    selector: 'p-inputIcon',\n    template: `<span class=\"p-input-icon\" [ngClass]=\"styleClass\"><ng-content></ng-content></span>`,\n    styleUrl: './inputicon.css',\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class InputIcon {\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [InputIcon, SharedModule],\n    declarations: [InputIcon]\n})\nexport class InputIconModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAIA;;;AAGG;MAQU,SAAS,CAAA;AAClB;;;AAGG;AACM,IAAA,UAAU,CAAqB;uGAL/B,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,yFALR,CAAoF,kFAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,wFAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAKrF,SAAS,EAAA,UAAA,EAAA,CAAA;kBAPrB,SAAS;+BACI,aAAa,EAAA,QAAA,EACb,oFAAoF,EAE/E,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,wFAAA,CAAA,EAAA,CAAA;8BAOtC,UAAU,EAAA,CAAA;sBAAlB,KAAK;;MAQG,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,iBAbf,SAAS,CAAA,EAAA,OAAA,EAAA,CASR,YAAY,CATb,EAAA,OAAA,EAAA,CAAA,SAAS,EAUG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGxB,eAAe,EAAA,OAAA,EAAA,CAJd,YAAY,EACD,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGxB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;oBAClC,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;AC3BD;;AAEG;;;;"}