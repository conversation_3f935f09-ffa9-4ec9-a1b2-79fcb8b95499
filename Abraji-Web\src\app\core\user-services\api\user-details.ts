export interface Status {
  status: boolean;
  traffic: boolean;
  expiration: boolean;
  uptime: boolean;
}

export interface UserDetails {
  id: number;
  username: string;
  profile_id: number;
  enabled: number;
  expiration: string;
  address: string | null;
  city: string | null;
  country: string | null;
  mac_auth: number;
  static_ip: string | null;
  group_id: number;
  service: string | null;
  firstname: string;
  lastname: string | null;
  email: string | null;
  phone: string | null;
  company: string | null;
  apartment: string | null;
  street: string | null;
  contract_id: string | null;
  parent_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  last_ip_address: string | null;
  last_online: string | null;
  user_type: number;
  created_by: string;
  national_id: string | null;
  simultaneous_sessions: number;
  mikrotik_winbox_group: string | null;
  mikrotik_framed_route: string | null;
  mikrotik_addresslist: string | null;
  mikrotik_ipv6_prefix: string | null;
  balance: string;
  loan_balance: string | null;
  notes: string | null;
  picture: string | null;
  pin_tries: number;
  site_id: string | null;
  gps_lat: string;
  gps_lng: string;
  last_profile_id: string | null;
  auto_renew: number;
  use_separate_portal_password: number;
  restricted: number;
  profile_name: string;
  status: Status;
  profileChange: boolean;
  parent_username: string;
}
