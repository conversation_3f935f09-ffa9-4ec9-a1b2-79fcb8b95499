import { Observable } from "rxjs";
import { ColumnState, RequestForm, TableResponse } from "../../common-services/interfaces/table-response";

export abstract class UsersData {
  abstract getUsers(RequestForm: UserForm): Observable<TableResponse<User>>;
  abstract getOnlineUsers(RequestForm: UserForm): Observable<TableResponse<User>>;
  abstract getUser(id: string): Observable<UserDetails>;
  abstract getUserSessions(id: string, requestForm: UserForm): Observable<TableResponse<UserSession>>;
  abstract getUserTraffic(requestForm: UserTrafficRequestForm): Observable<any>;
  abstract editUser(userId: string | null, payload: string): Observable<any>;
  abstract createUser(payload: string): Observable<any>;
}

export interface UserTrafficRequestForm {
  report_type: string;
  month: number;
  year: number;
  user_id: string;
}


export interface UserStatus {
  status: boolean;
  traffic: boolean;
  expiration: boolean;
  uptime: boolean;
}

export interface ProfileDetails {
  id: number;
  name: string;
  type: number;
}

export interface Overview {
  username: string;
  parent_username: string;
  profile_name: string;
  profile_id: number;
  expiration: string;
  status: boolean;
  created_at: string;
  created_by: string;
  balance: number;
  password: string;
  firstname: string;
  lastname: string;
  phone: string;
  address: string;
  city: string | null;
  email: string;
  remaining_rx: number | null;
  remaining_tx: number | null;
  remaining_rxtx: number | null;
  remaining_uptime: string | null;
  next_profile_change: boolean;
  pin_tries: number;
  last_online: string;
  addons: any[]; // Adjust this type if you have a specific type for addons
  debt_days: number;
}

export interface UserDetails {
  status: number;
  data: User;
  overview: Overview;
}

export interface NasDetails {
  id: number;
  shortname: string;
  nasname: string;
}

export interface User {
  id: number;
  username: string;
  firstname: string;
  lastname: string;
  expiration: string;
  profile: string;
  city: string;
  notes: string;
  phone: string;
  address: string;
  site: string;
  profile_id: number | null;
  balance: string;
  loan_balance: string | null;
  last_online: string | null;
  parent_id: number;
  email: string;
  static_ip: string | null;
  enabled: number | null;
  company: string;
  simultaneous_sessions: number;
  contract_id: string | null;
  created_at: string;
  national_id: string | null;
  mikrotik_ipv6_prefix: string | null;
  group_id: number | null;
  gps_lat: number | null;
  gps_lng: number | null;
  street: string | null;
  site_id: number | null;
  n_row: number;
  remaining_days: number;
  status: UserStatus;
  online_status: number;
  used_traffic: number;
  available_traffic: number;
  parent_username: string;
  profile_details: ProfileDetails | null;
  daily_traffic_details: any; // Update this with the correct type if available
  group_details: any; // Update this with the correct type if available
  site_details: any; // Update this with the correct type if available
  // New attributes
  radacctid: number;
  nasipaddress: string;
  framedipaddress: string;
  framedprotocol: string;
  acctsessiontime: number;
  acctstarttime: string;
  acctinputoctets: number;
  acctoutputoctets: number;
  callingstationid: string;
  calledstationid: string;
  fup: number;
  user_profile_name: string;
  user_profile_id: number;
  daily_usage_percentage: number;
  nas_details: NasDetails | null;
  user_details: any | null;
  oui: string;
}


export interface UserSession {
  radacctid: number;
  username: string;
  nasipaddress: string;
  acctstarttime: string;
  acctstoptime: string;
  framedipaddress: string;
  acctoutputoctets: number;
  acctinputoctets: number;
  callingstationid: string;
  profile_id: number;
  calledstationid: string;
  acctterminatecause: string;
  profile_details: {id: number, name: string};
}

export interface UserTrafficData {
  rx: number[];
  tx: number[];
  total: number[];
  total_real: number[];
  free_traffic: number[];
}


export interface UserForm extends RequestForm {
  status?: number;
  connection?: number;
  profile_id?: number;
  parent_id?: number;
  group_id?: number;
  site_id?: number;
  sub_users?: boolean;
  mac?: string;
}

export const initialUserColumnsState: ColumnState[] = [
  { key: 'status', hidden: false },
  { key: 'id', hidden: false },
  { key: 'username', hidden: false },
  { key: 'firstname', hidden: false },
  { key: 'lastname', hidden: false },
  { key: 'expiration', hidden: false },
  { key: 'parent_username', hidden: false },
  { key: 'profile', hidden: false },
  { key: 'balance', hidden: false },
  { key: 'loan_balance', hidden: false },
  { key: 'group', hidden: false },
  { key: 'daily_traffic', hidden: false },
  { key: 'city', hidden: false },
  { key: 'remaining_days', hidden: false },
  { key: 'static_ip', hidden: false },
  { key: 'notes', hidden: false },
  { key: 'last_online', hidden: false },
  { key: 'company', hidden: false },
  { key: 'simultaneous_sessions', hidden: false },
  { key: 'used_traffic', hidden: false },
  { key: 'phone', hidden: false },
  { key: 'address', hidden: false },
  { key: 'contract_id', hidden: false },
  { key: 'created_at', hidden: false },
  { key: 'available_traffic', hidden: false },
  { key: 'national_id', hidden: false },
  { key: 'mikrotik_ipv6_prefix', hidden: false },
  { key: 'site', hidden: false },
  //{ key: 'pin_errors', hidden: false }
];

export const initialOnlineUsersColumnsState: ColumnState[] = [
    { key: 'status', hidden: false },
    { key: 'username', hidden: false },
    { key: 'firstname', hidden: false },
    { key: 'lastname', hidden: false },
    { key: 'expiration', hidden: false },
    { key: 'acctinputoctets', hidden: false }, // Download
    { key: 'acctoutputoctets', hidden: false }, // Upload
    { key: 'profile', hidden: false },
    { key: 'nasipaddress', hidden: false }, // NAS
    { key: 'parent_username', hidden: false }, // Parent
    { key: 'framedipaddress', hidden: false }, // IP
    { key: 'callingstationid', hidden: false }, // MAC
    { key: 'daily_usage_percentage', hidden: false }, // Daily Quota
    { key: 'acctstarttime', hidden: false }, // uptime
    { key: 'oui', hidden: false }, // Device
    { key: 'calledstationid', hidden: false }, // Service
    { key: 'contract_id', hidden: false }, // Contract ID
];
export { ColumnState };

