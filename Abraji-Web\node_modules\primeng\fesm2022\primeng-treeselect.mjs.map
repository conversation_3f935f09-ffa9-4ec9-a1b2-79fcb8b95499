{"version": 3, "file": "primeng-treeselect.mjs", "sources": ["../../src/app/components/treeselect/treeselect.ts", "../../src/app/components/treeselect/primeng-treeselect.ts"], "sourcesContent": ["import { AnimationEvent } from '@angular/animations';\nimport { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    booleanAttribute,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    forwardRef,\n    Input,\n    NgModule,\n    Output,\n    QueryList,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation\n} from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { OverlayOptions, OverlayService, PrimeNGConfig, PrimeTemplate, ScrollerOptions, SharedModule, TreeNode } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { SearchIcon } from 'primeng/icons/search';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { Overlay, OverlayModule } from 'primeng/overlay';\nimport { RippleModule } from 'primeng/ripple';\nimport { Tree, TreeFilterEvent, TreeModule, TreeNodeSelectEvent, TreeNodeUnSelectEvent } from 'primeng/tree';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { TreeSelectNodeCollapseEvent, TreeSelectNodeExpandEvent } from './treeselect.interface';\n\nexport const TREESELECT_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => TreeSelect),\n    multi: true\n};\n/**\n * TreeSelect is a form component to choose from hierarchical data.\n * @group Components\n */\n@Component({\n    selector: 'p-treeSelect',\n    template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"containerStyleClass\" [ngStyle]=\"containerStyle\" (click)=\"onClick($event)\">\n            <div class=\"p-hidden-accessible\">\n                <input\n                    #focusInput\n                    type=\"text\"\n                    role=\"combobox\"\n                    [attr.id]=\"inputId\"\n                    readonly\n                    [disabled]=\"disabled\"\n                    (focus)=\"onInputFocus($event)\"\n                    (blur)=\"onInputBlur($event)\"\n                    (keydown)=\"onKeyDown($event)\"\n                    [attr.tabindex]=\"!disabled ? tabindex : -1\"\n                    [attr.aria-controls]=\"overlayVisible ? listId : null\"\n                    [attr.aria-haspopup]=\"'tree'\"\n                    [attr.aria-expanded]=\"overlayVisible ?? false\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel || (label === 'p-emptylabel' ? undefined : label)\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                />\n            </div>\n            <div class=\"p-treeselect-label-container\">\n                <div [ngClass]=\"labelClass()\" [class]=\"labelStyleClass\" [ngStyle]=\"labelStyle\">\n                    <ng-container *ngIf=\"valueTemplate; else defaultValueTemplate\">\n                        <ng-container *ngTemplateOutlet=\"valueTemplate; context: { $implicit: value, placeholder: placeholder }\"></ng-container>\n                    </ng-container>\n                    <ng-template #defaultValueTemplate>\n                        <ng-container *ngIf=\"display === 'comma'; else chipsValueTemplate\">\n                            {{ label || 'empty' }}\n                        </ng-container>\n                        <ng-template #chipsValueTemplate>\n                            <div *ngFor=\"let node of value\" class=\"p-treeselect-token\">\n                                <span class=\"p-treeselect-token-label\">{{ node.label }}</span>\n                            </div>\n                            <ng-container *ngIf=\"emptyValue\">{{ placeholder || 'empty' }}</ng-container>\n                        </ng-template>\n                    </ng-template>\n                </div>\n                <ng-container *ngIf=\"checkValue() && !disabled && showClear\">\n                    <TimesIcon *ngIf=\"!clearIconTemplate\" [styleClass]=\"'p-treeselect-clear-icon'\" (click)=\"clear($event)\" />\n                    <span *ngIf=\"clearIconTemplate\" class=\"p-treeselect-clear-icon\" (click)=\"clear($event)\">\n                        <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                    </span>\n                </ng-container>\n            </div>\n            <div class=\"p-treeselect-trigger\" role=\"button\" aria-haspopup=\"tree\" [attr.aria-expanded]=\"overlayVisible ?? false\" [attr.aria-label]=\"'treeselect trigger'\">\n                <ChevronDownIcon *ngIf=\"!triggerIconTemplate\" [styleClass]=\"'p-treeselect-trigger-icon'\" />\n                <span *ngIf=\"triggerIconTemplate\" class=\"p-treeselect-trigger-icon\">\n                    <ng-template *ngTemplateOutlet=\"triggerIconTemplate\"></ng-template>\n                </span>\n            </div>\n            <p-overlay\n                #overlay\n                [(visible)]=\"overlayVisible\"\n                [options]=\"overlayOptions\"\n                [target]=\"'@parent'\"\n                [appendTo]=\"appendTo\"\n                [showTransitionOptions]=\"showTransitionOptions\"\n                [hideTransitionOptions]=\"hideTransitionOptions\"\n                (onAnimationStart)=\"onOverlayAnimationStart($event)\"\n                (onBeforeHide)=\"onOverlayBeforeHide($event)\"\n                (onShow)=\"onShow.emit($event)\"\n                (onHide)=\"hide($event)\"\n            >\n                <ng-template pTemplate=\"content\">\n                    <div #panel [attr.id]=\"listId\" class=\"p-treeselect-panel p-component\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\" [ngClass]=\"panelClass\">\n                        <span\n                            #firstHiddenFocusableEl\n                            role=\"presentation\"\n                            class=\"p-hidden-accessible p-hidden-focusable\"\n                            [attr.tabindex]=\"0\"\n                            (focus)=\"onFirstHiddenFocus($event)\"\n                            [attr.data-p-hidden-accessible]=\"true\"\n                            [attr.data-p-hidden-focusable]=\"true\"\n                        >\n                        </span>\n                        <ng-container *ngTemplateOutlet=\"headerTemplate; context: { $implicit: value, options: options }\"></ng-container>\n                        <div class=\"p-treeselect-header\" *ngIf=\"filter\" (keydown.arrowdown)=\"onArrowDown($event)\">\n                            <div class=\"p-treeselect-filter-container\">\n                                <input\n                                    #filter\n                                    type=\"search\"\n                                    autocomplete=\"off\"\n                                    class=\"p-treeselect-filter p-inputtext p-component\"\n                                    [attr.placeholder]=\"filterPlaceholder\"\n                                    (keydown.enter)=\"$event.preventDefault()\"\n                                    (input)=\"onFilterInput($event)\"\n                                    [value]=\"filterValue\"\n                                />\n                                <SearchIcon *ngIf=\"!filterIconTemplate\" [styleClass]=\"'p-treeselect-filter-icon'\" />\n                                <span *ngIf=\"filterIconTemplate\" class=\"p-treeselect-filter-icon\">\n                                    <ng-template *ngTemplateOutlet=\"filterIconTemplate\"></ng-template>\n                                </span>\n                            </div>\n                            <button class=\"p-treeselect-close p-link\" (click)=\"hide()\">\n                                <TimesIcon *ngIf=\"!closeIconTemplate\" />\n                                <span *ngIf=\"closeIconTemplate\">\n                                    <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                                </span>\n                            </button>\n                        </div>\n                        <div class=\"p-treeselect-items-wrapper\" [ngStyle]=\"{ 'max-height': scrollHeight }\">\n                            <p-tree\n                                #tree\n                                [value]=\"options\"\n                                [propagateSelectionDown]=\"propagateSelectionDown\"\n                                [propagateSelectionUp]=\"propagateSelectionUp\"\n                                [selectionMode]=\"selectionMode\"\n                                (selectionChange)=\"onSelectionChange($event)\"\n                                [selection]=\"value\"\n                                [metaKeySelection]=\"metaKeySelection\"\n                                (onNodeExpand)=\"nodeExpand($event)\"\n                                (onNodeCollapse)=\"nodeCollapse($event)\"\n                                (onNodeSelect)=\"onSelect($event)\"\n                                [emptyMessage]=\"emptyMessage\"\n                                (onNodeUnselect)=\"onUnselect($event)\"\n                                [filterBy]=\"filterBy\"\n                                [filterMode]=\"filterMode\"\n                                [filterPlaceholder]=\"filterPlaceholder\"\n                                [filterLocale]=\"filterLocale\"\n                                [filteredNodes]=\"filteredNodes\"\n                                [virtualScroll]=\"virtualScroll\"\n                                [virtualScrollItemSize]=\"virtualScrollItemSize\"\n                                [virtualScrollOptions]=\"virtualScrollOptions\"\n                                [_templateMap]=\"templateMap\"\n                                [loading]=\"loading\"\n                            >\n                                <ng-container *ngIf=\"emptyTemplate\">\n                                    <ng-template pTemplate=\"empty\">\n                                        <ng-container *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                                    </ng-template>\n                                </ng-container>\n                                <ng-template pTemplate=\"togglericon\" let-expanded *ngIf=\"itemTogglerIconTemplate\">\n                                    <ng-container *ngTemplateOutlet=\"itemTogglerIconTemplate; context: { $implicit: expanded }\"></ng-container>\n                                </ng-template>\n                                <ng-template pTemplate=\"checkboxicon\" let-selected let-partialSelected=\"partialSelected\" *ngIf=\"itemCheckboxIconTemplate\">\n                                    <ng-container *ngTemplateOutlet=\"itemCheckboxIconTemplate; context: { $implicit: selected, partialSelected: partialSelected }\"></ng-container>\n                                </ng-template>\n                                <ng-template pTemplate=\"loadingicon\" *ngIf=\"itemLoadingIconTemplate\">\n                                    <ng-container *ngTemplateOutlet=\"itemLoadingIconTemplate\"></ng-container>\n                                </ng-template>\n                            </p-tree>\n                        </div>\n                        <ng-container *ngTemplateOutlet=\"footerTemplate; context: { $implicit: value, options: options }\"></ng-container>\n                        <span\n                            #lastHiddenFocusableEl\n                            role=\"presentation\"\n                            class=\"p-hidden-accessible p-hidden-focusable\"\n                            [attr.tabindex]=\"0\"\n                            (focus)=\"onLastHiddenFocus($event)\"\n                            [attr.data-p-hidden-accessible]=\"true\"\n                            [attr.data-p-hidden-focusable]=\"true\"\n                        ></span>\n                    </div>\n                </ng-template>\n            </p-overlay>\n        </div>\n    `,\n    styleUrls: ['./treeselect.css'],\n    host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': '!emptyValue',\n        '[class.p-inputwrapper-focus]': 'focused',\n        '[class.p-treeselect-clearable]': 'showClear && !disabled'\n    },\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    providers: [TREESELECT_VALUE_ACCESSOR],\n    encapsulation: ViewEncapsulation.None\n})\nexport class TreeSelect implements AfterContentInit {\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Height of the viewport, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    @Input() scrollHeight: string = '400px';\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) metaKeySelection: boolean = false;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Defines how the selected items are displayed.\n     * @group Props\n     */\n    @Input() display: 'comma' | 'chip' = 'comma';\n    /**\n     * Defines the selection mode.\n     * @group Props\n     */\n    @Input() selectionMode: 'single' | 'multiple' | 'checkbox' = 'single';\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input() tabindex: string | undefined = '0';\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Label to display when there are no selections.\n     * @group Props\n     */\n    @Input() placeholder: string | undefined;\n    /**\n     * Style class of the overlay panel.\n     * @group Props\n     */\n    @Input() panelClass: string | string[] | Set<string> | { [klass: string]: any } | undefined;\n    /**\n     * Inline style of the panel element.\n     * @group Props\n     */\n    @Input() panelStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the panel element.\n     * @group Props\n     */\n    @Input() panelStyleClass: string | undefined;\n    /**\n     * Inline style of the container element.\n     * @group Props\n     */\n    @Input() containerStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the container element.\n     * @group Props\n     */\n    @Input() containerStyleClass: string | undefined;\n    /**\n     * Inline style of the label element.\n     * @group Props\n     */\n    @Input() labelStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the label element.\n     * @group Props\n     */\n    @Input() labelStyleClass: string | undefined;\n    /**\n     * Specifies the options for the overlay.\n     * @group Props\n     */\n    @Input() overlayOptions: OverlayOptions | undefined;\n    /**\n     * Text to display when there are no options available. Defaults to value from PrimeNG locale configuration.\n     * @group Props\n     */\n    @Input() emptyMessage: string = '';\n    /**\n     * A valid query selector or an HTMLElement to specify where the overlay gets attached. Special keywords are \"body\" for document body and \"self\" for the element itself.\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * When specified, displays an input field to filter the items.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) filter: boolean = false;\n    /**\n     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n     * @group Props\n     */\n    @Input() filterBy: string = 'label';\n    /**\n     * Mode for filtering valid values are \"lenient\" and \"strict\". Default is lenient.\n     * @group Props\n     */\n    @Input() filterMode: string = 'lenient';\n    /**\n     * Placeholder text to show when filter input is empty.\n     * @group Props\n     */\n    @Input() filterPlaceholder: string | undefined;\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() filterLocale: string | undefined;\n    /**\n     * Determines whether the filter input should be automatically focused when the component is rendered.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) filterInputAutoFocus: boolean = true;\n    /**\n     * Whether checkbox selections propagate to descendant nodes.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) propagateSelectionDown: boolean = true;\n    /**\n     * Whether checkbox selections propagate to ancestor nodes.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) propagateSelectionUp: boolean = true;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean = false;\n    /**\n     * Clears the filter value when hiding the dropdown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) resetFilterOnHide: boolean = true;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    @Input() virtualScroll: boolean | undefined;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    @Input() virtualScrollItemSize: number | undefined;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() virtualScrollOptions: ScrollerOptions | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * An array of treenodes.\n     * @defaultValue undefined\n     * @group Props\n     */\n    @Input() get options(): TreeNode[] | undefined {\n        return this._options;\n    }\n    set options(options: TreeNode[] | undefined) {\n        this._options = options;\n        this.updateTreeState();\n    }\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     * @deprecated since v14.2.0 use overlayOptions property instead.\n     */\n    @Input() get showTransitionOptions(): string | undefined {\n        return this._showTransitionOptions;\n    }\n    set showTransitionOptions(val: string | undefined) {\n        this._showTransitionOptions = val;\n        console.warn('The showTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     * @deprecated since v14.2.0 use overlayOptions property instead.\n     */\n    @Input() get hideTransitionOptions(): string | undefined {\n        return this._hideTransitionOptions;\n    }\n    set hideTransitionOptions(val: string | undefined) {\n        this._hideTransitionOptions = val;\n        console.warn('The hideTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Displays a loader to indicate data load is in progress.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) loading: boolean | undefined;\n    /**\n     * Callback to invoke when a node is expanded.\n     * @param {TreeSelectNodeExpandEvent} event - Custom node expand event.\n     * @group Emits\n     */\n    @Output() onNodeExpand: EventEmitter<TreeSelectNodeExpandEvent> = new EventEmitter<TreeSelectNodeExpandEvent>();\n    /**\n     * Callback to invoke when a node is collapsed.\n     * @param {TreeSelectNodeCollapseEvent} event - Custom node collapse event.\n     * @group Emits\n     */\n    @Output() onNodeCollapse: EventEmitter<TreeSelectNodeCollapseEvent> = new EventEmitter<TreeSelectNodeCollapseEvent>();\n\n    /**\n     * Callback to invoke when the overlay is shown.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when the overlay is hidden.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when input field is cleared.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when data is filtered.\n     * @group Emits\n     */\n    @Output() onFilter: EventEmitter<TreeFilterEvent> = new EventEmitter<TreeFilterEvent>();\n    /**\n     * Callback to invoke when treeselect gets focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when treeselect loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when a node is unselected.\n     * @param {TreeNodeUnSelectEvent} event - node unselect event.\n     * @group Emits\n     */\n    @Output() onNodeUnselect: EventEmitter<TreeNodeUnSelectEvent> = new EventEmitter<TreeNodeUnSelectEvent>();\n    /**\n     * Callback to invoke when a node is selected.\n     * @param {TreeNodeSelectEvent} event - node select event.\n     * @group Emits\n     */\n    @Output() onNodeSelect: EventEmitter<TreeNodeSelectEvent> = new EventEmitter<TreeNodeSelectEvent>();\n\n    _showTransitionOptions: string | undefined;\n\n    _hideTransitionOptions: string | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    @ViewChild('container') containerEl: Nullable<ElementRef>;\n\n    @ViewChild('focusInput') focusInput: Nullable<ElementRef>;\n\n    @ViewChild('filter') filterViewChild: Nullable<ElementRef>;\n\n    @ViewChild('tree') treeViewChild: Nullable<Tree>;\n\n    @ViewChild('panel') panelEl: Nullable<ElementRef>;\n\n    @ViewChild('overlay') overlayViewChild: Nullable<Overlay>;\n\n    @ViewChild('firstHiddenFocusableEl') firstHiddenFocusableElementOnOverlay: Nullable<ElementRef>;\n\n    @ViewChild('lastHiddenFocusableEl') lastHiddenFocusableElementOnOverlay: Nullable<ElementRef>;\n\n    public filteredNodes: TreeNode[] | undefined | null;\n\n    filterValue: Nullable<string> = null;\n\n    serializedValue: Nullable<any[]>;\n\n    valueTemplate: Nullable<TemplateRef<any>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    emptyTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    clearIconTemplate: Nullable<TemplateRef<any>>;\n\n    triggerIconTemplate: Nullable<TemplateRef<any>>;\n\n    filterIconTemplate: Nullable<TemplateRef<any>>;\n\n    closeIconTemplate: Nullable<TemplateRef<any>>;\n\n    itemTogglerIconTemplate: Nullable<TemplateRef<any>>;\n\n    itemCheckboxIconTemplate: Nullable<TemplateRef<any>>;\n\n    itemLoadingIconTemplate: Nullable<TemplateRef<any>>;\n\n    focused: Nullable<boolean>;\n\n    overlayVisible: Nullable<boolean>;\n\n    selfChange: Nullable<boolean>;\n\n    value: any | undefined;\n\n    expandedNodes: any[] = [];\n\n    _options: TreeNode[] | undefined;\n\n    public templateMap: any;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    listId: string = '';\n\n    constructor(public config: PrimeNGConfig, public cd: ChangeDetectorRef, public el: ElementRef, public overlayService: OverlayService) {}\n\n    ngOnInit() {\n        this.listId = UniqueComponentId() + '_list';\n        this.updateTreeState();\n    }\n\n    ngAfterContentInit() {\n        if ((this.templates as QueryList<PrimeTemplate>).length) {\n            this.templateMap = {};\n        }\n\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'value':\n                    this.valueTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n\n                case 'triggericon':\n                    this.triggerIconTemplate = item.template;\n                    break;\n\n                case 'filtericon':\n                    this.filterIconTemplate = item.template;\n                    break;\n\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n\n                case 'itemtogglericon':\n                    this.itemTogglerIconTemplate = item.template;\n                    break;\n\n                case 'itemcheckboxicon':\n                    this.itemCheckboxIconTemplate = item.template;\n                    break;\n\n                case 'itemloadingicon':\n                    this.itemLoadingIconTemplate = item.template;\n                    break;\n\n                default: //TODO: @deprecated Used \"value\" template instead\n                    if (item.name) this.templateMap[item.name] = item.template;\n                    else this.valueTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    onOverlayAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                if (this.filter) {\n                    ObjectUtils.isNotEmpty(this.filterValue) && this.treeViewChild?._filter(<any>this.filterValue);\n                    this.filterInputAutoFocus && this.filterViewChild?.nativeElement.focus();\n                } else {\n                    let focusableElements = DomHandler.getFocusableElements(this.panelEl.nativeElement);\n\n                    if (focusableElements && focusableElements.length > 0) {\n                        focusableElements[0].focus();\n                    }\n                }\n                break;\n        }\n    }\n\n    onOverlayBeforeHide(event: Event) {\n        let focusableElements = DomHandler.getFocusableElements(this.containerEl.nativeElement);\n\n        if (focusableElements && focusableElements.length > 0) {\n            focusableElements[0].focus();\n        }\n    }\n\n    onSelectionChange(event: Event) {\n        this.value = event;\n        this.onModelChange(this.value);\n        this.cd.markForCheck();\n    }\n\n    onClick(event: Event) {\n        if (this.disabled) {\n            return;\n        }\n\n        if (\n            !this.overlayViewChild?.el?.nativeElement?.contains(event.target) &&\n            !DomHandler.hasClass(event.target, 'p-treeselect-close') &&\n            !DomHandler.hasClass(event.target, 'p-checkbox-box') &&\n            !DomHandler.hasClass(event.target, 'p-checkbox-icon')\n        ) {\n            if (this.overlayVisible) {\n                this.hide();\n            } else {\n                this.show();\n            }\n\n            this.focusInput?.nativeElement.focus();\n        }\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        switch (event.code) {\n            //down\n            case 'ArrowDown':\n                if (!this.overlayVisible) {\n                    this.show();\n                    event.preventDefault();\n                }\n                this.onArrowDown(event);\n                event.preventDefault();\n                break;\n\n            //space\n            case 'Space':\n            case 'Enter':\n                if (!this.overlayVisible) {\n                    this.show();\n                    event.preventDefault();\n                }\n                break;\n\n            //escape\n            case 'Escape':\n                if (this.overlayVisible) {\n                    this.hide();\n                    this.focusInput?.nativeElement.focus();\n                    event.preventDefault();\n                }\n                break;\n\n            //tab\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onFilterInput(event: Event) {\n        this.filterValue = (event.target as HTMLInputElement).value;\n        this.treeViewChild?._filter(this.filterValue);\n        this.onFilter.emit({\n            filter: this.filterValue,\n            filteredValue: this.treeViewChild?.filteredNodes\n        });\n        setTimeout(() => {\n            this.overlayViewChild.alignOverlay();\n        });\n    }\n\n    onArrowDown(event: KeyboardEvent) {\n        if (this.overlayVisible && this.panelEl?.nativeElement) {\n            let focusableElements = DomHandler.getFocusableElements(this.panelEl.nativeElement, '.p-treenode');\n\n            if (focusableElements && focusableElements.length > 0) {\n                focusableElements[0].focus();\n            }\n\n            event.preventDefault();\n        }\n    }\n\n    onFirstHiddenFocus(event) {\n        const focusableEl =\n            event.relatedTarget === this.focusInput?.nativeElement ? DomHandler.getFirstFocusableElement(this.overlayViewChild?.overlayViewChild?.nativeElement, ':not([data-p-hidden-focusable=\"true\"])') : this.focusInput?.nativeElement;\n\n        DomHandler.focus(focusableEl);\n    }\n\n    onLastHiddenFocus(event) {\n        const focusableEl =\n            event.relatedTarget === this.focusInput?.nativeElement ? DomHandler.getLastFocusableElement(this.overlayViewChild?.overlayViewChild?.nativeElement, ':not([data-p-hidden-focusable=\"true\"])') : this.focusInput?.nativeElement;\n\n        DomHandler.focus(focusableEl);\n    }\n\n    show() {\n        this.overlayVisible = true;\n    }\n\n    hide(event?: any) {\n        this.overlayVisible = false;\n        this.resetFilter();\n\n        this.onHide.emit(event);\n        this.cd.markForCheck();\n    }\n\n    clear(event: Event) {\n        this.value = null;\n        this.resetExpandedNodes();\n        this.resetPartialSelected();\n        this.onModelChange(this.value);\n        this.onClear.emit();\n\n        event.stopPropagation();\n    }\n\n    checkValue() {\n        return this.value !== null && ObjectUtils.isNotEmpty(this.value);\n    }\n\n    onTabKey(event, pressedInInputText = false) {\n        if (!pressedInInputText) {\n            if (this.overlayVisible && this.hasFocusableElements()) {\n                DomHandler.focus(event.shiftKey ? this.lastHiddenFocusableElementOnOverlay.nativeElement : this.firstHiddenFocusableElementOnOverlay.nativeElement);\n\n                event.preventDefault();\n            } else {\n                this.overlayVisible && this.hide(this.filter);\n            }\n        }\n    }\n\n    hasFocusableElements() {\n        return DomHandler.getFocusableElements(this.overlayViewChild.overlayViewChild.nativeElement, ':not([data-p-hidden-focusable=\"true\"])').length > 0;\n    }\n\n    resetFilter() {\n        if (this.filter && !this.resetFilterOnHide) {\n            this.filteredNodes = this.treeViewChild?.filteredNodes;\n            this.treeViewChild?.resetFilter();\n        } else {\n            this.filterValue = null;\n        }\n    }\n\n    updateTreeState() {\n        if (this.value) {\n            let selectedNodes = this.selectionMode === 'single' ? [this.value] : [...this.value];\n            this.resetExpandedNodes();\n            this.resetPartialSelected();\n            if (selectedNodes && this.options) {\n                this.updateTreeBranchState(null, null, selectedNodes);\n            }\n        }\n    }\n\n    updateTreeBranchState(node: TreeNode | null, path: any, selectedNodes: TreeNode[]) {\n        if (node) {\n            if (this.isSelected(node)) {\n                this.expandPath(path);\n                selectedNodes.splice(selectedNodes.indexOf(node), 1);\n            }\n\n            if (selectedNodes.length > 0 && node.children) {\n                for (let childNode of node.children) {\n                    this.updateTreeBranchState(childNode, [...path, node], selectedNodes);\n                }\n            }\n        } else {\n            for (let childNode of this.options as TreeNode[]) {\n                this.updateTreeBranchState(childNode, [], selectedNodes);\n            }\n        }\n    }\n\n    expandPath(expandedNodes: TreeNode[]) {\n        for (let node of expandedNodes) {\n            node.expanded = true;\n        }\n\n        this.expandedNodes = [...expandedNodes];\n    }\n\n    nodeExpand(event: { originalEvent: Event; node: TreeNode }) {\n        this.onNodeExpand.emit(event);\n        this.expandedNodes.push(event.node);\n    }\n\n    nodeCollapse(event: { originalEvent: Event; node: TreeNode }) {\n        this.onNodeCollapse.emit(event);\n        this.expandedNodes.splice(this.expandedNodes.indexOf(event.node), 1);\n    }\n\n    resetExpandedNodes() {\n        for (let node of this.expandedNodes) {\n            node.expanded = false;\n        }\n\n        this.expandedNodes = [];\n    }\n\n    resetPartialSelected(nodes = this.options): void {\n        if (!nodes) {\n            return;\n        }\n\n        for (let node of nodes) {\n            node.partialSelected = false;\n\n            if (node.children && node.children?.length > 0) {\n                this.resetPartialSelected(node.children);\n            }\n        }\n    }\n\n    findSelectedNodes(node: TreeNode, keys: any[], selectedNodes: TreeNode[]) {\n        if (node) {\n            if (this.isSelected(node)) {\n                selectedNodes.push(node);\n                delete keys[node.key as any];\n            }\n\n            if (Object.keys(keys).length && node.children) {\n                for (let childNode of node.children) {\n                    this.findSelectedNodes(childNode, keys, selectedNodes);\n                }\n            }\n        } else {\n            for (let childNode of this.options as TreeNode[]) {\n                this.findSelectedNodes(childNode, keys, selectedNodes);\n            }\n        }\n    }\n\n    isSelected(node: TreeNode) {\n        return this.findIndexInSelection(node) != -1;\n    }\n\n    findIndexInSelection(node: TreeNode) {\n        let index: number = -1;\n\n        if (this.value) {\n            if (this.selectionMode === 'single') {\n                let areNodesEqual = (this.value.key && this.value.key === node.key) || this.value == node;\n                index = areNodesEqual ? 0 : -1;\n            } else {\n                for (let i = 0; i < this.value.length; i++) {\n                    let selectedNode = this.value[i];\n                    let areNodesEqual = (selectedNode.key && selectedNode.key === node.key) || selectedNode == node;\n                    if (areNodesEqual) {\n                        index = i;\n                        break;\n                    }\n                }\n            }\n        }\n\n        return index;\n    }\n\n    onSelect(event: TreeNodeSelectEvent) {\n        this.onNodeSelect.emit(event);\n\n        if (this.selectionMode === 'single') {\n            this.hide();\n            this.focusInput?.nativeElement.focus();\n        }\n    }\n\n    onUnselect(event: TreeNodeUnSelectEvent) {\n        this.onNodeUnselect.emit(event);\n    }\n\n    onInputFocus(event: Event) {\n        if (this.disabled) {\n            // For ScreenReaders\n            return;\n        }\n\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event: Event) {\n        this.focused = false;\n        this.onBlur.emit(event);\n        this.onModelTouched();\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.updateTreeState();\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        setTimeout(() => {\n            this.disabled = val;\n            this.cd.markForCheck();\n        });\n    }\n\n    containerClass() {\n        return {\n            'p-treeselect p-component p-inputwrapper': true,\n            'p-treeselect-chip': this.display === 'chip',\n            'p-disabled': this.disabled,\n            'p-focus': this.focused,\n            'p-variant-filled': this.variant === 'filled' || this.config.inputStyle() === 'filled'\n        };\n    }\n\n    labelClass() {\n        return {\n            'p-treeselect-label': true,\n            'p-placeholder': this.label === this.placeholder,\n            'p-treeselect-label-empty': !this.placeholder && this.emptyValue\n        };\n    }\n\n    get emptyValue() {\n        return !this.value || Object.keys(this.value).length === 0;\n    }\n\n    get emptyOptions() {\n        return !this.options || this.options.length === 0;\n    }\n\n    get label() {\n        let value = this.value || [];\n        return value.length ? value.map((node: TreeNode) => node.label).join(', ') : this.selectionMode === 'single' && this.value ? value.label : this.placeholder;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, OverlayModule, RippleModule, SharedModule, TreeModule, AutoFocusModule, SearchIcon, TimesIcon, ChevronDownIcon],\n    exports: [TreeSelect, OverlayModule, SharedModule, TreeModule],\n    declarations: [TreeSelect]\n})\nexport class TreeSelectModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAkCa,MAAA,yBAAyB,GAAQ;AAC1C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,UAAU,CAAC;AACzC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MA8KU,UAAU,CAAA;AA2VA,IAAA,MAAA,CAAA;AAA8B,IAAA,EAAA,CAAA;AAA8B,IAAA,EAAA,CAAA;AAAuB,IAAA,cAAA,CAAA;AA1VtG;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACM,YAAY,GAAW,OAAO,CAAC;AACxC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACqC,gBAAgB,GAAY,KAAK,CAAC;AAC1E;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;AAGG;IACM,OAAO,GAAqB,OAAO,CAAC;AAC7C;;;AAGG;IACM,aAAa,GAAuC,QAAQ,CAAC;AACtE;;;AAGG;IACM,QAAQ,GAAuB,GAAG,CAAC;AAC5C;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,UAAU,CAAyE;AAC5F;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,cAAc,CAA8C;AACrE;;;AAGG;AACM,IAAA,mBAAmB,CAAqB;AACjD;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,cAAc,CAA6B;AACpD;;;AAGG;IACM,YAAY,GAAW,EAAE,CAAC;AACnC;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;IACqC,MAAM,GAAY,KAAK,CAAC;AAChE;;;AAGG;IACM,QAAQ,GAAW,OAAO,CAAC;AACpC;;;AAGG;IACM,UAAU,GAAW,SAAS,CAAC;AACxC;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;IACqC,oBAAoB,GAAY,IAAI,CAAC;AAC7E;;;AAGG;IACqC,sBAAsB,GAAY,IAAI,CAAC;AAC/E;;;AAGG;IACqC,oBAAoB,GAAY,IAAI,CAAC;AAC7E;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;AAGG;IACqC,iBAAiB,GAAY,IAAI,CAAC;AAC1E;;;AAGG;AACM,IAAA,aAAa,CAAsB;AAC5C;;;AAGG;AACM,IAAA,qBAAqB,CAAqB;AACnD;;;AAGG;AACM,IAAA,oBAAoB,CAA8B;AAC3D;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,OAA+B,EAAA;AACvC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,eAAe,EAAE,CAAC;KAC1B;AACD;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACtC;IACD,IAAI,qBAAqB,CAAC,GAAuB,EAAA;AAC7C,QAAA,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;KACxH;AACD;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACtC;IACD,IAAI,qBAAqB,CAAC,GAAuB,EAAA;AAC7C,QAAA,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;KACxH;AACD;;;AAGG;AACqC,IAAA,OAAO,CAAsB;AACrE;;;;AAIG;AACO,IAAA,YAAY,GAA4C,IAAI,YAAY,EAA6B,CAAC;AAChH;;;;AAIG;AACO,IAAA,cAAc,GAA8C,IAAI,YAAY,EAA+B,CAAC;AAEtH;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;AAGG;AACO,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC/D;;;AAGG;AACO,IAAA,QAAQ,GAAkC,IAAI,YAAY,EAAmB,CAAC;AACxF;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,cAAc,GAAwC,IAAI,YAAY,EAAyB,CAAC;AAC1G;;;;AAIG;AACO,IAAA,YAAY,GAAsC,IAAI,YAAY,EAAuB,CAAC;AAEpG,IAAA,sBAAsB,CAAqB;AAE3C,IAAA,sBAAsB,CAAqB;AAEX,IAAA,SAAS,CAAqC;AAEtD,IAAA,WAAW,CAAuB;AAEjC,IAAA,UAAU,CAAuB;AAErC,IAAA,eAAe,CAAuB;AAExC,IAAA,aAAa,CAAiB;AAE7B,IAAA,OAAO,CAAuB;AAE5B,IAAA,gBAAgB,CAAoB;AAErB,IAAA,oCAAoC,CAAuB;AAE5D,IAAA,mCAAmC,CAAuB;AAEvF,IAAA,aAAa,CAAgC;IAEpD,WAAW,GAAqB,IAAI,CAAC;AAErC,IAAA,eAAe,CAAkB;AAEjC,IAAA,aAAa,CAA6B;AAE1C,IAAA,cAAc,CAA6B;AAE3C,IAAA,aAAa,CAA6B;AAE1C,IAAA,cAAc,CAA6B;AAE3C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,uBAAuB,CAA6B;AAEpD,IAAA,wBAAwB,CAA6B;AAErD,IAAA,uBAAuB,CAA6B;AAEpD,IAAA,OAAO,CAAoB;AAE3B,IAAA,cAAc,CAAoB;AAElC,IAAA,UAAU,CAAoB;AAE9B,IAAA,KAAK,CAAkB;IAEvB,aAAa,GAAU,EAAE,CAAC;AAE1B,IAAA,QAAQ,CAAyB;AAE1B,IAAA,WAAW,CAAM;AAExB,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;IAEpC,MAAM,GAAW,EAAE,CAAC;AAEpB,IAAA,WAAA,CAAmB,MAAqB,EAAS,EAAqB,EAAS,EAAc,EAAS,cAA8B,EAAA;QAAjH,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;KAAI;IAExI,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,MAAM,GAAG,iBAAiB,EAAE,GAAG,OAAO,CAAC;QAC5C,IAAI,CAAC,eAAe,EAAE,CAAC;KAC1B;IAED,kBAAkB,GAAA;AACd,QAAA,IAAK,IAAI,CAAC,SAAsC,CAAC,MAAM,EAAE;AACrD,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACzB,SAAA;QAEA,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,iBAAiB;AAClB,oBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC7C,MAAM;AAEV,gBAAA,KAAK,kBAAkB;AACnB,oBAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC9C,MAAM;AAEV,gBAAA,KAAK,iBAAiB;AAClB,oBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC7C,MAAM;AAEV,gBAAA;oBACI,IAAI,IAAI,CAAC,IAAI;wBAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;;AACtD,wBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,uBAAuB,CAAC,KAAqB,EAAA;QACzC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;gBACV,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,oBAAA,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,OAAO,CAAM,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC/F,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,eAAe,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;AAC5E,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,iBAAiB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAEpF,oBAAA,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AACnD,wBAAA,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AAChC,qBAAA;AACJ,iBAAA;gBACD,MAAM;AACb,SAAA;KACJ;AAED,IAAA,mBAAmB,CAAC,KAAY,EAAA;AAC5B,QAAA,IAAI,iBAAiB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAExF,QAAA,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AACnD,YAAA,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,OAAO,CAAC,KAAY,EAAA;QAChB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;AAED,QAAA,IACI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;YACjE,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,oBAAoB,CAAC;YACxD,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,gBAAgB,CAAC;YACpD,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,iBAAiB,CAAC,EACvD;YACE,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,aAAA;AAED,YAAA,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;AAC1C,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,QAAQ,KAAK,CAAC,IAAI;;AAEd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;oBACtB,IAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;AACD,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;;AAGV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;oBACtB,IAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBACD,MAAM;;AAGV,YAAA,KAAK,QAAQ;gBACT,IAAI,IAAI,CAAC,cAAc,EAAE;oBACrB,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,oBAAA,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;oBACvC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBACD,MAAM;;AAGV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,KAAY,EAAA;QACtB,IAAI,CAAC,WAAW,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;QAC5D,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACf,MAAM,EAAE,IAAI,CAAC,WAAW;AACxB,YAAA,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,aAAa;AACnD,SAAA,CAAC,CAAC;QACH,UAAU,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;KACN;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;QAC5B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE;AACpD,YAAA,IAAI,iBAAiB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAEnG,YAAA,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AACnD,gBAAA,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AAChC,aAAA;YAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,kBAAkB,CAAC,KAAK,EAAA;AACpB,QAAA,MAAM,WAAW,GACb,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,UAAU,EAAE,aAAa,GAAG,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,wCAAwC,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC;AAEpO,QAAA,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACjC;AAED,IAAA,iBAAiB,CAAC,KAAK,EAAA;AACnB,QAAA,MAAM,WAAW,GACb,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,UAAU,EAAE,aAAa,GAAG,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,wCAAwC,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC;AAEnO,QAAA,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACjC;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC9B;AAED,IAAA,IAAI,CAAC,KAAW,EAAA;AACZ,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;AAEnB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,KAAK,CAAC,KAAY,EAAA;AACd,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpB,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACpE;AAED,IAAA,QAAQ,CAAC,KAAK,EAAE,kBAAkB,GAAG,KAAK,EAAA;QACtC,IAAI,CAAC,kBAAkB,EAAE;YACrB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBACpD,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,mCAAmC,CAAC,aAAa,GAAG,IAAI,CAAC,oCAAoC,CAAC,aAAa,CAAC,CAAC;gBAEpJ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,EAAE,wCAAwC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACrJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC;AACvD,YAAA,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC;AACrC,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B,SAAA;KACJ;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,KAAK,QAAQ,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YACrF,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,aAAa,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC/B,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzD,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,qBAAqB,CAAC,IAAqB,EAAE,IAAS,EAAE,aAAyB,EAAA;AAC7E,QAAA,IAAI,IAAI,EAAE;AACN,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACvB,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACtB,gBAAA,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACxD,aAAA;YAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC3C,gBAAA,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjC,oBAAA,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE,aAAa,CAAC,CAAC;AACzE,iBAAA;AACJ,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,OAAqB,EAAE;gBAC9C,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;AAC5D,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,aAAyB,EAAA;AAChC,QAAA,KAAK,IAAI,IAAI,IAAI,aAAa,EAAE;AAC5B,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACxB,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC;KAC3C;AAED,IAAA,UAAU,CAAC,KAA+C,EAAA;AACtD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACvC;AAED,IAAA,YAAY,CAAC,KAA+C,EAAA;AACxD,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;KACxE;IAED,kBAAkB,GAAA;AACd,QAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AACjC,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACzB,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAA;QACrC,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;AACV,SAAA;AAED,QAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACpB,YAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAE7B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE;AAC5C,gBAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5C,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,IAAc,EAAE,IAAW,EAAE,aAAyB,EAAA;AACpE,QAAA,IAAI,IAAI,EAAE;AACN,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACvB,gBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzB,gBAAA,OAAO,IAAI,CAAC,IAAI,CAAC,GAAU,CAAC,CAAC;AAChC,aAAA;AAED,YAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC3C,gBAAA,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AAC1D,iBAAA;AACJ,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,OAAqB,EAAE;gBAC9C,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AAC1D,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,IAAc,EAAA;QACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KAChD;AAED,IAAA,oBAAoB,CAAC,IAAc,EAAA;AAC/B,QAAA,IAAI,KAAK,GAAW,CAAC,CAAC,CAAC;QAEvB,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;gBACjC,IAAI,aAAa,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;gBAC1F,KAAK,GAAG,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACxC,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACjC,oBAAA,IAAI,aAAa,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,KAAK,YAAY,IAAI,IAAI,CAAC;AAChG,oBAAA,IAAI,aAAa,EAAE;wBACf,KAAK,GAAG,CAAC,CAAC;wBACV,MAAM;AACT,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,QAAQ,CAAC,KAA0B,EAAA;AAC/B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAE9B,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;YACjC,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,YAAA,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;AAC1C,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAA4B,EAAA;AACnC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnC;AAED,IAAA,YAAY,CAAC,KAAY,EAAA;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE;;YAEf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;QACzB,UAAU,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAED,cAAc,GAAA;QACV,OAAO;AACH,YAAA,yCAAyC,EAAE,IAAI;AAC/C,YAAA,mBAAmB,EAAE,IAAI,CAAC,OAAO,KAAK,MAAM;YAC5C,YAAY,EAAE,IAAI,CAAC,QAAQ;YAC3B,SAAS,EAAE,IAAI,CAAC,OAAO;AACvB,YAAA,kBAAkB,EAAE,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ;SACzF,CAAC;KACL;IAED,UAAU,GAAA;QACN,OAAO;AACH,YAAA,oBAAoB,EAAE,IAAI;AAC1B,YAAA,eAAe,EAAE,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,WAAW;YAChD,0BAA0B,EAAE,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU;SACnE,CAAC;KACL;AAED,IAAA,IAAI,UAAU,GAAA;AACV,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;KAC9D;AAED,IAAA,IAAI,YAAY,GAAA;AACZ,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC;KACrD;AAED,IAAA,IAAI,KAAK,GAAA;AACL,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAc,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,KAAK,QAAQ,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;KAC/J;uGApxBQ,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAV,UAAU,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAeC,gBAAgB,CAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAKhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EA0FhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,CAAA,sBAAA,EAAA,sBAAA,EAyBhB,gBAAgB,CAAA,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAKhB,gBAAgB,CAAA,EAAA,oBAAA,EAAA,CAAA,sBAAA,EAAA,sBAAA,EAKhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,gBAAgB,CAKhB,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAAA,gBAAgB,CAoBhB,EAAA,aAAA,EAAA,eAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAyChB,EAAA,OAAA,EAAA,SAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CA3NzB,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,aAAA,EAAA,4BAAA,EAAA,SAAA,EAAA,8BAAA,EAAA,wBAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,SAAA,EAAA,CAAC,yBAAyB,CAAC,EA4RrB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EApcpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sCAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qCAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,uBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+JT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,+tCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,cAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,eAAA,EAAA,aAAA,EAAA,WAAA,EAAA,OAAA,EAAA,YAAA,EAAA,aAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,SAAA,EAAA,aAAA,EAAA,cAAA,EAAA,WAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,cAAA,EAAA,cAAA,EAAA,MAAA,EAAA,eAAA,EAAA,uBAAA,EAAA,sBAAA,EAAA,aAAA,EAAA,cAAA,EAAA,SAAA,EAAA,mBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,yBAAA,EAAA,YAAA,EAAA,YAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAoyB+F,UAAU,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAxxB7H,UAAU,EAAA,UAAA,EAAA,CAAA;kBA7KtB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACd,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+JT,EAEK,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACjC,wBAAA,+BAA+B,EAAE,aAAa;AAC9C,wBAAA,8BAA8B,EAAE,SAAS;AACzC,wBAAA,gCAAgC,EAAE,wBAAwB;qBAC7D,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACpC,SAAA,EAAA,CAAC,yBAAyB,CAAC,EAAA,aAAA,EACvB,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,+tCAAA,CAAA,EAAA,CAAA;wKAO5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,oBAAoB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,sBAAsB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,oBAAoB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAMzB,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAYO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAYO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAWkC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAOG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAKG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEN,WAAW,EAAA,CAAA;sBAAlC,SAAS;uBAAC,WAAW,CAAA;gBAEG,UAAU,EAAA,CAAA;sBAAlC,SAAS;uBAAC,YAAY,CAAA;gBAEF,eAAe,EAAA,CAAA;sBAAnC,SAAS;uBAAC,QAAQ,CAAA;gBAEA,aAAa,EAAA,CAAA;sBAA/B,SAAS;uBAAC,MAAM,CAAA;gBAEG,OAAO,EAAA,CAAA;sBAA1B,SAAS;uBAAC,OAAO,CAAA;gBAEI,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEiB,oCAAoC,EAAA,CAAA;sBAAxE,SAAS;uBAAC,wBAAwB,CAAA;gBAEC,mCAAmC,EAAA,CAAA;sBAAtE,SAAS;uBAAC,uBAAuB,CAAA;;MAmfzB,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAhB,gBAAgB,EAAA,YAAA,EAAA,CA5xBhB,UAAU,CAAA,EAAA,OAAA,EAAA,CAwxBT,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,CAAA,EAAA,OAAA,EAAA,CAxxB7H,UAAU,EAyxBG,aAAa,EAAE,YAAY,EAAE,UAAU,CAAA,EAAA,CAAA,CAAA;wGAGpD,gBAAgB,EAAA,OAAA,EAAA,CAJf,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,EAChH,aAAa,EAAE,YAAY,EAAE,UAAU,CAAA,EAAA,CAAA,CAAA;;2FAGpD,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,eAAe,CAAC;oBACvI,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,CAAC;oBAC9D,YAAY,EAAE,CAAC,UAAU,CAAC;AAC7B,iBAAA,CAAA;;;ACn/BD;;AAEG;;;;"}