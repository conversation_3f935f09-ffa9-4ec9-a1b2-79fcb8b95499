{"version": 3, "file": "primeng-focustrap.mjs", "sources": ["../../src/app/components/focustrap/focustrap.ts", "../../src/app/components/focustrap/primeng-focustrap.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { Directive, ElementRef, Input, NgModule, inject, booleanAttribute, PLATFORM_ID, SimpleChanges } from '@angular/core';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\n@Directive({\n    selector: '[pFocusTrap]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class FocusTrap {\n    /**\n     * When set as true, focus wouldn't be managed.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) pFocusTrapDisabled: boolean = false;\n\n    platformId = inject(PLATFORM_ID);\n\n    host: ElementRef = inject(ElementRef);\n\n    document: Document = inject(DOCUMENT);\n\n    firstHiddenFocusableElement!: HTMLElement;\n\n    lastHiddenFocusableElement!: HTMLElement;\n\n    ngOnInit() {\n        if (isPlatformBrowser(this.platformId) && !this.pFocusTrapDisabled) {\n            !this.firstHiddenFocusableElement && !this.lastHiddenFocusableElement && this.createHiddenFocusableElements();\n        }\n    }\n\n    ngOnChanges(changes: SimpleChanges) {\n        if (changes.pFocusTrapDisabled && isPlatformBrowser(this.platformId)) {\n            if (changes.pFocusTrapDisabled.currentValue) {\n                this.removeHiddenFocusableElements();\n            } else {\n                this.createHiddenFocusableElements();\n            }\n        }\n    }\n\n    removeHiddenFocusableElements() {\n        if (this.firstHiddenFocusableElement && this.firstHiddenFocusableElement.parentNode) {\n            this.firstHiddenFocusableElement.parentNode.removeChild(this.firstHiddenFocusableElement);\n        }\n\n        if (this.lastHiddenFocusableElement && this.lastHiddenFocusableElement.parentNode) {\n            this.lastHiddenFocusableElement.parentNode.removeChild(this.lastHiddenFocusableElement);\n        }\n    }\n    getComputedSelector(selector) {\n        return `:not(.p-hidden-focusable):not([data-p-hidden-focusable=\"true\"])${selector ?? ''}`;\n    }\n\n    createHiddenFocusableElements() {\n        const tabindex = '0';\n\n        const createFocusableElement = (onFocus) => {\n            return DomHandler.createElement('span', {\n                class: 'p-hidden-accessible p-hidden-focusable',\n                tabindex,\n                role: 'presentation',\n                'data-p-hidden-accessible': true,\n                'data-p-hidden-focusable': true,\n                onFocus: onFocus?.bind(this)\n            });\n        };\n\n        this.firstHiddenFocusableElement = createFocusableElement(this.onFirstHiddenElementFocus);\n        this.lastHiddenFocusableElement = createFocusableElement(this.onLastHiddenElementFocus);\n\n        this.firstHiddenFocusableElement.setAttribute('data-pc-section', 'firstfocusableelement');\n        this.lastHiddenFocusableElement.setAttribute('data-pc-section', 'lastfocusableelement');\n\n        this.host.nativeElement.prepend(this.firstHiddenFocusableElement);\n        this.host.nativeElement.append(this.lastHiddenFocusableElement);\n    }\n\n    onFirstHiddenElementFocus(event) {\n        const { currentTarget, relatedTarget } = event;\n        const focusableElement =\n            relatedTarget === this.lastHiddenFocusableElement || !this.host.nativeElement?.contains(relatedTarget) ? DomHandler.getFirstFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.lastHiddenFocusableElement;\n\n        DomHandler.focus(focusableElement);\n    }\n\n    onLastHiddenElementFocus(event) {\n        const { currentTarget, relatedTarget } = event;\n        const focusableElement =\n            relatedTarget === this.firstHiddenFocusableElement || !this.host.nativeElement?.contains(relatedTarget) ? DomHandler.getLastFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.firstHiddenFocusableElement;\n\n        DomHandler.focus(focusableElement);\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [FocusTrap],\n    declarations: [FocusTrap]\n})\nexport class FocusTrapModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAIA;;;AAGG;MAOU,SAAS,CAAA;AAClB;;;AAGG;IACqC,kBAAkB,GAAY,KAAK,CAAC;AAE5E,IAAA,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AAEjC,IAAA,IAAI,GAAe,MAAM,CAAC,UAAU,CAAC,CAAC;AAEtC,IAAA,QAAQ,GAAa,MAAM,CAAC,QAAQ,CAAC,CAAC;AAEtC,IAAA,2BAA2B,CAAe;AAE1C,IAAA,0BAA0B,CAAe;IAEzC,QAAQ,GAAA;QACJ,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;AAChE,YAAA,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACjH,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;QAC9B,IAAI,OAAO,CAAC,kBAAkB,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAClE,YAAA,IAAI,OAAO,CAAC,kBAAkB,CAAC,YAAY,EAAE;gBACzC,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACxC,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACxC,aAAA;AACJ,SAAA;KACJ;IAED,6BAA6B,GAAA;QACzB,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,2BAA2B,CAAC,UAAU,EAAE;YACjF,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;AAC7F,SAAA;QAED,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE;YAC/E,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;AAC3F,SAAA;KACJ;AACD,IAAA,mBAAmB,CAAC,QAAQ,EAAA;AACxB,QAAA,OAAO,CAAkE,+DAAA,EAAA,QAAQ,IAAI,EAAE,EAAE,CAAC;KAC7F;IAED,6BAA6B,GAAA;QACzB,MAAM,QAAQ,GAAG,GAAG,CAAC;AAErB,QAAA,MAAM,sBAAsB,GAAG,CAAC,OAAO,KAAI;AACvC,YAAA,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE;AACpC,gBAAA,KAAK,EAAE,wCAAwC;gBAC/C,QAAQ;AACR,gBAAA,IAAI,EAAE,cAAc;AACpB,gBAAA,0BAA0B,EAAE,IAAI;AAChC,gBAAA,yBAAyB,EAAE,IAAI;AAC/B,gBAAA,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC;AAC/B,aAAA,CAAC,CAAC;AACP,SAAC,CAAC;QAEF,IAAI,CAAC,2BAA2B,GAAG,sBAAsB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1F,IAAI,CAAC,0BAA0B,GAAG,sBAAsB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAExF,IAAI,CAAC,2BAA2B,CAAC,YAAY,CAAC,iBAAiB,EAAE,uBAAuB,CAAC,CAAC;QAC1F,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,iBAAiB,EAAE,sBAAsB,CAAC,CAAC;QAExF,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;KACnE;AAED,IAAA,yBAAyB,CAAC,KAAK,EAAA;AAC3B,QAAA,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAC/C,QAAA,MAAM,gBAAgB,GAClB,aAAa,KAAK,IAAI,CAAC,0BAA0B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,wBAAwB,CAAC,aAAa,CAAC,aAAa,EAAE,2BAA2B,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AAE7O,QAAA,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;KACtC;AAED,IAAA,wBAAwB,CAAC,KAAK,EAAA;AAC1B,QAAA,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAC/C,QAAA,MAAM,gBAAgB,GAClB,aAAa,KAAK,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,uBAAuB,CAAC,aAAa,CAAC,aAAa,EAAE,2BAA2B,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;AAE9O,QAAA,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;KACtC;uGApFQ,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,uGAKE,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAL3B,SAAS,EAAA,UAAA,EAAA,CAAA;kBANrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;8BAM2C,kBAAkB,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;;MAuF7B,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EA5Ff,YAAA,EAAA,CAAA,SAAS,CAwFR,EAAA,OAAA,EAAA,CAAA,YAAY,aAxFb,SAAS,CAAA,EAAA,CAAA,CAAA;AA4FT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAJd,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,SAAS,CAAC;oBACpB,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;ACzGD;;AAEG;;;;"}