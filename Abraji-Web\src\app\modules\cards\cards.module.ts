import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AllCardsComponent } from './all-cards/all-cards.component';
import { CardsRoutingModule } from './cards-routing.module';
import { TableSkeletonComponent } from '../shared/skeletons/table/table.component';
import { SmallCardSkeletonComponent } from '../shared/skeletons/small-card-skeleton/small-card-skeleton.component';
import { SingleCardComponent } from './single-card/single-card.component';
import { TranslocoModule } from '@jsverse/transloco';
import { SharedModule } from '../shared/shared.module';

@NgModule({
  declarations: [AllCardsComponent, SingleCardComponent],
  imports: [
    CommonModule,
    CardsRoutingModule,
    TableSkeletonComponent,
    SmallCardSkeletonComponent,
    TranslocoModule,
    SharedModule,
  ],
})
export class CardsModule {}
