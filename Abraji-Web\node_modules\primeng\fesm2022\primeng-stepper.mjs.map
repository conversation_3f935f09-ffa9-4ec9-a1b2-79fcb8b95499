{"version": 3, "file": "primeng-stepper.mjs", "sources": ["../../src/app/components/stepper/stepper.ts", "../../src/app/components/stepper/primeng-stepper.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewEncapsulation } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { animate, state, style, transition, trigger } from '@angular/animations';\n\n@Component({\n    selector: 'p-stepperHeader',\n    template: `\n        <ng-container *ngIf=\"template; else buttonRef\">\n            <ng-container\n                *ngTemplateOutlet=\"\n                    template;\n                    context: {\n                        index: index,\n                        active: active,\n                        highlighted: highlighted,\n                        class: 'p-stepper-action',\n                        headerClass: 'p-stepper-action',\n                        numberClass: 'p-stepper-number',\n                        titleClass: 'p-stepper-title',\n                        onClick: onClick\n                    }\n                \"\n            ></ng-container>\n        </ng-container>\n        <ng-template #buttonRef>\n            <p-button [id]=\"id\" class=\"p-stepper-action\" role=\"tab\" [tabindex]=\"disabled ? -1 : undefined\" [aria-controls]=\"ariaControls\" (click)=\"onClick.emit($event, index)\">\n                <span class=\"p-stepper-number\">{{ index + 1 }}</span>\n                <span class=\"p-stepper-title\">{{ getStepProp }}</span>\n            </p-button>\n        </ng-template>\n    `,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class StepperHeader {\n    @Input() id: string | undefined;\n\n    @Input() template: TemplateRef<any> | undefined;\n\n    @Input() stepperPanel: StepperPanel;\n\n    @Input() index: string | undefined;\n\n    @Input() disabled: boolean | undefined;\n\n    @Input() active: boolean | undefined;\n\n    @Input() highlighted: boolean | undefined;\n\n    @Input() getStepProp: string | undefined;\n\n    @Input() ariaControls: string | undefined;\n\n    @Output() onClick = new EventEmitter<void>();\n}\n\n@Component({\n    selector: 'p-stepperSeparator',\n    template: `\n        <ng-container *ngIf=\"template; else span\">\n            <ng-container *ngTemplateOutlet=\"template; context: { index: index, active: active, highlighted: highlighted, class: separatorClass }\"></ng-container>\n        </ng-container>\n        <ng-template #span>\n            <span [class]=\"separatorClass\" aria-hidden=\"true\"></span>\n        </ng-template>\n    `,\n    host: {\n        class: 'p-stepper-separator'\n    }\n})\nexport class StepperSeparator {\n    @Input() template: TemplateRef<any> | undefined;\n\n    @Input() separatorClass: string | undefined;\n\n    @Input() stepperPanel: StepperPanel;\n\n    @Input() index: string | undefined;\n\n    @Input() active: boolean | undefined;\n\n    @Input() highlighted: boolean | undefined;\n}\n\n@Component({\n    selector: 'p-stepperContent',\n    template: ` <div [id]=\"id\" role=\"tabpanel\" data-pc-name=\"stepperpanel\" [attr.data-pc-index]=\"index\" [attr.data-p-active]=\"active\" [attr.aria-labelledby]=\"ariaLabelledby\">\n        <ng-container *ngIf=\"template\">\n            <ng-container *ngTemplateOutlet=\"template; context: { index: index, active: active, highlighted: highlighted, onClick: onClick, prevCallback: prevCallback, nextCallback: nextCallback }\"></ng-container>\n        </ng-container>\n        <ng-template *ngIf=\"!template\">\n            <ng-container *ngIf=\"stepperPanel\">\n                <ng-container *ngTemplateOutlet=\"stepperPanel\"></ng-container>\n            </ng-container>\n        </ng-template>\n    </div>`,\n\n    host: {\n        '[class.p-stepper-content]': 'true',\n        '[class.p-element]': 'true',\n        '[class.p-toggleable-content]': \"orientation === 'vertical'\"\n    }\n})\nexport class StepperContent {\n    @Input() id: string | undefined;\n\n    @Input() orientation: 'vertical' | 'horizontal';\n\n    @Input() template: TemplateRef<any> | undefined;\n\n    @Input() ariaLabelledby: string | undefined;\n\n    @Input() stepperPanel: StepperPanel;\n\n    @Input() index: string | undefined;\n\n    @Input() active: boolean | undefined;\n\n    @Input() highlighted: boolean | undefined;\n\n    @Output() onClick = new EventEmitter<void>();\n\n    @Output() prevCallback = new EventEmitter<void>();\n\n    @Output() nextCallback = new EventEmitter<void>();\n}\n\n@Component({\n    selector: 'p-stepperPanel',\n    template: ` <ng-content></ng-content> `,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class StepperPanel {\n    @Input() header: string | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    startTemplate: Nullable<TemplateRef<any>>;\n\n    contentTemplate: Nullable<TemplateRef<any>>;\n\n    separatorTemplate: Nullable<TemplateRef<any>>;\n\n    endTemplate: Nullable<TemplateRef<any>>;\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'separator':\n                    this.separatorTemplate = item.template;\n                    break;\n            }\n        });\n    }\n}\n\n/**\n * The Stepper component displays a wizard-like workflow by guiding users through the multi-step progression.\n * @group Components\n */\n@Component({\n    selector: 'p-stepper',\n    template: `\n        <div role=\"tablist\">\n            <ng-container *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </ng-container>\n            <ng-container *ngIf=\"orientation === 'horizontal'; else vertical\">\n                <ul class=\"p-stepper-nav\">\n                    <ng-template ngFor let-step [ngForOf]=\"panels\" let-index=\"index\" [ngForTrackBy]=\"trackByFn\">\n                        <li\n                            [key]=\"getStepKey(step, index)\"\n                            class=\"p-stepper-header\"\n                            [ngClass]=\"{\n                                'p-highlight': isStepActive(index),\n                                'p-disabled': isItemDisabled(index)\n                            }\"\n                            [attr.aria-current]=\"isStepActive(index) ? 'step' : undefined\"\n                            role=\"presentation\"\n                            [data-pc-name]=\"stepperPanel\"\n                            [data-p-highlight]=\"isStepActive(index)\"\n                            [data-p-disabled]=\"isItemDisabled(index)\"\n                            [data-pc-index]=\"index\"\n                            [data-p-active]=\"isStepActive(index)\"\n                        >\n                            <p-stepperHeader\n                                [id]=\"getStepHeaderActionId(index)\"\n                                [template]=\"step.headerTemplate\"\n                                [stepperPanel]=\"step\"\n                                [getStepProp]=\"getStepProp(step, 'header')\"\n                                [index]=\"index\"\n                                [disabled]=\"isItemDisabled(index)\"\n                                [active]=\"isStepActive(index)\"\n                                [highlighted]=\"index < activeStep\"\n                                [class]=\"'p-stepper-action'\"\n                                [aria-controls]=\"getStepContentId(index)\"\n                                (onClick)=\"onItemClick($event, index)\"\n                            ></p-stepperHeader>\n\n                            <ng-container *ngIf=\"index !== stepperPanels.length - 1\">\n                                <p-stepperSeparator [template]=\"step.separatorTemplate\" [separatorClass]=\"'p-stepper-separator'\" [stepperPanel]=\"step\" [index]=\"index\" [active]=\"isStepActive(index)\" [highlighted]=\"index < activeStep\" />\n                            </ng-container>\n                        </li>\n                    </ng-template>\n                </ul>\n                <div class=\"p-stepper-panels\">\n                    <ng-template ngFor let-step [ngForOf]=\"panels\" let-index=\"index\" [ngForTrackBy]=\"trackByFn\">\n                        <ng-container *ngIf=\"isStepActive(index)\">\n                            <p-stepperContent\n                                [id]=\"getStepContentId(index)\"\n                                [template]=\"step.contentTemplate\"\n                                [orientation]=\"orientation\"\n                                [stepperPanel]=\"step\"\n                                [index]=\"index\"\n                                [active]=\"isStepActive(index)\"\n                                [highlighted]=\"index < activeStep\"\n                                [ariaLabelledby]=\"getStepHeaderActionId(index)\"\n                                (onClick)=\"onItemClick($event, index)\"\n                                (nextCallback)=\"nextCallback($event, index)\"\n                                (prevCallback)=\"prevCallback($event, index)\"\n                            />\n                        </ng-container>\n                    </ng-template>\n                </div>\n            </ng-container>\n            <ng-template #vertical>\n                <ng-template ngFor let-step [ngForOf]=\"panels\" let-index=\"index\" [ngForTrackBy]=\"trackByFn\">\n                    <div\n                        [key]=\"getStepKey(step, index)\"\n                        class=\"p-stepper-panel\"\n                        [ngClass]=\"{\n                            'p-stepper-panel-active': orientation === 'vertical' && isStepActive(index)\n                        }\"\n                        [attr.aria-current]=\"isStepActive(index) ? 'step' : undefined\"\n                        [data-pc-name]=\"'stepperpanel'\"\n                        [data-p-highlight]=\"isStepActive(index)\"\n                        [data-p-disabled]=\"isItemDisabled(index)\"\n                        [data-pc-index]=\"index\"\n                        [data-p-active]=\"isStepActive(index)\"\n                    >\n                        <div\n                            class=\"p-stepper-header \"\n                            [ngClass]=\"{\n                                'p-highlight': isStepActive(index),\n                                'p-disabled': isItemDisabled(index)\n                            }\"\n                        >\n                            <p-stepperHeader\n                                [id]=\"getStepHeaderActionId(index)\"\n                                [template]=\"step.headerTemplate\"\n                                [stepperPanel]=\"step\"\n                                [getStepProp]=\"getStepProp(step, 'header')\"\n                                [index]=\"index\"\n                                [disabled]=\"isItemDisabled(index)\"\n                                [active]=\"isStepActive(index)\"\n                                [highlighted]=\"index < activeStep\"\n                                [class]=\"'p-stepper-action'\"\n                                [aria-controls]=\"getStepContentId(index)\"\n                                (onClick)=\"onItemClick($event, index)\"\n                            ></p-stepperHeader>\n                        </div>\n\n                        <div class=\"p-stepper-toggleable-content\" [@tabContent]=\"isStepActive(index) ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\">\n                            <ng-container *ngIf=\"index !== stepperPanels.length - 1\">\n                                <p-stepperSeparator [template]=\"step.separatorTemplate\" [separatorClass]=\"'p-stepper-separator'\" [stepperPanel]=\"step\" [index]=\"index\" [active]=\"isStepActive(index)\" [highlighted]=\"index < activeStep\" />\n                            </ng-container>\n                            <p-stepperContent\n                                [id]=\"getStepContentId(index)\"\n                                [template]=\"step.contentTemplate\"\n                                [orientation]=\"orientation\"\n                                [stepperPanel]=\"step\"\n                                [index]=\"index\"\n                                [active]=\"isStepActive(index)\"\n                                [highlighted]=\"index < activeStep\"\n                                [ariaLabelledby]=\"getStepHeaderActionId(index)\"\n                                (onClick)=\"onItemClick($event, index)\"\n                                (nextCallback)=\"nextCallback($event, index)\"\n                                (prevCallback)=\"prevCallback($event, index)\"\n                            />\n                        </div>\n                    </div>\n                </ng-template>\n            </ng-template>\n            <ng-container *ngIf=\"endTemplate\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </ng-container>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./stepper.css'],\n    host: {\n        '[class.p-stepper]': 'true',\n        '[class.p-component]': 'true',\n        '[class.p-stepper-vertical]': \"orientation === 'vertical'\"\n    },\n    animations: [\n        trigger('tabContent', [\n            state(\n                'hidden',\n                style({\n                    height: '0',\n                    visibility: 'hidden'\n                })\n            ),\n            state(\n                'visible',\n                style({\n                    height: '*',\n                    visibility: 'visible'\n                })\n            ),\n            transition('visible <=> hidden', [animate('250ms cubic-bezier(0.86, 0, 0.07, 1)')]),\n            transition('void => *', animate(0))\n        ])\n    ]\n})\nexport class Stepper implements AfterContentInit {\n    /**\n     * Active step index of stepper.\n     * @group Props\n     */\n    @Input() activeStep: number | undefined | null = 0;\n    /**\n     * Orientation of the stepper.\n     * @group Props\n     */\n    @Input() orientation: 'vertical' | 'horizontal' = 'horizontal';\n    /**\n     * Whether the steps are clickable or not.\n     * @group Props\n     */\n    @Input() linear: boolean = false;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    @Input() transitionOptions: string = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n\n    @ContentChildren(StepperPanel) stepperPanels: QueryList<StepperPanel> | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @Output() onClick: EventEmitter<any> = new EventEmitter<any>();\n\n    /**\n     * Emitted when the value changes.\n     * @param {ActiveStepChangeEvent} event - custom change event.\n     * @group Emits\n     */\n    @Output() activeStepChange: EventEmitter<number> = new EventEmitter<number>();\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    startTemplate: Nullable<TemplateRef<any>>;\n\n    separatorTemplate: Nullable<TemplateRef<any>>;\n\n    endTemplate: Nullable<TemplateRef<any>>;\n\n    id: string = UniqueComponentId();\n\n    panels!: StepperPanel[];\n\n    isStepActive(index: number) {\n        return this.activeStep === index;\n    }\n\n    getStepProp(step) {\n        if (step?.header) {\n            return step.header;\n        }\n\n        if (step?.content) {\n            return step.content;\n        }\n        return undefined;\n    }\n\n    getStepKey(step, index) {\n        return this.getStepProp(step) || index;\n    }\n\n    getStepHeaderActionId(index) {\n        return `${this.id}_${index}_header_action`;\n    }\n\n    getStepContentId(index) {\n        return `${this.id}_${index}_content`;\n    }\n\n    updateActiveStep(event, index) {\n        this.activeStep = index;\n\n        this.activeStepChange.emit(this.activeStep);\n    }\n\n    onItemClick(event, index) {\n        if (this.linear) {\n            event.preventDefault();\n\n            return;\n        }\n        if (index !== this.activeStep) {\n            this.updateActiveStep(event, index);\n        }\n    }\n\n    isItemDisabled(index) {\n        return this.linear && !this.isStepActive(index);\n    }\n\n    prevCallback(event, index) {\n        if (index !== 0) {\n            this.updateActiveStep(event, index - 1);\n        }\n    }\n\n    nextCallback(event, index) {\n        if (index !== this.stepperPanels.length - 1) {\n            this.updateActiveStep(event, index + 1);\n        }\n    }\n\n    trackByFn(index: number): number {\n        return index;\n    }\n\n    ngAfterContentInit() {\n        this.panels = (this.stepperPanels as QueryList<StepperPanel>).toArray();\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n\n                default:\n                    break;\n            }\n        });\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule],\n    exports: [Stepper, StepperPanel, StepperContent, StepperHeader, StepperSeparator, SharedModule],\n    declarations: [Stepper, StepperPanel, StepperPanel, StepperContent, StepperHeader, StepperSeparator]\n})\nexport class StepperModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;MAsCa,aAAa,CAAA;AACb,IAAA,EAAE,CAAqB;AAEvB,IAAA,QAAQ,CAA+B;AAEvC,IAAA,YAAY,CAAe;AAE3B,IAAA,KAAK,CAAqB;AAE1B,IAAA,QAAQ,CAAsB;AAE9B,IAAA,MAAM,CAAsB;AAE5B,IAAA,WAAW,CAAsB;AAEjC,IAAA,WAAW,CAAqB;AAEhC,IAAA,YAAY,CAAqB;AAEhC,IAAA,OAAO,GAAG,IAAI,YAAY,EAAQ,CAAC;uGAnBpC,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,EA7BZ,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;AAwBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;2FAKQ,aAAa,EAAA,UAAA,EAAA,CAAA;kBA/BzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;AAwBT,IAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;8BAEY,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAEG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAEG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEI,OAAO,EAAA,CAAA;sBAAhB,MAAM;;MAiBE,gBAAgB,CAAA;AAChB,IAAA,QAAQ,CAA+B;AAEvC,IAAA,cAAc,CAAqB;AAEnC,IAAA,YAAY,CAAe;AAE3B,IAAA,KAAK,CAAqB;AAE1B,IAAA,MAAM,CAAsB;AAE5B,IAAA,WAAW,CAAsB;uGAXjC,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EAZf,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;AAOT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;2FAKQ,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAd5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;AAOT,IAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,qBAAqB;AAC/B,qBAAA;AACJ,iBAAA,CAAA;8BAEY,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEG,WAAW,EAAA,CAAA;sBAAnB,KAAK;;MAsBG,cAAc,CAAA;AACd,IAAA,EAAE,CAAqB;AAEvB,IAAA,WAAW,CAA4B;AAEvC,IAAA,QAAQ,CAA+B;AAEvC,IAAA,cAAc,CAAqB;AAEnC,IAAA,YAAY,CAAe;AAE3B,IAAA,KAAK,CAAqB;AAE1B,IAAA,MAAM,CAAsB;AAE5B,IAAA,WAAW,CAAsB;AAEhC,IAAA,OAAO,GAAG,IAAI,YAAY,EAAQ,CAAC;AAEnC,IAAA,YAAY,GAAG,IAAI,YAAY,EAAQ,CAAC;AAExC,IAAA,YAAY,GAAG,IAAI,YAAY,EAAQ,CAAC;uGArBzC,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EAjBb,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,4BAAA,EAAA,4BAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;AASH,UAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;2FAQE,cAAc,EAAA,UAAA,EAAA,CAAA;kBAnB1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;AASH,UAAA,CAAA;AAEP,oBAAA,IAAI,EAAE;AACF,wBAAA,2BAA2B,EAAE,MAAM;AACnC,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,8BAA8B,EAAE,4BAA4B;AAC/D,qBAAA;AACJ,iBAAA,CAAA;8BAEY,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAEG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEI,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAEG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAEG,YAAY,EAAA,CAAA;sBAArB,MAAM;;MAUE,YAAY,CAAA;AACZ,IAAA,MAAM,CAAqB;AAEJ,IAAA,SAAS,CAAqC;AAE9E,IAAA,cAAc,CAA6B;AAE3C,IAAA,aAAa,CAA6B;AAE1C,IAAA,eAAe,CAA6B;AAE5C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,WAAW,CAA6B;IAExC,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AACV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;uGA7BQ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAZ,YAAY,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAGJ,aAAa,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EARpB,CAA6B,2BAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;2FAK9B,YAAY,EAAA,UAAA,EAAA,CAAA;kBAPxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,CAA6B,2BAAA,CAAA;AACvC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;8BAEY,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAE0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;AA6BlC;;;AAGG;MA8JU,OAAO,CAAA;AAChB;;;AAGG;IACM,UAAU,GAA8B,CAAC,CAAC;AACnD;;;AAGG;IACM,WAAW,GAA8B,YAAY,CAAC;AAC/D;;;AAGG;IACM,MAAM,GAAY,KAAK,CAAC;AACjC;;;AAGG;IACM,iBAAiB,GAAW,sCAAsC,CAAC;AAE7C,IAAA,aAAa,CAAsC;AAElD,IAAA,SAAS,CAAuC;AAEtE,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAO,CAAC;AAE/D;;;;AAIG;AACO,IAAA,gBAAgB,GAAyB,IAAI,YAAY,EAAU,CAAC;AAE9E,IAAA,cAAc,CAA6B;AAE3C,IAAA,aAAa,CAA6B;AAE1C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,WAAW,CAA6B;IAExC,EAAE,GAAW,iBAAiB,EAAE,CAAC;AAEjC,IAAA,MAAM,CAAkB;AAExB,IAAA,YAAY,CAAC,KAAa,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC;KACpC;AAED,IAAA,WAAW,CAAC,IAAI,EAAA;QACZ,IAAI,IAAI,EAAE,MAAM,EAAE;YACd,OAAO,IAAI,CAAC,MAAM,CAAC;AACtB,SAAA;QAED,IAAI,IAAI,EAAE,OAAO,EAAE;YACf,OAAO,IAAI,CAAC,OAAO,CAAC;AACvB,SAAA;AACD,QAAA,OAAO,SAAS,CAAC;KACpB;IAED,UAAU,CAAC,IAAI,EAAE,KAAK,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;KAC1C;AAED,IAAA,qBAAqB,CAAC,KAAK,EAAA;AACvB,QAAA,OAAO,GAAG,IAAI,CAAC,EAAE,CAAI,CAAA,EAAA,KAAK,gBAAgB,CAAC;KAC9C;AAED,IAAA,gBAAgB,CAAC,KAAK,EAAA;AAClB,QAAA,OAAO,GAAG,IAAI,CAAC,EAAE,CAAI,CAAA,EAAA,KAAK,UAAU,CAAC;KACxC;IAED,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAC/C;IAED,WAAW,CAAC,KAAK,EAAE,KAAK,EAAA;QACpB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,OAAO;AACV,SAAA;AACD,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,EAAE;AAC3B,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACvC,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;KACnD;IAED,YAAY,CAAC,KAAK,EAAE,KAAK,EAAA;QACrB,IAAI,KAAK,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAC3C,SAAA;KACJ;IAED,YAAY,CAAC,KAAK,EAAE,KAAK,EAAA;QACrB,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YACzC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAC3C,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAa,EAAA;AACnB,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,MAAM,GAAI,IAAI,CAAC,aAAyC,CAAC,OAAO,EAAE,CAAC;QACvE,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,KAAK;AACN,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,MAAM;AAEV,gBAAA;oBACI,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;uGA/HQ,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EAsBC,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,MAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,0BAAA,EAAA,4BAAA,EAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,SAAA,EAAA,YAAY,EAEZ,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAnLpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6HT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,qNAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAlMQ,cAAc,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,aAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,OAAA,EAAA,QAAA,EAAA,aAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EArEd,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,UAAA,EAAA,cAAA,EAAA,OAAA,EAAA,UAAA,EAAA,QAAA,EAAA,aAAA,EAAA,aAAA,EAAA,cAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAoCb,gBAAgB,EA4Ob,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,OAAA,EAAA,QAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,YAAY,EAAE;AAClB,gBAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACX,oBAAA,UAAU,EAAE,QAAQ;AACvB,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACX,oBAAA,UAAU,EAAE,SAAS;AACxB,iBAAA,CAAC,CACL;gBACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,CAAC;AACnF,gBAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;aACtC,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAEQ,OAAO,EAAA,UAAA,EAAA,CAAA;kBA7JnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6HT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,mBAAmB,EAAE,MAAM;AAC3B,wBAAA,qBAAqB,EAAE,MAAM;AAC7B,wBAAA,4BAA4B,EAAE,4BAA4B;qBAC7D,EACW,UAAA,EAAA;wBACR,OAAO,CAAC,YAAY,EAAE;AAClB,4BAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACX,gCAAA,UAAU,EAAE,QAAQ;AACvB,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACX,gCAAA,UAAU,EAAE,SAAS;AACxB,6BAAA,CAAC,CACL;4BACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,CAAC;AACnF,4BAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;yBACtC,CAAC;AACL,qBAAA,EAAA,MAAA,EAAA,CAAA,qNAAA,CAAA,EAAA,CAAA;8BAOQ,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAEyB,aAAa,EAAA,CAAA;sBAA3C,eAAe;uBAAC,YAAY,CAAA;gBAEG,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEpB,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAOG,gBAAgB,EAAA,CAAA;sBAAzB,MAAM;;MAsGE,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAb,aAAa,EAAA,YAAA,EAAA,CAvIb,OAAO,EAjMP,YAAY,EAAZ,YAAY,EA/BZ,cAAc,EArEd,aAAa,EAoCb,gBAAgB,CAAA,EAAA,OAAA,EAAA,CAoYf,YAAY,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CAnI3B,OAAO,EAjMP,YAAY,EA/BZ,cAAc,EArEd,aAAa,EAoCb,gBAAgB,EAqYyD,YAAY,CAAA,EAAA,CAAA,CAAA;AAGrF,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAJZ,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAC8C,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGrF,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACrC,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY,CAAC;AAC/F,oBAAA,YAAY,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,gBAAgB,CAAC;AACvG,iBAAA,CAAA;;;ACjdD;;AAEG;;;;"}