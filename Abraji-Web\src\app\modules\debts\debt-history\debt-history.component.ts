import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DebtHistory } from '../../../core/debts-services/api/debts';
import { ToastService } from '../../shared/toast/toast.service';
import { HttpErrorResponse } from '@angular/common/http';
import { DebtsService } from '../../../core/debts-services/services/debts.service';

@Component({
  selector: 'app-debt-history',
  templateUrl: './debt-history.component.html',
  styleUrl: './debt-history.component.scss'
})
export class DebtHistoryComponent implements OnInit {
  private debtId!: string | null;
  isLoading = false;
  debtHistory: DebtHistory[] = [];

  constructor(
    private route: ActivatedRoute,
    private debtsService: DebtsService,
    protected toastService: ToastService,
  ) { }

  ngOnInit(): void {
    this.route?.paramMap.subscribe((params) => {
      this.debtId = params.get('id');
      if (this.debtId) {
        this.loadDebtHistory(this.debtId);
      }
    });
  }
  loadDebtHistory(debtId: string) {
    this.isLoading = true;
    console.log("loadDebtHistory");
    this.debtsService.getDebtHistory(debtId).subscribe({
      next: (response: any) => {
        this.debtHistory = response.partial_payments;
        this.isLoading = false;
      },
      error: (error: HttpErrorResponse) => {
        this.toastService.addToast("error", "Error Message", error?.error?.message);
        this.isLoading = false;
      }
    });
    }

}
