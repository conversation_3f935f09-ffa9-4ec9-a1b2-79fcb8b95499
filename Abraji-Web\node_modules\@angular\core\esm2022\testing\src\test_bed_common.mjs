/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken, ɵDeferBlockBehavior as DeferBlockBehavior, } from '@angular/core';
/** Whether test modules should be torn down by default. */
export const TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;
/** Whether unknown elements in templates should throw by default. */
export const THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;
/** Whether unknown properties in templates should throw by default. */
export const THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;
/** Whether defer blocks should use manual triggering or play through normally. */
export const DEFER_BLOCK_DEFAULT_BEHAVIOR = DeferBlockBehavior.Playthrough;
/**
 * An abstract class for inserting the root test component element in a platform independent way.
 *
 * @publicApi
 */
export class TestComponentRenderer {
    insertRootElement(rootElementId) { }
    removeAllRootElements() { }
}
/**
 * @publicApi
 */
export const ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');
/**
 * @publicApi
 */
export const ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');
//# sourceMappingURL=data:application/json;base64,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