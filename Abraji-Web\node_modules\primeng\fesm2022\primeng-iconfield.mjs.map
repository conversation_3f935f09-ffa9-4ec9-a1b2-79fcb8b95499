{"version": 3, "file": "primeng-iconfield.mjs", "sources": ["../../src/app/components/iconfield/iconfield.ts", "../../src/app/components/iconfield/primeng-iconfield.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, Input, NgModule, ViewEncapsulation } from '@angular/core';\n\nimport { SharedModule } from 'primeng/api';\n\n/**\n * IconField wraps an input and an icon.\n * @group Components\n */\n@Component({\n    selector: 'p-iconField',\n    template: ` <span class=\"p-icon-field\" [ngClass]=\"containerClass\"><ng-content></ng-content> </span>`,\n    styleUrl: './iconfield.css',\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class IconField {\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    @Input() iconPosition: 'right' | 'left' = 'left';\n\n    get containerClass() {\n        return {\n            'p-icon-field-left': this.iconPosition === 'left',\n            'p-icon-field-right': this.iconPosition === 'right'\n        };\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [IconField, SharedModule],\n    declarations: [IconField]\n})\nexport class IconFieldModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAKA;;;AAGG;MAQU,SAAS,CAAA;AAClB;;;AAGG;IACM,YAAY,GAAqB,MAAM,CAAC;AAEjD,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;AACH,YAAA,mBAAmB,EAAE,IAAI,CAAC,YAAY,KAAK,MAAM;AACjD,YAAA,oBAAoB,EAAE,IAAI,CAAC,YAAY,KAAK,OAAO;SACtD,CAAC;KACL;uGAZQ,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,6FALR,CAA0F,wFAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,oDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAK3F,SAAS,EAAA,UAAA,EAAA,CAAA;kBAPrB,SAAS;+BACI,aAAa,EAAA,QAAA,EACb,0FAA0F,EAErF,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,oDAAA,CAAA,EAAA,CAAA;8BAOtC,YAAY,EAAA,CAAA;sBAApB,KAAK;;MAeG,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,iBApBf,SAAS,CAAA,EAAA,OAAA,EAAA,CAgBR,YAAY,CAhBb,EAAA,OAAA,EAAA,CAAA,SAAS,EAiBG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGxB,eAAe,EAAA,OAAA,EAAA,CAJd,YAAY,EACD,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGxB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;oBAClC,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;ACnCD;;AAEG;;;;"}