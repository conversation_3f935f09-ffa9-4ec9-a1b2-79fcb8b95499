<section class="bg-gray-50 dark:bg-gray-900 p-3">
  <div *ngIf="isLoading">
    <app-table-skeleton></app-table-skeleton>
  </div>

  <div appFlowbiteInit *ngIf="!isLoading" class="overflow-x-auto mt-4">
    <section class="bg-gray-50 dark:bg-gray-900">
      <div class="mx-auto max-w-screen-xl">
        <!-- Start coding here -->
        <div class="bg-white dark:bg-gray-800 relative shadow-xl border-2 sm:rounded-lg">
          <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
            <div class="w-full md:w-1/2">
              <div class="flex items-center">
                <label for="simple-search" class="sr-only">{{'table.search' | transloco}}</label>
                <div class="relative w-full">
                  <div class="absolute inset-y-0 left-0 flex items-center p-3 pointer-events-none">
                    <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor"
                      viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd"
                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                        clip-rule="evenodd" />
                    </svg>
                  </div>
                  <input type="text" id="simple-search" (change)="searchChange($event)"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full ps-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                    placeholder="{{'table.search' | transloco}}..." required="" [value]="requestForm.search">
                </div>
              </div>
            </div>

            <div
              class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
              <div class="flex items-center space-x-3 w-full md:w-auto">
                <!-- Modal toggle -->
                <button data-modal-target="create-expenses-modal" data-modal-toggle="create-expenses-modal"
                  class="w-full text-white md:w-auto flex items-center justify-center mx-2 py-2 px-4 text-sm font-medium bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                  type="button">
                  <svg class="-ml-1 me-1.5 w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                    height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2"
                      d="M8 7V6a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-1M3 18v-7a1 1 0 0 1 1-1h11a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1Zm8-3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z" />
                  </svg>
                  {{ 'resources.addExpenses' | transloco }}
                </button>
                <button id="actionsDropdownButton" data-dropdown-toggle="actionsDropdown"
                  class="w-full md:w-auto flex items-center justify-center me-2 py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  type="button">
                  {{'table.actions' | transloco}}
                  <svg class="-ml-1 ms-1.5 w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path clip-rule="evenodd" fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>
                <div id="actionsDropdown"
                  class="hidden z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                  <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="actionsDropdownButton">
                    <li>
                      <a href="#"
                        class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Export</a>
                    </li>
                  </ul>
                  <div class="py-1">
                    <a href="#"
                      class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">Delete
                      all</a>
                  </div>
                </div>
                <button id="ColumnsDropdownButton" data-dropdown-toggle="ColumnsDropdown"
                  class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  type="button">
                  <svg class="h-4 w-4 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 5v14M9 5v14M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z" />
                  </svg>
                  {{'table.columns' | transloco}}
                  <svg class="-mr-1 ms-1.5 w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path clip-rule="evenodd" fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>
                <div id="ColumnsDropdown" class="z-10 hidden w-48 p-3 bg-white rounded-lg shadow dark:bg-gray-700">
                  <h6 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">{{'table.showColumns' | transloco}}
                  </h6>
                  <ul class="space-y-2 text-sm" aria-labelledby="filterDropdownButton">
                    <li *ngFor="let column of expensesColumnsState" class="flex items-center">
                      <input title="{{column.key}}" type="checkbox" [id]="column.key" [checked]="!column.hidden"
                        (change)="toggleColumnSelection(column.key)"
                        class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                      <label [for]="column.key" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                        {{ ('resources.' + column.key) | transloco | titlecase }}
                      </label>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
              <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <!-- <tr>
                    <th scope="col" class="px-2 py-3">#</th>
                    <ng-container *ngFor="let column of expensesColumnsState">
                      <th scope="col" class="px-4 py-3" *ngIf="!column.hidden">
                        <div class="cursor-pointer flex" (click)="sortByColumn(column.key)">
                          {{ ('resources.' + column.key) | transloco | titlecase }}
                          <svg title="sort" _ngcontent-ng-c3067077598="" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                            class="w-3 h-3 ms-1.5">
                            <path _ngcontent-ng-c3067077598=""
                              d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z">
                            </path>
                          </svg>
                        </div>
                      </th>
                    </ng-container>
                    <th scope="col" class="px-4 py-3">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr> -->
                <tr>
                  <th scope="col" class="px-2 py-3">#</th>
                  <ng-container *ngFor="let column of expensesColumnsState">
                    <th scope="col" class="px-4 py-3" *ngIf="!column.hidden">
                      <!-- provide sorting column -->
                      @if (false) {
                      <div class="cursor-pointer flex" (click)="sortByColumn(column.key)">
                        {{ ('resources.' + column.key) | transloco | titlecase }}
                        <svg title="sort" _ngcontent-ng-c3067077598="" aria-hidden="true"
                          xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
                          class="w-3 h-3 ms-1.5">
                          <path _ngcontent-ng-c3067077598=""
                            d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z">
                          </path>
                        </svg>
                      </div>
                      }@else {
                      {{ ('resources.' + column.key) | transloco | titlecase }}
                      }
                    </th>
                  </ng-container>
                  <th scope="col" class="px-4 py-3">
                    <span scope="col"> {{'table.actions' | transloco}} </span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of tableResponse.data; let i = index" class="border-b dark:border-gray-700">
                  <td class="px-4 py-3">{{ tableResponse.from + i }}</td>
                  <ng-container *ngFor="let column of expensesColumnsState">
                    <td class="px-4 py-3" *ngIf="!column.hidden">
                      <ng-container>
                        @if (column.key === 'date' || column.key === 'created_at' || column.key === 'updated_at') {
                        {{ getPropertyValue(item, column.key) | date:'medium' }}
                        }@else if(column.key === 'type') {
                        @if(item.type === 'in') {

                        <span
                          class="flex items-center bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300"><span
                            class="me-2">
                            <svg class="w-6 h-6 text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                              width="24" height="24" fill="none" viewBox="0 0 24 24">
                              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                stroke-width="2"
                                d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                          </span>
                          <span>
                            {{ getPropertyValue(item, column.key) }}
                          </span>
                        </span>
                        }@else {
                        <span
                          class="flex items-center bg-red-100 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-full dark:bg-red-900 dark:text-red-300"><span
                            class="me-2">
                            <svg class="w-6 h-6 text-red-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                              width="24" height="24" fill="none" viewBox="0 0 24 24">
                              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                stroke-width="2"
                                d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                          </span>
                          <span>
                            {{ getPropertyValue(item, column.key) }}
                          </span>
                        </span>
                        }

                        }@else {
                        {{ getPropertyValue(item, column.key) }}
                        }
                      </ng-container>
                    </td>
                  </ng-container>
                  <td class="px-4 py-3">
                    <div class="flex justify-between">
                      <div>
                        <button (click)="selectExpense(item)" data-modal-target="edit-expenses-modal"
                          data-modal-toggle="edit-expenses-modal" data-tooltip-target="tooltip-edit" type="button"
                          title="{{'resources.edit' | transloco}}">
                          <svg class="w-6 h-6 text-blue-500 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z" />
                          </svg>

                        </button>
                        <div id="tooltip-edit" role="tooltip"
                          class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip">
                          {{'resources.edit' | transloco}}
                          <div class="tooltip-arrow" data-popper-arrow></div>
                        </div>
                      </div>
                    </div>

                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <nav class="flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4"
            aria-label="Table navigation">
            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
              {{'table.showing' | transloco}}
              <span class="font-semibold text-gray-900 dark:text-white">{{(tableResponse.from != null?
                tableResponse.from
                :'0') + '-' + (tableResponse.to != null? tableResponse.to : '0')}}</span>
              {{'table.of' | transloco}}
              <span class="font-semibold text-gray-900 dark:text-white">{{tableResponse.total}}</span>
            </span>
            <!-- Pagination controls -->
            <ul dir="ltr" class="inline-flex items-stretch -space-x-px">
              <li>
                <button (click)="changePage((tableResponse.current_page - 1).toString())"
                  [disabled]="tableResponse.current_page === 1"
                  class="flex items-center justify-center h-full py-1.5 px-3 ms-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                  <span class="sr-only">Previous</span>
                  <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </li>
              <ng-container *ngFor="let page of getPagesToDisplay()">
                <li *ngIf="page === '...'">
                  <span
                    class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400">...</span>
                </li>
                <li *ngIf="page !== '...'">
                  <button (click)="changePage(page)" [class.bg-primary-50]="tableResponse.current_page === page"
                    [class.text-primary-600]="tableResponse.current_page === page"
                    [class.z-10]="tableResponse.current_page === page"
                    class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    {{ page }}
                  </button>
                </li>
              </ng-container>
              <li>
                <button (click)="changePage((tableResponse.current_page + 1).toString())"
                  [disabled]="tableResponse.current_page === tableResponse.last_page"
                  class="flex items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                  <span class="sr-only">Next</span>
                  <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 00-1.414 1.414L10.586 10l-3.293 3.293a1 1 0 000 1.414z"
                      clip-rule="evenodd" />
                  </svg>

                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </section>
  </div>

  <!-- Main modal -->
  <div *ngIf="!isLoading" id="create-expenses-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ 'resources.createNewExpenses' | transloco }}
          </h3>
          <button type="button"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
            data-modal-toggle="create-expenses-modal">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <div [formGroup]="createExpenseForm" class="p-4 md:p-5">
          <div class="grid gap-4 mb-4 grid-cols-2">
            <div class="col-span-2">
              <label for="description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                'resources.expensesDescription' | transloco }}</label>
              <textarea id="description" rows="4" formControlName="description"
                class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="{{ 'resources.descriptionPlaceholder' |transloco }}"></textarea>
            </div>
            <div class="col-span-2 sm:col-span-1">
              <label for="price" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                'resources.price' | transloco }}</label>
              <input type="number" name="price" id="price" formControlName="amount"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                placeholder="2999 {{ 'resources.iqd'| transloco }}" required="">
              <p class="error-message text-red-500 mt-2"
                *ngIf="createFormAmount && createFormAmount.invalid && createFormAmount.touched">
                <span *ngIf="createFormAmount.errors.required">{{ 'validationErrors.required' | transloco }}</span>
                <span *ngIf="createFormAmount.errors.min">{{ 'validationErrors.min' | transloco: { value: 0} }}</span>
              </p>
            </div>
            <div class="col-span-2 sm:col-span-1">
              <label for="category"
                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'resources.category' |
                transloco}}</label>
              <select id="category" formControlName="category"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option selected="" disabled>{{ 'resources.selectCategory' | transloco }}</option>
                <option value="expense">{{ 'resources.cardsPurchase' | transloco }}</option> <!-- card -->
                <option value="maintenance">{{ 'resources.maintenance' | transloco }}</option>
                <option value="general">{{ 'resources.generalExpenses' | transloco }}</option>
              </select>
              <p class="error-message text-red-500 mt-2"
                *ngIf="createFormCategory && createFormCategory.invalid && createFormCategory.touched">
                <span *ngIf="createFormCategory.errors.required">{{ 'validationErrors.required' | transloco }}</span>
              </p>
            </div>
            <!-- ['maintenance', 'expense', 'general'] -->
          </div>
          <button type="button" (click)="createNewExpense()"
            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
            <svg class="me-1 -ms-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"></path>
            </svg>
            {{ 'resources.addExpenses' | transloco }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="!isLoading" id="edit-expenses-modal" tabindex="-1" aria-hidden="true"
    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
      <!-- Modal content -->
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <!-- Modal header -->
        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ 'resources.editExpenses' | transloco }} ~~Not completed~~
          </h3>
          <button type="button"
            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
            data-modal-toggle="edit-expenses-modal">
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
        <!-- Modal body -->
        <div [formGroup]="editExpenseForm" class="p-4 md:p-5">
          <div class="grid gap-4 mb-4 grid-cols-2">
            <div class="col-span-2">
              <label for="description2" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                'resources.expensesDescription' | transloco }}</label>
              <textarea id="description2" rows="4" formControlName="description"
                class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="{{ 'resources.descriptionPlaceholder' |transloco }}"></textarea>
            </div>
            <div class="col-span-2 sm:col-span-1">
              <label for="price2" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                'resources.price' | transloco }}</label>
              <input type="number" name="price" id="price2" formControlName="amount"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                placeholder="2999 {{ 'resources.iqd'| transloco }}" required="">
              <p class="error-message text-red-500 mt-2"
                *ngIf="createFormAmount && createFormAmount.invalid && createFormAmount.touched">
                <span *ngIf="createFormAmount.errors.required">{{ 'validationErrors.required' | transloco }}</span>
                <span *ngIf="createFormAmount.errors.min">{{ 'validationErrors.min' | transloco: { value: 0} }}</span>
              </p>
            </div>
            <div class="col-span-2 sm:col-span-1">
              <label for="category2"
                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'resources.category' |
                transloco}}</label>
              <select id="2" formControlName="category"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option selected="" disabled>{{ 'resources.selectCategory' | transloco }}</option>
                <option value="expense">{{ 'resources.cardsPurchase' | transloco }}</option> <!-- card -->
                <option value="maintenance">{{ 'resources.maintenance' | transloco }}</option>
                <option value="general">{{ 'resources.generalExpenses' | transloco }}</option>
              </select>
              <p class="error-message text-red-500 mt-2"
                *ngIf="createFormCategory && createFormCategory.invalid && createFormCategory.touched">
                <span *ngIf="createFormCategory.errors.required">{{ 'validationErrors.required' | transloco }}</span>
              </p>
            </div>
            <!-- ['maintenance', 'expense', 'general'] -->
          </div>
          <div>
            <button type="button" (click)="createNewExpense()"
              class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
              <svg class="w-6 h-6 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z" />
              </svg>
              {{ 'common.edit' | transloco }}
            </button>
            <button data-modal-hide="edit-expenses-modal" type="button"
              class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
              {{'common.cancel' | transloco}} </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
