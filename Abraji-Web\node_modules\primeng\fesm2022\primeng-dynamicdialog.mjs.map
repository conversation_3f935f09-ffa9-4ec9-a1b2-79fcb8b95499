{"version": 3, "file": "primeng-dynamicdialog.mjs", "sources": ["../../src/app/components/dynamicdialog/dynamicdialogcontent.ts", "../../src/app/components/dynamicdialog/dynamicdialog-config.ts", "../../src/app/components/dynamicdialog/dynamicdialog-ref.ts", "../../src/app/components/dynamicdialog/dynamicdialog.ts", "../../src/app/components/dynamicdialog/dynamicdialog-injector.ts", "../../src/app/components/dynamicdialog/dialogservice.ts", "../../src/app/components/dynamicdialog/primeng-dynamicdialog.ts"], "sourcesContent": ["import { Directive, ViewContainerRef } from '@angular/core';\n\n@Directive({\n    selector: '[pDynamicDialogContent]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class DynamicDialogContent {\n    constructor(public viewContainerRef: ViewContainerRef) {}\n}\n", "import { Type } from '@angular/core';\n\n/**\n * Dialogs can be created dynamically with any component as the content using a DialogService.\n * @group Components\n */\nexport class DynamicDialogConfig<T = any> {\n    /**\n     * An object to pass to the component loaded inside the Dialog.\n     * @group Props\n     */\n    data?: T;\n    /**\n     * Header text of the dialog.\n     * @group Props\n     */\n    header?: string;\n    /**\n     * Identifies the element (or elements) that labels the element it is applied to.\n     * @group Props\n     */\n    ariaLabelledBy?: string;\n    /**\n     * Footer text of the dialog.\n     * @group Props\n     */\n    footer?: string;\n    /**\n     * Width of the dialog.\n     * @group Props\n     */\n    width?: string;\n    /**\n     * Height of the dialog.\n     * @group Props\n     */\n    height?: string;\n    /**\n     * Specifies if pressing escape key should hide the dialog.\n     * @group Props\n     */\n    closeOnEscape?: boolean;\n    /**\n     * Specifies if autofocus should happen on show.\n     * @group Props\n     */\n    focusOnShow?: boolean = true;\n    /**\n     * When enabled, can only focus on elements inside the dialog.\n     * @group Props\n     */\n    focusTrap?: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex?: number;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex?: boolean;\n    /**\n     * Specifies if clicking the modal background should hide the dialog.\n     * @group Props\n     */\n    dismissableMask?: boolean;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    rtl?: boolean;\n    /**\n     * Inline style of the comopnent.\n     * @group Props\n     */\n    style?: { [klass: string]: any } | null | undefined;\n    /**\n     * Inline style of the content.\n     * @group Props\n     */\n    contentStyle?: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass?: string;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions?: string;\n    /**\n     * Adds a close icon to the header to hide the dialog.\n     * @group Props\n     */\n    closable?: boolean;\n    /**\n     * Whether to show the header or not.\n     * @group Props\n     */\n    showHeader?: boolean;\n    /**\n     * Defines if background should be blocked when dialog is displayed.\n     * @group Props\n     */\n    modal?: boolean;\n    /**\n     * Style class of the mask.\n     * @group Props\n     */\n    maskStyleClass?: string;\n    /**\n     * Enables resizing of the content.\n     * @group Props\n     */\n    resizable?: boolean;\n    /**\n     * Enables dragging to change the position using header.\n     * @group Props\n     */\n    draggable?: boolean;\n    /**\n     * Keeps dialog in the viewport.\n     * @group Props\n     */\n    keepInViewport?: boolean;\n    /**\n     * Minimum value for the left coordinate of dialog in dragging.\n     * @group Props\n     */\n    minX?: number;\n    /**\n     * Minimum value for the top coordinate of dialog in dragging.\n     * @group Props\n     */\n    minY?: number;\n    /**\n     * Whether the dialog can be displayed full screen.\n     * @group Props\n     */\n    maximizable?: boolean;\n    /**\n     * Name of the maximize icon.\n     * @group Props\n     */\n    maximizeIcon?: string;\n    /**\n     * Name of the minimize icon.\n     * @group Props\n     */\n    minimizeIcon?: string;\n    /**\n     * Position of the dialog, options are \"center\", \"top\", \"bottom\", \"left\", \"right\", \"top-left\", \"top-right\", \"bottom-left\" or \"bottom-right\".\n     * @group Props\n     */\n    position?: string;\n    /**\n     * Defines a string that labels the close button for accessibility.\n     * @group Props\n     */\n    closeAriaLabel?: string;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo?: any;\n    /**\n     * A boolean to determine if it can be duplicate.\n     * @group Props\n     */\n    duplicate?: boolean;\n    /**\n     * Object literal to define widths per screen size.\n     * @group Props\n     */\n    breakpoints?: any;\n    /**\n     * Dialog templates.\n     * @group Props\n     */\n    templates?: DynamicDialogTemplates;\n}\n\n/**\n * Defines valid templates in Dynamic Dialog.\n * @group Interface\n */\nexport interface DynamicDialogTemplates {\n    /**\n     * Template of the header.\n     */\n    header?: Type<any>;\n    /**\n     * Template of the content.\n     */\n    content?: Type<any>;\n    /**\n     * Template of the footer.\n     */\n    footer?: Type<any>;\n    /**\n     * Template of the minimize icon.\n     */\n    minimizeicon?: Type<any>;\n    /**\n     * Template of the maximize icon.\n     */\n    maximizeicon?: Type<any>;\n    /**\n     * Template of the close icon.\n     */\n    closeicon?: Type<any>;\n}\n", "import { Observable, Subject } from 'rxjs';\nimport { Output, EventEmitter, Type } from '@angular/core';\n/**\n * Dynamic Dialog instance.\n * @group Components\n */\nexport class DynamicDialogRef<ComponentType = any> {\n    constructor() {}\n    /**\n     * Closes dialog.\n     * @group Method\n     */\n    close(result?: any) {\n        this._onClose.next(result);\n\n        setTimeout(() => {\n            this._onClose.complete();\n        }, 1000);\n    }\n    /**\n     * Destroys the dialog instance.\n     * @group Method\n     */\n    destroy() {\n        this._onDestroy.next(null);\n    }\n    /**\n     * Callback to invoke on drag start.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Method\n     */\n    dragStart(event: MouseEvent) {\n        this._onDragStart.next(event);\n    }\n    /**\n     * Callback to invoke on drag end.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Method\n     */\n    dragEnd(event: MouseEvent) {\n        this._onDragEnd.next(event);\n    }\n    /**\n     * Callback to invoke on resize start.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Method\n     */\n    resizeInit(event: MouseEvent) {\n        this._onResizeInit.next(event);\n    }\n    /**\n     * Callback to invoke on resize start.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Method\n     */\n    resizeEnd(event: MouseEvent) {\n        this._onResizeEnd.next(event);\n    }\n    /**\n     * Callback to invoke on dialog is maximized.\n     * @param {*} value - Size value.\n     * @group Method\n     */\n    maximize(value: any) {\n        this._onMaximize.next(value);\n    }\n\n    private readonly _onClose = new Subject<any>();\n    /**\n     * Event triggered on dialog is closed.\n     * @group Events\n     */\n    onClose: Observable<any> = this._onClose.asObservable();\n\n    private readonly _onDestroy = new Subject<any>();\n    /**\n     * Event triggered on dialog instance is destroyed.\n     * @group Events\n     */\n    onDestroy: Observable<any> = this._onDestroy.asObservable();\n\n    private readonly _onDragStart = new Subject<any>();\n    /**\n     * Event triggered on drag start.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Events\n     */\n    onDragStart: Observable<any> = this._onDragStart.asObservable();\n\n    private readonly _onDragEnd = new Subject<any>();\n    /**\n     * Event triggered on drag end.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Events\n     */\n    onDragEnd: Observable<any> = this._onDragEnd.asObservable();\n\n    private readonly _onResizeInit = new Subject<any>();\n    /**\n     * Event triggered on resize start.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Events\n     */\n    onResizeInit: Observable<any> = this._onResizeInit.asObservable();\n\n    private readonly _onResizeEnd = new Subject<any>();\n    /**\n     * Event triggered on resize end.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Events\n     */\n    onResizeEnd: Observable<any> = this._onResizeEnd.asObservable();\n\n    private readonly _onMaximize = new Subject<any>();\n    /**\n     * Event triggered on dialog is maximized.\n     * @param {*} value - Size value.\n     * @group Events\n     */\n    onMaximize: Observable<any> = this._onMaximize.asObservable();\n\n    /**\n     * Event triggered on child component load.\n     * @param {*} value - Chi.\n     * @group Events\n     */\n    readonly onChildComponentLoaded = new Subject<ComponentType>();\n}\n", "import { animate, animation, AnimationEvent, style, transition, trigger, useAnimation } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ComponentRef,\n    ElementRef,\n    Inject,\n    NgModule,\n    NgZone,\n    OnDestroy,\n    Optional,\n    PLATFORM_ID,\n    Renderer2,\n    SkipSelf,\n    Type,\n    ViewChild,\n    ViewEncapsulation,\n    ViewRef\n} from '@angular/core';\nimport { PrimeNGConfig, SharedModule, TranslationKeys } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { FocusTrapModule } from 'primeng/focustrap';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { WindowMaximizeIcon } from 'primeng/icons/windowmaximize';\nimport { WindowMinimizeIcon } from 'primeng/icons/windowminimize';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { DynamicDialogConfig } from './dynamicdialog-config';\nimport { DynamicDialogRef } from './dynamicdialog-ref';\nimport { DynamicDialogContent } from './dynamicdialogcontent';\n\nconst showAnimation = animation([style({ transform: '{{transform}}', opacity: 0 }), animate('{{transition}}', style({ transform: 'none', opacity: 1 }))]);\n\nconst hideAnimation = animation([animate('{{transition}}', style({ transform: '{{transform}}', opacity: 0 }))]);\n\n@Component({\n    selector: 'p-dynamicDialog',\n    template: `\n        <div\n            #mask\n            [ngClass]=\"{\n                'p-dialog-mask': true,\n                'p-component-overlay p-component-overlay-enter p-dialog-mask-scrollblocker': config.modal !== false,\n                'p-dialog-left': position === 'left',\n                'p-dialog-right': position === 'right',\n                'p-dialog-top': position === 'top',\n                'p-dialog-bottom': position === 'bottom',\n                'p-dialog-top-left': position === 'topleft' || position === 'top-left',\n                'p-dialog-top-right': position === 'topright' || position === 'top-right',\n                'p-dialog-bottom-left': position === 'bottomleft' || position === 'bottom-left',\n                'p-dialog-bottom-right': position === 'bottomright' || position === 'bottom-right'\n            }\"\n            [class]=\"config.maskStyleClass\"\n        >\n            <div\n                #container\n                [ngClass]=\"{ 'p-dialog p-dynamic-dialog p-component': true, 'p-dialog-rtl': config.rtl, 'p-dialog-resizable': config.resizable, 'p-dialog-draggable': config.draggable, 'p-dialog-maximized': maximized }\"\n                [ngStyle]=\"containerStyle\"\n                [class]=\"config.styleClass\"\n                [@animation]=\"{ value: 'visible', params: { transform: transformOptions, transition: config.transitionOptions || '150ms cubic-bezier(0, 0, 0.2, 1)' } }\"\n                (@animation.start)=\"onAnimationStart($event)\"\n                (@animation.done)=\"onAnimationEnd($event)\"\n                role=\"dialog\"\n                *ngIf=\"visible\"\n                pFocusTrap\n                [pFocusTrapDisabled]=\"config.focusTrap === false\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-modal]=\"true\"\n            >\n                <div *ngIf=\"config.resizable\" class=\"p-resizable-handle\" (mousedown)=\"initResize($event)\"></div>\n                <div #titlebar class=\"p-dialog-header\" (mousedown)=\"initDrag($event)\" *ngIf=\"config.showHeader === false ? false : true\">\n                    <ng-container *ngComponentOutlet=\"headerTemplate\"></ng-container>\n                    <ng-container *ngIf=\"!headerTemplate\">\n                        <span class=\"p-dialog-title\" [id]=\"ariaLabelledBy\">{{ config.header }}</span>\n                        <div class=\"p-dialog-header-icons\">\n                            <button *ngIf=\"config.maximizable\" type=\"button\" [ngClass]=\"{ 'p-dialog-header-icon p-dialog-header-maximize p-link': true }\" (click)=\"maximize()\" (keydown.enter)=\"maximize()\" tabindex=\"-1\" pRipple>\n                                <span class=\"p-dialog-header-maximize-icon\" *ngIf=\"!maximizeIconTemplate || !minimizeIconTemplate\" [ngClass]=\"maximized ? minimizeIcon : maximizeIcon\"></span>\n                                <WindowMaximizeIcon *ngIf=\"!maximized && !maximizeIcon && !maximizeIconTemplate\" [styleClass]=\"'p-dialog-header-maximize-icon'\" />\n                                <WindowMinimizeIcon *ngIf=\"maximized && !minimizeIcon && !minimizeIconTemplate\" [styleClass]=\"'p-dialog-header-maximize-icon'\" />\n                                <ng-container *ngComponentOutlet=\"maximizeIconTemplate\"></ng-container>\n                                <ng-container *ngComponentOutlet=\"minimizeIconTemplate\"></ng-container>\n                            </button>\n                            <button [ngClass]=\"'p-dialog-header-icon p-dialog-header-maximize p-link'\" type=\"button\" role=\"button\" (click)=\"hide()\" (keydown.enter)=\"hide()\" *ngIf=\"config.closable !== false\" [attr.aria-label]=\"closeAriaLabel\">\n                                <TimesIcon [styleClass]=\"'p-dialog-header-close-icon'\" *ngIf=\"!closeIconTemplate\" />\n                                <ng-container *ngComponentOutlet=\"closeIconTemplate\"></ng-container>\n                            </button>\n                        </div>\n                    </ng-container>\n                </div>\n                <div #content class=\"p-dialog-content\" [ngStyle]=\"config.contentStyle\">\n                    <ng-template pDynamicDialogContent *ngIf=\"!contentTemplate\"></ng-template>\n                    <ng-container *ngComponentOutlet=\"contentTemplate\"></ng-container>\n                </div>\n                <div #footer class=\"p-dialog-footer\" *ngIf=\"config.footer || footerTemplate\">\n                    <ng-container *ngIf=\"!footerTemplate\">\n                        {{ config.footer }}\n                    </ng-container>\n                    <ng-container *ngComponentOutlet=\"footerTemplate\"></ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n    animations: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])],\n    changeDetection: ChangeDetectionStrategy.Default,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['../dialog/dialog.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class DynamicDialogComponent implements AfterViewInit, OnDestroy {\n    visible: boolean = true;\n\n    componentRef: Nullable<ComponentRef<any>>;\n\n    mask: Nullable<HTMLDivElement>;\n\n    resizing: boolean | undefined;\n\n    dragging: boolean | undefined;\n\n    maximized: boolean | undefined;\n\n    _style: any = {};\n\n    originalStyle: any;\n\n    lastPageX: number | undefined;\n\n    lastPageY: number | undefined;\n\n    ariaLabelledBy: string | undefined;\n\n    id: string = UniqueComponentId();\n\n    styleElement: any;\n\n    @ViewChild(DynamicDialogContent) insertionPoint: Nullable<DynamicDialogContent>;\n\n    @ViewChild('mask') maskViewChild: Nullable<ElementRef>;\n\n    @ViewChild('content') contentViewChild: Nullable<ElementRef>;\n\n    @ViewChild('footer') footerViewChild: Nullable<ElementRef>;\n\n    @ViewChild('titlebar') headerViewChild: Nullable<ElementRef>;\n\n    childComponentType: Nullable<Type<any>>;\n\n    container: Nullable<HTMLDivElement>;\n\n    wrapper: Nullable<HTMLElement>;\n\n    documentKeydownListener: VoidListener;\n\n    documentEscapeListener: VoidListener;\n\n    maskClickListener: VoidListener;\n\n    transformOptions: string = 'scale(0.7)';\n\n    documentResizeListener: VoidListener;\n\n    documentResizeEndListener: VoidListener;\n\n    documentDragListener: VoidListener;\n\n    documentDragEndListener: VoidListener;\n\n    get minX(): number {\n        return this.config.minX ? this.config.minX : 0;\n    }\n\n    get minY(): number {\n        return this.config.minY ? this.config.minY : 0;\n    }\n\n    get keepInViewport(): boolean {\n        return this.config.keepInViewport!;\n    }\n\n    get maximizable(): boolean {\n        return this.config.maximizable!;\n    }\n\n    get maximizeIcon(): string {\n        return this.config.maximizeIcon!;\n    }\n\n    get minimizeIcon(): string {\n        return this.config.minimizeIcon!;\n    }\n\n    get style(): any {\n        return this._style;\n    }\n\n    get position(): string {\n        return this.config.position!;\n    }\n\n    get closeAriaLabel(): string {\n        return this.primeNGConfig.getTranslation(TranslationKeys.ARIA)['close'];\n    }\n\n    set style(value: any) {\n        if (value) {\n            this._style = { ...value };\n            this.originalStyle = value;\n        }\n    }\n\n    get parent() {\n        const domElements = Array.from(this.document.getElementsByClassName('p-dialog'));\n\n        if (domElements.length > 1) {\n            return domElements.pop();\n        }\n    }\n\n    get parentContent() {\n        const domElements = Array.from(this.document.getElementsByClassName('p-dialog'));\n        if (domElements.length > 0) {\n            const contentElements = domElements[domElements.length - 1].querySelector('.p-dialog-content');\n            if (contentElements) return Array.isArray(contentElements) ? contentElements[0] : contentElements;\n        }\n    }\n\n    get header() {\n        return this.config.header;\n    }\n\n    get data() {\n        return this.config.data;\n    }\n\n    get breakpoints() {\n        return this.config.breakpoints;\n    }\n\n    get footerTemplate() {\n        return this.config?.templates?.footer;\n    }\n\n    get headerTemplate() {\n        return this.config?.templates?.header;\n    }\n\n    get contentTemplate() {\n        return this.config?.templates?.content;\n    }\n\n    get minimizeIconTemplate() {\n        return this.config?.templates?.minimizeicon;\n    }\n\n    get maximizeIconTemplate() {\n        return this.config?.templates?.maximizeicon;\n    }\n\n    get closeIconTemplate() {\n        return this.config?.templates?.closeicon;\n    }\n\n    get dynamicDialogCount() {\n        const dynamicDialogs = this.document.querySelectorAll('p-dynamicdialog');\n        const dynamicDialogCount = dynamicDialogs?.length;\n\n        return dynamicDialogCount;\n    }\n\n    get containerStyle() {\n        return {\n            ...this.config.style,\n            width: this.config.width,\n            height: this.config.height\n        };\n    }\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        private cd: ChangeDetectorRef,\n        public renderer: Renderer2,\n        public config: DynamicDialogConfig,\n        private dialogRef: DynamicDialogRef,\n        public zone: NgZone,\n        public primeNGConfig: PrimeNGConfig,\n        @SkipSelf() @Optional() private parentDialog: DynamicDialogComponent\n    ) {}\n\n    ngOnInit() {\n        if (this.breakpoints) {\n            this.createStyle();\n        }\n    }\n    createStyle() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.styleElement) {\n                this.styleElement = this.renderer.createElement('style');\n                this.styleElement.type = 'text/css';\n                DomHandler.setAttribute(this.styleElement, 'nonce', this.primeNGConfig?.csp()?.nonce);\n                this.renderer.appendChild(this.document.head, this.styleElement);\n                let innerHTML = '';\n                for (let breakpoint in this.breakpoints) {\n                    innerHTML += `\n                        @media screen and (max-width: ${breakpoint}) {\n                            .p-dialog[${this.id}]:not(.p-dialog-maximized) {\n                                width: ${this.breakpoints[breakpoint]} !important;\n                            }\n                        }\n                    `;\n                }\n\n                this.renderer.setProperty(this.styleElement, 'innerHTML', innerHTML);\n            }\n        }\n    }\n\n    destroyStyle() {\n        if (this.styleElement) {\n            this.renderer.removeChild(this.document.head, this.styleElement);\n            this.styleElement = null;\n        }\n    }\n\n    ngAfterViewInit() {\n        this.loadChildComponent(this.childComponentType!);\n        this.ariaLabelledBy = this.getAriaLabelledBy();\n        this.cd.detectChanges();\n    }\n\n    getAriaLabelledBy() {\n        return this.header !== null ? UniqueComponentId() + '_header' : null;\n    }\n\n    loadChildComponent(componentType: Type<any>) {\n        let viewContainerRef = this.insertionPoint?.viewContainerRef;\n        viewContainerRef?.clear();\n\n        this.componentRef = viewContainerRef?.createComponent(componentType);\n        this.dialogRef.onChildComponentLoaded.next(this.componentRef!.instance);\n    }\n\n    moveOnTop() {\n        if (this.config.autoZIndex !== false) {\n            ZIndexUtils.set('modal', this.container, (this.config.baseZIndex || 0) + this.primeNGConfig.zIndex.modal);\n            (this.wrapper as HTMLElement).style.zIndex = String(parseInt((this.container as HTMLDivElement).style.zIndex, 10) - 1);\n        }\n    }\n\n    onAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                this.container = event.element;\n                this.wrapper = (this.container as HTMLDivElement).parentElement;\n                this.moveOnTop();\n                if (this.parent) {\n                    this.unbindGlobalListeners();\n                }\n                this.bindGlobalListeners();\n                this.container?.setAttribute(this.id, '');\n\n                if (this.config.modal !== false) {\n                    this.enableModality();\n                }\n\n                if (this.config.focusOnShow !== false) {\n                    this.focus();\n                }\n                break;\n\n            case 'void':\n                if (this.wrapper && this.config.modal !== false) {\n                    DomHandler.addClass(this.wrapper, 'p-component-overlay-leave');\n                }\n                break;\n        }\n    }\n\n    onAnimationEnd(event: AnimationEvent) {\n        if (event.toState === 'void') {\n            if (this.parentContent) {\n                this.focus(this.parentContent);\n            }\n            this.onContainerDestroy();\n            this.dialogRef.destroy();\n        }\n    }\n\n    onContainerDestroy() {\n        this.unbindGlobalListeners();\n\n        if (this.container && this.config.autoZIndex !== false) {\n            ZIndexUtils.clear(this.container);\n        }\n\n        if (this.config.modal !== false) {\n            this.disableModality();\n        }\n        this.container = null;\n    }\n\n    close() {\n        this.visible = false;\n        this.cd.markForCheck();\n    }\n\n    hide() {\n        if (this.dialogRef) {\n            this.dialogRef.close();\n        }\n    }\n\n    enableModality() {\n        if (this.config.dismissableMask) {\n            this.maskClickListener = this.renderer.listen(this.wrapper, 'mousedown', (event: any) => {\n                if (this.wrapper && this.wrapper.isSameNode(event.target)) {\n                    this.hide();\n                }\n            });\n        }\n\n        if (this.dynamicDialogCount === 1) {\n            DomHandler.addClass(this.document.body, 'p-overflow-hidden');\n        }\n    }\n\n    disableModality() {\n        if (this.wrapper) {\n            if (this.config.dismissableMask) {\n                this.unbindMaskClickListener();\n            }\n            if (this.dynamicDialogCount === 1) {\n                DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n            }\n\n            if (!(this.cd as ViewRef).destroyed) {\n                this.cd.detectChanges();\n            }\n        }\n    }\n\n    focus(focusParentElement = this.contentViewChild.nativeElement) {\n        const focusableElements = DomHandler.getFocusableElements(focusParentElement);\n\n        if (!focusableElements.length) {\n            return;\n        }\n\n        let focusable = DomHandler.getFocusableElement(focusParentElement, '[autofocus]');\n        if (focusable) {\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => focusable.focus(), 5);\n            });\n            return;\n        }\n        const focusableElement = DomHandler.getFocusableElement(focusParentElement);\n        if (focusableElement) {\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => focusableElement.focus(), 5);\n            });\n        } else if (this.footerViewChild) {\n            // If the content section is empty try to focus on footer\n            this.focus(this.footerViewChild.nativeElement);\n        } else if (!focusableElement && this.headerViewChild) {\n            this.focus(this.headerViewChild.nativeElement);\n        }\n    }\n\n    maximize() {\n        this.maximized = !this.maximized;\n\n        if (this.maximized) {\n            DomHandler.addClass(this.document.body, 'p-overflow-hidden');\n        } else {\n            DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n        }\n\n        this.dialogRef.maximize({ maximized: this.maximized });\n    }\n\n    initResize(event: MouseEvent) {\n        if (this.config.resizable) {\n            if (!this.documentResizeListener) {\n                this.bindDocumentResizeListeners();\n            }\n\n            this.resizing = true;\n            this.lastPageX = event.pageX;\n            this.lastPageY = event.pageY;\n            DomHandler.addClass(this.document.body, 'p-unselectable-text');\n            this.dialogRef.resizeInit(event);\n        }\n    }\n\n    onResize(event: MouseEvent) {\n        if (this.resizing) {\n            let deltaX = event.pageX - (this.lastPageX as number);\n            let deltaY = event.pageY - (this.lastPageY as number);\n            let containerWidth = DomHandler.getOuterWidth(this.container);\n            let containerHeight = DomHandler.getOuterHeight(this.container);\n            let contentHeight = DomHandler.getOuterHeight((<ElementRef>this.contentViewChild).nativeElement);\n            let newWidth = containerWidth + deltaX;\n            let newHeight = containerHeight + deltaY;\n            let minWidth = (this.container as HTMLDivElement).style.minWidth;\n            let minHeight = (this.container as HTMLDivElement).style.minHeight;\n            let offset = (this.container as HTMLDivElement).getBoundingClientRect();\n            let viewport = DomHandler.getViewport();\n            let hasBeenDragged = !parseInt((this.container as HTMLDivElement).style.top) || !parseInt((this.container as HTMLDivElement).style.left);\n\n            if (hasBeenDragged) {\n                newWidth += deltaX;\n                newHeight += deltaY;\n            }\n\n            if ((!minWidth || newWidth > parseInt(minWidth)) && offset.left + newWidth < viewport.width) {\n                this._style.width = newWidth + 'px';\n                (this.container as HTMLDivElement).style.width = this._style.width;\n            }\n\n            if ((!minHeight || newHeight > parseInt(minHeight)) && offset.top + newHeight < viewport.height) {\n                (<ElementRef>this.contentViewChild).nativeElement.style.height = contentHeight + newHeight - containerHeight + 'px';\n\n                if (this._style.height) {\n                    this._style.height = newHeight + 'px';\n                    (this.container as HTMLDivElement).style.height = this._style.height;\n                }\n            }\n\n            this.lastPageX = event.pageX;\n            this.lastPageY = event.pageY;\n        }\n    }\n\n    resizeEnd(event: MouseEvent) {\n        if (this.resizing) {\n            this.resizing = false;\n            DomHandler.removeClass(this.document.body, 'p-unselectable-text');\n            this.dialogRef.resizeEnd(event);\n        }\n    }\n\n    initDrag(event: MouseEvent) {\n        if (DomHandler.hasClass(event.target, 'p-dialog-header-icon') || DomHandler.hasClass((<HTMLElement>event.target).parentElement, 'p-dialog-header-icon')) {\n            return;\n        }\n\n        if (this.config.draggable) {\n            this.dragging = true;\n            this.lastPageX = event.pageX;\n            this.lastPageY = event.pageY;\n\n            (this.container as HTMLDivElement).style.margin = '0';\n            DomHandler.addClass(this.document.body, 'p-unselectable-text');\n            this.dialogRef.dragStart(event);\n        }\n    }\n\n    onDrag(event: MouseEvent) {\n        if (this.dragging) {\n            let containerWidth = DomHandler.getOuterWidth(this.container);\n            let containerHeight = DomHandler.getOuterHeight(this.container);\n            let deltaX = event.pageX - (this.lastPageX as number);\n            let deltaY = event.pageY - (this.lastPageY as number);\n            let offset = (this.container as HTMLDivElement).getBoundingClientRect();\n            let leftPos = offset.left + deltaX;\n            let topPos = offset.top + deltaY;\n            let viewport = DomHandler.getViewport();\n\n            (this.container as HTMLDivElement).style.position = 'fixed';\n\n            if (this.keepInViewport) {\n                if (leftPos >= this.minX && leftPos + containerWidth < viewport.width) {\n                    this._style.left = leftPos + 'px';\n                    this.lastPageX = event.pageX;\n                    (this.container as HTMLDivElement).style.left = leftPos + 'px';\n                }\n\n                if (topPos >= this.minY && topPos + containerHeight < viewport.height) {\n                    this._style.top = topPos + 'px';\n                    this.lastPageY = event.pageY;\n                    (this.container as HTMLDivElement).style.top = topPos + 'px';\n                }\n            } else {\n                this.lastPageX = event.pageX;\n                (this.container as HTMLDivElement).style.left = leftPos + 'px';\n                this.lastPageY = event.pageY;\n                (this.container as HTMLDivElement).style.top = topPos + 'px';\n            }\n        }\n    }\n\n    endDrag(event: MouseEvent) {\n        if (this.dragging) {\n            this.dragging = false;\n            DomHandler.removeClass(this.document.body, 'p-unselectable-text');\n            this.dialogRef.dragEnd(event);\n            this.cd.detectChanges();\n        }\n    }\n\n    resetPosition() {\n        (this.container as HTMLDivElement).style.position = '';\n        (this.container as HTMLDivElement).style.left = '';\n        (this.container as HTMLDivElement).style.top = '';\n        (this.container as HTMLDivElement).style.margin = '';\n    }\n\n    bindDocumentDragListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                this.documentDragListener = this.renderer.listen(this.document, 'mousemove', this.onDrag.bind(this));\n            });\n        }\n    }\n\n    bindDocumentDragEndListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                this.documentDragEndListener = this.renderer.listen(this.document, 'mouseup', this.endDrag.bind(this));\n            });\n        }\n    }\n\n    unbindDocumentDragEndListener() {\n        if (this.documentDragEndListener) {\n            this.documentDragEndListener();\n            this.documentDragListener = null;\n        }\n    }\n\n    unbindDocumentDragListener() {\n        if (this.documentDragListener) {\n            this.documentDragListener();\n            this.documentDragListener = null;\n        }\n    }\n\n    bindDocumentResizeListeners() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                this.documentResizeListener = this.renderer.listen(this.document, 'mousemove', this.onResize.bind(this));\n                this.documentResizeEndListener = this.renderer.listen(this.document, 'mouseup', this.resizeEnd.bind(this));\n            });\n        }\n    }\n\n    unbindDocumentResizeListeners() {\n        if (this.documentResizeListener && this.documentResizeEndListener) {\n            this.documentResizeListener();\n            this.documentResizeEndListener();\n            this.documentResizeListener = null;\n            this.documentResizeEndListener = null;\n        }\n    }\n\n    bindGlobalListeners() {\n        if (this.config.closeOnEscape !== false) {\n            this.bindDocumentEscapeListener();\n        }\n\n        if (this.config.resizable) {\n            this.bindDocumentResizeListeners();\n        }\n\n        if (this.config.draggable) {\n            this.bindDocumentDragListener();\n            this.bindDocumentDragEndListener();\n        }\n    }\n\n    unbindGlobalListeners() {\n        this.unbindDocumentEscapeListener();\n        this.unbindDocumentResizeListeners();\n        this.unbindDocumentDragListener();\n        this.unbindDocumentDragEndListener();\n    }\n\n    bindDocumentEscapeListener() {\n        const documentTarget: any = this.maskViewChild ? this.maskViewChild.nativeElement.ownerDocument : 'document';\n\n        this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', (event) => {\n            if (event.which == 27) {\n                if (parseInt((this.container as HTMLDivElement).style.zIndex) == ZIndexUtils.getCurrent()) {\n                    this.hide();\n                }\n            }\n        });\n    }\n\n    unbindDocumentEscapeListener() {\n        if (this.documentEscapeListener) {\n            this.documentEscapeListener();\n            this.documentEscapeListener = null;\n        }\n    }\n\n    unbindMaskClickListener() {\n        if (this.maskClickListener) {\n            this.maskClickListener();\n            this.maskClickListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.onContainerDestroy();\n\n        if (this.componentRef) {\n            this.componentRef.destroy();\n        }\n        this.destroyStyle();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, WindowMaximizeIcon, WindowMinimizeIcon, TimesIcon, SharedModule, FocusTrapModule],\n    declarations: [DynamicDialogComponent, DynamicDialogContent],\n    exports: [SharedModule]\n})\nexport class DynamicDialogModule {}\n", "import { InjectOptions, Injector, ProviderToken, InjectFlags } from '@angular/core';\n\nexport class DynamicDialogInjector implements Injector {\n    constructor(private _parentInjector: Injector, private _additionalTokens: WeakMap<any, any>) {}\n\n    get<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions | InjectFlags): T {\n        const value = this._additionalTokens.get(token);\n\n        if (value) return value;\n\n        return this._parentInjector.get<any>(token, notFoundValue);\n    }\n}\n", "import { Injectable, ApplicationRef, Injector, Type, EmbeddedViewRef, ComponentRef, Inject, createComponent } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { DynamicDialogComponent } from './dynamicdialog';\nimport { DynamicDialogInjector } from './dynamicdialog-injector';\nimport { DynamicDialogConfig } from './dynamicdialog-config';\nimport { DynamicDialogRef } from './dynamicdialog-ref';\nimport { DOCUMENT } from '@angular/common';\nimport { ObjectUtils } from 'primeng/utils';\n/**\n * Dynamic Dialog component methods.\n * @group Service\n */\n@Injectable()\nexport class DialogService {\n    dialogComponentRefMap: Map<DynamicDialogRef<any>, ComponentRef<DynamicDialogComponent>> = new Map();\n\n    constructor(private appRef: ApplicationRef, private injector: Injector, @Inject(DOCUMENT) private document: Document) {}\n    /**\n     * Displays the dialog using the dynamic dialog object options.\n     * @param {*} componentType - Dynamic component for content template.\n     * @param {DynamicDialogConfig} config - DynamicDialog object.\n     * @returns {DynamicDialogRef} DynamicDialog instance.\n     * @group Method\n     */\n    public open<T>(componentType: Type<T>, config: DynamicDialogConfig): DynamicDialogRef<T> {\n        if (!this.duplicationPermission(componentType, config)) {\n            return null;\n        }\n\n        const dialogRef = this.appendDialogComponentToBody<T>(config, componentType);\n\n        this.dialogComponentRefMap.get(dialogRef).instance.childComponentType = componentType;\n\n        return dialogRef;\n    }\n    /**\n     * Returns the dynamic dialog component instance.\n     * @param {ref} DynamicDialogRef - DynamicDialog instance.\n     * @group Method\n     */\n    public getInstance(ref: DynamicDialogRef<any>) {\n        return this.dialogComponentRefMap.get(ref).instance;\n    }\n\n    private appendDialogComponentToBody<T>(config: DynamicDialogConfig, componentType: Type<T>): DynamicDialogRef<T> {\n        const map = new WeakMap();\n        map.set(DynamicDialogConfig, config);\n\n        const dialogRef = new DynamicDialogRef<T>();\n        map.set(DynamicDialogRef, dialogRef);\n\n        const sub = dialogRef.onClose.subscribe(() => {\n            this.dialogComponentRefMap.get(dialogRef).instance.close();\n        });\n\n        const destroySub = dialogRef.onDestroy.subscribe(() => {\n            this.removeDialogComponentFromBody(dialogRef);\n            destroySub.unsubscribe();\n            sub.unsubscribe();\n        });\n\n        const componentRef = createComponent(DynamicDialogComponent, { environmentInjector: this.appRef.injector, elementInjector: new DynamicDialogInjector(this.injector, map) });\n\n        this.appRef.attachView(componentRef.hostView);\n\n        const domElem = (componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;\n        if (!config.appendTo || config.appendTo === 'body') {\n            this.document.body.appendChild(domElem);\n        } else {\n            DomHandler.appendChild(domElem, config.appendTo);\n        }\n\n        this.dialogComponentRefMap.set(dialogRef, componentRef);\n\n        return dialogRef;\n    }\n\n    private removeDialogComponentFromBody(dialogRef: DynamicDialogRef<any>) {\n        if (!dialogRef || !this.dialogComponentRefMap.has(dialogRef)) {\n            return;\n        }\n\n        const dialogComponentRef = this.dialogComponentRefMap.get(dialogRef);\n        this.appRef.detachView(dialogComponentRef.hostView);\n        dialogComponentRef.destroy();\n        this.dialogComponentRefMap.delete(dialogRef);\n    }\n\n    private duplicationPermission(componentType: Type<any>, config: DynamicDialogConfig): boolean {\n        if (config.duplicate) {\n            return true;\n        }\n        let permission = true;\n        for (const [key, value] of this.dialogComponentRefMap) {\n            if (value.instance.childComponentType === componentType) {\n                permission = false;\n                break;\n            }\n        }\n        return permission;\n    }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": ["i1.DynamicDialogConfig", "i2.DynamicDialogRef"], "mappings": ";;;;;;;;;;;;;;;;MAQa,oBAAoB,CAAA;AACV,IAAA,gBAAA,CAAA;AAAnB,IAAA,WAAA,CAAmB,gBAAkC,EAAA;QAAlC,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;KAAI;uGADhD,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAApB,oBAAoB,EAAA,QAAA,EAAA,yBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBANhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,yBAAyB;AACnC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;;ACLD;;;AAGG;MACU,mBAAmB,CAAA;AAC5B;;;AAGG;AACH,IAAA,IAAI,CAAK;AACT;;;AAGG;AACH,IAAA,MAAM,CAAU;AAChB;;;AAGG;AACH,IAAA,cAAc,CAAU;AACxB;;;AAGG;AACH,IAAA,MAAM,CAAU;AAChB;;;AAGG;AACH,IAAA,KAAK,CAAU;AACf;;;AAGG;AACH,IAAA,MAAM,CAAU;AAChB;;;AAGG;AACH,IAAA,aAAa,CAAW;AACxB;;;AAGG;IACH,WAAW,GAAa,IAAI,CAAC;AAC7B;;;AAGG;IACH,SAAS,GAAa,IAAI,CAAC;AAC3B;;;AAGG;AACH,IAAA,UAAU,CAAU;AACpB;;;AAGG;AACH,IAAA,UAAU,CAAW;AACrB;;;AAGG;AACH,IAAA,eAAe,CAAW;AAC1B;;;AAGG;AACH,IAAA,GAAG,CAAW;AACd;;;AAGG;AACH,IAAA,KAAK,CAA+C;AACpD;;;AAGG;AACH,IAAA,YAAY,CAA+C;AAC3D;;;AAGG;AACH,IAAA,UAAU,CAAU;AACpB;;;AAGG;AACH,IAAA,iBAAiB,CAAU;AAC3B;;;AAGG;AACH,IAAA,QAAQ,CAAW;AACnB;;;AAGG;AACH,IAAA,UAAU,CAAW;AACrB;;;AAGG;AACH,IAAA,KAAK,CAAW;AAChB;;;AAGG;AACH,IAAA,cAAc,CAAU;AACxB;;;AAGG;AACH,IAAA,SAAS,CAAW;AACpB;;;AAGG;AACH,IAAA,SAAS,CAAW;AACpB;;;AAGG;AACH,IAAA,cAAc,CAAW;AACzB;;;AAGG;AACH,IAAA,IAAI,CAAU;AACd;;;AAGG;AACH,IAAA,IAAI,CAAU;AACd;;;AAGG;AACH,IAAA,WAAW,CAAW;AACtB;;;AAGG;AACH,IAAA,YAAY,CAAU;AACtB;;;AAGG;AACH,IAAA,YAAY,CAAU;AACtB;;;AAGG;AACH,IAAA,QAAQ,CAAU;AAClB;;;AAGG;AACH,IAAA,cAAc,CAAU;AACxB;;;AAGG;AACH,IAAA,QAAQ,CAAO;AACf;;;AAGG;AACH,IAAA,SAAS,CAAW;AACpB;;;AAGG;AACH,IAAA,WAAW,CAAO;AAClB;;;AAGG;AACH,IAAA,SAAS,CAA0B;AACtC;;ACpLD;;;AAGG;MACU,gBAAgB,CAAA;AACzB,IAAA,WAAA,GAAA,GAAgB;AAChB;;;AAGG;AACH,IAAA,KAAK,CAAC,MAAY,EAAA;AACd,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE3B,UAAU,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;SAC5B,EAAE,IAAI,CAAC,CAAC;KACZ;AACD;;;AAGG;IACH,OAAO,GAAA;AACH,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC9B;AACD;;;;AAIG;AACH,IAAA,SAAS,CAAC,KAAiB,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACjC;AACD;;;;AAIG;AACH,IAAA,OAAO,CAAC,KAAiB,EAAA;AACrB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC/B;AACD;;;;AAIG;AACH,IAAA,UAAU,CAAC,KAAiB,EAAA;AACxB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAClC;AACD;;;;AAIG;AACH,IAAA,SAAS,CAAC,KAAiB,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACjC;AACD;;;;AAIG;AACH,IAAA,QAAQ,CAAC,KAAU,EAAA;AACf,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAChC;AAEgB,IAAA,QAAQ,GAAG,IAAI,OAAO,EAAO,CAAC;AAC/C;;;AAGG;AACH,IAAA,OAAO,GAAoB,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;AAEvC,IAAA,UAAU,GAAG,IAAI,OAAO,EAAO,CAAC;AACjD;;;AAGG;AACH,IAAA,SAAS,GAAoB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;AAE3C,IAAA,YAAY,GAAG,IAAI,OAAO,EAAO,CAAC;AACnD;;;;AAIG;AACH,IAAA,WAAW,GAAoB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;AAE/C,IAAA,UAAU,GAAG,IAAI,OAAO,EAAO,CAAC;AACjD;;;;AAIG;AACH,IAAA,SAAS,GAAoB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;AAE3C,IAAA,aAAa,GAAG,IAAI,OAAO,EAAO,CAAC;AACpD;;;;AAIG;AACH,IAAA,YAAY,GAAoB,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;AAEjD,IAAA,YAAY,GAAG,IAAI,OAAO,EAAO,CAAC;AACnD;;;;AAIG;AACH,IAAA,WAAW,GAAoB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;AAE/C,IAAA,WAAW,GAAG,IAAI,OAAO,EAAO,CAAC;AAClD;;;;AAIG;AACH,IAAA,UAAU,GAAoB,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;AAE9D;;;;AAIG;AACM,IAAA,sBAAsB,GAAG,IAAI,OAAO,EAAiB,CAAC;AAClE;;AC7FD,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAE1J,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MA6EnG,sBAAsB,CAAA;AA0KD,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACrB,IAAA,EAAA,CAAA;AACD,IAAA,QAAA,CAAA;AACA,IAAA,MAAA,CAAA;AACC,IAAA,SAAA,CAAA;AACD,IAAA,IAAA,CAAA;AACA,IAAA,aAAA,CAAA;AACyB,IAAA,YAAA,CAAA;IAjLpC,OAAO,GAAY,IAAI,CAAC;AAExB,IAAA,YAAY,CAA8B;AAE1C,IAAA,IAAI,CAA2B;AAE/B,IAAA,QAAQ,CAAsB;AAE9B,IAAA,QAAQ,CAAsB;AAE9B,IAAA,SAAS,CAAsB;IAE/B,MAAM,GAAQ,EAAE,CAAC;AAEjB,IAAA,aAAa,CAAM;AAEnB,IAAA,SAAS,CAAqB;AAE9B,IAAA,SAAS,CAAqB;AAE9B,IAAA,cAAc,CAAqB;IAEnC,EAAE,GAAW,iBAAiB,EAAE,CAAC;AAEjC,IAAA,YAAY,CAAM;AAEe,IAAA,cAAc,CAAiC;AAE7D,IAAA,aAAa,CAAuB;AAEjC,IAAA,gBAAgB,CAAuB;AAExC,IAAA,eAAe,CAAuB;AAEpC,IAAA,eAAe,CAAuB;AAE7D,IAAA,kBAAkB,CAAsB;AAExC,IAAA,SAAS,CAA2B;AAEpC,IAAA,OAAO,CAAwB;AAE/B,IAAA,uBAAuB,CAAe;AAEtC,IAAA,sBAAsB,CAAe;AAErC,IAAA,iBAAiB,CAAe;IAEhC,gBAAgB,GAAW,YAAY,CAAC;AAExC,IAAA,sBAAsB,CAAe;AAErC,IAAA,yBAAyB,CAAe;AAExC,IAAA,oBAAoB,CAAe;AAEnC,IAAA,uBAAuB,CAAe;AAEtC,IAAA,IAAI,IAAI,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;KAClD;AAED,IAAA,IAAI,IAAI,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;KAClD;AAED,IAAA,IAAI,cAAc,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC;KACtC;AAED,IAAA,IAAI,WAAW,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAY,CAAC;KACnC;AAED,IAAA,IAAI,YAAY,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC;KACpC;AAED,IAAA,IAAI,YAAY,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC;KACpC;AAED,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AAED,IAAA,IAAI,QAAQ,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,QAAS,CAAC;KAChC;AAED,IAAA,IAAI,cAAc,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;KAC3E;IAED,IAAI,KAAK,CAAC,KAAU,EAAA;AAChB,QAAA,IAAI,KAAK,EAAE;AACP,YAAA,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC9B,SAAA;KACJ;AAED,IAAA,IAAI,MAAM,GAAA;AACN,QAAA,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC;AAEjF,QAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AACxB,YAAA,OAAO,WAAW,CAAC,GAAG,EAAE,CAAC;AAC5B,SAAA;KACJ;AAED,IAAA,IAAI,aAAa,GAAA;AACb,QAAA,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC;AACjF,QAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AACxB,YAAA,MAAM,eAAe,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;AAC/F,YAAA,IAAI,eAAe;AAAE,gBAAA,OAAO,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;AACrG,SAAA;KACJ;AAED,IAAA,IAAI,MAAM,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;KAC7B;AAED,IAAA,IAAI,IAAI,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;KAC3B;AAED,IAAA,IAAI,WAAW,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;KAClC;AAED,IAAA,IAAI,cAAc,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;KACzC;AAED,IAAA,IAAI,cAAc,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;KACzC;AAED,IAAA,IAAI,eAAe,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;KAC1C;AAED,IAAA,IAAI,oBAAoB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,CAAC;KAC/C;AAED,IAAA,IAAI,oBAAoB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,CAAC;KAC/C;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC;KAC5C;AAED,IAAA,IAAI,kBAAkB,GAAA;QAClB,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AACzE,QAAA,MAAM,kBAAkB,GAAG,cAAc,EAAE,MAAM,CAAC;AAElD,QAAA,OAAO,kBAAkB,CAAC;KAC7B;AAED,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;AACpB,YAAA,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;AACxB,YAAA,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;SAC7B,CAAC;KACL;AAED,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACpC,EAAqB,EACtB,QAAmB,EACnB,MAA2B,EAC1B,SAA2B,EAC5B,IAAY,EACZ,aAA4B,EACH,YAAoC,EAAA;QAR1C,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACpC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACtB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqB;QAC1B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAkB;QAC5B,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACZ,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QACH,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAwB;KACpE;IAEJ,QAAQ,GAAA;QACJ,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;KACJ;IACD,WAAW,GAAA;AACP,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACzD,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,UAAU,CAAC;AACpC,gBAAA,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;AACtF,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjE,IAAI,SAAS,GAAG,EAAE,CAAC;AACnB,gBAAA,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE;AACrC,oBAAA,SAAS,IAAI,CAAA;wDACuB,UAAU,CAAA;AAC1B,sCAAA,EAAA,IAAI,CAAC,EAAE,CAAA;AACN,uCAAA,EAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;;;qBAGhD,CAAC;AACL,iBAAA;AAED,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AACxE,aAAA;AACJ,SAAA;KACJ;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACjE,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC5B,SAAA;KACJ;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAmB,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC/C,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,iBAAiB,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,iBAAiB,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC;KACxE;AAED,IAAA,kBAAkB,CAAC,aAAwB,EAAA;AACvC,QAAA,IAAI,gBAAgB,GAAG,IAAI,CAAC,cAAc,EAAE,gBAAgB,CAAC;QAC7D,gBAAgB,EAAE,KAAK,EAAE,CAAC;QAE1B,IAAI,CAAC,YAAY,GAAG,gBAAgB,EAAE,eAAe,CAAC,aAAa,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,CAAC;KAC3E;IAED,SAAS,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,KAAK,EAAE;YAClC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzG,IAAI,CAAC,OAAuB,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAE,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1H,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAqB,EAAA;QAClC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC/B,IAAI,CAAC,OAAO,GAAI,IAAI,CAAC,SAA4B,CAAC,aAAa,CAAC;gBAChE,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,IAAI,CAAC,MAAM,EAAE;oBACb,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAChC,iBAAA;gBACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAE1C,gBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;oBAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,iBAAA;AAED,gBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,KAAK,EAAE;oBACnC,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;oBAC7C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC;AAClE,iBAAA;gBACD,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAqB,EAAA;AAChC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;YAC1B,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,gBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAClC,aAAA;YACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;AAC5B,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,KAAK,EAAE;AACpD,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;YAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB;IAED,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,IAAI,GAAA;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,cAAc,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;AAC7B,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,KAAU,KAAI;AACpF,gBAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;oBACvD,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,EAAE;YAC/B,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;AAChE,SAAA;KACJ;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC7B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAClC,aAAA;AACD,YAAA,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,EAAE;gBAC/B,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;AACnE,aAAA;AAED,YAAA,IAAI,CAAE,IAAI,CAAC,EAAc,CAAC,SAAS,EAAE;AACjC,gBAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC3B,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAA;QAC1D,MAAM,iBAAiB,GAAG,UAAU,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;AAE9E,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAC3B,OAAO;AACV,SAAA;QAED,IAAI,SAAS,GAAG,UAAU,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;AAClF,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,UAAU,CAAC,MAAM,SAAS,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3C,aAAC,CAAC,CAAC;YACH,OAAO;AACV,SAAA;QACD,MAAM,gBAAgB,GAAG,UAAU,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AAC5E,QAAA,IAAI,gBAAgB,EAAE;AAClB,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,UAAU,CAAC,MAAM,gBAAgB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;AAClD,aAAC,CAAC,CAAC;AACN,SAAA;aAAM,IAAI,IAAI,CAAC,eAAe,EAAE;;YAE7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAClD,SAAA;AAAM,aAAA,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,EAAE;YAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAClD,SAAA;KACJ;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;QAEjC,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;AAChE,SAAA;AAAM,aAAA;YACH,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;AACnE,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;KAC1D;AAED,IAAA,UAAU,CAAC,KAAiB,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AACvB,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAC9B,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,aAAA;AAED,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;YAC7B,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;AAC/D,YAAA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACpC,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAiB,EAAA;QACtB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,GAAI,IAAI,CAAC,SAAoB,CAAC;YACtD,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,GAAI,IAAI,CAAC,SAAoB,CAAC;YACtD,IAAI,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9D,IAAI,eAAe,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChE,YAAA,IAAI,aAAa,GAAG,UAAU,CAAC,cAAc,CAAc,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,CAAC;AACjG,YAAA,IAAI,QAAQ,GAAG,cAAc,GAAG,MAAM,CAAC;AACvC,YAAA,IAAI,SAAS,GAAG,eAAe,GAAG,MAAM,CAAC;YACzC,IAAI,QAAQ,GAAI,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,QAAQ,CAAC;YACjE,IAAI,SAAS,GAAI,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,SAAS,CAAC;YACnE,IAAI,MAAM,GAAI,IAAI,CAAC,SAA4B,CAAC,qBAAqB,EAAE,CAAC;AACxE,YAAA,IAAI,QAAQ,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;YACxC,IAAI,cAAc,GAAG,CAAC,QAAQ,CAAE,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAE,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAEzI,YAAA,IAAI,cAAc,EAAE;gBAChB,QAAQ,IAAI,MAAM,CAAC;gBACnB,SAAS,IAAI,MAAM,CAAC;AACvB,aAAA;YAED,IAAI,CAAC,CAAC,QAAQ,IAAI,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,MAAM,CAAC,IAAI,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE;gBACzF,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC;AACnC,gBAAA,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AACtE,aAAA;YAED,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,MAAM,CAAC,GAAG,GAAG,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAE;AAChF,gBAAA,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,GAAG,SAAS,GAAG,eAAe,GAAG,IAAI,CAAC;AAEpH,gBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;oBACpB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;AACrC,oBAAA,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AACxE,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAiB,EAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACnC,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAiB,EAAA;QACtB,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,sBAAsB,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAe,KAAK,CAAC,MAAO,CAAC,aAAa,EAAE,sBAAsB,CAAC,EAAE;YACrJ,OAAO;AACV,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AACvB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;YAE5B,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;YACtD,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;AAC/D,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACnC,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAiB,EAAA;QACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9D,IAAI,eAAe,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChE,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,GAAI,IAAI,CAAC,SAAoB,CAAC;YACtD,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,GAAI,IAAI,CAAC,SAAoB,CAAC;YACtD,IAAI,MAAM,GAAI,IAAI,CAAC,SAA4B,CAAC,qBAAqB,EAAE,CAAC;AACxE,YAAA,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC;AACnC,YAAA,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;AACjC,YAAA,IAAI,QAAQ,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;YAEvC,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;YAE5D,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,gBAAA,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,cAAc,GAAG,QAAQ,CAAC,KAAK,EAAE;oBACnE,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC;AAClC,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;oBAC5B,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC;AAClE,iBAAA;AAED,gBAAA,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG,eAAe,GAAG,QAAQ,CAAC,MAAM,EAAE;oBACnE,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;AAChC,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;oBAC5B,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;AAChE,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC5B,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC;AAC/D,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC5B,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC;AAChE,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,KAAiB,EAAA;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC3B,SAAA;KACJ;IAED,aAAa,GAAA;QACR,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACtD,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;QAClD,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;QACjD,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;KACxD;IAED,wBAAwB,GAAA;AACpB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACzG,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,2BAA2B,GAAA;AACvB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3G,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,6BAA6B,GAAA;QACzB,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,2BAA2B,GAAA;AACvB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBACzG,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/G,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,6BAA6B,GAAA;AACzB,QAAA,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAC/D,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjC,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACnC,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;AACzC,SAAA;KACJ;IAED,mBAAmB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,KAAK,KAAK,EAAE;YACrC,IAAI,CAAC,0BAA0B,EAAE,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACvB,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACvB,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,SAAA;KACJ;IAED,qBAAqB,GAAA;QACjB,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,6BAA6B,EAAE,CAAC;QACrC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,6BAA6B,EAAE,CAAC;KACxC;IAED,0BAA0B,GAAA;AACtB,QAAA,MAAM,cAAc,GAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,GAAG,UAAU,CAAC;AAE7G,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,SAAS,EAAE,CAAC,KAAK,KAAI;AACpF,YAAA,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE;AACnB,gBAAA,IAAI,QAAQ,CAAE,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,UAAU,EAAE,EAAE;oBACvF,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,uBAAuB,GAAA;QACnB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AAC/B,SAAA;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;KACvB;uGA1lBQ,sBAAsB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA0KnB,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,mBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,sBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA3Kd,sBAAsB,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EA2BpB,oBAAoB,EApGrB,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgET,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,g4DAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,CAAA,mBAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,kCAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAumBuB,kBAAkB,CAAE,EAAA,QAAA,EAAA,oBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,kBAAkB,CAAE,EAAA,QAAA,EAAA,oBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,SAAS,qMAClC,oBAAoB,CAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,CAAA,EAAA,UAAA,EAvmB/C,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQvJ,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBA3ElC,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EACjB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgET,IAAA,CAAA,EAAA,UAAA,EACW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAC/I,uBAAuB,CAAC,OAAO,iBACjC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,g4DAAA,CAAA,EAAA,CAAA;;0BA4KI,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;;0BAOlB,QAAQ;;0BAAI,QAAQ;yCAvJQ,cAAc,EAAA,CAAA;sBAA9C,SAAS;uBAAC,oBAAoB,CAAA;gBAEZ,aAAa,EAAA,CAAA;sBAA/B,SAAS;uBAAC,MAAM,CAAA;gBAEK,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEC,eAAe,EAAA,CAAA;sBAAnC,SAAS;uBAAC,QAAQ,CAAA;gBAEI,eAAe,EAAA,CAAA;sBAArC,SAAS;uBAAC,UAAU,CAAA;;MA+jBZ,mBAAmB,CAAA;uGAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,iBAlmBnB,sBAAsB,EA+lBQ,oBAAoB,CAAA,EAAA,OAAA,EAAA,CADjD,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,aAE9F,YAAY,CAAA,EAAA,CAAA,CAAA;AAEb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EAJlB,OAAA,EAAA,CAAA,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,EAE9F,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAEb,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAL/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,CAAC;AACzG,oBAAA,YAAY,EAAE,CAAC,sBAAsB,EAAE,oBAAoB,CAAC;oBAC5D,OAAO,EAAE,CAAC,YAAY,CAAC;AAC1B,iBAAA,CAAA;;;MChtBY,qBAAqB,CAAA;AACV,IAAA,eAAA,CAAA;AAAmC,IAAA,iBAAA,CAAA;IAAvD,WAAoB,CAAA,eAAyB,EAAU,iBAAoC,EAAA;QAAvE,IAAe,CAAA,eAAA,GAAf,eAAe,CAAU;QAAU,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAmB;KAAI;AAE/F,IAAA,GAAG,CAAI,KAAuB,EAAE,aAAiB,EAAE,OAAqC,EAAA;QACpF,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAEhD,QAAA,IAAI,KAAK;AAAE,YAAA,OAAO,KAAK,CAAC;QAExB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAM,KAAK,EAAE,aAAa,CAAC,CAAC;KAC9D;AACJ;;ACJD;;;AAGG;MAEU,aAAa,CAAA;AAGF,IAAA,MAAA,CAAA;AAAgC,IAAA,QAAA,CAAA;AAA8C,IAAA,QAAA,CAAA;AAFlG,IAAA,qBAAqB,GAAqE,IAAI,GAAG,EAAE,CAAC;AAEpG,IAAA,WAAA,CAAoB,MAAsB,EAAU,QAAkB,EAA4B,QAAkB,EAAA;QAAhG,IAAM,CAAA,MAAA,GAAN,MAAM,CAAgB;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA4B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;KAAI;AACxH;;;;;;AAMG;IACI,IAAI,CAAI,aAAsB,EAAE,MAA2B,EAAA;QAC9D,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE;AACpD,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,2BAA2B,CAAI,MAAM,EAAE,aAAa,CAAC,CAAC;AAE7E,QAAA,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,kBAAkB,GAAG,aAAa,CAAC;AAEtF,QAAA,OAAO,SAAS,CAAC;KACpB;AACD;;;;AAIG;AACI,IAAA,WAAW,CAAC,GAA0B,EAAA;QACzC,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;KACvD;IAEO,2BAA2B,CAAI,MAA2B,EAAE,aAAsB,EAAA;AACtF,QAAA,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,CAAC;AAC1B,QAAA,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;AAErC,QAAA,MAAM,SAAS,GAAG,IAAI,gBAAgB,EAAK,CAAC;AAC5C,QAAA,GAAG,CAAC,GAAG,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAErC,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,MAAK;AACzC,YAAA,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC/D,SAAC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,MAAK;AAClD,YAAA,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC,CAAC;YAC9C,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,GAAG,CAAC,WAAW,EAAE,CAAC;AACtB,SAAC,CAAC,CAAC;AAEH,QAAA,MAAM,YAAY,GAAG,eAAe,CAAC,sBAAsB,EAAE,EAAE,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,eAAe,EAAE,IAAI,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QAE5K,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE9C,MAAM,OAAO,GAAI,YAAY,CAAC,QAAiC,CAAC,SAAS,CAAC,CAAC,CAAgB,CAAC;QAC5F,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,EAAE;YAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC3C,SAAA;AAAM,aAAA;YACH,UAAU,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AACpD,SAAA;QAED,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AAExD,QAAA,OAAO,SAAS,CAAC;KACpB;AAEO,IAAA,6BAA6B,CAAC,SAAgC,EAAA;AAClE,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YAC1D,OAAO;AACV,SAAA;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACpD,kBAAkB,CAAC,OAAO,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KAChD;IAEO,qBAAqB,CAAC,aAAwB,EAAE,MAA2B,EAAA;QAC/E,IAAI,MAAM,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QACD,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AACnD,YAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,kBAAkB,KAAK,aAAa,EAAE;gBACrD,UAAU,GAAG,KAAK,CAAC;gBACnB,MAAM;AACT,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,UAAU,CAAC;KACrB;AAvFQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,wEAG0D,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAH/E,aAAa,EAAA,CAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,UAAU;;0BAIkE,MAAM;2BAAC,QAAQ,CAAA;;;AChB5F;;AAEG;;;;"}