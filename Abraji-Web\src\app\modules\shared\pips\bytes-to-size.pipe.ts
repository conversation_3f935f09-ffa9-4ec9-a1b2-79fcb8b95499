import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'bytesToSize',
})
export class BytesToSizePipe implements PipeTransform {

  transform(bytes: number, decimalPoints: number = 2): string {
    if (bytes === 0) return '0 MB';

    const k = 1024;
    const sizes = ['MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    let i = Math.floor(Math.log(bytes) / Math.log(k));

    // Ensure we start with MB
    if (i < 2) {
      i = 2;
    }

    const size = parseFloat((bytes / Math.pow(k, i)).toFixed(decimalPoints));
    return `${size} ${sizes[i - 2]}`;
  }

}
