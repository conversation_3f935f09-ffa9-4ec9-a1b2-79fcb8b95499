{"version": 3, "file": "primeng-keyfilter.mjs", "sources": ["../../src/app/components/keyfilter/keyfilter.ts", "../../src/app/components/keyfilter/primeng-keyfilter.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { Directive, ElementRef, EventEmitter, HostListener, Inject, Input, NgModule, Output, PLATFORM_ID, Provider, booleanAttribute, forwardRef } from '@angular/core';\nimport { AbstractControl, NG_VALIDATORS, Validator } from '@angular/forms';\nimport { DomHandler } from 'primeng/dom';\nimport { KeyFilterPattern } from './keyfilter.interface';\n\nexport const KEYFILTER_VALIDATOR: Provider = {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => KeyFilter),\n    multi: true\n};\n\ntype SafariKeys = {\n    63234: number;\n    63235: number;\n    63232: number;\n    63233: number;\n    63276: number;\n    63277: number;\n    63272: number;\n    63273: number;\n    63275: number;\n};\n\ntype Keys = {\n    TAB: number;\n    RETURN: number;\n    ESC: number;\n    BACKSPACE: number;\n    DELETE: number;\n};\n\nconst DEFAULT_MASKS: Record<KeyFilterPattern, RegExp> = {\n    pint: /^[\\d]*$/,\n    int: /^[-]?[\\d]*$/,\n    pnum: /^[\\d\\.]*$/,\n    money: /^[\\d\\.\\s,]*$/,\n    num: /^[-]?[\\d\\.]*$/,\n    hex: /^[0-9a-f]*$/i,\n    email: /^[a-z0-9_\\.\\-@]*$/i,\n    alpha: /^[a-z_]*$/i,\n    alphanum: /^[a-z0-9_]*$/i\n};\n\nconst KEYS: Keys = {\n    TAB: 9,\n    RETURN: 13,\n    ESC: 27,\n    BACKSPACE: 8,\n    DELETE: 46\n};\n\nconst SAFARI_KEYS: SafariKeys = {\n    63234: 37, // left\n    63235: 39, // right\n    63232: 38, // up\n    63233: 40, // down\n    63276: 33, // page up\n    63277: 34, // page down\n    63272: 46, // delete\n    63273: 36, // home\n    63275: 35 // end\n};\n/**\n * KeyFilter Directive is a built-in feature of InputText to restrict user input based on a regular expression.\n * @group Components\n */\n@Directive({\n    selector: '[pKeyFilter]',\n    providers: [KEYFILTER_VALIDATOR],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class KeyFilter implements Validator {\n    /**\n     * When enabled, instead of blocking keys, input is validated internally to test against the regular expression.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) pValidateOnly: boolean | undefined;\n    /**\n     * Sets the pattern for key filtering.\n     * @group Props\n     */\n\n    @Input('pKeyFilter') set pattern(_pattern: RegExp | KeyFilterPattern | null | undefined) {\n        this._pattern = _pattern;\n\n        if (_pattern instanceof RegExp) {\n            this.regex = _pattern;\n        } else if (_pattern in DEFAULT_MASKS) {\n            this.regex = DEFAULT_MASKS[_pattern];\n        } else {\n            this.regex = /./;\n        }\n    }\n    get pattern(): RegExp | KeyFilterPattern | null | undefined {\n        return this._pattern;\n    }\n\n    /**\n     * Emits a value whenever the ngModel of the component changes.\n     * @param {(string | number)} modelValue - Custom model change event.\n     * @group Emits\n     */\n    @Output() ngModelChange: EventEmitter<string | number> = new EventEmitter<string | number>();\n\n    regex: RegExp = /./;\n\n    _pattern: RegExp | KeyFilterPattern | null | undefined;\n\n    isAndroid: boolean;\n\n    lastValue: any;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, public el: ElementRef) {\n        if (isPlatformBrowser(this.platformId)) {\n            this.isAndroid = DomHandler.isAndroid();\n        } else {\n            this.isAndroid = false;\n        }\n    }\n\n    isNavKeyPress(e: KeyboardEvent) {\n        let k = e.keyCode;\n        k = DomHandler.getBrowser().safari ? (SAFARI_KEYS as any)[k] || k : k;\n\n        return (k >= 33 && k <= 40) || k == KEYS.RETURN || k == KEYS.TAB || k == KEYS.ESC;\n    }\n\n    isSpecialKey(e: KeyboardEvent) {\n        let k = e.keyCode || e.charCode;\n\n        return k == 9 || k == 13 || k == 27 || k == 16 || k == 17 || (k >= 18 && k <= 20) || (DomHandler.getBrowser().opera && !e.shiftKey && (k == 8 || (k >= 33 && k <= 35) || (k >= 36 && k <= 39) || (k >= 44 && k <= 45)));\n    }\n\n    getKey(e: KeyboardEvent) {\n        let k = e.keyCode || e.charCode;\n        return DomHandler.getBrowser().safari ? (SAFARI_KEYS as any)[k] || k : k;\n    }\n\n    getCharCode(e: KeyboardEvent) {\n        return e.charCode || e.keyCode || e.which;\n    }\n\n    findDelta(value: string, prevValue: string) {\n        let delta = '';\n\n        for (let i = 0; i < value.length; i++) {\n            let str = value.substr(0, i) + value.substr(i + value.length - prevValue.length);\n\n            if (str === prevValue) delta = value.substr(i, value.length - prevValue.length);\n        }\n\n        return delta;\n    }\n\n    isValidChar(c: string) {\n        return (<RegExp>this.regex).test(c);\n    }\n\n    isValidString(str: string) {\n        for (let i = 0; i < str.length; i++) {\n            if (!this.isValidChar(str.substr(i, 1))) {\n                return false;\n            }\n        }\n\n        return true;\n    }\n\n    @HostListener('input', ['$event'])\n    onInput(e: KeyboardEvent) {\n        if (this.isAndroid && !this.pValidateOnly) {\n            let val = this.el.nativeElement.value;\n            let lastVal = this.lastValue || '';\n\n            let inserted = this.findDelta(val, lastVal);\n            let removed = this.findDelta(lastVal, val);\n            let pasted = inserted.length > 1 || (!inserted && !removed);\n\n            if (pasted) {\n                if (!this.isValidString(val)) {\n                    this.el.nativeElement.value = lastVal;\n                    this.ngModelChange.emit(lastVal);\n                }\n            } else if (!removed) {\n                if (!this.isValidChar(inserted)) {\n                    this.el.nativeElement.value = lastVal;\n                    this.ngModelChange.emit(lastVal);\n                }\n            }\n\n            val = this.el.nativeElement.value;\n            if (this.isValidString(val)) {\n                this.lastValue = val;\n            }\n        }\n    }\n\n    @HostListener('keypress', ['$event'])\n    onKeyPress(e: KeyboardEvent) {\n        if (this.isAndroid || this.pValidateOnly) {\n            return;\n        }\n\n        let browser = DomHandler.getBrowser();\n        let k = this.getKey(e);\n\n        if (browser.mozilla && (e.ctrlKey || e.altKey)) {\n            return;\n        } else if (k == 17 || k == 18) {\n            return;\n        }\n\n        // Enter key\n        if (k == 13) {\n            return;\n        }\n\n        let c = this.getCharCode(e);\n        let cc = String.fromCharCode(c);\n        let ok = true;\n\n        if (!browser.mozilla && (this.isSpecialKey(e) || !cc)) {\n            return;\n        }\n\n        let valueCheck = this.el.nativeElement.value || '';\n        const selectionStart = (<HTMLInputElement>e.currentTarget).selectionStart || 0;\n        const selectionEnd = (<HTMLInputElement>e.currentTarget).selectionEnd || 0;\n        let val = valueCheck.substring(0, selectionStart) + cc + valueCheck.substring(selectionEnd);\n\n        ok = (<RegExp>this.regex).test(val);\n\n        if (!ok) {\n            e.preventDefault();\n        }\n    }\n\n    @HostListener('paste', ['$event'])\n    onPaste(e: ClipboardEvent) {\n        const clipboardData = e.clipboardData || (<any>this.document.defaultView).clipboardData.getData('text');\n        if (clipboardData) {\n            let pattern = /\\{[0-9]+\\}/;\n            const pastedText = clipboardData.getData('text');\n            if (pattern.test(this.regex.toString())) {\n                if (!this.regex.test(pastedText)) {\n                    e.preventDefault();\n                    return;\n                }\n            } else {\n                for (let char of pastedText.toString()) {\n                    if (!this.regex.test(char)) {\n                        e.preventDefault();\n                        return;\n                    }\n                }\n            }\n        }\n    }\n\n    validate(c: AbstractControl): { [key: string]: any } | any {\n        if (this.pValidateOnly) {\n            let value = this.el.nativeElement.value;\n            if (value && !this.regex.test(value)) {\n                return {\n                    validatePattern: false\n                };\n            }\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [KeyFilter],\n    declarations: [KeyFilter]\n})\nexport class KeyFilterModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAMa,MAAA,mBAAmB,GAAa;AACzC,IAAA,OAAO,EAAE,aAAa;AACtB,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,SAAS,CAAC;AACxC,IAAA,KAAK,EAAE,IAAI;EACb;AAsBF,MAAM,aAAa,GAAqC;AACpD,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,GAAG,EAAE,aAAa;AAClB,IAAA,IAAI,EAAE,WAAW;AACjB,IAAA,KAAK,EAAE,cAAc;AACrB,IAAA,GAAG,EAAE,eAAe;AACpB,IAAA,GAAG,EAAE,cAAc;AACnB,IAAA,KAAK,EAAE,oBAAoB;AAC3B,IAAA,KAAK,EAAE,YAAY;AACnB,IAAA,QAAQ,EAAE,eAAe;CAC5B,CAAC;AAEF,MAAM,IAAI,GAAS;AACf,IAAA,GAAG,EAAE,CAAC;AACN,IAAA,MAAM,EAAE,EAAE;AACV,IAAA,GAAG,EAAE,EAAE;AACP,IAAA,SAAS,EAAE,CAAC;AACZ,IAAA,MAAM,EAAE,EAAE;CACb,CAAC;AAEF,MAAM,WAAW,GAAe;AAC5B,IAAA,KAAK,EAAE,EAAE;AACT,IAAA,KAAK,EAAE,EAAE;AACT,IAAA,KAAK,EAAE,EAAE;AACT,IAAA,KAAK,EAAE,EAAE;AACT,IAAA,KAAK,EAAE,EAAE;AACT,IAAA,KAAK,EAAE,EAAE;AACT,IAAA,KAAK,EAAE,EAAE;AACT,IAAA,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,EAAE;CACZ,CAAC;AACF;;;AAGG;MAQU,SAAS,CAAA;AAyCoB,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAxC/G;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;IAEH,IAAyB,OAAO,CAAC,QAAsD,EAAA;AACnF,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAI,QAAQ,YAAY,MAAM,EAAE;AAC5B,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;AACzB,SAAA;aAAM,IAAI,QAAQ,IAAI,aAAa,EAAE;AAClC,YAAA,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;AACxC,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AACpB,SAAA;KACJ;AACD,IAAA,IAAI,OAAO,GAAA;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;AAED;;;;AAIG;AACO,IAAA,aAAa,GAAkC,IAAI,YAAY,EAAmB,CAAC;IAE7F,KAAK,GAAW,GAAG,CAAC;AAEpB,IAAA,QAAQ,CAA+C;AAEvD,IAAA,SAAS,CAAU;AAEnB,IAAA,SAAS,CAAM;AAEf,IAAA,WAAA,CAAsC,QAAkB,EAA+B,UAAe,EAAS,EAAc,EAAA;QAAvF,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;AACzH,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;AAC3C,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,CAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;QAClB,CAAC,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC,MAAM,GAAI,WAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEtE,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC;KACrF;AAED,IAAA,YAAY,CAAC,CAAgB,EAAA;QACzB,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,QAAQ,CAAC;AAEhC,QAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,UAAU,CAAC,UAAU,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3N;AAED,IAAA,MAAM,CAAC,CAAgB,EAAA;QACnB,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,QAAQ,CAAC;AAChC,QAAA,OAAO,UAAU,CAAC,UAAU,EAAE,CAAC,MAAM,GAAI,WAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC5E;AAED,IAAA,WAAW,CAAC,CAAgB,EAAA;QACxB,OAAO,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC;KAC7C;IAED,SAAS,CAAC,KAAa,EAAE,SAAiB,EAAA;QACtC,IAAI,KAAK,GAAG,EAAE,CAAC;AAEf,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YAEjF,IAAI,GAAG,KAAK,SAAS;AAAE,gBAAA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;AACnF,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,WAAW,CAAC,CAAS,EAAA;QACjB,OAAgB,IAAI,CAAC,KAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACvC;AAED,IAAA,aAAa,CAAC,GAAW,EAAA;AACrB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjC,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;AACrC,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAGD,IAAA,OAAO,CAAC,CAAgB,EAAA;QACpB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvC,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;AACtC,YAAA,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;YAEnC,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC3C,YAAA,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,CAAC;AAE5D,YAAA,IAAI,MAAM,EAAE;AACR,gBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;oBAC1B,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC;AACtC,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpC,iBAAA;AACJ,aAAA;iBAAM,IAAI,CAAC,OAAO,EAAE;AACjB,gBAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;oBAC7B,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC;AACtC,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpC,iBAAA;AACJ,aAAA;YAED,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;AAClC,YAAA,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;AACzB,gBAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACxB,aAAA;AACJ,SAAA;KACJ;AAGD,IAAA,UAAU,CAAC,CAAgB,EAAA;AACvB,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,EAAE;YACtC,OAAO;AACV,SAAA;AAED,QAAA,IAAI,OAAO,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;QACtC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAEvB,QAAA,IAAI,OAAO,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE;YAC5C,OAAO;AACV,SAAA;AAAM,aAAA,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE;YAC3B,OAAO;AACV,SAAA;;QAGD,IAAI,CAAC,IAAI,EAAE,EAAE;YACT,OAAO;AACV,SAAA;QAED,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,EAAE,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,EAAE,GAAG,IAAI,CAAC;AAEd,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACnD,OAAO;AACV,SAAA;QAED,IAAI,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;QACnD,MAAM,cAAc,GAAsB,CAAC,CAAC,aAAc,CAAC,cAAc,IAAI,CAAC,CAAC;QAC/E,MAAM,YAAY,GAAsB,CAAC,CAAC,aAAc,CAAC,YAAY,IAAI,CAAC,CAAC;AAC3E,QAAA,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAE5F,EAAE,GAAY,IAAI,CAAC,KAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,CAAC,EAAE,EAAE;YACL,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAGD,IAAA,OAAO,CAAC,CAAiB,EAAA;AACrB,QAAA,MAAM,aAAa,GAAG,CAAC,CAAC,aAAa,IAAU,IAAI,CAAC,QAAQ,CAAC,WAAY,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACxG,QAAA,IAAI,aAAa,EAAE;YACf,IAAI,OAAO,GAAG,YAAY,CAAC;YAC3B,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACrC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBAC9B,CAAC,CAAC,cAAc,EAAE,CAAC;oBACnB,OAAO;AACV,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAK,IAAI,IAAI,IAAI,UAAU,CAAC,QAAQ,EAAE,EAAE;oBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACxB,CAAC,CAAC,cAAc,EAAE,CAAC;wBACnB,OAAO;AACV,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,CAAkB,EAAA;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;YACxC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAClC,OAAO;AACH,oBAAA,eAAe,EAAE,KAAK;iBACzB,CAAC;AACL,aAAA;AACJ,SAAA;KACJ;uGArMQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAyCE,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAzCpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EAKE,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAVzB,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,SAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,mBAAmB,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAKvB,SAAS,EAAA,UAAA,EAAA,CAAA;kBAPrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,CAAC,mBAAmB,CAAC;AAChC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BA0CgB,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;kEApCrC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAMb,OAAO,EAAA,CAAA;sBAA/B,KAAK;uBAAC,YAAY,CAAA;gBAoBT,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAmEP,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;gBA8BjC,UAAU,EAAA,CAAA;sBADT,YAAY;uBAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAA;gBAyCpC,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAuCxB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EA7Mf,YAAA,EAAA,CAAA,SAAS,CAyMR,EAAA,OAAA,EAAA,CAAA,YAAY,aAzMb,SAAS,CAAA,EAAA,CAAA,CAAA;AA6MT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAJd,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,SAAS,CAAC;oBACpB,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;ACtRD;;AAEG;;;;"}