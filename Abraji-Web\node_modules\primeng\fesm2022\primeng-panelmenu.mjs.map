{"version": 3, "file": "primeng-panelmenu.mjs", "sources": ["../../src/app/components/panelmenu/panelmenu.ts", "../../src/app/components/panelmenu/primeng-panelmenu.ts"], "sourcesContent": ["import { animate, state, style, transition, trigger } from '@angular/animations';\nimport { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnChanges,\n    Output,\n    QueryList,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    computed,\n    forwardRef,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { MenuItem, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\n\n@Component({\n    selector: 'p-panelMenuSub',\n    template: `\n        <ul\n            #list\n            [ngClass]=\"{ 'p-submenu-list': true, 'p-panelmenu-root-list': root }\"\n            role=\"tree\"\n            [tabindex]=\"-1\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.data-pc-section]=\"'menu'\"\n            [attr.aria-hidden]=\"!parentExpanded\"\n            (focusin)=\"menuFocus.emit($event)\"\n            (focusout)=\"menuBlur.emit($event)\"\n            (keydown)=\"menuKeyDown.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem let-index=\"index\" [ngForOf]=\"items\">\n                <li *ngIf=\"processedItem.separator\" class=\"p-menuitem-separator\" role=\"separator\"></li>\n                <li\n                    *ngIf=\"!processedItem.separator && isItemVisible(processedItem)\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    role=\"treeitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.aria-label]=\"getItemProp(processedItem, 'label')\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    [class.p-hidden]=\"processedItem.visible === false\"\n                    [class.p-focus]=\"isItemFocused(processedItem) && !isItemDisabled(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [pTooltip]=\"getItemProp(processedItem, 'tooltip')\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                class=\"p-menuitem-link\"\n                                [ngClass]=\"{ 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [attr.tabindex]=\"!!parentExpanded ? '0' : '-1'\"\n                            >\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!panelMenu.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"isItemActive(processedItem)\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!isItemActive(processedItem)\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"panelMenu.submenuIconTemplate\"></ng-template>\n                                </ng-container>\n                                <span class=\"p-menuitem-icon\" [ngClass]=\"processedItem.icon\" *ngIf=\"processedItem.icon\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"processedItem.item?.escape !== false; else htmlLabel\">{{ getItemProp(processedItem, 'label') }}</span>\n                                <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(processedItem, 'label')\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"processedItem.badge\" [ngClass]=\"processedItem.badgeStyleClass\">{{ processedItem.badge }}</span>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                class=\"p-menuitem-link\"\n                                [ngClass]=\"{ 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [attr.title]=\"getItemProp(processedItem, 'title')\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [attr.tabindex]=\"!!parentExpanded ? '0' : '-1'\"\n                            >\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!panelMenu.submenuIconTemplate\">\n                                        <AngleDownIcon *ngIf=\"isItemActive(processedItem)\" [styleClass]=\"'p-submenu-icon'\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\" />\n                                        <AngleRightIcon *ngIf=\"!isItemActive(processedItem)\" [styleClass]=\"'p-submenu-icon'\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"panelMenu.submenuIconTemplate\"></ng-template>\n                                </ng-container>\n                                <span class=\"p-menuitem-icon\" [ngClass]=\"processedItem.icon\" *ngIf=\"processedItem.icon\" [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape') !== false; else htmlRouteLabel\">{{ getItemProp(processedItem, 'label') }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(processedItem, 'label')\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"processedItem.badge\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <div class=\"p-toggleable-content\" [@submenu]=\"getAnimation(processedItem)\">\n                        <p-panelMenuSub\n                            *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem) && isItemExpanded(processedItem)\"\n                            [id]=\"getItemId(processedItem) + '_list'\"\n                            [panelId]=\"panelId\"\n                            [items]=\"processedItem?.items\"\n                            [itemTemplate]=\"itemTemplate\"\n                            [transitionOptions]=\"transitionOptions\"\n                            [focusedItemId]=\"focusedItemId\"\n                            [activeItemPath]=\"activeItemPath\"\n                            [level]=\"level + 1\"\n                            [parentExpanded]=\"!!parentExpanded && isItemExpanded(processedItem)\"\n                            (itemToggle)=\"onItemToggle($event)\"\n                        ></p-panelMenuSub>\n                    </div>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n    animations: [\n        trigger('submenu', [\n            state(\n                'hidden',\n                style({\n                    height: '0'\n                })\n            ),\n            state(\n                'visible',\n                style({\n                    height: '*'\n                })\n            ),\n            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ],\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class PanelMenuSub {\n    @Input() panelId: string | undefined;\n\n    @Input() focusedItemId: string | undefined;\n\n    @Input() items: any[];\n\n    @Input() itemTemplate: HTMLElement | undefined;\n\n    @Input({ transform: numberAttribute }) level: number = 0;\n\n    @Input() activeItemPath: any[];\n\n    @Input({ transform: booleanAttribute }) root: boolean | undefined;\n\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n\n    @Input() transitionOptions: string | undefined;\n\n    @Input({ transform: booleanAttribute }) parentExpanded: boolean | undefined;\n\n    @Output() itemToggle: EventEmitter<any> = new EventEmitter<any>();\n\n    @Output() menuFocus: EventEmitter<any> = new EventEmitter<any>();\n\n    @Output() menuBlur: EventEmitter<any> = new EventEmitter<any>();\n\n    @Output() menuKeyDown: EventEmitter<any> = new EventEmitter<any>();\n\n    @ViewChild('list') listViewChild: ElementRef;\n\n    constructor(@Inject(forwardRef(() => PanelMenu)) public panelMenu: PanelMenu, public el: ElementRef) {}\n\n    getItemId(processedItem) {\n        return processedItem.item?.id ?? `${this.panelId}_${processedItem.key}`;\n    }\n\n    getItemKey(processedItem) {\n        return this.getItemId(processedItem);\n    }\n\n    getItemClass(processedItem) {\n        return {\n            'p-menuitem': true,\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n\n    getItemProp(processedItem, name?, params?) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n\n    getItemLabel(processedItem) {\n        return this.getItemProp(processedItem, 'label');\n    }\n\n    isItemExpanded(processedItem) {\n        return processedItem.expanded;\n    }\n\n    isItemActive(processedItem) {\n        return this.isItemExpanded(processedItem) || this.activeItemPath.some((path) => path && path.key === processedItem.key);\n    }\n\n    isItemVisible(processedItem) {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n\n    isItemDisabled(processedItem) {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n\n    isItemFocused(processedItem) {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n\n    isItemGroup(processedItem) {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    getAnimation(processedItem) {\n        return this.isItemActive(processedItem) ? { value: 'visible', params: { transitionParams: this.transitionOptions, height: '*' } } : { value: 'hidden', params: { transitionParams: this.transitionOptions, height: '0' } };\n    }\n\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n\n    getAriaPosInset(index) {\n        return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n\n    onItemClick(event, processedItem) {\n        if (!this.isItemDisabled(processedItem)) {\n            this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n            this.itemToggle.emit({ processedItem, expanded: !this.isItemActive(processedItem) });\n        }\n    }\n\n    onItemToggle(event) {\n        this.itemToggle.emit(event);\n    }\n}\n\n@Component({\n    selector: 'p-panelMenuList',\n    template: `\n        <p-panelMenuSub\n            #submenu\n            [root]=\"true\"\n            [id]=\"panelId + '_list'\"\n            [panelId]=\"panelId\"\n            [tabindex]=\"tabindex\"\n            [itemTemplate]=\"itemTemplate\"\n            [focusedItemId]=\"focused ? focusedItemId : undefined\"\n            [activeItemPath]=\"activeItemPath()\"\n            [transitionOptions]=\"transitionOptions\"\n            [items]=\"processedItems()\"\n            [parentExpanded]=\"parentExpanded\"\n            (itemToggle)=\"onItemToggle($event)\"\n            (keydown)=\"onKeyDown($event)\"\n            (menuFocus)=\"onFocus($event)\"\n            (menuBlur)=\"onBlur($event)\"\n        ></p-panelMenuSub>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./panelmenu.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class PanelMenuList implements OnChanges {\n    @Input() panelId: string | undefined;\n\n    @Input() id: string | undefined;\n\n    @Input() items: any[];\n\n    @Input() itemTemplate: HTMLElement | undefined;\n\n    @Input({ transform: booleanAttribute }) parentExpanded: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) expanded: boolean | undefined;\n\n    @Input() transitionOptions: string | undefined;\n\n    @Input({ transform: booleanAttribute }) root: boolean | undefined;\n\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n\n    @Input() activeItem: any;\n\n    @Output() itemToggle: EventEmitter<any> = new EventEmitter<any>();\n\n    @Output() headerFocus: EventEmitter<any> = new EventEmitter<any>();\n\n    @ViewChild('submenu') subMenuViewChild: PanelMenuSub;\n\n    searchTimeout: any;\n\n    searchValue: any;\n\n    focused: boolean | undefined;\n\n    focusedItem = signal<any>(null);\n\n    activeItemPath = signal<any[]>([]);\n\n    processedItems = signal<any[]>([]);\n\n    visibleItems = computed(() => {\n        const processedItems = this.processedItems();\n        return this.flatItems(processedItems);\n    });\n\n    get focusedItemId() {\n        const focusedItem = this.focusedItem();\n        return focusedItem && focusedItem.item?.id ? focusedItem.item.id : ObjectUtils.isNotEmpty(this.focusedItem()) ? `${this.panelId}_${this.focusedItem().key}` : undefined;\n    }\n\n    constructor(private el: ElementRef) {}\n\n    ngOnChanges(changes: SimpleChanges) {\n        this.processedItems.set(this.createProcessedItems(changes?.items?.currentValue || this.items || []));\n    }\n\n    getItemProp(processedItem, name) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name]) : undefined;\n    }\n\n    getItemLabel(processedItem) {\n        return this.getItemProp(processedItem, 'label');\n    }\n\n    isItemVisible(processedItem) {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n\n    isItemDisabled(processedItem) {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n\n    isItemActive(processedItem) {\n        return this.activeItemPath().some((path) => path.key === processedItem.parentKey);\n    }\n\n    isItemGroup(processedItem) {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    isElementInPanel(event, element) {\n        const panel = event.currentTarget.closest('[data-pc-section=\"panel\"]');\n\n        return panel && panel.contains(element);\n    }\n\n    isItemMatched(processedItem) {\n        return this.isValidItem(processedItem) && this.getItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n\n    isVisibleItem(processedItem) {\n        return !!processedItem && (processedItem.level === 0 || this.isItemActive(processedItem)) && this.isItemVisible(processedItem);\n    }\n\n    isValidItem(processedItem) {\n        return !!processedItem && !this.isItemDisabled(processedItem) && !processedItem.separator;\n    }\n\n    findFirstItem() {\n        return this.visibleItems().find((processedItem) => this.isValidItem(processedItem));\n    }\n\n    findLastItem() {\n        return ObjectUtils.findLast(this.visibleItems(), (processedItem) => this.isValidItem(processedItem));\n    }\n\n    findItemByEventTarget(target: EventTarget): undefined | any {\n        let parentNode = target as ParentNode & Element;\n\n        while (parentNode && parentNode.tagName?.toLowerCase() !== 'li') {\n            parentNode = parentNode?.parentNode as Element;\n        }\n\n        return parentNode?.id && this.visibleItems().find((processedItem) => this.isValidItem(processedItem) && `${this.panelId}_${processedItem.key}` === parentNode.id);\n    }\n\n    createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n        const processedItems = [];\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newItem = {\n                    icon: item.icon,\n                    expanded: item.expanded,\n                    separator: item.separator,\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n\n                newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n        return processedItems;\n    }\n\n    findProcessedItemByItemKey(key, processedItems?, level = 0) {\n        processedItems = processedItems || this.processedItems();\n        if (processedItems && processedItems.length) {\n            for (let i = 0; i < processedItems.length; i++) {\n                const processedItem = processedItems[i];\n\n                if (this.getItemProp(processedItem, 'key') === key) return processedItem;\n                const matchedItem = this.findProcessedItemByItemKey(key, processedItem.items, level + 1);\n                if (matchedItem) return matchedItem;\n            }\n        }\n    }\n\n    flatItems(processedItems, processedFlattenItems = []) {\n        processedItems &&\n            processedItems.forEach((processedItem) => {\n                if (this.isVisibleItem(processedItem)) {\n                    processedFlattenItems.push(processedItem);\n                    this.flatItems(processedItem.items, processedFlattenItems);\n                }\n            });\n\n        return processedFlattenItems;\n    }\n\n    changeFocusedItem(event) {\n        const { originalEvent, processedItem, focusOnNext, selfCheck, allowHeaderFocus = true } = event;\n\n        if (ObjectUtils.isNotEmpty(this.focusedItem()) && this.focusedItem().key !== processedItem.key) {\n            this.focusedItem.set(processedItem);\n            this.scrollInView();\n        } else if (allowHeaderFocus) {\n            this.headerFocus.emit({ originalEvent, focusOnNext, selfCheck });\n        }\n    }\n\n    scrollInView() {\n        const element = DomHandler.findSingle(this.subMenuViewChild.listViewChild.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n\n    onFocus(event) {\n        if (!this.focused) {\n            this.focused = true;\n            const focusedItem = this.focusedItem() || (this.isElementInPanel(event, event.relatedTarget) ? this.findItemByEventTarget(event.target) || this.findFirstItem() : this.findLastItem());\n            if (event.relatedTarget !== null) this.focusedItem.set(focusedItem);\n        }\n    }\n\n    onBlur(event) {\n        const target = event.relatedTarget;\n\n        if (this.focused && !this.el.nativeElement.contains(target)) {\n            this.focused = false;\n            this.focusedItem.set(null);\n            this.searchValue = '';\n        }\n    }\n\n    onItemToggle(event) {\n        const { processedItem, expanded } = event;\n        processedItem.expanded = !processedItem.expanded;\n\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== processedItem.parentKey);\n        expanded && activeItemPath.push(processedItem);\n\n        this.activeItemPath.set(activeItemPath);\n        this.processedItems.update((value) => value.map((i) => (i === processedItem ? processedItem : i)));\n        this.focusedItem.set(processedItem);\n    }\n\n    onKeyDown(event) {\n        const metaKey = event.metaKey || event.ctrlKey;\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n            case 'Tab':\n            case 'PageDown':\n            case 'PageUp':\n            case 'Backspace':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchItems(event, event.key);\n                }\n\n                break;\n        }\n    }\n\n    onArrowDownKey(event) {\n        const processedItem = ObjectUtils.isNotEmpty(this.focusedItem()) ? this.findNextItem(this.focusedItem()) : this.findFirstItem();\n        this.changeFocusedItem({ originalEvent: event, processedItem, focusOnNext: true });\n        event.preventDefault();\n    }\n    onArrowUpKey(event) {\n        const processedItem = ObjectUtils.isNotEmpty(this.focusedItem()) ? this.findPrevItem(this.focusedItem()) : this.findLastItem();\n\n        this.changeFocusedItem({ originalEvent: event, processedItem, selfCheck: true });\n        event.preventDefault();\n    }\n\n    onArrowLeftKey(event) {\n        if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n            const matched = this.activeItemPath().some((p) => p.key === this.focusedItem().key);\n\n            if (matched) {\n                const activeItemPath = this.activeItemPath().filter((p) => p.key !== this.focusedItem().key);\n                this.activeItemPath.set(activeItemPath);\n            } else {\n                const focusedItem = ObjectUtils.isNotEmpty(this.focusedItem().parent) ? this.focusedItem().parent : this.focusedItem();\n                this.focusedItem.set(focusedItem);\n            }\n\n            event.preventDefault();\n        }\n    }\n\n    onArrowRightKey(event) {\n        if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n            const grouped = this.isItemGroup(this.focusedItem());\n\n            if (grouped) {\n                const matched = this.activeItemPath().some((p) => p.key === this.focusedItem().key);\n\n                if (matched) {\n                    this.onArrowDownKey(event);\n                } else {\n                    const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItem().parentKey);\n                    activeItemPath.push(this.focusedItem());\n\n                    this.activeItemPath.set(activeItemPath);\n                }\n            }\n\n            event.preventDefault();\n        }\n    }\n\n    onHomeKey(event) {\n        this.changeFocusedItem({ originalEvent: event, processedItem: this.findFirstItem(), allowHeaderFocus: false });\n        event.preventDefault();\n    }\n\n    onEndKey(event) {\n        this.changeFocusedItem({ originalEvent: event, processedItem: this.findLastItem(), focusOnNext: true, allowHeaderFocus: false });\n        event.preventDefault();\n    }\n\n    onEnterKey(event) {\n        if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n            const element = DomHandler.findSingle(this.subMenuViewChild.listViewChild.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n            const anchorElement = element && (DomHandler.findSingle(element, '[data-pc-section=\"action\"]') || DomHandler.findSingle(element, 'a,button'));\n\n            anchorElement ? anchorElement.click() : element && element.click();\n        }\n\n        event.preventDefault();\n    }\n\n    onSpaceKey(event) {\n        this.onEnterKey(event);\n    }\n\n    findNextItem(processedItem) {\n        const index = this.visibleItems().findIndex((item) => item.key === processedItem.key);\n\n        const matchedItem =\n            index < this.visibleItems().length - 1\n                ? this.visibleItems()\n                      .slice(index + 1)\n                      .find((pItem) => this.isValidItem(pItem))\n                : undefined;\n        return matchedItem || processedItem;\n    }\n\n    findPrevItem(processedItem) {\n        const index = this.visibleItems().findIndex((item) => item.key === processedItem.key);\n        const matchedItem = index > 0 ? ObjectUtils.findLast(this.visibleItems().slice(0, index), (pItem) => this.isValidItem(pItem)) : undefined;\n\n        return matchedItem || processedItem;\n    }\n\n    searchItems(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let matchedItem = null;\n        let matched = false;\n\n        if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n            const focusedItemIndex = this.visibleItems().findIndex((processedItem) => processedItem.key === this.focusedItem().key);\n\n            matchedItem = this.visibleItems()\n                .slice(focusedItemIndex)\n                .find((processedItem) => this.isItemMatched(processedItem));\n            matchedItem = ObjectUtils.isEmpty(matchedItem)\n                ? this.visibleItems()\n                      .slice(0, focusedItemIndex)\n                      .find((processedItem) => this.isItemMatched(processedItem))\n                : matchedItem;\n        } else {\n            matchedItem = this.visibleItems().find((processedItem) => this.isItemMatched(processedItem));\n        }\n\n        if (ObjectUtils.isNotEmpty(matchedItem)) {\n            matched = true;\n        }\n\n        if (ObjectUtils.isEmpty(matchedItem) && ObjectUtils.isEmpty(this.focusedItem())) {\n            matchedItem = this.findFirstItem();\n        }\n\n        if (ObjectUtils.isNotEmpty(matchedItem)) {\n            this.changeFocusedItem({\n                originalEvent: event,\n                processedItem: matchedItem,\n                allowHeaderFocus: false\n            });\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n}\n\n/**\n * PanelMenu is a hybrid of Accordion and Tree components.\n * @group Components\n */\n@Component({\n    selector: 'p-panelMenu',\n    template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-panelmenu p-component'\" #container>\n            <ng-container *ngFor=\"let item of model; let f = first; let l = last; let i = index\">\n                <div *ngIf=\"isItemVisible(item)\" class=\"p-panelmenu-panel\" [ngClass]=\"getItemProp(item, 'headerClass')\" [ngStyle]=\"getItemProp(item, 'style')\" [attr.data-pc-section]=\"'panel'\">\n                    <div\n                        [ngClass]=\"{ 'p-component p-panelmenu-header': true, 'p-highlight': isItemActive(item), 'p-disabled': isItemDisabled(item) }\"\n                        [class]=\"getItemProp(item, 'styleClass')\"\n                        [ngStyle]=\"getItemProp(item, 'style')\"\n                        [pTooltip]=\"getItemProp(item, 'tooltip')\"\n                        [attr.id]=\"getHeaderId(item, i)\"\n                        [tabindex]=\"0\"\n                        role=\"button\"\n                        [tooltipOptions]=\"getItemProp(item, 'tooltipOptions')\"\n                        [attr.aria-expanded]=\"isItemActive(item)\"\n                        [attr.aria-label]=\"getItemProp(item, 'label')\"\n                        [attr.aria-controls]=\"getContentId(item, i)\"\n                        [attr.aria-disabled]=\"isItemDisabled(item)\"\n                        [attr.data-p-highlight]=\"isItemActive(item)\"\n                        [attr.data-p-disabled]=\"isItemDisabled(item)\"\n                        [attr.data-pc-section]=\"'header'\"\n                        (click)=\"onHeaderClick($event, item, i)\"\n                        (keydown)=\"onHeaderKeyDown($event, item, i)\"\n                    >\n                        <div class=\"p-panelmenu-header-content\">\n                            <ng-container *ngIf=\"!itemTemplate\">\n                                <a\n                                    *ngIf=\"!getItemProp(item, 'routerLink')\"\n                                    [attr.href]=\"getItemProp(item, 'url')\"\n                                    [attr.tabindex]=\"-1\"\n                                    [target]=\"getItemProp(item, 'target')\"\n                                    [attr.title]=\"getItemProp(item, 'title')\"\n                                    class=\"p-panelmenu-header-action\"\n                                    [attr.data-pc-section]=\"'headeraction'\"\n                                >\n                                    <ng-container *ngIf=\"isItemGroup(item)\">\n                                        <ng-container *ngIf=\"!submenuIconTemplate\">\n                                            <ChevronDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"isItemActive(item)\" />\n                                            <ChevronRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!isItemActive(item)\" />\n                                        </ng-container>\n                                        <ng-template *ngTemplateOutlet=\"submenuIconTemplate\"></ng-template>\n                                    </ng-container>\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"getItemProp(item, 'iconStyle')\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(item, 'escape') !== false; else htmlLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(item, 'badge')\" [ngClass]=\"getItemProp(item, 'badgeStyleClass')\">{{ getItemProp(item, 'badge') }}</span>\n                                </a>\n                            </ng-container>\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            <a\n                                *ngIf=\"getItemProp(item, 'routerLink')\"\n                                [routerLink]=\"getItemProp(item, 'routerLink')\"\n                                [queryParams]=\"getItemProp(item, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(item, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(item, 'target')\"\n                                class=\"p-panelmenu-header-action\"\n                                [attr.tabindex]=\"-1\"\n                                [fragment]=\"getItemProp(item, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(item, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(item, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(item, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(item, 'replaceUrl')\"\n                                [state]=\"getItemProp(item, 'state')\"\n                                [attr.data-pc-section]=\"'headeraction'\"\n                            >\n                                <ng-container *ngIf=\"isItemGroup(item)\">\n                                    <ng-container *ngIf=\"!submenuIconTemplate\">\n                                        <ChevronDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"isItemActive(item)\" />\n                                        <ChevronRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!isItemActive(item)\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"submenuIconTemplate\"></ng-template>\n                                </ng-container>\n                                <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"getItemProp(item, 'iconStyle')\"></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(item, 'escape') !== false; else htmlRouteLabel\">{{ getItemProp(item, 'label') }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(item, 'badge')\" [ngClass]=\"getItemProp(item, 'badgeStyleClass')\">{{ getItemProp(item, 'badge') }}</span>\n                            </a>\n                        </div>\n                    </div>\n                    <div\n                        *ngIf=\"isItemGroup(item)\"\n                        class=\"p-toggleable-content\"\n                        [ngClass]=\"{ 'p-panelmenu-expanded': isItemActive(item) }\"\n                        [@rootItem]=\"getAnimation(item)\"\n                        (@rootItem.done)=\"onToggleDone()\"\n                        role=\"region\"\n                        [attr.id]=\"getContentId(item, i)\"\n                        [attr.aria-labelledby]=\"getHeaderId(item, i)\"\n                        [attr.data-pc-section]=\"'toggleablecontent'\"\n                    >\n                        <div class=\"p-panelmenu-content\" [attr.data-pc-section]=\"'menucontent'\">\n                            <p-panelMenuList\n                                [panelId]=\"getPanelId(i, item)\"\n                                [items]=\"getItemProp(item, 'items')\"\n                                [itemTemplate]=\"itemTemplate\"\n                                [transitionOptions]=\"transitionOptions\"\n                                [root]=\"true\"\n                                [activeItem]=\"activeItem()\"\n                                [tabindex]=\"tabindex\"\n                                [parentExpanded]=\"isItemActive(item)\"\n                                (headerFocus)=\"updateFocusedHeader($event)\"\n                            ></p-panelMenuList>\n                        </div>\n                    </div>\n                </div>\n            </ng-container>\n        </div>\n    `,\n    animations: [\n        trigger('rootItem', [\n            state(\n                'hidden',\n                style({\n                    height: '0'\n                })\n            ),\n            state(\n                'visible',\n                style({\n                    height: '*'\n                })\n            ),\n            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./panelmenu.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class PanelMenu implements AfterContentInit {\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    @Input() model: MenuItem[] | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Whether multiple tabs can be activated at the same time or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) multiple: boolean = false;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    @Input() transitionOptions: string = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined = 0;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @ViewChild('container') containerViewChild: ElementRef | undefined;\n\n    submenuIconTemplate: TemplateRef<any> | undefined;\n\n    itemTemplate: TemplateRef<any> | undefined;\n\n    public animating: boolean | undefined;\n\n    activeItem = signal<any>(null);\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    constructor(private cd: ChangeDetectorRef) {}\n\n    /**\n     * Collapses open panels.\n     * @group Method\n     */\n    collapseAll() {\n        for (let item of this.model!) {\n            if (item.expanded) {\n                item.expanded = false;\n            }\n        }\n\n        this.cd.detectChanges();\n    }\n\n    onToggleDone() {\n        this.animating = false;\n        this.cd.markForCheck();\n    }\n\n    changeActiveItem(event, item, index?: number, selfActive = false) {\n        if (!this.isItemDisabled(item)) {\n            const activeItem = selfActive ? item : this.activeItem && ObjectUtils.equals(item, this.activeItem) ? null : item;\n            this.activeItem.set(activeItem);\n        }\n    }\n\n    getAnimation(item: MenuItem) {\n        return item.expanded ? { value: 'visible', params: { transitionParams: this.animating ? this.transitionOptions : '0ms', height: '*' } } : { value: 'hidden', params: { transitionParams: this.transitionOptions, height: '0' } };\n    }\n\n    getItemProp(item, name) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n\n    getItemLabel(item) {\n        return this.getItemProp(item, 'label');\n    }\n\n    isItemActive(item) {\n        return item.expanded;\n    }\n\n    isItemVisible(item) {\n        return this.getItemProp(item, 'visible') !== false;\n    }\n\n    isItemDisabled(item) {\n        return this.getItemProp(item, 'disabled');\n    }\n\n    isItemGroup(item) {\n        return ObjectUtils.isNotEmpty(item.items);\n    }\n\n    getPanelId(index, item?) {\n        return item && item.id ? item.id : `${this.id}_${index}`;\n    }\n\n    getHeaderId(item, index) {\n        return item.id ? item.id + '_header' : `${this.getPanelId(index)}_header`;\n    }\n\n    getContentId(item, index) {\n        return item.id ? item.id + '_content' : `${this.getPanelId(index)}_content`;\n    }\n\n    updateFocusedHeader(event) {\n        const { originalEvent, focusOnNext, selfCheck } = event;\n        const panelElement = originalEvent.currentTarget.closest('[data-pc-section=\"panel\"]');\n        const header = selfCheck ? DomHandler.findSingle(panelElement, '[data-pc-section=\"header\"]') : focusOnNext ? this.findNextHeader(panelElement) : this.findPrevHeader(panelElement);\n\n        header ? this.changeFocusedHeader(originalEvent, header) : focusOnNext ? this.onHeaderHomeKey(originalEvent) : this.onHeaderEndKey(originalEvent);\n    }\n\n    changeFocusedHeader(event, element) {\n        element && DomHandler.focus(element);\n    }\n\n    findNextHeader(panelElement, selfCheck = false) {\n        const nextPanelElement = selfCheck ? panelElement : panelElement.nextElementSibling;\n        const headerElement = DomHandler.findSingle(nextPanelElement, '[data-pc-section=\"header\"]');\n\n        return headerElement ? (DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeader(headerElement.parentElement) : headerElement) : null;\n    }\n\n    findPrevHeader(panelElement, selfCheck = false) {\n        const prevPanelElement = selfCheck ? panelElement : panelElement.previousElementSibling;\n        const headerElement = DomHandler.findSingle(prevPanelElement, '[data-pc-section=\"header\"]');\n\n        return headerElement ? (DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeader(headerElement.parentElement) : headerElement) : null;\n    }\n\n    findFirstHeader() {\n        return this.findNextHeader(this.containerViewChild.nativeElement.firstElementChild, true);\n    }\n\n    findLastHeader() {\n        return this.findPrevHeader(this.containerViewChild.nativeElement.lastElementChild, true);\n    }\n\n    onHeaderClick(event, item, index) {\n        if (this.isItemDisabled(item)) {\n            event.preventDefault();\n\n            return;\n        }\n\n        if (item.command) {\n            item.command({ originalEvent: event, item });\n        }\n\n        if (!this.multiple) {\n            for (let modelItem of this.model!) {\n                if (item !== modelItem && modelItem.expanded) {\n                    modelItem.expanded = false;\n                }\n            }\n        }\n\n        item.expanded = !item.expanded;\n        this.changeActiveItem(event, item, index);\n        this.animating = true;\n        DomHandler.focus(event.currentTarget as HTMLElement);\n    }\n\n    onHeaderKeyDown(event, item, index) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onHeaderArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onHeaderArrowUpKey(event);\n                break;\n\n            case 'Home':\n                this.onHeaderHomeKey(event);\n                break;\n\n            case 'End':\n                this.onHeaderEndKey(event);\n                break;\n\n            case 'Enter':\n            case 'Space':\n                this.onHeaderEnterKey(event, item, index);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onHeaderArrowDownKey(event) {\n        const rootList = DomHandler.getAttribute(event.currentTarget, 'data-p-highlight') === true ? DomHandler.findSingle(event.currentTarget.nextElementSibling, '[data-pc-section=\"menu\"]') : null;\n\n        rootList ? DomHandler.focus(rootList) : this.updateFocusedHeader({ originalEvent: event, focusOnNext: true });\n        event.preventDefault();\n    }\n\n    onHeaderArrowUpKey(event) {\n        const prevHeader = this.findPrevHeader(event.currentTarget.parentElement) || this.findLastHeader();\n        const rootList = DomHandler.getAttribute(prevHeader, 'data-p-highlight') === true ? DomHandler.findSingle(prevHeader.nextElementSibling, '[data-pc-section=\"menu\"]') : null;\n\n        rootList ? DomHandler.focus(rootList) : this.updateFocusedHeader({ originalEvent: event, focusOnNext: false });\n        event.preventDefault();\n    }\n\n    onHeaderHomeKey(event) {\n        this.changeFocusedHeader(event, this.findFirstHeader());\n        event.preventDefault();\n    }\n\n    onHeaderEndKey(event) {\n        this.changeFocusedHeader(event, this.findLastHeader());\n        event.preventDefault();\n    }\n\n    onHeaderEnterKey(event, item, index) {\n        const headerAction = DomHandler.findSingle(event.currentTarget, '[data-pc-section=\"headeraction\"]');\n\n        headerAction ? headerAction.click() : this.onHeaderClick(event, item, index);\n        event.preventDefault();\n    }\n}\n@NgModule({\n    imports: [CommonModule, RouterModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon, ChevronDownIcon, ChevronRightIcon],\n    exports: [PanelMenu, RouterModule, TooltipModule, SharedModule],\n    declarations: [PanelMenu, PanelMenuSub, PanelMenuList]\n})\nexport class PanelMenuModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;MA4Ka,YAAY,CAAA;AA+BmC,IAAA,SAAA,CAAA;AAA6B,IAAA,EAAA,CAAA;AA9B5E,IAAA,OAAO,CAAqB;AAE5B,IAAA,aAAa,CAAqB;AAElC,IAAA,KAAK,CAAQ;AAEb,IAAA,YAAY,CAA0B;IAER,KAAK,GAAW,CAAC,CAAC;AAEhD,IAAA,cAAc,CAAQ;AAES,IAAA,IAAI,CAAsB;AAE3B,IAAA,QAAQ,CAAqB;AAE3D,IAAA,iBAAiB,CAAqB;AAEP,IAAA,cAAc,CAAsB;AAElE,IAAA,UAAU,GAAsB,IAAI,YAAY,EAAO,CAAC;AAExD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAO,CAAC;AAEvD,IAAA,QAAQ,GAAsB,IAAI,YAAY,EAAO,CAAC;AAEtD,IAAA,WAAW,GAAsB,IAAI,YAAY,EAAO,CAAC;AAEhD,IAAA,aAAa,CAAa;IAE7C,WAAwD,CAAA,SAAoB,EAAS,EAAc,EAAA;QAA3C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;AAEvG,IAAA,SAAS,CAAC,aAAa,EAAA;AACnB,QAAA,OAAO,aAAa,CAAC,IAAI,EAAE,EAAE,IAAI,CAAA,EAAG,IAAI,CAAC,OAAO,CAAI,CAAA,EAAA,aAAa,CAAC,GAAG,EAAE,CAAC;KAC3E;AAED,IAAA,UAAU,CAAC,aAAa,EAAA;AACpB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KACxC;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,OAAO;AACH,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;SACnD,CAAC;KACL;AAED,IAAA,WAAW,CAAC,aAAa,EAAE,IAAK,EAAE,MAAO,EAAA;QACrC,OAAO,aAAa,IAAI,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC;KACvH;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;KACnD;AAED,IAAA,cAAc,CAAC,aAAa,EAAA;QACxB,OAAO,aAAa,CAAC,QAAQ,CAAC;KACjC;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;KAC3H;AAED,IAAA,aAAa,CAAC,aAAa,EAAA;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK,CAAC;KAC/D;AAED,IAAA,cAAc,CAAC,aAAa,EAAA;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KACtD;AAED,IAAA,aAAa,CAAC,aAAa,EAAA;QACvB,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KAC/D;AAED,IAAA,WAAW,CAAC,aAAa,EAAA;QACrB,OAAO,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACtD;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;KAC9N;IAED,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;KAC1I;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;AACjB,QAAA,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACrK;IAED,WAAW,CAAC,KAAK,EAAE,aAAa,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE;AACrC,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/F,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AACxF,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC/B;AArGQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBA+BD,UAAU,CAAC,MAAM,SAAS,CAAC,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA/BtC,YAAY,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,eAAA,EAAA,KAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EASD,eAAe,CAIf,EAAA,cAAA,EAAA,gBAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,sCAEhB,eAAe,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAIf,gBAAgB,CAzJ1B,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8GT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAo8BkE,aAAa,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,cAAc,CAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA56BvF,YAAY,CAvBT,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,eAAA,EAAA,OAAA,EAAA,cAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,SAAS,EAAE;AACf,gBAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACd,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACd,iBAAA,CAAC,CACL;gBACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACnE,gBAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;aACtC,CAAC;AACL,SAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAMQ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAxIxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8GT,IAAA,CAAA;AACD,oBAAA,UAAU,EAAE;wBACR,OAAO,CAAC,SAAS,EAAE;AACf,4BAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACd,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACd,6BAAA,CAAC,CACL;4BACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACnE,4BAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;yBACtC,CAAC;AACL,qBAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAgCgB,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,SAAS,CAAC,CAAA;kEA9BtC,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAEG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAEG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAEkC,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE5B,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAEG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAEY,aAAa,EAAA,CAAA;sBAA/B,SAAS;uBAAC,MAAM,CAAA;;MAuGR,aAAa,CAAA;AAiDF,IAAA,EAAA,CAAA;AAhDX,IAAA,OAAO,CAAqB;AAE5B,IAAA,EAAE,CAAqB;AAEvB,IAAA,KAAK,CAAQ;AAEb,IAAA,YAAY,CAA0B;AAEP,IAAA,cAAc,CAAsB;AAEpC,IAAA,QAAQ,CAAsB;AAE7D,IAAA,iBAAiB,CAAqB;AAEP,IAAA,IAAI,CAAsB;AAE3B,IAAA,QAAQ,CAAqB;AAE3D,IAAA,UAAU,CAAM;AAEf,IAAA,UAAU,GAAsB,IAAI,YAAY,EAAO,CAAC;AAExD,IAAA,WAAW,GAAsB,IAAI,YAAY,EAAO,CAAC;AAE7C,IAAA,gBAAgB,CAAe;AAErD,IAAA,aAAa,CAAM;AAEnB,IAAA,WAAW,CAAM;AAEjB,IAAA,OAAO,CAAsB;AAE7B,IAAA,WAAW,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAEhC,IAAA,cAAc,GAAG,MAAM,CAAQ,EAAE,CAAC,CAAC;AAEnC,IAAA,cAAc,GAAG,MAAM,CAAQ,EAAE,CAAC,CAAC;AAEnC,IAAA,YAAY,GAAG,QAAQ,CAAC,MAAK;AACzB,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAC7C,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AAC1C,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,aAAa,GAAA;AACb,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACvC,OAAO,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,OAAO,CAAA,CAAA,EAAI,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;KAC3K;AAED,IAAA,WAAA,CAAoB,EAAc,EAAA;QAAd,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;AAEtC,IAAA,WAAW,CAAC,OAAsB,EAAA;QAC9B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;KACxG;IAED,WAAW,CAAC,aAAa,EAAE,IAAI,EAAA;QAC3B,OAAO,aAAa,IAAI,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;KAC/G;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;KACnD;AAED,IAAA,aAAa,CAAC,aAAa,EAAA;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK,CAAC;KAC/D;AAED,IAAA,cAAc,CAAC,aAAa,EAAA;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KACtD;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC,SAAS,CAAC,CAAC;KACrF;AAED,IAAA,WAAW,CAAC,aAAa,EAAA;QACrB,OAAO,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACtD;IAED,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAA;QAC3B,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAEvE,OAAO,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;KAC3C;AAED,IAAA,aAAa,CAAC,aAAa,EAAA;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,iBAAiB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC;KACnJ;AAED,IAAA,aAAa,CAAC,aAAa,EAAA;QACvB,OAAO,CAAC,CAAC,aAAa,KAAK,aAAa,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;KAClI;AAED,IAAA,WAAW,CAAC,aAAa,EAAA;AACrB,QAAA,OAAO,CAAC,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;KAC7F;IAED,aAAa,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KACvF;IAED,YAAY,GAAA;QACR,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KACxG;AAED,IAAA,qBAAqB,CAAC,MAAmB,EAAA;QACrC,IAAI,UAAU,GAAG,MAA8B,CAAC;QAEhD,OAAO,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,IAAI,EAAE;AAC7D,YAAA,UAAU,GAAG,UAAU,EAAE,UAAqB,CAAC;AAClD,SAAA;AAED,QAAA,OAAO,UAAU,EAAE,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAG,EAAA,IAAI,CAAC,OAAO,CAAA,CAAA,EAAI,aAAa,CAAC,GAAG,CAAE,CAAA,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC;KACrK;AAED,IAAA,oBAAoB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAA;QAC9D,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,KAAK;YACD,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AAC1B,gBAAA,MAAM,GAAG,GAAG,CAAC,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,GAAG,EAAE,IAAI,KAAK,CAAC;AAC9D,gBAAA,MAAM,OAAO,GAAG;oBACZ,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,GAAG;oBACH,MAAM;oBACN,SAAS;iBACZ,CAAC;gBAEF,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AAClF,gBAAA,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;AACP,QAAA,OAAO,cAAc,CAAC;KACzB;AAED,IAAA,0BAA0B,CAAC,GAAG,EAAE,cAAe,EAAE,KAAK,GAAG,CAAC,EAAA;AACtD,QAAA,cAAc,GAAG,cAAc,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;AACzD,QAAA,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE;AACzC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,gBAAA,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAExC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,CAAC,KAAK,GAAG;AAAE,oBAAA,OAAO,aAAa,CAAC;AACzE,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,EAAE,aAAa,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AACzF,gBAAA,IAAI,WAAW;AAAE,oBAAA,OAAO,WAAW,CAAC;AACvC,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,cAAc,EAAE,qBAAqB,GAAG,EAAE,EAAA;QAChD,cAAc;AACV,YAAA,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,KAAI;AACrC,gBAAA,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;AACnC,oBAAA,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC1C,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;AAC9D,iBAAA;AACL,aAAC,CAAC,CAAC;AAEP,QAAA,OAAO,qBAAqB,CAAC;KAChC;AAED,IAAA,iBAAiB,CAAC,KAAK,EAAA;AACnB,QAAA,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,EAAE,gBAAgB,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;QAEhG,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE;AAC5F,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACpC,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;AAAM,aAAA,IAAI,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC;AACpE,SAAA;KACJ;IAED,YAAY,GAAA;QACR,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,aAAa,EAAE,UAAU,CAAG,EAAA,IAAI,CAAC,aAAa,CAAA,CAAE,CAAI,EAAA,CAAA,CAAC,CAAC;AAEhI,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,KAAK,EAAA;AACT,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACf,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;AACvL,YAAA,IAAI,KAAK,CAAC,aAAa,KAAK,IAAI;AAAE,gBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACvE,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAK,EAAA;AACR,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC;AAEnC,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACzD,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACzB,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;AAC1C,QAAA,aAAa,CAAC,QAAQ,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC;QAEjD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,aAAa,CAAC,SAAS,CAAC,CAAC;AACpG,QAAA,QAAQ,IAAI,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAE/C,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,aAAa,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnG,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;KACvC;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;QACX,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QAE/C,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,KAAK,CAAC;AACX,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;;gBAEb,MAAM;AAEV,YAAA;gBACI,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;AAChB,QAAA,MAAM,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AAChI,QAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;QACnF,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AACD,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,MAAM,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAE/H,QAAA,IAAI,CAAC,iBAAiB,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACjF,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;YAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;AAEpF,YAAA,IAAI,OAAO,EAAE;gBACT,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;AAC7F,gBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC3C,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACvH,gBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACrC,aAAA;YAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;YAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAErD,YAAA,IAAI,OAAO,EAAE;gBACT,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;AAEpF,gBAAA,IAAI,OAAO,EAAE;AACT,oBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,iBAAA;AAAM,qBAAA;oBACH,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC;oBACzG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAExC,oBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC3C,iBAAA;AACJ,aAAA;YAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;QACX,IAAI,CAAC,iBAAiB,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/G,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;QACV,IAAI,CAAC,iBAAiB,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,CAAC;QACjI,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;QACZ,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;YAC5C,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,aAAa,EAAE,UAAU,CAAG,EAAA,IAAI,CAAC,aAAa,CAAA,CAAE,CAAI,EAAA,CAAA,CAAC,CAAC;YAChI,MAAM,aAAa,GAAG,OAAO,KAAK,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,4BAA4B,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;AAE9I,YAAA,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;AACtE,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;QAEtF,MAAM,WAAW,GACb,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,GAAG,CAAC;AAClC,cAAE,IAAI,CAAC,YAAY,EAAE;AACd,iBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChB,iBAAA,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;cAC7C,SAAS,CAAC;QACpB,OAAO,WAAW,IAAI,aAAa,CAAC;KACvC;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;AACtF,QAAA,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,CAAC;QAE1I,OAAO,WAAW,IAAI,aAAa,CAAC;KACvC;IAED,WAAW,CAAC,KAAK,EAAE,IAAI,EAAA;AACnB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;QAEnD,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;YAC5C,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,aAAa,CAAC,GAAG,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC;AAExH,YAAA,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE;iBAC5B,KAAK,CAAC,gBAAgB,CAAC;AACvB,iBAAA,IAAI,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;AAChE,YAAA,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;AAC1C,kBAAE,IAAI,CAAC,YAAY,EAAE;AACd,qBAAA,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC;AAC1B,qBAAA,IAAI,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;kBAC/D,WAAW,CAAC;AACrB,SAAA;AAAM,aAAA;YACH,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;AAChG,SAAA;AAED,QAAA,IAAI,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YACrC,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;AAC7E,YAAA,WAAW,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AACtC,SAAA;AAED,QAAA,IAAI,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YACrC,IAAI,CAAC,iBAAiB,CAAC;AACnB,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,aAAa,EAAE,WAAW;AAC1B,gBAAA,gBAAgB,EAAE,KAAK;AAC1B,aAAA,CAAC,CAAC;AACN,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;uGAvZQ,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAb,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EASF,gBAAgB,CAEhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,kEAIhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAEhB,eAAe,CA3CzB,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;AAkBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,8uBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EA5HQ,YAAY,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,eAAA,EAAA,OAAA,EAAA,cAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAoIZ,aAAa,EAAA,UAAA,EAAA,CAAA;kBA5BzB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EACjB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;AAkBT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,8uBAAA,CAAA,EAAA,CAAA;+EAGQ,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAEG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAEG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEkC,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAEkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAEI,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAEG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAEe,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;;AAiYxB;;;AAGG;MAwIU,SAAS,CAAA;AAuEE,IAAA,EAAA,CAAA;AAtEpB;;;AAGG;AACM,IAAA,KAAK,CAAyB;AACvC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACM,iBAAiB,GAAW,sCAAsC,CAAC;AAC5E;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;IACoC,QAAQ,GAAuB,CAAC,CAAC;AAExC,IAAA,SAAS,CAAuC;AAExD,IAAA,kBAAkB,CAAyB;AAEnE,IAAA,mBAAmB,CAA+B;AAElD,IAAA,YAAY,CAA+B;AAEpC,IAAA,SAAS,CAAsB;AAEtC,IAAA,UAAU,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;IAE/B,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;KAC5C;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,WAAA,CAAoB,EAAqB,EAAA;QAArB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;AAE7C;;;AAGG;IACH,WAAW,GAAA;AACP,QAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAM,EAAE;YAC1B,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,gBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACzB,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,YAAY,GAAA;AACR,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,KAAc,EAAE,UAAU,GAAG,KAAK,EAAA;AAC5D,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;AAC5B,YAAA,MAAM,UAAU,GAAG,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;AAClH,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACnC,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,IAAc,EAAA;QACvB,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,GAAG,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;KACpO;IAED,WAAW,CAAC,IAAI,EAAE,IAAI,EAAA;AAClB,QAAA,OAAO,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;KAClE;AAED,IAAA,YAAY,CAAC,IAAI,EAAA;QACb,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC1C;AAED,IAAA,YAAY,CAAC,IAAI,EAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;AAED,IAAA,aAAa,CAAC,IAAI,EAAA;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,KAAK,CAAC;KACtD;AAED,IAAA,cAAc,CAAC,IAAI,EAAA;QACf,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC7C;AAED,IAAA,WAAW,CAAC,IAAI,EAAA;QACZ,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC7C;IAED,UAAU,CAAC,KAAK,EAAE,IAAK,EAAA;QACnB,OAAO,IAAI,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAA,CAAE,CAAC;KAC5D;IAED,WAAW,CAAC,IAAI,EAAE,KAAK,EAAA;QACnB,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,SAAS,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA,OAAA,CAAS,CAAC;KAC7E;IAED,YAAY,CAAC,IAAI,EAAE,KAAK,EAAA;QACpB,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA,QAAA,CAAU,CAAC;KAC/E;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;QACrB,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QACxD,MAAM,YAAY,GAAG,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACtF,QAAA,MAAM,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,YAAY,EAAE,4BAA4B,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAEnL,QAAA,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,MAAM,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;KACrJ;IAED,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAA;AAC9B,QAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KACxC;AAED,IAAA,cAAc,CAAC,YAAY,EAAE,SAAS,GAAG,KAAK,EAAA;AAC1C,QAAA,MAAM,gBAAgB,GAAG,SAAS,GAAG,YAAY,GAAG,YAAY,CAAC,kBAAkB,CAAC;QACpF,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE,4BAA4B,CAAC,CAAC;AAE5F,QAAA,OAAO,aAAa,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa,IAAI,IAAI,CAAC;KAChK;AAED,IAAA,cAAc,CAAC,YAAY,EAAE,SAAS,GAAG,KAAK,EAAA;AAC1C,QAAA,MAAM,gBAAgB,GAAG,SAAS,GAAG,YAAY,GAAG,YAAY,CAAC,sBAAsB,CAAC;QACxF,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE,4BAA4B,CAAC,CAAC;AAE5F,QAAA,OAAO,aAAa,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa,IAAI,IAAI,CAAC;KAChK;IAED,eAAe,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;KAC7F;IAED,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;KAC5F;AAED,IAAA,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC3B,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAChD,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,KAAM,EAAE;AAC/B,gBAAA,IAAI,IAAI,KAAK,SAAS,IAAI,SAAS,CAAC,QAAQ,EAAE;AAC1C,oBAAA,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,aAA4B,CAAC,CAAC;KACxD;AAED,IAAA,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAA;QAC9B,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBAC/B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;gBACR,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC1C,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,KAAK,EAAA;AACtB,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,aAAa,EAAE,kBAAkB,CAAC,KAAK,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,kBAAkB,EAAE,0BAA0B,CAAC,GAAG,IAAI,CAAC;QAE9L,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9G,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,kBAAkB,CAAC,KAAK,EAAA;AACpB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;AACnG,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,kBAAkB,CAAC,KAAK,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,kBAAkB,EAAE,0BAA0B,CAAC,GAAG,IAAI,CAAC;QAE5K,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/G,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QACxD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACvD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAA;AAC/B,QAAA,MAAM,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,EAAE,kCAAkC,CAAC,CAAC;QAEpG,YAAY,GAAG,YAAY,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC7E,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;uGAhQQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,kIAoBE,gBAAgB,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAehB,eAAe,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAElB,aAAa,EA1KpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2GT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,8uBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA6RiG,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,gBAAgB,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAxyB1H,aAAa,CA4gBV,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,IAAA,EAAA,OAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,UAAU,EAAE;AAChB,gBAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACd,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACd,iBAAA,CAAC,CACL;gBACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACnE,gBAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;aACtC,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,SAAS,EAAA,UAAA,EAAA,CAAA;kBAvIrB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACb,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2GT,EACW,UAAA,EAAA;wBACR,OAAO,CAAC,UAAU,EAAE;AAChB,4BAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACd,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACd,6BAAA,CAAC,CACL;4BACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACnE,4BAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;yBACtC,CAAC;AACL,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,8uBAAA,CAAA,EAAA,CAAA;sFAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEL,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEN,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;;MAgOb,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAvQf,YAAA,EAAA,CAAA,SAAS,EAzqBT,YAAY,EAoIZ,aAAa,CAwyBZ,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,CAnQ1H,EAAA,OAAA,EAAA,CAAA,SAAS,EAoQG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGrD,eAAe,EAAA,OAAA,EAAA,CAJd,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAC9G,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGrD,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,CAAC;oBACpI,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;AAC/D,oBAAA,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,aAAa,CAAC;AACzD,iBAAA,CAAA;;;AC3lCD;;AAEG;;;;"}