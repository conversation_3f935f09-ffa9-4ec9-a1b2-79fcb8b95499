{"version": 3, "file": "primeng-cascadeselect.mjs", "sources": ["../../src/app/components/cascadeselect/cascadeselect.ts", "../../src/app/components/cascadeselect/primeng-cascadeselect.ts"], "sourcesContent": ["import { AnimationEvent } from '@angular/animations';\nimport { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    booleanAttribute,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    computed,\n    ContentChildren,\n    effect,\n    ElementRef,\n    EventEmitter,\n    forwardRef,\n    Inject,\n    Input,\n    NgModule,\n    numberAttribute,\n    OnInit,\n    Output,\n    QueryList,\n    signal,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation\n} from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { OverlayOptions, OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule, TranslationKeys } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { AutoFocusModule } from 'primeng/autofocus';\n\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { Overlay, OverlayModule } from 'primeng/overlay';\nimport { RippleModule } from 'primeng/ripple';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { CascadeSelectBeforeHideEvent, CascadeSelectBeforeShowEvent, CascadeSelectChangeEvent, CascadeSelectHideEvent, CascadeSelectShowEvent } from './cascadeselect.interface';\n\nexport const CASCADESELECT_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => CascadeSelect),\n    multi: true\n};\n\n@Component({\n    selector: 'p-cascadeSelectSub',\n    template: `\n        <ul\n            class=\"p-cascadeselect-panel p-cascadeselect-items\"\n            [ngClass]=\"{ 'p-cascadeselect-panel-root': root }\"\n            [attr.role]=\"role\"\n            aria-orientation=\"horizontal\"\n            [attr.data-pc-section]=\"level === 0 ? 'list' : 'sublist'\"\n            [attr.aria-label]=\"listLabel\"\n        >\n            <ng-template ngFor let-processedOption [ngForOf]=\"options\" let-i=\"index\">\n                <li\n                    [ngClass]=\"getItemClass(processedOption)\"\n                    role=\"treeitem\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"options.length\"\n                    [attr.data-pc-section]=\"'item'\"\n                    [id]=\"getOptionId(processedOption)\"\n                    [attr.aria-label]=\"getOptionLabelToRender(processedOption)\"\n                    [attr.aria-selected]=\"isOptionGroup(processedOption) ? undefined : isOptionSelected(processedOption)\"\n                    [attr.aria-posinset]=\"i + 1\"\n                >\n                    <div class=\"p-cascadeselect-item-content\" (click)=\"onOptionClick($event, processedOption)\" [attr.tabindex]=\"0\" pRipple [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngIf=\"optionTemplate; else defaultOptionTemplate\">\n                            <ng-container *ngTemplateOutlet=\"optionTemplate; context: { $implicit: processedOption.option }\"></ng-container>\n                        </ng-container>\n                        <ng-template #defaultOptionTemplate>\n                            <span class=\"p-cascadeselect-item-text\" [attr.data-pc-section]=\"'text'\">{{ getOptionLabelToRender(processedOption) }}</span>\n                        </ng-template>\n                        <span class=\"p-cascadeselect-group-icon\" *ngIf=\"isOptionGroup(processedOption)\" [attr.data-pc-section]=\"'groupIcon'\">\n                            <AngleRightIcon *ngIf=\"!groupIconTemplate\" />\n                            <ng-template *ngTemplateOutlet=\"groupIconTemplate\"></ng-template>\n                        </span>\n                    </div>\n                    <p-cascadeSelectSub\n                        *ngIf=\"isOptionGroup(processedOption) && isOptionActive(processedOption)\"\n                        [role]=\"'group'\"\n                        class=\"p-cascadeselect-sublist\"\n                        [selectId]=\"selectId\"\n                        [focusedOptionId]=\"focusedOptionId\"\n                        [activeOptionPath]=\"activeOptionPath\"\n                        [options]=\"getOptionGroupChildren(processedOption)\"\n                        [optionLabel]=\"optionLabel\"\n                        [optionValue]=\"optionValue\"\n                        [level]=\"level + 1\"\n                        (onChange)=\"onOptionChange($event)\"\n                        [optionGroupLabel]=\"optionGroupLabel\"\n                        [optionGroupChildren]=\"optionGroupChildren\"\n                        [dirty]=\"dirty\"\n                        [optionTemplate]=\"optionTemplate\"\n                    >\n                    </p-cascadeSelectSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class CascadeSelectSub implements OnInit {\n    @Input() role: string | undefined;\n\n    @Input() selectId: string | undefined;\n\n    @Input() activeOptionPath: any[];\n\n    @Input() optionDisabled: any[];\n\n    @Input() focusedOptionId: string | undefined;\n\n    @Input() options: string[] | string | undefined | null;\n\n    @Input() optionGroupChildren: string[] | string | undefined | null;\n\n    @Input() optionTemplate: Nullable<TemplateRef<any>>;\n\n    @Input() groupIconTemplate: Nullable<TemplateRef<any>>;\n\n    @Input({ transform: numberAttribute }) level: number = 0;\n\n    @Input() optionLabel: string | undefined;\n\n    @Input() optionValue: string | undefined;\n\n    @Input() optionGroupLabel: string | undefined;\n\n    @Input({ transform: booleanAttribute }) dirty: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) root: boolean | undefined;\n\n    @Output() onChange: EventEmitter<any> = new EventEmitter();\n\n    get listLabel(): string {\n        return this.config.getTranslation(TranslationKeys.ARIA)['listLabel'];\n    }\n\n    constructor(private el: ElementRef, public config: PrimeNGConfig) {}\n\n    ngOnInit() {\n        if (!this.root) {\n            this.position();\n        }\n    }\n\n    onOptionClick(event, option: any) {\n        this.onChange.emit({\n            originalEvent: event,\n            value: option,\n            isFocus: true\n        });\n    }\n\n    onOptionChange(event) {\n        this.onChange.emit(event);\n    }\n\n    getOptionId(processedOption) {\n        return `${this.selectId}_${processedOption.key}`;\n    }\n\n    getOptionLabel(processedOption) {\n        return this.optionLabel ? ObjectUtils.resolveFieldData(processedOption.option, this.optionLabel) : processedOption.option;\n    }\n\n    getOptionValue(processedOption) {\n        return this.optionValue ? ObjectUtils.resolveFieldData(processedOption.option, this.optionValue) : processedOption.option;\n    }\n\n    getOptionLabelToRender(processedOption) {\n        return this.isOptionGroup(processedOption) ? this.getOptionGroupLabel(processedOption) : this.getOptionLabel(processedOption);\n    }\n\n    isOptionDisabled(processedOption) {\n        return this.optionDisabled ? ObjectUtils.resolveFieldData(processedOption.option, this.optionDisabled) : false;\n    }\n\n    getOptionGroupLabel(processedOption) {\n        return this.optionGroupLabel ? ObjectUtils.resolveFieldData(processedOption.option, this.optionGroupLabel) : null;\n    }\n\n    getOptionGroupChildren(processedOption) {\n        return processedOption.children;\n    }\n\n    isOptionGroup(processedOption) {\n        return ObjectUtils.isNotEmpty(processedOption.children);\n    }\n\n    isOptionSelected(processedOption) {\n        return !this.isOptionGroup(processedOption) && this.isOptionActive(processedOption);\n    }\n\n    isOptionActive(processedOption) {\n        return this.activeOptionPath.some((path) => path.key === processedOption.key);\n    }\n\n    isOptionFocused(processedOption) {\n        return this.focusedOptionId === this.getOptionId(processedOption);\n    }\n\n    getItemClass(option: string | string[]) {\n        return {\n            'p-cascadeselect-item': true,\n            'p-cascadeselect-item-group': this.isOptionGroup(option),\n            'p-cascadeselect-item-active p-highlight': this.isOptionActive(option),\n            'p-focus': this.isOptionFocused(option),\n            'p-disabled': this.isOptionDisabled(option)\n        };\n    }\n\n    position() {\n        const parentItem = this.el.nativeElement.parentElement;\n        const containerOffset = DomHandler.getOffset(parentItem);\n        const viewport = DomHandler.getViewport();\n        const sublistWidth = this.el.nativeElement.children[0].offsetParent ? this.el.nativeElement.children[0].offsetWidth : DomHandler.getHiddenElementOuterWidth(this.el.nativeElement.children[0]);\n        const itemOuterWidth = DomHandler.getOuterWidth(parentItem.children[0]);\n\n        if (parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth > viewport.width - DomHandler.calculateScrollbarWidth()) {\n            this.el.nativeElement.children[0].style.left = '-200%';\n        }\n    }\n}\n/**\n * CascadeSelect is a form component to select a value from a nested structure of options.\n * @group Components\n */\n@Component({\n    selector: 'p-cascadeSelect',\n    template: ` <div #container [ngClass]=\"containerClass\" [class]=\"styleClass\" [ngStyle]=\"style\" (click)=\"onContainerClick($event)\" [attr.data-pc-name]=\"'cascadeselect'\" [attr.data-pc-section]=\"'root'\">\n        <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\">\n            <input\n                #focusInput\n                readonly\n                type=\"text\"\n                role=\"combobox\"\n                [disabled]=\"disabled\"\n                [placeholder]=\"placeholder\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [attr.id]=\"inputId\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                aria-haspopup=\"tree\"\n                [attr.aria-expanded]=\"overlayVisible ?? false\"\n                [attr.aria-controls]=\"overlayVisible ? id + '_tree' : null\"\n                [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                (keydown)=\"onInputKeyDown($event)\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n            />\n        </div>\n        <span [ngClass]=\"labelClass\" [attr.data-pc-section]=\"'label'\">\n            <ng-container *ngIf=\"valueTemplate; else defaultValueTemplate\">\n                <ng-container *ngTemplateOutlet=\"valueTemplate; context: { $implicit: value, placeholder: placeholder }\"></ng-container>\n            </ng-container>\n            <ng-template #defaultValueTemplate>\n                {{ label() }}\n            </ng-template>\n        </span>\n\n        <ng-container *ngIf=\"filled && !disabled && showClear\">\n            <TimesIcon *ngIf=\"!clearIconTemplate\" [styleClass]=\"'p-cascadeselect-clear-icon'\" (click)=\"clear($event)\" [attr.data-pc-section]=\"'clearicon'\" [attr.aria-hidden]=\"true\" />\n            <span *ngIf=\"clearIconTemplate\" class=\"p-cascadeselect-clear-icon\" (click)=\"clear($event)\" [attr.data-pc-section]=\"'clearicon'\" [attr.aria-hidden]=\"true\">\n                <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n            </span>\n        </ng-container>\n\n        <div class=\"p-cascadeselect-trigger\" role=\"button\" aria-haspopup=\"listbox\" [attr.aria-expanded]=\"overlayVisible ?? false\" [attr.data-pc-section]=\"'dropdownIcon'\" [attr.aria-hidden]=\"true\">\n            <ng-container *ngIf=\"loading; else elseBlock\">\n                <ng-container *ngIf=\"loadingIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"loadingIconTemplate\"></ng-container>\n                </ng-container>\n                <ng-container *ngIf=\"!loadingIconTemplate\">\n                    <span *ngIf=\"loadingIcon\" [ngClass]=\"'p-cascadeselect-trigger-icon pi-spin ' + loadingIcon\" aria-hidden=\"true\"></span>\n                    <span *ngIf=\"!loadingIcon\" [class]=\"'p-cascadeselect-trigger-icon pi pi-spinner pi-spin'\" aria-hidden=\"true\"></span>\n                </ng-container>\n            </ng-container>\n            <ng-template #elseBlock>\n                <ChevronDownIcon *ngIf=\"!triggerIconTemplate\" [styleClass]=\"'p-cascadeselect-trigger-icon'\" />\n                <span *ngIf=\"triggerIconTemplate\" class=\"p-cascadeselect-trigger-icon\">\n                    <ng-template *ngTemplateOutlet=\"triggerIconTemplate\"></ng-template>\n                </span>\n            </ng-template>\n        </div>\n        <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\">\n            {{ searchResultMessageText }}\n        </span>\n        <p-overlay\n            #overlay\n            [(visible)]=\"overlayVisible\"\n            [options]=\"overlayOptions\"\n            [target]=\"'@parent'\"\n            [appendTo]=\"appendTo\"\n            [showTransitionOptions]=\"showTransitionOptions\"\n            [hideTransitionOptions]=\"hideTransitionOptions\"\n            (onAnimationDone)=\"onOverlayAnimationDone($event)\"\n            (onBeforeShow)=\"onBeforeShow.emit($event)\"\n            (onShow)=\"show($event)\"\n            (onBeforeHide)=\"onBeforeHide.emit($event)\"\n            (onHide)=\"hide($event)\"\n        >\n            <ng-template pTemplate=\"content\">\n                <div #panel class=\"p-cascadeselect-panel p-component\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\" [attr.data-pc-section]=\"'panel'\">\n                    <div class=\"p-cascadeselect-items-wrapper\" [attr.data-pc-section]=\"'wrapper'\">\n                        <p-cascadeSelectSub\n                            [options]=\"processedOptions\"\n                            [selectId]=\"id\"\n                            [focusedOptionId]=\"focused ? focusedOptionId : undefined\"\n                            [activeOptionPath]=\"activeOptionPath()\"\n                            [optionLabel]=\"optionLabel\"\n                            [optionValue]=\"optionValue\"\n                            [level]=\"0\"\n                            [optionTemplate]=\"optionTemplate\"\n                            [groupIconTemplate]=\"groupIconTemplate\"\n                            [optionGroupLabel]=\"optionGroupLabel\"\n                            [optionGroupChildren]=\"optionGroupChildren\"\n                            [optionDisabled]=\"optionDisabled\"\n                            [optionValue]=\"optionValue\"\n                            [optionLabel]=\"optionLabel\"\n                            [root]=\"true\"\n                            (onChange)=\"onOptionChange($event)\"\n                            [dirty]=\"dirty\"\n                            [role]=\"'tree'\"\n                        >\n                        </p-cascadeSelectSub>\n                    </div>\n                    <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\">\n                        {{ selectedMessageText }}\n                    </span>\n                </div>\n            </ng-template>\n        </p-overlay>\n    </div>`,\n    host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': 'focused || overlayVisible',\n        '[class.p-cascadeselect-clearable]': 'showClear && !disabled'\n    },\n    providers: [CASCADESELECT_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./cascadeselect.css']\n})\nexport class CascadeSelect implements OnInit, AfterContentInit {\n    /**\n     * Unique identifier of the component\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Determines if the option will be selected on focus.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) selectOnFocus: boolean = false;\n    /**\n     * Text to display when the search is active. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} results are available'\n     */\n    @Input() searchMessage: string | undefined;\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    @Input() emptyMessage: string | undefined;\n    /**\n     * Text to be displayed in hidden accessible field when options are selected. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} items selected'\n     */\n    @Input() selectionMessage: string | undefined;\n    /**\n     * Text to display when filtering does not return any results. Defaults to value from PrimeNG locale configuration.\n     * @group Props\n     * @defaultValue 'No available options'\n     */\n    @Input() emptySearchMessage: string | undefined;\n    /**\n     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue 'No selected item'\n     */\n    @Input() emptySelectionMessage: string | undefined;\n    /**\n     * Locale to use in searching. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() searchLocale: string | undefined;\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    @Input() optionDisabled: any;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoOptionFocus: boolean = true;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * An array of selectitems to display as the available options.\n     * @group Props\n     */\n    @Input() options: string[] | string | undefined;\n    /**\n     * Property name or getter function to use as the label of an option.\n     * @group Props\n     */\n    @Input() optionLabel: string | undefined;\n    /**\n     * Property name or getter function to use as the value of an option, defaults to the option itself when not defined.\n     * @group Props\n     */\n    @Input() optionValue: string | undefined;\n    /**\n     * Property name or getter function to use as the label of an option group.\n     * @group Props\n     */\n    @Input() optionGroupLabel: string | string[] | undefined;\n    /**\n     * Property name or getter function to retrieve the items of a group.\n     * @group Props\n     */\n    @Input() optionGroupChildren: string | string[] | undefined;\n    /**\n     * Default text to display when no option is selected.\n     * @group Props\n     */\n    @Input() placeholder: string | undefined;\n    /**\n     * Selected value of the component.\n     * @group Props\n     */\n    @Input() value: string | undefined | null;\n    /**\n     * A property to uniquely identify an option.\n     * @group Props\n     */\n    @Input() dataKey: string | undefined;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined = 0;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Label of the input for accessibility.\n     * @group Props\n     */\n    @Input() inputLabel: string | undefined;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Id of the element or \"body\" for document where the overlay should be appended to.\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean = false;\n    /**\n     * Style class of the overlay panel.\n     * @group Props\n     */\n    @Input() panelStyleClass: string | undefined;\n    /**\n     * Inline style of the overlay panel.\n     * @group Props\n     */\n    @Input() panelStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Whether to use overlay API feature. The properties of overlay API can be used like an object in it.\n     * @group Props\n     */\n    @Input() overlayOptions: OverlayOptions | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     * @deprecated deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    @Input() get showTransitionOptions(): string {\n        return this._showTransitionOptions;\n    }\n    set showTransitionOptions(val: string) {\n        this._showTransitionOptions = val;\n        console.warn('The showTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Whether the dropdown is in loading state.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) loading: boolean | undefined = false;\n    /**\n     * Icon to display in loading state.\n     * @group Props\n     */\n    @Input() loadingIcon: string | undefined;\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     * @deprecated deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    @Input() get hideTransitionOptions(): string {\n        return this._hideTransitionOptions;\n    }\n    set hideTransitionOptions(val: string) {\n        this._hideTransitionOptions = val;\n        console.warn('The hideTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    /**\n     * Callback to invoke on value change.\n     * @param {CascadeSelectChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<CascadeSelectChangeEvent> = new EventEmitter<CascadeSelectChangeEvent>();\n    /**\n     * Callback to invoke when a group changes.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onGroupChange: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when the overlay is shown.\n     * @param {CascadeSelectShowEvent} event - Custom overlay show event.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<CascadeSelectShowEvent> = new EventEmitter<CascadeSelectShowEvent>();\n    /**\n     * Callback to invoke when the overlay is hidden.\n     * @param {CascadeSelectHideEvent} event - Custom overlay hide event.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<CascadeSelectHideEvent> = new EventEmitter<CascadeSelectHideEvent>();\n    /**\n     * Callback to invoke when the clear token is clicked.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<any> = new EventEmitter();\n    /**\n     * Callback to invoke before overlay is shown.\n     * @param {CascadeSelectBeforeShowEvent} event - Custom overlay show event.\n     * @group Emits\n     */\n    @Output() onBeforeShow: EventEmitter<CascadeSelectBeforeShowEvent> = new EventEmitter<CascadeSelectBeforeShowEvent>();\n    /**\n     * Callback to invoke before overlay is hidden.\n     * @param {CascadeSelectBeforeHideEvent} event - Custom overlay hide event.\n     * @group Emits\n     */\n    @Output() onBeforeHide: EventEmitter<CascadeSelectBeforeHideEvent> = new EventEmitter<CascadeSelectBeforeHideEvent>();\n    /**\n     * Callback to invoke when input receives focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n    /**\n     * Callback to invoke when input loses focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n\n    @ViewChild('focusInput') focusInputViewChild: Nullable<ElementRef>;\n\n    @ViewChild('container') containerViewChild: Nullable<ElementRef>;\n\n    @ViewChild('panel') panelViewChild: Nullable<ElementRef>;\n\n    @ViewChild('overlay') overlayViewChild: Nullable<Overlay>;\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    _showTransitionOptions: string = '';\n\n    _hideTransitionOptions: string = '';\n\n    selectionPath: any = null;\n\n    focused: boolean = false;\n\n    overlayVisible: boolean = false;\n\n    dirty: boolean = true;\n\n    searchValue: string | undefined;\n\n    searchTimeout: any;\n\n    valueTemplate: Nullable<TemplateRef<any>>;\n\n    optionTemplate: Nullable<TemplateRef<any>>;\n\n    triggerIconTemplate: Nullable<TemplateRef<any>>;\n\n    loadingIconTemplate: Nullable<TemplateRef<any>>;\n\n    groupIconTemplate: Nullable<TemplateRef<any>>;\n\n    clearIconTemplate: Nullable<TemplateRef<any>>;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    focusedOptionInfo = signal<any>({ index: -1, level: 0, parentKey: '' });\n\n    activeOptionPath = signal<any>([]);\n\n    modelValue = signal<any>(null);\n\n    processedOptions: string[] | string | undefined = [];\n\n    get containerClass() {\n        return {\n            'p-cascadeselect p-component p-inputwrapper': true,\n            'p-disabled': this.disabled,\n            'p-focus': this.focused,\n            'p-inputwrapper-filled': this.modelValue(),\n            'p-variant-filled': this.variant === 'filled' || this.config.inputStyle() === 'filled',\n            'p-inputwrapper-focus': this.focused || this.overlayVisible,\n            'p-overlay-open': this.overlayVisible\n        };\n    }\n\n    get labelClass() {\n        return {\n            'p-cascadeselect-label': true,\n            'p-placeholder': this.label() === this.placeholder,\n            'p-cascadeselect-label-empty': !this.value && (this.label() === 'p-emptylabel' || this.label().length === 0)\n        };\n    }\n\n    get focusedOptionId() {\n        return this.focusedOptionInfo().index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(this.focusedOptionInfo().parentKey) ? '_' + this.focusedOptionInfo().parentKey : ''}_${this.focusedOptionInfo().index}` : null;\n    }\n\n    get filled(): boolean {\n        if (typeof this.modelValue() === 'string') return !!this.modelValue();\n\n        return this.modelValue() || this.modelValue() != null || this.modelValue() != undefined;\n    }\n\n    get searchResultMessageText() {\n        return ObjectUtils.isNotEmpty(this.visibleOptions()) ? this.searchMessageText.replaceAll('{0}', this.visibleOptions().length) : this.emptySearchMessageText;\n    }\n\n    get searchMessageText() {\n        return this.searchMessage || this.config.translation.searchMessage || '';\n    }\n\n    get emptySearchMessageText() {\n        return this.emptySearchMessage || this.config.translation.emptySearchMessage || '';\n    }\n\n    get emptyMessageText() {\n        return this.emptyMessage || this.config.translation.emptyMessage || '';\n    }\n\n    get selectionMessageText() {\n        return this.selectionMessage || this.config.translation.selectionMessage || '';\n    }\n\n    get emptySelectionMessageText() {\n        return this.emptySelectionMessage || this.config.translation.emptySelectionMessage || '';\n    }\n\n    get selectedMessageText() {\n        return this.hasSelectedOption ? this.selectionMessageText.replaceAll('{0}', '1') : this.emptySelectionMessageText;\n    }\n\n    visibleOptions = computed(() => {\n        const processedOption = this.activeOptionPath().find((p) => p.key === this.focusedOptionInfo().parentKey);\n\n        return processedOption ? processedOption.children : this.processedOptions;\n    });\n\n    label = computed(() => {\n        const label = this.placeholder || 'p-emptylabel';\n\n        if (this.hasSelectedOption()) {\n            const activeOptionPath = this.findOptionPathByValue(this.modelValue(), null);\n            const processedOption = ObjectUtils.isNotEmpty(activeOptionPath) ? activeOptionPath[activeOptionPath.length - 1] : null;\n\n            return processedOption ? this.getOptionLabel(processedOption.option) : label;\n        }\n        return label;\n    });\n\n    get _label() {\n        const label = this.placeholder || 'p-emptylabel';\n\n        if (this.hasSelectedOption()) {\n            const activeOptionPath = this.findOptionPathByValue(this.modelValue(), null);\n            const processedOption = ObjectUtils.isNotEmpty(activeOptionPath) ? activeOptionPath[activeOptionPath.length - 1] : null;\n\n            return processedOption ? this.getOptionLabel(processedOption.option) : label;\n        }\n        return label;\n    }\n\n    ngOnChanges(changes: SimpleChanges): void {\n        if (changes.options) {\n            this.processedOptions = this.createProcessedOptions(changes.options.currentValue || []);\n            this.updateModel(null);\n        }\n    }\n\n    hasSelectedOption() {\n        return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n\n    createProcessedOptions(options, level = 0, parent = {}, parentKey = '') {\n        const processedOptions = [];\n\n        options &&\n            options.forEach((option, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newOption = {\n                    option,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n\n                newOption['children'] = this.createProcessedOptions(this.getOptionGroupChildren(option, level), level + 1, newOption, key);\n                processedOptions.push(newOption);\n            });\n\n        return processedOptions;\n    }\n\n    onInputFocus(event: FocusEvent) {\n        if (this.disabled) {\n            // For screenreaders\n            return;\n        }\n\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event: FocusEvent) {\n        this.focused = false;\n        this.focusedOptionInfo.set({ indeX: -1, level: 0, parentKey: '' });\n        this.searchValue = '';\n        this.onModelTouched();\n        this.onBlur.emit(event);\n    }\n\n    onInputKeyDown(event: KeyboardEvent) {\n        if (this.disabled || this.loading) {\n            event.preventDefault();\n\n            return;\n        }\n\n        const metaKey = event.metaKey || event.ctrlKey;\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n\n            case 'Enter':\n            case 'NumpadEnter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            case 'Backspace':\n                this.onBackspaceKey(event);\n                break;\n\n            case 'PageDown':\n            case 'PageUp':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    !this.overlayVisible && this.show();\n                    this.searchOptions(event, event.key);\n                }\n\n                break;\n        }\n    }\n\n    onArrowDownKey(event) {\n        const optionIndex = this.focusedOptionInfo().index !== -1 ? this.findNextOptionIndex(this.focusedOptionInfo().index) : this.findFirstFocusedOptionIndex();\n\n        this.changeFocusedOptionIndex(event, optionIndex);\n\n        !this.overlayVisible && this.show();\n        event.preventDefault();\n    }\n\n    onArrowUpKey(event) {\n        if (event.altKey) {\n            if (this.focusedOptionInfo().index !== -1) {\n                const processedOption = this.visibleOptions[this.focusedOptionInfo().index];\n                const grouped = this.isProccessedOptionGroup(processedOption);\n\n                !grouped && this.onOptionChange({ originalEvent: event, value: processedOption });\n            }\n\n            this.overlayVisible && this.hide();\n            event.preventDefault();\n        } else {\n            const optionIndex = this.focusedOptionInfo().index !== -1 ? this.findPrevOptionIndex(this.focusedOptionInfo().index) : this.findLastFocusedOptionIndex();\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n\n            !this.overlayVisible && this.show();\n            event.preventDefault();\n        }\n    }\n\n    onArrowLeftKey(event) {\n        if (this.overlayVisible) {\n            const processedOption = this.visibleOptions()[this.focusedOptionInfo().index];\n            const parentOption = this.activeOptionPath().find((p) => p.key === processedOption.parentKey);\n            const matched = this.focusedOptionInfo().parentKey === '' || (parentOption && parentOption.key === this.focusedOptionInfo().parentKey);\n            const root = ObjectUtils.isEmpty(processedOption.parent);\n\n            if (matched) {\n                const activeOptionPath = this.activeOptionPath().filter((p) => p.parentKey !== this.focusedOptionInfo().parentKey);\n                this.activeOptionPath.set(activeOptionPath);\n            }\n\n            if (!root) {\n                this.focusedOptionInfo.set({ index: -1, parentKey: parentOption ? parentOption.parentKey : '' });\n                this.searchValue = '';\n                this.onArrowDownKey(event);\n            }\n\n            event.preventDefault();\n        }\n    }\n\n    onArrowRightKey(event) {\n        if (this.overlayVisible) {\n            const processedOption = this.visibleOptions()[this.focusedOptionInfo().index];\n            const grouped = this.isProccessedOptionGroup(processedOption);\n\n            if (grouped) {\n                const matched = this.activeOptionPath().some((p) => processedOption.key === p.key);\n\n                if (matched) {\n                    this.focusedOptionInfo.set({ index: -1, parentKey: processedOption.key });\n                    this.searchValue = '';\n                    this.onArrowDownKey(event);\n                } else {\n                    this.onOptionChange({ originalEvent: event, value: processedOption });\n                }\n            }\n\n            event.preventDefault();\n        }\n    }\n\n    onHomeKey(event) {\n        this.changeFocusedOptionIndex(event, this.findFirstOptionIndex());\n\n        !this.overlayVisible && this.show();\n        event.preventDefault();\n    }\n\n    onEndKey(event) {\n        this.changeFocusedOptionIndex(event, this.findLastOptionIndex());\n\n        !this.overlayVisible && this.show();\n        event.preventDefault();\n    }\n\n    onEnterKey(event) {\n        if (!this.overlayVisible) {\n            this.onArrowDownKey(event);\n        } else {\n            if (this.focusedOptionInfo().index !== -1) {\n                const processedOption = this.visibleOptions()[this.focusedOptionInfo().index];\n                const grouped = this.isProccessedOptionGroup(processedOption);\n\n                this.onOptionChange({ originalEvent: event, value: processedOption });\n                !grouped && this.hide();\n            }\n        }\n\n        event.preventDefault();\n    }\n\n    onSpaceKey(event) {\n        this.onEnterKey(event);\n    }\n\n    onEscapeKey(event) {\n        this.overlayVisible && this.hide(true);\n        event.preventDefault();\n    }\n\n    onTabKey(event) {\n        if (this.focusedOptionInfo().index !== -1) {\n            const processedOption = this.visibleOptions()[this.focusedOptionInfo().index];\n            const grouped = this.isProccessedOptionGroup(processedOption);\n\n            !grouped && this.onOptionChange({ originalEvent: event, value: processedOption });\n        }\n\n        this.overlayVisible && this.hide();\n    }\n\n    onBackspaceKey(event) {\n        if (ObjectUtils.isNotEmpty(this.modelValue()) && this.showClear) {\n            this.clear();\n        }\n\n        event.stopPropagation();\n    }\n\n    equalityKey() {\n        return this.optionValue ? null : this.dataKey;\n    }\n\n    updateModel(value, event?) {\n        this.value = value;\n        this.onModelChange(value);\n        this.modelValue.set(value);\n\n        this.onChange.emit({\n            originalEvent: event,\n            value: value\n        });\n    }\n\n    autoUpdateModel() {\n        if (this.selectOnFocus && this.autoOptionFocus && !this.hasSelectedOption()) {\n            this.focusedOptionInfo().index = this.findFirstFocusedOptionIndex();\n            this.onOptionChange({ originalEvent: null, processedOption: this.visibleOptions()[this.focusedOptionInfo().index], isHide: false });\n\n            !this.overlayVisible && this.focusedOptionInfo.set({ index: -1, level: 0, parentKey: '' });\n        }\n    }\n\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n        const element = DomHandler.findSingle(this.panelViewChild?.nativeElement, `li[id=\"${id}\"]`);\n\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'start' });\n        }\n    }\n\n    changeFocusedOptionIndex(event, index) {\n        if (this.focusedOptionInfo().index !== index) {\n            const focusedOptionInfo = this.focusedOptionInfo();\n            this.focusedOptionInfo.set({ ...focusedOptionInfo, index });\n            this.scrollInView();\n        }\n\n        if (this.selectOnFocus) {\n            this.onOptionChange({ originalEvent: event, processedOption: this.visibleOptions()[index], isHide: false });\n        }\n    }\n\n    onOptionChange(event) {\n        const { originalEvent, value, isFocus, isHide } = event;\n        if (ObjectUtils.isEmpty(value)) return;\n\n        const { index, level, parentKey, children } = value;\n        const grouped = ObjectUtils.isNotEmpty(children);\n\n        const activeOptionPath = this.activeOptionPath().filter((p) => p.parentKey !== parentKey);\n\n        activeOptionPath.push(value);\n\n        this.focusedOptionInfo.set({ index, level, parentKey });\n        this.activeOptionPath.set(activeOptionPath);\n\n        grouped ? this.onOptionGroupSelect({ originalEvent, value, isFocus: false }) : this.onOptionSelect({ originalEvent, value, isFocus });\n        isFocus && DomHandler.focus(this.focusInputViewChild.nativeElement);\n    }\n\n    onOptionSelect(event) {\n        const { originalEvent, value, isFocus } = event;\n        const newValue = this.getOptionValue(value.option);\n\n        const activeOptionPath = this.activeOptionPath();\n        activeOptionPath.forEach((p) => (p.selected = true));\n\n        this.activeOptionPath.set(activeOptionPath);\n        this.updateModel(newValue, originalEvent);\n        isFocus && this.hide(true);\n    }\n\n    onOptionGroupSelect(event) {\n        this.dirty = true;\n        this.onGroupChange.emit(event);\n    }\n\n    onContainerClick(event: MouseEvent) {\n        if (this.disabled || this.loading) {\n            return;\n        }\n\n        if (!this.overlayViewChild?.el?.nativeElement?.contains(event.target)) {\n            if (this.overlayVisible) {\n                this.hide();\n            } else {\n                this.show();\n            }\n\n            this.focusInputViewChild?.nativeElement.focus();\n        }\n    }\n\n    isOptionMatched(processedOption) {\n        return this.isValidOption(processedOption) && this.getProccessedOptionLabel(processedOption).toLocaleLowerCase(this.searchLocale).startsWith(this.searchValue.toLocaleLowerCase(this.searchLocale));\n    }\n\n    isOptionDisabled(option) {\n        return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : false;\n    }\n\n    isValidOption(processedOption) {\n        return !!processedOption && !this.isOptionDisabled(processedOption.option);\n    }\n\n    isValidSelectedOption(processedOption) {\n        return this.isValidOption(processedOption) && this.isSelected(processedOption);\n    }\n\n    isSelected(processedOption) {\n        return this.activeOptionPath().some((p) => p.key === processedOption.key);\n    }\n\n    findOptionPathByValue(value, processedOptions?, level = 0) {\n        processedOptions = processedOptions || (level === 0 && this.processedOptions);\n\n        if (!processedOptions) return null;\n        if (ObjectUtils.isEmpty(value)) return [];\n\n        for (let i = 0; i < processedOptions.length; i++) {\n            const processedOption = processedOptions[i];\n\n            if (ObjectUtils.equals(value, this.getOptionValue(processedOption.option), this.equalityKey())) {\n                return [processedOption];\n            }\n\n            const matchedOptions = this.findOptionPathByValue(value, processedOption.children, level + 1);\n\n            if (matchedOptions) {\n                matchedOptions.unshift(processedOption);\n\n                return matchedOptions;\n            }\n        }\n    }\n\n    findFirstOptionIndex() {\n        return this.visibleOptions().findIndex((processedOption) => this.isValidOption(processedOption));\n    }\n\n    findLastOptionIndex() {\n        return ObjectUtils.findLastIndex(this.visibleOptions(), (processedOption) => this.isValidOption(processedOption));\n    }\n\n    findNextOptionIndex(index) {\n        const matchedOptionIndex =\n            index < this.visibleOptions().length - 1\n                ? this.visibleOptions()\n                      .slice(index + 1)\n                      .findIndex((processedOption) => this.isValidOption(processedOption))\n                : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n\n    findPrevOptionIndex(index) {\n        const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), (processedOption) => this.isValidOption(processedOption)) : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n\n    findSelectedOptionIndex() {\n        return this.visibleOptions().findIndex((processedOption) => this.isValidSelectedOption(processedOption));\n    }\n\n    findFirstFocusedOptionIndex() {\n        const selectedIndex = this.findSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n\n    findLastFocusedOptionIndex() {\n        const selectedIndex = this.findSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n\n    searchOptions(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let optionIndex = -1;\n        let matched = false;\n        const focusedOptionInfo = this.focusedOptionInfo();\n\n        if (focusedOptionInfo.index !== -1) {\n            optionIndex = this.visibleOptions()\n                .slice(focusedOptionInfo.index)\n                .findIndex((processedOption) => this.isOptionMatched(processedOption));\n            optionIndex =\n                optionIndex === -1\n                    ? this.visibleOptions()\n                          .slice(0, focusedOptionInfo.index)\n                          .findIndex((processedOption) => this.isOptionMatched(processedOption))\n                    : optionIndex + focusedOptionInfo.index;\n        } else {\n            optionIndex = this.visibleOptions().findIndex((processedOption) => this.isOptionMatched(processedOption));\n        }\n\n        if (optionIndex !== -1) {\n            matched = true;\n        }\n\n        if (optionIndex === -1 && focusedOptionInfo.index === -1) {\n            optionIndex = this.findFirstFocusedOptionIndex();\n        }\n\n        if (optionIndex !== -1) {\n            this.changeFocusedOptionIndex(event, optionIndex);\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n\n    hide(event?, isFocus = false) {\n        const _hide = () => {\n            this.overlayVisible = false;\n            this.activeOptionPath.set([]);\n            this.focusedOptionInfo.set({ index: -1, level: 0, parentKey: '' });\n\n            isFocus && DomHandler.focus(this.focusInputViewChild.nativeElement);\n            this.onHide.emit(event);\n        };\n\n        setTimeout(() => {\n            _hide();\n        }, 0); // For ScreenReaders\n    }\n\n    show(event?, isFocus = false) {\n        this.onShow.emit(event);\n        this.overlayVisible = true;\n        const activeOptionPath = this.hasSelectedOption() ? this.findOptionPathByValue(this.modelValue()) : this.activeOptionPath();\n        this.activeOptionPath.set(activeOptionPath);\n\n        let focusedOptionInfo;\n\n        if (this.hasSelectedOption() && ObjectUtils.isNotEmpty(this.activeOptionPath())) {\n            const processedOption = this.activeOptionPath()[this.activeOptionPath().length - 1];\n\n            focusedOptionInfo = { index: this.autoOptionFocus ? processedOption.index : -1, level: processedOption.level, parentKey: processedOption.parentKey };\n        } else {\n            focusedOptionInfo = { index: this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1, level: 0, parentKey: '' };\n        }\n\n        this.focusedOptionInfo.set(focusedOptionInfo);\n\n        isFocus && DomHandler.focus(this.focusInputViewChild.nativeElement);\n    }\n\n    clear(event?: MouseEvent) {\n        if (ObjectUtils.isNotEmpty(this.modelValue()) && this.showClear) {\n            this.updateModel(null);\n            this.focusedOptionInfo.set({ index: -1, level: 0, parentKey: '' });\n            this.activeOptionPath.set([]);\n            this.onClear.emit();\n        }\n\n        event && event.stopPropagation();\n    }\n\n    getOptionLabel(option) {\n        return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option;\n    }\n\n    getOptionValue(option) {\n        return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : option;\n    }\n\n    getOptionGroupLabel(optionGroup) {\n        return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : null;\n    }\n\n    getOptionGroupChildren(optionGroup, level) {\n        return ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren[level]);\n    }\n\n    isOptionGroup(option, level) {\n        return Object.prototype.hasOwnProperty.call(option, this.optionGroupChildren[level]);\n    }\n\n    isProccessedOptionGroup(processedOption) {\n        return ObjectUtils.isNotEmpty(processedOption.children);\n    }\n\n    getProccessedOptionLabel(processedOption) {\n        const grouped = this.isProccessedOptionGroup(processedOption);\n\n        return grouped ? this.getOptionGroupLabel(processedOption.option) : this.getOptionLabel(processedOption.option);\n    }\n\n    constructor(private el: ElementRef, private cd: ChangeDetectorRef, private config: PrimeNGConfig, public overlayService: OverlayService) {\n        effect(() => {\n            const activeOptionPath = this.activeOptionPath();\n            if (ObjectUtils.isNotEmpty(activeOptionPath)) {\n                this.overlayViewChild.alignOverlay();\n            }\n        });\n    }\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n        this.autoUpdateModel();\n    }\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'value':\n                    this.valueTemplate = item.template;\n                    break;\n\n                case 'option':\n                    this.optionTemplate = item.template;\n                    break;\n\n                case 'triggericon':\n                    this.triggerIconTemplate = item.template;\n                    break;\n\n                case 'loadingicon':\n                    this.loadingIconTemplate = item.template;\n                    break;\n\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n\n                case 'optiongroupicon':\n                    this.groupIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    onOverlayAnimationDone(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                this.dirty = false;\n                break;\n        }\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.updateModel(value);\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, OverlayModule, SharedModule, RippleModule, AutoFocusModule, ChevronDownIcon, AngleRightIcon, TimesIcon],\n    exports: [CascadeSelect, OverlayModule, CascadeSelectSub, SharedModule],\n    declarations: [CascadeSelect, CascadeSelectSub]\n})\nexport class CascadeSelectModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAyCa,MAAA,4BAA4B,GAAQ;AAC7C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,aAAa,CAAC;AAC5C,IAAA,KAAK,EAAE,IAAI;EACb;MA8DW,gBAAgB,CAAA;AAqCL,IAAA,EAAA,CAAA;AAAuB,IAAA,MAAA,CAAA;AApClC,IAAA,IAAI,CAAqB;AAEzB,IAAA,QAAQ,CAAqB;AAE7B,IAAA,gBAAgB,CAAQ;AAExB,IAAA,cAAc,CAAQ;AAEtB,IAAA,eAAe,CAAqB;AAEpC,IAAA,OAAO,CAAuC;AAE9C,IAAA,mBAAmB,CAAuC;AAE1D,IAAA,cAAc,CAA6B;AAE3C,IAAA,iBAAiB,CAA6B;IAEhB,KAAK,GAAW,CAAC,CAAC;AAEhD,IAAA,WAAW,CAAqB;AAEhC,IAAA,WAAW,CAAqB;AAEhC,IAAA,gBAAgB,CAAqB;AAEN,IAAA,KAAK,CAAsB;AAE3B,IAAA,IAAI,CAAsB;AAExD,IAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;AAE3D,IAAA,IAAI,SAAS,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC;KACxE;IAED,WAAoB,CAAA,EAAc,EAAS,MAAqB,EAAA;QAA5C,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAEpE,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,SAAA;KACJ;IAED,aAAa,CAAC,KAAK,EAAE,MAAW,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,KAAK,EAAE,MAAM;AACb,YAAA,OAAO,EAAE,IAAI;AAChB,SAAA,CAAC,CAAC;KACN;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;AAChB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC7B;AAED,IAAA,WAAW,CAAC,eAAe,EAAA;QACvB,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,IAAI,eAAe,CAAC,GAAG,CAAA,CAAE,CAAC;KACpD;AAED,IAAA,cAAc,CAAC,eAAe,EAAA;QAC1B,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC;KAC7H;AAED,IAAA,cAAc,CAAC,eAAe,EAAA;QAC1B,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC;KAC7H;AAED,IAAA,sBAAsB,CAAC,eAAe,EAAA;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;KACjI;AAED,IAAA,gBAAgB,CAAC,eAAe,EAAA;QAC5B,OAAO,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC;KAClH;AAED,IAAA,mBAAmB,CAAC,eAAe,EAAA;QAC/B,OAAO,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;KACrH;AAED,IAAA,sBAAsB,CAAC,eAAe,EAAA;QAClC,OAAO,eAAe,CAAC,QAAQ,CAAC;KACnC;AAED,IAAA,aAAa,CAAC,eAAe,EAAA;QACzB,OAAO,WAAW,CAAC,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;KAC3D;AAED,IAAA,gBAAgB,CAAC,eAAe,EAAA;AAC5B,QAAA,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;KACvF;AAED,IAAA,cAAc,CAAC,eAAe,EAAA;AAC1B,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,eAAe,CAAC,GAAG,CAAC,CAAC;KACjF;AAED,IAAA,eAAe,CAAC,eAAe,EAAA;QAC3B,OAAO,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;KACrE;AAED,IAAA,YAAY,CAAC,MAAyB,EAAA;QAClC,OAAO;AACH,YAAA,sBAAsB,EAAE,IAAI;AAC5B,YAAA,4BAA4B,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;AACxD,YAAA,yCAAyC,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;AACtE,YAAA,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;AACvC,YAAA,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;SAC9C,CAAC;KACL;IAED,QAAQ,GAAA;QACJ,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC;QACvD,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACzD,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,UAAU,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/L,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAExE,IAAI,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,cAAc,GAAG,YAAY,GAAG,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,uBAAuB,EAAE,EAAE;AAC5H,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;AAC1D,SAAA;KACJ;uGAzHQ,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,gWAmBL,eAAe,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAQf,gBAAgB,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAEhB,gBAAgB,CAvF1B,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsDT,EA2vCoG,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,cAAc,gFAvvC1G,gBAAgB,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,qBAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,OAAA,EAAA,aAAA,EAAA,aAAA,EAAA,kBAAA,EAAA,OAAA,EAAA,MAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBA5D5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAClD,iBAAA,CAAA;2GAEY,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAEG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAEG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAEG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAEG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAEiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAEkC,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE5B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;;AA4FX;;;AAGG;MAwHU,aAAa,CAAA;AAw7BF,IAAA,EAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAA+B,IAAA,MAAA,CAAA;AAA8B,IAAA,cAAA,CAAA;AAv7BzG;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;IACqC,aAAa,GAAY,KAAK,CAAC;AACvE;;;;AAIG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;;AAIG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;;AAIG;AACM,IAAA,kBAAkB,CAAqB;AAChD;;;;AAIG;AACM,IAAA,qBAAqB,CAAqB;AACnD;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACM,IAAA,cAAc,CAAM;AAC7B;;;AAGG;IACqC,eAAe,GAAY,IAAI,CAAC;AACxE;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,OAAO,CAAgC;AAChD;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,gBAAgB,CAAgC;AACzD;;;AAGG;AACM,IAAA,mBAAmB,CAAgC;AAC5D;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,KAAK,CAA4B;AAC1C;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACoC,QAAQ,GAAuB,CAAC,CAAC;AACxE;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,cAAc,CAA6B;AACpD;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACtC;IACD,IAAI,qBAAqB,CAAC,GAAW,EAAA;AACjC,QAAA,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;KACxH;AACD;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;AAGG;IACqC,OAAO,GAAwB,KAAK,CAAC;AAC7E;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACtC;IACD,IAAI,qBAAqB,CAAC,GAAW,EAAA;AACjC,QAAA,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;KACxH;AACD;;;;AAIG;AACO,IAAA,QAAQ,GAA2C,IAAI,YAAY,EAA4B,CAAC;AAC1G;;;;AAIG;AACO,IAAA,aAAa,GAAwB,IAAI,YAAY,EAAS,CAAC;AACzE;;;;AAIG;AACO,IAAA,MAAM,GAAyC,IAAI,YAAY,EAA0B,CAAC;AACpG;;;;AAIG;AACO,IAAA,MAAM,GAAyC,IAAI,YAAY,EAA0B,CAAC;AACpG;;;AAGG;AACO,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAE,CAAC;AAC1D;;;;AAIG;AACO,IAAA,YAAY,GAA+C,IAAI,YAAY,EAAgC,CAAC;AACtH;;;;AAIG;AACO,IAAA,YAAY,GAA+C,IAAI,YAAY,EAAgC,CAAC;AACtH;;;;AAIG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC7E;;;;AAIG;AACO,IAAA,MAAM,GAA6B,IAAI,YAAY,EAAc,CAAC;AAEnD,IAAA,mBAAmB,CAAuB;AAE3C,IAAA,kBAAkB,CAAuB;AAE7C,IAAA,cAAc,CAAuB;AAEnC,IAAA,gBAAgB,CAAoB;AAE1B,IAAA,SAAS,CAA4B;IAErE,sBAAsB,GAAW,EAAE,CAAC;IAEpC,sBAAsB,GAAW,EAAE,CAAC;IAEpC,aAAa,GAAQ,IAAI,CAAC;IAE1B,OAAO,GAAY,KAAK,CAAC;IAEzB,cAAc,GAAY,KAAK,CAAC;IAEhC,KAAK,GAAY,IAAI,CAAC;AAEtB,IAAA,WAAW,CAAqB;AAEhC,IAAA,aAAa,CAAM;AAEnB,IAAA,aAAa,CAA6B;AAE1C,IAAA,cAAc,CAA6B;AAE3C,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,iBAAiB,GAAG,MAAM,CAAM,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AAExE,IAAA,gBAAgB,GAAG,MAAM,CAAM,EAAE,CAAC,CAAC;AAEnC,IAAA,UAAU,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;IAE/B,gBAAgB,GAAkC,EAAE,CAAC;AAErD,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;AACH,YAAA,4CAA4C,EAAE,IAAI;YAClD,YAAY,EAAE,IAAI,CAAC,QAAQ;YAC3B,SAAS,EAAE,IAAI,CAAC,OAAO;AACvB,YAAA,uBAAuB,EAAE,IAAI,CAAC,UAAU,EAAE;AAC1C,YAAA,kBAAkB,EAAE,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ;AACtF,YAAA,sBAAsB,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc;YAC3D,gBAAgB,EAAE,IAAI,CAAC,cAAc;SACxC,CAAC;KACL;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,uBAAuB,EAAE,IAAI;YAC7B,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,WAAW;YAClD,6BAA6B,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC;SAC/G,CAAC;KACL;AAED,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAA,EAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,GAAG,EAAE,CAAI,CAAA,EAAA,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAA,CAAE,GAAG,IAAI,CAAC;KACrN;AAED,IAAA,IAAI,MAAM,GAAA;AACN,QAAA,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,QAAQ;AAAE,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAEtE,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,SAAS,CAAC;KAC3F;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;KAC/J;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,IAAI,EAAE,CAAC;KAC5E;AAED,IAAA,IAAI,sBAAsB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC;KACtF;AAED,IAAA,IAAI,gBAAgB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC;KAC1E;AAED,IAAA,IAAI,oBAAoB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,IAAI,EAAE,CAAC;KAClF;AAED,IAAA,IAAI,yBAAyB,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,qBAAqB,IAAI,EAAE,CAAC;KAC5F;AAED,IAAA,IAAI,mBAAmB,GAAA;QACnB,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;KACrH;AAED,IAAA,cAAc,GAAG,QAAQ,CAAC,MAAK;QAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,CAAC;AAE1G,QAAA,OAAO,eAAe,GAAG,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC9E,KAAC,CAAC,CAAC;AAEH,IAAA,KAAK,GAAG,QAAQ,CAAC,MAAK;AAClB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,IAAI,cAAc,CAAC;AAEjD,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC1B,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC;YAC7E,MAAM,eAAe,GAAG,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAExH,YAAA,OAAO,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AAChF,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;AACjB,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,MAAM,GAAA;AACN,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,IAAI,cAAc,CAAC;AAEjD,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC1B,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC;YAC7E,MAAM,eAAe,GAAG,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAExH,YAAA,OAAO,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AAChF,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,WAAW,CAAC,OAAsB,EAAA;QAC9B,IAAI,OAAO,CAAC,OAAO,EAAE;AACjB,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;AACxF,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,SAAA;KACJ;IAED,iBAAiB,GAAA;QACb,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KACpD;AAED,IAAA,sBAAsB,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAA;QAClE,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,OAAO;YACH,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;AAC9B,gBAAA,MAAM,GAAG,GAAG,CAAC,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,GAAG,EAAE,IAAI,KAAK,CAAC;AAC9D,gBAAA,MAAM,SAAS,GAAG;oBACd,MAAM;oBACN,KAAK;oBACL,KAAK;oBACL,GAAG;oBACH,MAAM;oBACN,SAAS;iBACZ,CAAC;gBAEF,SAAS,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;AAC3H,gBAAA,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,aAAC,CAAC,CAAC;AAEP,QAAA,OAAO,gBAAgB,CAAC;KAC3B;AAED,IAAA,YAAY,CAAC,KAAiB,EAAA;QAC1B,IAAI,IAAI,CAAC,QAAQ,EAAE;;YAEf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,OAAO;AACV,SAAA;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QAE/C,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;;gBAEb,MAAM;AAEV,YAAA;gBACI,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBACpC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACxC,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;AAChB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAE1J,QAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAElD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACpC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;QACd,IAAI,KAAK,CAAC,MAAM,EAAE;YACd,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACvC,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC5E,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;AAE9D,gBAAA,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;AACrF,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACnC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAEzJ,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAElD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACpC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,eAAe,CAAC,SAAS,CAAC,CAAC;YAC9F,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,KAAK,EAAE,KAAK,YAAY,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,CAAC;YACvI,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAEzD,YAAA,IAAI,OAAO,EAAE;gBACT,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,CAAC;AACnH,gBAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAC/C,aAAA;YAED,IAAI,CAAC,IAAI,EAAE;gBACP,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,YAAY,GAAG,YAAY,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC,CAAC;AACjG,gBAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,aAAA;YAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;AAE9D,YAAA,IAAI,OAAO,EAAE;gBACT,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAEnF,gBAAA,IAAI,OAAO,EAAE;AACT,oBAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1E,oBAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,oBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;AACzE,iBAAA;AACJ,aAAA;YAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;QACX,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAElE,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACpC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;QACV,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAEjE,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACpC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,SAAA;AAAM,aAAA;YACH,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACvC,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;AAE9D,gBAAA,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;AACtE,gBAAA,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AAC3B,aAAA;AACJ,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;QACb,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;QACV,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACvC,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;AAE9D,YAAA,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;AACrF,SAAA;AAED,QAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;KACtC;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;AAChB,QAAA,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;YAC7D,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,SAAA;QAED,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;IAED,WAAW,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;KACjD;IAED,WAAW,CAAC,KAAK,EAAE,KAAM,EAAA;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAE3B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,KAAK,EAAE,KAAK;AACf,SAAA,CAAC,CAAC;KACN;IAED,eAAe,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;YACzE,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACpE,YAAA,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YAEpI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AAC9F,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;QACnB,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,eAAe,CAAC;AACvE,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,EAAE,CAAA,OAAA,EAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAE5F,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;AAC3F,SAAA;KACJ;IAED,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAA;QACjC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,KAAK,KAAK,EAAE;AAC1C,YAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACnD,YAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,GAAG,iBAAiB,EAAE,KAAK,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/G,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AACxD,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE,OAAO;QAEvC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;QACpD,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAEjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;AAE1F,QAAA,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAE7B,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAE5C,QAAA,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;QACtI,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;KACvE;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEnD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACjD,QAAA,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;AAErD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AAC1C,QAAA,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC9B;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAClC;AAED,IAAA,gBAAgB,CAAC,KAAiB,EAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;YAC/B,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE,aAAa,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACnE,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,aAAA;AAED,YAAA,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;AACnD,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,eAAe,EAAA;AAC3B,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KACvM;AAED,IAAA,gBAAgB,CAAC,MAAM,EAAA;QACnB,OAAO,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC;KAClG;AAED,IAAA,aAAa,CAAC,eAAe,EAAA;AACzB,QAAA,OAAO,CAAC,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KAC9E;AAED,IAAA,qBAAqB,CAAC,eAAe,EAAA;AACjC,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;KAClF;AAED,IAAA,UAAU,CAAC,eAAe,EAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,eAAe,CAAC,GAAG,CAAC,CAAC;KAC7E;AAED,IAAA,qBAAqB,CAAC,KAAK,EAAE,gBAAiB,EAAE,KAAK,GAAG,CAAC,EAAA;AACrD,QAAA,gBAAgB,GAAG,gBAAgB,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAE9E,QAAA,IAAI,CAAC,gBAAgB;AAAE,YAAA,OAAO,IAAI,CAAC;AACnC,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;AAAE,YAAA,OAAO,EAAE,CAAC;AAE1C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC9C,YAAA,MAAM,eAAe,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAE5C,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;gBAC5F,OAAO,CAAC,eAAe,CAAC,CAAC;AAC5B,aAAA;AAED,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,eAAe,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAE9F,YAAA,IAAI,cAAc,EAAE;AAChB,gBAAA,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAExC,gBAAA,OAAO,cAAc,CAAC;AACzB,aAAA;AACJ,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,eAAe,KAAK,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC;KACpG;IAED,mBAAmB,GAAA;QACf,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,eAAe,KAAK,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC;KACrH;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;QACrB,MAAM,kBAAkB,GACpB,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC;AACpC,cAAE,IAAI,CAAC,cAAc,EAAE;AAChB,iBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChB,iBAAA,SAAS,CAAC,CAAC,eAAe,KAAK,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;cACxE,CAAC,CAAC,CAAC;AAEb,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KAC3E;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,eAAe,KAAK,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEvK,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,CAAC;KAC/D;IAED,uBAAuB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,eAAe,KAAK,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC,CAAC;KAC5G;IAED,2BAA2B,GAAA;AACvB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAErD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,aAAa,CAAC;KAC1E;IAED,0BAA0B,GAAA;AACtB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAErD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,aAAa,CAAC;KACzE;IAED,aAAa,CAAC,KAAK,EAAE,IAAI,EAAA;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;AAEnD,QAAA,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;QACrB,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAEnD,QAAA,IAAI,iBAAiB,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AAChC,YAAA,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE;AAC9B,iBAAA,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC;AAC9B,iBAAA,SAAS,CAAC,CAAC,eAAe,KAAK,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC;YAC3E,WAAW;gBACP,WAAW,KAAK,CAAC,CAAC;AACd,sBAAE,IAAI,CAAC,cAAc,EAAE;AAChB,yBAAA,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK,CAAC;AACjC,yBAAA,SAAS,CAAC,CAAC,eAAe,KAAK,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAC5E,sBAAE,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC;AACnD,SAAA;AAAM,aAAA;YACH,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,eAAe,KAAK,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC;AAC7G,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;YACpB,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;QAED,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,iBAAiB,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACtD,YAAA,WAAW,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACpD,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;AACpB,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,IAAI,CAAC,KAAM,EAAE,OAAO,GAAG,KAAK,EAAA;QACxB,MAAM,KAAK,GAAG,MAAK;AACf,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,YAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC9B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YAEnE,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;AACpE,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAC,CAAC;QAEF,UAAU,CAAC,MAAK;AACZ,YAAA,KAAK,EAAE,CAAC;AACZ,SAAC,EAAE,CAAC,CAAC,CAAC;KACT;AAED,IAAA,IAAI,CAAC,KAAM,EAAE,OAAO,GAAG,KAAK,EAAA;AACxB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC5H,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AAE5C,QAAA,IAAI,iBAAiB,CAAC;AAEtB,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE;AAC7E,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAEpF,YAAA,iBAAiB,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,eAAe,CAAC,KAAK,EAAE,SAAS,EAAE,eAAe,CAAC,SAAS,EAAE,CAAC;AACxJ,SAAA;AAAM,aAAA;AACH,YAAA,iBAAiB,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;AAC1H,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAE9C,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;KACvE;AAED,IAAA,KAAK,CAAC,KAAkB,EAAA;AACpB,QAAA,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;AAC7D,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AACnE,YAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AACvB,SAAA;AAED,QAAA,KAAK,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;KACpC;AAED,IAAA,cAAc,CAAC,MAAM,EAAA;QACjB,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;KAC7F;AAED,IAAA,cAAc,CAAC,MAAM,EAAA;QACjB,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;KAC7F;AAED,IAAA,mBAAmB,CAAC,WAAW,EAAA;QAC3B,OAAO,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;KAC1G;IAED,sBAAsB,CAAC,WAAW,EAAE,KAAK,EAAA;AACrC,QAAA,OAAO,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;KACrF;IAED,aAAa,CAAC,MAAM,EAAE,KAAK,EAAA;AACvB,QAAA,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;KACxF;AAED,IAAA,uBAAuB,CAAC,eAAe,EAAA;QACnC,OAAO,WAAW,CAAC,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;KAC3D;AAED,IAAA,wBAAwB,CAAC,eAAe,EAAA;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;QAE9D,OAAO,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KACnH;AAED,IAAA,WAAA,CAAoB,EAAc,EAAU,EAAqB,EAAU,MAAqB,EAAS,cAA8B,EAAA;QAAnH,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAS,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QACnI,MAAM,CAAC,MAAK;AACR,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACjD,YAAA,IAAI,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;AAC1C,gBAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;AACxC,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;QACzC,IAAI,CAAC,eAAe,EAAE,CAAC;KAC1B;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,iBAAiB;AAClB,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,sBAAsB,CAAC,KAAqB,EAAA;QACxC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gBACnB,MAAM;AACb,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;uGA7/BQ,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAb,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAUF,gBAAgB,CA4ChB,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,kUA4DhB,eAAe,CAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAyBf,gBAAgB,CAKhB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,yIAoBhB,gBAAgB,CAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAsBhB,gBAAgB,CA/LzB,EAAA,WAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,eAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,YAAA,EAAA,cAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,2BAAA,EAAA,iCAAA,EAAA,wBAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,SAAA,EAAA,CAAC,4BAA4B,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA+QxB,aAAa,EA/XpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGH,UAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,wwCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,cAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA6gC6E,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAkB,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAvvCrH,gBAAgB,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,qBAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,OAAA,EAAA,aAAA,EAAA,aAAA,EAAA,kBAAA,EAAA,OAAA,EAAA,MAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAsPhB,aAAa,EAAA,UAAA,EAAA,CAAA;kBAvHzB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EACjB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;WAyGH,EACD,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACjC,wBAAA,+BAA+B,EAAE,QAAQ;AACzC,wBAAA,8BAA8B,EAAE,2BAA2B;AAC3D,wBAAA,mCAAmC,EAAE,wBAAwB;qBAChE,EACU,SAAA,EAAA,CAAC,4BAA4B,CAAC,EACxB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,wwCAAA,CAAA,EAAA,CAAA;wKAQ5B,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM7B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAMG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAMG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAMG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAMzB,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAWG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAMO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAYI,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEkB,mBAAmB,EAAA,CAAA;sBAA3C,SAAS;uBAAC,YAAY,CAAA;gBAEC,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEF,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBAEI,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA2vBrB,mBAAmB,CAAA;uGAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAnB,mBAAmB,EAAA,YAAA,EAAA,CArgCnB,aAAa,EAtPb,gBAAgB,CAAA,EAAA,OAAA,EAAA,CAuvCf,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,CAAA,EAAA,OAAA,EAAA,CAjgCrH,aAAa,EAkgCG,aAAa,EAxvC7B,gBAAgB,EAwvCiC,YAAY,CAAA,EAAA,CAAA,CAAA;AAG7D,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,YAJlB,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,EACrG,aAAa,EAAoB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG7D,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAL/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,CAAC;oBAC/H,OAAO,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY,CAAC;AACvE,oBAAA,YAAY,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC;AAClD,iBAAA,CAAA;;;ACr2CD;;AAEG;;;;"}