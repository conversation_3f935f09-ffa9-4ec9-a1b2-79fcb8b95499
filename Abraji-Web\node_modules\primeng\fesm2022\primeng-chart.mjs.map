{"version": 3, "file": "primeng-chart.mjs", "sources": ["../../src/app/components/chart/chart.ts", "../../src/app/components/chart/primeng-chart.ts"], "sourcesContent": ["import { NgModule, Component, ElementRef, AfterViewInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectionStrategy, ViewEncapsulation, Inject, PLATFORM_ID, NgZone, booleanAttribute } from '@angular/core';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\nimport Chart from 'chart.js/auto';\n/**\n * Chart groups a collection of contents in tabs.\n * @group Components\n */\n@Component({\n    selector: 'p-chart',\n    template: `\n        <div\n            [ngStyle]=\"{\n                position: 'relative',\n                width: responsive && !width ? null : width,\n                height: responsive && !height ? null : height\n            }\"\n        >\n            <canvas\n                role=\"img\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [ngStyle]=\"{\n                    width: responsive && !width ? null : width,\n                    height: responsive && !height ? null : height\n                }\"\n                (click)=\"onCanvasClick($event)\"\n            ></canvas>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class UIChart implements AfterViewInit, OnDestroy {\n    /**\n     * Type of the chart.\n     * @group Props\n     */\n    @Input() type: 'bar' | 'line' | 'scatter' | 'bubble' | 'pie' | 'doughnut' | 'polarArea' | 'radar' | undefined;\n    /**\n     * Array of per-chart plugins to customize the chart behaviour.\n     * @group Props\n     */\n    @Input() plugins: any[] = [];\n    /**\n     * Width of the chart.\n     * @group Props\n     */\n    @Input() width: string | undefined;\n    /**\n     * Height of the chart.\n     * @group Props\n     */\n    @Input() height: string | undefined;\n    /**\n     * Whether the chart is redrawn on screen size change.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) responsive: boolean = true;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Data to display.\n     * @group Props\n     */\n    @Input() get data(): any {\n        return this._data;\n    }\n    set data(val: any) {\n        this._data = val;\n        this.reinit();\n    }\n    /**\n     * Options to customize the chart.\n     * @group Props\n     */\n    @Input() get options(): any {\n        return this._options;\n    }\n    set options(val: any) {\n        this._options = val;\n        this.reinit();\n    }\n    /**\n     * Callback to execute when an element on chart is clicked.\n     * @group Emits\n     */\n    @Output() onDataSelect: EventEmitter<any> = new EventEmitter<any>();\n\n    isBrowser: boolean = false;\n\n    initialized: boolean | undefined;\n\n    _data: any;\n\n    _options: any = {};\n\n    chart: any;\n\n    constructor(@Inject(PLATFORM_ID) private platformId: any, public el: ElementRef, private zone: NgZone) {}\n\n    ngAfterViewInit() {\n        this.initChart();\n        this.initialized = true;\n    }\n\n    onCanvasClick(event: Event) {\n        if (this.chart) {\n            const element = this.chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, false);\n            const dataset = this.chart.getElementsAtEventForMode(event, 'dataset', { intersect: true }, false);\n\n            if (element && element[0] && dataset) {\n                this.onDataSelect.emit({ originalEvent: event, element: element[0], dataset: dataset });\n            }\n        }\n    }\n\n    initChart() {\n        if (isPlatformBrowser(this.platformId)) {\n            let opts = this.options || {};\n            opts.responsive = this.responsive;\n\n            // allows chart to resize in responsive mode\n            if (opts.responsive && (this.height || this.width)) {\n                opts.maintainAspectRatio = false;\n            }\n\n            this.zone.runOutsideAngular(() => {\n                this.chart = new Chart(this.el.nativeElement.children[0].children[0], {\n                    type: this.type,\n                    data: this.data,\n                    options: this.options,\n                    plugins: this.plugins\n                });\n            });\n        }\n    }\n\n    getCanvas() {\n        return this.el.nativeElement.children[0].children[0];\n    }\n\n    getBase64Image() {\n        return this.chart.toBase64Image();\n    }\n\n    generateLegend() {\n        if (this.chart) {\n            return this.chart.generateLegend();\n        }\n    }\n\n    refresh() {\n        if (this.chart) {\n            this.chart.update();\n        }\n    }\n\n    reinit() {\n        if (this.chart) {\n            this.chart.destroy();\n            this.initChart();\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.chart) {\n            this.chart.destroy();\n            this.initialized = false;\n            this.chart = null;\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [UIChart],\n    declarations: [UIChart]\n})\nexport class ChartModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAGA;;;AAGG;MA6BU,OAAO,CAAA;AA0EyB,IAAA,UAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAAwB,IAAA,IAAA,CAAA;AAzEzF;;;AAGG;AACM,IAAA,IAAI,CAAiG;AAC9G;;;AAGG;IACM,OAAO,GAAU,EAAE,CAAC;AAC7B;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACH,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;IACD,IAAI,IAAI,CAAC,GAAQ,EAAA;AACb,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QACjB,IAAI,CAAC,MAAM,EAAE,CAAC;KACjB;AACD;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,GAAQ,EAAA;AAChB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QACpB,IAAI,CAAC,MAAM,EAAE,CAAC;KACjB;AACD;;;AAGG;AACO,IAAA,YAAY,GAAsB,IAAI,YAAY,EAAO,CAAC;IAEpE,SAAS,GAAY,KAAK,CAAC;AAE3B,IAAA,WAAW,CAAsB;AAEjC,IAAA,KAAK,CAAM;IAEX,QAAQ,GAAQ,EAAE,CAAC;AAEnB,IAAA,KAAK,CAAM;AAEX,IAAA,WAAA,CAAyC,UAAe,EAAS,EAAc,EAAU,IAAY,EAAA;QAA5D,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAU,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAI;IAEzG,eAAe,GAAA;QACX,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;AAED,IAAA,aAAa,CAAC,KAAY,EAAA;QACtB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;YACnG,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;YAEnG,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE;gBAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;AAC3F,aAAA;AACJ,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;;AAGlC,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;AAChD,gBAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;AACpC,aAAA;AAED,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBAClE,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,iBAAA,CAAC,CAAC;AACP,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KACxD;IAED,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;KACrC;IAED,cAAc,GAAA;QACV,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;AACtC,SAAA;KACJ;IAED,OAAO,GAAA;QACH,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;AACvB,SAAA;KACJ;IAED,MAAM,GAAA;QACF,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,EAAE,CAAC;AACpB,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACrB,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB,SAAA;KACJ;AAlJQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kBA0EI,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA1EtB,OAAO,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAyBI,gBAAgB,CAnD1B,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;AAmBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAOQ,OAAO,EAAA,UAAA,EAAA,CAAA;kBA5BnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;AAmBT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BA2EgB,MAAM;2BAAC,WAAW,CAAA;uFArEtB,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKO,IAAI,EAAA,CAAA;sBAAhB,KAAK;gBAWO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAWI,YAAY,EAAA,CAAA;sBAArB,MAAM;;MA4FE,WAAW,CAAA;uGAAX,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,EA1JX,YAAA,EAAA,CAAA,OAAO,CAsJN,EAAA,OAAA,EAAA,CAAA,YAAY,aAtJb,OAAO,CAAA,EAAA,CAAA,CAAA;AA0JP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,YAJV,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,WAAW,EAAA,UAAA,EAAA,CAAA;kBALvB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,OAAO,CAAC;oBAClB,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;AC5LD;;AAEG;;;;"}