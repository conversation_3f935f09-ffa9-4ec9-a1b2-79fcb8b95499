{"version": 3, "file": "primeng-inputtextarea.mjs", "sources": ["../../src/app/components/inputtextarea/inputtextarea.ts", "../../src/app/components/inputtextarea/primeng-inputtextarea.ts"], "sourcesContent": ["import { NgModule, Directive, ElementRef, HostListener, Input, Output, EventEmitter, Optional, AfterViewInit, OnInit, OnDestroy, ChangeDetectorRef, AfterViewChecked, booleanAttribute } from '@angular/core';\nimport { NgModel, NgControl, FormControl } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { PrimeNGConfig } from 'primeng/api';\n/**\n * InputTextarea adds styling and autoResize functionality to standard textarea element.\n * @group Components\n */\n@Directive({\n    selector: '[pInputTextarea]',\n    host: {\n        class: 'p-inputtextarea p-inputtext p-component p-element',\n        '[class.p-filled]': 'filled',\n        '[class.p-inputtextarea-resizable]': 'autoResize',\n        '[class.p-variant-filled]': 'variant === \"filled\" || config.inputStyle() === \"filled\"'\n    }\n})\nexport class InputTextarea implements OnInit, After<PERSON>iewInit, On<PERSON><PERSON>roy {\n    /**\n     * When present, textarea size changes as being typed.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoResize: boolean | undefined;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Callback to invoke on textarea resize.\n     * @param {(Event | {})} event - Custom resize event.\n     * @group Emits\n     */\n    @Output() onResize: EventEmitter<Event | {}> = new EventEmitter<Event | {}>();\n\n    filled: boolean | undefined;\n\n    cachedScrollHeight: number | undefined;\n\n    ngModelSubscription: Subscription | undefined;\n\n    ngControlSubscription: Subscription | undefined;\n\n    constructor(public el: ElementRef, @Optional() public ngModel: NgModel, @Optional() public control: NgControl, private cd: ChangeDetectorRef, public config: PrimeNGConfig) {}\n\n    ngOnInit() {\n        if (this.ngModel) {\n            this.ngModelSubscription = (this.ngModel as any).valueChanges.subscribe(() => {\n                this.updateState();\n            });\n        }\n\n        if (this.control) {\n            this.ngControlSubscription = (this.control as any).valueChanges.subscribe(() => {\n                this.updateState();\n            });\n        }\n    }\n\n    ngAfterViewInit() {\n        if (this.autoResize) this.resize();\n\n        this.updateFilledState();\n        this.cd.detectChanges();\n    }\n\n    @HostListener('input', ['$event'])\n    onInput(e: Event) {\n        this.updateState();\n    }\n\n    updateFilledState() {\n        this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n    }\n\n    resize(event?: Event) {\n        this.el.nativeElement.style.height = 'auto';\n        this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n\n        if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n            this.el.nativeElement.style.overflowY = 'scroll';\n            this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n        } else {\n            this.el.nativeElement.style.overflow = 'hidden';\n        }\n\n        this.onResize.emit(event || {});\n    }\n\n    updateState() {\n        this.updateFilledState();\n\n        if (this.autoResize) {\n            this.resize();\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.ngModelSubscription) {\n            this.ngModelSubscription.unsubscribe();\n        }\n\n        if (this.ngControlSubscription) {\n            this.ngControlSubscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [InputTextarea],\n    declarations: [InputTextarea]\n})\nexport class InputTextareaModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAKA;;;AAGG;MAUU,aAAa,CAAA;AA0BH,IAAA,EAAA,CAAA;AAAmC,IAAA,OAAA,CAAA;AAAqC,IAAA,OAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AAzBrJ;;;AAGG;AACqC,IAAA,UAAU,CAAsB;AACxE;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;;AAIG;AACO,IAAA,QAAQ,GAA6B,IAAI,YAAY,EAAc,CAAC;AAE9E,IAAA,MAAM,CAAsB;AAE5B,IAAA,kBAAkB,CAAqB;AAEvC,IAAA,mBAAmB,CAA2B;AAE9C,IAAA,qBAAqB,CAA2B;IAEhD,WAAmB,CAAA,EAAc,EAAqB,OAAgB,EAAqB,OAAkB,EAAU,EAAqB,EAAS,MAAqB,EAAA;QAAvJ,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAqB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAAqB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAW;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAE9K,QAAQ,GAAA;QACJ,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,CAAC,mBAAmB,GAAI,IAAI,CAAC,OAAe,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;gBACzE,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,aAAC,CAAC,CAAC;AACN,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,CAAC,qBAAqB,GAAI,IAAI,CAAC,OAAe,CAAC,YAAY,CAAC,SAAS,CAAC,MAAK;gBAC3E,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,UAAU;YAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QAEnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;AAGD,IAAA,OAAO,CAAC,CAAQ,EAAA;QACZ,IAAI,CAAC,WAAW,EAAE,CAAC;KACtB;IAED,iBAAiB,GAAA;QACb,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;KACnF;AAED,IAAA,MAAM,CAAC,KAAa,EAAA;QAChB,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5C,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC;QAE/E,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YACrG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC;AACjD,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC;AAC9E,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACnD,SAAA;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;KACnC;IAED,WAAW,GAAA;QACP,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC1B,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AAC1C,SAAA;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC5B,YAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;AAC5C,SAAA;KACJ;uGAxFQ,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,mFAKF,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,iCAAA,EAAA,YAAA,EAAA,wBAAA,EAAA,8DAAA,EAAA,EAAA,cAAA,EAAA,mDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAL3B,aAAa,EAAA,UAAA,EAAA,CAAA;kBATzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,mDAAmD;AAC1D,wBAAA,kBAAkB,EAAE,QAAQ;AAC5B,wBAAA,mCAAmC,EAAE,YAAY;AACjD,wBAAA,0BAA0B,EAAE,0DAA0D;AACzF,qBAAA;AACJ,iBAAA,CAAA;;0BA2BuC,QAAQ;;0BAA6B,QAAQ;qGArBzC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAMI,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAkCP,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;;MA+CxB,mBAAmB,CAAA;uGAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EAhGnB,YAAA,EAAA,CAAA,aAAa,CA4FZ,EAAA,OAAA,EAAA,CAAA,YAAY,aA5Fb,aAAa,CAAA,EAAA,CAAA,CAAA;AAgGb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,YAJlB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAL/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,aAAa,CAAC;oBACxB,YAAY,EAAE,CAAC,aAAa,CAAC;AAChC,iBAAA,CAAA;;;ACjHD;;AAEG;;;;"}