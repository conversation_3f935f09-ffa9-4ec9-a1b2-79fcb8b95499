{"version": 3, "file": "primeng-contextmenu.mjs", "sources": ["../../src/app/components/contextmenu/contextmenu.ts", "../../src/app/components/contextmenu/primeng-contextmenu.ts"], "sourcesContent": ["import { AnimationEvent, animate, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewContainerRef,\n    ViewEncapsulation,\n    ViewRef,\n    booleanAttribute,\n    effect,\n    forwardRef,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { MenuItem, OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\n\n@Component({\n    selector: 'p-contextMenuSub',\n    template: `\n        <ul\n            *ngIf=\"root ? true : visible\"\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-contextmenu-root-list': root }\"\n            [@overlayAnimation]=\"visible\"\n            (@overlayAnimation.start)=\"onEnter($event, sublist)\"\n            [attr.id]=\"menuId + '_list'\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!contextMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"contextMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!contextMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"contextMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n\n                    <p-contextMenuSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [items]=\"processedItem.items\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [menuId]=\"menuId\"\n                        [visible]=\"isItemActive(processedItem) && isItemGroup(processedItem)\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    ></p-contextMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n    animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0 })]), transition(':leave', [style({ opacity: 0 })])])],\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ContextMenuSub {\n    @Input({ transform: booleanAttribute }) visible: boolean = false;\n\n    @Input() items: any[];\n\n    @Input() itemTemplate: HTMLElement | undefined;\n\n    @Input({ transform: booleanAttribute }) root: boolean | undefined = false;\n\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n\n    @Input({ transform: booleanAttribute }) popup: boolean | undefined;\n\n    @Input() menuId: string | undefined;\n\n    @Input() ariaLabel: string | undefined;\n\n    @Input() ariaLabelledBy: string | undefined;\n\n    @Input({ transform: numberAttribute }) level: number = 0;\n\n    @Input() focusedItemId: string | undefined;\n\n    @Input() activeItemPath: any[];\n\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n\n    @Output() itemClick: EventEmitter<any> = new EventEmitter();\n\n    @Output() itemMouseEnter: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuFocus: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuBlur: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuKeydown: EventEmitter<any> = new EventEmitter();\n\n    @ViewChild('sublist') sublistViewChild: ElementRef;\n\n    constructor(@Inject(DOCUMENT) private document: Document, public el: ElementRef, public renderer: Renderer2, private cd: ChangeDetectorRef, @Inject(forwardRef(() => ContextMenu)) public contextMenu: ContextMenu, private ref: ViewContainerRef) {}\n\n    getItemProp(processedItem: any, name: string, params: any | null = null) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n\n    getItemId(processedItem: any): string {\n        return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n    }\n\n    getItemKey(processedItem: any): string {\n        return this.getItemId(processedItem);\n    }\n\n    getItemClass(processedItem: any) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem': true,\n            'p-highlight': this.isItemActive(processedItem),\n            'p-menuitem-active': this.isItemActive(processedItem),\n            'p-focus': this.isItemFocused(processedItem),\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n\n    getItemLabel(processedItem: any): string {\n        return this.getItemProp(processedItem, 'label');\n    }\n\n    getSeparatorItemClass(processedItem: any) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem-separator': true\n        };\n    }\n\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n\n    getAriaPosInset(index: number) {\n        return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n\n    isItemVisible(processedItem: any): boolean {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n\n    isItemActive(processedItem: any): boolean {\n        if (this.activeItemPath) {\n            return this.activeItemPath.some((path) => path.key === processedItem.key);\n        }\n    }\n\n    isItemDisabled(processedItem: any): boolean {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n\n    isItemFocused(processedItem: any): boolean {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n\n    isItemGroup(processedItem: any): boolean {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    onItemMouseEnter(param: any) {\n        const { event, processedItem } = param;\n        this.itemMouseEnter.emit({ originalEvent: event, processedItem });\n    }\n\n    onItemClick(event: any, processedItem: any) {\n        this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n        this.itemClick.emit({ originalEvent: event, processedItem, isFocus: true });\n    }\n\n    onEnter(event, sublist) {\n        if (event.fromState === 'void' && event.toState) {\n            const sublist = event.element;\n            this.position(sublist);\n        }\n    }\n\n    position(sublist) {\n        const parentItem = sublist.parentElement.parentElement;\n        const containerOffset = DomHandler.getOffset(sublist.parentElement.parentElement);\n        const viewport = DomHandler.getViewport();\n        const sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getHiddenElementOuterWidth(sublist);\n        const itemOuterWidth = DomHandler.getOuterWidth(parentItem.children[0]);\n\n        sublist.style.top = '0px';\n\n        if (parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth > viewport.width - DomHandler.calculateScrollbarWidth()) {\n            sublist.style.left = -1 * sublistWidth + 'px';\n        } else {\n            sublist.style.left = itemOuterWidth + 'px';\n        }\n    }\n}\n/**\n * ContextMenu displays an overlay menu on right click of its target. Note that components like Table has special integration with ContextMenu.\n * @group Components\n */\n@Component({\n    selector: 'p-contextMenu',\n    template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'contextmenu'\"\n            [attr.id]=\"id\"\n            [ngClass]=\"{ 'p-contextmenu p-component': true, 'p-contextmenu-overlay': true }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            [@overlayAnimation]=\"{ value: 'visible' }\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"visible()\"\n        >\n            <p-contextMenuSub\n                #rootmenu\n                [root]=\"true\"\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [visible]=\"submenuVisible()\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-contextMenuSub>\n        </div>\n    `,\n    animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0 }), animate('250ms')]), transition(':leave', [animate('.1s linear', style({ opacity: 0 }))])])],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./contextmenu.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ContextMenu implements OnInit, AfterContentInit, OnDestroy {\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    @Input() set model(value: MenuItem[] | undefined) {\n        this._model = value;\n        this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model(): MenuItem[] | undefined {\n        return this._model;\n    }\n    /**\n     * Event for which the menu must be displayed.\n     * @group Props\n     */\n    @Input() triggerEvent: string = 'contextmenu';\n    /**\n     * Local template variable name of the element to attach the context menu.\n     * @group Props\n     */\n    @Input() target: HTMLElement | string | undefined;\n    /**\n     * Attaches the menu to document instead of a particular item.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) global: boolean;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element.\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Press delay in touch devices as miliseconds.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) pressDelay: number | undefined = 500;\n    /**\n     * Callback to invoke when overlay menu is shown.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<null> = new EventEmitter<null>();\n    /**\n     * Callback to invoke when overlay menu is hidden.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<null> = new EventEmitter<null>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @ViewChild('rootmenu') rootmenu: ContextMenuSub | undefined;\n\n    @ViewChild('container') containerViewChild: ElementRef<any> | undefined;\n\n    submenuIconTemplate: Nullable<TemplateRef<any>>;\n\n    itemTemplate: Nullable<TemplateRef<any>>;\n\n    container: HTMLDivElement | undefined;\n\n    outsideClickListener: VoidListener;\n\n    resizeListener: VoidListener;\n\n    triggerEventListener: VoidListener;\n\n    documentClickListener: VoidListener;\n\n    documentTriggerListener: VoidListener;\n\n    touchEndListener: VoidListener;\n\n    pageX: number;\n\n    pageY: number;\n\n    visible = signal(false);\n\n    relativeAlign: boolean | undefined;\n\n    private window: Window;\n\n    focused: boolean = false;\n\n    activeItemPath = signal<any>([]);\n\n    focusedItemInfo = signal<any>({ index: -1, level: 0, parentKey: '', item: null });\n\n    submenuVisible = signal<boolean>(false);\n\n    searchValue: string = '';\n\n    searchTimeout: any;\n\n    _processedItems: any[];\n\n    _model: MenuItem[] | undefined;\n\n    pressTimer: any;\n\n    get visibleItems() {\n        const processedItem = this.activeItemPath().find((p) => p.key === this.focusedItemInfo().parentKey);\n\n        return processedItem ? processedItem.items : this.processedItems;\n    }\n\n    get processedItems() {\n        if (!this._processedItems || !this._processedItems.length) {\n            this._processedItems = this.createProcessedItems(this.model || []);\n        }\n        return this._processedItems;\n    }\n\n    get focusedItemId() {\n        const focusedItem = this.focusedItemInfo();\n        return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n    }\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        public el: ElementRef,\n        public renderer: Renderer2,\n        public cd: ChangeDetectorRef,\n        public config: PrimeNGConfig,\n        public overlayService: OverlayService\n    ) {\n        this.window = this.document.defaultView as Window;\n        effect(() => {\n            const path = this.activeItemPath();\n\n            if (ObjectUtils.isNotEmpty(path)) {\n                this.bindGlobalListeners();\n            } else if (!this.visible()) {\n                this.unbindGlobalListeners();\n            }\n        });\n    }\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n        this.bindTriggerEventListener();\n    }\n\n    isMobile() {\n        return DomHandler.isIOS() || DomHandler.isAndroid();\n    }\n\n    bindTriggerEventListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.triggerEventListener) {\n                if (!this.isMobile()) {\n                    if (this.global) {\n                        this.triggerEventListener = this.renderer.listen(this.document, this.triggerEvent, (event) => {\n                            this.show(event);\n                        });\n                    } else if (this.target) {\n                        this.triggerEventListener = this.renderer.listen(this.target, this.triggerEvent, (event) => {\n                            this.show(event);\n                        });\n                    }\n                } else {\n                    if (this.global) {\n                        this.triggerEventListener = this.renderer.listen(this.document, 'touchstart', this.onTouchStart.bind(this));\n                        this.touchEndListener = this.renderer.listen(this.document, 'touchend', this.onTouchEnd.bind(this));\n                    } else if (this.target) {\n                        this.triggerEventListener = this.renderer.listen(this.target, 'touchstart', this.onTouchStart.bind(this));\n                        this.touchEndListener = this.renderer.listen(this.target, 'touchend', this.onTouchEnd.bind(this));\n                    }\n                }\n            }\n        }\n    }\n\n    bindGlobalListeners() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.documentClickListener) {\n                const documentTarget: any = this.el ? this.el.nativeElement.ownerDocument : 'document';\n\n                this.documentClickListener = this.renderer.listen(documentTarget, 'click', (event) => {\n                    if (this.containerViewChild.nativeElement.offsetParent && this.isOutsideClicked(event) && !event.ctrlKey && event.button !== 2 && this.triggerEvent !== 'click') {\n                        this.hide();\n                    }\n                });\n\n                this.documentTriggerListener = this.renderer.listen(documentTarget, this.triggerEvent, (event) => {\n                    if (this.containerViewChild.nativeElement.offsetParent && this.isOutsideClicked(event)) {\n                        this.hide();\n                    }\n                });\n            }\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    this.hide();\n                });\n            }\n        }\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    createProcessedItems(items: any, level: number = 0, parent: any = {}, parentKey: any = '') {\n        const processedItems = [];\n\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newItem = {\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n\n                newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n\n        return processedItems;\n    }\n\n    getItemProp(item: any, name: string) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n\n    getProccessedItemLabel(processedItem: any) {\n        return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n\n    getItemLabel(item: any) {\n        return this.getItemProp(item, 'label');\n    }\n\n    isProcessedItemGroup(processedItem: any): boolean {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    isSelected(processedItem: any): boolean {\n        return this.activeItemPath().some((p) => p.key === processedItem.key);\n    }\n\n    isValidSelectedItem(processedItem: any): boolean {\n        return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n\n    isValidItem(processedItem: any): boolean {\n        return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n    }\n\n    isItemDisabled(item: any): boolean {\n        return this.getItemProp(item, 'disabled');\n    }\n\n    isItemSeparator(item: any): boolean {\n        return this.getItemProp(item, 'separator');\n    }\n\n    isItemMatched(processedItem: any): boolean {\n        return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n\n    isProccessedItemGroup(processedItem: any): boolean {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    onItemClick(event: any) {\n        const { processedItem } = event;\n        const grouped = this.isProcessedItemGroup(processedItem);\n        const selected = this.isSelected(processedItem);\n\n        if (selected) {\n            const { index, key, level, parentKey, item } = processedItem;\n\n            this.activeItemPath.set(this.activeItemPath().filter((p) => key !== p.key && key.startsWith(p.key)));\n            this.focusedItemInfo.set({ index, level, parentKey, item });\n\n            DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n        } else {\n            grouped ? this.onItemChange(event) : this.hide();\n        }\n    }\n\n    onItemMouseEnter(event: any) {\n        this.onItemChange(event);\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        const metaKey = event.metaKey || event.ctrlKey;\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            case 'PageDown':\n            case 'PageUp':\n            case 'Backspace':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchItems(event, event.key);\n                }\n\n                break;\n        }\n    }\n\n    onArrowDownKey(event: KeyboardEvent) {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n\n        this.changeFocusedItemIndex(event, itemIndex);\n        event.preventDefault();\n    }\n\n    onArrowRightKey(event: KeyboardEvent) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n\n        if (grouped) {\n            this.onItemChange({ originalEvent: event, processedItem });\n            this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n            this.searchValue = '';\n            this.onArrowDownKey(event);\n        }\n\n        event.preventDefault();\n    }\n\n    onArrowUpKey(event: KeyboardEvent) {\n        if (event.altKey) {\n            if (this.focusedItemInfo().index !== -1) {\n                const processedItem = this.visibleItems[this.focusedItemInfo().index];\n                const grouped = this.isProccessedItemGroup(processedItem);\n\n                !grouped && this.onItemChange({ originalEvent: event, processedItem });\n            }\n\n            this.hide();\n            event.preventDefault();\n        } else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n\n    onArrowLeftKey(event: KeyboardEvent) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = this.activeItemPath().find((p) => p.key === processedItem.parentKey);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n\n        if (!root) {\n            this.focusedItemInfo.set({ index: -1, parentKey: parentItem ? parentItem.parentKey : '', item: processedItem.item });\n            this.searchValue = '';\n            this.onArrowDownKey(event);\n        }\n\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n        this.activeItemPath.set(activeItemPath);\n\n        event.preventDefault();\n    }\n\n    onHomeKey(event: KeyboardEvent) {\n        this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n        event.preventDefault();\n    }\n\n    onEndKey(event: KeyboardEvent) {\n        this.changeFocusedItemIndex(event, this.findLastItemIndex());\n        event.preventDefault();\n    }\n\n    onSpaceKey(event: KeyboardEvent) {\n        this.onEnterKey(event);\n    }\n\n    onEscapeKey(event: KeyboardEvent) {\n        this.hide();\n        const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n        const focusedItemInfo = this.focusedItemInfo();\n        this.focusedItemInfo.set({ ...focusedItemInfo, index: this.findFirstFocusedItemIndex(), item: processedItem.item });\n\n        event.preventDefault();\n    }\n\n    onTabKey(event: KeyboardEvent) {\n        if (this.focusedItemInfo().index !== -1) {\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            !grouped && this.onItemChange({ originalEvent: event, processedItem });\n        }\n\n        this.hide();\n    }\n\n    onEnterKey(event: KeyboardEvent) {\n        if (this.focusedItemInfo().index !== -1) {\n            const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n            const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n\n            anchorElement ? anchorElement.click() : element && element.click();\n\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            if (!grouped) {\n                const focusedItemInfo = this.focusedItemInfo();\n                this.focusedItemInfo.set({ ...focusedItemInfo, index: this.findFirstFocusedItemIndex() });\n            }\n        }\n\n        event.preventDefault();\n    }\n\n    onItemChange(event: any) {\n        const { processedItem, isFocus } = event;\n        if (ObjectUtils.isEmpty(processedItem)) return;\n\n        const { index, key, level, parentKey, items } = processedItem;\n        const grouped = ObjectUtils.isNotEmpty(items);\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n\n        if (grouped) {\n            activeItemPath.push(processedItem);\n            this.submenuVisible.set(true);\n        }\n        this.focusedItemInfo.set({ index, level, parentKey, item: processedItem.item });\n        this.activeItemPath.set(activeItemPath);\n\n        isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n    }\n\n    onMenuFocus(event: any) {\n        this.focused = true;\n        const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : { index: -1, level: 0, parentKey: '', item: null };\n\n        this.focusedItemInfo.set(focusedItemInfo);\n    }\n\n    onMenuBlur(event: any) {\n        this.focused = false;\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        this.searchValue = '';\n    }\n\n    onOverlayAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                this.container = event.element;\n                this.position();\n                this.moveOnTop();\n                this.appendOverlay();\n                this.bindGlobalListeners();\n                DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n                break;\n        }\n    }\n\n    onOverlayAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                this.onOverlayHide();\n                break;\n        }\n    }\n\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.containerViewChild.nativeElement);\n            else DomHandler.appendChild(this.containerViewChild.nativeElement, this.appendTo);\n        }\n    }\n\n    moveOnTop() {\n        if (this.autoZIndex && this.containerViewChild) {\n            ZIndexUtils.set('menu', this.containerViewChild.nativeElement, this.baseZIndex + this.config.zIndex.menu);\n        }\n    }\n\n    onOverlayHide() {\n        this.unbindGlobalListeners();\n\n        if (!(this.cd as ViewRef).destroyed) {\n            this.target = null;\n        }\n\n        if (this.container && this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n\n        this.container = null;\n    }\n\n    onTouchStart(event: MouseEvent) {\n        this.pressTimer = setTimeout(() => {\n            this.show(event);\n        }, this.pressDelay);\n    }\n\n    onTouchEnd() {\n        clearTimeout(this.pressTimer);\n    }\n\n    hide() {\n        this.visible.set(false);\n        this.onHide.emit();\n        this.activeItemPath.set([]);\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n    }\n\n    toggle(event?: any) {\n        this.visible() ? this.hide() : this.show(event);\n    }\n\n    show(event: any) {\n        this.activeItemPath.set([]);\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n\n        this.pageX = event.pageX;\n        this.pageY = event.pageY;\n\n        this.onShow.emit();\n        this.visible() ? this.position() : this.visible.set(true);\n\n        event.stopPropagation();\n        event.preventDefault();\n    }\n\n    position() {\n        let left = this.pageX + 1;\n        let top = this.pageY + 1;\n        let width = this.containerViewChild.nativeElement.offsetParent ? this.containerViewChild.nativeElement.offsetWidth : DomHandler.getHiddenElementOuterWidth(this.containerViewChild.nativeElement);\n        let height = this.containerViewChild.nativeElement.offsetParent ? this.containerViewChild.nativeElement.offsetHeight : DomHandler.getHiddenElementOuterHeight(this.containerViewChild.nativeElement);\n        let viewport = DomHandler.getViewport();\n\n        //flip\n        if (left + width - this.document.scrollingElement.scrollLeft > viewport.width) {\n            left -= width;\n        }\n\n        //flip\n        if (top + height - this.document.scrollingElement.scrollTop > viewport.height) {\n            top -= height;\n        }\n\n        //fit\n        if (left < this.document.scrollingElement.scrollLeft) {\n            left = this.document.scrollingElement.scrollLeft;\n        }\n\n        //fit\n        if (top < this.document.scrollingElement.scrollTop) {\n            top = this.document.scrollingElement.scrollTop;\n        }\n\n        this.containerViewChild.nativeElement.style.left = left + 'px';\n        this.containerViewChild.nativeElement.style.top = top + 'px';\n    }\n\n    searchItems(event: any, char: string) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let itemIndex = -1;\n        let matched = false;\n\n        if (this.focusedItemInfo().index !== -1) {\n            itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem));\n            itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n        } else {\n            itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n        }\n\n        if (itemIndex !== -1) {\n            matched = true;\n        }\n\n        if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n            itemIndex = this.findFirstFocusedItemIndex();\n        }\n\n        if (itemIndex !== -1) {\n            this.changeFocusedItemIndex(event, itemIndex);\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n\n    findVisibleItem(index) {\n        return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n    }\n\n    findLastFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n\n    findLastItemIndex() {\n        return ObjectUtils.findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n    }\n\n    findPrevItemIndex(index: number) {\n        const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n\n        return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n\n    findNextItemIndex(index: number) {\n        const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n\n        return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n\n    findFirstFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n\n        return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n\n    findFirstItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n    }\n\n    findSelectedItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n    }\n\n    changeFocusedItemIndex(event: any, index: number) {\n        const processedItem = this.findVisibleItem(index);\n        const focusedItemInfo = this.focusedItemInfo();\n        if (focusedItemInfo.index !== index) {\n            this.focusedItemInfo.set({ ...focusedItemInfo, index, item: processedItem.item });\n            this.scrollInView();\n        }\n    }\n\n    scrollInView(index: number = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n        const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    this.hide();\n                });\n            }\n        }\n    }\n\n    isOutsideClicked(event: Event) {\n        return !(this.containerViewChild.nativeElement.isSameNode(event.target) || this.containerViewChild.nativeElement.contains(event.target));\n    }\n\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n\n    unbindGlobalListeners() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n\n        if (this.documentTriggerListener) {\n            this.documentTriggerListener();\n            this.documentTriggerListener = null;\n        }\n\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n\n        if (this.touchEndListener) {\n            this.touchEndListener();\n            this.touchEndListener = null;\n        }\n    }\n\n    unbindTriggerEventListener() {\n        if (this.triggerEventListener) {\n            this.triggerEventListener();\n            this.triggerEventListener = null;\n        }\n    }\n\n    removeAppendedElements() {\n        if (this.appendTo && this.containerViewChild) {\n            if (this.appendTo === 'body') {\n                this.renderer.removeChild(this.document.body, this.containerViewChild.nativeElement);\n            } else {\n                DomHandler.removeChild(this.containerViewChild.nativeElement, this.appendTo);\n            }\n        }\n    }\n\n    ngOnDestroy() {\n        this.unbindGlobalListeners();\n        this.unbindTriggerEventListener();\n        this.removeAppendedElements();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule],\n    exports: [ContextMenu, RouterModule, TooltipModule, SharedModule],\n    declarations: [ContextMenu, ContextMenuSub]\n})\nexport class ContextMenuModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;MAsMa,cAAc,CAAA;AAyCe,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA6B,IAAA,EAAA,CAAA;AAAqE,IAAA,WAAA,CAAA;AAAkC,IAAA,GAAA,CAAA;IAxCpL,OAAO,GAAY,KAAK,CAAC;AAExD,IAAA,KAAK,CAAQ;AAEb,IAAA,YAAY,CAA0B;IAEP,IAAI,GAAwB,KAAK,CAAC;IAElC,UAAU,GAAY,IAAI,CAAC;IAE5B,UAAU,GAAW,CAAC,CAAC;AAEtB,IAAA,KAAK,CAAsB;AAE1D,IAAA,MAAM,CAAqB;AAE3B,IAAA,SAAS,CAAqB;AAE9B,IAAA,cAAc,CAAqB;IAEL,KAAK,GAAW,CAAC,CAAC;AAEhD,IAAA,aAAa,CAAqB;AAElC,IAAA,cAAc,CAAQ;IAEQ,QAAQ,GAAW,CAAC,CAAC;AAElD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElD,IAAA,cAAc,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEvD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElD,IAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEjD,IAAA,WAAW,GAAsB,IAAI,YAAY,EAAE,CAAC;AAExC,IAAA,gBAAgB,CAAa;IAEnD,WAAsC,CAAA,QAAkB,EAAS,EAAc,EAAS,QAAmB,EAAU,EAAqB,EAAgD,WAAwB,EAAU,GAAqB,EAAA;QAA3M,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAgD,IAAW,CAAA,WAAA,GAAX,WAAW,CAAa;QAAU,IAAG,CAAA,GAAA,GAAH,GAAG,CAAkB;KAAI;AAErP,IAAA,WAAW,CAAC,aAAkB,EAAE,IAAY,EAAE,SAAqB,IAAI,EAAA;QACnE,OAAO,aAAa,IAAI,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC;KACvH;AAED,IAAA,SAAS,CAAC,aAAkB,EAAA;AACxB,QAAA,OAAO,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAI,CAAA,EAAA,aAAa,CAAC,GAAG,EAAE,CAAC;KACvH;AAED,IAAA,UAAU,CAAC,aAAkB,EAAA;AACzB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KACxC;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;AAC3C,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AAC/C,YAAA,mBAAmB,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AACrD,YAAA,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;AAC5C,YAAA,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;SACnD,CAAC;KACL;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;KACnD;AAED,IAAA,qBAAqB,CAAC,aAAkB,EAAA;QACpC,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;AAC3C,YAAA,sBAAsB,EAAE,IAAI;SAC/B,CAAC;KACL;IAED,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;KAC1I;AAED,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACrK;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK,CAAC;KAC/D;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;AAC7E,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,aAAkB,EAAA;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KACtD;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KAC/D;AAED,IAAA,WAAW,CAAC,aAAkB,EAAA;QAC1B,OAAO,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACtD;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;AACvB,QAAA,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACvC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;KACrE;IAED,WAAW,CAAC,KAAU,EAAE,aAAkB,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/F,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;KAC/E;IAED,OAAO,CAAC,KAAK,EAAE,OAAO,EAAA;QAClB,IAAI,KAAK,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7C,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC9B,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,OAAO,EAAA;AACZ,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC;AACvD,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAClF,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACjH,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAExE,QAAA,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;QAE1B,IAAI,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,cAAc,GAAG,YAAY,GAAG,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,uBAAuB,EAAE,EAAE;YAC5H,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;AACjD,SAAA;AAAM,aAAA;YACH,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,cAAc,GAAG,IAAI,CAAC;AAC9C,SAAA;KACJ;uGA1IQ,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAyCH,QAAQ,EAAwH,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,UAAU,CAAC,MAAM,WAAW,CAAC,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAzCxK,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,cAAc,EACH,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAMhB,EAAA,KAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,4CAEhB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAEhB,eAAe,CAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAEf,gBAAgB,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAQhB,eAAe,CAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAMf,eAAe,CAxLzB,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsJT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAo/BkE,cAAc,CA7+BxE,EAAA,QAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,cAAc,8TANX,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAMhI,cAAc,EAAA,UAAA,EAAA,CAAA;kBA/J1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsJT,IAAA,CAAA;AACD,oBAAA,UAAU,EAAE,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzI,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BA0CgB,MAAM;2BAAC,QAAQ,CAAA;;0BAAiH,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,WAAW,CAAC,CAAA;wEAxCzI,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE3B,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAEG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAEe,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;;AAqGxB;;;AAGG;MA+CU,WAAW,CAAA;AA0JU,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACtB,IAAA,EAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACA,IAAA,MAAA,CAAA;AACA,IAAA,cAAA,CAAA;AA/JX;;;AAGG;IACH,IAAa,KAAK,CAAC,KAA6B,EAAA;AAC5C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;KACvE;AACD,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AACD;;;AAGG;IACM,YAAY,GAAW,aAAa,CAAC;AAC9C;;;AAGG;AACM,IAAA,MAAM,CAAmC;AAClD;;;AAGG;AACqC,IAAA,MAAM,CAAU;AACxD;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACoC,UAAU,GAAuB,GAAG,CAAC;AAC5E;;;AAGG;AACO,IAAA,MAAM,GAAuB,IAAI,YAAY,EAAQ,CAAC;AAChE;;;AAGG;AACO,IAAA,MAAM,GAAuB,IAAI,YAAY,EAAQ,CAAC;AAEhC,IAAA,SAAS,CAAuC;AAEzD,IAAA,QAAQ,CAA6B;AAEpC,IAAA,kBAAkB,CAA8B;AAExE,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,YAAY,CAA6B;AAEzC,IAAA,SAAS,CAA6B;AAEtC,IAAA,oBAAoB,CAAe;AAEnC,IAAA,cAAc,CAAe;AAE7B,IAAA,oBAAoB,CAAe;AAEnC,IAAA,qBAAqB,CAAe;AAEpC,IAAA,uBAAuB,CAAe;AAEtC,IAAA,gBAAgB,CAAe;AAE/B,IAAA,KAAK,CAAS;AAEd,IAAA,KAAK,CAAS;AAEd,IAAA,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAExB,IAAA,aAAa,CAAsB;AAE3B,IAAA,MAAM,CAAS;IAEvB,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,cAAc,GAAG,MAAM,CAAM,EAAE,CAAC,CAAC;IAEjC,eAAe,GAAG,MAAM,CAAM,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAElF,IAAA,cAAc,GAAG,MAAM,CAAU,KAAK,CAAC,CAAC;IAExC,WAAW,GAAW,EAAE,CAAC;AAEzB,IAAA,aAAa,CAAM;AAEnB,IAAA,eAAe,CAAQ;AAEvB,IAAA,MAAM,CAAyB;AAE/B,IAAA,UAAU,CAAM;AAEhB,IAAA,IAAI,YAAY,GAAA;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC;AAEpG,QAAA,OAAO,aAAa,GAAG,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;KACpE;AAED,IAAA,IAAI,cAAc,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AACvD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACtE,SAAA;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;AAED,IAAA,IAAI,aAAa,GAAA;AACb,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3C,QAAA,OAAO,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAG,EAAA,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC,SAAS,GAAG,EAAE,CAAI,CAAA,EAAA,WAAW,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC;KAClO;AAED,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACrC,EAAc,EACd,QAAmB,EACnB,EAAqB,EACrB,MAAqB,EACrB,cAA8B,EAAA;QANX,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACrC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACrB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACrB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QAErC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;QAClD,MAAM,CAAC,MAAK;AACR,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAEnC,YAAA,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC9B,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC9B,aAAA;AAAM,iBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;gBACxB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAChC,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;QACzC,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACnC;IAED,QAAQ,GAAA;QACJ,OAAO,UAAU,CAAC,KAAK,EAAE,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;KACvD;IAED,wBAAwB,GAAA;AACpB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;oBAClB,IAAI,IAAI,CAAC,MAAM,EAAE;wBACb,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,KAAI;AACzF,4BAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,yBAAC,CAAC,CAAC;AACN,qBAAA;yBAAM,IAAI,IAAI,CAAC,MAAM,EAAE;wBACpB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,KAAI;AACvF,4BAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,yBAAC,CAAC,CAAC;AACN,qBAAA;AACJ,iBAAA;AAAM,qBAAA;oBACH,IAAI,IAAI,CAAC,MAAM,EAAE;wBACb,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC5G,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACvG,qBAAA;yBAAM,IAAI,IAAI,CAAC,MAAM,EAAE;wBACpB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC1G,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACrG,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,mBAAmB,GAAA;AACf,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AAC7B,gBAAA,MAAM,cAAc,GAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,GAAG,UAAU,CAAC;AAEvF,gBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;AACjF,oBAAA,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE;wBAC7J,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AACL,iBAAC,CAAC,CAAC;AAEH,gBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,KAAI;AAC7F,oBAAA,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;wBACpF,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AACD,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC,KAAK,KAAI;oBACtF,IAAI,CAAC,IAAI,EAAE,CAAC;AAChB,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AACV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,oBAAoB,CAAC,KAAU,EAAE,KAAgB,GAAA,CAAC,EAAE,MAAc,GAAA,EAAE,EAAE,SAAA,GAAiB,EAAE,EAAA;QACrF,MAAM,cAAc,GAAG,EAAE,CAAC;QAE1B,KAAK;YACD,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AAC1B,gBAAA,MAAM,GAAG,GAAG,CAAC,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,GAAG,EAAE,IAAI,KAAK,CAAC;AAC9D,gBAAA,MAAM,OAAO,GAAG;oBACZ,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,GAAG;oBACH,MAAM;oBACN,SAAS;iBACZ,CAAC;gBAEF,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AAClF,gBAAA,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;AAEP,QAAA,OAAO,cAAc,CAAC;KACzB;IAED,WAAW,CAAC,IAAS,EAAE,IAAY,EAAA;AAC/B,QAAA,OAAO,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;KAClE;AAED,IAAA,sBAAsB,CAAC,aAAkB,EAAA;AACrC,QAAA,OAAO,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;KAC5E;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC1C;AAED,IAAA,oBAAoB,CAAC,aAAkB,EAAA;QACnC,OAAO,aAAa,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvE;AAED,IAAA,UAAU,CAAC,aAAkB,EAAA;QACzB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;KACzE;AAED,IAAA,mBAAmB,CAAC,aAAkB,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KAC5E;AAED,IAAA,WAAW,CAAC,aAAkB,EAAA;QAC1B,OAAO,CAAC,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KACnH;AAED,IAAA,cAAc,CAAC,IAAS,EAAA;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC7C;AAED,IAAA,eAAe,CAAC,IAAS,EAAA;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;KAC9C;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,iBAAiB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC;KAC7J;AAED,IAAA,qBAAqB,CAAC,aAAkB,EAAA;QACpC,OAAO,aAAa,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvE;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;AAClB,QAAA,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AAEhD,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;AAE7D,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrG,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE5D,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAClE,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACpD,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QAE/C,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;;gBAEb,MAAM;AAEV,YAAA;gBACI,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAEhJ,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC9C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;AAChC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAChG,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAoB,EAAA;QAC7B,IAAI,KAAK,CAAC,MAAM,EAAE;YACd,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;gBACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,gBAAA,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AAC1E,aAAA;YAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAE/I,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC9C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,CAAC,SAAS,CAAC,CAAC;QACxF,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,EAAE;AACP,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,UAAU,GAAG,UAAU,CAAC,SAAS,GAAG,EAAE,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AACrH,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,SAAA;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC;AAC7G,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAExC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC9D,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAC7D,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;QAC5B,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;AAC7E,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC,yBAAyB,EAAE,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;QAEpH,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,YAAA,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AAC1E,SAAA;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;KACf;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;QAC3B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;YACrC,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,CAAG,EAAA,IAAI,CAAC,aAAa,CAAA,CAAE,CAAI,EAAA,CAAA,CAAC,CAAC;AAC7G,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC;AAE/F,YAAA,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;AAEnE,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAE1D,IAAI,CAAC,OAAO,EAAE;AACV,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/C,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;AAC7F,aAAA;AACJ,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAU,EAAA;AACnB,QAAA,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;AACzC,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC;YAAE,OAAO;AAE/C,QAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,aAAa,CAAC;QAC9D,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,CAAC,SAAS,KAAK,GAAG,CAAC,CAAC;AAE7G,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACnC,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAChF,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAExC,QAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;KAC7E;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAE1I,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;KAC7C;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7E,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;KACzB;AAED,IAAA,uBAAuB,CAAC,KAAqB,EAAA;QACzC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC/B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAC/D,MAAM;AACb,SAAA;KACJ;AAED,IAAA,qBAAqB,CAAC,KAAqB,EAAA;QACvC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,MAAM;AACb,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;AAAE,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;;AAC9G,gBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrF,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC5C,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7G,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAE7B,QAAA,IAAI,CAAE,IAAI,CAAC,EAAc,CAAC,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB;AAED,IAAA,YAAY,CAAC,KAAiB,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,MAAK;AAC9B,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,SAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;KACvB;IAED,UAAU,GAAA;AACN,QAAA,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACjC;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;KAChF;AAED,IAAA,MAAM,CAAC,KAAW,EAAA;AACd,QAAA,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnD;AAED,IAAA,IAAI,CAAC,KAAU,EAAA;AACX,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAE7E,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAEzB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACnB,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE1D,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AAC1B,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACzB,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,GAAG,UAAU,CAAC,0BAA0B,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;AAClM,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,GAAG,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACrM,QAAA,IAAI,QAAQ,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;;AAGxC,QAAA,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAE;YAC3E,IAAI,IAAI,KAAK,CAAC;AACjB,SAAA;;AAGD,QAAA,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAE;YAC3E,GAAG,IAAI,MAAM,CAAC;AACjB,SAAA;;QAGD,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE;YAClD,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACpD,SAAA;;QAGD,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE;YAChD,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC;AAClD,SAAA;AAED,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC/D,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;KAChE;IAED,WAAW,CAAC,KAAU,EAAE,IAAY,EAAA;AAChC,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;AAEnD,QAAA,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;QACnB,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;YAClI,SAAS,GAAG,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC;AACtM,SAAA;AAAM,aAAA;AACH,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;AACjG,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;YAClB,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACzD,YAAA,SAAS,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAChD,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;AAClB,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACjD,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACtF;IAED,wBAAwB,GAAA;AACpB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACnD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,aAAa,CAAC;KACvE;IAED,iBAAiB,GAAA;QACb,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC3G;AAED,IAAA,iBAAiB,CAAC,KAAa,EAAA;AAC3B,QAAA,MAAM,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE3J,QAAA,OAAO,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,GAAG,KAAK,CAAC;KAC3D;AAED,IAAA,iBAAiB,CAAC,KAAa,EAAA;QAC3B,MAAM,gBAAgB,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEtK,QAAA,OAAO,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KACvE;IAED,yBAAyB,GAAA;AACrB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAEnD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,aAAa,CAAC;KACxE;IAED,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC1F;IAED,qBAAqB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC;KAClG;IAED,sBAAsB,CAAC,KAAU,EAAE,KAAa,EAAA;QAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAClD,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/C,QAAA,IAAI,eAAe,CAAC,KAAK,KAAK,KAAK,EAAE;AACjC,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;YAClF,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;KACJ;IAED,YAAY,CAAC,KAAgB,GAAA,CAAC,CAAC,EAAA;QAC3B,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,aAAa,CAAC;AACrE,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAExF,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC,KAAK,KAAI;oBACtF,IAAI,CAAC,IAAI,EAAE,CAAC;AAChB,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAY,EAAA;AACzB,QAAA,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;KAC5I;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;KACJ;IAED,qBAAqB,GAAA;QACjB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACvC,SAAA;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAChC,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,sBAAsB,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC1C,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;AAC1B,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACxF,aAAA;AAAM,iBAAA;AACH,gBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChF,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,sBAAsB,EAAE,CAAC;KACjC;uGA3yBQ,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA0JR,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA3Jd,WAAW,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EA0BA,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAoBhB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,eAAe,CAoBf,EAAA,EAAA,EAAA,IAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,eAAe,CAYlB,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA/HpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmCT,EArLQ,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,giBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,cAAc,6TAsLX,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQzK,WAAW,EAAA,UAAA,EAAA,CAAA;kBA9CvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmCT,EACW,UAAA,EAAA,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjK,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,giBAAA,CAAA,EAAA,CAAA;;0BA4JI,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;mLAtJV,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAWG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKiC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK3B,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEP,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;gBAEG,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;;MA4tBb,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAjB,iBAAiB,EAAA,YAAA,EAAA,CAnzBjB,WAAW,EA9LX,cAAc,aA6+Bb,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,CA/yBtF,EAAA,OAAA,EAAA,CAAA,WAAW,EAgzBG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;AAGvD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAJhB,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EACxE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGvD,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,CAAC;oBAChG,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;AACjE,oBAAA,YAAY,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC;AAC9C,iBAAA,CAAA;;;ACtrCD;;AAEG;;;;"}