{"version": 3, "file": "primeng-splitter.mjs", "sources": ["../../src/app/components/splitter/splitter.ts", "../../src/app/components/splitter/primeng-splitter.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, ElementRef, EventEmitter, Inject, Input, NgModule, Output, PLATFORM_ID, QueryList, Renderer2, ViewChild, ViewEncapsulation, numberAttribute } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { SplitterResizeEndEvent, SplitterResizeStartEvent } from './splitter.interface';\n/**\n * Splitter is utilized to separate and resize panels.\n * @group Components\n */\n@Component({\n    selector: 'p-splitter',\n    template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-name]=\"'splitter'\" [attr.data-p-gutter-resizing]=\"false\" [attr.data-pc-section]=\"'root'\">\n            <ng-template ngFor let-panel [ngForOf]=\"panels\" let-i=\"index\">\n                <div [ngClass]=\"panelContainerClass()\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\" tabindex=\"-1\" [attr.data-pc-name]=\"'splitter'\" [attr.data-pc-section]=\"'root'\">\n                    <ng-container *ngTemplateOutlet=\"panel\"></ng-container>\n                </div>\n                <div\n                    *ngIf=\"i !== panels.length - 1\"\n                    class=\"p-splitter-gutter\"\n                    role=\"separator\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onGutterMouseDown($event, i)\"\n                    (touchstart)=\"onGutterTouchStart($event, i)\"\n                    (touchmove)=\"onGutterTouchMove($event)\"\n                    (touchend)=\"onGutterTouchEnd($event, i)\"\n                    [attr.data-p-gutter-resizing]=\"false\"\n                    [attr.data-pc-section]=\"'gutter'\"\n                >\n                    <div\n                        class=\"p-splitter-gutter-handle\"\n                        tabindex=\"0\"\n                        [ngStyle]=\"gutterStyle()\"\n                        [attr.aria-orientation]=\"layout\"\n                        [attr.aria-valuenow]=\"prevSize\"\n                        [attr.data-pc-section]=\"'gutterhandle'\"\n                        (keyup)=\"onGutterKeyUp($event)\"\n                        (keydown)=\"onGutterKeyDown($event, i)\"\n                    ></div>\n                </div>\n            </ng-template>\n        </div>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    styleUrls: ['./splitter.css'],\n    host: {\n        class: 'p-element',\n        '[class.p-splitter-panel-nested]': 'nested'\n    }\n})\nexport class Splitter {\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Style class of the panel.\n     * @group Props\n     */\n    @Input() panelStyleClass: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Inline style of the panel.\n     * @group Props\n     */\n    @Input() panelStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Defines where a stateful splitter keeps its state, valid values are 'session' for sessionStorage and 'local' for localStorage.\n     * @group Props\n     */\n    @Input() stateStorage: string | undefined = 'session';\n    /**\n     * Storage identifier of a stateful Splitter.\n     * @group Props\n     */\n    @Input() stateKey: string | undefined | null = null;\n    /**\n     * Orientation of the panels. Valid values are 'horizontal' and 'vertical'.\n     * @group Props\n     */\n    @Input() layout: string | undefined = 'horizontal';\n    /**\n     * Size of the divider in pixels.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) gutterSize: number = 4;\n    /**\n     * Step factor to increment/decrement the size of the panels while pressing the arrow keys.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) step: number = 5;\n    /**\n     * Minimum size of the elements relative to 100%.\n     * @group Props\n     */\n    @Input() minSizes: number[] = [];\n    /**\n     * Size of the elements relative to 100%.\n     * @group Props\n     */\n    @Input() get panelSizes(): number[] {\n        return this._panelSizes;\n    }\n    set panelSizes(val: number[]) {\n        this._panelSizes = val;\n\n        if (this.el && this.el.nativeElement && this.panels.length > 0) {\n            let children = [...this.el.nativeElement.children[0].children].filter((child) => DomHandler.hasClass(child, 'p-splitter-panel'));\n            let _panelSizes = [];\n\n            this.panels.map((panel, i) => {\n                let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n                let panelSize = panelInitialSize || 100 / this.panels.length;\n                _panelSizes[i] = panelSize;\n                children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            });\n        }\n    }\n    /**\n     * Callback to invoke when resize ends.\n     * @param {SplitterResizeEndEvent} event - Custom panel resize end event\n     * @group Emits\n     */\n    @Output() onResizeEnd: EventEmitter<SplitterResizeEndEvent> = new EventEmitter<SplitterResizeEndEvent>();\n    /**\n     * Callback to invoke when resize starts.\n     * @param {SplitterResizeStartEvent} event - Custom panel resize start event\n     * @group Emits\n     */\n    @Output() onResizeStart: EventEmitter<SplitterResizeStartEvent> = new EventEmitter<SplitterResizeStartEvent>();\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    @ViewChild('container', { static: false }) containerViewChild: Nullable<ElementRef>;\n\n    nested: boolean = false;\n\n    panels: any[] = [];\n\n    dragging: boolean = false;\n\n    mouseMoveListener: VoidListener;\n\n    mouseUpListener: VoidListener;\n\n    touchMoveListener: VoidListener;\n\n    touchEndListener: VoidListener;\n\n    size: Nullable<number>;\n\n    gutterElement: Nullable<ElementRef | HTMLElement>;\n\n    startPos: Nullable<number>;\n\n    prevPanelElement: Nullable<ElementRef | HTMLElement>;\n\n    nextPanelElement: Nullable<ElementRef | HTMLElement>;\n\n    nextPanelSize: Nullable<number>;\n\n    prevPanelSize: Nullable<number>;\n\n    _panelSizes: number[] = [];\n\n    prevPanelIndex: Nullable<number>;\n\n    timer: any;\n\n    prevSize: any;\n\n    private window: Window;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, public cd: ChangeDetectorRef, private el: ElementRef) {\n        this.window = this.document.defaultView as Window;\n    }\n\n    ngOnInit() {\n        this.nested = this.isNested();\n    }\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'panel':\n                    this.panels.push(item.template);\n                    break;\n                default:\n                    this.panels.push(item.template);\n                    break;\n            }\n        });\n    }\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.panels && this.panels.length) {\n                let initialized = false;\n                if (this.isStateful()) {\n                    initialized = this.restoreState();\n                }\n\n                if (!initialized) {\n                    let children = [...this.el.nativeElement.children[0].children].filter((child) => DomHandler.hasClass(child, 'p-splitter-panel'));\n                    let _panelSizes = [];\n\n                    this.panels.map((panel, i) => {\n                        let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n                        let panelSize = panelInitialSize || 100 / this.panels.length;\n                        _panelSizes[i] = panelSize;\n                        children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * (this.gutterSize as number) + 'px)';\n                    });\n\n                    this._panelSizes = _panelSizes;\n\n                    this.prevSize = parseFloat(_panelSizes[0]).toFixed(4);\n                }\n            }\n        }\n    }\n\n    resizeStart(event: TouchEvent | MouseEvent, index: number, isKeyDown?: boolean) {\n        this.gutterElement = (event.currentTarget as HTMLElement) || (event.target as HTMLElement).parentElement;\n        this.size = this.horizontal() ? DomHandler.getWidth((this.containerViewChild as ElementRef).nativeElement) : DomHandler.getHeight((this.containerViewChild as ElementRef).nativeElement);\n\n        if (!isKeyDown) {\n            this.dragging = true;\n            this.startPos = this.horizontal() ? (event instanceof MouseEvent ? event.pageX : event.changedTouches[0].pageX) : event instanceof MouseEvent ? event.pageY : event.changedTouches[0].pageY;\n        }\n\n        this.prevPanelElement = this.gutterElement.previousElementSibling as HTMLElement;\n        this.nextPanelElement = this.gutterElement.nextElementSibling as HTMLElement;\n\n        if (isKeyDown) {\n            this.prevPanelSize = this.horizontal() ? DomHandler.getOuterWidth(this.prevPanelElement, true) : DomHandler.getOuterHeight(this.prevPanelElement, true);\n            this.nextPanelSize = this.horizontal() ? DomHandler.getOuterWidth(this.nextPanelElement, true) : DomHandler.getOuterHeight(this.nextPanelElement, true);\n        } else {\n            this.prevPanelSize = (100 * (this.horizontal() ? DomHandler.getOuterWidth(this.prevPanelElement, true) : DomHandler.getOuterHeight(this.prevPanelElement, true))) / this.size;\n            this.nextPanelSize = (100 * (this.horizontal() ? DomHandler.getOuterWidth(this.nextPanelElement, true) : DomHandler.getOuterHeight(this.nextPanelElement, true))) / this.size;\n        }\n\n        this.prevPanelIndex = index;\n        DomHandler.addClass(this.gutterElement, 'p-splitter-gutter-resizing');\n        this.gutterElement.setAttribute('data-p-gutter-resizing', 'true');\n        DomHandler.addClass((this.containerViewChild as ElementRef).nativeElement, 'p-splitter-resizing');\n        this.containerViewChild.nativeElement.setAttribute('data-p-resizing', 'true');\n        this.onResizeStart.emit({ originalEvent: event, sizes: this._panelSizes as number[] });\n    }\n\n    onResize(event: MouseEvent, step?: number, isKeyDown?: boolean) {\n        let newPos, newPrevPanelSize, newNextPanelSize;\n\n        if (isKeyDown) {\n            if (this.horizontal()) {\n                newPrevPanelSize = (100 * (this.prevPanelSize + step)) / this.size;\n                newNextPanelSize = (100 * (this.nextPanelSize - step)) / this.size;\n            } else {\n                newPrevPanelSize = (100 * (this.prevPanelSize - step)) / this.size;\n                newNextPanelSize = (100 * (this.nextPanelSize + step)) / this.size;\n            }\n        } else {\n            if (this.horizontal()) newPos = (event.pageX * 100) / this.size - (this.startPos * 100) / this.size;\n            else newPos = (event.pageY * 100) / this.size - (this.startPos * 100) / this.size;\n\n            newPrevPanelSize = (this.prevPanelSize as number) + newPos;\n            newNextPanelSize = (this.nextPanelSize as number) - newPos;\n        }\n\n        this.prevSize = parseFloat(newPrevPanelSize).toFixed(4);\n\n        if (this.validateResize(newPrevPanelSize, newNextPanelSize)) {\n            (this.prevPanelElement as HTMLElement).style.flexBasis = 'calc(' + newPrevPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            (this.nextPanelElement as HTMLElement).style.flexBasis = 'calc(' + newNextPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            this._panelSizes[this.prevPanelIndex as number] = newPrevPanelSize;\n            this._panelSizes[(this.prevPanelIndex as number) + 1] = newNextPanelSize;\n        }\n    }\n\n    resizeEnd(event: MouseEvent | TouchEvent) {\n        if (this.isStateful()) {\n            this.saveState();\n        }\n\n        this.onResizeEnd.emit({ originalEvent: event, sizes: this._panelSizes });\n        DomHandler.removeClass(this.gutterElement, 'p-splitter-gutter-resizing');\n        DomHandler.removeClass((this.containerViewChild as ElementRef).nativeElement, 'p-splitter-resizing');\n        this.clear();\n    }\n\n    onGutterMouseDown(event: MouseEvent, index: number) {\n        this.resizeStart(event, index);\n        this.bindMouseListeners();\n    }\n\n    onGutterTouchStart(event: TouchEvent, index: number) {\n        if (event.cancelable) {\n            this.resizeStart(event, index);\n            this.bindTouchListeners();\n\n            event.preventDefault();\n        }\n    }\n\n    onGutterTouchMove(event) {\n        this.onResize(event);\n        event.preventDefault();\n    }\n\n    onGutterTouchEnd(event: TouchEvent) {\n        this.resizeEnd(event);\n        this.unbindTouchListeners();\n\n        if (event.cancelable) event.preventDefault();\n    }\n\n    repeat(event, index, step) {\n        this.resizeStart(event, index, true);\n        this.onResize(event, step, true);\n    }\n\n    setTimer(event, index, step) {\n        this.clearTimer();\n        this.timer = setTimeout(() => {\n            this.repeat(event, index, step);\n        }, 40);\n    }\n\n    clearTimer() {\n        if (this.timer) {\n            clearTimeout(this.timer);\n        }\n    }\n\n    onGutterKeyUp(event) {\n        this.clearTimer();\n        this.resizeEnd(event);\n    }\n\n    onGutterKeyDown(event, index) {\n        switch (event.code) {\n            case 'ArrowLeft': {\n                if (this.layout === 'horizontal') {\n                    this.setTimer(event, index, this.step * -1);\n                }\n\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowRight': {\n                if (this.layout === 'horizontal') {\n                    this.setTimer(event, index, this.step);\n                }\n\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowDown': {\n                if (this.layout === 'vertical') {\n                    this.setTimer(event, index, this.step * -1);\n                }\n\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowUp': {\n                if (this.layout === 'vertical') {\n                    this.setTimer(event, index, this.step);\n                }\n\n                event.preventDefault();\n                break;\n            }\n\n            default:\n                //no op\n                break;\n        }\n    }\n\n    validateResize(newPrevPanelSize: number, newNextPanelSize: number) {\n        const prevPanelIndex = this.prevPanelIndex;\n        if (this.minSizes.length > prevPanelIndex && this.minSizes[prevPanelIndex] && this.minSizes[prevPanelIndex] > newPrevPanelSize) {\n            return false;\n        }\n\n        const nextPanelIndex = this.prevPanelIndex + 1;\n        if (this.minSizes.length > nextPanelIndex && this.minSizes[nextPanelIndex] && this.minSizes[nextPanelIndex] > newNextPanelSize) {\n            return false;\n        }\n\n        return true;\n    }\n\n    bindMouseListeners() {\n        if (!this.mouseMoveListener) {\n            this.mouseMoveListener = this.renderer.listen(this.document, 'mousemove', (event) => {\n                this.onResize(event);\n            });\n        }\n\n        if (!this.mouseUpListener) {\n            this.mouseUpListener = this.renderer.listen(this.document, 'mouseup', (event) => {\n                this.resizeEnd(event);\n                this.unbindMouseListeners();\n            });\n        }\n    }\n\n    bindTouchListeners() {\n        if (!this.touchMoveListener) {\n            this.touchMoveListener = this.renderer.listen(this.document, 'touchmove', (event) => {\n                this.onResize(event.changedTouches[0]);\n            });\n        }\n\n        if (!this.touchEndListener) {\n            this.touchEndListener = this.renderer.listen(this.document, 'touchend', (event) => {\n                this.resizeEnd(event);\n                this.unbindTouchListeners();\n            });\n        }\n    }\n\n    unbindMouseListeners() {\n        if (this.mouseMoveListener) {\n            this.mouseMoveListener();\n            this.mouseMoveListener = null;\n        }\n\n        if (this.mouseUpListener) {\n            this.mouseUpListener();\n            this.mouseUpListener = null;\n        }\n    }\n\n    unbindTouchListeners() {\n        if (this.touchMoveListener) {\n            this.touchMoveListener();\n            this.touchMoveListener = null;\n        }\n\n        if (this.touchEndListener) {\n            this.touchEndListener();\n            this.touchEndListener = null;\n        }\n    }\n\n    clear() {\n        this.dragging = false;\n        this.size = null;\n        this.startPos = null;\n        this.prevPanelElement = null;\n        this.nextPanelElement = null;\n        this.prevPanelSize = null;\n        this.nextPanelSize = null;\n        this.gutterElement = null;\n        this.prevPanelIndex = null;\n    }\n\n    isNested() {\n        if (this.el.nativeElement) {\n            let parent = this.el.nativeElement.parentElement;\n            while (parent && !DomHandler.hasClass(parent, 'p-splitter')) {\n                parent = parent.parentElement;\n            }\n\n            return parent !== null;\n        } else {\n            return false;\n        }\n    }\n\n    isStateful() {\n        return this.stateKey != null;\n    }\n\n    getStorage() {\n        if (isPlatformBrowser(this.platformId)) {\n            switch (this.stateStorage) {\n                case 'local':\n                    return this.window.localStorage;\n\n                case 'session':\n                    return this.window.sessionStorage;\n\n                default:\n                    throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n            }\n        } else {\n            throw new Error('Storage is not a available by default on the server.');\n        }\n    }\n\n    saveState() {\n        this.getStorage().setItem(this.stateKey as string, JSON.stringify(this._panelSizes));\n    }\n\n    restoreState() {\n        const storage = this.getStorage();\n        const stateString = storage.getItem(this.stateKey as string);\n\n        if (stateString) {\n            this._panelSizes = JSON.parse(stateString);\n            let children = [...(this.containerViewChild as ElementRef).nativeElement.children].filter((child) => DomHandler.hasClass(child, 'p-splitter-panel'));\n            children.forEach((child, i) => {\n                child.style.flexBasis = 'calc(' + this._panelSizes[i] + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            });\n\n            return true;\n        }\n\n        return false;\n    }\n\n    containerClass() {\n        return {\n            'p-splitter p-component': true,\n            'p-splitter-horizontal': this.layout === 'horizontal',\n            'p-splitter-vertical': this.layout === 'vertical'\n        };\n    }\n\n    panelContainerClass() {\n        return {\n            'p-splitter-panel': true,\n            'p-splitter-panel-nested': true\n        };\n    }\n\n    gutterStyle() {\n        if (this.horizontal()) return { width: this.gutterSize + 'px' };\n        else return { height: this.gutterSize + 'px' };\n    }\n\n    horizontal() {\n        return this.layout === 'horizontal';\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Splitter, SharedModule],\n    declarations: [Splitter]\n})\nexport class SplitterModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;AAMA;;;AAGG;MA2CU,QAAQ,CAAA;AAgIqB,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAA+B,IAAA,EAAA,CAAA;AA/H3K;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;IACM,YAAY,GAAuB,SAAS,CAAC;AACtD;;;AAGG;IACM,QAAQ,GAA8B,IAAI,CAAC;AACpD;;;AAGG;IACM,MAAM,GAAuB,YAAY,CAAC;AACnD;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;IACoC,IAAI,GAAW,CAAC,CAAC;AACxD;;;AAGG;IACM,QAAQ,GAAa,EAAE,CAAC;AACjC;;;AAGG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAa,EAAA;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AAEvB,QAAA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5D,YAAA,IAAI,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC,CAAC;YACjI,IAAI,WAAW,GAAG,EAAE,CAAC;YAErB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAI;gBACzB,IAAI,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBACnF,IAAI,SAAS,GAAG,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AAC7D,gBAAA,WAAW,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AAC3B,gBAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACpH,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AACD;;;;AAIG;AACO,IAAA,WAAW,GAAyC,IAAI,YAAY,EAA0B,CAAC;AACzG;;;;AAIG;AACO,IAAA,aAAa,GAA2C,IAAI,YAAY,EAA4B,CAAC;AAE/E,IAAA,SAAS,CAA4B;AAE1B,IAAA,kBAAkB,CAAuB;IAEpF,MAAM,GAAY,KAAK,CAAC;IAExB,MAAM,GAAU,EAAE,CAAC;IAEnB,QAAQ,GAAY,KAAK,CAAC;AAE1B,IAAA,iBAAiB,CAAe;AAEhC,IAAA,eAAe,CAAe;AAE9B,IAAA,iBAAiB,CAAe;AAEhC,IAAA,gBAAgB,CAAe;AAE/B,IAAA,IAAI,CAAmB;AAEvB,IAAA,aAAa,CAAqC;AAElD,IAAA,QAAQ,CAAmB;AAE3B,IAAA,gBAAgB,CAAqC;AAErD,IAAA,gBAAgB,CAAqC;AAErD,IAAA,aAAa,CAAmB;AAEhC,IAAA,aAAa,CAAmB;IAEhC,WAAW,GAAa,EAAE,CAAC;AAE3B,IAAA,cAAc,CAAmB;AAEjC,IAAA,KAAK,CAAM;AAEX,IAAA,QAAQ,CAAM;AAEN,IAAA,MAAM,CAAS;IAEvB,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAU,QAAmB,EAAS,EAAqB,EAAU,EAAc,EAAA;QAAnJ,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACrL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;KACrD;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;KACjC;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;oBACR,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAChC,MAAM;AACV,gBAAA;oBACI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAChC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACnC,IAAI,WAAW,GAAG,KAAK,CAAC;AACxB,gBAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;AACnB,oBAAA,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AACrC,iBAAA;gBAED,IAAI,CAAC,WAAW,EAAE;AACd,oBAAA,IAAI,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC,CAAC;oBACjI,IAAI,WAAW,GAAG,EAAE,CAAC;oBAErB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,KAAI;wBACzB,IAAI,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;wBACnF,IAAI,SAAS,GAAG,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AAC7D,wBAAA,WAAW,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AAC3B,wBAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAK,IAAI,CAAC,UAAqB,GAAG,KAAK,CAAC;AAChI,qBAAC,CAAC,CAAC;AAEH,oBAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAE/B,oBAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACzD,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAA8B,EAAE,KAAa,EAAE,SAAmB,EAAA;AAC1E,QAAA,IAAI,CAAC,aAAa,GAAI,KAAK,CAAC,aAA6B,IAAK,KAAK,CAAC,MAAsB,CAAC,aAAa,CAAC;AACzG,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAE,IAAI,CAAC,kBAAiC,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,SAAS,CAAE,IAAI,CAAC,kBAAiC,CAAC,aAAa,CAAC,CAAC;QAEzL,IAAI,CAAC,SAAS,EAAE;AACZ,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,YAAY,UAAU,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,YAAY,UAAU,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC/L,SAAA;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAqC,CAAC;QACjF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAiC,CAAC;AAE7E,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;AACxJ,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;AAC3J,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;AAC9K,YAAA,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;AACjL,SAAA;AAED,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,4BAA4B,CAAC,CAAC;QACtE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;QAClE,UAAU,CAAC,QAAQ,CAAE,IAAI,CAAC,kBAAiC,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;QAClG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,YAAY,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;AAC9E,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAuB,EAAE,CAAC,CAAC;KAC1F;AAED,IAAA,QAAQ,CAAC,KAAiB,EAAE,IAAa,EAAE,SAAmB,EAAA;AAC1D,QAAA,IAAI,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;AAE/C,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;AACnB,gBAAA,gBAAgB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;AACnE,gBAAA,gBAAgB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;AACtE,aAAA;AAAM,iBAAA;AACH,gBAAA,gBAAgB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;AACnE,gBAAA,gBAAgB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC;AACtE,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,IAAI,CAAC,UAAU,EAAE;gBAAE,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC;;gBAC/F,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC;AAElF,YAAA,gBAAgB,GAAI,IAAI,CAAC,aAAwB,GAAG,MAAM,CAAC;AAC3D,YAAA,gBAAgB,GAAI,IAAI,CAAC,aAAwB,GAAG,MAAM,CAAC;AAC9D,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAExD,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,EAAE;AACxD,YAAA,IAAI,CAAC,gBAAgC,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,GAAG,gBAAgB,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACjJ,YAAA,IAAI,CAAC,gBAAgC,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,GAAG,gBAAgB,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAClJ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAwB,CAAC,GAAG,gBAAgB,CAAC;YACnE,IAAI,CAAC,WAAW,CAAE,IAAI,CAAC,cAAyB,GAAG,CAAC,CAAC,GAAG,gBAAgB,CAAC;AAC5E,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAA8B,EAAA;AACpC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACnB,IAAI,CAAC,SAAS,EAAE,CAAC;AACpB,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACzE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,4BAA4B,CAAC,CAAC;QACzE,UAAU,CAAC,WAAW,CAAE,IAAI,CAAC,kBAAiC,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;QACrG,IAAI,CAAC,KAAK,EAAE,CAAC;KAChB;IAED,iBAAiB,CAAC,KAAiB,EAAE,KAAa,EAAA;AAC9C,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B;IAED,kBAAkB,CAAC,KAAiB,EAAE,KAAa,EAAA;QAC/C,IAAI,KAAK,CAAC,UAAU,EAAE;AAClB,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAK,EAAA;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAiB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,KAAK,CAAC,UAAU;YAAE,KAAK,CAAC,cAAc,EAAE,CAAC;KAChD;AAED,IAAA,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAA;QACrB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KACpC;AAED,IAAA,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAA;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,MAAK;YACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACnC,EAAE,EAAE,CAAC,CAAC;KACV;IAED,UAAU,GAAA;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;QACf,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACzB;IAED,eAAe,CAAC,KAAK,EAAE,KAAK,EAAA;QACxB,QAAQ,KAAK,CAAC,IAAI;YACd,KAAK,WAAW,EAAE;AACd,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,YAAY,EAAE;AAC9B,oBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,iBAAA;gBAED,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,YAAY,EAAE;AACf,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,YAAY,EAAE;oBAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C,iBAAA;gBAED,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,WAAW,EAAE;AACd,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;AAC5B,oBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,iBAAA;gBAED,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,SAAS,EAAE;AACZ,gBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;oBAC5B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C,iBAAA;gBAED,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;AAED,YAAA;;gBAEI,MAAM;AACb,SAAA;KACJ;IAED,cAAc,CAAC,gBAAwB,EAAE,gBAAwB,EAAA;AAC7D,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,gBAAgB,EAAE;AAC5H,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,gBAAgB,EAAE;AAC5H,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AACzB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC,KAAK,KAAI;AAChF,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACvB,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,KAAK,KAAI;AAC5E,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAChC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AACzB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC,KAAK,KAAI;gBAChF,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AACxB,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,KAAK,KAAI;AAC9E,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAChC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,SAAA;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC/B,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,SAAA;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAChC,SAAA;KACJ;IAED,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC9B;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;YACvB,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC;YACjD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE;AACzD,gBAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC;AACjC,aAAA;YAED,OAAO,MAAM,KAAK,IAAI,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;KAChC;IAED,UAAU,GAAA;AACN,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,QAAQ,IAAI,CAAC,YAAY;AACrB,gBAAA,KAAK,OAAO;AACR,oBAAA,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;AAEpC,gBAAA,KAAK,SAAS;AACV,oBAAA,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;AAEtC,gBAAA;oBACI,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,GAAG,0FAA0F,CAAC,CAAC;AACvI,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;AAC3E,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,IAAI,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;KACxF;IAED,YAAY,GAAA;AACR,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAkB,CAAC,CAAC;AAE7D,QAAA,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC3C,YAAA,IAAI,QAAQ,GAAG,CAAC,GAAI,IAAI,CAAC,kBAAiC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC,CAAC;YACrJ,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,KAAI;AAC1B,gBAAA,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACxH,aAAC,CAAC,CAAC;AAEH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,cAAc,GAAA;QACV,OAAO;AACH,YAAA,wBAAwB,EAAE,IAAI;AAC9B,YAAA,uBAAuB,EAAE,IAAI,CAAC,MAAM,KAAK,YAAY;AACrD,YAAA,qBAAqB,EAAE,IAAI,CAAC,MAAM,KAAK,UAAU;SACpD,CAAC;KACL;IAED,mBAAmB,GAAA;QACf,OAAO;AACH,YAAA,kBAAkB,EAAE,IAAI;AACxB,YAAA,yBAAyB,EAAE,IAAI;SAClC,CAAC;KACL;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,EAAE,CAAC;;YAC3D,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,EAAE,CAAC;KAClD;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,CAAC;KACvC;uGA9eQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAgIG,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAhIpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,2PAwCG,eAAe,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAKf,eAAe,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,QAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAyClB,aAAa,EA9HpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,i9BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FASQ,QAAQ,EAAA,UAAA,EAAA,CAAA;kBA1CpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BT,IAAA,CAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EAEzC,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,iCAAiC,EAAE,QAAQ;AAC9C,qBAAA,EAAA,MAAA,EAAA,CAAA,i9BAAA,CAAA,EAAA,CAAA;;0BAkIY,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;0HA3HpE,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKiC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAuBI,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAMG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEa,kBAAkB,EAAA,CAAA;sBAA5D,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;;MA8ZhC,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,iBAtfd,QAAQ,CAAA,EAAA,OAAA,EAAA,CAkfP,YAAY,CAlfb,EAAA,OAAA,EAAA,CAAA,QAAQ,EAmfG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGvB,cAAc,EAAA,OAAA,EAAA,CAJb,YAAY,EACF,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGvB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;oBACjC,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;ACziBD;;AAEG;;;;"}