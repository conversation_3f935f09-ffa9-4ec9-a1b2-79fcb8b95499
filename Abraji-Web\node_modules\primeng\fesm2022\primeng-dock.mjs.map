{"version": 3, "file": "primeng-dock.mjs", "sources": ["../../src/app/components/dock/dock.ts", "../../src/app/components/dock/primeng-dock.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, ElementRef, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { MenuItem, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\n/**\n * Dock is a navigation component consisting of menuitems.\n * @group Components\n */\n@Component({\n    selector: 'p-dock',\n    template: `\n        <div [ngClass]=\"containerClass\" [ngStyle]=\"style\" [class]=\"styleClass\" [attr.data-pc-name]=\"'dock'\">\n            <div class=\"p-dock-list-container\">\n                <ul\n                    #list\n                    [attr.id]=\"id\"\n                    class=\"p-dock-list\"\n                    role=\"menu\"\n                    [attr.aria-orientation]=\"position === 'bottom' || position === 'top' ? 'horizontal' : 'vertical'\"\n                    [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                    [tabindex]=\"tabindex\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.data-pc-section]=\"'menu'\"\n                    (focus)=\"onListFocus($event)\"\n                    (blur)=\"onListBlur($event)\"\n                    (keydown)=\"onListKeyDown($event)\"\n                    (mouseleave)=\"onListMouseLeave()\"\n                >\n                    <li\n                        *ngFor=\"let item of model; let i = index\"\n                        [attr.id]=\"getItemId(item, i)\"\n                        [ngClass]=\"itemClass(item, i)\"\n                        role=\"menuitem\"\n                        [attr.aria-label]=\"item.label\"\n                        [attr.aria-disabled]=\"disabled(item)\"\n                        (click)=\"onItemClick($event, item)\"\n                        (mouseenter)=\"onItemMouseEnter(i)\"\n                        [attr.data-pc-section]=\"'menuitem'\"\n                        [attr.data-p-focused]=\"isItemActive(getItemId(item, i))\"\n                        [attr.data-p-disabled]=\"disabled(item) || false\"\n                    >\n                        <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\">\n                            <a\n                                *ngIf=\"isClickableRouterLink(item); else elseBlock\"\n                                pRipple\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [ngClass]=\"{ 'p-disabled': item.disabled }\"\n                                class=\"p-dock-link\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                [target]=\"item.target\"\n                                [attr.tabindex]=\"item.disabled || readonly ? null : item.tabindex ? item.tabindex : '-1'\"\n                                pTooltip\n                                [tooltipOptions]=\"item.tooltipOptions\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                [attr.aria-hidden]=\"true\"\n                            >\n                                <span class=\"p-dock-action-icon\" *ngIf=\"item.icon && !itemTemplate\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </a>\n                            <ng-template #elseBlock>\n                                <a\n                                    [tooltipPosition]=\"item.tooltipPosition\"\n                                    [attr.href]=\"item.url || null\"\n                                    class=\"p-dock-link\"\n                                    pRipple\n                                    pTooltip\n                                    [tooltipOptions]=\"item.tooltipOptions\"\n                                    [ngClass]=\"{ 'p-disabled': item.disabled }\"\n                                    [target]=\"item.target\"\n                                    [attr.tabindex]=\"item.disabled || (i !== activeIndex && readonly) ? null : item.tabindex ? item.tabindex : '-1'\"\n                                    [attr.aria-hidden]=\"true\"\n                                >\n                                    <span class=\"p-dock-action-icon\" *ngIf=\"item.icon && !itemTemplate\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                                </a>\n                            </ng-template>\n                        </div>\n                    </li>\n                </ul>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./dock.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Dock implements AfterContentInit {\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * MenuModel instance to define the action items.\n     * @group Props\n     */\n    @Input() model: MenuItem[] | undefined | null = null;\n    /**\n     * Position of element.\n     * @group Props\n     */\n    @Input() position: 'bottom' | 'top' | 'left' | 'right' = 'bottom';\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Defines a string that labels the dropdown button for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Callback to execute when button is focused.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @ViewChild('list', { static: false }) listViewChild: Nullable<ElementRef>;\n\n    itemTemplate: TemplateRef<any> | undefined;\n\n    currentIndex: number;\n\n    tabindex: number = 0;\n\n    focused: boolean = false;\n\n    focusedOptionIndex: number = -1;\n\n    get focusedOptionId() {\n        return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;\n    }\n\n    constructor(private el: ElementRef, public cd: ChangeDetectorRef) {\n        this.currentIndex = -3;\n    }\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    getItemId(item, index) {\n        return item && item?.id ? item.id : `${index}`;\n    }\n\n    getItemProp(processedItem, name) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name]) : undefined;\n    }\n\n    disabled(item) {\n        return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n    }\n\n    isItemActive(id) {\n        return id === this.focusedOptionIndex;\n    }\n\n    onListMouseLeave() {\n        this.currentIndex = -3;\n        this.cd.markForCheck();\n    }\n\n    onItemMouseEnter(index: number) {\n        this.currentIndex = index;\n\n        if (index === 1) {\n        }\n\n        this.cd.markForCheck();\n    }\n\n    onItemClick(e: Event, item: MenuItem) {\n        if (item.command) {\n            item.command({ originalEvent: e, item });\n        }\n    }\n\n    onListFocus(event) {\n        this.focused = true;\n        this.changeFocusedOptionIndex(0);\n        this.onFocus.emit(event);\n    }\n\n    onListBlur(event) {\n        this.focused = false;\n        this.focusedOptionIndex = -1;\n        this.onBlur.emit(event);\n    }\n    onListKeyDown(event) {\n        switch (event.code) {\n            case 'ArrowDown': {\n                if (this.position === 'left' || this.position === 'right') this.onArrowDownKey();\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowUp': {\n                if (this.position === 'left' || this.position === 'right') this.onArrowUpKey();\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowRight': {\n                if (this.position === 'top' || this.position === 'bottom') this.onArrowDownKey();\n                event.preventDefault();\n                break;\n            }\n\n            case 'ArrowLeft': {\n                if (this.position === 'top' || this.position === 'bottom') this.onArrowUpKey();\n                event.preventDefault();\n                break;\n            }\n\n            case 'Home': {\n                this.onHomeKey();\n                event.preventDefault();\n                break;\n            }\n\n            case 'End': {\n                this.onEndKey();\n                event.preventDefault();\n                break;\n            }\n\n            case 'Enter':\n\n            case 'Space': {\n                this.onSpaceKey();\n                event.preventDefault();\n                break;\n            }\n\n            default:\n                break;\n        }\n    }\n\n    onArrowDownKey() {\n        const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);\n\n        this.changeFocusedOptionIndex(optionIndex);\n    }\n\n    onArrowUpKey() {\n        const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);\n\n        this.changeFocusedOptionIndex(optionIndex);\n    }\n\n    onHomeKey() {\n        this.changeFocusedOptionIndex(0);\n    }\n\n    onEndKey() {\n        this.changeFocusedOptionIndex(DomHandler.find(this.listViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]').length - 1);\n    }\n\n    onSpaceKey() {\n        const element = DomHandler.findSingle(this.listViewChild.nativeElement, `li[id=\"${`${this.focusedOptionIndex}`}\"]`);\n        const anchorElement = element && DomHandler.findSingle(element, '[data-pc-section=\"action\"]');\n\n        anchorElement ? anchorElement.click() : element && element.click();\n    }\n\n    findNextOptionIndex(index) {\n        const menuitems = DomHandler.find(this.listViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n        const matchedOptionIndex = [...menuitems].findIndex((link) => link.id === index);\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n    }\n\n    changeFocusedOptionIndex(index) {\n        const menuitems = DomHandler.find(this.listViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n\n        let order = index >= menuitems.length ? menuitems.length - 1 : index < 0 ? 0 : index;\n\n        this.focusedOptionIndex = menuitems[order].getAttribute('id');\n    }\n\n    findPrevOptionIndex(index) {\n        const menuitems = DomHandler.find(this.listViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n        const matchedOptionIndex = [...menuitems].findIndex((link) => link.id === index);\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n    }\n\n    get containerClass() {\n        return {\n            ['p-dock p-component ' + ` p-dock-${this.position}`]: true\n        };\n    }\n\n    isClickableRouterLink(item: any) {\n        return item.routerLink && !item.disabled;\n    }\n\n    itemClass(item, index: number) {\n        return {\n            'p-dock-item': true,\n            'p-dock-item-second-prev': this.currentIndex - 2 === index,\n            'p-dock-item-prev': this.currentIndex - 1 === index,\n            'p-dock-item-current': this.currentIndex === index,\n            'p-dock-item-next': this.currentIndex + 1 === index,\n            'p-dock-item-second-next': this.currentIndex + 2 === index,\n            'p-focus': this.isItemActive(this.getItemId(item, index))\n        };\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n    exports: [Dock, SharedModule, TooltipModule, RouterModule],\n    declarations: [Dock]\n})\nexport class DockModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AASA;;;AAGG;MAyFU,IAAI,CAAA;AAmEO,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAlE3C;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACM,KAAK,GAAkC,IAAI,CAAC;AACrD;;;AAGG;IACM,QAAQ,GAAwC,QAAQ,CAAC;AAClE;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;;AAIG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC7E;;;;AAIG;AACO,IAAA,MAAM,GAA6B,IAAI,YAAY,EAAc,CAAC;AAE5C,IAAA,SAAS,CAAuC;AAE1C,IAAA,aAAa,CAAuB;AAE1E,IAAA,YAAY,CAA+B;AAE3C,IAAA,YAAY,CAAS;IAErB,QAAQ,GAAW,CAAC,CAAC;IAErB,OAAO,GAAY,KAAK,CAAC;IAEzB,kBAAkB,GAAW,CAAC,CAAC,CAAC;AAEhC,IAAA,IAAI,eAAe,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,kBAAkB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1E;IAED,WAAoB,CAAA,EAAc,EAAS,EAAqB,EAAA;QAA5C,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;AAC5D,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;KAC1B;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;KAC5C;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,SAAS,CAAC,IAAI,EAAE,KAAK,EAAA;AACjB,QAAA,OAAO,IAAI,IAAI,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAG,EAAA,KAAK,EAAE,CAAC;KAClD;IAED,WAAW,CAAC,aAAa,EAAE,IAAI,EAAA;QAC3B,OAAO,aAAa,IAAI,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;KAC/G;AAED,IAAA,QAAQ,CAAC,IAAI,EAAA;AACT,QAAA,OAAO,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;KAChF;AAED,IAAA,YAAY,CAAC,EAAE,EAAA;AACX,QAAA,OAAO,EAAE,KAAK,IAAI,CAAC,kBAAkB,CAAC;KACzC;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAa,EAAA;AAC1B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,KAAK,KAAK,CAAC,EAAE;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,WAAW,CAAC,CAAQ,EAAE,IAAc,EAAA;QAChC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5C,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AACD,IAAA,aAAa,CAAC,KAAK,EAAA;QACf,QAAQ,KAAK,CAAC,IAAI;YACd,KAAK,WAAW,EAAE;gBACd,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO;oBAAE,IAAI,CAAC,cAAc,EAAE,CAAC;gBACjF,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,SAAS,EAAE;gBACZ,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO;oBAAE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC/E,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,YAAY,EAAE;gBACf,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ;oBAAE,IAAI,CAAC,cAAc,EAAE,CAAC;gBACjF,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,WAAW,EAAE;gBACd,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ;oBAAE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC/E,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,MAAM,EAAE;gBACT,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;YAED,KAAK,KAAK,EAAE;gBACR,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;AAED,YAAA,KAAK,OAAO,CAAC;YAEb,KAAK,OAAO,EAAE;gBACV,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACT,aAAA;AAED,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;IAED,cAAc,GAAA;QACV,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAEtE,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;KAC9C;IAED,YAAY,GAAA;QACR,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAEtE,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;KAC9C;IAED,SAAS,GAAA;AACL,QAAA,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;KACpC;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,yDAAyD,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC1J;IAED,UAAU,GAAA;QACN,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAA,OAAA,EAAU,GAAG,IAAI,CAAC,kBAAkB,CAAE,CAAA,CAAA,EAAA,CAAI,CAAC,CAAC;AACpH,QAAA,MAAM,aAAa,GAAG,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC;AAE9F,QAAA,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;KACtE;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,yDAAyD,CAAC,CAAC;QAC/H,MAAM,kBAAkB,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAEjF,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC;KAC/D;AAED,IAAA,wBAAwB,CAAC,KAAK,EAAA;AAC1B,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,yDAAyD,CAAC,CAAC;AAE/H,QAAA,IAAI,KAAK,GAAG,KAAK,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAErF,QAAA,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KACjE;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,yDAAyD,CAAC,CAAC;QAC/H,MAAM,kBAAkB,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAEjF,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC;KAC/D;AAED,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;YACH,CAAC,qBAAqB,GAAG,CAAW,QAAA,EAAA,IAAI,CAAC,QAAQ,CAAA,CAAE,GAAG,IAAI;SAC7D,CAAC;KACL;AAED,IAAA,qBAAqB,CAAC,IAAS,EAAA;QAC3B,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC5C;IAED,SAAS,CAAC,IAAI,EAAE,KAAa,EAAA;QACzB,OAAO;AACH,YAAA,aAAa,EAAE,IAAI;AACnB,YAAA,yBAAyB,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AAC1D,YAAA,kBAAkB,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AACnD,YAAA,qBAAqB,EAAE,IAAI,CAAC,YAAY,KAAK,KAAK;AAClD,YAAA,kBAAkB,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AACnD,YAAA,yBAAyB,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AAC1D,YAAA,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAC5D,CAAC;KACL;uGAhQQ,IAAI,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAJ,IAAI,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAiDI,aAAa,EAvIpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8ET,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,4oCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,IAAI,EAAA,UAAA,EAAA,CAAA;kBAxFhB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,QAAQ,EACR,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8ET,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,4oCAAA,CAAA,EAAA,CAAA;+GAOQ,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMI,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEQ,aAAa,EAAA,CAAA;sBAAlD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,MAAM,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;;MAqN3B,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,iBAxQV,IAAI,CAAA,EAAA,OAAA,EAAA,CAoQH,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,aApQxD,IAAI,EAqQG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;AAGhD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EAJT,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EACjD,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGhD,UAAU,EAAA,UAAA,EAAA,CAAA;kBALtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;oBAClE,OAAO,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;oBAC1D,YAAY,EAAE,CAAC,IAAI,CAAC;AACvB,iBAAA,CAAA;;;AC5WD;;AAEG;;;;"}