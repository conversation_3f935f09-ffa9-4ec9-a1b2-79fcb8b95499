{"version": 3, "file": "primeng-knob.mjs", "sources": ["../../src/app/components/knob/knob.ts", "../../src/app/components/knob/primeng-knob.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT } from '@angular/common';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Inject, Input, NgModule, Output, Renderer2, ViewEncapsulation, booleanAttribute, forwardRef, numberAttribute } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { VoidListener } from 'primeng/ts-helpers';\n\nexport const KNOB_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Knob),\n    multi: true\n};\n/**\n * Knob is a form component to define number inputs with a dial.\n * @group Components\n */\n@Component({\n    selector: 'p-knob',\n    template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-name]=\"'knob'\" [attr.data-pc-section]=\"'root'\">\n            <svg\n                viewBox=\"0 0 100 100\"\n                role=\"slider\"\n                [style.width]=\"size + 'px'\"\n                [style.height]=\"size + 'px'\"\n                (click)=\"onClick($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                (mousedown)=\"onMouseDown($event)\"\n                (mouseup)=\"onMouseUp($event)\"\n                (touchstart)=\"onTouchStart($event)\"\n                (touchend)=\"onTouchEnd($event)\"\n                [attr.aria-valuemin]=\"min\"\n                [attr.aria-valuemax]=\"max\"\n                [attr.aria-valuenow]=\"_value\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.tabindex]=\"readonly || disabled ? -1 : tabindex\"\n                [attr.data-pc-section]=\"'svg'\"\n            >\n                <path [attr.d]=\"rangePath()\" [attr.stroke-width]=\"strokeWidth\" [attr.stroke]=\"rangeColor\" class=\"p-knob-range\"></path>\n                <path [attr.d]=\"valuePath()\" [attr.stroke-width]=\"strokeWidth\" [attr.stroke]=\"valueColor\" class=\"p-knob-value\"></path>\n                <text *ngIf=\"showValue\" [attr.x]=\"50\" [attr.y]=\"57\" text-anchor=\"middle\" [attr.fill]=\"textColor\" class=\"p-knob-text\" [attr.name]=\"name\">{{ valueToDisplay() }}</text>\n            </svg>\n        </div>\n    `,\n    providers: [KNOB_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./knob.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Knob {\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n    /**\n     * Background of the value.\n     * @group Props\n     */\n    @Input() valueColor: string = 'var(--primary-color, Black)';\n    /**\n     * Background color of the range.\n     * @group Props\n     */\n    @Input() rangeColor: string = 'var(--surface-border, LightGray)';\n    /**\n     * Color of the value text.\n     * @group Props\n     */\n    @Input() textColor: string = 'var(--text-color-secondary, Black)';\n    /**\n     * Template string of the value.\n     * @group Props\n     */\n    @Input() valueTemplate: string = '{value}';\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * Size of the component in pixels.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) size: number = 100;\n    /**\n     * Step factor to increment/decrement the value.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) step: number = 1;\n    /**\n     * Mininum boundary value.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) min: number = 0;\n    /**\n     * Maximum boundary value.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) max: number = 100;\n    /**\n     * Width of the knob stroke.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) strokeWidth: number = 14;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Whether the show the value inside the knob.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showValue: boolean = true;\n    /**\n     * When present, it specifies that the component value cannot be edited.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean = false;\n    /**\n     * Callback to invoke on value change.\n     * @param {number} value - New value.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<number> = new EventEmitter<number>();\n\n    radius: number = 40;\n\n    midX: number = 50;\n\n    midY: number = 50;\n\n    minRadians: number = (4 * Math.PI) / 3;\n\n    maxRadians: number = -Math.PI / 3;\n\n    value: number = 0;\n\n    windowMouseMoveListener: VoidListener;\n\n    windowMouseUpListener: VoidListener;\n\n    windowTouchMoveListener: VoidListener;\n\n    windowTouchEndListener: VoidListener;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    constructor(@Inject(DOCUMENT) private document: Document, private renderer: Renderer2, private cd: ChangeDetectorRef, private el: ElementRef) {}\n\n    mapRange(x: number, inMin: number, inMax: number, outMin: number, outMax: number) {\n        return ((x - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;\n    }\n\n    onClick(event: MouseEvent) {\n        if (!this.disabled && !this.readonly) {\n            this.updateValue(event.offsetX, event.offsetY);\n        }\n    }\n\n    updateValue(offsetX: number, offsetY: number) {\n        let dx = offsetX - this.size / 2;\n        let dy = this.size / 2 - offsetY;\n        let angle = Math.atan2(dy, dx);\n        let start = -Math.PI / 2 - Math.PI / 6;\n        this.updateModel(angle, start);\n    }\n\n    updateModel(angle: number, start: number) {\n        let mappedValue;\n        if (angle > this.maxRadians) mappedValue = this.mapRange(angle, this.minRadians, this.maxRadians, this.min, this.max);\n        else if (angle < start) mappedValue = this.mapRange(angle + 2 * Math.PI, this.minRadians, this.maxRadians, this.min, this.max);\n        else return;\n\n        let newValue = Math.round((mappedValue - this.min) / this.step) * this.step + this.min;\n        this.value = newValue;\n        this.onModelChange(this.value);\n        this.onChange.emit(this.value);\n    }\n\n    onMouseDown(event: MouseEvent) {\n        if (!this.disabled && !this.readonly) {\n            const window = this.document.defaultView || 'window';\n            this.windowMouseMoveListener = this.renderer.listen(window, 'mousemove', this.onMouseMove.bind(this));\n            this.windowMouseUpListener = this.renderer.listen(window, 'mouseup', this.onMouseUp.bind(this));\n            event.preventDefault();\n        }\n    }\n\n    onMouseUp(event: MouseEvent) {\n        if (!this.disabled && !this.readonly) {\n            if (this.windowMouseMoveListener) {\n                this.windowMouseMoveListener();\n                this.windowMouseUpListener = null;\n            }\n\n            if (this.windowMouseUpListener) {\n                this.windowMouseUpListener();\n                this.windowMouseMoveListener = null;\n            }\n            event.preventDefault();\n        }\n    }\n\n    onTouchStart(event: TouchEvent) {\n        if (!this.disabled && !this.readonly) {\n            const window = this.document.defaultView || 'window';\n            this.windowTouchMoveListener = this.renderer.listen(window, 'touchmove', this.onTouchMove.bind(this));\n            this.windowTouchEndListener = this.renderer.listen(window, 'touchend', this.onTouchEnd.bind(this));\n            event.preventDefault();\n        }\n    }\n\n    onTouchEnd(event: TouchEvent) {\n        if (!this.disabled && !this.readonly) {\n            if (this.windowTouchMoveListener) {\n                this.windowTouchMoveListener();\n            }\n            if (this.windowTouchEndListener) {\n                this.windowTouchEndListener();\n            }\n            this.windowTouchMoveListener = null;\n            this.windowTouchEndListener = null;\n            event.preventDefault();\n        }\n    }\n\n    onMouseMove(event: MouseEvent) {\n        if (!this.disabled && !this.readonly) {\n            this.updateValue(event.offsetX, event.offsetY);\n            event.preventDefault();\n        }\n    }\n\n    onTouchMove(event: Event) {\n        if (!this.disabled && !this.readonly && event instanceof TouchEvent && event.touches.length === 1) {\n            const rect = this.el.nativeElement.children[0].getBoundingClientRect();\n            const touch = event.targetTouches.item(0);\n            if (touch) {\n                const offsetX = touch.clientX - rect.left;\n                const offsetY = touch.clientY - rect.top;\n                this.updateValue(offsetX, offsetY);\n            }\n        }\n    }\n\n    updateModelValue(newValue) {\n        if (newValue > this.max) this.value = this.max;\n        else if (newValue < this.min) this.value = this.min;\n        else this.value = newValue;\n\n        this.onModelChange(this.value);\n        this.onChange.emit(this.value);\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        if (!this.disabled && !this.readonly) {\n            switch (event.code) {\n                case 'ArrowRight':\n\n                case 'ArrowUp': {\n                    event.preventDefault();\n                    this.updateModelValue(this._value + 1);\n                    break;\n                }\n\n                case 'ArrowLeft':\n\n                case 'ArrowDown': {\n                    event.preventDefault();\n                    this.updateModelValue(this._value - 1);\n                    break;\n                }\n\n                case 'Home': {\n                    event.preventDefault();\n                    this.updateModelValue(this.min);\n\n                    break;\n                }\n\n                case 'End': {\n                    event.preventDefault();\n                    this.updateModelValue(this.max);\n                    break;\n                }\n\n                case 'PageUp': {\n                    event.preventDefault();\n                    this.updateModelValue(this._value + 10);\n                    break;\n                }\n\n                case 'PageDown': {\n                    event.preventDefault();\n                    this.updateModelValue(this._value - 10);\n                    break;\n                }\n            }\n        }\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    containerClass() {\n        return {\n            'p-knob p-component': true,\n            'p-disabled': this.disabled\n        };\n    }\n\n    rangePath() {\n        return `M ${this.minX()} ${this.minY()} A ${this.radius} ${this.radius} 0 1 1 ${this.maxX()} ${this.maxY()}`;\n    }\n\n    valuePath() {\n        return `M ${this.zeroX()} ${this.zeroY()} A ${this.radius} ${this.radius} 0 ${this.largeArc()} ${this.sweep()} ${this.valueX()} ${this.valueY()}`;\n    }\n\n    zeroRadians() {\n        if (this.min > 0 && this.max > 0) return this.mapRange(this.min, this.min, this.max, this.minRadians, this.maxRadians);\n        else return this.mapRange(0, this.min, this.max, this.minRadians, this.maxRadians);\n    }\n\n    valueRadians() {\n        return this.mapRange(this._value, this.min, this.max, this.minRadians, this.maxRadians);\n    }\n\n    minX() {\n        return this.midX + Math.cos(this.minRadians) * this.radius;\n    }\n\n    minY() {\n        return this.midY - Math.sin(this.minRadians) * this.radius;\n    }\n\n    maxX() {\n        return this.midX + Math.cos(this.maxRadians) * this.radius;\n    }\n\n    maxY() {\n        return this.midY - Math.sin(this.maxRadians) * this.radius;\n    }\n\n    zeroX() {\n        return this.midX + Math.cos(this.zeroRadians()) * this.radius;\n    }\n\n    zeroY() {\n        return this.midY - Math.sin(this.zeroRadians()) * this.radius;\n    }\n\n    valueX() {\n        return this.midX + Math.cos(this.valueRadians()) * this.radius;\n    }\n\n    valueY() {\n        return this.midY - Math.sin(this.valueRadians()) * this.radius;\n    }\n\n    largeArc() {\n        return Math.abs(this.zeroRadians() - this.valueRadians()) < Math.PI ? 0 : 1;\n    }\n\n    sweep() {\n        return this.valueRadians() > this.zeroRadians() ? 0 : 1;\n    }\n\n    valueToDisplay() {\n        return this.valueTemplate.replace('{value}', this._value.toString());\n    }\n\n    get _value(): number {\n        return this.value != null ? this.value : this.min;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Knob],\n    declarations: [Knob]\n})\nexport class KnobModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAKa,MAAA,mBAAmB,GAAQ;AACpC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,IAAI,CAAC;AACnC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MAsCU,IAAI,CAAA;AA0HyB,IAAA,QAAA,CAAA;AAA4B,IAAA,QAAA,CAAA;AAA6B,IAAA,EAAA,CAAA;AAA+B,IAAA,EAAA,CAAA;AAzH9H;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACoC,QAAQ,GAAW,CAAC,CAAC;AAC5D;;;AAGG;IACM,UAAU,GAAW,6BAA6B,CAAC;AAC5D;;;AAGG;IACM,UAAU,GAAW,kCAAkC,CAAC;AACjE;;;AAGG;IACM,SAAS,GAAW,oCAAoC,CAAC;AAClE;;;AAGG;IACM,aAAa,GAAW,SAAS,CAAC;AAC3C;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;IACoC,IAAI,GAAW,GAAG,CAAC;AAC1D;;;AAGG;IACoC,IAAI,GAAW,CAAC,CAAC;AACxD;;;AAGG;IACoC,GAAG,GAAW,CAAC,CAAC;AACvD;;;AAGG;IACoC,GAAG,GAAW,GAAG,CAAC;AACzD;;;AAGG;IACoC,WAAW,GAAW,EAAE,CAAC;AAChE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACqC,SAAS,GAAY,IAAI,CAAC;AAClE;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;;AAIG;AACO,IAAA,QAAQ,GAAyB,IAAI,YAAY,EAAU,CAAC;IAEtE,MAAM,GAAW,EAAE,CAAC;IAEpB,IAAI,GAAW,EAAE,CAAC;IAElB,IAAI,GAAW,EAAE,CAAC;IAElB,UAAU,GAAW,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAEvC,IAAA,UAAU,GAAW,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAElC,KAAK,GAAW,CAAC,CAAC;AAElB,IAAA,uBAAuB,CAAe;AAEtC,IAAA,qBAAqB,CAAe;AAEpC,IAAA,uBAAuB,CAAe;AAEtC,IAAA,sBAAsB,CAAe;AAErC,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,WAAA,CAAsC,QAAkB,EAAU,QAAmB,EAAU,EAAqB,EAAU,EAAc,EAAA;QAAtG,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;IAEhJ,QAAQ,CAAC,CAAS,EAAE,KAAa,EAAE,KAAa,EAAE,MAAc,EAAE,MAAc,EAAA;QAC5E,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,KAAK,MAAM,GAAG,MAAM,CAAC,KAAK,KAAK,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;KACvE;AAED,IAAA,OAAO,CAAC,KAAiB,EAAA;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAClD,SAAA;KACJ;IAED,WAAW,CAAC,OAAe,EAAE,OAAe,EAAA;QACxC,IAAI,EAAE,GAAG,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACjC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,OAAO,CAAC;QACjC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/B,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KAClC;IAED,WAAW,CAAC,KAAa,EAAE,KAAa,EAAA;AACpC,QAAA,IAAI,WAAW,CAAC;AAChB,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU;YAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;aACjH,IAAI,KAAK,GAAG,KAAK;AAAE,YAAA,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;;YAC1H,OAAO;QAEZ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;AACvF,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAClC;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;YACrD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACtG,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAChG,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAiB,EAAA;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,gBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,aAAA;YAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,gBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACvC,aAAA;YACD,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAiB,EAAA;QAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;YACrD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACtG,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACnG,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAiB,EAAA;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAClC,aAAA;YACD,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AACjC,aAAA;AACD,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACpC,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;QACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,YAAY,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/F,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;YACvE,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C,YAAA,IAAI,KAAK,EAAE;gBACP,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;AACzC,gBAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACtC,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,QAAQ,EAAA;AACrB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG;AAAE,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;AAC1C,aAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG;AAAE,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;;AAC/C,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;AAE3B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAClC;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,QAAQ,KAAK,CAAC,IAAI;AACd,gBAAA,KAAK,YAAY,CAAC;gBAElB,KAAK,SAAS,EAAE;oBACZ,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACvC,MAAM;AACT,iBAAA;AAED,gBAAA,KAAK,WAAW,CAAC;gBAEjB,KAAK,WAAW,EAAE;oBACd,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACvC,MAAM;AACT,iBAAA;gBAED,KAAK,MAAM,EAAE;oBACT,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,oBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAEhC,MAAM;AACT,iBAAA;gBAED,KAAK,KAAK,EAAE;oBACR,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,oBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAChC,MAAM;AACT,iBAAA;gBAED,KAAK,QAAQ,EAAE;oBACX,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;oBACxC,MAAM;AACT,iBAAA;gBAED,KAAK,UAAU,EAAE;oBACb,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;oBACxC,MAAM;AACT,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,cAAc,GAAA;QACV,OAAO;AACH,YAAA,oBAAoB,EAAE,IAAI;YAC1B,YAAY,EAAE,IAAI,CAAC,QAAQ;SAC9B,CAAC;KACL;IAED,SAAS,GAAA;AACL,QAAA,OAAO,CAAK,EAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,IAAI,EAAE,CAAM,GAAA,EAAA,IAAI,CAAC,MAAM,CAAI,CAAA,EAAA,IAAI,CAAC,MAAM,CAAU,OAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;KAChH;IAED,SAAS,GAAA;AACL,QAAA,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,MAAM,CAAI,CAAA,EAAA,IAAI,CAAC,MAAM,CAAA,GAAA,EAAM,IAAI,CAAC,QAAQ,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,KAAK,EAAE,CAAI,CAAA,EAAA,IAAI,CAAC,MAAM,EAAE,CAAI,CAAA,EAAA,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;KACrJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;YAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;;YAClH,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;KACtF;IAED,YAAY,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;KAC3F;IAED,IAAI,GAAA;AACA,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KAC9D;IAED,IAAI,GAAA;AACA,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KAC9D;IAED,IAAI,GAAA;AACA,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KAC9D;IAED,IAAI,GAAA;AACA,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KAC9D;IAED,KAAK,GAAA;AACD,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KACjE;IAED,KAAK,GAAA;AACD,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KACjE;IAED,MAAM,GAAA;AACF,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KAClE;IAED,MAAM,GAAA;AACF,QAAA,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;KAClE;IAED,QAAQ,GAAA;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;KAC/E;IAED,KAAK,GAAA;AACD,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;KAC3D;IAED,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;KACxE;AAED,IAAA,IAAI,MAAM,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;KACrD;AA5WQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAI,kBA0HO,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA1HnB,IAAI,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAyBO,eAAe,CA8Bf,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,0BAKf,eAAe,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAKf,eAAe,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAKf,eAAe,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAKf,eAAe,CAKf,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,yCAKhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAlGzB,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,mBAAmB,CAAC,EA3BtB,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,uPAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FASQ,IAAI,EAAA,UAAA,EAAA,CAAA;kBArChB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,QAAQ,EACR,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;KA0BT,EACU,SAAA,EAAA,CAAC,mBAAmB,CAAC,EACf,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,uPAAA,CAAA,EAAA,CAAA;;0BA4HY,MAAM;2BAAC,QAAQ,CAAA;0HArHnB,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKiC,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;;MAoRE,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EApXV,YAAA,EAAA,CAAA,IAAI,CAgXH,EAAA,OAAA,EAAA,CAAA,YAAY,aAhXb,IAAI,CAAA,EAAA,CAAA,CAAA;AAoXJ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,YAJT,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,UAAU,EAAA,UAAA,EAAA,CAAA;kBALtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,IAAI,CAAC;oBACf,YAAY,EAAE,CAAC,IAAI,CAAC;AACvB,iBAAA,CAAA;;;ACtaD;;AAEG;;;;"}