import { DOCUMENT } from '@angular/common';
import { Injectable, inject } from '@angular/core';
import { TranslocoService } from '@jsverse/transloco';

@Injectable({
  providedIn: 'root',
})
export class TranslationService {
  translate = inject(TranslocoService);
  document = inject(DOCUMENT);

  setLanguage(lang: string) {
    let dir = "ltr";
    if (lang === 'ar') {
      dir = 'rtl';
    }
    // Update page dir and lang
    this.document.dir = dir;
    this.document.documentElement.lang = lang;

    // Save lang in local storage and set as default
    localStorage.setItem('language', lang);
    this.translate.setDefaultLang(lang);
    this.translate.setActiveLang(lang);
  }

  initializeLanguage() {
    const lang = localStorage.getItem('language') || 'en'; // Default to 'en' if no language is set
    this.setLanguage(lang);
  }
}
