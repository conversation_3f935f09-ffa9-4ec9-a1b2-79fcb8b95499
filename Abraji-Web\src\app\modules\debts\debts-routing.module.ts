import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AllDebtsComponent } from './all-debts/all-debts.component';
import { DebtHistoryComponent } from './debt-history/debt-history.component';
import { validateIdNumberGuard } from '../../core/guards/validate-id-number.guard';

const routes: Routes = [
  {
    path: 'all',
    component: AllDebtsComponent,
  },
  {
    path: 'history/:id',
    canActivate: [validateIdNumberGuard],
    component: DebtHistoryComponent,
  },
  {
    path: '',
    redirectTo: 'all',
    pathMatch: 'full',
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DebtsRoutingModule { }
