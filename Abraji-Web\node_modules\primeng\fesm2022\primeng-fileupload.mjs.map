{"version": 3, "file": "primeng-fileupload.mjs", "sources": ["../../src/app/components/fileupload/fileupload.ts", "../../src/app/components/fileupload/primeng-fileupload.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { HttpClient, HttpEvent, HttpEventType, HttpHeaders } from '@angular/common/http';\nimport {\n    AfterContentInit,\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    NgZone,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { BlockableUI, Message, PrimeNGConfig, PrimeTemplate, SharedModule, TranslationKeys } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler } from 'primeng/dom';\nimport { PlusIcon } from 'primeng/icons/plus';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { UploadIcon } from 'primeng/icons/upload';\nimport { MessagesModule } from 'primeng/messages';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { RippleModule } from 'primeng/ripple';\nimport { VoidListener } from 'primeng/ts-helpers';\nimport { Subscription } from 'rxjs';\nimport { FileBeforeUploadEvent, FileProgressEvent, FileRemoveEvent, FileSelectEvent, FileSendEvent, FileUploadErrorEvent, FileUploadEvent, FileUploadHandlerEvent, RemoveUploadedFileEvent } from './fileupload.interface';\n/**\n * FileUpload is an advanced uploader with dragdrop support, multi file uploads, auto uploading, progress tracking and validations.\n * @group Components\n */\n@Component({\n    selector: 'p-fileUpload',\n    template: `\n        <div [ngClass]=\"'p-fileupload p-fileupload-advanced p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" *ngIf=\"mode === 'advanced'\" [attr.data-pc-name]=\"'fileupload'\" [attr.data-pc-section]=\"'root'\">\n            <input\n                [attr.aria-label]=\"browseFilesLabel\"\n                #advancedfileinput\n                type=\"file\"\n                (change)=\"onFileSelect($event)\"\n                [multiple]=\"multiple\"\n                [accept]=\"accept\"\n                [disabled]=\"disabled || isChooseDisabled()\"\n                [attr.title]=\"''\"\n                [attr.data-pc-section]=\"'input'\"\n                [style.display]=\"'none'\"\n            />\n            <div class=\"p-fileupload-buttonbar\" [attr.data-pc-section]=\"'buttonbar'\">\n                <ng-container *ngIf=\"!headerTemplate\">\n                    <span\n                        class=\"p-button p-component p-fileupload-choose\"\n                        [ngClass]=\"{ 'p-focus': focus, 'p-disabled': disabled || isChooseDisabled() }\"\n                        (focus)=\"onFocus()\"\n                        (blur)=\"onBlur()\"\n                        pRipple\n                        (click)=\"choose()\"\n                        (keydown.enter)=\"choose()\"\n                        tabindex=\"0\"\n                        [class]=\"chooseStyleClass\"\n                        [attr.data-pc-section]=\"'choosebutton'\"\n                    >\n                        <input\n                            [attr.aria-label]=\"browseFilesLabel\"\n                            #advancedfileinput\n                            type=\"file\"\n                            (change)=\"onFileSelect($event)\"\n                            [multiple]=\"multiple\"\n                            [accept]=\"accept\"\n                            [disabled]=\"disabled || isChooseDisabled()\"\n                            [attr.title]=\"''\"\n                            [attr.data-pc-section]=\"'input'\"\n                        />\n                        <span *ngIf=\"chooseIcon\" [ngClass]=\"'p-button-icon p-button-icon-left'\" [class]=\"chooseIcon\" [attr.aria-label]=\"true\" [attr.data-pc-section]=\"'chooseicon'\"></span>\n                        <ng-container *ngIf=\"!chooseIcon\">\n                            <PlusIcon *ngIf=\"!chooseIconTemplate\" [styleClass]=\"'p-button-icon p-button-icon-left'\" [attr.aria-label]=\"true\" [attr.data-pc-section]=\"'chooseicon'\" />\n                            <span *ngIf=\"chooseIconTemplate\" class=\"p-button-icon p-button-icon-left\" [attr.aria-label]=\"true\" [attr.data-pc-section]=\"'chooseicon'\">\n                                <ng-template *ngTemplateOutlet=\"chooseIconTemplate\"></ng-template>\n                            </span>\n                        </ng-container>\n                        <span class=\"p-button-label\" [attr.data-pc-section]=\"'choosebuttonlabel'\">{{ chooseButtonLabel }}</span>\n                    </span>\n\n                    <p-button *ngIf=\"!auto && showUploadButton\" type=\"button\" [label]=\"uploadButtonLabel\" (onClick)=\"upload()\" [disabled]=\"!hasFiles() || isFileLimitExceeded()\" [styleClass]=\"uploadStyleClass\">\n                        <span *ngIf=\"uploadIcon\" [ngClass]=\"uploadIcon\" [attr.aria-hidden]=\"true\" class=\"p-button-icon p-button-icon-left\"></span>\n                        <ng-container *ngIf=\"!uploadIcon\">\n                            <UploadIcon *ngIf=\"!uploadIconTemplate\" [styleClass]=\"'p-button-icon p-button-icon-left'\" />\n                            <span *ngIf=\"uploadIconTemplate\" class=\"p-button-icon p-button-icon-left\" [attr.aria-hidden]=\"true\">\n                                <ng-template *ngTemplateOutlet=\"uploadIconTemplate\"></ng-template>\n                            </span>\n                        </ng-container>\n                    </p-button>\n                    <p-button *ngIf=\"!auto && showCancelButton\" type=\"button\" [label]=\"cancelButtonLabel\" (onClick)=\"clear()\" [disabled]=\"!hasFiles() || uploading\" [styleClass]=\"cancelStyleClass\">\n                        <span *ngIf=\"cancelIcon\" [ngClass]=\"cancelIcon\" class=\"p-button-icon p-button-icon-left\"></span>\n                        <ng-container *ngIf=\"!cancelIcon\">\n                            <TimesIcon *ngIf=\"!cancelIconTemplate\" [styleClass]=\"'p-button-icon p-button-icon-left'\" [attr.aria-hidden]=\"true\" />\n                            <span *ngIf=\"cancelIconTemplate\" class=\"p-button-icon p-button-icon-left\" [attr.aria-hidden]=\"true\">\n                                <ng-template *ngTemplateOutlet=\"cancelIconTemplate\"></ng-template>\n                            </span>\n                        </ng-container>\n                    </p-button>\n                </ng-container>\n                <ng-container *ngTemplateOutlet=\"headerTemplate; context: { $implicit: files, uploadedFiles: uploadedFiles, chooseCallback: choose.bind(this), clearCallback: clear.bind(this), uploadCallback: upload.bind(this) }\"></ng-container>\n                <ng-container *ngTemplateOutlet=\"toolbarTemplate\"></ng-container>\n            </div>\n            <div #content class=\"p-fileupload-content\" (dragenter)=\"onDragEnter($event)\" (dragleave)=\"onDragLeave($event)\" (drop)=\"onDrop($event)\" [attr.data-pc-section]=\"'content'\">\n                <p-progressBar [value]=\"progress\" [showValue]=\"false\" *ngIf=\"hasFiles()\"></p-progressBar>\n\n                <p-messages [value]=\"msgs\" [enableService]=\"false\"></p-messages>\n\n                <div class=\"p-fileupload-files\" *ngIf=\"hasFiles()\">\n                    <div *ngIf=\"!fileTemplate\">\n                        <div class=\"p-fileupload-row\" *ngFor=\"let file of files; let i = index\">\n                            <div *ngIf=\"isImage(file)\"><img [src]=\"file.objectURL\" [width]=\"previewWidth\" (error)=\"imageError($event)\" /></div>\n                            <div class=\"p-fileupload-filename\">{{ file.name }}</div>\n                            <div>{{ formatSize(file.size) }}</div>\n                            <div>\n                                <button type=\"button\" pButton (click)=\"remove($event, i)\" [disabled]=\"uploading\" class=\"p-button-icon-only\" [class]=\"removeStyleClass\">\n                                    <TimesIcon *ngIf=\"!cancelIconTemplate\" />\n                                    <ng-template *ngTemplateOutlet=\"cancelIconTemplate\"></ng-template>\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                    <div *ngIf=\"fileTemplate\">\n                        <ng-template ngFor [ngForOf]=\"files\" [ngForTemplate]=\"fileTemplate\"></ng-template>\n                    </div>\n                </div>\n                <ng-container\n                    *ngTemplateOutlet=\"\n                        contentTemplate;\n                        context: {\n                            $implicit: files,\n                            uploadedFiles: uploadedFiles,\n                            chooseCallback: choose.bind(this),\n                            clearCallback: clear.bind(this),\n                            removeUploadedFileCallback: removeUploadedFile.bind(this),\n                            removeFileCallback: remove.bind(this),\n                            progress: progress,\n                            messages: msgs\n                        }\n                    \"\n                ></ng-container>\n                <div *ngIf=\"emptyTemplate && !hasFiles() && !hasUploadedFiles()\" class=\"p-fileupload-empty\">\n                    <ng-container *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                </div>\n            </div>\n        </div>\n        <div class=\"p-fileupload p-fileupload-basic p-component\" *ngIf=\"mode === 'basic'\" [attr.data-pc-name]=\"'fileupload'\">\n            <p-messages [value]=\"msgs\" [enableService]=\"false\"></p-messages>\n            <span\n                [ngClass]=\"{ 'p-button p-component p-fileupload-choose': true, 'p-button-icon-only': !basicButtonLabel, 'p-fileupload-choose-selected': hasFiles(), 'p-focus': focus, 'p-disabled': disabled }\"\n                [ngStyle]=\"style\"\n                [class]=\"styleClass\"\n                (click)=\"onBasicUploaderClick()\"\n                (keydown)=\"onBasicKeydown($event)\"\n                tabindex=\"0\"\n                pRipple\n                [attr.data-pc-section]=\"'choosebutton'\"\n            >\n                <ng-container *ngIf=\"hasFiles() && !auto; else chooseSection\">\n                    <span *ngIf=\"uploadIcon\" class=\"p-button-icon p-button-icon-left\" [ngClass]=\"uploadIcon\"></span>\n                    <ng-container *ngIf=\"!uploadIcon\">\n                        <UploadIcon *ngIf=\"!uploadIconTemplate\" [styleClass]=\"'p-button-icon p-button-icon-left'\" />\n                        <span *ngIf=\"uploadIconTemplate\" class=\"p-button-icon p-button-icon-left\">\n                            <ng-template *ngTemplateOutlet=\"uploadIconTemplate\"></ng-template>\n                        </span>\n                    </ng-container>\n                </ng-container>\n                <ng-template #chooseSection>\n                    <span *ngIf=\"chooseIcon\" class=\"p-button-icon p-button-icon-left pi\" [ngClass]=\"chooseIcon\"></span>\n                    <ng-container *ngIf=\"!chooseIcon\">\n                        <PlusIcon [styleClass]=\"'p-button-icon p-button-icon-left pi'\" *ngIf=\"!chooseIconTemplate\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'uploadicon'\" />\n                        <span *ngIf=\"chooseIconTemplate\" class=\"p-button-icon p-button-icon-left pi\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'uploadicon'\">\n                            <ng-template *ngTemplateOutlet=\"chooseIconTemplate\"></ng-template>\n                        </span>\n                    </ng-container>\n                </ng-template>\n                <span *ngIf=\"basicButtonLabel\" class=\"p-button-label\" [attr.data-pc-section]=\"'label'\">{{ basicButtonLabel }}</span>\n                <input\n                    [attr.aria-label]=\"browseFilesLabel\"\n                    #basicfileinput\n                    type=\"file\"\n                    [accept]=\"accept\"\n                    [multiple]=\"multiple\"\n                    [disabled]=\"disabled\"\n                    (change)=\"onFileSelect($event)\"\n                    *ngIf=\"!hasFiles()\"\n                    (focus)=\"onFocus()\"\n                    (blur)=\"onBlur()\"\n                    [attr.data-pc-section]=\"'input'\"\n                />\n            </span>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./fileupload.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class FileUpload implements AfterViewInit, AfterContentInit, OnInit, OnDestroy, BlockableUI {\n    /**\n     * Name of the request parameter to identify the files at backend.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * Remote url to upload the files.\n     * @group Props\n     */\n    @Input() url: string | undefined;\n    /**\n     * HTTP method to send the files to the url such as \"post\" and \"put\".\n     * @group Props\n     */\n    @Input() method: 'post' | 'put' | undefined = 'post';\n    /**\n     * Used to select multiple files at once from file dialog.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) multiple: boolean | undefined;\n    /**\n     * Comma-separated list of pattern to restrict the allowed file types. Can be any combination of either the MIME types (such as \"image/*\") or the file extensions (such as \".jpg\").\n     * @group Props\n     */\n    @Input() accept: string | undefined;\n    /**\n     * Disables the upload functionality.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * When enabled, upload begins automatically after selection is completed.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) auto: boolean | undefined;\n    /**\n     * Cross-site Access-Control requests should be made using credentials such as cookies, authorization headers or TLS client certificates.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) withCredentials: boolean | undefined;\n    /**\n     * Maximum file size allowed in bytes.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) maxFileSize: number | undefined;\n    /**\n     * Summary message of the invalid file size.\n     * @group Props\n     */\n    @Input() invalidFileSizeMessageSummary: string = '{0}: Invalid file size, ';\n    /**\n     * Detail message of the invalid file size.\n     * @group Props\n     */\n    @Input() invalidFileSizeMessageDetail: string = 'maximum upload size is {0}.';\n    /**\n     * Summary message of the invalid file type.\n     * @group Props\n     */\n    @Input() invalidFileTypeMessageSummary: string = '{0}: Invalid file type, ';\n    /**\n     * Detail message of the invalid file type.\n     * @group Props\n     */\n    @Input() invalidFileTypeMessageDetail: string = 'allowed file types: {0}.';\n    /**\n     * Detail message of the invalid file type.\n     * @group Props\n     */\n    @Input() invalidFileLimitMessageDetail: string = 'limit is {0} at most.';\n    /**\n     * Summary message of the invalid file type.\n     * @group Props\n     */\n    @Input() invalidFileLimitMessageSummary: string = 'Maximum number of files exceeded, ';\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Width of the image thumbnail in pixels.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) previewWidth: number = 50;\n    /**\n     * Label of the choose button. Defaults to PrimeNG Locale configuration.\n     * @group Props\n     */\n    @Input() chooseLabel: string | undefined;\n    /**\n     * Label of the upload button. Defaults to PrimeNG Locale configuration.\n     * @group Props\n     */\n    @Input() uploadLabel: string | undefined;\n    /**\n     * Label of the cancel button. Defaults to PrimeNG Locale configuration.\n     * @group Props\n     */\n    @Input() cancelLabel: string | undefined;\n    /**\n     * Icon of the choose button.\n     * @group Props\n     */\n    @Input() chooseIcon: string | undefined;\n    /**\n     * Icon of the upload button.\n     * @group Props\n     */\n    @Input() uploadIcon: string | undefined;\n    /**\n     * Icon of the cancel button.\n     * @group Props\n     */\n    @Input() cancelIcon: string | undefined;\n    /**\n     * Whether to show the upload button.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showUploadButton: boolean = true;\n    /**\n     * Whether to show the cancel button.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showCancelButton: boolean = true;\n    /**\n     * Defines the UI of the component.\n     * @group Props\n     */\n    @Input() mode: 'advanced' | 'basic' | undefined = 'advanced';\n    /**\n     * HttpHeaders class represents the header configuration options for an HTTP request.\n     * @group Props\n     */\n    @Input() headers: HttpHeaders | undefined;\n    /**\n     * Whether to use the default upload or a manual implementation defined in uploadHandler callback. Defaults to PrimeNG Locale configuration.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) customUpload: boolean | undefined;\n    /**\n     * Maximum number of files that can be uploaded.\n     * @group Props\n     */\n    @Input({ transform: (value: unknown) => numberAttribute(value, null) }) fileLimit: number | undefined;\n    /**\n     * Style class of the upload button.\n     * @group Props\n     */\n    @Input() uploadStyleClass: string | undefined;\n    /**\n     * Style class of the cancel button.\n     * @group Props\n     */\n    @Input() cancelStyleClass: string | undefined;\n    /**\n     * Style class of the remove button.\n     * @group Props\n     */\n    @Input() removeStyleClass: string | undefined;\n    /**\n     * Style class of the choose button.\n     * @group Props\n     */\n    @Input() chooseStyleClass: string | undefined;\n    /**\n     * Callback to invoke before file upload is initialized.\n     * @param {FileBeforeUploadEvent} event - Custom upload event.\n     * @group Emits\n     */\n    @Output() onBeforeUpload: EventEmitter<FileBeforeUploadEvent> = new EventEmitter<FileBeforeUploadEvent>();\n    /**\n     * An event indicating that the request was sent to the server. Useful when a request may be retried multiple times, to distinguish between retries on the final event stream.\n     * @param {FileSendEvent} event - Custom send event.\n     * @group Emits\n     */\n    @Output() onSend: EventEmitter<FileSendEvent> = new EventEmitter<FileSendEvent>();\n    /**\n     * Callback to invoke when file upload is complete.\n     * @param {FileUploadEvent} event - Custom upload event.\n     * @group Emits\n     */\n    @Output() onUpload: EventEmitter<FileUploadEvent> = new EventEmitter<FileUploadEvent>();\n    /**\n     * Callback to invoke if file upload fails.\n     * @param {FileUploadErrorEvent} event - Custom error event.\n     * @group Emits\n     */\n    @Output() onError: EventEmitter<FileUploadErrorEvent> = new EventEmitter<FileUploadErrorEvent>();\n    /**\n     * Callback to invoke when files in queue are removed without uploading using clear all button.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when a file is removed without uploading using clear button of a file.\n     * @param {FileRemoveEvent} event - Remove event.\n     * @group Emits\n     */\n    @Output() onRemove: EventEmitter<FileRemoveEvent> = new EventEmitter<FileRemoveEvent>();\n    /**\n     * Callback to invoke when files are selected.\n     * @param {FileSelectEvent} event - Select event.\n     * @group Emits\n     */\n    @Output() onSelect: EventEmitter<FileSelectEvent> = new EventEmitter<FileSelectEvent>();\n    /**\n     * Callback to invoke when files are being uploaded.\n     * @param {FileProgressEvent} event - Progress event.\n     * @group Emits\n     */\n    @Output() onProgress: EventEmitter<FileProgressEvent> = new EventEmitter<FileProgressEvent>();\n    /**\n     * Callback to invoke in custom upload mode to upload the files manually.\n     * @param {FileUploadHandlerEvent} event - Upload handler event.\n     * @group Emits\n     */\n    @Output() uploadHandler: EventEmitter<FileUploadHandlerEvent> = new EventEmitter<FileUploadHandlerEvent>();\n    /**\n     * This event is triggered if an error occurs while loading an image file.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onImageError: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * This event is triggered if an error occurs while loading an image file.\n     * @param {RemoveUploadedFileEvent} event - Remove event.\n     * @group Emits\n     */\n    @Output() onRemoveUploadedFile: EventEmitter<RemoveUploadedFileEvent> = new EventEmitter<RemoveUploadedFileEvent>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @ViewChild('advancedfileinput') advancedFileInput: ElementRef | undefined | any;\n\n    @ViewChild('basicfileinput') basicFileInput: ElementRef | undefined;\n\n    @ViewChild('content') content: ElementRef | undefined;\n\n    @Input() set files(files) {\n        this._files = [];\n\n        for (let i = 0; i < files.length; i++) {\n            let file = files[i];\n\n            if (this.validate(file)) {\n                if (this.isImage(file)) {\n                    (<any>file).objectURL = this.sanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(files[i]));\n                }\n\n                this._files.push(files[i]);\n            }\n        }\n    }\n\n    get files(): File[] {\n        return this._files;\n    }\n\n    public get basicButtonLabel(): string {\n        if (this.auto || !this.hasFiles()) {\n            return this.chooseLabel as string;\n        }\n\n        return this.uploadLabel ?? this.files[0].name;\n    }\n\n    public _files: File[] = [];\n\n    public progress: number = 0;\n\n    public dragHighlight: boolean | undefined;\n\n    public msgs: Message[] | undefined;\n\n    public fileTemplate: TemplateRef<any> | undefined;\n\n    public headerTemplate: TemplateRef<any> | undefined;\n\n    public contentTemplate: TemplateRef<any> | undefined;\n\n    public toolbarTemplate: TemplateRef<any> | undefined;\n\n    chooseIconTemplate: TemplateRef<any> | undefined;\n\n    uploadIconTemplate: TemplateRef<any> | undefined;\n\n    cancelIconTemplate: TemplateRef<any> | undefined;\n\n    emptyTemplate: TemplateRef<any> | undefined;\n\n    public uploadedFileCount: number = 0;\n\n    focus: boolean | undefined;\n\n    uploading: boolean | undefined;\n\n    duplicateIEEvent: boolean | undefined; // flag to recognize duplicate onchange event for file input\n\n    translationSubscription: Subscription | undefined;\n\n    dragOverListener: VoidListener;\n\n    public uploadedFiles = [];\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        private renderer: Renderer2,\n        private el: ElementRef,\n        public sanitizer: DomSanitizer,\n        public zone: NgZone,\n        private http: HttpClient,\n        public cd: ChangeDetectorRef,\n        public config: PrimeNGConfig\n    ) {}\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'file':\n                    this.fileTemplate = item.template;\n                    break;\n\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'toolbar':\n                    this.toolbarTemplate = item.template;\n                    break;\n\n                case 'chooseicon':\n                    this.chooseIconTemplate = item.template;\n                    break;\n\n                case 'uploadicon':\n                    this.uploadIconTemplate = item.template;\n                    break;\n\n                case 'cancelicon':\n                    this.cancelIconTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyTemplate = item.template;\n                    break;\n\n                default:\n                    this.fileTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngOnInit() {\n        this.translationSubscription = this.config.translationObserver.subscribe(() => {\n            this.cd.markForCheck();\n        });\n    }\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.mode === 'advanced') {\n                this.zone.runOutsideAngular(() => {\n                    if (this.content) {\n                        this.dragOverListener = this.renderer.listen(this.content.nativeElement, 'dragover', this.onDragOver.bind(this));\n                    }\n                });\n            }\n        }\n    }\n\n    getTranslation(option: string) {\n        return this.config.getTranslation(option);\n    }\n\n    choose() {\n        this.advancedFileInput?.nativeElement.click();\n    }\n\n    onFileSelect(event: any) {\n        if (event.type !== 'drop' && this.isIE11() && this.duplicateIEEvent) {\n            this.duplicateIEEvent = false;\n            return;\n        }\n\n        this.msgs = [];\n        if (!this.multiple) {\n            this.files = [];\n        }\n\n        let files = event.dataTransfer ? event.dataTransfer.files : event.target.files;\n        for (let i = 0; i < files.length; i++) {\n            let file = files[i];\n\n            if (!this.isFileSelected(file)) {\n                if (this.validate(file)) {\n                    if (this.isImage(file)) {\n                        file.objectURL = this.sanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(files[i]));\n                    }\n\n                    this.files.push(files[i]);\n                }\n            }\n        }\n\n        this.onSelect.emit({ originalEvent: event, files: files, currentFiles: this.files });\n\n        // this will check the fileLimit with the uploaded files\n        this.checkFileLimit(files);\n\n        if (this.hasFiles() && this.auto && (this.mode !== 'advanced' || !this.isFileLimitExceeded())) {\n            this.upload();\n        }\n\n        if (event.type !== 'drop' && this.isIE11()) {\n            this.clearIEInput();\n        } else {\n            this.clearInputElement();\n        }\n    }\n\n    isFileSelected(file: File): boolean {\n        for (let sFile of this.files) {\n            if (sFile.name + sFile.type + sFile.size === file.name + file.type + file.size) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    isIE11() {\n        if (isPlatformBrowser(this.platformId)) {\n            return !!(this.document.defaultView as any)['MSInputMethodContext'] && !!(this.document as any)['documentMode'];\n        }\n    }\n\n    validate(file: File): boolean {\n        this.msgs = this.msgs || [];\n        if (this.accept && !this.isFileTypeValid(file)) {\n            this.msgs.push({\n                severity: 'error',\n                summary: this.invalidFileTypeMessageSummary.replace('{0}', file.name),\n                detail: this.invalidFileTypeMessageDetail.replace('{0}', this.accept)\n            });\n            return false;\n        }\n\n        if (this.maxFileSize && file.size > this.maxFileSize) {\n            this.msgs.push({\n                severity: 'error',\n                summary: this.invalidFileSizeMessageSummary.replace('{0}', file.name),\n                detail: this.invalidFileSizeMessageDetail.replace('{0}', this.formatSize(this.maxFileSize))\n            });\n            return false;\n        }\n\n        return true;\n    }\n\n    private isFileTypeValid(file: File): boolean {\n        let acceptableTypes = this.accept?.split(',').map((type) => type.trim());\n        for (let type of acceptableTypes!) {\n            let acceptable = this.isWildcard(type) ? this.getTypeClass(file.type) === this.getTypeClass(type) : file.type == type || this.getFileExtension(file).toLowerCase() === type.toLowerCase();\n\n            if (acceptable) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    getTypeClass(fileType: string): string {\n        return fileType.substring(0, fileType.indexOf('/'));\n    }\n\n    isWildcard(fileType: string): boolean {\n        return fileType.indexOf('*') !== -1;\n    }\n\n    getFileExtension(file: File): string {\n        return '.' + file.name.split('.').pop();\n    }\n\n    isImage(file: File): boolean {\n        return /^image\\//.test(file.type);\n    }\n\n    onImageLoad(img: any) {\n        window.URL.revokeObjectURL(img.src);\n    }\n    /**\n     * Uploads the selected files.\n     * @group Method\n     */\n    upload() {\n        if (this.customUpload) {\n            if (this.fileLimit) {\n                this.uploadedFileCount += this.files.length;\n            }\n\n            this.uploadHandler.emit({\n                files: this.files\n            });\n\n            this.cd.markForCheck();\n        } else {\n            this.uploading = true;\n            this.msgs = [];\n            let formData = new FormData();\n\n            this.onBeforeUpload.emit({\n                formData: formData\n            });\n\n            for (let i = 0; i < this.files.length; i++) {\n                formData.append(this.name!, this.files[i], this.files[i].name);\n            }\n\n            this.http\n                .request(<string>this.method, this.url as string, {\n                    body: formData,\n                    headers: this.headers,\n                    reportProgress: true,\n                    observe: 'events',\n                    withCredentials: this.withCredentials\n                })\n                .subscribe(\n                    (event: HttpEvent<any>) => {\n                        switch (event.type) {\n                            case HttpEventType.Sent:\n                                this.onSend.emit({\n                                    originalEvent: event,\n                                    formData: formData\n                                });\n                                break;\n                            case HttpEventType.Response:\n                                this.uploading = false;\n                                this.progress = 0;\n\n                                if (event['status'] >= 200 && event['status'] < 300) {\n                                    if (this.fileLimit) {\n                                        this.uploadedFileCount += this.files.length;\n                                    }\n\n                                    this.onUpload.emit({ originalEvent: event, files: this.files });\n                                } else {\n                                    this.onError.emit({ files: this.files });\n                                }\n                                this.uploadedFiles.push(...this.files);\n                                this.clear();\n                                break;\n                            case HttpEventType.UploadProgress: {\n                                if (event['loaded']) {\n                                    this.progress = Math.round((event['loaded'] * 100) / event['total']!);\n                                }\n\n                                this.onProgress.emit({ originalEvent: event, progress: this.progress });\n                                break;\n                            }\n                        }\n\n                        this.cd.markForCheck();\n                    },\n                    (error: ErrorEvent) => {\n                        this.uploading = false;\n                        this.onError.emit({ files: this.files, error: error });\n                    }\n                );\n        }\n    }\n    /**\n     * Clears the files list.\n     * @group Method\n     */\n    clear() {\n        this.files = [];\n        this.uploadedFileCount = 0;\n        this.onClear.emit();\n        this.clearInputElement();\n        this.cd.markForCheck();\n    }\n    /**\n     * Removes a single file.\n     * @param {Event} event - Browser event.\n     * @param {Number} index - Index of the file.\n     * @group Method\n     */\n    remove(event: Event, index: number) {\n        this.clearInputElement();\n        this.onRemove.emit({ originalEvent: event, file: this.files[index] });\n        this.files.splice(index, 1);\n        this.checkFileLimit(this.files);\n    }\n    /**\n     * Removes uploaded file.\n     * @param {Number} index - Index of the file to be removed.\n     * @group Method\n     */\n    removeUploadedFile(index) {\n        let removedFile = this.uploadedFiles.splice(index, 1)[0];\n        this.uploadedFiles = [...this.uploadedFiles];\n        this.onRemoveUploadedFile.emit({ file: removedFile, files: this.uploadedFiles });\n    }\n\n    isFileLimitExceeded() {\n        const isAutoMode = this.auto;\n        const totalFileCount = isAutoMode ? this.files.length : this.files.length + this.uploadedFileCount;\n\n        if (this.fileLimit && this.fileLimit <= totalFileCount && this.focus) {\n            this.focus = false;\n        }\n\n        return this.fileLimit && this.fileLimit < totalFileCount;\n    }\n\n    isChooseDisabled() {\n        if (this.auto) {\n            return this.fileLimit && this.fileLimit <= this.files.length;\n        } else {\n            return this.fileLimit && this.fileLimit <= this.files.length + this.uploadedFileCount;\n        }\n    }\n\n    checkFileLimit(files: File[]) {\n        this.msgs ??= [];\n        const hasExistingValidationMessages = this.msgs.length > 0 && this.fileLimit < files.length;\n        if (this.isFileLimitExceeded() || hasExistingValidationMessages) {\n            this.msgs.push({\n                severity: 'error',\n                summary: this.invalidFileLimitMessageSummary.replace('{0}', (this.fileLimit as number).toString()),\n                detail: this.invalidFileLimitMessageDetail.replace('{0}', (this.fileLimit as number).toString())\n            });\n        }\n    }\n\n    clearInputElement() {\n        if (this.advancedFileInput && this.advancedFileInput.nativeElement) {\n            this.advancedFileInput.nativeElement.value = '';\n        }\n\n        if (this.basicFileInput && this.basicFileInput.nativeElement) {\n            this.basicFileInput.nativeElement.value = '';\n        }\n    }\n\n    clearIEInput() {\n        if (this.advancedFileInput && this.advancedFileInput.nativeElement) {\n            this.duplicateIEEvent = true; //IE11 fix to prevent onFileChange trigger again\n            this.advancedFileInput.nativeElement.value = '';\n        }\n    }\n\n    hasFiles(): boolean {\n        return this.files && this.files.length > 0;\n    }\n\n    hasUploadedFiles() {\n        return this.uploadedFiles && this.uploadedFiles.length > 0;\n    }\n\n    onDragEnter(e: DragEvent) {\n        if (!this.disabled) {\n            e.stopPropagation();\n            e.preventDefault();\n        }\n    }\n\n    onDragOver(e: DragEvent) {\n        if (!this.disabled) {\n            DomHandler.addClass(this.content?.nativeElement, 'p-fileupload-highlight');\n            this.dragHighlight = true;\n            e.stopPropagation();\n            e.preventDefault();\n        }\n    }\n\n    onDragLeave(event: DragEvent) {\n        if (!this.disabled) {\n            DomHandler.removeClass(this.content?.nativeElement, 'p-fileupload-highlight');\n        }\n    }\n\n    onDrop(event: any) {\n        if (!this.disabled) {\n            DomHandler.removeClass(this.content?.nativeElement, 'p-fileupload-highlight');\n            event.stopPropagation();\n            event.preventDefault();\n\n            let files = event.dataTransfer ? event.dataTransfer.files : event.target.files;\n            let allowDrop = this.multiple || (files && files.length === 1);\n\n            if (allowDrop) {\n                this.onFileSelect(event);\n            }\n        }\n    }\n\n    onFocus() {\n        this.focus = true;\n    }\n\n    onBlur() {\n        this.focus = false;\n    }\n\n    formatSize(bytes: number) {\n        const k = 1024;\n        const dm = 3;\n        const sizes = this.getTranslation(TranslationKeys.FILE_SIZE_TYPES);\n\n        if (bytes === 0) {\n            return `0 ${sizes[0]}`;\n        }\n\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        const formattedSize = (bytes / Math.pow(k, i)).toFixed(dm);\n\n        return `${formattedSize} ${sizes[i]}`;\n    }\n\n    onBasicUploaderClick() {\n        if (this.hasFiles()) this.upload();\n        else this.basicFileInput?.nativeElement.click();\n    }\n\n    onBasicKeydown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'Space':\n            case 'Enter':\n                this.onBasicUploaderClick();\n\n                event.preventDefault();\n                break;\n        }\n    }\n\n    imageError(event: Event) {\n        this.onImageError.emit(event);\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    get chooseButtonLabel(): string {\n        return this.chooseLabel || this.config.getTranslation(TranslationKeys.CHOOSE);\n    }\n\n    get uploadButtonLabel(): string {\n        return this.uploadLabel || this.config.getTranslation(TranslationKeys.UPLOAD);\n    }\n\n    get cancelButtonLabel(): string {\n        return this.cancelLabel || this.config.getTranslation(TranslationKeys.CANCEL);\n    }\n\n    get browseFilesLabel(): string {\n        return this.config.getTranslation(TranslationKeys.ARIA)[TranslationKeys.BROWSE_FILES];\n    }\n\n    ngOnDestroy() {\n        if (this.content && this.content.nativeElement) {\n            if (this.dragOverListener) {\n                this.dragOverListener();\n                this.dragOverListener = null;\n            }\n        }\n\n        if (this.translationSubscription) {\n            this.translationSubscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, ButtonModule, ProgressBarModule, MessagesModule, RippleModule, PlusIcon, UploadIcon, TimesIcon],\n    exports: [FileUpload, SharedModule, ButtonModule, ProgressBarModule, MessagesModule],\n    declarations: [FileUpload]\n})\nexport class FileUploadModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAyCA;;;AAGG;MA2KU,UAAU,CAAA;AAyTW,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACrB,IAAA,QAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACD,IAAA,SAAA,CAAA;AACA,IAAA,IAAA,CAAA;AACC,IAAA,IAAA,CAAA;AACD,IAAA,EAAA,CAAA;AACA,IAAA,MAAA,CAAA;AAhUX;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACM,IAAA,GAAG,CAAqB;AACjC;;;AAGG;IACM,MAAM,GAA+B,MAAM,CAAC;AACrD;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,IAAI,CAAsB;AAClE;;;AAGG;AACqC,IAAA,eAAe,CAAsB;AAC7E;;;AAGG;AACoC,IAAA,WAAW,CAAqB;AACvE;;;AAGG;IACM,6BAA6B,GAAW,0BAA0B,CAAC;AAC5E;;;AAGG;IACM,4BAA4B,GAAW,6BAA6B,CAAC;AAC9E;;;AAGG;IACM,6BAA6B,GAAW,0BAA0B,CAAC;AAC5E;;;AAGG;IACM,4BAA4B,GAAW,0BAA0B,CAAC;AAC3E;;;AAGG;IACM,6BAA6B,GAAW,uBAAuB,CAAC;AACzE;;;AAGG;IACM,8BAA8B,GAAW,oCAAoC,CAAC;AACvF;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACoC,YAAY,GAAW,EAAE,CAAC;AACjE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACqC,gBAAgB,GAAY,IAAI,CAAC;AACzE;;;AAGG;IACqC,gBAAgB,GAAY,IAAI,CAAC;AACzE;;;AAGG;IACM,IAAI,GAAqC,UAAU,CAAC;AAC7D;;;AAGG;AACM,IAAA,OAAO,CAA0B;AAC1C;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;AACqE,IAAA,SAAS,CAAqB;AACtG;;;AAGG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;;AAIG;AACO,IAAA,cAAc,GAAwC,IAAI,YAAY,EAAyB,CAAC;AAC1G;;;;AAIG;AACO,IAAA,MAAM,GAAgC,IAAI,YAAY,EAAiB,CAAC;AAClF;;;;AAIG;AACO,IAAA,QAAQ,GAAkC,IAAI,YAAY,EAAmB,CAAC;AACxF;;;;AAIG;AACO,IAAA,OAAO,GAAuC,IAAI,YAAY,EAAwB,CAAC;AACjG;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,QAAQ,GAAkC,IAAI,YAAY,EAAmB,CAAC;AACxF;;;;AAIG;AACO,IAAA,QAAQ,GAAkC,IAAI,YAAY,EAAmB,CAAC;AACxF;;;;AAIG;AACO,IAAA,UAAU,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAC9F;;;;AAIG;AACO,IAAA,aAAa,GAAyC,IAAI,YAAY,EAA0B,CAAC;AAC3G;;;;AAIG;AACO,IAAA,YAAY,GAAwB,IAAI,YAAY,EAAS,CAAC;AACxE;;;;AAIG;AACO,IAAA,oBAAoB,GAA0C,IAAI,YAAY,EAA2B,CAAC;AAEpF,IAAA,SAAS,CAAuC;AAEhD,IAAA,iBAAiB,CAA+B;AAEnD,IAAA,cAAc,CAAyB;AAE9C,IAAA,OAAO,CAAyB;IAEtD,IAAa,KAAK,CAAC,KAAK,EAAA;AACpB,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AAEjB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,YAAA,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAEpB,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACrB,gBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACd,IAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvG,iBAAA;gBAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AAED,IAAA,IAAW,gBAAgB,GAAA;QACvB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YAC/B,OAAO,IAAI,CAAC,WAAqB,CAAC;AACrC,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;KACjD;IAEM,MAAM,GAAW,EAAE,CAAC;IAEpB,QAAQ,GAAW,CAAC,CAAC;AAErB,IAAA,aAAa,CAAsB;AAEnC,IAAA,IAAI,CAAwB;AAE5B,IAAA,YAAY,CAA+B;AAE3C,IAAA,cAAc,CAA+B;AAE7C,IAAA,eAAe,CAA+B;AAE9C,IAAA,eAAe,CAA+B;AAErD,IAAA,kBAAkB,CAA+B;AAEjD,IAAA,kBAAkB,CAA+B;AAEjD,IAAA,kBAAkB,CAA+B;AAEjD,IAAA,aAAa,CAA+B;IAErC,iBAAiB,GAAW,CAAC,CAAC;AAErC,IAAA,KAAK,CAAsB;AAE3B,IAAA,SAAS,CAAsB;IAE/B,gBAAgB,CAAsB;AAEtC,IAAA,uBAAuB,CAA2B;AAElD,IAAA,gBAAgB,CAAe;IAExB,aAAa,GAAG,EAAE,CAAC;AAE1B,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACpC,QAAmB,EACnB,EAAc,EACf,SAAuB,EACvB,IAAY,EACX,IAAgB,EACjB,EAAqB,EACrB,MAAqB,EAAA;QARF,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACpC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAc;QACvB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACX,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAY;QACjB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACrB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAC5B;IAEJ,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAK;AAC1E,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;AAC1B,gBAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;oBAC7B,IAAI,IAAI,CAAC,OAAO,EAAE;wBACd,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACpH,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,MAAc,EAAA;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;KAC7C;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;KACjD;AAED,IAAA,YAAY,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACjE,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACnB,SAAA;QAED,IAAI,KAAK,GAAG,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AAC/E,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,YAAA,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAEpB,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;AAC5B,gBAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACrB,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChG,qBAAA;oBAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;;AAGrF,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE3B,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,EAAE;YAC3F,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB,SAAA;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACxC,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,IAAU,EAAA;AACrB,QAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;YAC1B,IAAI,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;AAC5E,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,MAAM,GAAA;AACF,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,OAAO,CAAC,CAAE,IAAI,CAAC,QAAQ,CAAC,WAAmB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAE,IAAI,CAAC,QAAgB,CAAC,cAAc,CAAC,CAAC;AACnH,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,IAAU,EAAA;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;AAC5C,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACX,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,OAAO,EAAE,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;AACrE,gBAAA,MAAM,EAAE,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC;AACxE,aAAA,CAAC,CAAC;AACH,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE;AAClD,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACX,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,OAAO,EAAE,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;AACrE,gBAAA,MAAM,EAAE,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9F,aAAA,CAAC,CAAC;AACH,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAEO,IAAA,eAAe,CAAC,IAAU,EAAA;QAC9B,IAAI,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACzE,QAAA,KAAK,IAAI,IAAI,IAAI,eAAgB,EAAE;YAC/B,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;AAE1L,YAAA,IAAI,UAAU,EAAE;AACZ,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,YAAY,CAAC,QAAgB,EAAA;AACzB,QAAA,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;KACvD;AAED,IAAA,UAAU,CAAC,QAAgB,EAAA;QACvB,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;KACvC;AAED,IAAA,gBAAgB,CAAC,IAAU,EAAA;AACvB,QAAA,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;KAC3C;AAED,IAAA,OAAO,CAAC,IAAU,EAAA;QACd,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACrC;AAED,IAAA,WAAW,CAAC,GAAQ,EAAA;QAChB,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KACvC;AACD;;;AAGG;IACH,MAAM,GAAA;QACF,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC/C,aAAA;AAED,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,aAAA,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACf,YAAA,IAAI,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;AAE9B,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;AACrB,gBAAA,QAAQ,EAAE,QAAQ;AACrB,aAAA,CAAC,CAAC;AAEH,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAClE,aAAA;AAED,YAAA,IAAI,CAAC,IAAI;iBACJ,OAAO,CAAS,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAa,EAAE;AAC9C,gBAAA,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,gBAAA,cAAc,EAAE,IAAI;AACpB,gBAAA,OAAO,EAAE,QAAQ;gBACjB,eAAe,EAAE,IAAI,CAAC,eAAe;aACxC,CAAC;AACD,iBAAA,SAAS,CACN,CAAC,KAAqB,KAAI;gBACtB,QAAQ,KAAK,CAAC,IAAI;oBACd,KAAK,aAAa,CAAC,IAAI;AACnB,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACb,4BAAA,aAAa,EAAE,KAAK;AACpB,4BAAA,QAAQ,EAAE,QAAQ;AACrB,yBAAA,CAAC,CAAC;wBACH,MAAM;oBACV,KAAK,aAAa,CAAC,QAAQ;AACvB,wBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,wBAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AAElB,wBAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,EAAE;4BACjD,IAAI,IAAI,CAAC,SAAS,EAAE;gCAChB,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC/C,6BAAA;AAED,4BAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AACnE,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AAC5C,yBAAA;wBACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;wBACvC,IAAI,CAAC,KAAK,EAAE,CAAC;wBACb,MAAM;AACV,oBAAA,KAAK,aAAa,CAAC,cAAc,EAAE;AAC/B,wBAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE;4BACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,CAAE,CAAC,CAAC;AACzE,yBAAA;AAED,wBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;wBACxE,MAAM;AACT,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,aAAC,EACD,CAAC,KAAiB,KAAI;AAClB,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAC3D,aAAC,CACJ,CAAC;AACT,SAAA;KACJ;AACD;;;AAGG;IACH,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AAChB,QAAA,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AACD;;;;;AAKG;IACH,MAAM,CAAC,KAAY,EAAE,KAAa,EAAA;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACtE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnC;AACD;;;;AAIG;AACH,IAAA,kBAAkB,CAAC,KAAK,EAAA;AACpB,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KACpF;IAED,mBAAmB,GAAA;AACf,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,MAAM,cAAc,GAAG,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAEnG,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,cAAc,IAAI,IAAI,CAAC,KAAK,EAAE;AAClE,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,SAAA;QAED,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC;KAC5D;IAED,gBAAgB,GAAA;QACZ,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,YAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAChE,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACzF,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC;AACjB,QAAA,MAAM,6BAA6B,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5F,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE,IAAI,6BAA6B,EAAE;AAC7D,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACX,gBAAA,QAAQ,EAAE,OAAO;AACjB,gBAAA,OAAO,EAAE,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,KAAK,EAAG,IAAI,CAAC,SAAoB,CAAC,QAAQ,EAAE,CAAC;AAClG,gBAAA,MAAM,EAAE,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,KAAK,EAAG,IAAI,CAAC,SAAoB,CAAC,QAAQ,EAAE,CAAC;AACnG,aAAA,CAAC,CAAC;AACN,SAAA;KACJ;IAED,iBAAiB,GAAA;QACb,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE;YAChE,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AACnD,SAAA;QAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;YAC1D,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AAChD,SAAA;KACJ;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE;AAChE,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AACnD,SAAA;KACJ;IAED,QAAQ,GAAA;QACJ,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KAC9C;IAED,gBAAgB,GAAA;QACZ,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;KAC9D;AAED,IAAA,WAAW,CAAC,CAAY,EAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,CAAY,EAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,wBAAwB,CAAC,CAAC;AAC3E,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,wBAAwB,CAAC,CAAC;AACjF,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAU,EAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,wBAAwB,CAAC,CAAC;YAC9E,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,IAAI,KAAK,GAAG,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AAC/E,YAAA,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AAE/D,YAAA,IAAI,SAAS,EAAE;AACX,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5B,aAAA;AACJ,SAAA;KACJ;IAED,OAAO,GAAA;AACH,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;KACrB;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;AAED,IAAA,UAAU,CAAC,KAAa,EAAA;QACpB,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,EAAE,GAAG,CAAC,CAAC;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAEnE,IAAI,KAAK,KAAK,CAAC,EAAE;AACb,YAAA,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1B,SAAA;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,QAAA,MAAM,aAAa,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QAE3D,OAAO,CAAA,EAAG,aAAa,CAAI,CAAA,EAAA,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;KACzC;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,QAAQ,EAAE;YAAE,IAAI,CAAC,MAAM,EAAE,CAAC;;AAC9B,YAAA,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;KACnD;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;QAC/B,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;gBACR,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAE5B,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACb,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAY,EAAA;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACjC;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KACjF;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KACjF;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KACjF;AAED,IAAA,IAAI,gBAAgB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;KACzF;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC5C,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAChC,aAAA;AACJ,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAC9B,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;AAC9C,SAAA;KACJ;uGAlxBQ,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAyTP,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA1Td,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EAoBC,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,GAAA,EAAA,KAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAUhB,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,0BAKhB,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAKhB,gBAAgB,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAKhB,eAAe,CAAA,EAAA,6BAAA,EAAA,+BAAA,EAAA,4BAAA,EAAA,8BAAA,EAAA,6BAAA,EAAA,+BAAA,EAAA,4BAAA,EAAA,8BAAA,EAAA,6BAAA,EAAA,+BAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EA6Cf,eAAe,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAmCf,gBAAgB,CAKhB,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAAA,gBAAgB,CAehB,EAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAKhB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAwFnD,aAAa,EAtZpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,ojBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,OAAA,EAAA,UAAA,EAAA,SAAA,EAAA,aAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,UAAA,EAAA,UAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,WAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,WAAA,EAAA,YAAA,EAAA,OAAA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,UAAA,EAAA,OAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,QAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,uBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,aAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA8xBoG,QAAQ,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,UAAU,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAtxB3H,UAAU,EAAA,UAAA,EAAA,CAAA;kBA1KtB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACd,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,ojBAAA,CAAA,EAAA,CAAA;;0BA2TI,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;+NArTd,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,6BAA6B,EAAA,CAAA;sBAArC,KAAK;gBAKG,4BAA4B,EAAA,CAAA;sBAApC,KAAK;gBAKG,6BAA6B,EAAA,CAAA;sBAArC,KAAK;gBAKG,4BAA4B,EAAA,CAAA;sBAApC,KAAK;gBAKG,6BAA6B,EAAA,CAAA;sBAArC,KAAK;gBAKG,8BAA8B,EAAA,CAAA;sBAAtC,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKiC,YAAY,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKkC,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKkC,SAAS,EAAA,CAAA;sBAAhF,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,SAAS,EAAE,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAA;gBAK7D,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAMI,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,oBAAoB,EAAA,CAAA;sBAA7B,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEE,iBAAiB,EAAA,CAAA;sBAAhD,SAAS;uBAAC,mBAAmB,CAAA;gBAED,cAAc,EAAA,CAAA;sBAA1C,SAAS;uBAAC,gBAAgB,CAAA;gBAEL,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS,CAAA;gBAEP,KAAK,EAAA,CAAA;sBAAjB,KAAK;;MAoiBG,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EA1xBhB,YAAA,EAAA,CAAA,UAAU,CAsxBT,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAtxB3H,EAAA,OAAA,EAAA,CAAA,UAAU,EAuxBG,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;wGAG1E,gBAAgB,EAAA,OAAA,EAAA,CAJf,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAC9G,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;;2FAG1E,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;oBACrI,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,CAAC;oBACpF,YAAY,EAAE,CAAC,UAAU,CAAC;AAC7B,iBAAA,CAAA;;;ACh/BD;;AAEG;;;;"}