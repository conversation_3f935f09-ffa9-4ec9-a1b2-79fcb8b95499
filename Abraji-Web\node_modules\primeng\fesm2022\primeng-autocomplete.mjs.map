{"version": 3, "file": "primeng-autocomplete.mjs", "sources": ["../../src/app/components/autocomplete/autocomplete.ts", "../../src/app/components/autocomplete/primeng-autocomplete.ts"], "sourcesContent": ["import { animate, AnimationEvent, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewChecked,\n    booleanAttribute,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    computed,\n    ContentChildren,\n    effect,\n    ElementRef,\n    EventEmitter,\n    forwardRef,\n    Inject,\n    Input,\n    NgModule,\n    NgZone,\n    numberAttribute,\n    OnDestroy,\n    Output,\n    QueryList,\n    Renderer2,\n    signal,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { OverlayOptions, OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule, TranslationKeys } from 'primeng/api';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { ButtonModule } from 'primeng/button';\nimport { ConnectedOverlayScroll<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { Overlay, OverlayModule } from 'primeng/overlay';\nimport { RippleModule } from 'primeng/ripple';\nimport { Scroller, ScrollerModule } from 'primeng/scroller';\nimport { ScrollerOptions } from 'primeng/api';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { AutoCompleteCompleteEvent, AutoCompleteDropdownClickEvent, AutoCompleteLazyLoadEvent, AutoCompleteSelectEvent, AutoCompleteUnselectEvent } from './autocomplete.interface';\n\nexport const AUTOCOMPLETE_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => AutoComplete),\n    multi: true\n};\n/**\n * AutoComplete is an input component that provides real-time suggestions when being typed.\n * @group Components\n */\n@Component({\n    selector: 'p-autoComplete',\n    template: `\n        <div #container [ngClass]=\"containerClass\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onContainerClick($event)\">\n            <input\n                *ngIf=\"!multiple\"\n                #focusInput\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n                [ngClass]=\"inputClass\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [type]=\"type\"\n                [attr.value]=\"inputValue()\"\n                [attr.id]=\"inputId\"\n                [autocomplete]=\"autocomplete\"\n                [required]=\"required\"\n                [name]=\"name\"\n                aria-autocomplete=\"list\"\n                role=\"combobox\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.size]=\"size\"\n                [attr.maxlength]=\"maxlength\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [readonly]=\"readonly\"\n                [disabled]=\"disabled\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-required]=\"required\"\n                [attr.aria-expanded]=\"overlayVisible ?? false\"\n                [attr.aria-controls]=\"overlayVisible ? id + '_list' : null\"\n                [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                (input)=\"onInput($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                (change)=\"onInputChange($event)\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                (paste)=\"onInputPaste($event)\"\n                (keyup)=\"onInputKeyUp($event)\"\n            />\n            <ng-container *ngIf=\"filled && !disabled && showClear && !loading\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [styleClass]=\"'p-autocomplete-clear-icon'\" (click)=\"clear()\" [attr.aria-hidden]=\"true\" />\n                <span *ngIf=\"clearIconTemplate\" class=\"p-autocomplete-clear-icon\" (click)=\"clear()\" [attr.aria-hidden]=\"true\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n\n            <ul\n                *ngIf=\"multiple\"\n                #multiContainer\n                [ngClass]=\"multiContainerClass\"\n                [tabindex]=\"-1\"\n                role=\"listbox\"\n                [attr.aria-orientation]=\"'horizontal'\"\n                [attr.aria-activedescendant]=\"focused ? focusedMultipleOptionId : undefined\"\n                (focus)=\"onMultipleContainerFocus($event)\"\n                (blur)=\"onMultipleContainerBlur($event)\"\n                (keydown)=\"onMultipleContainerKeyDown($event)\"\n            >\n                <li\n                    #token\n                    *ngFor=\"let option of modelValue(); let i = index\"\n                    [ngClass]=\"{ 'p-autocomplete-token': true, 'p-focus': focusedMultipleOptionIndex() === i }\"\n                    [attr.id]=\"id + '_multiple_option_' + i\"\n                    role=\"option\"\n                    [attr.aria-label]=\"getOptionLabel(option)\"\n                    [attr.aria-setsize]=\"modelValue().length\"\n                    [attr.aria-posinset]=\"i + 1\"\n                    [attr.aria-selected]=\"true\"\n                >\n                    <ng-container *ngTemplateOutlet=\"selectedItemTemplate; context: { $implicit: option }\"></ng-container>\n                    <span *ngIf=\"!selectedItemTemplate\" class=\"p-autocomplete-token-label\">{{ getMultipleLabel(option) }}</span>\n                    <span class=\"p-autocomplete-token-icon\" (click)=\"!readonly ? removeOption($event, i) : ''\">\n                        <TimesCircleIcon [styleClass]=\"'p-autocomplete-token-icon'\" *ngIf=\"!removeIconTemplate\" [attr.aria-hidden]=\"true\" />\n                        <span *ngIf=\"removeIconTemplate\" class=\"p-autocomplete-token-icon\" [attr.aria-hidden]=\"true\">\n                            <ng-template *ngTemplateOutlet=\"removeIconTemplate\"></ng-template>\n                        </span>\n                    </span>\n                </li>\n                <li class=\"p-autocomplete-input-token\" role=\"option\">\n                    <input\n                        #focusInput\n                        pAutoFocus\n                        [autofocus]=\"autofocus\"\n                        [ngClass]=\"inputClass\"\n                        [ngStyle]=\"inputStyle\"\n                        [class]=\"inputStyleClass\"\n                        [attr.type]=\"type\"\n                        [attr.id]=\"inputId\"\n                        [autocomplete]=\"autocomplete\"\n                        [required]=\"required\"\n                        [attr.name]=\"name\"\n                        role=\"combobox\"\n                        [attr.placeholder]=\"!filled ? placeholder : null\"\n                        [attr.size]=\"size\"\n                        aria-autocomplete=\"list\"\n                        [attr.maxlength]=\"maxlength\"\n                        [tabindex]=\"!disabled ? tabindex : -1\"\n                        [readonly]=\"readonly\"\n                        [disabled]=\"disabled\"\n                        [attr.aria-label]=\"ariaLabel\"\n                        [attr.aria-labelledby]=\"ariaLabelledBy\"\n                        [attr.aria-required]=\"required\"\n                        [attr.aria-expanded]=\"overlayVisible ?? false\"\n                        [attr.aria-controls]=\"overlayVisible ? id + '_list' : null\"\n                        [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                        (input)=\"onInput($event)\"\n                        (keydown)=\"onKeyDown($event)\"\n                        (change)=\"onInputChange($event)\"\n                        (focus)=\"onInputFocus($event)\"\n                        (blur)=\"onInputBlur($event)\"\n                        (paste)=\"onInputPaste($event)\"\n                        (keyup)=\"onInputKeyUp($event)\"\n                    />\n                </li>\n            </ul>\n            <ng-container *ngIf=\"loading\">\n                <SpinnerIcon *ngIf=\"!loadingIconTemplate\" [styleClass]=\"'p-autocomplete-loader'\" [spin]=\"true\" [attr.aria-hidden]=\"true\" />\n                <span *ngIf=\"loadingIconTemplate\" class=\"p-autocomplete-loader pi-spin \" [attr.aria-hidden]=\"true\">\n                    <ng-template *ngTemplateOutlet=\"loadingIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n            <button #ddBtn type=\"button\" pButton [attr.aria-label]=\"dropdownAriaLabel\" class=\"p-autocomplete-dropdown p-button-icon-only\" [disabled]=\"disabled\" pRipple (click)=\"handleDropdownClick($event)\" *ngIf=\"dropdown\" [attr.tabindex]=\"tabindex\">\n                <span *ngIf=\"dropdownIcon\" [ngClass]=\"dropdownIcon\" [attr.aria-hidden]=\"true\"></span>\n                <ng-container *ngIf=\"!dropdownIcon\">\n                    <ChevronDownIcon *ngIf=\"!dropdownIconTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"dropdownIconTemplate\"></ng-template>\n                </ng-container>\n            </button>\n            <p-overlay\n                #overlay\n                [(visible)]=\"overlayVisible\"\n                [options]=\"overlayOptions\"\n                [target]=\"'@parent'\"\n                [appendTo]=\"appendTo\"\n                [showTransitionOptions]=\"showTransitionOptions\"\n                [hideTransitionOptions]=\"hideTransitionOptions\"\n                (onAnimationStart)=\"onOverlayAnimationStart($event)\"\n                (onHide)=\"hide()\"\n            >\n                <div [ngClass]=\"panelClass\" [ngStyle]=\"panelStyles\" [class]=\"panelStyleClass\">\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <p-scroller\n                        *ngIf=\"virtualScroll\"\n                        #scroller\n                        [items]=\"visibleOptions()\"\n                        [style]=\"{ height: scrollHeight }\"\n                        [itemSize]=\"virtualScrollItemSize || _itemSize\"\n                        [autoSize]=\"true\"\n                        [lazy]=\"lazy\"\n                        (onLazyLoad)=\"onLazyLoad.emit($event)\"\n                        [options]=\"virtualScrollOptions\"\n                    >\n                        <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: items, options: scrollerOptions }\"></ng-container>\n                        </ng-template>\n                        <ng-container *ngIf=\"loaderTemplate\">\n                            <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                                <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: scrollerOptions }\"></ng-container>\n                            </ng-template>\n                        </ng-container>\n                    </p-scroller>\n                    <ng-container *ngIf=\"!virtualScroll\">\n                        <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: visibleOptions(), options: {} }\"></ng-container>\n                    </ng-container>\n\n                    <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                        <ul #items class=\"p-autocomplete-items\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"listbox\" [attr.id]=\"id + '_list'\" [attr.aria-label]=\"listLabel\">\n                            <ng-template ngFor let-option [ngForOf]=\"items\" let-i=\"index\">\n                                <ng-container *ngIf=\"isOptionGroup(option)\">\n                                    <li [attr.id]=\"id + '_' + getOptionIndex(i, scrollerOptions)\" class=\"p-autocomplete-item-group\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                        <span *ngIf=\"!groupTemplate\">{{ getOptionGroupLabel(option.optionGroup) }}</span>\n                                        <ng-container *ngTemplateOutlet=\"groupTemplate; context: { $implicit: option.optionGroup }\"></ng-container>\n                                    </li>\n                                </ng-container>\n                                <ng-container *ngIf=\"!isOptionGroup(option)\">\n                                    <li\n                                        class=\"p-autocomplete-item\"\n                                        pRipple\n                                        [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\"\n                                        [ngClass]=\"{ 'p-highlight': isSelected(option), 'p-focus': focusedOptionIndex() === getOptionIndex(i, scrollerOptions), 'p-disabled': isOptionDisabled(option) }\"\n                                        [attr.id]=\"id + '_' + getOptionIndex(i, scrollerOptions)\"\n                                        role=\"option\"\n                                        [attr.aria-label]=\"getOptionLabel(option)\"\n                                        [attr.aria-selected]=\"isSelected(option)\"\n                                        [attr.aria-disabled]=\"isOptionDisabled(option)\"\n                                        [attr.data-p-focused]=\"focusedOptionIndex() === getOptionIndex(i, scrollerOptions)\"\n                                        [attr.aria-setsize]=\"ariaSetSize\"\n                                        [attr.aria-posinset]=\"getAriaPosInset(getOptionIndex(i, scrollerOptions))\"\n                                        (click)=\"onOptionSelect($event, option)\"\n                                        (mouseenter)=\"onOptionMouseEnter($event, getOptionIndex(i, scrollerOptions))\"\n                                    >\n                                        <span *ngIf=\"!itemTemplate\">{{ getOptionLabel(option) }}</span>\n                                        <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: option, index: scrollerOptions.getOptions ? scrollerOptions.getOptions(i) : i }\"></ng-container>\n                                    </li>\n                                </ng-container>\n                            </ng-template>\n                            <li *ngIf=\"!items || (items && items.length === 0 && showEmptyMessage)\" class=\"p-autocomplete-empty-message\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                    {{ searchResultMessageText }}\n                                </ng-container>\n                                <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                            </li>\n                        </ul>\n                    </ng-template>\n                    <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n                </div>\n                <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\">\n                    {{ selectedMessageText }}\n                </span>\n            </p-overlay>\n        </div>\n    `,\n    host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': '((focused && !disabled) || autofocus) || overlayVisible',\n        '[class.p-autocomplete-clearable]': 'showClear && !disabled'\n    },\n    providers: [AUTOCOMPLETE_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./autocomplete.css']\n})\nexport class AutoComplete implements AfterViewChecked, AfterContentInit, OnDestroy, ControlValueAccessor {\n    /**\n     * Minimum number of characters to initiate a search.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) minLength: number = 1;\n    /**\n     * Delay between keystrokes to wait before sending a query.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) delay: number = 300;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Inline style of the overlay panel element.\n     * @group Props\n     */\n    @Input() panelStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Style class of the overlay panel element.\n     * @group Props\n     */\n    @Input() panelStyleClass: string | undefined;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    @Input() inputStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    @Input() inputStyleClass: string | undefined;\n    /**\n     * Hint text for the input field.\n     * @group Props\n     */\n    @Input() placeholder: string | undefined;\n    /**\n     * When present, it specifies that the input cannot be typed.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean | undefined;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Maximum height of the suggestions panel.\n     * @group Props\n     */\n    @Input() scrollHeight: string = '200px';\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazy: boolean = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) virtualScroll: boolean | undefined;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) virtualScrollItemSize: number | undefined;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() virtualScrollOptions: ScrollerOptions | undefined;\n    /**\n     * Maximum number of character allows in the input field.\n     * @group Props\n     */\n    @Input({ transform: (value: unknown) => numberAttribute(value, null) }) maxlength: number | undefined;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * When present, it specifies that an input field must be filled out before submitting the form.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) required: boolean | undefined;\n    /**\n     * Size of the input field.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) size: number | undefined;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * When enabled, highlights the first item in the list by default.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoHighlight: boolean | undefined;\n    /**\n     * When present, autocomplete clears the manual input if it does not match of the suggestions to force only accepting values from the suggestions.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) forceSelection: boolean | undefined;\n    /**\n     * Type of the input, defaults to \"text\".\n     * @group Props\n     */\n    @Input() type: string = 'text';\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Defines a string that labels the dropdown button for accessibility.\n     * @group Props\n     */\n    @Input() dropdownAriaLabel: string | undefined;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Icon class of the dropdown icon.\n     * @group Props\n     */\n    @Input() dropdownIcon: string | undefined;\n    /**\n     * Ensures uniqueness of selected items on multiple mode.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) unique: boolean = true;\n    /**\n     * Whether to display options as grouped when nested options are provided.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) group: boolean | undefined;\n    /**\n     * Whether to run a query when input receives focus.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) completeOnFocus: boolean = false;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean = false;\n    /**\n     * Field of a suggested object to resolve and display.\n     * @group Props\n     * @deprecated use optionLabel property instead\n     */\n    @Input() field: string | undefined;\n    /**\n     * Displays a button next to the input field when enabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) dropdown: boolean | undefined;\n    /**\n     * Whether to show the empty message or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showEmptyMessage: boolean | undefined = true;\n    /**\n     * Specifies the behavior dropdown button. Default \"blank\" mode sends an empty string and \"current\" mode sends the input value.\n     * @group Props\n     */\n    @Input() dropdownMode: string = 'blank';\n    /**\n     * Specifies if multiple values can be selected.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) multiple: boolean | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    @Input() dataKey: string | undefined;\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    @Input() emptyMessage: string | undefined;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.1s linear';\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    @Input() autocomplete: string = 'off';\n    /**\n     * Name of the options field of an option group.\n     * @group Props\n     */\n    @Input() optionGroupChildren: string | undefined = 'items';\n    /**\n     * Name of the label field of an option group.\n     * @group Props\n     */\n    @Input() optionGroupLabel: string | undefined = 'label';\n    /**\n     * Options for the overlay element.\n     * @group Props\n     */\n    @Input() overlayOptions: OverlayOptions | undefined;\n    /**\n     * An array of suggestions to display.\n     * @group Props\n     */\n    @Input() get suggestions(): any[] {\n        return this._suggestions();\n    }\n    set suggestions(value: any[]) {\n        this._suggestions.set(value);\n        this.handleSuggestionsChange();\n    }\n    /**\n     * Element dimensions of option for virtual scrolling.\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    @Input() get itemSize(): number {\n        return this._itemSize as number;\n    }\n    set itemSize(val: number) {\n        this._itemSize = val;\n        console.warn('The itemSize property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    /**\n     * Property name or getter function to use as the label of an option.\n     * @group Props\n     */\n    @Input() optionLabel: string | ((item: any) => string) | undefined;\n    /**\n     * Property name or getter function to use as the value of an option.\n     * @group Props\n     */\n    @Input() optionValue: string | ((item: any) => string) | undefined;\n    /**\n     * Unique identifier of the component.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Text to display when the search is active. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} results are available'\n     */\n    @Input() searchMessage: string | undefined;\n    /**\n     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue 'No selected item'\n     */\n    @Input() emptySelectionMessage: string | undefined;\n    /**\n     * Text to be displayed in hidden accessible field when options are selected. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} items selected'\n     */\n    @Input() selectionMessage: string | undefined;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoOptionFocus: boolean | undefined = false;\n    /**\n     * When enabled, the focused option is selected.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) selectOnFocus: boolean | undefined;\n    /**\n     * Locale to use in searching. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) searchLocale: boolean | undefined;\n    /**\n     * Property name or getter function to use as the disabled flag of an option, defaults to false when not defined.\n     * @group Props\n     */\n    @Input() optionDisabled: string | undefined;\n    /**\n     * When enabled, the hovered option will be focused.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) focusOnHover: boolean | undefined;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Callback to invoke to search for suggestions.\n     * @param {AutoCompleteCompleteEvent} event - Custom complete event.\n     * @group Emits\n     */\n    @Output() completeMethod: EventEmitter<AutoCompleteCompleteEvent> = new EventEmitter<AutoCompleteCompleteEvent>();\n    /**\n     * Callback to invoke when a suggestion is selected.\n     * @param {AutoCompleteSelectEvent} event - custom select event.\n     * @group Emits\n     */\n    @Output() onSelect: EventEmitter<AutoCompleteSelectEvent> = new EventEmitter<AutoCompleteSelectEvent>();\n    /**\n     * Callback to invoke when a selected value is removed.\n     * @param {AutoCompleteUnselectEvent} event - custom unselect event.\n     * @group Emits\n     */\n    @Output() onUnselect: EventEmitter<AutoCompleteUnselectEvent> = new EventEmitter<AutoCompleteUnselectEvent>();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter();\n    /**\n     * Callback to invoke to when dropdown button is clicked.\n     * @param {AutoCompleteDropdownClickEvent} event - custom dropdown click event.\n     * @group Emits\n     */\n    @Output() onDropdownClick: EventEmitter<AutoCompleteDropdownClickEvent> = new EventEmitter<AutoCompleteDropdownClickEvent>();\n    /**\n     * Callback to invoke when clear button is clicked.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<Event | undefined> = new EventEmitter<Event | undefined>();\n    /**\n     * Callback to invoke on input key up.\n     * @param {KeyboardEvent} event - Keyboard event.\n     * @group Emits\n     */\n    @Output() onKeyUp: EventEmitter<KeyboardEvent> = new EventEmitter();\n    /**\n     * Callback to invoke on overlay is shown.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke on overlay is hidden.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke on lazy load data.\n     * @param {AutoCompleteLazyLoadEvent} event - Lazy load event.\n     * @group Emits\n     */\n    @Output() onLazyLoad: EventEmitter<AutoCompleteLazyLoadEvent> = new EventEmitter<AutoCompleteLazyLoadEvent>();\n\n    @ViewChild('container') containerEL: Nullable<ElementRef>;\n\n    @ViewChild('focusInput') inputEL: Nullable<ElementRef>;\n\n    @ViewChild('multiIn') multiInputEl: Nullable<ElementRef>;\n\n    @ViewChild('multiContainer') multiContainerEL: Nullable<ElementRef>;\n\n    @ViewChild('ddBtn') dropdownButton: Nullable<ElementRef>;\n\n    @ViewChild('items') itemsViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scroller') scroller: Nullable<Scroller>;\n\n    @ViewChild('overlay') overlayViewChild!: Overlay;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    _itemSize: Nullable<number>;\n\n    itemsWrapper: Nullable<HTMLDivElement>;\n\n    itemTemplate: Nullable<TemplateRef<any>>;\n\n    emptyTemplate: Nullable<TemplateRef<any>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    selectedItemTemplate: Nullable<TemplateRef<any>>;\n\n    groupTemplate: Nullable<TemplateRef<any>>;\n\n    loaderTemplate: Nullable<TemplateRef<any>>;\n\n    removeIconTemplate: Nullable<TemplateRef<any>>;\n\n    loadingIconTemplate: Nullable<TemplateRef<any>>;\n\n    clearIconTemplate: Nullable<TemplateRef<any>>;\n\n    dropdownIconTemplate: Nullable<TemplateRef<any>>;\n\n    value: string | any;\n\n    _suggestions = signal<any>(null);\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    timeout: Nullable<any>;\n\n    overlayVisible: boolean | undefined;\n\n    suggestionsUpdated: Nullable<boolean>;\n\n    highlightOption: any;\n\n    highlightOptionChanged: Nullable<boolean>;\n\n    focused: boolean = false;\n\n    _filled: boolean;\n\n    get filled() {\n        return this._filled;\n    }\n    set filled(value: any) {\n        this._filled = value;\n    }\n\n    loading: Nullable<boolean>;\n\n    scrollHandler: Nullable<ConnectedOverlayScrollHandler>;\n\n    listId: string | undefined;\n\n    searchTimeout: any;\n\n    dirty: boolean = false;\n\n    modelValue = signal<any>(null);\n\n    focusedMultipleOptionIndex = signal<number>(-1);\n\n    focusedOptionIndex = signal<number>(-1);\n\n    visibleOptions = computed(() => {\n        return this.group ? this.flatOptions(this._suggestions()) : this._suggestions() || [];\n    });\n\n    inputValue = computed(() => {\n        const modelValue = this.modelValue();\n        const selectedOption = this.getSelectedOption(modelValue);\n\n        if (modelValue) {\n            if (typeof modelValue === 'object' || this.optionValue) {\n                const label = this.getOptionLabel(selectedOption);\n\n                return label != null ? label : modelValue;\n            } else {\n                return modelValue;\n            }\n        } else {\n            return '';\n        }\n    });\n\n    get focusedMultipleOptionId() {\n        return this.focusedMultipleOptionIndex() !== -1 ? `${this.id}_multiple_option_${this.focusedMultipleOptionIndex()}` : null;\n    }\n\n    get focusedOptionId() {\n        return this.focusedOptionIndex() !== -1 ? `${this.id}_${this.focusedOptionIndex()}` : null;\n    }\n\n    get containerClass() {\n        return {\n            'p-autocomplete p-component p-inputwrapper': true,\n            'p-disabled': this.disabled,\n            'p-focus': this.focused,\n            'p-autocomplete-dd': this.dropdown,\n            'p-autocomplete-multiple': this.multiple,\n            'p-inputwrapper-focus': this.focused,\n            'p-overlay-open': this.overlayVisible\n        };\n    }\n\n    get multiContainerClass() {\n        return { 'p-autocomplete-multiple-container p-component p-inputtext': true, 'p-variant-filled': this.variant === 'filled' || this.config.inputStyle() === 'filled' };\n    }\n\n    get panelClass() {\n        return {\n            'p-autocomplete-panel p-component': true,\n            'p-input-filled': this.config.inputStyle() === 'filled',\n            'p-ripple-disabled': this.config.ripple === false\n        };\n    }\n\n    get panelStyles() {\n        return {\n            'max-height': this.virtualScroll ? 'auto' : this.scrollHeight,\n            ...this.panelStyle\n        };\n    }\n\n    get inputClass() {\n        return {\n            'p-autocomplete-input p-inputtext p-component': !this.multiple,\n            'p-autocomplete-dd-input': this.dropdown,\n            'p-variant-filled': this.variant === 'filled' || this.config.inputStyle() === 'filled'\n        };\n    }\n\n    get searchResultMessageText() {\n        return ObjectUtils.isNotEmpty(this.visibleOptions()) && this.overlayVisible ? this.searchMessageText.replaceAll('{0}', this.visibleOptions().length) : this.emptySearchMessageText;\n    }\n\n    get searchMessageText() {\n        return this.searchMessage || this.config.translation.searchMessage || '';\n    }\n\n    get emptySearchMessageText() {\n        return this.emptyMessage || this.config.translation.emptySearchMessage || '';\n    }\n\n    get selectionMessageText() {\n        return this.selectionMessage || this.config.translation.selectionMessage || '';\n    }\n\n    get emptySelectionMessageText() {\n        return this.emptySelectionMessage || this.config.translation.emptySelectionMessage || '';\n    }\n\n    get selectedMessageText() {\n        return this.hasSelectedOption() ? this.selectionMessageText.replaceAll('{0}', this.multiple ? this.modelValue().length : '1') : this.emptySelectionMessageText;\n    }\n\n    get ariaSetSize() {\n        return this.visibleOptions().filter((option) => !this.isOptionGroup(option)).length;\n    }\n\n    get listLabel(): string {\n        return this.config.getTranslation(TranslationKeys.ARIA)['listLabel'];\n    }\n\n    get virtualScrollerDisabled() {\n        return !this.virtualScroll;\n    }\n\n    get optionValueSelected() {\n        return typeof this.modelValue() === 'string' && this.optionValue;\n    }\n\n    constructor(@Inject(DOCUMENT) private document: Document, public el: ElementRef, public renderer: Renderer2, public cd: ChangeDetectorRef, public config: PrimeNGConfig, public overlayService: OverlayService, private zone: NgZone) {\n        effect(() => {\n            this.filled = ObjectUtils.isNotEmpty(this.modelValue());\n        });\n    }\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n        this.cd.detectChanges();\n    }\n\n    ngAfterViewChecked() {\n        //Use timeouts as since Angular 4.2, AfterViewChecked is broken and not called after panel is updated\n        if (this.suggestionsUpdated && this.overlayViewChild) {\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    if (this.overlayViewChild) {\n                        this.overlayViewChild.alignOverlay();\n                    }\n                }, 1);\n                this.suggestionsUpdated = false;\n            });\n        }\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'group':\n                    this.groupTemplate = item.template;\n                    break;\n\n                case 'selectedItem':\n                    this.selectedItemTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n\n                case 'removetokenicon':\n                    this.removeIconTemplate = item.template;\n                    break;\n\n                case 'loadingicon':\n                    this.loadingIconTemplate = item.template;\n                    break;\n\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n\n                case 'dropdownicon':\n                    this.dropdownIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    handleSuggestionsChange() {\n        if (this.loading) {\n            this._suggestions().length > 0 || this.showEmptyMessage ? this.show() : !!this.emptyTemplate ? this.show() : this.hide();\n            const focusedOptionIndex = this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n            this.focusedOptionIndex.set(focusedOptionIndex);\n            this.suggestionsUpdated = true;\n            this.loading = false;\n            this.cd.markForCheck();\n        }\n    }\n\n    flatOptions(options) {\n        return (options || []).reduce((result, option, index) => {\n            result.push({ optionGroup: option, group: true, index });\n\n            const optionGroupChildren = this.getOptionGroupChildren(option);\n\n            optionGroupChildren && optionGroupChildren.forEach((o) => result.push(o));\n\n            return result;\n        }, []);\n    }\n\n    isOptionGroup(option) {\n        return this.optionGroupLabel && option.optionGroup && option.group;\n    }\n\n    findFirstOptionIndex() {\n        return this.visibleOptions().findIndex((option) => this.isValidOption(option));\n    }\n\n    findLastOptionIndex() {\n        return ObjectUtils.findLastIndex(this.visibleOptions(), (option) => this.isValidOption(option));\n    }\n\n    findFirstFocusedOptionIndex() {\n        const selectedIndex = this.findSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n\n    findLastFocusedOptionIndex() {\n        const selectedIndex = this.findSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n\n    findSelectedOptionIndex() {\n        return this.hasSelectedOption() ? this.visibleOptions().findIndex((option) => this.isValidSelectedOption(option)) : -1;\n    }\n\n    findNextOptionIndex(index) {\n        const matchedOptionIndex =\n            index < this.visibleOptions().length - 1\n                ? this.visibleOptions()\n                      .slice(index + 1)\n                      .findIndex((option) => this.isValidOption(option))\n                : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n\n    findPrevOptionIndex(index) {\n        const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), (option) => this.isValidOption(option)) : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n\n    isValidSelectedOption(option) {\n        return this.isValidOption(option) && this.isSelected(option);\n    }\n\n    isValidOption(option) {\n        return option && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n    }\n\n    isOptionDisabled(option) {\n        return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : false;\n    }\n\n    isSelected(option) {\n        if (this.multiple) {\n            return this.unique ? this.modelValue()?.find((model) => ObjectUtils.equals(model, this.getOptionValue(option), this.equalityKey())) : false;\n        }\n        return ObjectUtils.equals(this.modelValue(), this.getOptionValue(option), this.equalityKey());\n    }\n\n    isOptionMatched(option, value) {\n        return this.isValidOption(option) && this.getOptionLabel(option).toLocaleLowerCase(this.searchLocale) === value.toLocaleLowerCase(this.searchLocale);\n    }\n\n    isInputClicked(event) {\n        return event.target === this.inputEL.nativeElement;\n    }\n    isDropdownClicked(event) {\n        return this.dropdownButton?.nativeElement ? event.target === this.dropdownButton.nativeElement || this.dropdownButton.nativeElement.contains(event.target) : false;\n    }\n    equalityKey() {\n        return this.dataKey; // TODO: The 'optionValue' properties can be added.\n    }\n\n    onContainerClick(event) {\n        if (this.disabled || this.loading || this.isInputClicked(event) || this.isDropdownClicked(event)) {\n            return;\n        }\n\n        if (!this.overlayViewChild || !this.overlayViewChild.overlayViewChild?.nativeElement.contains(event.target)) {\n            DomHandler.focus(this.inputEL.nativeElement);\n        }\n    }\n\n    handleDropdownClick(event) {\n        let query = undefined;\n\n        if (this.overlayVisible) {\n            this.hide(true);\n        } else {\n            DomHandler.focus(this.inputEL.nativeElement);\n            query = this.inputEL.nativeElement.value;\n\n            if (this.dropdownMode === 'blank') this.search(event, '', 'dropdown');\n            else if (this.dropdownMode === 'current') this.search(event, query, 'dropdown');\n        }\n\n        this.onDropdownClick.emit({ originalEvent: event, query });\n    }\n\n    onInput(event) {\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        let query = event.target.value;\n        if (this.maxlength !== null) {\n            query = query.split('').slice(0, this.maxlength).join('');\n        }\n\n        if (!this.multiple && !this.forceSelection) {\n            this.updateModel(query);\n        }\n\n        if (query.length === 0 && !this.multiple) {\n            this.onClear.emit();\n\n            setTimeout(() => {\n                this.hide();\n            }, this.delay / 2);\n        } else {\n            if (query.length >= this.minLength) {\n                this.focusedOptionIndex.set(-1);\n\n                this.searchTimeout = setTimeout(() => {\n                    this.search(event, query, 'input');\n                }, this.delay);\n            } else {\n                this.hide();\n            }\n        }\n    }\n\n    onInputChange(event) {\n        if (this.forceSelection) {\n            let valid = false;\n\n            if (this.visibleOptions()) {\n                const matchedValue = this.visibleOptions().find((option) => this.isOptionMatched(option, this.inputEL.nativeElement.value || ''));\n\n                if (matchedValue !== undefined) {\n                    valid = true;\n                    !this.isSelected(matchedValue) && this.onOptionSelect(event, matchedValue);\n                }\n            }\n\n            if (!valid) {\n                this.inputEL.nativeElement.value = '';\n                !this.multiple && this.updateModel(null);\n            }\n        }\n    }\n\n    onInputFocus(event) {\n        if (this.disabled) {\n            // For ScreenReaders\n            return;\n        }\n\n        if (!this.dirty && this.completeOnFocus) {\n            this.search(event, event.target.value, 'focus');\n        }\n        this.dirty = true;\n        this.focused = true;\n        const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n        this.focusedOptionIndex.set(focusedOptionIndex);\n        this.overlayVisible && this.scrollInView(this.focusedOptionIndex());\n        this.onFocus.emit(event);\n    }\n\n    onMultipleContainerFocus(event) {\n        if (this.disabled) {\n            // For ScreenReaders\n            return;\n        }\n\n        this.focused = true;\n    }\n\n    onMultipleContainerBlur(event) {\n        this.focusedMultipleOptionIndex.set(-1);\n        this.focused = false;\n    }\n\n    onMultipleContainerKeyDown(event) {\n        if (this.disabled) {\n            event.preventDefault();\n\n            return;\n        }\n\n        switch (event.code) {\n            case 'ArrowLeft':\n                this.onArrowLeftKeyOnMultiple(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKeyOnMultiple(event);\n                break;\n\n            case 'Backspace':\n                this.onBackspaceKeyOnMultiple(event);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onInputBlur(event) {\n        this.dirty = false;\n        this.focused = false;\n        this.focusedOptionIndex.set(-1);\n        this.onModelTouched();\n        this.onBlur.emit(event);\n    }\n\n    onInputPaste(event) {\n        this.onKeyDown(event);\n    }\n\n    onInputKeyUp(event) {\n        this.onKeyUp.emit(event);\n    }\n\n    onKeyDown(event) {\n        if (this.disabled) {\n            event.preventDefault();\n\n            return;\n        }\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'PageDown':\n                this.onPageDownKey(event);\n                break;\n\n            case 'PageUp':\n                this.onPageUpKey(event);\n                break;\n\n            case 'Enter':\n            case 'NumpadEnter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            case 'Backspace':\n                this.onBackspaceKey(event);\n                break;\n\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onArrowDownKey(event) {\n        if (!this.overlayVisible) {\n            return;\n        }\n\n        const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.findFirstFocusedOptionIndex();\n\n        this.changeFocusedOptionIndex(event, optionIndex);\n\n        event.preventDefault();\n        event.stopPropagation();\n    }\n\n    onArrowUpKey(event) {\n        if (!this.overlayVisible) {\n            return;\n        }\n\n        if (event.altKey) {\n            if (this.focusedOptionIndex() !== -1) {\n                this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n            }\n\n            this.overlayVisible && this.hide();\n            event.preventDefault();\n        } else {\n            const optionIndex = this.focusedOptionIndex() !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex()) : this.findLastFocusedOptionIndex();\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n\n    onArrowLeftKey(event) {\n        const target = event.currentTarget;\n        this.focusedOptionIndex.set(-1);\n        if (this.multiple) {\n            if (ObjectUtils.isEmpty(target.value) && this.hasSelectedOption()) {\n                DomHandler.focus(this.multiContainerEL.nativeElement);\n                this.focusedMultipleOptionIndex.set(this.modelValue().length);\n            } else {\n                event.stopPropagation(); // To prevent onArrowLeftKeyOnMultiple method\n            }\n        }\n    }\n\n    onArrowRightKey(event) {\n        this.focusedOptionIndex.set(-1);\n\n        this.multiple && event.stopPropagation(); // To prevent onArrowRightKeyOnMultiple method\n    }\n\n    onHomeKey(event) {\n        const { currentTarget } = event;\n        const len = currentTarget.value.length;\n\n        currentTarget.setSelectionRange(0, event.shiftKey ? len : 0);\n        this.focusedOptionIndex.set(-1);\n\n        event.preventDefault();\n    }\n\n    onEndKey(event) {\n        const { currentTarget } = event;\n        const len = currentTarget.value.length;\n\n        currentTarget.setSelectionRange(event.shiftKey ? 0 : len, len);\n        this.focusedOptionIndex.set(-1);\n\n        event.preventDefault();\n    }\n\n    onPageDownKey(event) {\n        this.scrollInView(this.visibleOptions().length - 1);\n        event.preventDefault();\n    }\n\n    onPageUpKey(event) {\n        this.scrollInView(0);\n        event.preventDefault();\n    }\n\n    onEnterKey(event) {\n        if (!this.overlayVisible) {\n            this.onArrowDownKey(event);\n        } else {\n            if (this.focusedOptionIndex() !== -1) {\n                this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n            }\n\n            this.hide();\n        }\n\n        event.preventDefault();\n    }\n\n    onEscapeKey(event) {\n        this.overlayVisible && this.hide(true);\n        event.preventDefault();\n    }\n\n    onTabKey(event) {\n        if (this.focusedOptionIndex() !== -1) {\n            this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n        }\n\n        this.overlayVisible && this.hide();\n    }\n\n    onBackspaceKey(event) {\n        if (this.multiple) {\n            if (ObjectUtils.isNotEmpty(this.modelValue()) && !this.inputEL.nativeElement.value) {\n                const removedValue = this.modelValue()[this.modelValue().length - 1];\n                const newValue = this.modelValue().slice(0, -1);\n                this.updateModel(newValue);\n                this.onUnselect.emit({ originalEvent: event, value: removedValue });\n            }\n\n            event.stopPropagation(); // To prevent onBackspaceKeyOnMultiple method\n        }\n\n        if (!this.multiple && this.showClear && this.findSelectedOptionIndex() != -1) {\n            this.clear();\n        }\n    }\n\n    onArrowLeftKeyOnMultiple(event) {\n        const optionIndex = this.focusedMultipleOptionIndex() < 1 ? 0 : this.focusedMultipleOptionIndex() - 1;\n        this.focusedMultipleOptionIndex.set(optionIndex);\n    }\n\n    onArrowRightKeyOnMultiple(event) {\n        let optionIndex = this.focusedMultipleOptionIndex();\n        optionIndex++;\n\n        this.focusedMultipleOptionIndex.set(optionIndex);\n        if (optionIndex > this.modelValue().length - 1) {\n            this.focusedMultipleOptionIndex.set(-1);\n            DomHandler.focus(this.inputEL.nativeElement);\n        }\n    }\n\n    onBackspaceKeyOnMultiple(event) {\n        if (this.focusedMultipleOptionIndex() !== -1) {\n            this.removeOption(event, this.focusedMultipleOptionIndex());\n        }\n    }\n\n    onOptionSelect(event, option, isHide = true) {\n        const value = this.getOptionValue(option);\n\n        if (this.multiple) {\n            this.inputEL.nativeElement.value = '';\n\n            if (!this.isSelected(option)) {\n                this.updateModel([...(this.modelValue() || []), value]);\n            }\n        } else {\n            this.updateModel(value);\n        }\n\n        this.onSelect.emit({ originalEvent: event, value: option });\n\n        isHide && this.hide(true);\n    }\n\n    onOptionMouseEnter(event, index) {\n        if (this.focusOnHover) {\n            this.changeFocusedOptionIndex(event, index);\n        }\n    }\n\n    search(event, query, source) {\n        //allow empty string but not undefined or null\n        if (query === undefined || query === null) {\n            return;\n        }\n\n        //do not search blank values on input change\n        if (source === 'input' && query.trim().length === 0) {\n            return;\n        }\n        this.loading = true;\n        this.completeMethod.emit({ originalEvent: event, query });\n    }\n\n    removeOption(event, index) {\n        event.stopPropagation();\n\n        const removedOption = this.modelValue()[index];\n        const value = this.modelValue()\n            .filter((_, i) => i !== index)\n            .map((option) => this.getOptionValue(option));\n\n        this.updateModel(value);\n        this.onUnselect.emit({ originalEvent: event, value: removedOption });\n        DomHandler.focus(this.inputEL.nativeElement);\n    }\n\n    updateModel(value) {\n        this.value = value;\n        this.modelValue.set(value);\n        this.onModelChange(value);\n        this.updateInputValue();\n        this.cd.markForCheck();\n    }\n\n    updateInputValue() {\n        if (this.inputEL && this.inputEL.nativeElement) {\n            if (!this.multiple) {\n                this.inputEL.nativeElement.value = this.inputValue();\n            } else {\n                this.inputEL.nativeElement.value = '';\n            }\n        }\n    }\n\n    autoUpdateModel() {\n        if ((this.selectOnFocus || this.autoHighlight) && this.autoOptionFocus && !this.hasSelectedOption()) {\n            const focusedOptionIndex = this.findFirstFocusedOptionIndex();\n            this.focusedOptionIndex.set(focusedOptionIndex);\n            this.onOptionSelect(null, this.visibleOptions()[this.focusedOptionIndex()], false);\n        }\n    }\n\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n        if (this.itemsViewChild && this.itemsViewChild.nativeElement) {\n            const element = DomHandler.findSingle(this.itemsViewChild.nativeElement, `li[id=\"${id}\"]`);\n            if (element) {\n                element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n            } else if (!this.virtualScrollerDisabled) {\n                setTimeout(() => {\n                    this.virtualScroll && this.scroller?.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex());\n                }, 0);\n            }\n        }\n    }\n\n    changeFocusedOptionIndex(event, index) {\n        if (this.focusedOptionIndex() !== index) {\n            this.focusedOptionIndex.set(index);\n            this.scrollInView();\n\n            if (this.selectOnFocus) {\n                this.onOptionSelect(event, this.visibleOptions()[index], false);\n            }\n        }\n    }\n\n    show(isFocus = false) {\n        this.dirty = true;\n        this.overlayVisible = true;\n        const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n        this.focusedOptionIndex.set(focusedOptionIndex);\n        isFocus && DomHandler.focus(this.inputEL.nativeElement);\n        if (isFocus) {\n            DomHandler.focus(this.inputEL.nativeElement);\n        }\n        this.onShow.emit();\n        this.cd.markForCheck();\n    }\n\n    hide(isFocus = false) {\n        const _hide = () => {\n            this.dirty = isFocus;\n            this.overlayVisible = false;\n            this.focusedOptionIndex.set(-1);\n            isFocus && DomHandler.focus(this.inputEL.nativeElement);\n            this.onHide.emit();\n            this.cd.markForCheck();\n        };\n\n        // Added to adjust the scroller's content position when the dropdown closes.\n        if (this.virtualScroll) {\n            this.scroller.onScrollChange(event);\n        }\n\n        setTimeout(() => {\n            _hide();\n        }, 0); // For ScreenReaders\n    }\n\n    clear() {\n        this.updateModel(null);\n        this.inputEL.nativeElement.value = '';\n        this.onClear.emit();\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.modelValue.set(value);\n        this.updateInputValue();\n        this.cd.markForCheck();\n    }\n\n    hasSelectedOption() {\n        return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n\n    getAriaPosInset(index) {\n        return (\n            (this.optionGroupLabel\n                ? index -\n                  this.visibleOptions()\n                      .slice(0, index)\n                      .filter((option) => this.isOptionGroup(option)).length\n                : index) + 1\n        );\n    }\n\n    getOptionLabel(option: any) {\n        return this.field || this.optionLabel ? ObjectUtils.resolveFieldData(option, this.field || this.optionLabel) : option && option.label != undefined ? option.label : option;\n    }\n\n    getOptionValue(option) {\n        return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : option;\n    }\n\n    getOptionIndex(index, scrollerOptions) {\n        return this.virtualScrollerDisabled ? index : scrollerOptions && scrollerOptions.getItemOptions(index)['index'];\n    }\n\n    getOptionGroupLabel(optionGroup: any) {\n        return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label != undefined ? optionGroup.label : optionGroup;\n    }\n\n    getOptionGroupChildren(optionGroup: any) {\n        return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n\n    getSelectedOption(modelValue: any) {\n        if (!this.optionValue) {\n            return modelValue;\n        }\n\n        return (this.suggestions || []).find((item: any) => ObjectUtils.resolveFieldData(item, this.optionValue) === modelValue);\n    }\n\n    getMultipleLabel(option: any) {\n        let selected = this.getSelectedOption(option);\n\n        return this.getOptionLabel(selected);\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    onOverlayAnimationStart(event: AnimationEvent) {\n        if (event.toState === 'visible') {\n            this.itemsWrapper = DomHandler.findSingle(this.overlayViewChild.overlayViewChild?.nativeElement, this.virtualScroll ? '.p-scroller' : '.p-autocomplete-panel');\n\n            if (this.virtualScroll) {\n                this.scroller?.setContentEl(this.itemsViewChild?.nativeElement);\n                this.scroller.viewInit();\n            }\n            if (this.visibleOptions() && this.visibleOptions().length) {\n                if (this.virtualScroll) {\n                    const selectedIndex = this.modelValue() ? this.focusedOptionIndex() : -1;\n\n                    if (selectedIndex !== -1) {\n                        this.scroller?.scrollToIndex(selectedIndex);\n                    }\n                } else {\n                    let selectedListItem = DomHandler.findSingle(this.itemsWrapper, '.p-autocomplete-item.p-highlight');\n\n                    if (selectedListItem) {\n                        selectedListItem.scrollIntoView({ block: 'nearest', inline: 'center' });\n                    }\n                }\n            }\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, OverlayModule, InputTextModule, ButtonModule, SharedModule, RippleModule, ScrollerModule, AutoFocusModule, TimesCircleIcon, SpinnerIcon, TimesIcon, ChevronDownIcon],\n    exports: [AutoComplete, OverlayModule, SharedModule, ScrollerModule, AutoFocusModule],\n    declarations: [AutoComplete]\n})\nexport class AutoCompleteModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA+Ca,MAAA,2BAA2B,GAAQ;AAC5C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,YAAY,CAAC;AAC3C,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MAiOU,YAAY,CAAA;AA0lBiB,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AAA8B,IAAA,cAAA,CAAA;AAAwC,IAAA,IAAA,CAAA;AAzlBxN;;;AAGG;IACoC,SAAS,GAAW,CAAC,CAAC;AAC7D;;;AAGG;IACoC,KAAK,GAAW,GAAG,CAAC;AAC3D;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACM,YAAY,GAAW,OAAO,CAAC;AACxC;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACoC,IAAA,qBAAqB,CAAqB;AACjF;;;AAGG;AACM,IAAA,oBAAoB,CAA8B;AAC3D;;;AAGG;AACqE,IAAA,SAAS,CAAqB;AACtG;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACoC,IAAA,IAAI,CAAqB;AAChE;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACqC,IAAA,cAAc,CAAsB;AAC5E;;;AAGG;IACM,IAAI,GAAW,MAAM,CAAC;AAC/B;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;IACqC,MAAM,GAAY,IAAI,CAAC;AAC/D;;;AAGG;AACqC,IAAA,KAAK,CAAsB;AACnE;;;AAGG;IACqC,eAAe,GAAY,KAAK,CAAC;AACzE;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;;AAIG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACqC,gBAAgB,GAAwB,IAAI,CAAC;AACrF;;;AAGG;IACM,YAAY,GAAW,OAAO,CAAC;AACxC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;IACM,qBAAqB,GAAW,iCAAiC,CAAC;AAC3E;;;AAGG;IACM,qBAAqB,GAAW,YAAY,CAAC;AACtD;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;IACM,YAAY,GAAW,KAAK,CAAC;AACtC;;;AAGG;IACM,mBAAmB,GAAuB,OAAO,CAAC;AAC3D;;;AAGG;IACM,gBAAgB,GAAuB,OAAO,CAAC;AACxD;;;AAGG;AACM,IAAA,cAAc,CAA6B;AACpD;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;KAC9B;IACD,IAAI,WAAW,CAAC,KAAY,EAAA;AACxB,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,uBAAuB,EAAE,CAAC;KAClC;AACD;;;;AAIG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAmB,CAAC;KACnC;IACD,IAAI,QAAQ,CAAC,GAAW,EAAA;AACpB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACrB,QAAA,OAAO,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;KACpG;AACD;;;AAGG;AACM,IAAA,WAAW,CAA+C;AACnE;;;AAGG;AACM,IAAA,WAAW,CAA+C;AACnE;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;;AAIG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;;AAIG;AACM,IAAA,qBAAqB,CAAqB;AACnD;;;;AAIG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;IACqC,eAAe,GAAwB,KAAK,CAAC;AACrF;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;;AAIG;AACO,IAAA,cAAc,GAA4C,IAAI,YAAY,EAA6B,CAAC;AAClH;;;;AAIG;AACO,IAAA,QAAQ,GAA0C,IAAI,YAAY,EAA2B,CAAC;AACxG;;;;AAIG;AACO,IAAA,UAAU,GAA4C,IAAI,YAAY,EAA6B,CAAC;AAC9G;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAE,CAAC;AAC5D;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAE,CAAC;AAC3D;;;;AAIG;AACO,IAAA,eAAe,GAAiD,IAAI,YAAY,EAAkC,CAAC;AAC7H;;;;AAIG;AACO,IAAA,OAAO,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAC3F;;;;AAIG;AACO,IAAA,OAAO,GAAgC,IAAI,YAAY,EAAE,CAAC;AACpE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,UAAU,GAA4C,IAAI,YAAY,EAA6B,CAAC;AAEtF,IAAA,WAAW,CAAuB;AAEjC,IAAA,OAAO,CAAuB;AAEjC,IAAA,YAAY,CAAuB;AAE5B,IAAA,gBAAgB,CAAuB;AAEhD,IAAA,cAAc,CAAuB;AAErC,IAAA,cAAc,CAAuB;AAElC,IAAA,QAAQ,CAAqB;AAE9B,IAAA,gBAAgB,CAAW;AAEjB,IAAA,SAAS,CAAqC;AAE9E,IAAA,SAAS,CAAmB;AAE5B,IAAA,YAAY,CAA2B;AAEvC,IAAA,YAAY,CAA6B;AAEzC,IAAA,aAAa,CAA6B;AAE1C,IAAA,cAAc,CAA6B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,aAAa,CAA6B;AAE1C,IAAA,cAAc,CAA6B;AAE3C,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,KAAK,CAAe;AAEpB,IAAA,YAAY,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAEjC,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,OAAO,CAAgB;AAEvB,IAAA,cAAc,CAAsB;AAEpC,IAAA,kBAAkB,CAAoB;AAEtC,IAAA,eAAe,CAAM;AAErB,IAAA,sBAAsB,CAAoB;IAE1C,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,OAAO,CAAU;AAEjB,IAAA,IAAI,MAAM,GAAA;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;IACD,IAAI,MAAM,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB;AAED,IAAA,OAAO,CAAoB;AAE3B,IAAA,aAAa,CAA0C;AAEvD,IAAA,MAAM,CAAqB;AAE3B,IAAA,aAAa,CAAM;IAEnB,KAAK,GAAY,KAAK,CAAC;AAEvB,IAAA,UAAU,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAE/B,IAAA,0BAA0B,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC,CAAC;AAEhD,IAAA,kBAAkB,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC,CAAC;AAExC,IAAA,cAAc,GAAG,QAAQ,CAAC,MAAK;QAC3B,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC;AAC1F,KAAC,CAAC,CAAC;AAEH,IAAA,UAAU,GAAG,QAAQ,CAAC,MAAK;AACvB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AAE1D,QAAA,IAAI,UAAU,EAAE;YACZ,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;gBAElD,OAAO,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,UAAU,CAAC;AAC7C,aAAA;AAAM,iBAAA;AACH,gBAAA,OAAO,UAAU,CAAC;AACrB,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;AACL,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,uBAAuB,GAAA;QACvB,OAAO,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,iBAAA,EAAoB,IAAI,CAAC,0BAA0B,EAAE,EAAE,GAAG,IAAI,CAAC;KAC9H;AAED,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,GAAG,IAAI,CAAC;KAC9F;AAED,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;AACH,YAAA,2CAA2C,EAAE,IAAI;YACjD,YAAY,EAAE,IAAI,CAAC,QAAQ;YAC3B,SAAS,EAAE,IAAI,CAAC,OAAO;YACvB,mBAAmB,EAAE,IAAI,CAAC,QAAQ;YAClC,yBAAyB,EAAE,IAAI,CAAC,QAAQ;YACxC,sBAAsB,EAAE,IAAI,CAAC,OAAO;YACpC,gBAAgB,EAAE,IAAI,CAAC,cAAc;SACxC,CAAC;KACL;AAED,IAAA,IAAI,mBAAmB,GAAA;QACnB,OAAO,EAAE,2DAA2D,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ,EAAE,CAAC;KACxK;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,kCAAkC,EAAE,IAAI;YACxC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ;AACvD,YAAA,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,KAAK;SACpD,CAAC;KACL;AAED,IAAA,IAAI,WAAW,GAAA;QACX,OAAO;AACH,YAAA,YAAY,EAAE,IAAI,CAAC,aAAa,GAAG,MAAM,GAAG,IAAI,CAAC,YAAY;YAC7D,GAAG,IAAI,CAAC,UAAU;SACrB,CAAC;KACL;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,8CAA8C,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC9D,yBAAyB,EAAE,IAAI,CAAC,QAAQ;AACxC,YAAA,kBAAkB,EAAE,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ;SACzF,CAAC;KACL;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;KACtL;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,IAAI,EAAE,CAAC;KAC5E;AAED,IAAA,IAAI,sBAAsB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC;KAChF;AAED,IAAA,IAAI,oBAAoB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,IAAI,EAAE,CAAC;KAClF;AAED,IAAA,IAAI,yBAAyB,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,qBAAqB,IAAI,EAAE,CAAC;KAC5F;AAED,IAAA,IAAI,mBAAmB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;KAClK;AAED,IAAA,IAAI,WAAW,GAAA;QACX,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;KACvF;AAED,IAAA,IAAI,SAAS,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC;KACxE;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;KAC9B;AAED,IAAA,IAAI,mBAAmB,GAAA;QACnB,OAAO,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC;KACpE;AAED,IAAA,WAAA,CAAsC,QAAkB,EAAS,EAAc,EAAS,QAAmB,EAAS,EAAqB,EAAS,MAAqB,EAAS,cAA8B,EAAU,IAAY,EAAA;QAA9L,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAS,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QAAU,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAChO,MAAM,CAAC,MAAK;AACR,YAAA,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;AAC5D,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,kBAAkB,GAAA;;AAEd,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAClD,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,UAAU,CAAC,MAAK;oBACZ,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACvB,wBAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;AACxC,qBAAA;iBACJ,EAAE,CAAC,CAAC,CAAC;AACN,gBAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACpC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,iBAAiB;AAClB,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,uBAAuB,GAAA;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACzH,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,CAAC;AACjH,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC/B,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,OAAO,EAAA;AACf,QAAA,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,KAAI;AACpD,YAAA,MAAM,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAEzD,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;AAEhE,YAAA,mBAAmB,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAE1E,YAAA,OAAO,MAAM,CAAC;SACjB,EAAE,EAAE,CAAC,CAAC;KACV;AAED,IAAA,aAAa,CAAC,MAAM,EAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,CAAC;KACtE;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KAClF;IAED,mBAAmB,GAAA;QACf,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KACnG;IAED,2BAA2B,GAAA;AACvB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAErD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,aAAa,CAAC;KAC1E;IAED,0BAA0B,GAAA;AACtB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAErD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,aAAa,CAAC;KACzE;IAED,uBAAuB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1H;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;QACrB,MAAM,kBAAkB,GACpB,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC;AACpC,cAAE,IAAI,CAAC,cAAc,EAAE;AAChB,iBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChB,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC;AAEb,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KAC3E;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAErJ,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,CAAC;KAC/D;AAED,IAAA,qBAAqB,CAAC,MAAM,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;KAChE;AAED,IAAA,aAAa,CAAC,MAAM,EAAA;AAChB,QAAA,OAAO,MAAM,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KACnF;AAED,IAAA,gBAAgB,CAAC,MAAM,EAAA;QACnB,OAAO,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC;KAClG;AAED,IAAA,UAAU,CAAC,MAAM,EAAA;QACb,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;AAC/I,SAAA;QACD,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KACjG;IAED,eAAe,CAAC,MAAM,EAAE,KAAK,EAAA;AACzB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KACxJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,OAAO,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;KACtD;AACD,IAAA,iBAAiB,CAAC,KAAK,EAAA;AACnB,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,aAAa,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;KACtK;IACD,WAAW,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;AAED,IAAA,gBAAgB,CAAC,KAAK,EAAA;QAClB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;YAC9F,OAAO;AACV,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACzG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAChD,SAAA;KACJ;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;QACrB,IAAI,KAAK,GAAG,SAAS,CAAC;QAEtB,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnB,SAAA;AAAM,aAAA;YACH,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC7C,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;AAEzC,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO;gBAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,UAAU,CAAC,CAAC;AACjE,iBAAA,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS;gBAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AACnF,SAAA;AAED,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;KAC9D;AAED,IAAA,OAAO,CAAC,KAAK,EAAA;QACT,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AAC/B,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YACzB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7D,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxC,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACtC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAEpB,UAAU,CAAC,MAAK;gBACZ,IAAI,CAAC,IAAI,EAAE,CAAC;AAChB,aAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACtB,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAEhC,gBAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;oBACjC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACvC,iBAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;QACf,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,KAAK,GAAG,KAAK,CAAC;AAElB,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;AACvB,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;gBAElI,IAAI,YAAY,KAAK,SAAS,EAAE;oBAC5B,KAAK,GAAG,IAAI,CAAC;AACb,oBAAA,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC9E,iBAAA;AACJ,aAAA;YAED,IAAI,CAAC,KAAK,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;gBACtC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC5C,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;QACd,IAAI,IAAI,CAAC,QAAQ,EAAE;;YAEf,OAAO;AACV,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE;AACrC,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACnD,SAAA;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,CAAC;AAChL,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AACpE,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,wBAAwB,CAAC,KAAK,EAAA;QAC1B,IAAI,IAAI,CAAC,QAAQ,EAAE;;YAEf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;AAED,IAAA,uBAAuB,CAAC,KAAK,EAAA;QACzB,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB;AAED,IAAA,0BAA0B,CAAC,KAAK,EAAA;QAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,OAAO;AACV,SAAA;QAED,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBACrC,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;gBACtC,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBACrC,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KACzB;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,OAAO;AACV,SAAA;QAED,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,UAAU;AACX,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;;gBAEb,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;AACV,SAAA;AAED,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAEhJ,QAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAElD,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;AACV,SAAA;QAED,IAAI,KAAK,CAAC,MAAM,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AAChF,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACnC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAE/I,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAElD,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,KAAK,CAAC,eAAe,EAAE,CAAC;AAC3B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;AAChB,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC;QACnC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC/D,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AACtD,gBAAA,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC;AACjE,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAK,CAAC,eAAe,EAAE,CAAC;AAC3B,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;KAC5C;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;AACX,QAAA,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAChC,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;AAEvC,QAAA,aAAa,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;AACV,QAAA,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAChC,QAAA,MAAM,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;AAEvC,QAAA,aAAa,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;AACf,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AAChF,aAAA;YAED,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;QACb,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;AACV,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AAChF,SAAA;AAED,QAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;KACtC;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE;AAChF,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrE,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChD,gBAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC3B,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;AACvE,aAAA;AAED,YAAA,KAAK,CAAC,eAAe,EAAE,CAAC;AAC3B,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,EAAE;YAC1E,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,SAAA;KACJ;AAED,IAAA,wBAAwB,CAAC,KAAK,EAAA;QAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;AACtG,QAAA,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;KACpD;AAED,IAAA,yBAAyB,CAAC,KAAK,EAAA;AAC3B,QAAA,IAAI,WAAW,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AACpD,QAAA,WAAW,EAAE,CAAC;AAEd,QAAA,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACjD,IAAI,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5C,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAChD,SAAA;KACJ;AAED,IAAA,wBAAwB,CAAC,KAAK,EAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC,EAAE;YAC1C,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC,CAAC;AAC/D,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI,EAAA;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AAEtC,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AAC1B,gBAAA,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AAC3D,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AAE5D,QAAA,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7B;IAED,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAA;QAC3B,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/C,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAA;;AAEvB,QAAA,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;YACvC,OAAO;AACV,SAAA;;AAGD,QAAA,IAAI,MAAM,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACjD,OAAO;AACV,SAAA;AACD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;KAC7D;IAED,YAAY,CAAC,KAAK,EAAE,KAAK,EAAA;QACrB,KAAK,CAAC,eAAe,EAAE,CAAC;QAExB,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC;AAC/C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE;aAC1B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;AAC7B,aAAA,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAElD,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;QACrE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;KAChD;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,gBAAgB,GAAA;QACZ,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AAC5C,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACxD,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AACzC,aAAA;AACJ,SAAA;KACJ;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACjG,YAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAC9D,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACtF,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;QACnB,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,eAAe,CAAC;QACvE,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;AAC1D,YAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAA,OAAA,EAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAC3F,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,aAAA;AAAM,iBAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACtC,UAAU,CAAC,MAAK;oBACZ,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;iBACxG,EAAE,CAAC,CAAC,CAAC;AACT,aAAA;AACJ,SAAA;KACJ;IAED,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;YAEpB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;AACnE,aAAA;AACJ,SAAA;KACJ;IAED,IAAI,CAAC,OAAO,GAAG,KAAK,EAAA;AAChB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,CAAC;AACzJ,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAChD,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACxD,QAAA,IAAI,OAAO,EAAE;YACT,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAChD,SAAA;AACD,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,IAAI,CAAC,OAAO,GAAG,KAAK,EAAA;QAChB,MAAM,KAAK,GAAG,MAAK;AACf,YAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;AACrB,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACxD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACnB,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC;;QAGF,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACvC,SAAA;QAED,UAAU,CAAC,MAAK;AACZ,YAAA,KAAK,EAAE,CAAC;AACZ,SAAC,EAAE,CAAC,CAAC,CAAC;KACT;IAED,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AACtC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACvB;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,iBAAiB,GAAA;QACb,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KACpD;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;AACjB,QAAA,QACI,CAAC,IAAI,CAAC,gBAAgB;AAClB,cAAE,KAAK;gBACL,IAAI,CAAC,cAAc,EAAE;AAChB,qBAAA,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AACf,qBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;AAC5D,cAAE,KAAK,IAAI,CAAC,EAClB;KACL;AAED,IAAA,cAAc,CAAC,MAAW,EAAA;QACtB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;KAC9K;AAED,IAAA,cAAc,CAAC,MAAM,EAAA;QACjB,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;KAC7F;IAED,cAAc,CAAC,KAAK,EAAE,eAAe,EAAA;QACjC,OAAO,IAAI,CAAC,uBAAuB,GAAG,KAAK,GAAG,eAAe,IAAI,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;KACnH;AAED,IAAA,mBAAmB,CAAC,WAAgB,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,WAAW,IAAI,WAAW,CAAC,KAAK,IAAI,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC;KACrL;AAED,IAAA,sBAAsB,CAAC,WAAgB,EAAA;QACnC,OAAO,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;KAC7H;AAED,IAAA,iBAAiB,CAAC,UAAe,EAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACnB,YAAA,OAAO,UAAU,CAAC;AACrB,SAAA;AAED,QAAA,OAAO,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,IAAS,KAAK,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,UAAU,CAAC,CAAC;KAC5H;AAED,IAAA,gBAAgB,CAAC,MAAW,EAAA;QACxB,IAAI,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAE9C,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;KACxC;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,uBAAuB,CAAC,KAAqB,EAAA;AACzC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;YAC7B,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,GAAG,aAAa,GAAG,uBAAuB,CAAC,CAAC;YAE/J,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;AAChE,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAC5B,aAAA;YACD,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE;gBACvD,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,oBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC;AAEzE,oBAAA,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;AACtB,wBAAA,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AAC/C,qBAAA;AACJ,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,kCAAkC,CAAC,CAAC;AAEpG,oBAAA,IAAI,gBAAgB,EAAE;AAClB,wBAAA,gBAAgB,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC3E,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,SAAA;KACJ;AA/2CQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBA0lBD,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA1lBnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAKD,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,eAAe,CAKf,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,4PA6Cf,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAUhB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAKhB,gBAAgB,CAAA,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAKhB,eAAe,CAUf,EAAA,oBAAA,EAAA,sBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAUhD,gBAAgB,CAKhB,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,CAUf,EAAA,QAAA,EAAA,UAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,wDAKhB,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAUhB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,eAAe,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAyBf,gBAAgB,CAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAKhB,gBAAgB,CAKhB,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,CAKhB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAWhB,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKhB,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAAA,gBAAgB,oEAUhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,eAAe,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAyBf,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAA,EAAA,eAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAiFhB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAKhB,gBAAgB,CAKhB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAUhB,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,ujBAjVzB,CAAC,2BAA2B,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA0avB,aAAa,EAnoBpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkNT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,m3CAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,cAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA+3CkI,eAAe,CAAE,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,WAAW,CAAE,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,SAAS,2EAAE,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAn3ClL,YAAY,EAAA,UAAA,EAAA,CAAA;kBAhOxB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,gBAAgB,EAChB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkNT,EACK,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACjC,wBAAA,+BAA+B,EAAE,QAAQ;AACzC,wBAAA,8BAA8B,EAAE,yDAAyD;AACzF,wBAAA,kCAAkC,EAAE,wBAAwB;qBAC/D,EACU,SAAA,EAAA,CAAC,2BAA2B,CAAC,EACvB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,m3CAAA,CAAA,EAAA,CAAA;;0BA6lBxB,MAAM;2BAAC,QAAQ,CAAA;wMArlBW,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,qBAAqB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKkE,SAAS,EAAA,CAAA;sBAAhF,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAE,SAAS,EAAE,CAAC,KAAc,KAAK,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAA;gBAK7D,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAYO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAWG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAMG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAMG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAMG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKkC,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAMI,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAEiB,WAAW,EAAA,CAAA;sBAAlC,SAAS;uBAAC,WAAW,CAAA;gBAEG,OAAO,EAAA,CAAA;sBAA/B,SAAS;uBAAC,YAAY,CAAA;gBAED,YAAY,EAAA,CAAA;sBAAjC,SAAS;uBAAC,SAAS,CAAA;gBAES,gBAAgB,EAAA,CAAA;sBAA5C,SAAS;uBAAC,gBAAgB,CAAA;gBAEP,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBAEE,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBAEK,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;gBAEC,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAk9BrB,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,EAv3ClB,YAAA,EAAA,CAAA,YAAY,CAm3CX,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,CAAA,EAAA,OAAA,EAAA,CAn3ClL,YAAY,EAo3CG,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA;AAG3E,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,EAJjB,OAAA,EAAA,CAAA,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,EACnK,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA;;2FAG3E,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,CAAC;oBAC5L,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,CAAC;oBACrF,YAAY,EAAE,CAAC,YAAY,CAAC;AAC/B,iBAAA,CAAA;;;AC9oDD;;AAEG;;;;"}