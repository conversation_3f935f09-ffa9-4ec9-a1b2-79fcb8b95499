{"version": 3, "file": "primeng-divider.mjs", "sources": ["../../src/app/components/divider/divider.ts", "../../src/app/components/divider/primeng-divider.ts"], "sourcesContent": ["import { NgModule, Component, ChangeDetectionStrategy, ViewEncapsulation, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n/**\n * Divider is used to separate contents.\n * @group Components\n */\n@Component({\n    selector: 'p-divider',\n    template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" role=\"separator\" [attr.aria-orientation]=\"layout\" [attr.data-pc-name]=\"'divider'\">\n            <div class=\"p-divider-content\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./divider.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Divider {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Specifies the orientation.\n     * @group Props\n     */\n    @Input() layout: 'horizontal' | 'vertical' | undefined = 'horizontal';\n    /**\n     * Border style type.\n     * @group Props\n     */\n    @Input() type: 'solid' | 'dashed' | 'dotted' | undefined = 'solid';\n    /**\n     * Alignment of the content.\n     * @group Props\n     */\n    @Input() align: 'left' | 'center' | 'right' | 'top' | 'center' | 'bottom' | undefined;\n\n    containerClass() {\n        return {\n            'p-divider p-component': true,\n            'p-divider-horizontal': this.layout === 'horizontal',\n            'p-divider-vertical': this.layout === 'vertical',\n            'p-divider-solid': this.type === 'solid',\n            'p-divider-dashed': this.type === 'dashed',\n            'p-divider-dotted': this.type === 'dotted',\n            'p-divider-left': this.layout === 'horizontal' && (!this.align || this.align === 'left'),\n            'p-divider-center': (this.layout === 'horizontal' && this.align === 'center') || (this.layout === 'vertical' && (!this.align || this.align === 'center')),\n            'p-divider-right': this.layout === 'horizontal' && this.align === 'right',\n            'p-divider-top': this.layout === 'vertical' && this.align === 'top',\n            'p-divider-bottom': this.layout === 'vertical' && this.align === 'bottom'\n        };\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Divider],\n    declarations: [Divider]\n})\nexport class DividerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAEA;;;AAGG;MAiBU,OAAO,CAAA;AAChB;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACM,MAAM,GAA0C,YAAY,CAAC;AACtE;;;AAGG;IACM,IAAI,GAA8C,OAAO,CAAC;AACnE;;;AAGG;AACM,IAAA,KAAK,CAAwE;IAEtF,cAAc,GAAA;QACV,OAAO;AACH,YAAA,uBAAuB,EAAE,IAAI;AAC7B,YAAA,sBAAsB,EAAE,IAAI,CAAC,MAAM,KAAK,YAAY;AACpD,YAAA,oBAAoB,EAAE,IAAI,CAAC,MAAM,KAAK,UAAU;AAChD,YAAA,iBAAiB,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;AACxC,YAAA,kBAAkB,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ;AAC1C,YAAA,kBAAkB,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ;AAC1C,YAAA,gBAAgB,EAAE,IAAI,CAAC,MAAM,KAAK,YAAY,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC;AACxF,YAAA,kBAAkB,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,MAAM,IAAI,CAAC,MAAM,KAAK,UAAU,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC;YACzJ,iBAAiB,EAAE,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO;YACzE,eAAe,EAAE,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK;YACnE,kBAAkB,EAAE,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ;SAC5E,CAAC;KACL;uGAzCQ,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EAdN,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;AAMT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,wrCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,OAAO,EAAA,UAAA,EAAA,CAAA;kBAhBnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;AAMT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,wrCAAA,CAAA,EAAA,CAAA;8BAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;;MAwBG,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAjDb,YAAA,EAAA,CAAA,OAAO,CA6CN,EAAA,OAAA,EAAA,CAAA,YAAY,aA7Cb,OAAO,CAAA,EAAA,CAAA,CAAA;AAiDP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAJZ,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,OAAO,CAAC;oBAClB,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;ACtED;;AAEG;;;;"}