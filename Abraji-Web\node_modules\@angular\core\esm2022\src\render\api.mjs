/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { isLView } from '../render3/interfaces/type_checks';
import { RENDERER } from '../render3/interfaces/view';
import { getCurrentTNode, getLView } from '../render3/state';
import { getComponentLViewByIndex } from '../render3/util/view_utils';
/**
 * Creates and initializes a custom renderer that implements the `Renderer2` base class.
 *
 * @publicApi
 */
export class RendererFactory2 {
}
/**
 * Extend this base class to implement custom rendering. By default, <PERSON><PERSON>
 * renders a template into DOM. You can use custom rendering to intercept
 * rendering calls, or to render to something other than DOM.
 *
 * Create your custom renderer using `RendererFactory2`.
 *
 * Use a custom renderer to bypass <PERSON><PERSON>'s templating and
 * make custom UI changes that can't be expressed declaratively.
 * For example if you need to set a property or an attribute whose name is
 * not statically known, use the `setProperty()` or
 * `setAttribute()` method.
 *
 * @publicApi
 */
export class Renderer2 {
    constructor() {
        /**
         * If null or undefined, the view engine won't call it.
         * This is used as a performance optimization for production mode.
         */
        this.destroyNode = null;
    }
    /**
     * @internal
     * @nocollapse
     */
    static { this.__NG_ELEMENT_ID__ = () => injectRenderer2(); }
}
/** Injects a Renderer2 for the current component. */
export function injectRenderer2() {
    // We need the Renderer to be based on the component that it's being injected into, however since
    // DI happens before we've entered its view, `getLView` will return the parent view instead.
    const lView = getLView();
    const tNode = getCurrentTNode();
    const nodeAtIndex = getComponentLViewByIndex(tNode.index, lView);
    return (isLView(nodeAtIndex) ? nodeAtIndex : lView)[RENDERER];
}
//# sourceMappingURL=data:application/json;base64,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