{"version": 3, "file": "primeng-megamenu.mjs", "sources": ["../../src/app/components/megamenu/megamenu.ts", "../../src/app/components/megamenu/primeng-megamenu.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    effect,\n    forwardRef,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { MegaMenuItem, PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { VoidListener } from 'primeng/ts-helpers';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\n\n@Component({\n    selector: 'p-megaMenuSub',\n    template: `\n        <ul\n            *ngIf=\"isSubmenuVisible(submenu)\"\n            #menubar\n            [ngClass]=\"{ 'p-megamenu-root-list': root, 'p-submenu-list p-megamenu-submenu': !root }\"\n            [attr.role]=\"root ? 'menubar' : 'menu'\"\n            [attr.id]=\"id\"\n            [attr.aria-orientation]=\"orientation\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.data-pc-section]=\"root ? 'root' : 'submenu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <li *ngIf=\"submenu\" [ngClass]=\"getSubmenuHeaderClass(submenu)\" [ngStyle]=\"getItemProp(submenu, 'style')\" role=\"presentation\">{{ getItemLabel(submenu) }}</li>\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!megaMenu.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'horizontal'\" [attr.aria-hidden]=\"true\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'vertical'\" [attr.aria-hidden]=\"true\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"megaMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!megaMenu.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'horizontal'\" [attr.aria-hidden]=\"true\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" *ngIf=\"orientation === 'vertical'\" [attr.aria-hidden]=\"true\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"megaMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <div *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\" class=\"p-megamenu-panel\" [attr.data-pc-section]=\"'panel'\">\n                        <div class=\"p-megamenu-grid\" [attr.data-pc-section]=\"'grid'\">\n                            <div *ngFor=\"let col of processedItem.items\" [ngClass]=\"getColumnClass(processedItem)\">\n                                <p-megaMenuSub\n                                    *ngFor=\"let submenu of col\"\n                                    [id]=\"getSubListId(submenu)\"\n                                    [submenu]=\"submenu\"\n                                    [items]=\"submenu.items\"\n                                    [itemTemplate]=\"itemTemplate\"\n                                    [menuId]=\"menuId\"\n                                    [focusedItemId]=\"focusedItemId\"\n                                    [level]=\"level + 1\"\n                                    [root]=\"false\"\n                                    (itemClick)=\"itemClick.emit($event)\"\n                                    (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                                >\n                                </p-megaMenuSub>\n                            </div>\n                        </div>\n                    </div>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class MegaMenuSub {\n    @Input() id: string | undefined;\n\n    @Input() items: any[] | undefined;\n\n    @Input() itemTemplate: HTMLElement | undefined;\n\n    @Input() menuId: string | undefined;\n\n    @Input() ariaLabel: string | undefined;\n\n    @Input() ariaLabelledBy: string | undefined;\n\n    @Input({ transform: numberAttribute }) level: number = 0;\n\n    @Input() focusedItemId: string | undefined;\n\n    @Input({ transform: booleanAttribute }) disabled: boolean = false;\n\n    @Input() orientation: string | undefined;\n\n    @Input() activeItem: any;\n\n    @Input() submenu: any;\n\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n\n    @Input({ transform: booleanAttribute }) root: boolean = false;\n\n    @Output() itemClick: EventEmitter<any> = new EventEmitter();\n\n    @Output() itemMouseEnter: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuFocus: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuBlur: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuKeydown: EventEmitter<any> = new EventEmitter();\n\n    @ViewChild('menubar', { static: true }) menubarViewChild: ElementRef;\n\n    constructor(public el: ElementRef, @Inject(forwardRef(() => MegaMenu)) public megaMenu: MegaMenu) {}\n\n    onItemClick(event: any, processedItem: any) {\n        this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n        this.itemClick.emit({ originalEvent: event, processedItem, isFocus: true });\n    }\n\n    getItemProp(processedItem: any, name: string, params: any | null = null) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n\n    getItemId(processedItem: any): string {\n        return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n    }\n\n    getSubListId(processedItem) {\n        return `${this.getItemId(processedItem)}_list`;\n    }\n\n    getItemClass(processedItem: any) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem': true,\n            'p-menuitem-active p-highlight': this.isItemActive(processedItem),\n            'p-focus': this.isItemFocused(processedItem),\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n\n    getItemLabel(processedItem: any): string {\n        return this.getItemProp(processedItem, 'label');\n    }\n\n    getSeparatorItemClass(processedItem: any) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem-separator': true\n        };\n    }\n\n    getColumnClass(processedItem) {\n        let length = this.isItemGroup(processedItem) ? processedItem.items.length : 0;\n        let columnClass;\n\n        switch (length) {\n            case 2:\n                columnClass = 'p-megamenu-col-6';\n                break;\n\n            case 3:\n                columnClass = 'p-megamenu-col-4';\n                break;\n\n            case 4:\n                columnClass = 'p-megamenu-col-3';\n                break;\n\n            case 6:\n                columnClass = 'p-megamenu-col-2';\n                break;\n\n            default:\n                columnClass = 'p-megamenu-col-12';\n                break;\n        }\n\n        return columnClass;\n    }\n\n    getSubmenuHeaderClass(processedItem) {\n        return {\n            'p-megamenu-submenu-header p-submenu-header': true,\n            'p-disabled': this.isItemDisabled(processedItem),\n            ...this.getItemProp(processedItem, 'class')\n        };\n    }\n\n    isSubmenuVisible(submenu: any) {\n        if (this.submenu && !this.root) {\n            return this.isItemVisible(submenu);\n        } else {\n            return true;\n        }\n    }\n\n    isItemVisible(processedItem: any): boolean {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n\n    isItemActive(processedItem) {\n        return ObjectUtils.isNotEmpty(this.activeItem) ? this.activeItem.key === processedItem.key : false;\n    }\n\n    isItemDisabled(processedItem: any): boolean {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n\n    isItemFocused(processedItem: any): boolean {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n\n    isItemGroup(processedItem: any): boolean {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n\n    getAriaPosInset(index: number) {\n        return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n\n    onItemMouseEnter(param: any) {\n        const { event, processedItem } = param;\n        this.itemMouseEnter.emit({ originalEvent: event, processedItem });\n    }\n}\n/**\n * MegaMenu is navigation component that displays submenus together.\n * @group Components\n */\n@Component({\n    selector: 'p-megaMenu',\n    template: `\n        <div\n            [ngClass]=\"{ 'p-megamenu p-component': true, 'p-megamenu-horizontal': orientation == 'horizontal', 'p-megamenu-vertical': orientation == 'vertical' }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'megamenu'\"\n            [attr.id]=\"id\"\n        >\n            <div class=\"p-megamenu-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <p-megaMenuSub\n                #rootmenu\n                [itemTemplate]=\"itemTemplate\"\n                [items]=\"processedItems\"\n                [attr.id]=\"id + '_list'\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [orientation]=\"orientation\"\n                [ariaLabel]=\"ariaLabel\"\n                [disabled]=\"disabled\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [activeItem]=\"activeItem()\"\n                [level]=\"0\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-megaMenuSub>\n            <div class=\"p-megamenu-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-megamenu-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./megamenu.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class MegaMenu implements AfterContentInit, OnDestroy, OnInit {\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    @Input() set model(value: MegaMenuItem[] | undefined) {\n        this._model = value;\n        this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model(): MegaMenuItem[] | undefined {\n        return this._model;\n    }\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Defines the orientation.\n     * @group Props\n     */\n    @Input() orientation: 'horizontal' | 'vertical' | string = 'horizontal';\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean = false;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @ViewChild('menubutton') menubutton: ElementRef | undefined;\n\n    @ViewChild('rootmenu') rootmenu: MegaMenuSub | undefined;\n\n    startTemplate: TemplateRef<any> | undefined;\n\n    endTemplate: TemplateRef<any> | undefined;\n\n    menuIconTemplate: TemplateRef<any> | undefined;\n\n    submenuIconTemplate: TemplateRef<any> | undefined;\n\n    itemTemplate: TemplateRef<any> | undefined;\n\n    outsideClickListener: VoidListener;\n\n    resizeListener: VoidListener;\n\n    dirty: boolean = false;\n\n    focused: boolean = false;\n\n    activeItem = signal<any>(null);\n\n    focusedItemInfo = signal<any>({ index: -1, level: 0, parentKey: '', item: null });\n\n    searchValue: string = '';\n\n    searchTimeout: any;\n\n    _processedItems: any[];\n\n    _model: MegaMenuItem[] | undefined;\n\n    get visibleItems() {\n        const processedItem = ObjectUtils.isNotEmpty(this.activeItem()) ? this.activeItem() : null;\n\n        return processedItem\n            ? processedItem.items.reduce((items, col) => {\n                  col.forEach((submenu) => {\n                      submenu.items.forEach((a) => {\n                          items.push(a);\n                      });\n                  });\n\n                  return items;\n              }, [])\n            : this.processedItems;\n    }\n\n    get processedItems() {\n        if (!this._processedItems || !this._processedItems.length) {\n            this._processedItems = this.createProcessedItems(this.model || []);\n        }\n        return this._processedItems;\n    }\n\n    get focusedItemId() {\n        const focusedItem = this.focusedItemInfo();\n        return focusedItem?.item && focusedItem.item?.id ? focusedItem.item.id : ObjectUtils.isNotEmpty(focusedItem.key) ? `${this.id}_${focusedItem.key}` : null;\n    }\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, public el: ElementRef, public renderer: Renderer2, public config: PrimeNGConfig, public cd: ChangeDetectorRef) {\n        effect(() => {\n            const activeItem = this.activeItem();\n            if (ObjectUtils.isNotEmpty(activeItem)) {\n                this.bindOutsideClickListener();\n                this.bindResizeListener();\n            } else {\n                this.unbindOutsideClickListener();\n                this.unbindResizeListener();\n            }\n        });\n    }\n\n    ngOnInit(): void {\n        this.id = this.id || UniqueComponentId();\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n\n                case 'menuicon':\n                    this.menuIconTemplate = item.template;\n                    break;\n\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    createProcessedItems(items, level = 0, parent = {}, parentKey = '', columnIndex?) {\n        const processedItems = [];\n\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + (columnIndex !== undefined ? columnIndex + '_' : '') + index;\n                const newItem = {\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey,\n                    columnIndex: columnIndex !== undefined ? columnIndex : (<any>parent).columnIndex !== undefined ? (<any>parent).columnIndex : index\n                };\n\n                newItem['items'] =\n                    level === 0 && item.items && item.items.length > 0 ? item.items.map((_items, _index) => this.createProcessedItems(_items, level + 1, newItem, key, _index)) : this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n        return processedItems;\n    }\n\n    getItemProp(item: any, name: string) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n\n    onItemClick(event: any) {\n        const { originalEvent, processedItem } = event;\n        const grouped = this.isProcessedItemGroup(processedItem);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        const selected = this.isSelected(processedItem);\n\n        if (selected) {\n            const { index, key, parentKey, item } = processedItem;\n\n            this.activeItem.set(null);\n            this.focusedItemInfo.set({ index, key, parentKey, item });\n\n            this.dirty = !root;\n            DomHandler.focus(this.rootmenu?.menubarViewChild?.nativeElement);\n        } else {\n            if (grouped) {\n                this.onItemChange(event);\n            } else {\n                const rootProcessedItem = root ? processedItem : this.activeItem();\n                this.hide(originalEvent);\n                this.changeFocusedItemInfo(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n\n                DomHandler.focus(this.rootmenu?.menubarViewChild?.nativeElement);\n            }\n        }\n    }\n\n    onItemMouseEnter(event: any) {\n        if (!DomHandler.isTouchDevice()) {\n            this.onItemChange(event);\n        }\n    }\n\n    scrollInView(index: number = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n        const element = DomHandler.findSingle(this.rootmenu?.el.nativeElement, `li[id=\"${id}\"]`);\n\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n\n    onItemChange(event: any) {\n        const { processedItem, isFocus } = event;\n\n        if (ObjectUtils.isEmpty(processedItem)) return;\n\n        const { index, key, parentKey, items, item } = processedItem;\n        const grouped = ObjectUtils.isNotEmpty(items);\n\n        if (grouped) {\n            this.activeItem.set(processedItem);\n        }\n        this.focusedItemInfo.set({ index, key, parentKey, item });\n\n        grouped && (this.dirty = true);\n        isFocus && DomHandler.focus(this.rootmenu?.menubarViewChild?.nativeElement);\n    }\n\n    hide(event?, isFocus?: boolean) {\n        this.activeItem.set(null);\n        this.focusedItemInfo.set({ index: -1, key: '', parentKey: '', item: null });\n\n        isFocus && DomHandler.focus(this.rootmenu?.menubarViewChild?.nativeElement);\n        this.dirty = false;\n    }\n\n    onMenuFocus(event: any) {\n        this.focused = true;\n        if (this.focusedItemInfo().index === -1) {\n            const index = this.findFirstFocusedItemIndex();\n            const processedItem = this.findVisibleItem(index);\n\n            this.focusedItemInfo.set({ index, key: processedItem.key, parentKey: processedItem.parentKey, item: processedItem.item });\n        }\n    }\n\n    onMenuBlur(event: any) {\n        this.focused = false;\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        this.searchValue = '';\n        this.dirty = false;\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        const metaKey = event.metaKey || event.ctrlKey;\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            case 'PageDown':\n            case 'PageUp':\n            case 'Backspace':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchItems(event, event.key);\n                }\n\n                break;\n        }\n    }\n\n    findFirstFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n\n        return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n\n    findFirstItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n    }\n\n    findSelectedItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n    }\n\n    isProcessedItemGroup(processedItem: any): boolean {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    isSelected(processedItem: any): boolean {\n        return ObjectUtils.isNotEmpty(this.activeItem()) ? this.activeItem().key === processedItem.key : false;\n    }\n\n    isValidSelectedItem(processedItem: any): boolean {\n        return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n\n    isValidItem(processedItem: any): boolean {\n        return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n    }\n\n    isItemDisabled(item: any): boolean {\n        return this.getItemProp(item, 'disabled');\n    }\n\n    isItemSeparator(item: any): boolean {\n        return this.getItemProp(item, 'separator');\n    }\n\n    isItemMatched(processedItem: any): boolean {\n        return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n\n    isProccessedItemGroup(processedItem: any): boolean {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    searchItems(event: any, char: string) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let itemIndex = -1;\n        let matched = false;\n\n        if (this.focusedItemInfo().index !== -1) {\n            itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem));\n            itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n        } else {\n            itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n        }\n\n        if (itemIndex !== -1) {\n            matched = true;\n        }\n\n        if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n            itemIndex = this.findFirstFocusedItemIndex();\n        }\n\n        if (itemIndex !== -1) {\n            this.changeFocusedItemInfo(event, itemIndex);\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n\n    getProccessedItemLabel(processedItem: any) {\n        return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n\n    getItemLabel(item: any) {\n        return this.getItemProp(item, 'label');\n    }\n\n    changeFocusedItemInfo(event, index) {\n        const processedItem = this.findVisibleItem(index);\n        if (ObjectUtils.isNotEmpty(processedItem)) {\n            const { key, parentKey, item } = processedItem;\n            this.focusedItemInfo.set({ index, key: key ? key : '', parentKey, item });\n        }\n\n        this.scrollInView();\n    }\n\n    onArrowDownKey(event: KeyboardEvent) {\n        if (this.orientation === 'horizontal') {\n            if (ObjectUtils.isNotEmpty(this.activeItem()) && this.activeItem().key === this.focusedItemInfo().key) {\n                const { key, item } = this.activeItem();\n                this.focusedItemInfo.set({ index: -1, key: '', parentKey: key, item });\n            } else {\n                const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n                const grouped = this.isProccessedItemGroup(processedItem);\n\n                if (grouped) {\n                    const { parentKey, key, item } = processedItem;\n                    this.onItemChange({ originalEvent: event, processedItem });\n                    this.focusedItemInfo.set({ index: -1, key: key, parentKey: parentKey, item: item });\n                    this.searchValue = '';\n                }\n            }\n        }\n\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n        this.changeFocusedItemInfo(event, itemIndex);\n        event.preventDefault();\n    }\n\n    onArrowRightKey(event: KeyboardEvent) {\n        const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n        const grouped = this.isProccessedItemGroup(processedItem);\n\n        if (grouped) {\n            if (this.orientation === 'vertical') {\n                if (ObjectUtils.isNotEmpty(this.activeItem()) && this.activeItem().key === processedItem.key) {\n                    this.focusedItemInfo.set({ index: -1, key: '', parentKey: this.activeItem().key, item: processedItem.item });\n                } else {\n                    const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n                    const grouped = this.isProccessedItemGroup(processedItem);\n\n                    if (grouped) {\n                        this.onItemChange({ originalEvent: event, processedItem });\n                        this.focusedItemInfo.set({ index: -1, key: processedItem.key, parentKey: processedItem.parentKey, item: processedItem.item });\n                        this.searchValue = '';\n                    }\n                }\n            }\n\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n\n            this.changeFocusedItemInfo(event, itemIndex);\n        } else {\n            const columnIndex = processedItem.columnIndex + 1;\n            const itemIndex = this.visibleItems.findIndex((item) => item.columnIndex === columnIndex);\n\n            itemIndex !== -1 && this.changeFocusedItemInfo(event, itemIndex);\n        }\n\n        event.preventDefault();\n    }\n\n    onArrowUpKey(event: KeyboardEvent) {\n        if (event.altKey && this.orientation === 'horizontal') {\n            if (this.focusedItemInfo().index !== -1) {\n                const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n                const grouped = this.isProccessedItemGroup(processedItem);\n\n                if (!grouped && ObjectUtils.isNotEmpty(this.activeItem)) {\n                    if (this.focusedItemInfo().index === 0) {\n                        this.focusedItemInfo.set({ index: this.activeItem().index, key: this.activeItem().key, parentKey: this.activeItem().parentKey, item: processedItem.item });\n                        this.activeItem.set(null);\n                    } else {\n                        this.changeFocusedItemInfo(event, this.findFirstItemIndex());\n                    }\n                }\n            }\n\n            event.preventDefault();\n        } else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n\n            this.changeFocusedItemInfo(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n\n    onArrowLeftKey(event: KeyboardEvent) {\n        const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n        const grouped = this.isProccessedItemGroup(processedItem);\n\n        if (grouped) {\n            if (this.orientation === 'horizontal') {\n                const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n\n                this.changeFocusedItemInfo(event, itemIndex);\n            }\n        } else {\n            if (this.orientation === 'vertical' && ObjectUtils.isNotEmpty(this.activeItem())) {\n                if (processedItem.columnIndex === 0) {\n                    this.focusedItemInfo.set({ index: this.activeItem().index, key: this.activeItem().key, parentKey: this.activeItem().parentKey, item: processedItem.item });\n                    this.activeItem.set(null);\n                }\n            }\n\n            const columnIndex = processedItem.columnIndex - 1;\n            const itemIndex = this.visibleItems.findIndex((item) => item.columnIndex === columnIndex);\n\n            itemIndex !== -1 && this.changeFocusedItemInfo(event, itemIndex);\n        }\n\n        event.preventDefault();\n    }\n\n    onHomeKey(event: KeyboardEvent) {\n        this.changeFocusedItemInfo(event, this.findFirstItemIndex());\n        event.preventDefault();\n    }\n\n    onEndKey(event: KeyboardEvent) {\n        this.changeFocusedItemInfo(event, this.findLastItemIndex());\n        event.preventDefault();\n    }\n\n    onSpaceKey(event: KeyboardEvent) {\n        this.onEnterKey(event);\n    }\n\n    onEscapeKey(event: KeyboardEvent) {\n        if (ObjectUtils.isNotEmpty(this.activeItem())) {\n            this.focusedItemInfo.set({ index: this.activeItem().index, key: this.activeItem().key, item: this.activeItem().item });\n            this.activeItem.set(null);\n        }\n\n        event.preventDefault();\n    }\n\n    onTabKey(event: KeyboardEvent) {\n        if (this.focusedItemInfo().index !== -1) {\n            const processedItem = this.findVisibleItem(this.focusedItemInfo().index);\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            !grouped && this.onItemChange({ originalEvent: event, processedItem });\n        }\n\n        this.hide();\n    }\n\n    onEnterKey(event: KeyboardEvent) {\n        if (this.focusedItemInfo().index !== -1) {\n            const element = DomHandler.findSingle(this.rootmenu?.el?.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n            const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n\n            anchorElement ? anchorElement.click() : element && element.click();\n\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            !grouped && this.changeFocusedItemInfo(event, this.findFirstFocusedItemIndex());\n        }\n\n        event.preventDefault();\n    }\n\n    findVisibleItem(index) {\n        return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n    }\n\n    findLastFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n\n    findLastItemIndex() {\n        return ObjectUtils.findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n    }\n\n    findPrevItemIndex(index: number) {\n        const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n\n        return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n\n    findNextItemIndex(index: number) {\n        const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n\n        return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    this.hide(event, true);\n                });\n            }\n        }\n    }\n\n    bindOutsideClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                    const isOutsideContainer = this.rootmenu?.el.nativeElement !== event.target && !this.rootmenu?.el.nativeElement.contains(event.target);\n\n                    if (isOutsideContainer) {\n                        this.hide();\n                    }\n                });\n            }\n        }\n    }\n\n    unbindOutsideClickListener() {\n        if (this.outsideClickListener) {\n            this.outsideClickListener();\n            this.outsideClickListener = null;\n        }\n    }\n\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon],\n    exports: [MegaMenu, RouterModule, TooltipModule, SharedModule],\n    declarations: [MegaMenu, MegaMenuSub]\n})\nexport class MegaMenuModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;MAuMa,WAAW,CAAA;AAyCD,IAAA,EAAA,CAAA;AAA2D,IAAA,QAAA,CAAA;AAxCrE,IAAA,EAAE,CAAqB;AAEvB,IAAA,KAAK,CAAoB;AAEzB,IAAA,YAAY,CAA0B;AAEtC,IAAA,MAAM,CAAqB;AAE3B,IAAA,SAAS,CAAqB;AAE9B,IAAA,cAAc,CAAqB;IAEL,KAAK,GAAW,CAAC,CAAC;AAEhD,IAAA,aAAa,CAAqB;IAEH,QAAQ,GAAY,KAAK,CAAC;AAEzD,IAAA,WAAW,CAAqB;AAEhC,IAAA,UAAU,CAAM;AAEhB,IAAA,OAAO,CAAM;IAEiB,QAAQ,GAAW,CAAC,CAAC;IAEpB,IAAI,GAAY,KAAK,CAAC;AAEpD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElD,IAAA,cAAc,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEvD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElD,IAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEjD,IAAA,WAAW,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEtB,IAAA,gBAAgB,CAAa;IAErE,WAAmB,CAAA,EAAc,EAA6C,QAAkB,EAAA;QAA7E,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAA6C,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;KAAI;IAEpG,WAAW,CAAC,KAAU,EAAE,aAAkB,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/F,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;KAC/E;AAED,IAAA,WAAW,CAAC,aAAkB,EAAE,IAAY,EAAE,SAAqB,IAAI,EAAA;QACnE,OAAO,aAAa,IAAI,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC;KACvH;AAED,IAAA,SAAS,CAAC,aAAkB,EAAA;AACxB,QAAA,OAAO,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAI,CAAA,EAAA,aAAa,CAAC,GAAG,EAAE,CAAC;KACvH;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,OAAO,CAAA,EAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC;KAClD;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;AAC3C,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,+BAA+B,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AACjE,YAAA,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;AAC5C,YAAA,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;SACnD,CAAC;KACL;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;KACnD;AAED,IAAA,qBAAqB,CAAC,aAAkB,EAAA;QACpC,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;AAC3C,YAAA,sBAAsB,EAAE,IAAI;SAC/B,CAAC;KACL;AAED,IAAA,cAAc,CAAC,aAAa,EAAA;QACxB,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9E,QAAA,IAAI,WAAW,CAAC;AAEhB,QAAA,QAAQ,MAAM;AACV,YAAA,KAAK,CAAC;gBACF,WAAW,GAAG,kBAAkB,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,CAAC;gBACF,WAAW,GAAG,kBAAkB,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,CAAC;gBACF,WAAW,GAAG,kBAAkB,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,CAAC;gBACF,WAAW,GAAG,kBAAkB,CAAC;gBACjC,MAAM;AAEV,YAAA;gBACI,WAAW,GAAG,mBAAmB,CAAC;gBAClC,MAAM;AACb,SAAA;AAED,QAAA,OAAO,WAAW,CAAC;KACtB;AAED,IAAA,qBAAqB,CAAC,aAAa,EAAA;QAC/B,OAAO;AACH,YAAA,4CAA4C,EAAE,IAAI;AAClD,YAAA,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;AAChD,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;SAC9C,CAAC;KACL;AAED,IAAA,gBAAgB,CAAC,OAAY,EAAA;QACzB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACtC,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK,CAAC;KAC/D;AAED,IAAA,YAAY,CAAC,aAAa,EAAA;QACtB,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,GAAG,KAAK,CAAC;KACtG;AAED,IAAA,cAAc,CAAC,aAAkB,EAAA;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KACtD;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KAC/D;AAED,IAAA,WAAW,CAAC,aAAkB,EAAA;QAC1B,OAAO,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACtD;IAED,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;KAC1I;AAED,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACrK;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;AACvB,QAAA,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACvC,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;KACrE;AA7JQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,4CAyCuB,UAAU,CAAC,MAAM,QAAQ,CAAC,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAzC5D,WAAW,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAaA,eAAe,CAIf,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,gHAQhB,eAAe,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAEf,gBAAgB,CA3L1B,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0JT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAs3BgF,aAAa,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,cAAc,CAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAh3BrG,WAAW,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,cAAA,EAAA,QAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,eAAA,EAAA,UAAA,EAAA,aAAA,EAAA,YAAA,EAAA,SAAA,EAAA,UAAA,EAAA,MAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBAlKvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0JT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BA0CuC,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,QAAQ,CAAC,CAAA;yCAxC5D,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAEG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAEkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAEG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAEiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE5B,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAEG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAEiC,gBAAgB,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAA;;AAwH1C;;;AAGG;MAqDU,QAAQ,CAAA;AAqHqB,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA4B,IAAA,MAAA,CAAA;AAA8B,IAAA,EAAA,CAAA;AApHhM;;;AAGG;IACH,IAAa,KAAK,CAAC,KAAiC,EAAA;AAChD,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;KACvE;AACD,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AACD;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACM,WAAW,GAAuC,YAAY,CAAC;AACxE;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACoC,QAAQ,GAAW,CAAC,CAAC;AAE5B,IAAA,SAAS,CAAuC;AAEvD,IAAA,UAAU,CAAyB;AAErC,IAAA,QAAQ,CAA0B;AAEzD,IAAA,aAAa,CAA+B;AAE5C,IAAA,WAAW,CAA+B;AAE1C,IAAA,gBAAgB,CAA+B;AAE/C,IAAA,mBAAmB,CAA+B;AAElD,IAAA,YAAY,CAA+B;AAE3C,IAAA,oBAAoB,CAAe;AAEnC,IAAA,cAAc,CAAe;IAE7B,KAAK,GAAY,KAAK,CAAC;IAEvB,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,UAAU,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;IAE/B,eAAe,GAAG,MAAM,CAAM,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAElF,WAAW,GAAW,EAAE,CAAC;AAEzB,IAAA,aAAa,CAAM;AAEnB,IAAA,eAAe,CAAQ;AAEvB,IAAA,MAAM,CAA6B;AAEnC,IAAA,IAAI,YAAY,GAAA;QACZ,MAAM,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC;AAE3F,QAAA,OAAO,aAAa;AAChB,cAAE,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;AACtC,gBAAA,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;oBACpB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;AACxB,wBAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,qBAAC,CAAC,CAAC;AACP,iBAAC,CAAC,CAAC;AAEH,gBAAA,OAAO,KAAK,CAAC;aAChB,EAAE,EAAE,CAAC;AACR,cAAE,IAAI,CAAC,cAAc,CAAC;KAC7B;AAED,IAAA,IAAI,cAAc,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AACvD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACtE,SAAA;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;AAED,IAAA,IAAI,aAAa,GAAA;AACb,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,OAAO,WAAW,EAAE,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAI,CAAA,EAAA,WAAW,CAAC,GAAG,CAAA,CAAE,GAAG,IAAI,CAAC;KAC7J;IAED,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAS,EAAc,EAAS,QAAmB,EAAS,MAAqB,EAAS,EAAqB,EAAA;QAA/K,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACjN,MAAM,CAAC,MAAK;AACR,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACrC,YAAA,IAAI,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBACpC,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;KAC5C;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,KAAK;AACN,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,oBAAoB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,WAAY,EAAA;QAC5E,MAAM,cAAc,GAAG,EAAE,CAAC;QAE1B,KAAK;YACD,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AAC1B,gBAAA,MAAM,GAAG,GAAG,CAAC,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,GAAG,EAAE,KAAK,WAAW,KAAK,SAAS,GAAG,WAAW,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC;AACrH,gBAAA,MAAM,OAAO,GAAG;oBACZ,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,GAAG;oBACH,MAAM;oBACN,SAAS;oBACT,WAAW,EAAE,WAAW,KAAK,SAAS,GAAG,WAAW,GAAS,MAAO,CAAC,WAAW,KAAK,SAAS,GAAS,MAAO,CAAC,WAAW,GAAG,KAAK;iBACrI,CAAC;gBAEF,OAAO,CAAC,OAAO,CAAC;AACZ,oBAAA,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AACjO,gBAAA,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;AACP,QAAA,OAAO,cAAc,CAAC;KACzB;IAED,WAAW,CAAC,IAAS,EAAE,IAAY,EAAA;AAC/B,QAAA,OAAO,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;KAClE;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;AAClB,QAAA,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QACzD,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AAEhD,QAAA,IAAI,QAAQ,EAAE;YACV,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;AAEtD,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAE1D,YAAA,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC;YACnB,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;AACpE,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5B,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,iBAAiB,GAAG,IAAI,GAAG,aAAa,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACnE,gBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzB,gBAAA,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAE5F,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;AACpE,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;AAC7B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAA;KACJ;IAED,YAAY,CAAC,KAAgB,GAAA,CAAC,CAAC,EAAA;QAC3B,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,aAAa,CAAC;AACrE,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAEzF,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAU,EAAA;AACnB,QAAA,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;AAEzC,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC;YAAE,OAAO;AAE/C,QAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;QAC7D,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAE9C,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACtC,SAAA;AACD,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAE1D,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAC/B,QAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;KAC/E;IAED,IAAI,CAAC,KAAM,EAAE,OAAiB,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAE5E,QAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAElD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC7H,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7E,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QAE/C,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;;gBAEb,MAAM;AAEV,YAAA;gBACI,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;IAED,yBAAyB,GAAA;AACrB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAEnD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,aAAa,CAAC;KACxE;IAED,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC1F;IAED,qBAAqB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC;KAClG;AAED,IAAA,oBAAoB,CAAC,aAAkB,EAAA;QACnC,OAAO,aAAa,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvE;AAED,IAAA,UAAU,CAAC,aAAkB,EAAA;QACzB,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,GAAG,KAAK,CAAC;KAC1G;AAED,IAAA,mBAAmB,CAAC,aAAkB,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KAC5E;AAED,IAAA,WAAW,CAAC,aAAkB,EAAA;QAC1B,OAAO,CAAC,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KACnH;AAED,IAAA,cAAc,CAAC,IAAS,EAAA;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC7C;AAED,IAAA,eAAe,CAAC,IAAS,EAAA;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;KAC9C;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,iBAAiB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC;KAC7J;AAED,IAAA,qBAAqB,CAAC,aAAkB,EAAA;QACpC,OAAO,aAAa,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvE;IAED,WAAW,CAAC,KAAU,EAAE,IAAY,EAAA;AAChC,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;AAEnD,QAAA,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;QACnB,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;YAClI,SAAS,GAAG,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC;AACtM,SAAA;AAAM,aAAA;AACH,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;AACjG,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;YAClB,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACzD,YAAA,SAAS,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAChD,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;AAClB,YAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAChD,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,sBAAsB,CAAC,aAAkB,EAAA;AACrC,QAAA,OAAO,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;KAC5E;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC1C;IAED,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAA;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAClD,QAAA,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;YACvC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;YAC/C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7E,SAAA;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;KACvB;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,EAAE;YACnC,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE;gBACnG,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;gBACzE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,gBAAA,IAAI,OAAO,EAAE;oBACT,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;oBAC/C,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;oBAC3D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACpF,oBAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACzB,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAChJ,QAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC7C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;AAChC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;gBACjC,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE;AAC1F,oBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAChH,iBAAA;AAAM,qBAAA;AACH,oBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;oBACzE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,oBAAA,IAAI,OAAO,EAAE;wBACT,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AAC3D,wBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC9H,wBAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACzB,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAEhJ,YAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAChD,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC;AAClD,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;AAE1F,YAAA,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACpE,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAoB,EAAA;QAC7B,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,EAAE;YACnD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;gBACzE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;gBAE1D,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACrD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE;AACpC,wBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3J,wBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,qBAAA;AAAM,yBAAA;wBACH,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAChE,qBAAA;AACJ,iBAAA;AACJ,aAAA;YAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAE/I,YAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC7C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,EAAE;AACnC,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAE/I,gBAAA,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAChD,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;AAC9E,gBAAA,IAAI,aAAa,CAAC,WAAW,KAAK,CAAC,EAAE;AACjC,oBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3J,oBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,iBAAA;AACJ,aAAA;AAED,YAAA,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC;AAClD,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,WAAW,CAAC,CAAC;AAE1F,YAAA,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACpE,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC7D,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAC5D,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;QAC5B,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;AAC3C,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACvH,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;YACzE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,YAAA,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AAC1E,SAAA;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;KACf;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;QAC3B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;YACrC,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,aAAa,EAAE,UAAU,CAAG,EAAA,IAAI,CAAC,aAAa,CAAA,CAAE,CAAI,EAAA,CAAA,CAAC,CAAC;AAC/G,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC;AAE/F,YAAA,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;AAEnE,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,YAAA,CAAC,OAAO,IAAI,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;AACnF,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACtF;IAED,wBAAwB,GAAA;AACpB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACnD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,aAAa,CAAC;KACvE;IAED,iBAAiB,GAAA;QACb,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC3G;AAED,IAAA,iBAAiB,CAAC,KAAa,EAAA;AAC3B,QAAA,MAAM,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE3J,QAAA,OAAO,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,GAAG,KAAK,CAAC;KAC3D;AAED,IAAA,iBAAiB,CAAC,KAAa,EAAA;QAC3B,MAAM,gBAAgB,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEtK,QAAA,OAAO,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KACvE;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC,KAAK,KAAI;AACtF,oBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC3B,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,wBAAwB,GAAA;AACpB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;AAC/E,oBAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,aAAa,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEvI,oBAAA,IAAI,kBAAkB,EAAE;wBACpB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;uGArpBQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAqHG,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AArHpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,iOA8CG,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,eAAe,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAElB,aAAa,EAvGpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,s5CAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EA/MQ,WAAW,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,cAAA,EAAA,QAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,eAAA,EAAA,UAAA,EAAA,aAAA,EAAA,YAAA,EAAA,SAAA,EAAA,UAAA,EAAA,MAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAuNX,QAAQ,EAAA,UAAA,EAAA,CAAA;kBApDpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,s5CAAA,CAAA,EAAA,CAAA;;0BAuHY,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;sJAhHhE,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAWG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEL,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEL,UAAU,EAAA,CAAA;sBAAlC,SAAS;uBAAC,YAAY,CAAA;gBAEA,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;;MAomBZ,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAd,cAAc,EAAA,YAAA,EAAA,CA7pBd,QAAQ,EAvNR,WAAW,CAAA,EAAA,OAAA,EAAA,CAg3BV,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,CAAA,EAAA,OAAA,EAAA,CAzpBrG,QAAQ,EA0pBG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;AAGpD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAJb,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAC1F,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGpD,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,CAAC;oBAC/G,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;AAC9D,oBAAA,YAAY,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;AACxC,iBAAA,CAAA;;;AC1jCD;;AAEG;;;;"}