/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { FLAGS, HOST, NEXT, PARENT, T_HOST } from './view';
/**
 * Special location which allows easy identification of type. If we have an array which was
 * retrieved from the `LView` and that array has `true` at `TYPE` location, we know it is
 * `LContainer`.
 */
export const TYPE = 1;
/**
 * Below are constants for LContainer indices to help us look up LContainer members
 * without having to remember the specific indices.
 * Uglify will inline these when minifying so there shouldn't be a cost.
 */
// FLAGS, PARENT, NEXT, and T_HOST are indices 2, 3, 4, and 5
// As we already have these constants in LView, we don't need to re-create them.
export const DEHYDRATED_VIEWS = 6;
export const NATIVE = 7;
export const VIEW_REFS = 8;
export const MOVED_VIEWS = 9;
/**
 * Size of LContainer's header. Represents the index after which all views in the
 * container will be inserted. We need to keep a record of current views so we know
 * which views are already in the DOM (and don't need to be re-added) and so we can
 * remove views from the DOM when they are no longer required.
 */
export const CONTAINER_HEADER_OFFSET = 10;
/** Flags associated with an LContainer (saved in LContainer[FLAGS]) */
export var LContainerFlags;
(function (LContainerFlags) {
    LContainerFlags[LContainerFlags["None"] = 0] = "None";
    /**
     * Flag to signify that this `LContainer` may have transplanted views which need to be change
     * detected. (see: `LView[DECLARATION_COMPONENT_VIEW])`.
     *
     * This flag, once set, is never unset for the `LContainer`.
     */
    LContainerFlags[LContainerFlags["HasTransplantedViews"] = 2] = "HasTransplantedViews";
})(LContainerFlags || (LContainerFlags = {}));
//# sourceMappingURL=data:application/json;base64,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