/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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