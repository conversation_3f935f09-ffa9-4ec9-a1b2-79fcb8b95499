{"version": 3, "file": "primeng-speeddial.mjs", "sources": ["../../src/app/components/speeddial/speeddial.ts", "../../src/app/components/speeddial/primeng-speeddial.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { MenuItem, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { PlusIcon } from 'primeng/icons/plus';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { asapScheduler } from 'rxjs';\n\n/**\n * When pressed, a floating action button can display multiple primary actions that can be performed on a page.\n * @group Components\n */\n@Component({\n    selector: 'p-speedDial',\n    template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"className\" [ngStyle]=\"style\" [attr.data-pc-name]=\"'speeddial'\" [attr.data-pc-section]=\"'root'\">\n            <button\n                pRipple\n                pButton\n                class=\"p-button-icon-only\"\n                [ngStyle]=\"buttonStyle\"\n                [icon]=\"buttonIconClass\"\n                [ngClass]=\"buttonClass()\"\n                [disabled]=\"disabled\"\n                [attr.aria-expanded]=\"visible\"\n                [attr.aria-haspopup]=\"true\"\n                [attr.aria-controls]=\"id + '_list'\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                (click)=\"onButtonClick($event)\"\n                (keydown)=\"onTogglerKeydown($event)\"\n                [attr.data-pc-name]=\"'button'\"\n            >\n                <PlusIcon *ngIf=\"!showIcon && !buttonTemplate\" />\n                <ng-container *ngIf=\"buttonTemplate\">\n                    <ng-container *ngTemplateOutlet=\"buttonTemplate\"></ng-container>\n                </ng-container>\n            </button>\n            <ul\n                #list\n                class=\"p-speeddial-list\"\n                role=\"menu\"\n                [id]=\"id + '_list'\"\n                (focus)=\"onFocus($event)\"\n                (focusout)=\"onBlur($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                [tabindex]=\"-1\"\n                [attr.data-pc-section]=\"'menu'\"\n            >\n                <li\n                    *ngFor=\"let item of model; let i = index\"\n                    [ngStyle]=\"getItemStyle(i)\"\n                    class=\"p-speeddial-item\"\n                    pTooltip\n                    [tooltipOptions]=\"item.tooltipOptions\"\n                    [ngClass]=\"{ 'p-hidden': item.visible === false, 'p-focus': focusedOptionId == id + '_' + i }\"\n                    [id]=\"id + '_' + i\"\n                    [attr.aria-controls]=\"id + '_item'\"\n                    role=\"menuitem\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                >\n                    <ng-container *ngIf=\"itemTemplate\">\n                        <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                    </ng-container>\n                    <ng-container *ngIf=\"!itemTemplate\">\n                        <a\n                            *ngIf=\"isClickableRouterLink(item); else elseBlock\"\n                            pRipple\n                            [routerLink]=\"item.routerLink\"\n                            [queryParams]=\"item.queryParams\"\n                            class=\"p-speeddial-action\"\n                            [ngClass]=\"{ 'p-disabled': item.disabled }\"\n                            role=\"menuitem\"\n                            [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                            (click)=\"onItemClick($event, item)\"\n                            (keydown.enter)=\"onItemClick($event, item, i)\"\n                            [attr.target]=\"item.target\"\n                            [attr.tabindex]=\"item.disabled || readonly || !visible ? null : item.tabindex ? item.tabindex : '0'\"\n                            [fragment]=\"item.fragment\"\n                            [queryParamsHandling]=\"item.queryParamsHandling\"\n                            [preserveFragment]=\"item.preserveFragment\"\n                            [skipLocationChange]=\"item.skipLocationChange\"\n                            [replaceUrl]=\"item.replaceUrl\"\n                            [state]=\"item.state\"\n                            [attr.aria-label]=\"item.label\"\n                            [attr.data-pc-section]=\"'action'\"\n                        >\n                            <span class=\"p-speeddial-action-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\"></span>\n                        </a>\n                        <ng-template #elseBlock>\n                            <a\n                                [attr.href]=\"item.url || null\"\n                                class=\"p-speeddial-action\"\n                                role=\"menuitem\"\n                                pRipple\n                                (click)=\"onItemClick($event, item)\"\n                                [ngClass]=\"{ 'p-disabled': item.disabled }\"\n                                (keydown.enter)=\"onItemClick($event, item, i)\"\n                                [attr.target]=\"item.target\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [attr.aria-label]=\"item.label\"\n                                [attr.tabindex]=\"item.disabled || (i !== activeIndex && readonly) || !visible ? null : item.tabindex ? item.tabindex : '0'\"\n                            >\n                                <span class=\"p-speeddial-action-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\"></span>\n                            </a>\n                        </ng-template>\n                    </ng-container>\n                </li>\n            </ul>\n        </div>\n        <div *ngIf=\"mask && visible\" [ngClass]=\"{ 'p-speeddial-mask': true, 'p-speeddial-mask-visible': visible }\" [class]=\"maskClassName\" [ngStyle]=\"maskStyle\"></div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./speeddial.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class SpeedDial implements AfterViewInit, AfterContentInit, OnDestroy {\n    /**\n     * List of items id.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * MenuModel instance to define the action items.\n     * @group Props\n     */\n    @Input() model: MenuItem[] | null = null;\n    /**\n     * Specifies the visibility of the overlay.\n     * @defaultValue false\n     * @group Props\n     */\n    @Input() get visible(): boolean {\n        return this._visible;\n    }\n    set visible(value: boolean) {\n        this._visible = value;\n\n        if (this._visible) {\n            this.bindDocumentClickListener();\n        } else {\n            this.unbindDocumentClickListener();\n        }\n    }\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() className: string | undefined;\n    /**\n     * Specifies the opening direction of actions.\n     * @gruop Props\n     */\n    @Input() direction: 'up' | 'down' | 'left' | 'right' | 'up-left' | 'up-right' | 'down-left' | 'down-right' | undefined = 'up';\n    /**\n     * Transition delay step for each action item.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) transitionDelay: number = 30;\n    /**\n     * Specifies the opening type of actions.\n     * @group Props\n     */\n    @Input() type: 'linear' | 'circle' | 'semi-circle' | 'quarter-circle' | undefined = 'linear';\n    /**\n     * Radius for *circle types.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) radius: number = 0;\n    /**\n     * Whether to show a mask element behind the speeddial.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) mask: boolean = false;\n    /**\n     * Whether the component is disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean = false;\n    /**\n     * Whether the actions close when clicked outside.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) hideOnClickOutside: boolean = true;\n    /**\n     * Inline style of the button element.\n     * @group Props\n     */\n    @Input() buttonStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the button element.\n     * @group Props\n     */\n    @Input() buttonClassName: string | undefined;\n    /**\n     * Inline style of the mask element.\n     * @group Props\n     */\n    @Input() maskStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the mask element.\n     * @group Props\n     */\n    @Input() maskClassName: string | undefined;\n    /**\n     * Show icon of the button element.\n     * @group Props\n     */\n    @Input() showIcon: string | undefined;\n    /**\n     * Hide icon of the button element.\n     * @group Props\n     */\n    @Input() hideIcon: string | undefined;\n    /**\n     * Defined to rotate showIcon when hideIcon is not present.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) rotateAnimation: boolean = true;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Fired when the visibility of element changed.\n     * @param {boolean} boolean - Visibility value.\n     * @group Emits\n     */\n    @Output() onVisibleChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n    /**\n     * Fired when the visibility of element changed.\n     * @param {boolean} boolean - Visibility value.\n     * @group Emits\n     */\n    @Output() visibleChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n    /**\n     * Fired when the button element clicked.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    @Output() onClick: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();\n    /**\n     * Fired when the actions are visible.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Fired when the actions are hidden.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<Event> = new EventEmitter<Event>();\n\n    @ViewChild('container') container: ElementRef | undefined;\n\n    @ViewChild('list') list: ElementRef | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    buttonTemplate: TemplateRef<any> | undefined;\n\n    itemTemplate: TemplateRef<any> | undefined;\n\n    isItemClicked: boolean = false;\n\n    _visible: boolean = false;\n\n    documentClickListener: any;\n\n    focusedOptionIndex = signal<any>(null);\n\n    focused: boolean = false;\n\n    get focusedOptionId() {\n        return this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : null;\n    }\n\n    constructor(@Inject(PLATFORM_ID) private platformId: any, private el: ElementRef, public cd: ChangeDetectorRef, @Inject(DOCUMENT) private document: Document, private renderer: Renderer2) {}\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n    }\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.type !== 'linear') {\n                const button = DomHandler.findSingle(this.container?.nativeElement, '.p-speeddial-button');\n                const firstItem = DomHandler.findSingle(this.list?.nativeElement, '.p-speeddial-item');\n\n                if (button && firstItem) {\n                    const wDiff = Math.abs(button.offsetWidth - firstItem.offsetWidth);\n                    const hDiff = Math.abs(button.offsetHeight - firstItem.offsetHeight);\n                    this.list?.nativeElement.style.setProperty('--item-diff-x', `${wDiff / 2}px`);\n                    this.list?.nativeElement.style.setProperty('--item-diff-y', `${hDiff / 2}px`);\n                }\n            }\n        }\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'button':\n                    this.buttonTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    show() {\n        this.onVisibleChange.emit(true);\n        this.visibleChange.emit(true);\n        this._visible = true;\n        this.onShow.emit();\n        this.bindDocumentClickListener();\n        this.cd.markForCheck();\n    }\n\n    hide() {\n        this.onVisibleChange.emit(false);\n        this.visibleChange.emit(false);\n        this._visible = false;\n        this.onHide.emit();\n        this.unbindDocumentClickListener();\n        this.cd.markForCheck();\n    }\n\n    onButtonClick(event: MouseEvent) {\n        this.visible ? this.hide() : this.show();\n        this.onClick.emit(event);\n        this.isItemClicked = true;\n    }\n\n    onItemClick(e: MouseEvent, item: MenuItem) {\n        if (item.command) {\n            item.command({ originalEvent: e, item });\n        }\n\n        this.hide();\n\n        this.isItemClicked = true;\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDown(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUp(event);\n                break;\n\n            case 'ArrowLeft':\n                this.onArrowLeft(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRight(event);\n                break;\n\n            case 'Enter':\n            case 'Space':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onFocus(event) {\n        this.focused = true;\n    }\n\n    onBlur(event) {\n        this.focused = false;\n        asapScheduler.schedule(() => this.focusedOptionIndex.set(-1));\n    }\n\n    onArrowUp(event) {\n        if (this.direction === 'up') {\n            this.navigateNextItem(event);\n        } else if (this.direction === 'down') {\n            this.navigatePrevItem(event);\n        } else {\n            this.navigateNextItem(event);\n        }\n    }\n\n    onArrowDown(event) {\n        if (this.direction === 'up') {\n            this.navigatePrevItem(event);\n        } else if (this.direction === 'down') {\n            this.navigateNextItem(event);\n        } else {\n            this.navigatePrevItem(event);\n        }\n    }\n\n    onArrowLeft(event) {\n        const leftValidDirections = ['left', 'up-right', 'down-left'];\n        const rightValidDirections = ['right', 'up-left', 'down-right'];\n\n        if (leftValidDirections.includes(this.direction)) {\n            this.navigateNextItem(event);\n        } else if (rightValidDirections.includes(this.direction)) {\n            this.navigatePrevItem(event);\n        } else {\n            this.navigatePrevItem(event);\n        }\n    }\n\n    onArrowRight(event) {\n        const leftValidDirections = ['left', 'up-right', 'down-left'];\n        const rightValidDirections = ['right', 'up-left', 'down-right'];\n\n        if (leftValidDirections.includes(this.direction)) {\n            this.navigatePrevItem(event);\n        } else if (rightValidDirections.includes(this.direction)) {\n            this.navigateNextItem(event);\n        } else {\n            this.navigateNextItem(event);\n        }\n    }\n\n    onEndKey(event: any) {\n        event.preventDefault();\n\n        this.focusedOptionIndex.set(-1);\n        this.navigatePrevItem(event);\n    }\n\n    onHomeKey(event: any) {\n        event.preventDefault();\n\n        this.focusedOptionIndex.set(-1);\n        this.navigateNextItem(event);\n    }\n\n    onEnterKey(event: any) {\n        const items = DomHandler.find(this.container.nativeElement, '[data-pc-section=\"menuitem\"]');\n        const itemIndex = [...items].findIndex((item) => item.id === this.focusedOptionIndex());\n\n        this.onItemClick(event, this.model[itemIndex]);\n        this.onBlur(event);\n\n        const buttonEl = DomHandler.findSingle(this.container.nativeElement, 'button');\n\n        buttonEl && DomHandler.focus(buttonEl);\n    }\n\n    onEscapeKey(event: KeyboardEvent) {\n        this.hide();\n\n        const buttonEl = DomHandler.findSingle(this.container.nativeElement, 'button');\n\n        buttonEl && DomHandler.focus(buttonEl);\n    }\n\n    onTogglerKeydown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'ArrowDown':\n            case 'ArrowLeft':\n                this.onTogglerArrowDown(event);\n\n                break;\n\n            case 'ArrowUp':\n            case 'ArrowRight':\n                this.onTogglerArrowUp(event);\n\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onTogglerArrowUp(event) {\n        this.focused = true;\n        DomHandler.focus(this.list.nativeElement);\n\n        this.show();\n        this.navigatePrevItem(event);\n\n        event.preventDefault();\n    }\n\n    onTogglerArrowDown(event) {\n        this.focused = true;\n        DomHandler.focus(this.list.nativeElement);\n\n        this.show();\n        this.navigateNextItem(event);\n\n        event.preventDefault();\n    }\n\n    navigateNextItem(event) {\n        const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex());\n\n        this.changeFocusedOptionIndex(optionIndex);\n\n        event.preventDefault();\n    }\n\n    navigatePrevItem(event) {\n        const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex());\n\n        this.changeFocusedOptionIndex(optionIndex);\n\n        event.preventDefault();\n    }\n\n    findPrevOptionIndex(index) {\n        const items = DomHandler.find(this.container.nativeElement, '[data-pc-section=\"menuitem\"]');\n\n        const filteredItems = [...items].filter((item) => !DomHandler.hasClass(DomHandler.findSingle(item, 'a'), 'p-disabled'));\n        const newIndex = index === -1 ? filteredItems[filteredItems.length - 1].id : index;\n        let matchedOptionIndex = filteredItems.findIndex((link) => link.getAttribute('id') === newIndex);\n\n        matchedOptionIndex = index === -1 ? filteredItems.length - 1 : matchedOptionIndex - 1;\n\n        return matchedOptionIndex;\n    }\n\n    findNextOptionIndex(index) {\n        const items = DomHandler.find(this.container.nativeElement, '[data-pc-section=\"menuitem\"]');\n        const filteredItems = [...items].filter((item) => !DomHandler.hasClass(DomHandler.findSingle(item, 'a'), 'p-disabled'));\n        const newIndex = index === -1 ? filteredItems[0].id : index;\n        let matchedOptionIndex = filteredItems.findIndex((link) => link.getAttribute('id') === newIndex);\n\n        matchedOptionIndex = index === -1 ? 0 : matchedOptionIndex + 1;\n\n        return matchedOptionIndex;\n    }\n\n    changeFocusedOptionIndex(index) {\n        const items = DomHandler.find(this.container.nativeElement, '[data-pc-section=\"menuitem\"]');\n        const filteredItems = [...items].filter((item) => !DomHandler.hasClass(DomHandler.findSingle(item, 'a'), 'p-disabled'));\n\n        if (filteredItems[index]) {\n            this.focusedOptionIndex.set(filteredItems[index].getAttribute('id'));\n        }\n    }\n\n    calculatePointStyle(index: number) {\n        const type = this.type;\n\n        if (type !== 'linear') {\n            const length = (this.model as MenuItem[]).length;\n            const radius = this.radius || length * 20;\n\n            if (type === 'circle') {\n                const step = (2 * Math.PI) / length;\n\n                return {\n                    left: `calc(${radius * Math.cos(step * index)}px + var(--item-diff-x, 0px))`,\n                    top: `calc(${radius * Math.sin(step * index)}px + var(--item-diff-y, 0px))`\n                };\n            } else if (type === 'semi-circle') {\n                const direction = this.direction;\n                const step = Math.PI / (length - 1);\n                const x = `calc(${radius * Math.cos(step * index)}px + var(--item-diff-x, 0px))`;\n                const y = `calc(${radius * Math.sin(step * index)}px + var(--item-diff-y, 0px))`;\n                if (direction === 'up') {\n                    return { left: x, bottom: y };\n                } else if (direction === 'down') {\n                    return { left: x, top: y };\n                } else if (direction === 'left') {\n                    return { right: y, top: x };\n                } else if (direction === 'right') {\n                    return { left: y, top: x };\n                }\n            } else if (type === 'quarter-circle') {\n                const direction = this.direction;\n                const step = Math.PI / (2 * (length - 1));\n                const x = `calc(${radius * Math.cos(step * index)}px + var(--item-diff-x, 0px))`;\n                const y = `calc(${radius * Math.sin(step * index)}px + var(--item-diff-y, 0px))`;\n                if (direction === 'up-left') {\n                    return { right: x, bottom: y };\n                } else if (direction === 'up-right') {\n                    return { left: x, bottom: y };\n                } else if (direction === 'down-left') {\n                    return { right: y, top: x };\n                } else if (direction === 'down-right') {\n                    return { left: y, top: x };\n                }\n            }\n        }\n\n        return {};\n    }\n\n    calculateTransitionDelay(index: number) {\n        const length = (this.model as MenuItem[]).length;\n\n        return (this.visible ? index : length - index - 1) * this.transitionDelay;\n    }\n\n    containerClass() {\n        return {\n            ['p-speeddial p-component' + ` p-speeddial-${this.type}`]: true,\n            [`p-speeddial-direction-${this.direction}`]: this.type !== 'circle',\n            'p-speeddial-opened': this.visible,\n            'p-disabled': this.disabled\n        };\n    }\n\n    buttonClass() {\n        return {\n            'p-speeddial-button p-button-rounded': true,\n            'p-speeddial-rotate': this.rotateAnimation && !this.hideIcon,\n            [this.buttonClassName!]: true\n        };\n    }\n\n    get buttonIconClass() {\n        return (!this.visible && this.showIcon) || !this.hideIcon ? this.showIcon : this.hideIcon;\n    }\n\n    getItemStyle(index: number) {\n        const transitionDelay = this.calculateTransitionDelay(index);\n        const pointStyle = this.calculatePointStyle(index);\n        return {\n            transitionDelay: `${transitionDelay}ms`,\n            ...pointStyle\n        };\n    }\n\n    isClickableRouterLink(item: MenuItem) {\n        return item.routerLink && !this.disabled && !item.disabled;\n    }\n\n    isOutsideClicked(event: Event) {\n        return this.container && !(this.container.nativeElement.isSameNode(event.target) || this.container.nativeElement.contains(event.target) || this.isItemClicked);\n    }\n\n    bindDocumentClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.documentClickListener && this.hideOnClickOutside) {\n                this.documentClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                    if (this.visible && this.isOutsideClicked(event)) {\n                        this.hide();\n                    }\n\n                    this.isItemClicked = false;\n                });\n            }\n        }\n    }\n\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.unbindDocumentClickListener();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, ButtonModule, RippleModule, TooltipModule, RouterModule, PlusIcon],\n    exports: [SpeedDial, SharedModule, ButtonModule, TooltipModule, RouterModule],\n    declarations: [SpeedDial]\n})\nexport class SpeedDialModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAmCA;;;AAGG;MA6GU,SAAS,CAAA;AA6KuB,IAAA,UAAA,CAAA;AAAyB,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAAiD,IAAA,QAAA,CAAA;AAA4B,IAAA,QAAA,CAAA;AA5KtK;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;IACM,KAAK,GAAsB,IAAI,CAAC;AACzC;;;;AAIG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,KAAc,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACpC,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,SAAA;KACJ;AACD;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;IACM,SAAS,GAAuG,IAAI,CAAC;AAC9H;;;AAGG;IACoC,eAAe,GAAW,EAAE,CAAC;AACpE;;;AAGG;IACM,IAAI,GAAuE,QAAQ,CAAC;AAC7F;;;AAGG;IACoC,MAAM,GAAW,CAAC,CAAC;AAC1D;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACqC,kBAAkB,GAAY,IAAI,CAAC;AAC3E;;;AAGG;AACM,IAAA,WAAW,CAA8C;AAClE;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,SAAS,CAA8C;AAChE;;;AAGG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;IACqC,eAAe,GAAY,IAAI,CAAC;AACxE;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;;AAIG;AACO,IAAA,eAAe,GAA0B,IAAI,YAAY,EAAW,CAAC;AAC/E;;;;AAIG;AACO,IAAA,aAAa,GAA0B,IAAI,YAAY,EAAW,CAAC;AAC7E;;;;AAIG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC7E;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAE1C,IAAA,SAAS,CAAyB;AAEvC,IAAA,IAAI,CAAyB;AAEhB,IAAA,SAAS,CAAuC;AAEhF,IAAA,cAAc,CAA+B;AAE7C,IAAA,YAAY,CAA+B;IAE3C,aAAa,GAAY,KAAK,CAAC;IAE/B,QAAQ,GAAY,KAAK,CAAC;AAE1B,IAAA,qBAAqB,CAAM;AAE3B,IAAA,kBAAkB,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;IAEvC,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,IAAI,eAAe,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC;KAC9E;IAED,WAAyC,CAAA,UAAe,EAAU,EAAc,EAAS,EAAqB,EAA4B,QAAkB,EAAU,QAAmB,EAAA;QAAhJ,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAA4B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAAI;IAE7L,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;KAC5C;IAED,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AACxB,gBAAA,MAAM,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,qBAAqB,CAAC,CAAC;AAC3F,gBAAA,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,mBAAmB,CAAC,CAAC;gBAEvF,IAAI,MAAM,IAAI,SAAS,EAAE;AACrB,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;AACnE,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;AACrE,oBAAA,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,CAAG,EAAA,KAAK,GAAG,CAAC,CAAA,EAAA,CAAI,CAAC,CAAC;AAC9E,oBAAA,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,CAAG,EAAA,KAAK,GAAG,CAAC,CAAA,EAAA,CAAI,CAAC,CAAC;AACjF,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AACV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACnB,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACnB,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,aAAa,CAAC,KAAiB,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC7B;IAED,WAAW,CAAC,CAAa,EAAE,IAAc,EAAA;QACrC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5C,SAAA;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;AAEZ,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC7B;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,KAAK,EAAA;AACT,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;AAED,IAAA,MAAM,CAAC,KAAK,EAAA;AACR,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,aAAa,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACjE;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;AACX,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,EAAE;AAClC,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,EAAE;AAClC,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;QACb,MAAM,mBAAmB,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;QAC9D,MAAM,oBAAoB,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAEhE,IAAI,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC9C,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;aAAM,IAAI,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AACtD,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;QACd,MAAM,mBAAmB,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;QAC9D,MAAM,oBAAoB,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAEhE,IAAI,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC9C,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;aAAM,IAAI,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AACtD,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAU,EAAA;QACf,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KAChC;AAED,IAAA,SAAS,CAAC,KAAU,EAAA;QAChB,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KAChC;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;QAC5F,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAExF,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAEnB,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;AAE/E,QAAA,QAAQ,IAAI,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;KAC1C;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;QAC5B,IAAI,CAAC,IAAI,EAAE,CAAC;AAEZ,QAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;AAE/E,QAAA,QAAQ,IAAI,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;KAC1C;AAED,IAAA,gBAAgB,CAAC,KAAoB,EAAA;QACjC,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBAE/B,MAAM;AAEV,YAAA,KAAK,SAAS,CAAC;AACf,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAE7B,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBAExB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAK,EAAA;AAClB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE1C,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7B,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,kBAAkB,CAAC,KAAK,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE1C,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7B,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAK,EAAA;QAClB,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAExE,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAE3C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAK,EAAA;QAClB,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAExE,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAE3C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;AAE5F,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;QACxH,MAAM,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC;QACnF,IAAI,kBAAkB,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;AAEjG,QAAA,kBAAkB,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,CAAC;AAEtF,QAAA,OAAO,kBAAkB,CAAC;KAC7B;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;AAC5F,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AACxH,QAAA,MAAM,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC;QAC5D,IAAI,kBAAkB,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;AAEjG,QAAA,kBAAkB,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,CAAC;AAE/D,QAAA,OAAO,kBAAkB,CAAC;KAC7B;AAED,IAAA,wBAAwB,CAAC,KAAK,EAAA;AAC1B,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,8BAA8B,CAAC,CAAC;AAC5F,QAAA,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AAExH,QAAA,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;AACtB,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;AACxE,SAAA;KACJ;AAED,IAAA,mBAAmB,CAAC,KAAa,EAAA;AAC7B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAEvB,IAAI,IAAI,KAAK,QAAQ,EAAE;AACnB,YAAA,MAAM,MAAM,GAAI,IAAI,CAAC,KAAoB,CAAC,MAAM,CAAC;YACjD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG,EAAE,CAAC;YAE1C,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACnB,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC;gBAEpC,OAAO;AACH,oBAAA,IAAI,EAAE,CAAA,KAAA,EAAQ,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAA+B,6BAAA,CAAA;AAC5E,oBAAA,GAAG,EAAE,CAAA,KAAA,EAAQ,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAA+B,6BAAA,CAAA;iBAC9E,CAAC;AACL,aAAA;iBAAM,IAAI,IAAI,KAAK,aAAa,EAAE;AAC/B,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;gBACjC,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;AACpC,gBAAA,MAAM,CAAC,GAAG,CAAQ,KAAA,EAAA,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,+BAA+B,CAAC;AACjF,gBAAA,MAAM,CAAC,GAAG,CAAQ,KAAA,EAAA,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,+BAA+B,CAAC;gBACjF,IAAI,SAAS,KAAK,IAAI,EAAE;oBACpB,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AACjC,iBAAA;qBAAM,IAAI,SAAS,KAAK,MAAM,EAAE;oBAC7B,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAC9B,iBAAA;qBAAM,IAAI,SAAS,KAAK,MAAM,EAAE;oBAC7B,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAC/B,iBAAA;qBAAM,IAAI,SAAS,KAAK,OAAO,EAAE;oBAC9B,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAC9B,iBAAA;AACJ,aAAA;iBAAM,IAAI,IAAI,KAAK,gBAAgB,EAAE;AAClC,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACjC,gBAAA,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1C,gBAAA,MAAM,CAAC,GAAG,CAAQ,KAAA,EAAA,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,+BAA+B,CAAC;AACjF,gBAAA,MAAM,CAAC,GAAG,CAAQ,KAAA,EAAA,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,+BAA+B,CAAC;gBACjF,IAAI,SAAS,KAAK,SAAS,EAAE;oBACzB,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AAClC,iBAAA;qBAAM,IAAI,SAAS,KAAK,UAAU,EAAE;oBACjC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AACjC,iBAAA;qBAAM,IAAI,SAAS,KAAK,WAAW,EAAE;oBAClC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAC/B,iBAAA;qBAAM,IAAI,SAAS,KAAK,YAAY,EAAE;oBACnC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAC9B,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,EAAE,CAAC;KACb;AAED,IAAA,wBAAwB,CAAC,KAAa,EAAA;AAClC,QAAA,MAAM,MAAM,GAAI,IAAI,CAAC,KAAoB,CAAC,MAAM,CAAC;QAEjD,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC;KAC7E;IAED,cAAc,GAAA;QACV,OAAO;YACH,CAAC,yBAAyB,GAAG,CAAgB,aAAA,EAAA,IAAI,CAAC,IAAI,CAAA,CAAE,GAAG,IAAI;YAC/D,CAAC,CAAA,sBAAA,EAAyB,IAAI,CAAC,SAAS,CAAA,CAAE,GAAG,IAAI,CAAC,IAAI,KAAK,QAAQ;YACnE,oBAAoB,EAAE,IAAI,CAAC,OAAO;YAClC,YAAY,EAAE,IAAI,CAAC,QAAQ;SAC9B,CAAC;KACL;IAED,WAAW,GAAA;QACP,OAAO;AACH,YAAA,qCAAqC,EAAE,IAAI;YAC3C,oBAAoB,EAAE,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,QAAQ;AAC5D,YAAA,CAAC,IAAI,CAAC,eAAgB,GAAG,IAAI;SAChC,CAAC;KACL;AAED,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;KAC7F;AAED,IAAA,YAAY,CAAC,KAAa,EAAA;QACtB,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACnD,OAAO;YACH,eAAe,EAAE,CAAG,EAAA,eAAe,CAAI,EAAA,CAAA;AACvC,YAAA,GAAG,UAAU;SAChB,CAAC;KACL;AAED,IAAA,qBAAqB,CAAC,IAAc,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC9D;AAED,IAAA,gBAAgB,CAAC,KAAY,EAAA;AACzB,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC;KAClK;IAED,yBAAyB,GAAA;AACrB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACxD,gBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;oBAChF,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;wBAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AAED,oBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,2BAA2B,GAAA;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,2BAA2B,EAAE,CAAC;KACtC;uGAlkBQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA6KE,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAyF,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA7KvH,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EA+CE,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,eAAe,CAUf,EAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,eAAe,0BAKf,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAKhB,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAmChB,gBAAgB,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA8CnB,aAAa,EAnQpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkGT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,81DAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA8kBgF,QAAQ,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAtkBhF,SAAS,EAAA,UAAA,EAAA,CAAA;kBA5GrB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACb,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkGT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,81DAAA,CAAA,EAAA,CAAA;;0BA+KY,MAAM;2BAAC,WAAW,CAAA;;0BAAkF,MAAM;2BAAC,QAAQ,CAAA;iEAxKvH,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAMO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAgBG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKiC,eAAe,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKiC,MAAM,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,kBAAkB,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMI,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAMG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEiB,SAAS,EAAA,CAAA;sBAAhC,SAAS;uBAAC,WAAW,CAAA;gBAEH,IAAI,EAAA,CAAA;sBAAtB,SAAS;uBAAC,MAAM,CAAA;gBAEe,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAibrB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAf,eAAe,EAAA,YAAA,EAAA,CA1kBf,SAAS,CAskBR,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,CAtkBhF,EAAA,OAAA,EAAA,CAAA,SAAS,EAukBG,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;AAGnE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAJd,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EACpE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGnE,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,CAAC;oBAC1F,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;oBAC7E,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;AC5tBD;;AAEG;;;;"}