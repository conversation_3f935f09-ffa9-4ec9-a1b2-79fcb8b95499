/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertDefined } from '../../util/assert';
import { global } from '../../util/global';
import { setupFrameworkInjectorProfiler } from '../debug/framework_injector_profiler';
import { setProfiler } from '../profiler';
import { isSignal } from '../reactivity/api';
import { applyChanges } from './change_detection_utils';
import { getComponent, getContext, getDirectiveMetadata, getDirectives, getHostElement, getInjector, getListeners, getOwningComponent, getRootComponents, } from './discovery_utils';
import { getDependenciesFromInjectable, getInjectorMetadata, getInjectorProviders, getInjectorResolutionPath, } from './injector_discovery_utils';
/**
 * This file introduces series of globally accessible debug tools
 * to allow for the Angular debugging story to function.
 *
 * To see this in action run the following command:
 *
 *   bazel run //packages/core/test/bundling/todo:devserver
 *
 *  Then load `localhost:5432` and start using the console tools.
 */
/**
 * This value reflects the property on the window where the dev
 * tools are patched (window.ng).
 * */
export const GLOBAL_PUBLISH_EXPANDO_KEY = 'ng';
const globalUtilsFunctions = {
    /**
     * Warning: functions that start with `ɵ` are considered *INTERNAL* and should not be relied upon
     * in application's code. The contract of those functions might be changed in any release and/or a
     * function can be removed completely.
     */
    'ɵgetDependenciesFromInjectable': getDependenciesFromInjectable,
    'ɵgetInjectorProviders': getInjectorProviders,
    'ɵgetInjectorResolutionPath': getInjectorResolutionPath,
    'ɵgetInjectorMetadata': getInjectorMetadata,
    'ɵsetProfiler': setProfiler,
    'getDirectiveMetadata': getDirectiveMetadata,
    'getComponent': getComponent,
    'getContext': getContext,
    'getListeners': getListeners,
    'getOwningComponent': getOwningComponent,
    'getHostElement': getHostElement,
    'getInjector': getInjector,
    'getRootComponents': getRootComponents,
    'getDirectives': getDirectives,
    'applyChanges': applyChanges,
    'isSignal': isSignal,
};
let _published = false;
/**
 * Publishes a collection of default debug tools onto`window.ng`.
 *
 * These functions are available globally when Angular is in development
 * mode and are automatically stripped away from prod mode is on.
 */
export function publishDefaultGlobalUtils() {
    if (!_published) {
        _published = true;
        if (typeof window !== 'undefined') {
            // Only configure the injector profiler when running in the browser.
            setupFrameworkInjectorProfiler();
        }
        for (const [methodName, method] of Object.entries(globalUtilsFunctions)) {
            publishGlobalUtil(methodName, method);
        }
    }
}
/**
 * Publishes the given function to `window.ng` so that it can be
 * used from the browser console when an application is not in production.
 */
export function publishGlobalUtil(name, fn) {
    if (typeof COMPILED === 'undefined' || !COMPILED) {
        // Note: we can't export `ng` when using closure enhanced optimization as:
        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global
        // - we can't declare a closure extern as the namespace `ng` is already used within Google
        //   for typings for AngularJS (via `goog.provide('ng....')`).
        const w = global;
        ngDevMode && assertDefined(fn, 'function not defined');
        w[GLOBAL_PUBLISH_EXPANDO_KEY] ??= {};
        w[GLOBAL_PUBLISH_EXPANDO_KEY][name] = fn;
    }
}
//# sourceMappingURL=data:application/json;base64,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