{"version": 3, "file": "primeng-scrollpanel.mjs", "sources": ["../../src/app/components/scrollpanel/scrollpanel.ts", "../../src/app/components/scrollpanel/primeng-scrollpanel.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    Inject,\n    Input,\n    NgModule,\n    NgZone,\n    OnDestroy,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    numberAttribute\n} from '@angular/core';\nimport { PrimeTemplate } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * ScrollPanel is a cross browser, lightweight and themable alternative to native browser scrollbar.\n * @group Components\n */\n@Component({\n    selector: 'p-scrollPanel',\n    template: `\n        <div #container [ngClass]=\"'p-scrollpanel p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" [attr.data-pc-name]=\"'scrollpanel'\">\n            <div class=\"p-scrollpanel-wrapper\" [attr.data-pc-section]=\"'wrapper'\">\n                <div #content class=\"p-scrollpanel-content\" [attr.data-pc-section]=\"'content'\" (mouseenter)=\"moveBar()\" (scroll)=\"onScroll($event)\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n            </div>\n            <div\n                #xBar\n                class=\"p-scrollpanel-bar p-scrollpanel-bar-x\"\n                tabindex=\"0\"\n                role=\"scrollbar\"\n                [attr.aria-orientation]=\"'horizontal'\"\n                [attr.aria-valuenow]=\"lastScrollLeft\"\n                [attr.data-pc-section]=\"'barx'\"\n                [attr.aria-controls]=\"contentId\"\n                (mousedown)=\"onXBarMouseDown($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                (keyup)=\"onKeyUp()\"\n                (focus)=\"onFocus($event)\"\n                (blur)=\"onBlur()\"\n            ></div>\n            <div\n                #yBar\n                class=\"p-scrollpanel-bar p-scrollpanel-bar-y\"\n                tabindex=\"0\"\n                role=\"scrollbar\"\n                [attr.aria-orientation]=\"'vertical'\"\n                [attr.aria-valuenow]=\"lastScrollTop\"\n                [attr.data-pc-section]=\"'bary'\"\n                [attr.aria-controls]=\"contentId\"\n                (mousedown)=\"onYBarMouseDown($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                (keyup)=\"onKeyUp()\"\n                (focus)=\"onFocus($event)\"\n            ></div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./scrollpanel.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ScrollPanel implements AfterViewInit, AfterContentInit, OnDestroy {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Step factor to scroll the content while pressing the arrow keys.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) step: number = 5;\n\n    @ViewChild('container') containerViewChild: ElementRef | undefined;\n\n    @ViewChild('content') contentViewChild: ElementRef | undefined;\n\n    @ViewChild('xBar') xBarViewChild: ElementRef | undefined;\n\n    @ViewChild('yBar') yBarViewChild: ElementRef | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    scrollYRatio: number | undefined;\n\n    scrollXRatio: number | undefined;\n\n    timeoutFrame: any = (fn: VoidFunction) => setTimeout(fn, 0);\n\n    initialized: boolean = false;\n\n    lastPageY: number | undefined;\n\n    lastPageX: number | undefined;\n\n    isXBarClicked: boolean = false;\n\n    isYBarClicked: boolean = false;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    lastScrollLeft: number = 0;\n\n    lastScrollTop: number = 0;\n\n    orientation: string = 'vertical';\n\n    timer: any;\n\n    contentId: string | undefined;\n\n    windowResizeListener: VoidFunction | null | undefined;\n\n    contentScrollListener: VoidFunction | null | undefined;\n\n    mouseEnterListener: VoidFunction | null | undefined;\n\n    xBarMouseDownListener: VoidFunction | null | undefined;\n\n    yBarMouseDownListener: VoidFunction | null | undefined;\n\n    documentMouseMoveListener: Nullable<(event?: any) => void>;\n\n    documentMouseUpListener: Nullable<(event?: any) => void>;\n\n    constructor(@Inject(PLATFORM_ID) private platformId: any, public el: ElementRef, public zone: NgZone, public cd: ChangeDetectorRef, @Inject(DOCUMENT) private document: Document, private renderer: Renderer2) {\n        this.contentId = UniqueComponentId() + '_content';\n    }\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                this.moveBar();\n                this.moveBar = this.moveBar.bind(this);\n                this.onXBarMouseDown = this.onXBarMouseDown.bind(this);\n                this.onYBarMouseDown = this.onYBarMouseDown.bind(this);\n                this.onDocumentMouseMove = this.onDocumentMouseMove.bind(this);\n                this.onDocumentMouseUp = this.onDocumentMouseUp.bind(this);\n\n                this.windowResizeListener = this.renderer.listen(window, 'resize', this.moveBar);\n                this.contentScrollListener = this.renderer.listen((this.contentViewChild as ElementRef).nativeElement, 'scroll', this.moveBar);\n                this.mouseEnterListener = this.renderer.listen((this.contentViewChild as ElementRef).nativeElement, 'mouseenter', this.moveBar);\n                this.xBarMouseDownListener = this.renderer.listen((this.xBarViewChild as ElementRef).nativeElement, 'mousedown', this.onXBarMouseDown);\n                this.yBarMouseDownListener = this.renderer.listen((this.yBarViewChild as ElementRef).nativeElement, 'mousedown', this.onYBarMouseDown);\n                this.calculateContainerHeight();\n\n                this.initialized = true;\n            });\n        }\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    calculateContainerHeight() {\n        let container = (this.containerViewChild as ElementRef).nativeElement;\n        let content = (this.contentViewChild as ElementRef).nativeElement;\n        let xBar = (this.xBarViewChild as ElementRef).nativeElement;\n        const window = this.document.defaultView as Window;\n\n        let containerStyles: { [klass: string]: any } = window.getComputedStyle(container),\n            xBarStyles = window.getComputedStyle(xBar),\n            pureContainerHeight = DomHandler.getHeight(container) - parseInt(xBarStyles['height'], 10);\n\n        if (containerStyles['max-height'] != 'none' && pureContainerHeight == 0) {\n            if (content.offsetHeight + parseInt(xBarStyles['height'], 10) > parseInt(containerStyles['max-height'], 10)) {\n                container.style.height = containerStyles['max-height'];\n            } else {\n                container.style.height = content.offsetHeight + parseFloat(containerStyles.paddingTop) + parseFloat(containerStyles.paddingBottom) + parseFloat(containerStyles.borderTopWidth) + parseFloat(containerStyles.borderBottomWidth) + 'px';\n            }\n        }\n    }\n\n    moveBar() {\n        let container = (this.containerViewChild as ElementRef).nativeElement;\n        let content = (this.contentViewChild as ElementRef).nativeElement;\n\n        /* horizontal scroll */\n        let xBar = (this.xBarViewChild as ElementRef).nativeElement;\n        let totalWidth = content.scrollWidth;\n        let ownWidth = content.clientWidth;\n        let bottom = (container.clientHeight - xBar.clientHeight) * -1;\n\n        this.scrollXRatio = ownWidth / totalWidth;\n\n        /* vertical scroll */\n        let yBar = (this.yBarViewChild as ElementRef).nativeElement;\n        let totalHeight = content.scrollHeight;\n        let ownHeight = content.clientHeight;\n        let right = (container.clientWidth - yBar.clientWidth) * -1;\n\n        this.scrollYRatio = ownHeight / totalHeight;\n\n        this.requestAnimationFrame(() => {\n            if ((this.scrollXRatio as number) >= 1) {\n                xBar.setAttribute('data-p-scrollpanel-hidden', 'true');\n                DomHandler.addClass(xBar, 'p-scrollpanel-hidden');\n            } else {\n                xBar.setAttribute('data-p-scrollpanel-hidden', 'false');\n                DomHandler.removeClass(xBar, 'p-scrollpanel-hidden');\n                const xBarWidth = Math.max((this.scrollXRatio as number) * 100, 10);\n                const xBarLeft = (content.scrollLeft * (100 - xBarWidth)) / (totalWidth - ownWidth);\n                xBar.style.cssText = 'width:' + xBarWidth + '%; left:' + xBarLeft + '%;bottom:' + bottom + 'px;';\n            }\n\n            if ((this.scrollYRatio as number) >= 1) {\n                yBar.setAttribute('data-p-scrollpanel-hidden', 'true');\n                DomHandler.addClass(yBar, 'p-scrollpanel-hidden');\n            } else {\n                yBar.setAttribute('data-p-scrollpanel-hidden', 'false');\n                DomHandler.removeClass(yBar, 'p-scrollpanel-hidden');\n                const yBarHeight = Math.max((this.scrollYRatio as number) * 100, 10);\n                const yBarTop = (content.scrollTop * (100 - yBarHeight)) / (totalHeight - ownHeight);\n                yBar.style.cssText = 'height:' + yBarHeight + '%; top: calc(' + yBarTop + '% - ' + xBar.clientHeight + 'px);right:' + right + 'px;';\n            }\n        });\n        this.cd.markForCheck();\n    }\n\n    onScroll(event) {\n        if (this.lastScrollLeft !== event.target.scrollLeft) {\n            this.lastScrollLeft = event.target.scrollLeft;\n            this.orientation = 'horizontal';\n        } else if (this.lastScrollTop !== event.target.scrollTop) {\n            this.lastScrollTop = event.target.scrollTop;\n            this.orientation = 'vertical';\n        }\n\n        this.moveBar();\n    }\n\n    onKeyDown(event) {\n        if (this.orientation === 'vertical') {\n            switch (event.code) {\n                case 'ArrowDown': {\n                    this.setTimer('scrollTop', this.step);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowUp': {\n                    this.setTimer('scrollTop', this.step * -1);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowLeft':\n\n                case 'ArrowRight': {\n                    event.preventDefault();\n                    break;\n                }\n\n                default:\n                    //no op\n                    break;\n            }\n        } else if (this.orientation === 'horizontal') {\n            switch (event.code) {\n                case 'ArrowRight': {\n                    this.setTimer('scrollLeft', this.step);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowLeft': {\n                    this.setTimer('scrollLeft', this.step * -1);\n                    event.preventDefault();\n                    break;\n                }\n\n                case 'ArrowDown':\n\n                case 'ArrowUp': {\n                    event.preventDefault();\n                    break;\n                }\n\n                default:\n                    //no op\n                    break;\n            }\n        }\n    }\n\n    onKeyUp() {\n        this.clearTimer();\n    }\n\n    repeat(bar, step) {\n        this.contentViewChild.nativeElement[bar] += step;\n        this.moveBar();\n    }\n\n    setTimer(bar, step) {\n        this.clearTimer();\n        this.timer = setTimeout(() => {\n            this.repeat(bar, step);\n        }, 40);\n    }\n\n    clearTimer() {\n        if (this.timer) {\n            clearTimeout(this.timer);\n        }\n    }\n\n    bindDocumentMouseListeners(): void {\n        if (!this.documentMouseMoveListener) {\n            this.documentMouseMoveListener = (e) => {\n                this.onDocumentMouseMove(e);\n            };\n            this.document.addEventListener('mousemove', this.documentMouseMoveListener);\n        }\n\n        if (!this.documentMouseUpListener) {\n            this.documentMouseUpListener = (e) => {\n                this.onDocumentMouseUp(e);\n            };\n            this.document.addEventListener('mouseup', this.documentMouseUpListener);\n        }\n    }\n\n    unbindDocumentMouseListeners(): void {\n        if (this.documentMouseMoveListener) {\n            this.document.removeEventListener('mousemove', this.documentMouseMoveListener);\n            this.documentMouseMoveListener = null;\n        }\n\n        if (this.documentMouseUpListener) {\n            document.removeEventListener('mouseup', this.documentMouseUpListener);\n            this.documentMouseUpListener = null;\n        }\n    }\n\n    onYBarMouseDown(e: MouseEvent) {\n        this.isYBarClicked = true;\n        this.yBarViewChild.nativeElement.focus();\n        this.lastPageY = e.pageY;\n\n        this.yBarViewChild.nativeElement.setAttribute('data-p-scrollpanel-grabbed', 'true');\n        DomHandler.addClass((this.yBarViewChild as ElementRef).nativeElement, 'p-scrollpanel-grabbed');\n\n        this.document.body.setAttribute('data-p-scrollpanel-grabbed', 'true');\n        DomHandler.addClass(this.document.body, 'p-scrollpanel-grabbed');\n        this.bindDocumentMouseListeners();\n        e.preventDefault();\n    }\n\n    onXBarMouseDown(e: MouseEvent) {\n        this.isXBarClicked = true;\n        this.xBarViewChild.nativeElement.focus();\n        this.lastPageX = e.pageX;\n\n        this.xBarViewChild.nativeElement.setAttribute('data-p-scrollpanel-grabbed', 'false');\n        DomHandler.addClass((this.xBarViewChild as ElementRef).nativeElement, 'p-scrollpanel-grabbed');\n\n        this.document.body.setAttribute('data-p-scrollpanel-grabbed', 'false');\n        DomHandler.addClass(this.document.body, 'p-scrollpanel-grabbed');\n\n        this.bindDocumentMouseListeners();\n        e.preventDefault();\n    }\n\n    onDocumentMouseMove(e: MouseEvent) {\n        if (this.isXBarClicked) {\n            this.onMouseMoveForXBar(e);\n        } else if (this.isYBarClicked) {\n            this.onMouseMoveForYBar(e);\n        } else {\n            this.onMouseMoveForXBar(e);\n            this.onMouseMoveForYBar(e);\n        }\n    }\n\n    onMouseMoveForXBar(e: MouseEvent) {\n        let deltaX = e.pageX - (this.lastPageX as number);\n        this.lastPageX = e.pageX;\n\n        this.requestAnimationFrame(() => {\n            (this.contentViewChild as ElementRef).nativeElement.scrollLeft += deltaX / (this.scrollXRatio as number);\n        });\n    }\n\n    onMouseMoveForYBar(e: MouseEvent) {\n        let deltaY = e.pageY - (this.lastPageY as number);\n        this.lastPageY = e.pageY;\n\n        this.requestAnimationFrame(() => {\n            (this.contentViewChild as ElementRef).nativeElement.scrollTop += deltaY / (this.scrollYRatio as number);\n        });\n    }\n    /**\n     * Scrolls the top location to the given value.\n     * @param scrollTop\n     * @group Method\n     */\n    scrollTop(scrollTop: number) {\n        let scrollableHeight = (this.contentViewChild as ElementRef).nativeElement.scrollHeight - (this.contentViewChild as ElementRef).nativeElement.clientHeight;\n        scrollTop = scrollTop > scrollableHeight ? scrollableHeight : scrollTop > 0 ? scrollTop : 0;\n        (this.contentViewChild as ElementRef).nativeElement.scrollTop = scrollTop;\n    }\n\n    onFocus(event) {\n        if (this.xBarViewChild.nativeElement.isSameNode(event.target)) {\n            this.orientation = 'horizontal';\n        } else if (this.yBarViewChild.nativeElement.isSameNode(event.target)) {\n            this.orientation = 'vertical';\n        }\n    }\n\n    onBlur() {\n        if (this.orientation === 'horizontal') {\n            this.orientation = 'vertical';\n        }\n    }\n\n    onDocumentMouseUp(e: Event) {\n        this.yBarViewChild.nativeElement.setAttribute('data-p-scrollpanel-grabbed', 'false');\n        DomHandler.removeClass((this.yBarViewChild as ElementRef).nativeElement, 'p-scrollpanel-grabbed');\n        this.xBarViewChild.nativeElement.setAttribute('data-p-scrollpanel-grabbed', 'false');\n        DomHandler.removeClass((this.xBarViewChild as ElementRef).nativeElement, 'p-scrollpanel-grabbed');\n        this.document.body.setAttribute('data-p-scrollpanel-grabbed', 'false');\n        DomHandler.removeClass(this.document.body, 'p-scrollpanel-grabbed');\n\n        this.unbindDocumentMouseListeners();\n        this.isXBarClicked = false;\n        this.isYBarClicked = false;\n    }\n\n    requestAnimationFrame(f: VoidFunction) {\n        let frame = window.requestAnimationFrame || this.timeoutFrame;\n        frame(f);\n    }\n\n    unbindListeners() {\n        if (this.windowResizeListener) {\n            this.windowResizeListener();\n            this.windowResizeListener = null;\n        }\n\n        if (this.contentScrollListener) {\n            this.contentScrollListener();\n            this.contentScrollListener = null;\n        }\n\n        if (this.mouseEnterListener) {\n            this.mouseEnterListener();\n            this.mouseEnterListener = null;\n        }\n\n        if (this.xBarMouseDownListener) {\n            this.xBarMouseDownListener();\n            this.xBarMouseDownListener = null;\n        }\n\n        if (this.yBarMouseDownListener) {\n            this.yBarMouseDownListener();\n            this.yBarMouseDownListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.initialized) {\n            this.unbindListeners();\n        }\n    }\n    /**\n     * Refreshes the position and size of the scrollbar.\n     * @group Method\n     */\n    refresh() {\n        this.moveBar();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [ScrollPanel],\n    declarations: [ScrollPanel]\n})\nexport class ScrollPanelModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;AA2BA;;;AAGG;MAiDU,WAAW,CAAA;AAqEqB,IAAA,UAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAAqB,IAAA,EAAA,CAAA;AAAiD,IAAA,QAAA,CAAA;AAA4B,IAAA,QAAA,CAAA;AApE1L;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACoC,IAAI,GAAW,CAAC,CAAC;AAEhC,IAAA,kBAAkB,CAAyB;AAE7C,IAAA,gBAAgB,CAAyB;AAE5C,IAAA,aAAa,CAAyB;AAEtC,IAAA,aAAa,CAAyB;AAEzB,IAAA,SAAS,CAAuC;AAEhF,IAAA,YAAY,CAAqB;AAEjC,IAAA,YAAY,CAAqB;AAEjC,IAAA,YAAY,GAAQ,CAAC,EAAgB,KAAK,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAE5D,WAAW,GAAY,KAAK,CAAC;AAE7B,IAAA,SAAS,CAAqB;AAE9B,IAAA,SAAS,CAAqB;IAE9B,aAAa,GAAY,KAAK,CAAC;IAE/B,aAAa,GAAY,KAAK,CAAC;AAE/B,IAAA,eAAe,CAA+B;IAE9C,cAAc,GAAW,CAAC,CAAC;IAE3B,aAAa,GAAW,CAAC,CAAC;IAE1B,WAAW,GAAW,UAAU,CAAC;AAEjC,IAAA,KAAK,CAAM;AAEX,IAAA,SAAS,CAAqB;AAE9B,IAAA,oBAAoB,CAAkC;AAEtD,IAAA,qBAAqB,CAAkC;AAEvD,IAAA,kBAAkB,CAAkC;AAEpD,IAAA,qBAAqB,CAAkC;AAEvD,IAAA,qBAAqB,CAAkC;AAEvD,IAAA,yBAAyB,CAAkC;AAE3D,IAAA,uBAAuB,CAAkC;IAEzD,WAAyC,CAAA,UAAe,EAAS,EAAc,EAAS,IAAY,EAAS,EAAqB,EAA4B,QAAkB,EAAU,QAAmB,EAAA;QAApK,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAA4B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;AACzM,QAAA,IAAI,CAAC,SAAS,GAAG,iBAAiB,EAAE,GAAG,UAAU,CAAC;KACrD;IAED,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE3D,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBACjF,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAE,IAAI,CAAC,gBAA+B,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC/H,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAE,IAAI,CAAC,gBAA+B,CAAC,aAAa,EAAE,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAE,IAAI,CAAC,aAA4B,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBACvI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAE,IAAI,CAAC,aAA4B,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBACvI,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAEhC,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,wBAAwB,GAAA;AACpB,QAAA,IAAI,SAAS,GAAI,IAAI,CAAC,kBAAiC,CAAC,aAAa,CAAC;AACtE,QAAA,IAAI,OAAO,GAAI,IAAI,CAAC,gBAA+B,CAAC,aAAa,CAAC;AAClE,QAAA,IAAI,IAAI,GAAI,IAAI,CAAC,aAA4B,CAAC,aAAa,CAAC;AAC5D,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;AAEnD,QAAA,IAAI,eAAe,GAA6B,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAC9E,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAC1C,mBAAmB,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;QAE/F,IAAI,eAAe,CAAC,YAAY,CAAC,IAAI,MAAM,IAAI,mBAAmB,IAAI,CAAC,EAAE;YACrE,IAAI,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,EAAE;gBACzG,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC;AAC1D,aAAA;AAAM,iBAAA;AACH,gBAAA,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,eAAe,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC,eAAe,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;AAC1O,aAAA;AACJ,SAAA;KACJ;IAED,OAAO,GAAA;AACH,QAAA,IAAI,SAAS,GAAI,IAAI,CAAC,kBAAiC,CAAC,aAAa,CAAC;AACtE,QAAA,IAAI,OAAO,GAAI,IAAI,CAAC,gBAA+B,CAAC,aAAa,CAAC;;AAGlE,QAAA,IAAI,IAAI,GAAI,IAAI,CAAC,aAA4B,CAAC,aAAa,CAAC;AAC5D,QAAA,IAAI,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC;AACrC,QAAA,IAAI,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC;AACnC,QAAA,IAAI,MAAM,GAAG,CAAC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;AAE/D,QAAA,IAAI,CAAC,YAAY,GAAG,QAAQ,GAAG,UAAU,CAAC;;AAG1C,QAAA,IAAI,IAAI,GAAI,IAAI,CAAC,aAA4B,CAAC,aAAa,CAAC;AAC5D,QAAA,IAAI,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;AACvC,QAAA,IAAI,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC;AACrC,QAAA,IAAI,KAAK,GAAG,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;AAE5D,QAAA,IAAI,CAAC,YAAY,GAAG,SAAS,GAAG,WAAW,CAAC;AAE5C,QAAA,IAAI,CAAC,qBAAqB,CAAC,MAAK;AAC5B,YAAA,IAAK,IAAI,CAAC,YAAuB,IAAI,CAAC,EAAE;AACpC,gBAAA,IAAI,CAAC,YAAY,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC;AACvD,gBAAA,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;AACrD,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,YAAY,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;AACxD,gBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;AACrD,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAE,IAAI,CAAC,YAAuB,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;AACpE,gBAAA,MAAM,QAAQ,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC;AACpF,gBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,QAAQ,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,GAAG,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC;AACpG,aAAA;AAED,YAAA,IAAK,IAAI,CAAC,YAAuB,IAAI,CAAC,EAAE;AACpC,gBAAA,IAAI,CAAC,YAAY,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC;AACvD,gBAAA,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;AACrD,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,YAAY,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;AACxD,gBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;AACrD,gBAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAE,IAAI,CAAC,YAAuB,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC;AACrE,gBAAA,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,GAAG,GAAG,UAAU,CAAC,KAAK,WAAW,GAAG,SAAS,CAAC,CAAC;gBACrF,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,SAAS,GAAG,UAAU,GAAG,eAAe,GAAG,OAAO,GAAG,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC;AACvI,aAAA;AACL,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;QACV,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE;YACjD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC;AAC9C,YAAA,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;AACnC,SAAA;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE;YACtD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;AAC5C,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AACjC,SAAA;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;KAClB;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;AACX,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;YACjC,QAAQ,KAAK,CAAC,IAAI;gBACd,KAAK,WAAW,EAAE;oBACd,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBACtC,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM;AACT,iBAAA;gBAED,KAAK,SAAS,EAAE;AACZ,oBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC3C,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM;AACT,iBAAA;AAED,gBAAA,KAAK,WAAW,CAAC;gBAEjB,KAAK,YAAY,EAAE;oBACf,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM;AACT,iBAAA;AAED,gBAAA;;oBAEI,MAAM;AACb,aAAA;AACJ,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,EAAE;YAC1C,QAAQ,KAAK,CAAC,IAAI;gBACd,KAAK,YAAY,EAAE;oBACf,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvC,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM;AACT,iBAAA;gBAED,KAAK,WAAW,EAAE;AACd,oBAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC5C,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM;AACT,iBAAA;AAED,gBAAA,KAAK,WAAW,CAAC;gBAEjB,KAAK,SAAS,EAAE;oBACZ,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM;AACT,iBAAA;AAED,gBAAA;;oBAEI,MAAM;AACb,aAAA;AACJ,SAAA;KACJ;IAED,OAAO,GAAA;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB;IAED,MAAM,CAAC,GAAG,EAAE,IAAI,EAAA;QACZ,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;QACjD,IAAI,CAAC,OAAO,EAAE,CAAC;KAClB;IAED,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAA;QACd,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,MAAK;AACzB,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAC1B,EAAE,EAAE,CAAC,CAAC;KACV;IAED,UAAU,GAAA;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAA;KACJ;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;AACjC,YAAA,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC,KAAI;AACnC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAChC,aAAC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;AAC/E,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AAC/B,YAAA,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,KAAI;AACjC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,aAAC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC3E,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;AAC/E,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;AACzC,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACtE,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACvC,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,CAAa,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC;QAEzB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;QACpF,UAAU,CAAC,QAAQ,CAAE,IAAI,CAAC,aAA4B,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;QAE/F,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;QACtE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;QACjE,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,CAAC,CAAC,cAAc,EAAE,CAAC;KACtB;AAED,IAAA,eAAe,CAAC,CAAa,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC;QAEzB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QACrF,UAAU,CAAC,QAAQ,CAAE,IAAI,CAAC,aAA4B,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;QAE/F,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QACvE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;QAEjE,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,CAAC,CAAC,cAAc,EAAE,CAAC;KACtB;AAED,IAAA,mBAAmB,CAAC,CAAa,EAAA;QAC7B,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC9B,SAAA;aAAM,IAAI,IAAI,CAAC,aAAa,EAAE;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC9B,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC9B,SAAA;KACJ;AAED,IAAA,kBAAkB,CAAC,CAAa,EAAA;QAC5B,IAAI,MAAM,GAAG,CAAC,CAAC,KAAK,GAAI,IAAI,CAAC,SAAoB,CAAC;AAClD,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC;AAEzB,QAAA,IAAI,CAAC,qBAAqB,CAAC,MAAK;AAC3B,YAAA,IAAI,CAAC,gBAA+B,CAAC,aAAa,CAAC,UAAU,IAAI,MAAM,GAAI,IAAI,CAAC,YAAuB,CAAC;AAC7G,SAAC,CAAC,CAAC;KACN;AAED,IAAA,kBAAkB,CAAC,CAAa,EAAA;QAC5B,IAAI,MAAM,GAAG,CAAC,CAAC,KAAK,GAAI,IAAI,CAAC,SAAoB,CAAC;AAClD,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC;AAEzB,QAAA,IAAI,CAAC,qBAAqB,CAAC,MAAK;AAC3B,YAAA,IAAI,CAAC,gBAA+B,CAAC,aAAa,CAAC,SAAS,IAAI,MAAM,GAAI,IAAI,CAAC,YAAuB,CAAC;AAC5G,SAAC,CAAC,CAAC;KACN;AACD;;;;AAIG;AACH,IAAA,SAAS,CAAC,SAAiB,EAAA;AACvB,QAAA,IAAI,gBAAgB,GAAI,IAAI,CAAC,gBAA+B,CAAC,aAAa,CAAC,YAAY,GAAI,IAAI,CAAC,gBAA+B,CAAC,aAAa,CAAC,YAAY,CAAC;QAC3J,SAAS,GAAG,SAAS,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;QAC3F,IAAI,CAAC,gBAA+B,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;KAC7E;AAED,IAAA,OAAO,CAAC,KAAK,EAAA;AACT,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAC3D,YAAA,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;AACnC,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAClE,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AACjC,SAAA;KACJ;IAED,MAAM,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,YAAY,EAAE;AACnC,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AACjC,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,CAAQ,EAAA;QACtB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QACrF,UAAU,CAAC,WAAW,CAAE,IAAI,CAAC,aAA4B,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;QAClG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QACrF,UAAU,CAAC,WAAW,CAAE,IAAI,CAAC,aAA4B,CAAC,aAAa,EAAE,uBAAuB,CAAC,CAAC;QAClG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QACvE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;QAEpE,IAAI,CAAC,4BAA4B,EAAE,CAAC;AACpC,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;KAC9B;AAED,IAAA,qBAAqB,CAAC,CAAe,EAAA;QACjC,IAAI,KAAK,GAAG,MAAM,CAAC,qBAAqB,IAAI,IAAI,CAAC,YAAY,CAAC;QAC9D,KAAK,CAAC,CAAC,CAAC,CAAC;KACZ;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAClC,SAAA;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;KACJ;AACD;;;AAGG;IACH,OAAO,GAAA;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;KAClB;uGA5aQ,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAqEA,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAA6G,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AArE3I,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAeA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,CAUlB,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAvEpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,upBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,WAAW,EAAA,UAAA,EAAA,CAAA;kBAhDvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,upBAAA,CAAA,EAAA,CAAA;;0BAuEY,MAAM;2BAAC,WAAW,CAAA;;0BAAsG,MAAM;2BAAC,QAAQ,CAAA;iEAhE3I,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKiC,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEb,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEA,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAED,aAAa,EAAA,CAAA;sBAA/B,SAAS;uBAAC,MAAM,CAAA;gBAEE,aAAa,EAAA,CAAA;sBAA/B,SAAS;uBAAC,MAAM,CAAA;gBAEe,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA2ZrB,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EApbjB,YAAA,EAAA,CAAA,WAAW,CAgbV,EAAA,OAAA,EAAA,CAAA,YAAY,aAhbb,WAAW,CAAA,EAAA,CAAA,CAAA;AAobX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAJhB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,WAAW,CAAC;oBACtB,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;AClgBD;;AAEG;;;;"}