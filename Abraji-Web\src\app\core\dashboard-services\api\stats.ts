import { Observable } from "rxjs";
// stats.ts
export interface Statistics {
  users_count: number;
  active_users: number;
  online_users: number;
  expired_users: number;
  users_expire_in_3_days: number;
  balance: string;
  expiring_today: number;
  fup_users: number;
}

export interface CardStats {
  id: number;
  profile: string;
  total: number;
  used: number;
  remaining: number;
}

export interface CardsResponse {
  cards: CardStats[];
  status: number;
}

export interface TransactionStats {
  total_transactions: number;
  total_amount: number;
}

export interface TransactionsResponse {
  maintenance: TransactionStats;
  expense: TransactionStats;
  general: TransactionStats;
}

export abstract class StatsData {
  abstract getStats(): Observable<Statistics>;
  abstract getCards(): Observable<CardsResponse>;
  abstract getTransactions(): Observable<TransactionsResponse>;
}
