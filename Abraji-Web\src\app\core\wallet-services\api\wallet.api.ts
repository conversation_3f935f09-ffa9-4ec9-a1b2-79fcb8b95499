import { inject, Injectable } from '@angular/core';
import { HttpService } from '../../common-services/services/http.service';

@Injectable({
  providedIn: 'root',
})
export class WalletApi {
  private apiUrl = 'wallet';
  private httpService = inject(HttpService);

  getBalance() {
    return this.httpService.get(this.apiUrl + '/balance', {});
  }

  getWalletTransactions(payload: any) {
    return this.httpService.post(this.apiUrl + `/transactions`, payload);
  }

  createWallet(payload: string) {
    throw new Error('Method not implemented.');
  }

  editWallet(amount: number) {
    const url = this.apiUrl + `/update-balance`;
    return this.httpService.put(url, { amount });
  }

  deposit(amount: number) {
    const url = this.apiUrl + `/add-money`;
    return this.httpService.post(url, { amount });
  }

  withdraw(amount: number): any {
    throw new Error('Method not implemented.');
  }

  resetBalance(): any {
    const url = this.apiUrl + `/reset-balance`;
    return this.httpService.get(url, {});
  }
}
