{"version": 3, "file": "primeng-fieldset.mjs", "sources": ["../../src/app/components/fieldset/fieldset.ts", "../../src/app/components/fieldset/primeng-fieldset.ts"], "sourcesContent": ["import { animate, state, style, transition, trigger } from '@angular/animations';\nimport { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, ElementRef, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewEncapsulation, booleanAttribute } from '@angular/core';\nimport { BlockableUI, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { MinusIcon } from 'primeng/icons/minus';\nimport { PlusIcon } from 'primeng/icons/plus';\nimport { RippleModule } from 'primeng/ripple';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { FieldsetAfterToggleEvent, FieldsetBeforeToggleEvent } from './fieldset.interface';\n\n/**\n * Fieldset is a grouping component with the optional content toggle feature.\n * @group Components\n */\n@Component({\n    selector: 'p-fieldset',\n    template: `\n        <fieldset\n            [attr.id]=\"id\"\n            [ngClass]=\"{ 'p-fieldset p-component': true, 'p-fieldset-toggleable': toggleable, 'p-fieldset-expanded': !collapsed && toggleable }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'fieldset'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <legend class=\"p-fieldset-legend\" [attr.data-pc-section]=\"'legend'\">\n                <ng-container *ngIf=\"toggleable; else legendContent\">\n                    <a [attr.id]=\"id + '_header'\" pRipple tabindex=\"0\" role=\"button\" [attr.aria-controls]=\"id + '_content'\" [attr.aria-expanded]=\"!collapsed\" [attr.aria-label]=\"buttonAriaLabel\" (click)=\"toggle($event)\" (keydown)=\"onKeyDown($event)\">\n                        <ng-container *ngIf=\"collapsed\">\n                            <PlusIcon *ngIf=\"!expandIconTemplate\" [styleClass]=\"'p-fieldset-toggler'\" [attr.data-pc-section]=\"'togglericon'\" />\n                            <span *ngIf=\"expandIconTemplate\" class=\"p-fieldset-toggler\" [attr.data-pc-section]=\"'togglericon'\">\n                                <ng-container *ngTemplateOutlet=\"expandIconTemplate\"></ng-container>\n                            </span>\n                        </ng-container>\n                        <ng-container *ngIf=\"!collapsed\">\n                            <MinusIcon *ngIf=\"!collapseIconTemplate\" [styleClass]=\"'p-fieldset-toggler'\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'togglericon'\" />\n                            <span *ngIf=\"collapseIconTemplate\" class=\"p-fieldset-toggler\" [attr.data-pc-section]=\"'togglericon'\">\n                                <ng-container *ngTemplateOutlet=\"collapseIconTemplate\"></ng-container>\n                            </span>\n                        </ng-container>\n                        <ng-container *ngTemplateOutlet=\"legendContent\"></ng-container>\n                    </a>\n                </ng-container>\n                <ng-template #legendContent>\n                    <span class=\"p-fieldset-legend-text\" [attr.data-pc-section]=\"'legendtitle'\">{{ legend }}</span>\n                    <ng-content select=\"p-header\"></ng-content>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                </ng-template>\n            </legend>\n            <div\n                [attr.id]=\"id + '_content'\"\n                role=\"region\"\n                class=\"p-toggleable-content\"\n                [@fieldsetContent]=\"collapsed ? { value: 'hidden', params: { transitionParams: transitionOptions, height: '0' } } : { value: 'visible', params: { transitionParams: animating ? transitionOptions : '0ms', height: '*' } }\"\n                [attr.aria-labelledby]=\"id + '_header'\"\n                [attr.aria-hidden]=\"collapsed\"\n                [attr.data-pc-section]=\"'toggleablecontent'\"\n                (@fieldsetContent.done)=\"onToggleDone()\"\n            >\n                <div class=\"p-fieldset-content\" [attr.data-pc-section]=\"'content'\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n            </div>\n        </fieldset>\n    `,\n    animations: [\n        trigger('fieldsetContent', [\n            state(\n                'hidden',\n                style({\n                    height: '0'\n                })\n            ),\n            state(\n                'visible',\n                style({\n                    height: '*'\n                })\n            ),\n            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./fieldset.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Fieldset implements AfterContentInit, BlockableUI {\n    /**\n     * Header text of the fieldset.\n     * @group Props\n     */\n    @Input() legend: string | undefined;\n    /**\n     * When specified, content can toggled by clicking the legend.\n     * @group Props\n     * @defaultValue false\n     */\n    @Input({ transform: booleanAttribute }) toggleable: boolean | undefined;\n    /**\n     * Defines the default visibility state of the content.\n     * * @group Props\n     */\n    @Input({ transform: booleanAttribute }) collapsed: boolean | undefined = false;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Transition options of the panel animation.\n     * @group Props\n     */\n    @Input() transitionOptions: string = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Emits when the collapsed state changes.\n     * @param {boolean} value - New value.\n     * @group Emits\n     */\n    @Output() collapsedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n    /**\n     * Callback to invoke before panel toggle.\n     * @param {PanelBeforeToggleEvent} event - Custom toggle event\n     * @group Emits\n     */\n    @Output() onBeforeToggle: EventEmitter<FieldsetBeforeToggleEvent> = new EventEmitter<FieldsetBeforeToggleEvent>();\n    /**\n     * Callback to invoke after panel toggle.\n     * @param {PanelAfterToggleEvent} event - Custom toggle event\n     * @group Emits\n     */\n    @Output() onAfterToggle: EventEmitter<FieldsetAfterToggleEvent> = new EventEmitter<FieldsetAfterToggleEvent>();\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    get id() {\n        return UniqueComponentId();\n    }\n\n    get buttonAriaLabel() {\n        return this.legend;\n    }\n\n    public animating: Nullable<boolean>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    contentTemplate: Nullable<TemplateRef<any>>;\n\n    collapseIconTemplate: Nullable<TemplateRef<any>>;\n\n    expandIconTemplate: Nullable<TemplateRef<any>>;\n\n    constructor(private el: ElementRef) {}\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'expandicon':\n                    this.expandIconTemplate = item.template;\n                    break;\n\n                case 'collapseicon':\n                    this.collapseIconTemplate = item.template;\n                    break;\n\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    toggle(event: MouseEvent) {\n        if (this.animating) {\n            return false;\n        }\n\n        this.animating = true;\n        this.onBeforeToggle.emit({ originalEvent: event, collapsed: this.collapsed });\n\n        if (this.collapsed) this.expand();\n        else this.collapse();\n\n        this.onAfterToggle.emit({ originalEvent: event, collapsed: this.collapsed });\n        event.preventDefault();\n    }\n\n    onKeyDown(event) {\n        if (event.code === 'Enter' || event.code === 'Space') {\n            this.toggle(event);\n            event.preventDefault();\n        }\n    }\n\n    expand() {\n        this.collapsed = false;\n        this.collapsedChange.emit(this.collapsed);\n    }\n\n    collapse() {\n        this.collapsed = true;\n        this.collapsedChange.emit(this.collapsed);\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    onToggleDone() {\n        this.animating = false;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RippleModule, MinusIcon, PlusIcon],\n    exports: [Fieldset, SharedModule],\n    declarations: [Fieldset]\n})\nexport class FieldsetModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;AAWA;;;AAGG;MA8EU,QAAQ,CAAA;AAuEG,IAAA,EAAA,CAAA;AAtEpB;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;;AAIG;AACqC,IAAA,UAAU,CAAsB;AACxE;;;AAGG;IACqC,SAAS,GAAwB,KAAK,CAAC;AAC/E;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACM,iBAAiB,GAAW,sCAAsC,CAAC;AAC5E;;;;AAIG;AACO,IAAA,eAAe,GAA0B,IAAI,YAAY,EAAW,CAAC;AAC/E;;;;AAIG;AACO,IAAA,cAAc,GAA4C,IAAI,YAAY,EAA6B,CAAC;AAClH;;;;AAIG;AACO,IAAA,aAAa,GAA2C,IAAI,YAAY,EAA4B,CAAC;AAE/E,IAAA,SAAS,CAA4B;AAErE,IAAA,IAAI,EAAE,GAAA;QACF,OAAO,iBAAiB,EAAE,CAAC;KAC9B;AAED,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AAEM,IAAA,SAAS,CAAoB;AAEpC,IAAA,cAAc,CAA6B;AAE3C,IAAA,eAAe,CAA6B;AAE5C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,WAAA,CAAoB,EAAc,EAAA;QAAd,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;IAEtC,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,MAAM,CAAC,KAAiB,EAAA;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAE9E,IAAI,IAAI,CAAC,SAAS;YAAE,IAAI,CAAC,MAAM,EAAE,CAAC;;YAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;AAErB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7E,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;AAClD,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC7C;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC7C;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,YAAY,GAAA;AACR,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KAC1B;uGArIQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,+FAWG,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAmCnB,aAAa,EA9HpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiDT,EAmKqC,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,qeAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,SAAS,CAAE,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,QAAQ,CAlK7C,EAAA,QAAA,EAAA,UAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,iBAAiB,EAAE;AACvB,gBAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACd,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACd,iBAAA,CAAC,CACL;gBACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACnE,gBAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;aACtC,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,QAAQ,EAAA,UAAA,EAAA,CAAA;kBA7EpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiDT,EACW,UAAA,EAAA;wBACR,OAAO,CAAC,iBAAiB,EAAE;AACvB,4BAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACd,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACd,6BAAA,CAAC,CACL;4BACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACnE,4BAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;yBACtC,CAAC;AACL,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,qeAAA,CAAA,EAAA,CAAA;+EAOQ,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAMkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAMI,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA0FrB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EA7Id,YAAA,EAAA,CAAA,QAAQ,CAyIP,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,CAzIhD,EAAA,OAAA,EAAA,CAAA,QAAQ,EA0IG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGvB,cAAc,EAAA,OAAA,EAAA,CAJb,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EACrC,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGvB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC;AAC1D,oBAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;oBACjC,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;ACxOD;;AAEG;;;;"}