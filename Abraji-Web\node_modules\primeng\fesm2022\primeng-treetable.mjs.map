{"version": 3, "file": "primeng-treetable.mjs", "sources": ["../../src/app/components/treetable/treetable.ts", "../../src/app/components/treetable/primeng-treetable.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    Directive,\n    ElementRef,\n    EventEmitter,\n    HostListener,\n    Inject,\n    Injectable,\n    Input,\n    NgModule,\n    NgZone,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { BlockableUI, FilterMetadata, FilterService, PrimeNGConfig, PrimeTemplate, ScrollerOptions, SharedModule, SortMeta, TreeNode, TreeTableNode } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ArrowDownIcon } from 'primeng/icons/arrowdown';\nimport { ArrowUpIcon } from 'primeng/icons/arrowup';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { MinusIcon } from 'primeng/icons/minus';\nimport { SortAltIcon } from 'primeng/icons/sortalt';\nimport { SortAmountDownIcon } from 'primeng/icons/sortamountdown';\nimport { SortAmountUpAltIcon } from 'primeng/icons/sortamountupalt';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { RippleModule } from 'primeng/ripple';\nimport { Scroller, ScrollerModule } from 'primeng/scroller';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { ObjectUtils } from 'primeng/utils';\nimport { Subject, Subscription } from 'rxjs';\nimport {\n    TreeTableColResizeEvent,\n    TreeTableColumnReorderEvent,\n    TreeTableContextMenuSelectEvent,\n    TreeTableEditEvent,\n    TreeTableFilterEvent,\n    TreeTableFilterOptions,\n    TreeTableHeaderCheckboxToggleEvent,\n    TreeTableLazyLoadEvent,\n    TreeTableNodeCollapseEvent,\n    TreeTableNodeExpandEvent,\n    TreeTableNodeUnSelectEvent,\n    TreeTablePaginatorState,\n    TreeTableSortEvent\n} from './treetable.interface';\n\n@Injectable()\nexport class TreeTableService {\n    private sortSource = new Subject<SortMeta | SortMeta[] | null>();\n    private selectionSource = new Subject();\n    private contextMenuSource = new Subject<any>();\n    private uiUpdateSource = new Subject<any>();\n    private totalRecordsSource = new Subject<any>();\n\n    sortSource$ = this.sortSource.asObservable();\n    selectionSource$ = this.selectionSource.asObservable();\n    contextMenuSource$ = this.contextMenuSource.asObservable();\n    uiUpdateSource$ = this.uiUpdateSource.asObservable();\n    totalRecordsSource$ = this.totalRecordsSource.asObservable();\n\n    onSort(sortMeta: SortMeta | SortMeta[] | null) {\n        this.sortSource.next(sortMeta);\n    }\n\n    onSelectionChange() {\n        this.selectionSource.next(null);\n    }\n\n    onContextMenu(node: any) {\n        this.contextMenuSource.next(node);\n    }\n\n    onUIUpdate(value: any) {\n        this.uiUpdateSource.next(value);\n    }\n\n    onTotalRecordsChange(value: number) {\n        this.totalRecordsSource.next(value);\n    }\n}\n/**\n * TreeTable is used to display hierarchical data in tabular format.\n * @group Components\n */\n@Component({\n    selector: 'p-treeTable',\n    template: `\n        <div\n            #container\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            data-scrollselectors=\".p-treetable-scrollable-body\"\n            [ngClass]=\"{\n                'p-treetable p-component': true,\n                'p-treetable-hoverable-rows': rowHover || selectionMode === 'single' || selectionMode === 'multiple',\n                'p-treetable-auto-layout': autoLayout,\n                'p-treetable-resizable': resizableColumns,\n                'p-treetable-resizable-fit': resizableColumns && columnResizeMode === 'fit',\n                'p-treetable-flex-scrollable': scrollable && scrollHeight === 'flex'\n            }\"\n        >\n            <div class=\"p-treetable-loading\" *ngIf=\"loading && showLoader\">\n                <div class=\"p-treetable-loading-overlay p-component-overlay\">\n                    <i *ngIf=\"loadingIcon\" [class]=\"'p-treetable-loading-icon pi-spin ' + loadingIcon\"></i>\n                    <ng-container *ngIf=\"!loadingIcon\">\n                        <SpinnerIcon *ngIf=\"!loadingIconTemplate\" [spin]=\"true\" [styleClass]=\"'p-treetable-loading-icon'\" />\n                        <span *ngIf=\"loadingIconTemplate\" class=\"p-treetable-loading-icon\">\n                            <ng-template *ngTemplateOutlet=\"loadingIconTemplate\"></ng-template>\n                        </span>\n                    </ng-container>\n                </div>\n            </div>\n            <div *ngIf=\"captionTemplate\" class=\"p-treetable-header\">\n                <ng-container *ngTemplateOutlet=\"captionTemplate\"></ng-container>\n            </div>\n            <p-paginator\n                [rows]=\"rows\"\n                [first]=\"first\"\n                [totalRecords]=\"totalRecords\"\n                [pageLinkSize]=\"pageLinks\"\n                styleClass=\"p-paginator-top\"\n                [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"onPageChange($event)\"\n                [rowsPerPageOptions]=\"rowsPerPageOptions\"\n                *ngIf=\"paginator && (paginatorPosition === 'top' || paginatorPosition == 'both')\"\n                [templateLeft]=\"paginatorLeftTemplate\"\n                [templateRight]=\"paginatorRightTemplate\"\n                [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\"\n                [showFirstLastIcon]=\"showFirstLastIcon\"\n                [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\"\n                [showCurrentPageReport]=\"showCurrentPageReport\"\n                [showJumpToPageDropdown]=\"showJumpToPageDropdown\"\n                [showPageLinks]=\"showPageLinks\"\n                [styleClass]=\"paginatorStyleClass\"\n                [locale]=\"paginatorLocale\"\n            >\n                <ng-template pTemplate=\"firstpagelinkicon\" *ngIf=\"paginatorFirstPageLinkIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"paginatorFirstPageLinkIconTemplate\"></ng-container>\n                </ng-template>\n\n                <ng-template pTemplate=\"previouspagelinkicon\" *ngIf=\"paginatorPreviousPageLinkIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"paginatorPreviousPageLinkIconTemplate\"></ng-container>\n                </ng-template>\n\n                <ng-template pTemplate=\"lastpagelinkicon\" *ngIf=\"paginatorLastPageLinkIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"paginatorLastPageLinkIconTemplate\"></ng-container>\n                </ng-template>\n\n                <ng-template pTemplate=\"nextpagelinkicon\" *ngIf=\"paginatorNextPageLinkIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"paginatorNextPageLinkIconTemplate\"></ng-container>\n                </ng-template>\n            </p-paginator>\n\n            <div class=\"p-treetable-wrapper\" *ngIf=\"!scrollable\">\n                <table role=\"table\" #table [ngClass]=\"tableStyleClass\" [ngStyle]=\"tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"colGroupTemplate; context: { $implicit: columns }\"></ng-container>\n                    <thead role=\"rowgroup\" class=\"p-treetable-thead\">\n                        <ng-container *ngTemplateOutlet=\"headerTemplate; context: { $implicit: columns }\"></ng-container>\n                    </thead>\n                    <tbody class=\"p-treetable-tbody\" role=\"rowgroup\" [pTreeTableBody]=\"columns\" [pTreeTableBodyTemplate]=\"bodyTemplate\"></tbody>\n                    <tfoot class=\"p-treetable-tfoot\" role=\"rowgroup\">\n                        <ng-container *ngTemplateOutlet=\"footerTemplate; context: { $implicit: columns }\"></ng-container>\n                    </tfoot>\n                </table>\n            </div>\n\n            <div class=\"p-treetable-scrollable-wrapper\" *ngIf=\"scrollable\">\n                <div\n                    class=\"p-treetable-scrollable-view p-treetable-frozen-view\"\n                    *ngIf=\"frozenColumns || frozenBodyTemplate\"\n                    #scrollableFrozenView\n                    [ttScrollableView]=\"frozenColumns\"\n                    [frozen]=\"true\"\n                    [ngStyle]=\"{ width: frozenWidth }\"\n                    [scrollHeight]=\"scrollHeight\"\n                ></div>\n                <div class=\"p-treetable-scrollable-view\" #scrollableView [ttScrollableView]=\"columns\" [frozen]=\"false\" [scrollHeight]=\"scrollHeight\" [ngStyle]=\"{ left: frozenWidth, width: 'calc(100% - ' + frozenWidth + ')' }\"></div>\n            </div>\n\n            <p-paginator\n                [rows]=\"rows\"\n                [first]=\"first\"\n                [totalRecords]=\"totalRecords\"\n                [pageLinkSize]=\"pageLinks\"\n                styleClass=\"p-paginator-bottom\"\n                [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"onPageChange($event)\"\n                [rowsPerPageOptions]=\"rowsPerPageOptions\"\n                *ngIf=\"paginator && (paginatorPosition === 'bottom' || paginatorPosition == 'both')\"\n                [templateLeft]=\"paginatorLeftTemplate\"\n                [templateRight]=\"paginatorRightTemplate\"\n                [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\"\n                [showFirstLastIcon]=\"showFirstLastIcon\"\n                [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\"\n                [showCurrentPageReport]=\"showCurrentPageReport\"\n                [showJumpToPageDropdown]=\"showJumpToPageDropdown\"\n                [showPageLinks]=\"showPageLinks\"\n                [styleClass]=\"paginatorStyleClass\"\n                [locale]=\"paginatorLocale\"\n            >\n                <ng-template pTemplate=\"firstpagelinkicon\" *ngIf=\"paginatorFirstPageLinkIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"paginatorFirstPageLinkIconTemplate\"></ng-container>\n                </ng-template>\n\n                <ng-template pTemplate=\"previouspagelinkicon\" *ngIf=\"paginatorPreviousPageLinkIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"paginatorPreviousPageLinkIconTemplate\"></ng-container>\n                </ng-template>\n\n                <ng-template pTemplate=\"lastpagelinkicon\" *ngIf=\"paginatorLastPageLinkIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"paginatorLastPageLinkIconTemplate\"></ng-container>\n                </ng-template>\n\n                <ng-template pTemplate=\"nextpagelinkicon\" *ngIf=\"paginatorNextPageLinkIconTemplate\">\n                    <ng-container *ngTemplateOutlet=\"paginatorNextPageLinkIconTemplate\"></ng-container>\n                </ng-template>\n            </p-paginator>\n            <div *ngIf=\"summaryTemplate\" class=\"p-treetable-footer\">\n                <ng-container *ngTemplateOutlet=\"summaryTemplate\"></ng-container>\n            </div>\n\n            <div #resizeHelper class=\"p-column-resizer-helper\" [ngStyle]=\"{ display: 'none' }\" *ngIf=\"resizableColumns\"></div>\n            <span #reorderIndicatorUp class=\"p-treetable-reorder-indicator-up\" [ngStyle]=\"{ display: 'none' }\" *ngIf=\"reorderableColumns\">\n                <ArrowDownIcon *ngIf=\"!reorderIndicatorUpIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"reorderIndicatorUpIconTemplate\"></ng-template>\n            </span>\n            <span #reorderIndicatorDown class=\"p-treetable-reorder-indicator-down\" [ngStyle]=\"{ display: 'none' }\" *ngIf=\"reorderableColumns\">\n                <ArrowUpIcon *ngIf=\"!reorderIndicatorDownIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"reorderIndicatorDownIconTemplate\"></ng-template>\n            </span>\n        </div>\n    `,\n    providers: [TreeTableService],\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./treetable.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TreeTable implements AfterContentInit, OnInit, OnDestroy, BlockableUI, OnChanges {\n    /**\n     * An array of objects to represent dynamic columns.\n     * @group Props\n     */\n    @Input() columns: any[] | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the table.\n     * @group Props\n     */\n    @Input() tableStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the table.\n     * @group Props\n     */\n    @Input() tableStyleClass: string | undefined;\n    /**\n     * Whether the cell widths scale according to their content or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoLayout: boolean | undefined;\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazy: boolean = false;\n    /**\n     * Whether to call lazy loading on initialization.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazyLoadOnInit: boolean = true;\n    /**\n     * When specified as true, enables the pagination.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) paginator: boolean | undefined;\n    /**\n     * Number of rows to display per page.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) rows: number | undefined;\n    /**\n     * Index of the first row to be displayed.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) first: number = 0;\n    /**\n     * Number of page links to display in paginator.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) pageLinks: number = 5;\n    /**\n     * Array of integer/object values to display inside rows per page dropdown of paginator\n     * @group Props\n     */\n    @Input() rowsPerPageOptions: any[] | undefined;\n    /**\n     * Whether to show it even there is only one page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) alwaysShowPaginator: boolean = true;\n    /**\n     * Position of the paginator.\n     * @group Props\n     */\n    @Input() paginatorPosition: 'top' | 'bottom' | 'both' = 'bottom';\n    /**\n     * Custom style class for paginator\n     * @group Props\n     */\n    @Input() paginatorStyleClass: string | undefined;\n    /**\n     * Target element to attach the paginator dropdown overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() paginatorDropdownAppendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Template of the current page report element. Available placeholders are {currentPage},{totalPages},{rows},{first},{last} and {totalRecords}\n     * @group Props\n     */\n    @Input() currentPageReportTemplate: string = '{currentPage} of {totalPages}';\n    /**\n     * Whether to display current page report.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showCurrentPageReport: boolean | undefined;\n    /**\n     * Whether to display a dropdown to navigate to any page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showJumpToPageDropdown: boolean | undefined;\n    /**\n     * When enabled, icons are displayed on paginator to go first and last page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showFirstLastIcon: boolean = true;\n    /**\n     * Whether to show page links.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showPageLinks: boolean = true;\n    /**\n     * Sort order to use when an unsorted column gets sorted by user interaction.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) defaultSortOrder: number = 1;\n    /**\n     * Defines whether sorting works on single column or on multiple columns.\n     * @group Props\n     */\n    @Input() sortMode: 'single' | 'multiple' = 'single';\n    /**\n     * When true, resets paginator to first page after sorting.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) resetPageOnSort: boolean = true;\n    /**\n     * Whether to use the default sorting or a custom one using sortFunction.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) customSort: boolean | undefined;\n    /**\n     * Specifies the selection mode, valid values are \"single\" and \"multiple\".\n     * @group Props\n     */\n    @Input() selectionMode: string | undefined;\n    /**\n     * Selected row with a context menu.\n     * @group Props\n     */\n    @Input() contextMenuSelection: any;\n    /**\n     * Mode of the contet menu selection.\n     * @group Props\n     */\n    @Input() contextMenuSelectionMode: string = 'separate';\n    /**\n     * A property to uniquely identify a record in data.\n     * @group Props\n     */\n    @Input() dataKey: string | undefined;\n    /**\n     * Defines whether metaKey is should be considered for the selection. On touch enabled devices, metaKeySelection is turned off automatically.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) metaKeySelection: boolean | undefined = false;\n    /**\n     * Algorithm to define if a row is selected, valid values are \"equals\" that compares by reference and \"deepEquals\" that compares all fields.\n     * @group Props\n     */\n    @Input() compareSelectionBy: string = 'deepEquals';\n    /**\n     * Adds hover effect to rows without the need for selectionMode.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) rowHover: boolean | undefined;\n    /**\n     * Displays a loader to indicate data load is in progress.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) loading: boolean | undefined;\n    /**\n     * The icon to show while indicating data load is in progress.\n     * @group Props\n     */\n    @Input() loadingIcon: string | undefined;\n    /**\n     * Whether to show the loading mask when loading property is true.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showLoader: boolean = true;\n    /**\n     * When specifies, enables horizontal and/or vertical scrolling.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) scrollable: boolean | undefined;\n    /**\n     * Height of the scroll viewport in fixed pixels or the \"flex\" keyword for a dynamic size.\n     * @group Props\n     */\n    @Input() scrollHeight: string | undefined;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) virtualScroll: boolean | undefined;\n    /**\n     * Height of a row to use in calculations of virtual scrolling.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) virtualScrollItemSize: number | undefined;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() virtualScrollOptions: ScrollerOptions | undefined;\n    /**\n     * The delay (in milliseconds) before triggering the virtual scroll. This determines the time gap between the user's scroll action and the actual rendering of the next set of items in the virtual scroll.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) virtualScrollDelay: number = 150;\n    /**\n     * Width of the frozen columns container.\n     * @group Props\n     */\n    @Input() frozenWidth: string | undefined;\n    /**\n     * An array of objects to represent dynamic columns that are frozen.\n     * @group Props\n     */\n    @Input() frozenColumns: { [klass: string]: any } | null | undefined;\n    /**\n     * When enabled, columns can be resized using drag and drop.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) resizableColumns: boolean | undefined;\n    /**\n     * Defines whether the overall table width should change on column resize, valid values are \"fit\" and \"expand\".\n     * @group Props\n     */\n    @Input() columnResizeMode: string = 'fit';\n    /**\n     * When enabled, columns can be reordered using drag and drop.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) reorderableColumns: boolean | undefined;\n    /**\n     * Local ng-template varilable of a ContextMenu.\n     * @group Props\n     */\n    @Input() contextMenu: any;\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity.\n     * @group Props\n     */\n    @Input() rowTrackBy: Function = (index: number, item: any) => item;\n    /**\n     * An array of FilterMetadata objects to provide external filters.\n     * @group Props\n     */\n    @Input() filters: { [s: string]: FilterMetadata | undefined } = {};\n    /**\n     * An array of fields as string to use in global filtering.\n     * @group Props\n     */\n    @Input() globalFilterFields: string[] | undefined;\n    /**\n     * Delay in milliseconds before filtering the data.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) filterDelay: number = 300;\n    /**\n     * Mode for filtering valid values are \"lenient\" and \"strict\". Default is lenient.\n     * @group Props\n     */\n    @Input() filterMode: string = 'lenient';\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() filterLocale: string | undefined;\n    /**\n     * Locale to be used in paginator formatting.\n     * @group Props\n     */\n    @Input() paginatorLocale: string | undefined;\n    /**\n     * Number of total records, defaults to length of value when not defined.\n     * @group Props\n     */\n    @Input() get totalRecords(): number {\n        return this._totalRecords;\n    }\n    set totalRecords(val: number) {\n        this._totalRecords = val;\n        this.tableService.onTotalRecordsChange(this._totalRecords);\n    }\n    /**\n     * Name of the field to sort data by default.\n     * @group Props\n     */\n    @Input() get sortField(): string | undefined | null {\n        return this._sortField;\n    }\n    set sortField(val: string | undefined | null) {\n        this._sortField = val;\n    }\n    /**\n     * Order to sort when default sorting is enabled.\n     * @defaultValue 1\n     * @group Props\n     */\n    @Input() get sortOrder(): number {\n        return this._sortOrder;\n    }\n    set sortOrder(val: number) {\n        this._sortOrder = val;\n    }\n    /**\n     * An array of SortMeta objects to sort the data by default in multiple sort mode.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get multiSortMeta(): SortMeta[] | undefined | null {\n        return this._multiSortMeta;\n    }\n    set multiSortMeta(val: SortMeta[] | undefined | null) {\n        this._multiSortMeta = val;\n    }\n    /**\n     * Selected row in single mode or an array of values in multiple mode.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get selection(): any {\n        return this._selection;\n    }\n    set selection(val: any) {\n        this._selection = val;\n    }\n    /**\n     * An array of objects to display.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get value(): TreeNode<any>[] | undefined {\n        return this._value;\n    }\n    set value(val: TreeNode<any>[] | undefined) {\n        this._value = val;\n    }\n    /**\n     * Indicates the height of rows to be scrolled.\n     * @defaultValue 28\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    @Input() get virtualRowHeight(): number {\n        return this._virtualRowHeight;\n    }\n    set virtualRowHeight(val: number) {\n        this._virtualRowHeight = val;\n        console.warn('The virtualRowHeight property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    /**\n     * A map of keys to control the selection state.\n     * @group Props\n     */\n    @Input() get selectionKeys(): any {\n        return this._selectionKeys;\n    }\n    set selectionKeys(value: any) {\n        this._selectionKeys = value;\n        this.selectionKeysChange.emit(this._selectionKeys);\n    }\n    /**\n     * Callback to invoke on selected node change.\n     * @param {TreeTableNode} object - Node instance.\n     * @group Emits\n     */\n    @Output() selectionChange: EventEmitter<TreeTableNode<any> | TreeTableNode<any>[] | null> = new EventEmitter<TreeTableNode<any> | TreeTableNode<any>[] | null>();\n    /**\n     * Callback to invoke on context menu selection change.\n     * @param {TreeTableNode} object - Node instance.\n     * @group Emits\n     */\n    @Output() contextMenuSelectionChange: EventEmitter<TreeTableNode> = new EventEmitter<TreeTableNode>();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {TreeTableFilterEvent} event - Custom filter event.\n     * @group Emits\n     */\n    @Output() onFilter: EventEmitter<TreeTableFilterEvent> = new EventEmitter<TreeTableFilterEvent>();\n    /**\n     * Callback to invoke when a node is expanded.\n     * @param {TreeTableNodeExpandEvent} event - Node expand event.\n     * @group Emits\n     */\n    @Output() onNodeExpand: EventEmitter<TreeTableNodeExpandEvent> = new EventEmitter<TreeTableNodeExpandEvent>();\n    /**\n     * Callback to invoke when a node is collapsed.\n     * @param {TreeTableNodeCollapseEvent} event - Node collapse event.\n     * @group Emits\n     */\n    @Output() onNodeCollapse: EventEmitter<TreeTableNodeCollapseEvent> = new EventEmitter<TreeTableNodeCollapseEvent>();\n    /**\n     * Callback to invoke when pagination occurs.\n     * @param {TreeTablePaginatorState} object - Paginator state.\n     * @group Emits\n     */\n    @Output() onPage: EventEmitter<TreeTablePaginatorState> = new EventEmitter<TreeTablePaginatorState>();\n    /**\n     * Callback to invoke when a column gets sorted.\n     * @param {Object} Object - Sort data.\n     * @group Emits\n     */\n    @Output() onSort: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when paging, sorting or filtering happens in lazy mode.\n     * @param {TreeTableLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    @Output() onLazyLoad: EventEmitter<TreeTableLazyLoadEvent> = new EventEmitter<TreeTableLazyLoadEvent>();\n    /**\n     * An event emitter to invoke on custom sorting, refer to sorting section for details.\n     * @param {TreeTableSortEvent} event - Custom sort event.\n     * @group Emits\n     */\n    @Output() sortFunction: EventEmitter<TreeTableSortEvent> = new EventEmitter<TreeTableSortEvent>();\n    /**\n     * Callback to invoke when a column is resized.\n     * @param {TreeTableColResizeEvent} event - Custom column resize event.\n     * @group Emits\n     */\n    @Output() onColResize: EventEmitter<TreeTableColResizeEvent> = new EventEmitter<TreeTableColResizeEvent>();\n    /**\n     * Callback to invoke when a column is reordered.\n     * @param {TreeTableColumnReorderEvent} event - Custom column reorder.\n     * @group Emits\n     */\n    @Output() onColReorder: EventEmitter<TreeTableColumnReorderEvent> = new EventEmitter<TreeTableColumnReorderEvent>();\n    /**\n     * Callback to invoke when a node is selected.\n     * @param {TreeTableNode} object - Node instance.\n     * @group Emits\n     */\n    @Output() onNodeSelect: EventEmitter<TreeTableNode> = new EventEmitter<TreeTableNode>();\n    /**\n     * Callback to invoke when a node is unselected.\n     * @param {TreeTableNodeUnSelectEvent} event - Custom node unselect event.\n     * @group Emits\n     */\n    @Output() onNodeUnselect: EventEmitter<TreeTableNodeUnSelectEvent> = new EventEmitter<TreeTableNodeUnSelectEvent>();\n    /**\n     * Callback to invoke when a node is selected with right click.\n     * @param {TreeTableContextMenuSelectEvent} event - Custom context menu select event.\n     * @group Emits\n     */\n    @Output() onContextMenuSelect: EventEmitter<TreeTableContextMenuSelectEvent> = new EventEmitter<TreeTableContextMenuSelectEvent>();\n    /**\n     * Callback to invoke when state of header checkbox changes.\n     * @param {TreeTableHeaderCheckboxToggleEvent} event - Custom checkbox toggle event.\n     * @group Emits\n     */\n    @Output() onHeaderCheckboxToggle: EventEmitter<TreeTableHeaderCheckboxToggleEvent> = new EventEmitter<TreeTableHeaderCheckboxToggleEvent>();\n    /**\n     * Callback to invoke when a cell switches to edit mode.\n     * @param {TreeTableEditEvent} event - Custom edit event.\n     * @group Emits\n     */\n    @Output() onEditInit: EventEmitter<TreeTableEditEvent> = new EventEmitter<TreeTableEditEvent>();\n    /**\n     * Callback to invoke when cell edit is completed.\n     * @param {TreeTableEditEvent} event - Custom edit event.\n     * @group Emits\n     */\n    @Output() onEditComplete: EventEmitter<TreeTableEditEvent> = new EventEmitter<TreeTableEditEvent>();\n    /**\n     * Callback to invoke when cell edit is cancelled with escape key.\n     * @param {TreeTableEditEvent} event - Custom edit event.\n     * @group Emits\n     */\n    @Output() onEditCancel: EventEmitter<TreeTableEditEvent> = new EventEmitter<TreeTableEditEvent>();\n    /**\n     * Callback to invoke when selectionKeys are changed.\n     * @param {Object} object - updated value of the selectionKeys.\n     * @group Emits\n     */\n    @Output() selectionKeysChange: EventEmitter<any> = new EventEmitter();\n\n    @ViewChild('container') containerViewChild: Nullable<ElementRef>;\n\n    @ViewChild('resizeHelper') resizeHelperViewChild: Nullable<ElementRef>;\n\n    @ViewChild('reorderIndicatorUp') reorderIndicatorUpViewChild: Nullable<ElementRef>;\n\n    @ViewChild('reorderIndicatorDown') reorderIndicatorDownViewChild: Nullable<ElementRef>;\n\n    @ViewChild('table') tableViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scrollableView') scrollableViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scrollableFrozenView') scrollableFrozenViewChild: Nullable<ElementRef>;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    _value: TreeNode<any>[] | undefined = [];\n\n    _virtualRowHeight: number = 28;\n\n    _selectionKeys: any;\n\n    serializedValue: any[] | undefined | null;\n\n    _totalRecords: number = 0;\n\n    _multiSortMeta: SortMeta[] | undefined | null;\n\n    _sortField: string | undefined | null;\n\n    _sortOrder: number = 1;\n\n    filteredNodes: Nullable<any[]>;\n\n    filterTimeout: any;\n\n    colGroupTemplate: Nullable<TemplateRef<any>>;\n\n    captionTemplate: Nullable<TemplateRef<any>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    bodyTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    summaryTemplate: Nullable<TemplateRef<any>>;\n\n    emptyMessageTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorLeftTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorRightTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorDropdownItemTemplate: Nullable<TemplateRef<any>>;\n\n    frozenHeaderTemplate: Nullable<TemplateRef<any>>;\n\n    frozenBodyTemplate: Nullable<TemplateRef<any>>;\n\n    frozenFooterTemplate: Nullable<TemplateRef<any>>;\n\n    frozenColGroupTemplate: Nullable<TemplateRef<any>>;\n\n    loadingIconTemplate: Nullable<TemplateRef<any>>;\n\n    reorderIndicatorUpIconTemplate: Nullable<TemplateRef<any>>;\n\n    reorderIndicatorDownIconTemplate: Nullable<TemplateRef<any>>;\n\n    sortIconTemplate: Nullable<TemplateRef<any>>;\n\n    checkboxIconTemplate: Nullable<TemplateRef<any>>;\n\n    headerCheckboxIconTemplate: Nullable<TemplateRef<any>>;\n\n    togglerIconTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorFirstPageLinkIconTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorLastPageLinkIconTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorPreviousPageLinkIconTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorNextPageLinkIconTemplate: Nullable<TemplateRef<any>>;\n\n    lastResizerHelperX: Nullable<number>;\n\n    reorderIconWidth: Nullable<number>;\n\n    reorderIconHeight: Nullable<number>;\n\n    draggedColumn: Nullable<any[]>;\n\n    dropPosition: Nullable<number>;\n\n    preventSelectionSetterPropagation: Nullable<boolean>;\n\n    _selection: any;\n\n    selectedKeys: any = {};\n\n    rowTouched: Nullable<boolean>;\n\n    editingCell: Nullable<Element>;\n\n    editingCellData: any | undefined | null;\n\n    editingCellField: any | undefined | null;\n\n    editingCellClick: Nullable<boolean>;\n\n    documentEditListener: VoidListener;\n\n    initialized: Nullable<boolean>;\n\n    toggleRowIndex: Nullable<number>;\n\n    ngOnInit() {\n        if (this.lazy && this.lazyLoadOnInit && !this.virtualScroll) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        }\n        this.initialized = true;\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'caption':\n                    this.captionTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'body':\n                    this.bodyTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'summary':\n                    this.summaryTemplate = item.template;\n                    break;\n\n                case 'colgroup':\n                    this.colGroupTemplate = item.template;\n                    break;\n\n                case 'emptymessage':\n                    this.emptyMessageTemplate = item.template;\n                    break;\n\n                case 'paginatorleft':\n                    this.paginatorLeftTemplate = item.template;\n                    break;\n\n                case 'paginatorright':\n                    this.paginatorRightTemplate = item.template;\n                    break;\n\n                case 'paginatordropdownitem':\n                    this.paginatorDropdownItemTemplate = item.template;\n                    break;\n\n                case 'frozenheader':\n                    this.frozenHeaderTemplate = item.template;\n                    break;\n\n                case 'frozenbody':\n                    this.frozenBodyTemplate = item.template;\n                    break;\n\n                case 'frozenfooter':\n                    this.frozenFooterTemplate = item.template;\n                    break;\n\n                case 'frozencolgroup':\n                    this.frozenColGroupTemplate = item.template;\n                    break;\n\n                case 'loadingicon':\n                    this.loadingIconTemplate = item.template;\n                    break;\n\n                case 'reorderindicatorupicon':\n                    this.reorderIndicatorUpIconTemplate = item.template;\n                    break;\n\n                case 'reorderindicatordownicon':\n                    this.reorderIndicatorDownIconTemplate = item.template;\n                    break;\n\n                case 'sorticon':\n                    this.sortIconTemplate = item.template;\n                    break;\n\n                case 'checkboxicon':\n                    this.checkboxIconTemplate = item.template;\n                    break;\n\n                case 'headercheckboxicon':\n                    this.headerCheckboxIconTemplate = item.template;\n                    break;\n\n                case 'togglericon':\n                    this.togglerIconTemplate = item.template;\n                    break;\n\n                case 'paginatorfirstpagelinkicon':\n                    this.paginatorFirstPageLinkIconTemplate = item.template;\n                    break;\n\n                case 'paginatorlastpagelinkicon':\n                    this.paginatorLastPageLinkIconTemplate = item.template;\n                    break;\n\n                case 'paginatorpreviouspagelinkicon':\n                    this.paginatorPreviousPageLinkIconTemplate = item.template;\n                    break;\n\n                case 'paginatornextpagelinkicon':\n                    this.paginatorNextPageLinkIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        private renderer: Renderer2,\n        public el: ElementRef,\n        public cd: ChangeDetectorRef,\n        public zone: NgZone,\n        public tableService: TreeTableService,\n        public filterService: FilterService,\n        public config: PrimeNGConfig\n    ) {}\n\n    ngOnChanges(simpleChange: SimpleChanges) {\n        if (simpleChange.value) {\n            this._value = simpleChange.value.currentValue;\n\n            if (!this.lazy) {\n                this.totalRecords = this._value ? this._value.length : 0;\n\n                if (this.sortMode == 'single' && this.sortField) this.sortSingle();\n                else if (this.sortMode == 'multiple' && this.multiSortMeta) this.sortMultiple();\n                else if (this.hasFilter())\n                    //sort already filters\n                    this._filter();\n            }\n\n            this.updateSerializedValue();\n            this.tableService.onUIUpdate(this.value);\n        }\n\n        if (simpleChange.sortField) {\n            this._sortField = simpleChange.sortField.currentValue;\n\n            //avoid triggering lazy load prior to lazy initialization at onInit\n            if (!this.lazy || this.initialized) {\n                if (this.sortMode === 'single') {\n                    this.sortSingle();\n                }\n            }\n        }\n\n        if (simpleChange.sortOrder) {\n            this._sortOrder = simpleChange.sortOrder.currentValue;\n\n            //avoid triggering lazy load prior to lazy initialization at onInit\n            if (!this.lazy || this.initialized) {\n                if (this.sortMode === 'single') {\n                    this.sortSingle();\n                }\n            }\n        }\n\n        if (simpleChange.multiSortMeta) {\n            this._multiSortMeta = simpleChange.multiSortMeta.currentValue;\n            if (this.sortMode === 'multiple') {\n                this.sortMultiple();\n            }\n        }\n\n        if (simpleChange.selection) {\n            this._selection = simpleChange.selection.currentValue;\n\n            if (!this.preventSelectionSetterPropagation) {\n                this.updateselectedKeys();\n                this.tableService.onSelectionChange();\n            }\n            this.preventSelectionSetterPropagation = false;\n        }\n    }\n\n    updateSerializedValue() {\n        this.serializedValue = [];\n\n        if (this.paginator) this.serializePageNodes();\n        else this.serializeNodes(null, this.filteredNodes || this.value, 0, true);\n    }\n\n    serializeNodes(parent: Nullable<TreeTableNode>, nodes: Nullable<TreeNode[]>, level: Nullable<number>, visible: Nullable<boolean>) {\n        if (nodes && nodes.length) {\n            for (let node of nodes) {\n                node.parent = <TreeTableNode>parent;\n                const rowNode = {\n                    node: node,\n                    parent: parent,\n                    level: level,\n                    visible: visible && (parent ? parent.expanded : true)\n                };\n                (<TreeNode[]>this.serializedValue).push(<TreeTableNode>rowNode);\n\n                if (rowNode.visible && node.expanded) {\n                    this.serializeNodes(node, node.children, <number>level + 1, rowNode.visible);\n                }\n            }\n        }\n    }\n\n    serializePageNodes() {\n        let data = this.filteredNodes || this.value;\n        this.serializedValue = [];\n        if (data && data.length) {\n            const first = this.lazy ? 0 : this.first;\n\n            for (let i = first; i < first + <number>this.rows; i++) {\n                let node = data[i];\n                if (node) {\n                    this.serializedValue.push({\n                        node: node,\n                        parent: <any>null,\n                        level: 0,\n                        visible: true\n                    });\n\n                    this.serializeNodes(node, node.children, 1, true);\n                }\n            }\n        }\n    }\n\n    updateselectedKeys() {\n        if (this.dataKey && this._selection) {\n            this.selectedKeys = {};\n            if (Array.isArray(this._selection)) {\n                for (let node of this._selection) {\n                    this.selectedKeys[String(ObjectUtils.resolveFieldData(node.data, this.dataKey))] = 1;\n                }\n            } else {\n                this.selectedKeys[String(ObjectUtils.resolveFieldData((<any>this._selection).data, this.dataKey))] = 1;\n            }\n        }\n    }\n\n    onPageChange(event: TreeTablePaginatorState) {\n        this.first = <number>event.first;\n        this.rows = <number>event.rows;\n\n        if (this.lazy) this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        else this.serializePageNodes();\n\n        this.onPage.emit({\n            first: this.first,\n            rows: this.rows\n        });\n\n        this.tableService.onUIUpdate(this.value);\n\n        if (this.scrollable) {\n            this.resetScrollTop();\n        }\n    }\n\n    sort(event: TreeTableSortEvent) {\n        let originalEvent = event.originalEvent;\n\n        if (this.sortMode === 'single') {\n            this._sortOrder = this.sortField === event.field ? this.sortOrder * -1 : this.defaultSortOrder;\n            this._sortField = event.field;\n            this.sortSingle();\n\n            if (this.resetPageOnSort && this.scrollable) {\n                this.resetScrollTop();\n            }\n        }\n        if (this.sortMode === 'multiple') {\n            let metaKey = (<KeyboardEvent>originalEvent).metaKey || (<KeyboardEvent>originalEvent).ctrlKey;\n            let sortMeta = this.getSortMeta(<string>event.field);\n\n            if (sortMeta) {\n                if (!metaKey) {\n                    this._multiSortMeta = [{ field: <string>event.field, order: sortMeta.order * -1 }];\n\n                    if (this.resetPageOnSort && this.scrollable) {\n                        this.resetScrollTop();\n                    }\n                } else {\n                    sortMeta.order = sortMeta.order * -1;\n                }\n            } else {\n                if (!metaKey || !this.multiSortMeta) {\n                    this._multiSortMeta = [];\n\n                    if (this.resetPageOnSort && this.scrollable) {\n                        this.resetScrollTop();\n                    }\n                }\n                (<SortMeta[]>this.multiSortMeta).push({ field: <string>event.field, order: this.defaultSortOrder });\n            }\n\n            this.sortMultiple();\n        }\n    }\n\n    sortSingle() {\n        if (this.sortField && this.sortOrder) {\n            if (this.lazy) {\n                this.onLazyLoad.emit(this.createLazyLoadMetadata());\n            } else if (this.value) {\n                this.sortNodes(this.value);\n\n                if (this.hasFilter()) {\n                    this._filter();\n                }\n            }\n\n            let sortMeta: SortMeta = {\n                field: this.sortField,\n                order: this.sortOrder\n            };\n\n            this.onSort.emit(sortMeta);\n            this.tableService.onSort(sortMeta);\n            this.updateSerializedValue();\n        }\n    }\n\n    sortNodes(nodes: TreeNode[]) {\n        if (!nodes || nodes.length === 0) {\n            return;\n        }\n\n        if (this.customSort) {\n            this.sortFunction.emit({\n                data: nodes,\n                mode: this.sortMode,\n                field: <string>this.sortField,\n                order: this.sortOrder\n            });\n        } else {\n            nodes.sort((node1, node2) => {\n                let value1 = ObjectUtils.resolveFieldData(node1.data, this.sortField);\n                let value2 = ObjectUtils.resolveFieldData(node2.data, this.sortField);\n                let result = null;\n\n                if (value1 == null && value2 != null) result = -1;\n                else if (value1 != null && value2 == null) result = 1;\n                else if (value1 == null && value2 == null) result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, { numeric: true });\n                else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n\n                return this.sortOrder * result;\n            });\n        }\n\n        for (let node of nodes) {\n            this.sortNodes(node.children as TreeNode[]);\n        }\n    }\n\n    sortMultiple() {\n        if (this.multiSortMeta) {\n            if (this.lazy) {\n                this.onLazyLoad.emit(this.createLazyLoadMetadata());\n            } else if (this.value) {\n                this.sortMultipleNodes(this.value);\n\n                if (this.hasFilter()) {\n                    this._filter();\n                }\n            }\n\n            this.onSort.emit({\n                multisortmeta: this.multiSortMeta\n            });\n            this.updateSerializedValue();\n            this.tableService.onSort(this.multiSortMeta);\n        }\n    }\n\n    sortMultipleNodes(nodes: TreeNode[]) {\n        if (!nodes || nodes.length === 0) {\n            return;\n        }\n\n        if (this.customSort) {\n            this.sortFunction.emit({\n                data: this.value,\n                mode: this.sortMode,\n                multiSortMeta: this.multiSortMeta\n            });\n        } else {\n            nodes.sort((node1, node2) => {\n                return this.multisortField(node1, node2, <SortMeta[]>this.multiSortMeta, 0);\n            });\n        }\n\n        for (let node of nodes) {\n            this.sortMultipleNodes(node.children as TreeNode[]);\n        }\n    }\n\n    multisortField(node1: TreeTableNode, node2: TreeTableNode, multiSortMeta: SortMeta[], index: number): number {\n        if (ObjectUtils.isEmpty(this.multiSortMeta) || ObjectUtils.isEmpty(multiSortMeta[index])) {\n            return 0;\n        }\n\n        let value1 = ObjectUtils.resolveFieldData(node1.data, multiSortMeta[index].field);\n        let value2 = ObjectUtils.resolveFieldData(node2.data, multiSortMeta[index].field);\n        let result = null;\n\n        if (value1 == null && value2 != null) result = -1;\n        else if (value1 != null && value2 == null) result = 1;\n        else if (value1 == null && value2 == null) result = 0;\n        if (typeof value1 == 'string' || value1 instanceof String) {\n            if (value1.localeCompare && value1 != value2) {\n                return multiSortMeta[index].order * value1.localeCompare(value2, undefined, { numeric: true });\n            }\n        } else {\n            result = value1 < value2 ? -1 : 1;\n        }\n\n        if (value1 == value2) {\n            return multiSortMeta.length - 1 > index ? this.multisortField(node1, node2, multiSortMeta, index + 1) : 0;\n        }\n\n        return multiSortMeta[index].order * <number>result;\n    }\n\n    getSortMeta(field: string) {\n        if (this.multiSortMeta && this.multiSortMeta.length) {\n            for (let i = 0; i < this.multiSortMeta.length; i++) {\n                if (this.multiSortMeta[i].field === field) {\n                    return this.multiSortMeta[i];\n                }\n            }\n        }\n\n        return null;\n    }\n\n    isSorted(field: string) {\n        if (this.sortMode === 'single') {\n            return this.sortField && this.sortField === field;\n        } else if (this.sortMode === 'multiple') {\n            let sorted = false;\n            if (this.multiSortMeta) {\n                for (let i = 0; i < this.multiSortMeta.length; i++) {\n                    if (this.multiSortMeta[i].field == field) {\n                        sorted = true;\n                        break;\n                    }\n                }\n            }\n            return sorted;\n        }\n    }\n\n    createLazyLoadMetadata(): any {\n        return {\n            first: this.first,\n            rows: this.rows,\n            sortField: this.sortField,\n            sortOrder: this.sortOrder,\n            filters: this.filters,\n            globalFilter: this.filters && this.filters['global'] ? this.filters['global'].value : null,\n            multiSortMeta: this.multiSortMeta,\n            forceUpdate: () => this.cd.detectChanges()\n        };\n    }\n\n    onLazyItemLoad(event: TreeTableLazyLoadEvent) {\n        this.onLazyLoad.emit({\n            ...this.createLazyLoadMetadata(),\n            ...event,\n            rows: event.last - event.first\n        });\n    }\n    /**\n     * Resets scroll to top.\n     * @group Method\n     */\n    public resetScrollTop() {\n        if (this.virtualScroll) this.scrollToVirtualIndex(0);\n        else this.scrollTo({ top: 0 });\n    }\n    /**\n     * Scrolls to given index when using virtual scroll.\n     * @param {number} index - index of the element.\n     * @group Method\n     */\n    public scrollToVirtualIndex(index: number) {\n        if (this.scrollableViewChild) {\n            (<any>this.scrollableViewChild).scrollToVirtualIndex(<number>index);\n        }\n\n        if (this.scrollableFrozenViewChild) {\n            (<any>this.scrollableViewChild).scrollToVirtualIndex(index);\n        }\n    }\n    /**\n     * Scrolls to given index.\n     * @param {ScrollToOptions} options - Scroll options.\n     * @group Method\n     */\n    public scrollTo(options: ScrollToOptions) {\n        if (this.scrollableViewChild) {\n            (<any>this.scrollableViewChild).scrollTo(options);\n        }\n\n        if (this.scrollableFrozenViewChild) {\n            (<any>this.scrollableViewChild).scrollTo(options);\n        }\n    }\n\n    isEmpty() {\n        let data = this.filteredNodes || this.value;\n        return data == null || data.length == 0;\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    onColumnResizeBegin(event: MouseEvent) {\n        let containerLeft = DomHandler.getOffset(this.containerViewChild?.nativeElement).left;\n        this.lastResizerHelperX = event.pageX - containerLeft + this.containerViewChild?.nativeElement.scrollLeft;\n        event.preventDefault();\n    }\n\n    onColumnResize(event: MouseEvent) {\n        let containerLeft = DomHandler.getOffset(this.containerViewChild?.nativeElement).left;\n        DomHandler.addClass(this.containerViewChild?.nativeElement, 'p-unselectable-text');\n        (<ElementRef>this.resizeHelperViewChild).nativeElement.style.height = this.containerViewChild?.nativeElement.offsetHeight + 'px';\n        (<ElementRef>this.resizeHelperViewChild).nativeElement.style.top = 0 + 'px';\n        (<ElementRef>this.resizeHelperViewChild).nativeElement.style.left = event.pageX - containerLeft + this.containerViewChild?.nativeElement.scrollLeft + 'px';\n\n        (<ElementRef>this.resizeHelperViewChild).nativeElement.style.display = 'block';\n    }\n\n    onColumnResizeEnd(event: MouseEvent, column: any) {\n        let delta = (<ElementRef>this.resizeHelperViewChild).nativeElement.offsetLeft - <number>this.lastResizerHelperX;\n        let columnWidth = column.offsetWidth;\n        let newColumnWidth = columnWidth + delta;\n        let minWidth = column.style.minWidth || 15;\n\n        if (columnWidth + delta > parseInt(minWidth)) {\n            if (this.columnResizeMode === 'fit') {\n                let nextColumn = column.nextElementSibling;\n                while (!nextColumn.offsetParent) {\n                    nextColumn = nextColumn.nextElementSibling;\n                }\n\n                if (nextColumn) {\n                    let nextColumnWidth = nextColumn.offsetWidth - delta;\n                    let nextColumnMinWidth = nextColumn.style.minWidth || 15;\n\n                    if (newColumnWidth > 15 && nextColumnWidth > parseInt(nextColumnMinWidth)) {\n                        if (this.scrollable) {\n                            let scrollableView = this.findParentScrollableView(column);\n                            let scrollableBodyTable = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body table') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport table');\n                            let scrollableHeaderTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-header-table');\n                            let scrollableFooterTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-footer-table');\n                            let resizeColumnIndex = DomHandler.index(column);\n\n                            this.resizeColGroup(scrollableHeaderTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n                            this.resizeColGroup(scrollableBodyTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n                            this.resizeColGroup(scrollableFooterTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n                        } else {\n                            column.style.width = newColumnWidth + 'px';\n                            if (nextColumn) {\n                                nextColumn.style.width = nextColumnWidth + 'px';\n                            }\n                        }\n                    }\n                }\n            } else if (this.columnResizeMode === 'expand') {\n                if (this.scrollable) {\n                    let scrollableView = this.findParentScrollableView(column);\n                    let scrollableBody = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport');\n                    let scrollableHeader = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-header');\n                    let scrollableFooter = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-footer');\n                    let scrollableBodyTable = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body table') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport table');\n                    let scrollableHeaderTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-header-table');\n                    let scrollableFooterTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-footer-table');\n                    let resizeColumnIndex = DomHandler.index(column);\n\n                    const scrollableBodyTableWidth = column ? scrollableBodyTable.offsetWidth + delta : newColumnWidth;\n                    const scrollableHeaderTableWidth = column ? scrollableHeaderTable.offsetWidth + delta : newColumnWidth;\n                    const isContainerInViewport = this.containerViewChild?.nativeElement.offsetWidth >= scrollableBodyTableWidth;\n\n                    let setWidth = (container: HTMLElement, table: HTMLElement, width: number, isContainerInViewport: boolean) => {\n                        if (container && table) {\n                            container.style.width = isContainerInViewport ? width + DomHandler.calculateScrollbarWidth(scrollableBody) + 'px' : 'auto';\n                            table.style.width = width + 'px';\n                        }\n                    };\n\n                    setWidth(scrollableBody, scrollableBodyTable, scrollableBodyTableWidth, isContainerInViewport);\n                    setWidth(scrollableHeader, scrollableHeaderTable, scrollableHeaderTableWidth, isContainerInViewport);\n                    setWidth(scrollableFooter, scrollableFooterTable, scrollableHeaderTableWidth, isContainerInViewport);\n\n                    this.resizeColGroup(scrollableHeaderTable, resizeColumnIndex, newColumnWidth, null);\n                    this.resizeColGroup(scrollableBodyTable, resizeColumnIndex, newColumnWidth, null);\n                    this.resizeColGroup(scrollableFooterTable, resizeColumnIndex, newColumnWidth, null);\n                } else {\n                    (<ElementRef>this.tableViewChild).nativeElement.style.width = this.tableViewChild?.nativeElement.offsetWidth + delta + 'px';\n                    column.style.width = newColumnWidth + 'px';\n                    let containerWidth = this.tableViewChild?.nativeElement.style.width;\n                    (<ElementRef>this.containerViewChild).nativeElement.style.width = containerWidth + 'px';\n                }\n            }\n\n            this.onColResize.emit({\n                element: column,\n                delta: delta\n            });\n        }\n\n        (this.resizeHelperViewChild as ElementRef).nativeElement.style.display = 'none';\n        DomHandler.removeClass(this.containerViewChild?.nativeElement, 'p-unselectable-text');\n    }\n\n    findParentScrollableView(column: any) {\n        if (column) {\n            let parent = column.parentElement;\n            while (parent && !DomHandler.hasClass(parent, 'p-treetable-scrollable-view')) {\n                parent = parent.parentElement;\n            }\n\n            return parent;\n        } else {\n            return null;\n        }\n    }\n\n    resizeColGroup(table: Nullable<HTMLElement>, resizeColumnIndex: Nullable<number>, newColumnWidth: Nullable<number>, nextColumnWidth: Nullable<number>) {\n        if (table) {\n            let colGroup = table.children[0].nodeName === 'COLGROUP' ? table.children[0] : null;\n\n            if (colGroup) {\n                let col = colGroup.children[<number>resizeColumnIndex];\n                let nextCol = col.nextElementSibling;\n                (<HTMLElement>col).style.width = newColumnWidth + 'px';\n\n                if (nextCol && nextColumnWidth) {\n                    (<HTMLElement>nextCol).style.width = nextColumnWidth + 'px';\n                }\n            } else {\n                throw 'Scrollable tables require a colgroup to support resizable columns';\n            }\n        }\n    }\n\n    onColumnDragStart(event: DragEvent, columnElement: any) {\n        this.reorderIconWidth = DomHandler.getHiddenElementOuterWidth(this.reorderIndicatorUpViewChild?.nativeElement);\n        this.reorderIconHeight = DomHandler.getHiddenElementOuterHeight(this.reorderIndicatorDownViewChild?.nativeElement);\n        this.draggedColumn = columnElement;\n        (<any>event).dataTransfer.setData('text', 'b'); // For firefox\n    }\n\n    onColumnDragEnter(event: DragEvent, dropHeader: any) {\n        if (this.reorderableColumns && this.draggedColumn && dropHeader) {\n            event.preventDefault();\n            let containerOffset = DomHandler.getOffset(this.containerViewChild?.nativeElement);\n            let dropHeaderOffset = DomHandler.getOffset(dropHeader);\n\n            if (this.draggedColumn != dropHeader) {\n                let targetLeft = dropHeaderOffset.left - containerOffset.left;\n                let targetTop = containerOffset.top - dropHeaderOffset.top;\n                let columnCenter = dropHeaderOffset.left + dropHeader.offsetWidth / 2;\n\n                (<ElementRef>this.reorderIndicatorUpViewChild).nativeElement.style.top = dropHeaderOffset.top - containerOffset.top - (<number>this.reorderIconHeight - 1) + 'px';\n                (<ElementRef>this.reorderIndicatorDownViewChild).nativeElement.style.top = dropHeaderOffset.top - containerOffset.top + dropHeader.offsetHeight + 'px';\n\n                if (event.pageX > columnCenter) {\n                    (<ElementRef>this.reorderIndicatorUpViewChild).nativeElement.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(<number>this.reorderIconWidth / 2) + 'px';\n                    (<ElementRef>this.reorderIndicatorDownViewChild).nativeElement.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(<number>this.reorderIconWidth / 2) + 'px';\n                    this.dropPosition = 1;\n                } else {\n                    (<ElementRef>this.reorderIndicatorUpViewChild).nativeElement.style.left = targetLeft - Math.ceil(<number>this.reorderIconWidth / 2) + 'px';\n                    (<ElementRef>this.reorderIndicatorDownViewChild).nativeElement.style.left = targetLeft - Math.ceil(<number>this.reorderIconWidth / 2) + 'px';\n                    this.dropPosition = -1;\n                }\n\n                (<ElementRef>this.reorderIndicatorUpViewChild).nativeElement.style.display = 'block';\n                (<ElementRef>this.reorderIndicatorDownViewChild).nativeElement.style.display = 'block';\n            } else {\n                (<any>event).dataTransfer.dropEffect = 'none';\n            }\n        }\n    }\n\n    onColumnDragLeave(event: DragEvent) {\n        if (this.reorderableColumns && this.draggedColumn) {\n            event.preventDefault();\n            (<ElementRef>this.reorderIndicatorUpViewChild).nativeElement.style.display = 'none';\n            (<ElementRef>this.reorderIndicatorDownViewChild).nativeElement.style.display = 'none';\n        }\n    }\n\n    onColumnDrop(event: DragEvent, dropColumn: any) {\n        event.preventDefault();\n        if (this.draggedColumn) {\n            let dragIndex = DomHandler.indexWithinGroup(this.draggedColumn, 'ttreorderablecolumn');\n            let dropIndex = DomHandler.indexWithinGroup(dropColumn, 'ttreorderablecolumn');\n            let allowDrop = dragIndex != dropIndex;\n            if (allowDrop && ((dropIndex - dragIndex == 1 && this.dropPosition === -1) || (dragIndex - dropIndex == 1 && this.dropPosition === 1))) {\n                allowDrop = false;\n            }\n\n            if (allowDrop && dropIndex < dragIndex && this.dropPosition === 1) {\n                dropIndex = dropIndex + 1;\n            }\n\n            if (allowDrop && dropIndex > dragIndex && this.dropPosition === -1) {\n                dropIndex = dropIndex - 1;\n            }\n\n            if (allowDrop) {\n                ObjectUtils.reorderArray(<any[]>this.columns, dragIndex, dropIndex);\n\n                this.onColReorder.emit({\n                    dragIndex: dragIndex,\n                    dropIndex: dropIndex,\n                    columns: this.columns\n                });\n            }\n\n            (<ElementRef>this.reorderIndicatorUpViewChild).nativeElement.style.display = 'none';\n            (<ElementRef>this.reorderIndicatorDownViewChild).nativeElement.style.display = 'none';\n            (this.draggedColumn as any).draggable = false;\n            this.draggedColumn = null;\n            this.dropPosition = null;\n        }\n    }\n\n    handleRowClick(event: any) {\n        let targetNode = (<HTMLElement>event.originalEvent.target).nodeName;\n        if (targetNode == 'INPUT' || targetNode == 'BUTTON' || targetNode == 'A' || DomHandler.hasClass(event.originalEvent.target, 'p-clickable')) {\n            return;\n        }\n\n        if (this.selectionMode) {\n            this.preventSelectionSetterPropagation = true;\n            let rowNode = event.rowNode;\n            let selected = this.isSelected((<any>rowNode).node);\n            let metaSelection = this.rowTouched ? false : this.metaKeySelection;\n            let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData((<TreeTableNode>rowNode.node).data, this.dataKey)) : null;\n\n            if (metaSelection) {\n                let keyboardEvent = <KeyboardEvent>event.originalEvent;\n                let metaKey = keyboardEvent.metaKey || keyboardEvent.ctrlKey;\n\n                if (selected && metaKey) {\n                    if (this.isSingleSelectionMode()) {\n                        this._selection = null;\n                        this.selectedKeys = {};\n                        this.selectionChange.emit(null);\n                    } else {\n                        let selectionIndex = this.findIndexInSelection(rowNode.node);\n                        this._selection = this.selection.filter((val: TreeTableNode, i: number) => i != selectionIndex);\n                        this.selectionChange.emit(this.selection);\n                        if (dataKeyValue) {\n                            delete this.selectedKeys[dataKeyValue];\n                        }\n                    }\n\n                    this.onNodeUnselect.emit({ originalEvent: event.originalEvent, node: <TreeTableNode>rowNode.node, type: 'row' });\n                } else {\n                    if (this.isSingleSelectionMode()) {\n                        this._selection = rowNode.node;\n                        this.selectionChange.emit(rowNode.node);\n                        if (dataKeyValue) {\n                            this.selectedKeys = {};\n                            this.selectedKeys[dataKeyValue] = 1;\n                        }\n                    } else if (this.isMultipleSelectionMode()) {\n                        if (metaKey) {\n                            this._selection = this.selection || [];\n                        } else {\n                            this._selection = [];\n                            this.selectedKeys = {};\n                        }\n\n                        this._selection = [...this.selection, rowNode.node];\n                        this.selectionChange.emit(this.selection);\n                        if (dataKeyValue) {\n                            this.selectedKeys[dataKeyValue] = 1;\n                        }\n                    }\n\n                    this.onNodeSelect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row', index: (<any>event).rowIndex });\n                }\n            } else {\n                if (this.selectionMode === 'single') {\n                    if (selected) {\n                        this._selection = null;\n                        this.selectedKeys = {};\n                        this.selectionChange.emit(this.selection);\n                        this.onNodeUnselect.emit({ originalEvent: event.originalEvent, node: <TreeTableNode>rowNode.node, type: 'row' });\n                    } else {\n                        this._selection = rowNode.node;\n                        this.selectionChange.emit(this.selection);\n                        this.onNodeSelect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row', index: event.rowIndex });\n                        if (dataKeyValue) {\n                            this.selectedKeys = {};\n                            this.selectedKeys[dataKeyValue] = 1;\n                        }\n                    }\n                } else if (this.selectionMode === 'multiple') {\n                    if (selected) {\n                        let selectionIndex = this.findIndexInSelection(rowNode.node);\n                        this._selection = this.selection.filter((val: TreeTableNode, i: number) => i != selectionIndex);\n                        this.selectionChange.emit(this.selection);\n                        this.onNodeUnselect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row' });\n                        if (dataKeyValue) {\n                            delete this.selectedKeys[dataKeyValue];\n                        }\n                    } else {\n                        this._selection = this.selection ? [...this.selection, rowNode.node] : [rowNode.node];\n                        this.selectionChange.emit(this.selection);\n                        this.onNodeSelect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row', index: event.rowIndex });\n                        if (dataKeyValue) {\n                            this.selectedKeys[dataKeyValue] = 1;\n                        }\n                    }\n                }\n            }\n\n            this.tableService.onSelectionChange();\n        }\n\n        this.rowTouched = false;\n    }\n\n    handleRowTouchEnd(event: Event) {\n        this.rowTouched = true;\n    }\n\n    handleRowRightClick(event: any) {\n        if (this.contextMenu) {\n            const node = event.rowNode.node;\n\n            if (this.contextMenuSelectionMode === 'separate') {\n                this.contextMenuSelection = node;\n                this.contextMenuSelectionChange.emit(node);\n                this.onContextMenuSelect.emit({ originalEvent: event.originalEvent, node: node });\n                this.contextMenu.show(event.originalEvent);\n                this.tableService.onContextMenu(node);\n            } else if (this.contextMenuSelectionMode === 'joint') {\n                this.preventSelectionSetterPropagation = true;\n                let selected = this.isSelected(node);\n                let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n\n                if (!selected) {\n                    if (this.isSingleSelectionMode()) {\n                        this.selection = node;\n                        this.selectionChange.emit(node);\n                    } else if (this.isMultipleSelectionMode()) {\n                        this.selection = [node];\n                        this.selectionChange.emit(this.selection);\n                    }\n\n                    if (dataKeyValue) {\n                        this.selectedKeys[dataKeyValue] = 1;\n                    }\n                }\n\n                this.contextMenu.show(event.originalEvent);\n                this.onContextMenuSelect.emit({ originalEvent: event.originalEvent, node: node });\n            }\n        }\n    }\n\n    toggleNodeWithCheckbox(event: any) {\n        // legacy selection support, will be removed in v18\n        this.selection = this.selection || [];\n        this.preventSelectionSetterPropagation = true;\n        let node = event.rowNode.node;\n        let selected = this.isSelected(node);\n\n        if (selected) {\n            this.propagateSelectionDown(node, false);\n            if (event.rowNode.parent) {\n                this.propagateSelectionUp(node.parent, false);\n            }\n            this.selectionChange.emit(this.selection);\n            this.onNodeUnselect.emit({ originalEvent: event, node: node });\n        } else {\n            this.propagateSelectionDown(node, true);\n            if (event.rowNode.parent) {\n                this.propagateSelectionUp(node.parent, true);\n            }\n            this.selectionChange.emit(this.selection);\n            this.onNodeSelect.emit({ originalEvent: event, node: node });\n        }\n\n        this.tableService.onSelectionChange();\n    }\n\n    toggleNodesWithCheckbox(event: Event, check: boolean) {\n        // legacy selection support, will be removed in v18\n        let data = this.filteredNodes || this.value;\n        this._selection = check && data ? data.slice() : [];\n\n        this.toggleAll(check);\n\n        if (!check) {\n            this._selection = [];\n            this.selectedKeys = {};\n        }\n\n        this.preventSelectionSetterPropagation = true;\n        this.selectionChange.emit(this._selection);\n        this.tableService.onSelectionChange();\n\n        this.onHeaderCheckboxToggle.emit({ originalEvent: event, checked: check });\n    }\n\n    toggleAll(checked: boolean) {\n        let data = this.filteredNodes || this.value;\n\n        if (!this.selectionKeys) {\n            if (data && data.length) {\n                for (let node of data) {\n                    this.propagateSelectionDown(node, checked);\n                }\n            }\n        } else {\n            // legacy selection support, will be removed in v18\n            if (data && data.length) {\n                for (let node of data) {\n                    this.propagateDown(node, checked);\n                }\n                this.selectionKeysChange.emit(this.selectionKeys);\n            }\n        }\n    }\n\n    propagateSelectionUp(node: TreeTableNode, select: boolean) {\n        // legacy selection support, will be removed in v18\n        if (node.children && node.children.length) {\n            let selectedChildCount: number = 0;\n            let childPartialSelected: boolean = false;\n            let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n\n            for (let child of node.children) {\n                if (this.isSelected(child)) selectedChildCount++;\n                else if (child.partialSelected) childPartialSelected = true;\n            }\n\n            if (select && selectedChildCount == node.children.length) {\n                this._selection = [...(this.selection || []), node];\n                node.partialSelected = false;\n                if (dataKeyValue) {\n                    this.selectedKeys[dataKeyValue] = 1;\n                }\n            } else {\n                if (!select) {\n                    let index = this.findIndexInSelection(node);\n                    if (index >= 0) {\n                        this._selection = this.selection.filter((val: any, i: number) => i != index);\n\n                        if (dataKeyValue) {\n                            delete this.selectedKeys[dataKeyValue];\n                        }\n                    }\n                }\n\n                if (childPartialSelected || (selectedChildCount > 0 && selectedChildCount != node.children.length)) node.partialSelected = true;\n                else node.partialSelected = false;\n            }\n        }\n\n        let parent = node.parent;\n        node.checked = select;\n        if (parent) {\n            this.propagateSelectionUp(parent, select);\n        }\n    }\n\n    propagateSelectionDown(node: TreeTableNode, select: boolean) {\n        // legacy selection support, will be removed in v18\n        let index = this.findIndexInSelection(node);\n        let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n\n        if (select && index == -1) {\n            this._selection = [...(this.selection || []), node];\n            if (dataKeyValue) {\n                this.selectedKeys[dataKeyValue] = 1;\n            }\n        } else if (!select && index > -1) {\n            this._selection = this.selection.filter((val: any, i: number) => i != index);\n            if (dataKeyValue) {\n                delete this.selectedKeys[dataKeyValue];\n            }\n        }\n\n        node.partialSelected = false;\n        node.checked = select;\n\n        if (node.children && node.children.length) {\n            for (let child of node.children) {\n                this.propagateSelectionDown(child, select);\n            }\n        }\n    }\n\n    isSelected(node: TreeTableNode) {\n        // legacy selection support, will be removed in v18\n        if (node && this.selection) {\n            if (this.dataKey) {\n                if (node.hasOwnProperty('checked')) {\n                    return node['checked'];\n                } else {\n                    return this.selectedKeys[ObjectUtils.resolveFieldData(node.data, this.dataKey)] !== undefined;\n                }\n            } else {\n                if (Array.isArray(this.selection)) return this.findIndexInSelection(node) > -1;\n                else return this.equals(node, this.selection);\n            }\n        }\n\n        return false;\n    }\n\n    isNodeSelected(node) {\n        return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.nodeKey(node)]?.checked === true : false;\n    }\n\n    isNodePartialSelected(node) {\n        return this.selectionMode && this.selectionKeys ? this.selectionKeys[this.nodeKey(node)]?.partialChecked === true : false;\n    }\n\n    nodeKey(node) {\n        return ObjectUtils.resolveFieldData(node, this.dataKey) || ObjectUtils.resolveFieldData(node?.data, this.dataKey);\n    }\n\n    toggleCheckbox(event) {\n        let { rowNode, check, originalEvent } = event;\n        let node = rowNode.node;\n        if (this.selectionKeys) {\n            this.propagateDown(node, check);\n            if (node.parent) {\n                this.propagateUp(node.parent, check);\n            }\n\n            this.selectionKeysChange.emit(this.selectionKeys);\n        } else {\n            this.toggleNodeWithCheckbox({ originalEvent, rowNode });\n        }\n\n        this.tableService.onSelectionChange();\n    }\n\n    propagateDown(node, check) {\n        if (check) {\n            this.selectionKeys[this.nodeKey(node)] = { checked: true, partialChecked: false };\n        } else {\n            delete this.selectionKeys[this.nodeKey(node)];\n        }\n\n        if (node.children && node.children.length) {\n            for (let child of node.children) {\n                this.propagateDown(child, check);\n            }\n        }\n    }\n\n    propagateUp(node, check) {\n        let checkedChildCount = 0;\n        let childPartialSelected = false;\n\n        for (let child of node.children) {\n            if (this.selectionKeys[this.nodeKey(child)] && this.selectionKeys[this.nodeKey(child)].checked) checkedChildCount++;\n            else if (this.selectionKeys[this.nodeKey(child)] && this.selectionKeys[this.nodeKey(child)].partialChecked) childPartialSelected = true;\n        }\n\n        if (check && checkedChildCount === node.children.length) {\n            this.selectionKeys[this.nodeKey(node)] = { checked: true, partialChecked: false };\n        } else {\n            if (!check) {\n                delete this.selectionKeys[this.nodeKey(node)];\n            }\n\n            if (childPartialSelected || (checkedChildCount > 0 && checkedChildCount !== node.children.length)) this.selectionKeys[this.nodeKey(node)] = { checked: false, partialChecked: true };\n            else this.selectionKeys[this.nodeKey(node)] = { checked: false, partialChecked: false };\n        }\n\n        let parent = node.parent;\n        if (parent) {\n            this.propagateUp(parent, check);\n        }\n    }\n\n    findIndexInSelection(node: any) {\n        let index: number = -1;\n        if (this.selection && this.selection.length) {\n            for (let i = 0; i < this.selection.length; i++) {\n                if (this.equals(node, this.selection[i])) {\n                    index = i;\n                    break;\n                }\n            }\n        }\n\n        return index;\n    }\n\n    isSingleSelectionMode() {\n        return this.selectionMode === 'single';\n    }\n\n    isMultipleSelectionMode() {\n        return this.selectionMode === 'multiple';\n    }\n\n    equals(node1: TreeTableNode, node2: TreeTableNode) {\n        return this.compareSelectionBy === 'equals' ? ObjectUtils.equals(node1, node2) : ObjectUtils.equals(node1.data, node2.data, this.dataKey);\n    }\n\n    filter(value: string | string[], field: string, matchMode: string) {\n        if (this.filterTimeout) {\n            clearTimeout(this.filterTimeout);\n        }\n\n        if (!this.isFilterBlank(value)) {\n            this.filters[field] = { value: value, matchMode: matchMode };\n        } else if (this.filters[field]) {\n            delete this.filters[field];\n        }\n\n        this.filterTimeout = setTimeout(() => {\n            this._filter();\n            this.filterTimeout = null;\n        }, this.filterDelay);\n    }\n\n    filterGlobal(value: string, matchMode: string) {\n        this.filter(value, 'global', matchMode);\n    }\n\n    isFilterBlank(filter: any): boolean {\n        if (filter !== null && filter !== undefined) {\n            if ((typeof filter === 'string' && filter.trim().length == 0) || (Array.isArray(filter) && filter.length == 0)) return true;\n            else return false;\n        }\n        return true;\n    }\n\n    _filter() {\n        if (this.lazy) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        } else {\n            if (!this.value) {\n                return;\n            }\n\n            if (!this.hasFilter()) {\n                this.filteredNodes = null;\n                if (this.paginator) {\n                    this.totalRecords = this.value ? this.value.length : 0;\n                }\n            } else {\n                let globalFilterFieldsArray;\n                if (this.filters['global']) {\n                    if (!this.columns && !this.globalFilterFields) throw new Error('Global filtering requires dynamic columns or globalFilterFields to be defined.');\n                    else globalFilterFieldsArray = this.globalFilterFields || this.columns;\n                }\n\n                this.filteredNodes = [];\n                const isStrictMode = this.filterMode === 'strict';\n                let isValueChanged = false;\n\n                for (let node of this.value) {\n                    let copyNode = { ...node };\n                    let localMatch = true;\n                    let globalMatch = false;\n                    let paramsWithoutNode;\n\n                    for (let prop in this.filters) {\n                        if (this.filters.hasOwnProperty(prop) && prop !== 'global') {\n                            let filterMeta = <FilterMetadata>this.filters[prop];\n                            let filterField = prop;\n                            let filterValue = filterMeta.value;\n                            let filterMatchMode = filterMeta.matchMode || 'startsWith';\n                            let filterConstraint = (<any>this.filterService).filters[filterMatchMode];\n                            paramsWithoutNode = { filterField, filterValue, filterConstraint, isStrictMode };\n                            if (\n                                (isStrictMode && !(this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode))) ||\n                                (!isStrictMode && !(this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode)))\n                            ) {\n                                localMatch = false;\n                            }\n\n                            if (!localMatch) {\n                                break;\n                            }\n                        }\n                    }\n\n                    if (this.filters['global'] && !globalMatch && globalFilterFieldsArray) {\n                        let copyNodeForGlobal = { ...copyNode };\n                        let filterField = undefined;\n                        let filterValue = this.filters['global'].value;\n                        let filterConstraint = (<any>this.filterService).filters[(<any>this.filters)['global'].matchMode];\n                        paramsWithoutNode = { filterField, filterValue, filterConstraint, isStrictMode, globalFilterFieldsArray };\n\n                        if (\n                            (isStrictMode && (this.findFilteredNodes(copyNodeForGlobal, paramsWithoutNode) || this.isFilterMatched(copyNodeForGlobal, paramsWithoutNode))) ||\n                            (!isStrictMode && (this.isFilterMatched(copyNodeForGlobal, paramsWithoutNode) || this.findFilteredNodes(copyNodeForGlobal, paramsWithoutNode)))\n                        ) {\n                            globalMatch = true;\n                            copyNode = copyNodeForGlobal;\n                        }\n                    }\n\n                    let matches = localMatch;\n                    if (this.filters['global']) {\n                        matches = localMatch && globalMatch;\n                    }\n\n                    if (matches) {\n                        this.filteredNodes.push(copyNode);\n                    }\n\n                    isValueChanged = isValueChanged || !localMatch || globalMatch || (localMatch && this.filteredNodes.length > 0) || (!globalMatch && this.filteredNodes.length === 0);\n                }\n\n                if (!isValueChanged) {\n                    this.filteredNodes = null;\n                }\n\n                if (this.paginator) {\n                    this.totalRecords = this.filteredNodes ? this.filteredNodes.length : this.value ? this.value.length : 0;\n                }\n            }\n            this.cd.markForCheck();\n        }\n\n        this.first = 0;\n\n        const filteredValue = this.filteredNodes || this.value;\n\n        this.onFilter.emit({\n            filters: this.filters,\n            filteredValue: filteredValue\n        });\n\n        this.tableService.onUIUpdate(filteredValue);\n        this.updateSerializedValue();\n\n        if (this.scrollable) {\n            this.resetScrollTop();\n        }\n    }\n\n    findFilteredNodes(node: TreeTableNode, paramsWithoutNode: any) {\n        if (node) {\n            let matched = false;\n            if (node.children) {\n                let childNodes = [...node.children];\n                node.children = [];\n                for (let childNode of childNodes) {\n                    let copyChildNode = { ...childNode };\n                    if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n                        matched = true;\n                        node.children.push(copyChildNode);\n                    }\n                }\n            }\n\n            if (matched) {\n                return true;\n            }\n        }\n    }\n\n    isFilterMatched(node: TreeTableNode, filterOptions: TreeTableFilterOptions) {\n        let { filterField, filterValue, filterConstraint, isStrictMode, globalFilterFieldsArray } = <any>filterOptions;\n        let matched = false;\n        const isMatched = (field: string) => filterConstraint(ObjectUtils.resolveFieldData(node.data, field), filterValue, <string>this.filterLocale);\n\n        matched = globalFilterFieldsArray?.length ? globalFilterFieldsArray.some((globalFilterField) => isMatched(globalFilterField.field || globalFilterField)) : isMatched(filterField);\n\n        if (!matched || (isStrictMode && !this.isNodeLeaf(node))) {\n            matched = this.findFilteredNodes(node, { filterField, filterValue, filterConstraint, isStrictMode, globalFilterFieldsArray }) || matched;\n        }\n\n        return matched;\n    }\n\n    isNodeLeaf(node: TreeTableNode) {\n        return node.leaf === false ? false : !(node.children && node.children.length);\n    }\n\n    hasFilter() {\n        let empty = true;\n        for (let prop in this.filters) {\n            if (this.filters.hasOwnProperty(prop)) {\n                empty = false;\n                break;\n            }\n        }\n\n        return !empty;\n    }\n    /**\n     * Clears the sort and paginator state.\n     * @group Method\n     */\n    public reset() {\n        this._sortField = null;\n        this._sortOrder = 1;\n        this._multiSortMeta = null;\n        this.tableService.onSort(null);\n\n        this.filteredNodes = null;\n        this.filters = {};\n\n        this.first = 0;\n\n        if (this.lazy) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        } else {\n            this.totalRecords = this._value ? this._value.length : 0;\n        }\n    }\n\n    updateEditingCell(cell: any, data: any, field: string) {\n        this.editingCell = cell;\n        this.editingCellData = data;\n        this.editingCellField = field;\n        this.bindDocumentEditListener();\n    }\n\n    isEditingCellValid() {\n        return this.editingCell && DomHandler.find(this.editingCell, '.ng-invalid.ng-dirty').length === 0;\n    }\n\n    bindDocumentEditListener() {\n        if (!this.documentEditListener) {\n            this.documentEditListener = this.renderer.listen(this.document, 'click', (event) => {\n                if (this.editingCell && !this.editingCellClick && this.isEditingCellValid()) {\n                    DomHandler.removeClass(this.editingCell, 'p-cell-editing');\n                    this.editingCell = null;\n                    this.onEditComplete.emit({ field: this.editingCellField, data: this.editingCellData });\n                    this.editingCellField = null;\n                    this.editingCellData = null;\n                    this.unbindDocumentEditListener();\n                }\n\n                this.editingCellClick = false;\n            });\n        }\n    }\n\n    unbindDocumentEditListener() {\n        if (this.documentEditListener) {\n            this.documentEditListener();\n            this.documentEditListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.unbindDocumentEditListener();\n        this.editingCell = null;\n        this.editingCellField = null;\n        this.editingCellData = null;\n        this.initialized = null;\n    }\n}\n\n@Component({\n    selector: '[pTreeTableBody]',\n    template: `\n        <ng-template ngFor let-serializedNode let-rowIndex=\"index\" [ngForOf]=\"serializedNodes || tt.serializedValue\" [ngForTrackBy]=\"tt.rowTrackBy\">\n            <ng-container *ngIf=\"serializedNode.visible\">\n                <ng-container *ngTemplateOutlet=\"template; context: { $implicit: serializedNode, node: serializedNode.node, rowData: serializedNode.node.data, columns: columns }\"></ng-container>\n            </ng-container>\n        </ng-template>\n        <ng-container *ngIf=\"tt.isEmpty()\">\n            <ng-container *ngTemplateOutlet=\"tt.emptyMessageTemplate; context: { $implicit: columns, frozen: frozen }\"></ng-container>\n        </ng-container>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TTBody {\n    @Input('pTreeTableBody') columns: any[] | undefined;\n\n    @Input('pTreeTableBodyTemplate') template: Nullable<TemplateRef<any>>;\n\n    @Input({ transform: booleanAttribute }) frozen: boolean | undefined;\n\n    @Input() serializedNodes: any;\n\n    @Input() scrollerOptions: any;\n\n    subscription: Subscription;\n\n    constructor(public tt: TreeTable, public treeTableService: TreeTableService, public cd: ChangeDetectorRef) {\n        this.subscription = this.tt.tableService.uiUpdateSource$.subscribe(() => {\n            if (this.tt.virtualScroll) {\n                this.cd.detectChanges();\n            }\n        });\n    }\n\n    getScrollerOption(option: any, options?: any) {\n        if (this.tt.virtualScroll) {\n            options = options || this.scrollerOptions;\n            return options ? options[option] : null;\n        }\n\n        return null;\n    }\n\n    getRowIndex(rowIndex: number) {\n        const getItemOptions = this.getScrollerOption('getItemOptions');\n        return getItemOptions ? getItemOptions(rowIndex).index : rowIndex;\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@Component({\n    selector: '[ttScrollableView]',\n    template: `\n        <div #scrollHeader class=\"p-treetable-scrollable-header\">\n            <div #scrollHeaderBox class=\"p-treetable-scrollable-header-box\">\n                <table class=\"p-treetable-scrollable-header-table\" [ngClass]=\"tt.tableStyleClass\" [ngStyle]=\"tt.tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate || tt.colGroupTemplate : tt.colGroupTemplate; context: { $implicit: columns }\"></ng-container>\n                    <thead role=\"rowgroup\" class=\"p-treetable-thead\">\n                        <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenHeaderTemplate || tt.headerTemplate : tt.headerTemplate; context: { $implicit: columns }\"></ng-container>\n                    </thead>\n                </table>\n            </div>\n        </div>\n\n        <p-scroller\n            *ngIf=\"tt.virtualScroll\"\n            #scroller\n            [items]=\"tt.serializedValue\"\n            styleClass=\"p-treetable-scrollable-body\"\n            [style]=\"{ height: tt.scrollHeight !== 'flex' ? tt.scrollHeight : undefined }\"\n            [scrollHeight]=\"scrollHeight !== 'flex' ? undefined : '100%'\"\n            [itemSize]=\"tt.virtualScrollItemSize || tt._virtualRowHeight\"\n            [lazy]=\"tt.lazy\"\n            (onLazyLoad)=\"tt.onLazyItemLoad($event)\"\n            [options]=\"tt.virtualScrollOptions\"\n        >\n            <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: items, options: scrollerOptions }\"></ng-container>\n            </ng-template>\n            <ng-container *ngIf=\"loaderTemplate\">\n                <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                    <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: scrollerOptions }\"></ng-container>\n                </ng-template>\n            </ng-container>\n        </p-scroller>\n        <ng-container *ngIf=\"!tt.virtualScroll\">\n            <div #scrollBody class=\"p-treetable-scrollable-body\" [ngStyle]=\"{ 'max-height': tt.scrollHeight !== 'flex' ? scrollHeight : undefined, 'overflow-y': !frozen && tt.scrollHeight ? 'scroll' : undefined }\">\n                <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: serializedValue, options: {} }\"></ng-container>\n            </div>\n        </ng-container>\n\n        <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n            <table role=\"table\" #scrollTable [class]=\"tt.tableStyleClass\" [ngClass]=\"scrollerOptions.contentStyleClass\" [ngStyle]=\"getMergedTableStyles(scrollerOptions.contentStyle)\">\n                <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate || tt.colGroupTemplate : tt.colGroupTemplate; context: { $implicit: columns }\"></ng-container>\n                <tbody role=\"rowgroup\" class=\"p-treetable-tbody\" [pTreeTableBody]=\"columns\" [pTreeTableBodyTemplate]=\"frozen ? tt.frozenBodyTemplate || tt.bodyTemplate : tt.bodyTemplate\" [serializedNodes]=\"items\" [frozen]=\"frozen\"></tbody>\n            </table>\n            <div #scrollableAligner [ngStyle]=\"{ 'background-color': 'transparent' }\" *ngIf=\"frozen\"></div>\n        </ng-template>\n\n        <div #scrollFooter *ngIf=\"tt.footerTemplate\" class=\"p-treetable-scrollable-footer\">\n            <div #scrollFooterBox class=\"p-treetable-scrollable-footer-box\">\n                <table class=\"p-treetable-scrollable-footer-table\" [ngClass]=\"tt.tableStyleClass\" [ngStyle]=\"tt.tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate || tt.colGroupTemplate : tt.colGroupTemplate; context: { $implicit: columns }\"></ng-container>\n                    <tfoot role=\"rowgroup\" class=\"p-treetable-tfoot\">\n                        <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenFooterTemplate || tt.footerTemplate : tt.footerTemplate; context: { $implicit: columns }\"></ng-container>\n                    </tfoot>\n                </table>\n            </div>\n        </div>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TTScrollableView implements AfterViewInit, OnDestroy {\n    @Input('ttScrollableView') columns: any[] | undefined;\n\n    @Input({ transform: booleanAttribute }) frozen: boolean | undefined;\n\n    @ViewChild('scrollHeader') scrollHeaderViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scrollHeaderBox') scrollHeaderBoxViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scrollBody') scrollBodyViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scrollTable') scrollTableViewChild: Nullable<ElementRef>;\n\n    @ViewChild('loadingTable') scrollLoadingTableViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scrollFooter') scrollFooterViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scrollFooterBox') scrollFooterBoxViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scrollableAligner') scrollableAlignerViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scroller') scroller: Nullable<Scroller>;\n\n    headerScrollListener: VoidListener;\n\n    bodyScrollListener: VoidListener;\n\n    footerScrollListener: VoidListener;\n\n    frozenSiblingBody: Nullable<Element>;\n\n    totalRecordsSubscription: Nullable<Subscription>;\n\n    _scrollHeight: string | undefined | null;\n\n    preventBodyScrollPropagation: boolean | undefined;\n\n    getMergedTableStyles(contentStyle) {\n        return {\n            ...this.tt.tableStyle,\n            ...contentStyle\n        };\n    }\n\n    @Input() get scrollHeight(): string | undefined | null {\n        return this._scrollHeight;\n    }\n    set scrollHeight(val: string | undefined | null) {\n        this._scrollHeight = val;\n        if (val != null && (val.includes('%') || val.includes('calc'))) {\n            console.log('Percentage scroll height calculation is removed in favor of the more performant CSS based flex mode, use scrollHeight=\"flex\" instead.');\n        }\n    }\n\n    constructor(@Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, public tt: TreeTable, public el: ElementRef, public zone: NgZone) {}\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.frozen) {\n                if (this.tt.frozenColumns || this.tt.frozenBodyTemplate) {\n                    DomHandler.addClass(this.el.nativeElement, 'p-treetable-unfrozen-view');\n                }\n\n                let frozenView = this.el.nativeElement.previousElementSibling;\n                if (frozenView) {\n                    if (this.tt.virtualScroll) this.frozenSiblingBody = DomHandler.findSingle(frozenView, '.p-scroller-viewport');\n                    else this.frozenSiblingBody = DomHandler.findSingle(frozenView, '.p-treetable-scrollable-body');\n                }\n\n                let scrollBarWidth = DomHandler.calculateScrollbarWidth();\n                (this.scrollHeaderBoxViewChild as ElementRef).nativeElement.style.paddingRight = scrollBarWidth + 'px';\n\n                if (this.scrollFooterBoxViewChild && this.scrollFooterBoxViewChild.nativeElement) {\n                    this.scrollFooterBoxViewChild.nativeElement.style.paddingRight = scrollBarWidth + 'px';\n                }\n            } else {\n                if (this.scrollableAlignerViewChild && this.scrollableAlignerViewChild.nativeElement) {\n                    this.scrollableAlignerViewChild.nativeElement.style.height = DomHandler.calculateScrollbarHeight() + 'px';\n                }\n            }\n\n            this.bindEvents();\n        }\n    }\n\n    bindEvents() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n                    this.headerScrollListener = this.renderer.listen(this.scrollHeaderBoxViewChild?.nativeElement, 'scroll', this.onHeaderScroll.bind(this));\n                }\n\n                if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n                    this.footerScrollListener = this.renderer.listen(this.scrollFooterViewChild.nativeElement, 'scroll', this.onFooterScroll.bind(this));\n                }\n\n                if (!this.frozen) {\n                    if (this.tt.virtualScroll) {\n                        this.bodyScrollListener = this.renderer.listen((this.scroller?.getElementRef() as ElementRef).nativeElement, 'scroll', this.onBodyScroll.bind(this));\n                    } else {\n                        this.bodyScrollListener = this.renderer.listen(this.scrollBodyViewChild?.nativeElement, 'scroll', this.onBodyScroll.bind(this));\n                    }\n                }\n            });\n        }\n    }\n\n    unbindEvents() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n                if (this.headerScrollListener) {\n                    this.headerScrollListener();\n                    this.headerScrollListener = null;\n                }\n            }\n\n            if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n                if (this.footerScrollListener) {\n                    this.footerScrollListener();\n                    this.footerScrollListener = null;\n                }\n            }\n\n            if (this.scrollBodyViewChild && this.scrollBodyViewChild.nativeElement) {\n                if (this.bodyScrollListener) {\n                    this.bodyScrollListener();\n                    this.bodyScrollListener = null;\n                }\n            }\n\n            if (this.scroller && this.scroller.getElementRef()) {\n                if (this.bodyScrollListener) {\n                    this.bodyScrollListener();\n                    this.bodyScrollListener = null;\n                }\n            }\n        }\n    }\n\n    onHeaderScroll() {\n        const scrollLeft = this.scrollHeaderViewChild?.nativeElement.scrollLeft;\n\n        (this.scrollBodyViewChild as ElementRef).nativeElement.scrollLeft = scrollLeft;\n\n        if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n            this.scrollFooterViewChild.nativeElement.scrollLeft = scrollLeft;\n        }\n\n        this.preventBodyScrollPropagation = true;\n    }\n\n    onFooterScroll() {\n        const scrollLeft = this.scrollFooterViewChild?.nativeElement.scrollLeft;\n        (this.scrollBodyViewChild as ElementRef).nativeElement.scrollLeft = scrollLeft;\n\n        if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n            this.scrollHeaderViewChild.nativeElement.scrollLeft = scrollLeft;\n        }\n\n        this.preventBodyScrollPropagation = true;\n    }\n\n    onBodyScroll(event: any) {\n        if (this.preventBodyScrollPropagation) {\n            this.preventBodyScrollPropagation = false;\n            return;\n        }\n\n        if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n            (this.scrollHeaderBoxViewChild as ElementRef).nativeElement.style.marginLeft = -1 * event.target.scrollLeft + 'px';\n        }\n\n        if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n            (this.scrollFooterBoxViewChild as ElementRef).nativeElement.style.marginLeft = -1 * event.target.scrollLeft + 'px';\n        }\n\n        if (this.frozenSiblingBody) {\n            this.frozenSiblingBody.scrollTop = event.target.scrollTop;\n        }\n    }\n\n    scrollToVirtualIndex(index: number): void {\n        if (this.scroller) {\n            this.scroller.scrollToIndex(index);\n        }\n    }\n\n    scrollTo(options: ScrollToOptions): void {\n        if (this.scroller) {\n            this.scroller.scrollTo(options);\n        } else {\n            if (this.scrollBodyViewChild?.nativeElement.scrollTo) {\n                this.scrollBodyViewChild.nativeElement.scrollTo(options);\n            } else {\n                (this.scrollBodyViewChild as ElementRef).nativeElement.scrollLeft = options.left;\n                (this.scrollBodyViewChild as ElementRef).nativeElement.scrollTop = options.top;\n            }\n        }\n    }\n\n    ngOnDestroy() {\n        this.unbindEvents();\n\n        this.frozenSiblingBody = null;\n    }\n}\n\n@Directive({\n    selector: '[ttSortableColumn]',\n    host: {\n        class: 'p-element',\n        '[class.p-sortable-column]': 'isEnabled()',\n        '[class.p-highlight]': 'sorted',\n        '[attr.tabindex]': 'isEnabled() ? \"0\" : null',\n        '[attr.role]': '\"columnheader\"',\n        '[attr.aria-sort]': 'ariaSorted'\n    }\n})\nexport class TTSortableColumn implements OnInit, OnDestroy {\n    @Input('ttSortableColumn') field: string | undefined;\n\n    @Input({ transform: booleanAttribute }) ttSortableColumnDisabled: boolean | undefined;\n\n    sorted: boolean | undefined;\n\n    subscription: Subscription | undefined;\n\n    get ariaSorted() {\n        if (this.sorted && this.tt.sortOrder < 0) return 'descending';\n        else if (this.sorted && this.tt.sortOrder > 0) return 'ascending';\n        else return 'none';\n    }\n\n    constructor(public tt: TreeTable) {\n        if (this.isEnabled()) {\n            this.subscription = this.tt.tableService.sortSource$.subscribe((sortMeta) => {\n                this.updateSortState();\n            });\n        }\n    }\n\n    ngOnInit() {\n        if (this.isEnabled()) {\n            this.updateSortState();\n        }\n    }\n\n    updateSortState() {\n        this.sorted = this.tt.isSorted(<string>this.field) as boolean;\n    }\n\n    @HostListener('click', ['$event'])\n    onClick(event: MouseEvent) {\n        if (this.isEnabled()) {\n            this.updateSortState();\n            this.tt.sort({\n                originalEvent: event,\n                field: this.field\n            });\n\n            DomHandler.clearSelection();\n        }\n    }\n\n    @HostListener('keydown.enter', ['$event'])\n    onEnterKey(event: MouseEvent) {\n        this.onClick(event);\n    }\n\n    isEnabled() {\n        return this.ttSortableColumnDisabled !== true;\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@Component({\n    selector: 'p-treeTableSortIcon',\n    template: ` <ng-container *ngIf=\"!tt.sortIconTemplate\">\n            <SortAltIcon [styleClass]=\"'p-sortable-column-icon'\" *ngIf=\"sortOrder === 0\" />\n            <SortAmountUpAltIcon [styleClass]=\"'p-sortable-column-icon'\" *ngIf=\"sortOrder === 1\" />\n            <SortAmountDownIcon [styleClass]=\"'p-sortable-column-icon'\" *ngIf=\"sortOrder === -1\" />\n        </ng-container>\n        <span *ngIf=\"tt.sortIconTemplate\" class=\"p-sortable-column-icon\">\n            <ng-template *ngTemplateOutlet=\"tt.sortIconTemplate; context: { $implicit: sortOrder }\"></ng-template>\n        </span>`,\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TTSortIcon implements OnInit, OnDestroy {\n    @Input() field: string | undefined;\n\n    @Input() ariaLabelDesc: string | undefined;\n\n    @Input() ariaLabelAsc: string | undefined;\n\n    subscription: Subscription | undefined;\n\n    sortOrder: number | undefined;\n\n    constructor(public tt: TreeTable, public cd: ChangeDetectorRef) {\n        this.subscription = this.tt.tableService.sortSource$.subscribe((sortMeta) => {\n            this.updateSortState();\n            this.cd.markForCheck();\n        });\n    }\n\n    ngOnInit() {\n        this.updateSortState();\n    }\n\n    onClick(event: Event) {\n        event.preventDefault();\n    }\n\n    updateSortState() {\n        if (this.tt.sortMode === 'single') {\n            this.sortOrder = this.tt.isSorted(<string>this.field) ? this.tt.sortOrder : 0;\n        } else if (this.tt.sortMode === 'multiple') {\n            let sortMeta = this.tt.getSortMeta(<string>this.field);\n            this.sortOrder = sortMeta ? sortMeta.order : 0;\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@Directive({\n    selector: '[ttResizableColumn]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TTResizableColumn implements AfterViewInit, OnDestroy {\n    @Input({ transform: booleanAttribute }) ttResizableColumnDisabled: boolean | undefined;\n\n    resizer: HTMLSpanElement | undefined;\n\n    resizerMouseDownListener: VoidListener;\n\n    documentMouseMoveListener: VoidListener;\n\n    documentMouseUpListener: VoidListener;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, public tt: TreeTable, public el: ElementRef, public zone: NgZone) {}\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.isEnabled()) {\n                DomHandler.addClass(this.el.nativeElement, 'p-resizable-column');\n                this.resizer = this.renderer.createElement('span');\n                this.renderer.addClass(this.resizer, 'p-column-resizer');\n                this.renderer.appendChild(this.el.nativeElement, this.resizer);\n\n                this.zone.runOutsideAngular(() => {\n                    this.resizerMouseDownListener = this.renderer.listen(this.resizer, 'mousedown', this.onMouseDown.bind(this));\n                });\n            }\n        }\n    }\n\n    bindDocumentEvents() {\n        this.zone.runOutsideAngular(() => {\n            this.documentMouseMoveListener = this.renderer.listen(this.document, 'mousemove', this.onDocumentMouseMove.bind(this));\n            this.documentMouseUpListener = this.renderer.listen(this.document, 'mouseup', this.onDocumentMouseUp.bind(this));\n        });\n    }\n\n    unbindDocumentEvents() {\n        if (this.documentMouseMoveListener) {\n            this.documentMouseMoveListener();\n            this.documentMouseMoveListener = null;\n        }\n\n        if (this.documentMouseUpListener) {\n            this.documentMouseUpListener();\n            this.documentMouseUpListener = null;\n        }\n    }\n\n    onMouseDown(event: MouseEvent) {\n        this.tt.onColumnResizeBegin(event);\n        this.bindDocumentEvents();\n    }\n\n    onDocumentMouseMove(event: MouseEvent) {\n        this.tt.onColumnResize(event);\n    }\n\n    onDocumentMouseUp(event: MouseEvent) {\n        this.tt.onColumnResizeEnd(event, this.el.nativeElement);\n        this.unbindDocumentEvents();\n    }\n\n    isEnabled() {\n        return this.ttResizableColumnDisabled !== true;\n    }\n\n    ngOnDestroy() {\n        if (this.resizerMouseDownListener) {\n            this.resizerMouseDownListener();\n            this.resizerMouseDownListener = null;\n        }\n\n        this.unbindDocumentEvents();\n    }\n}\n\n@Directive({\n    selector: '[ttReorderableColumn]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TTReorderableColumn implements AfterViewInit, OnDestroy {\n    @Input({ transform: booleanAttribute }) ttReorderableColumnDisabled: boolean | undefined;\n\n    dragStartListener: VoidListener;\n\n    dragOverListener: VoidListener;\n\n    dragEnterListener: VoidListener;\n\n    dragLeaveListener: VoidListener;\n\n    mouseDownListener: VoidListener;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, public tt: TreeTable, public el: ElementRef, public zone: NgZone) {}\n\n    ngAfterViewInit() {\n        if (this.isEnabled()) {\n            this.bindEvents();\n        }\n    }\n\n    bindEvents() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n                this.dragStartListener = this.renderer.listen(this.el.nativeElement, 'dragstart', this.onDragStart.bind(this));\n                this.dragOverListener = this.renderer.listen(this.el.nativeElement, 'dragover', this.onDragEnter.bind(this));\n                this.dragEnterListener = this.renderer.listen(this.el.nativeElement, 'dragenter', this.onDragEnter.bind(this));\n                this.dragLeaveListener = this.renderer.listen(this.el.nativeElement, 'dragleave', this.onDragLeave.bind(this));\n            });\n        }\n    }\n\n    unbindEvents() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.mouseDownListener) {\n                this.mouseDownListener();\n                this.mouseDownListener = null;\n            }\n\n            if (this.dragOverListener) {\n                this.dragOverListener();\n                this.dragOverListener = null;\n            }\n\n            if (this.dragEnterListener) {\n                this.dragEnterListener();\n                this.dragEnterListener = null;\n            }\n\n            if (this.dragLeaveListener) {\n                this.dragLeaveListener();\n                this.dragLeaveListener = null;\n            }\n        }\n    }\n\n    onMouseDown(event: any) {\n        if (event.target.nodeName === 'INPUT' || event.target.nodeName === 'TEXTAREA' || DomHandler.hasClass(event.target, 'p-column-resizer')) this.el.nativeElement.draggable = false;\n        else this.el.nativeElement.draggable = true;\n    }\n\n    onDragStart(event: DragEvent) {\n        this.tt.onColumnDragStart(event, this.el.nativeElement);\n    }\n\n    onDragOver(event: DragEvent) {\n        event.preventDefault();\n    }\n\n    onDragEnter(event: DragEvent) {\n        this.tt.onColumnDragEnter(event, this.el.nativeElement);\n    }\n\n    onDragLeave(event: DragEvent) {\n        this.tt.onColumnDragLeave(event);\n    }\n\n    @HostListener('drop', ['$event'])\n    onDrop(event: DragEvent) {\n        if (this.isEnabled()) {\n            this.tt.onColumnDrop(event, this.el.nativeElement);\n        }\n    }\n\n    isEnabled() {\n        return this.ttReorderableColumnDisabled !== true;\n    }\n\n    ngOnDestroy() {\n        this.unbindEvents();\n    }\n}\n\n@Directive({\n    selector: '[ttSelectableRow]',\n    host: {\n        class: 'p-element',\n        '[class.p-highlight]': 'selected',\n        '[attr.data-p-highlight]': 'selected',\n        '[attr.aria-checked]': 'selected'\n    }\n})\nexport class TTSelectableRow implements OnInit, OnDestroy {\n    @Input('ttSelectableRow') rowNode: any;\n\n    @Input({ transform: booleanAttribute }) ttSelectableRowDisabled: boolean | undefined;\n\n    selected: boolean | undefined;\n\n    subscription: Subscription | undefined;\n\n    constructor(public tt: TreeTable, public tableService: TreeTableService) {\n        if (this.isEnabled()) {\n            this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n                this.selected = this.tt.isSelected(this.rowNode.node);\n            });\n        }\n    }\n\n    ngOnInit() {\n        if (this.isEnabled()) {\n            this.selected = this.tt.isSelected(this.rowNode.node);\n        }\n    }\n\n    @HostListener('click', ['$event'])\n    onClick(event: Event) {\n        if (this.isEnabled()) {\n            this.tt.handleRowClick({\n                originalEvent: event,\n                rowNode: this.rowNode\n            });\n        }\n    }\n\n    @HostListener('keydown', ['$event'])\n    onKeyDown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'Enter':\n            case 'Space':\n                this.onEnterKey(event);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    @HostListener('touchend', ['$event'])\n    onTouchEnd(event: Event) {\n        if (this.isEnabled()) {\n            this.tt.handleRowTouchEnd(event);\n        }\n    }\n\n    onEnterKey(event) {\n        if (this.tt.selectionMode === 'checkbox') {\n            this.tt.toggleNodeWithCheckbox({\n                originalEvent: event,\n                rowNode: this.rowNode\n            });\n        } else {\n            this.onClick(event);\n        }\n        event.preventDefault();\n    }\n\n    isEnabled() {\n        return this.ttSelectableRowDisabled !== true;\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@Directive({\n    selector: '[ttSelectableRowDblClick]',\n    host: {\n        class: 'p-element',\n        '[class.p-highlight]': 'selected'\n    }\n})\nexport class TTSelectableRowDblClick implements OnInit, OnDestroy {\n    @Input('ttSelectableRowDblClick') rowNode: any;\n\n    @Input({ transform: booleanAttribute }) ttSelectableRowDisabled: boolean | undefined;\n\n    selected: boolean | undefined;\n\n    subscription: Subscription | undefined;\n\n    constructor(public tt: TreeTable, public tableService: TreeTableService) {\n        if (this.isEnabled()) {\n            this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n                this.selected = this.tt.isSelected(this.rowNode.node);\n            });\n        }\n    }\n\n    ngOnInit() {\n        if (this.isEnabled()) {\n            this.selected = this.tt.isSelected(this.rowNode.node);\n        }\n    }\n\n    @HostListener('dblclick', ['$event'])\n    onClick(event: Event) {\n        if (this.isEnabled()) {\n            this.tt.handleRowClick({\n                originalEvent: event,\n                rowNode: this.rowNode\n            });\n        }\n    }\n\n    isEnabled() {\n        return this.ttSelectableRowDisabled !== true;\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@Directive({\n    selector: '[ttContextMenuRow]',\n    host: {\n        class: 'p-element',\n        '[class.p-highlight-contextmenu]': 'selected',\n        '[attr.tabindex]': 'isEnabled() ? 0 : undefined'\n    }\n})\nexport class TTContextMenuRow {\n    @Input('ttContextMenuRow') rowNode: any | undefined;\n\n    @Input({ transform: booleanAttribute }) ttContextMenuRowDisabled: boolean | undefined;\n\n    selected: boolean | undefined;\n\n    subscription: Subscription | undefined;\n\n    constructor(public tt: TreeTable, public tableService: TreeTableService, private el: ElementRef) {\n        if (this.isEnabled()) {\n            this.subscription = this.tt.tableService.contextMenuSource$.subscribe((node) => {\n                this.selected = this.tt.equals(this.rowNode.node, node);\n            });\n        }\n    }\n\n    @HostListener('contextmenu', ['$event'])\n    onContextMenu(event: Event) {\n        if (this.isEnabled()) {\n            this.tt.handleRowRightClick({\n                originalEvent: event,\n                rowNode: this.rowNode\n            });\n\n            this.el.nativeElement.focus();\n\n            event.preventDefault();\n        }\n    }\n\n    isEnabled() {\n        return this.ttContextMenuRowDisabled !== true;\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@Component({\n    selector: 'p-treeTableCheckbox',\n    template: `\n        <div class=\"p-checkbox p-component\" [ngClass]=\"{ 'p-checkbox-focused': focused, 'p-variant-filled': tt.config.inputStyle() === 'filled' }\" (click)=\"onClick($event)\">\n            <div class=\"p-hidden-accessible\">\n                <input type=\"checkbox\" [checked]=\"checked\" (focus)=\"onFocus()\" (blur)=\"onBlur()\" tabindex=\"-1\" />\n            </div>\n            <div #box [ngClass]=\"{ 'p-checkbox-box': true, 'p-highlight': checked, 'p-focus': focused, 'p-indeterminate': partialChecked, 'p-disabled': disabled }\" role=\"checkbox\" [attr.aria-checked]=\"checked\">\n                <ng-container *ngIf=\"!tt.checkboxIconTemplate\">\n                    <CheckIcon [styleClass]=\"'p-checkbox-icon'\" *ngIf=\"checked\" />\n                    <MinusIcon [styleClass]=\"'p-checkbox-icon'\" *ngIf=\"partialChecked\" />\n                </ng-container>\n                <span *ngIf=\"tt.checkboxIconTemplate\">\n                    <ng-template *ngTemplateOutlet=\"tt.checkboxIconTemplate; context: { $implicit: checked, partialSelected: partialChecked }\"></ng-template>\n                </span>\n            </div>\n        </div>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TTCheckbox {\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n\n    @Input('value') rowNode: any;\n\n    checked: boolean | undefined;\n\n    partialChecked: boolean | undefined;\n\n    focused: boolean | undefined;\n\n    subscription: Subscription | undefined;\n\n    constructor(public tt: TreeTable, public tableService: TreeTableService, public cd: ChangeDetectorRef) {\n        this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n            if (this.tt.selectionKeys) {\n                this.checked = this.tt.isNodeSelected(this.rowNode.node);\n                this.partialChecked = this.tt.isNodePartialSelected(this.rowNode.node);\n            } else {\n                this.checked = this.tt.isSelected(this.rowNode.node);\n                this.partialChecked = this.rowNode.node.partialSelected;\n            }\n            this.cd.markForCheck();\n        });\n    }\n\n    ngOnInit() {\n        if (this.tt.selectionKeys) {\n            this.checked = this.tt.isNodeSelected(this.rowNode.node);\n            this.partialChecked = this.tt.isNodePartialSelected(this.rowNode.node);\n        } else {\n            // for backward compatibility\n            this.checked = this.tt.isSelected(this.rowNode.node);\n            this.partialChecked = this.rowNode.node.partialSelected;\n        }\n    }\n\n    onClick(event: Event) {\n        if (!this.disabled) {\n            if (this.tt.selectionKeys) {\n                const _check = !this.checked;\n                this.tt.toggleCheckbox({\n                    originalEvent: event,\n                    check: _check,\n                    rowNode: this.rowNode\n                });\n            } else {\n                this.tt.toggleNodeWithCheckbox({\n                    originalEvent: event,\n                    rowNode: this.rowNode\n                });\n            }\n        }\n        DomHandler.clearSelection();\n    }\n\n    onFocus() {\n        this.focused = true;\n    }\n\n    onBlur() {\n        this.focused = false;\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@Component({\n    selector: 'p-treeTableHeaderCheckbox',\n    template: `\n        <div class=\"p-checkbox p-component\" [ngClass]=\"{ 'p-checkbox-focused': focused }\" (click)=\"onClick($event, cb.checked)\">\n            <div class=\"p-hidden-accessible\">\n                <input #cb type=\"checkbox\" [checked]=\"checked\" (focus)=\"onFocus()\" (blur)=\"onBlur()\" [disabled]=\"!tt.value || tt.value.length === 0\" />\n            </div>\n            <div #box [ngClass]=\"{ 'p-checkbox-box': true, 'p-highlight': checked, 'p-focus': focused, 'p-disabled': !tt.value || tt.value.length === 0 }\" role=\"checkbox\" [attr.aria-checked]=\"checked\">\n                <ng-container *ngIf=\"!tt.headerCheckboxIconTemplate\">\n                    <CheckIcon *ngIf=\"checked\" [styleClass]=\"'p-checkbox-icon'\" />\n                </ng-container>\n                <span class=\"p-checkbox-icon\" *ngIf=\"tt.headerCheckboxIconTemplate\">\n                    <ng-template *ngTemplateOutlet=\"tt.headerCheckboxIconTemplate; context: { $implicit: checked }\"></ng-template>\n                </span>\n            </div>\n        </div>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TTHeaderCheckbox {\n    @ViewChild('box') boxViewChild: ElementRef | undefined;\n\n    checked: boolean | undefined;\n\n    focused: boolean | undefined;\n\n    disabled: boolean | undefined;\n\n    selectionChangeSubscription: Subscription;\n\n    valueChangeSubscription: Subscription;\n\n    constructor(public tt: TreeTable, public tableService: TreeTableService, private cd: ChangeDetectorRef) {\n        this.valueChangeSubscription = this.tt.tableService.uiUpdateSource$.subscribe(() => {\n            this.checked = this.updateCheckedState();\n        });\n\n        this.selectionChangeSubscription = this.tt.tableService.selectionSource$.subscribe(() => {\n            this.checked = this.updateCheckedState();\n        });\n    }\n\n    ngOnInit() {\n        this.checked = this.updateCheckedState();\n    }\n\n    onClick(event: Event, checked: boolean) {\n        if ((this.tt.value || this.tt.filteredNodes) && (this.tt.value.length > 0 || this.tt.filteredNodes.length > 0)) {\n            this.tt.toggleNodesWithCheckbox(event, !checked);\n        }\n\n        DomHandler.clearSelection();\n    }\n\n    onFocus() {\n        this.focused = true;\n    }\n\n    onBlur() {\n        this.focused = false;\n    }\n\n    ngOnDestroy() {\n        if (this.selectionChangeSubscription) {\n            this.selectionChangeSubscription.unsubscribe();\n        }\n\n        if (this.valueChangeSubscription) {\n            this.valueChangeSubscription.unsubscribe();\n        }\n    }\n\n    updateCheckedState() {\n        this.cd.markForCheck();\n        let checked!: boolean;\n        const data = this.tt.filteredNodes || this.tt.value;\n\n        if (data) {\n            if (this.tt.selectionKeys) {\n                for (let node of data) {\n                    if (this.tt.isNodeSelected(node)) {\n                        checked = true;\n                    } else {\n                        checked = false;\n                        break;\n                    }\n                }\n            }\n            if (!this.tt.selectionKeys) {\n                // legacy selection support, will be removed in v18\n                for (let node of data) {\n                    if (this.tt.isSelected(node)) {\n                        checked = true;\n                    } else {\n                        checked = false;\n                        break;\n                    }\n                }\n            }\n        } else {\n            checked = false;\n        }\n\n        return checked;\n    }\n}\n\n@Directive({\n    selector: '[ttEditableColumn]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TTEditableColumn implements AfterViewInit {\n    @Input('ttEditableColumn') data: any;\n\n    @Input('ttEditableColumnField') field: any;\n\n    @Input({ transform: booleanAttribute }) ttEditableColumnDisabled: boolean | undefined;\n\n    constructor(public tt: TreeTable, public el: ElementRef, public zone: NgZone) {}\n\n    ngAfterViewInit() {\n        if (this.isEnabled()) {\n            DomHandler.addClass(this.el.nativeElement, 'p-editable-column');\n        }\n    }\n\n    @HostListener('click', ['$event'])\n    onClick(event: MouseEvent) {\n        if (this.isEnabled()) {\n            this.tt.editingCellClick = true;\n\n            if (this.tt.editingCell) {\n                if (this.tt.editingCell !== this.el.nativeElement) {\n                    if (!this.tt.isEditingCellValid()) {\n                        return;\n                    }\n\n                    DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n                    this.openCell();\n                }\n            } else {\n                this.openCell();\n            }\n        }\n    }\n\n    openCell() {\n        this.tt.updateEditingCell(this.el.nativeElement, this.data, this.field);\n        DomHandler.addClass(this.el.nativeElement, 'p-cell-editing');\n        this.tt.onEditInit.emit({ field: this.field, data: this.data });\n        this.tt.editingCellClick = true;\n        this.zone.runOutsideAngular(() => {\n            setTimeout(() => {\n                let focusable = DomHandler.findSingle(this.el.nativeElement, 'input, textarea');\n                if (focusable) {\n                    focusable.focus();\n                }\n            }, 50);\n        });\n    }\n\n    closeEditingCell() {\n        DomHandler.removeClass(this.tt.editingCell, 'p-checkbox-icon');\n        this.tt.editingCell = null;\n        this.tt.unbindDocumentEditListener();\n    }\n\n    @HostListener('keydown', ['$event'])\n    onKeyDown(event: KeyboardEvent) {\n        if (this.isEnabled()) {\n            if (event.code == 'Enter' && !event.shiftKey) {\n                if (this.tt.isEditingCellValid()) {\n                    DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n                    this.closeEditingCell();\n                    this.tt.onEditComplete.emit({ field: this.field, data: this.data });\n                }\n\n                event.preventDefault();\n            } else if (event.code == 'Escape') {\n                if (this.tt.isEditingCellValid()) {\n                    DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n                    this.closeEditingCell();\n                    this.tt.onEditCancel.emit({ field: this.field, data: this.data });\n                }\n\n                event.preventDefault();\n            } else if (event.code == 'Tab') {\n                this.tt.onEditComplete.emit({ field: this.field, data: this.data });\n\n                if (event.shiftKey) this.moveToPreviousCell(event);\n                else this.moveToNextCell(event);\n            }\n        }\n    }\n\n    findCell(element: any) {\n        if (element) {\n            let cell = element;\n            while (cell && !DomHandler.hasClass(cell, 'p-cell-editing')) {\n                cell = cell.parentElement;\n            }\n\n            return cell;\n        } else {\n            return null;\n        }\n    }\n\n    moveToPreviousCell(event: KeyboardEvent) {\n        let currentCell = this.findCell(event.target);\n        let row = currentCell.parentElement;\n        let targetCell = this.findPreviousEditableColumn(currentCell);\n\n        if (targetCell) {\n            DomHandler.invokeElementMethod(targetCell, 'click');\n            event.preventDefault();\n        }\n    }\n\n    moveToNextCell(event: KeyboardEvent) {\n        let currentCell = this.findCell(event.target);\n        let row = currentCell.parentElement;\n        let targetCell = this.findNextEditableColumn(currentCell);\n\n        if (targetCell) {\n            DomHandler.invokeElementMethod(targetCell, 'click');\n            event.preventDefault();\n        }\n    }\n\n    findPreviousEditableColumn(cell: any): Element | null {\n        let prevCell = cell.previousElementSibling;\n\n        if (!prevCell) {\n            let previousRow = cell.parentElement ? cell.parentElement.previousElementSibling : null;\n            if (previousRow) {\n                prevCell = previousRow.lastElementChild;\n            }\n        }\n\n        if (prevCell) {\n            if (DomHandler.hasClass(prevCell, 'p-editable-column')) return prevCell;\n            else return this.findPreviousEditableColumn(prevCell);\n        } else {\n            return null;\n        }\n    }\n\n    findNextEditableColumn(cell: Element): Element | null {\n        let nextCell = cell.nextElementSibling;\n\n        if (!nextCell) {\n            let nextRow = cell.parentElement ? cell.parentElement.nextElementSibling : null;\n            if (nextRow) {\n                nextCell = nextRow.firstElementChild;\n            }\n        }\n\n        if (nextCell) {\n            if (DomHandler.hasClass(nextCell, 'p-editable-column')) return nextCell;\n            else return this.findNextEditableColumn(nextCell);\n        } else {\n            return null;\n        }\n    }\n\n    isEnabled() {\n        return this.ttEditableColumnDisabled !== true;\n    }\n}\n\n@Component({\n    selector: 'p-treeTableCellEditor',\n    template: `\n        <ng-container *ngIf=\"tt.editingCell === editableColumn.el.nativeElement\">\n            <ng-container *ngTemplateOutlet=\"inputTemplate\"></ng-container>\n        </ng-container>\n        <ng-container *ngIf=\"!tt.editingCell || tt.editingCell !== editableColumn.el.nativeElement\">\n            <ng-container *ngTemplateOutlet=\"outputTemplate\"></ng-container>\n        </ng-container>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TreeTableCellEditor implements AfterContentInit {\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    inputTemplate: Nullable<TemplateRef<any>>;\n\n    outputTemplate: Nullable<TemplateRef<any>>;\n\n    constructor(public tt: TreeTable, public editableColumn: TTEditableColumn) {}\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'input':\n                    this.inputTemplate = item.template;\n                    break;\n\n                case 'output':\n                    this.outputTemplate = item.template;\n                    break;\n            }\n        });\n    }\n}\n\n@Directive({\n    selector: '[ttRow]',\n    host: {\n        class: 'p-element',\n        '[class]': `'p-element ' + styleClass`,\n        '[attr.tabindex]': \"'0'\",\n        '[attr.aria-expanded]': 'expanded',\n        '[attr.aria-level]': 'level',\n        '[attr.data-pc-section]': 'row',\n        '[attr.role]': 'row'\n    }\n})\nexport class TTRow {\n    get level() {\n        return this.rowNode?.['level'] + 1;\n    }\n\n    get styleClass() {\n        return this.rowNode?.node['styleClass'] || '';\n    }\n\n    get expanded() {\n        return this.rowNode?.node['expanded'];\n    }\n\n    @Input('ttRow') rowNode: any;\n\n    constructor(public tt: TreeTable, public el: ElementRef, public zone: NgZone) {}\n\n    @HostListener('keydown', ['$event'])\n    onKeyDown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onArrowDownKey(event: KeyboardEvent) {\n        let nextRow = this.el?.nativeElement?.nextElementSibling;\n        if (nextRow) {\n            this.focusRowChange(<HTMLElement>event.currentTarget, nextRow);\n        }\n\n        event.preventDefault();\n    }\n\n    onArrowUpKey(event: KeyboardEvent) {\n        let prevRow = this.el?.nativeElement?.previousElementSibling;\n        if (prevRow) {\n            this.focusRowChange(<HTMLElement>event.currentTarget, prevRow);\n        }\n\n        event.preventDefault();\n    }\n\n    onArrowRightKey(event: KeyboardEvent) {\n        const currentTarget = <HTMLElement>event.currentTarget;\n        const isHiddenIcon = DomHandler.findSingle(currentTarget, 'button').style.visibility === 'hidden';\n\n        if (!isHiddenIcon && !this.expanded && this.rowNode.node['children']) {\n            this.expand(event);\n\n            currentTarget.tabIndex = -1;\n        }\n        event.preventDefault();\n    }\n\n    onArrowLeftKey(event: KeyboardEvent) {\n        const container = this.tt.containerViewChild?.nativeElement;\n        const expandedRows = DomHandler.find(container, '[aria-expanded=\"true\"]');\n        const lastExpandedRow = expandedRows[expandedRows.length - 1];\n\n        if (this.expanded) {\n            this.collapse(event);\n        }\n        if (lastExpandedRow) {\n            this.tt.toggleRowIndex = DomHandler.index(lastExpandedRow);\n        }\n        this.restoreFocus();\n        event.preventDefault();\n    }\n\n    onHomeKey(event: KeyboardEvent) {\n        const firstElement = DomHandler.findSingle(this.tt.containerViewChild?.nativeElement, `tr[aria-level=\"${this.level}\"]`);\n        firstElement && DomHandler.focus(firstElement);\n        event.preventDefault();\n    }\n\n    onEndKey(event: KeyboardEvent) {\n        const nodes = DomHandler.find(this.tt.containerViewChild?.nativeElement, `tr[aria-level=\"${this.level}\"]`);\n        const lastElement = nodes[nodes.length - 1];\n        DomHandler.focus(lastElement);\n        event.preventDefault();\n    }\n\n    onTabKey(event: KeyboardEvent) {\n        const rows = this.el.nativeElement ? [...DomHandler.find(this.el.nativeElement.parentNode, 'tr')] : undefined;\n\n        if (rows && ObjectUtils.isNotEmpty(rows)) {\n            const hasSelectedRow = rows.some((row) => DomHandler.getAttribute(row, 'data-p-highlight') || row.getAttribute('aria-checked') === 'true');\n            rows.forEach((row) => {\n                row.tabIndex = -1;\n            });\n\n            if (hasSelectedRow) {\n                const selectedNodes = rows.filter((node) => DomHandler.getAttribute(node, 'data-p-highlight') || node.getAttribute('aria-checked') === 'true');\n                selectedNodes[0].tabIndex = 0;\n\n                return;\n            }\n\n            rows[0].tabIndex = 0;\n        }\n    }\n\n    expand(event: Event) {\n        this.tt.toggleRowIndex = DomHandler.index(this.el.nativeElement);\n        this.rowNode.node['expanded'] = true;\n\n        this.tt.updateSerializedValue();\n        this.tt.tableService.onUIUpdate(this.tt.value);\n        this.rowNode.node['children'] ? this.restoreFocus(this.tt.toggleRowIndex + 1) : this.restoreFocus();\n\n        this.tt.onNodeExpand.emit({\n            originalEvent: event,\n            node: this.rowNode.node\n        });\n    }\n\n    collapse(event: Event) {\n        this.rowNode.node['expanded'] = false;\n\n        this.tt.updateSerializedValue();\n        this.tt.tableService.onUIUpdate(this.tt.value);\n\n        this.tt.onNodeCollapse.emit({ originalEvent: event, node: this.rowNode.node });\n    }\n\n    focusRowChange(firstFocusableRow, currentFocusedRow, lastVisibleDescendant?) {\n        firstFocusableRow.tabIndex = '-1';\n        currentFocusedRow.tabIndex = '0';\n\n        DomHandler.focus(currentFocusedRow);\n    }\n\n    restoreFocus(index?) {\n        this.zone.runOutsideAngular(() => {\n            setTimeout(() => {\n                const container = this.tt.containerViewChild?.nativeElement;\n                const row = DomHandler.findSingle(container, '.p-treetable-tbody').children[<number>index || this.tt.toggleRowIndex];\n                const rows = [...DomHandler.find(container, 'tr')];\n\n                rows &&\n                    rows.forEach((r) => {\n                        if (!row.isSameNode(r)) {\n                            r.tabIndex = -1;\n                        }\n                    });\n\n                if (row) {\n                    row.tabIndex = 0;\n                    row.focus();\n                }\n            }, 25);\n        });\n    }\n}\n\n@Component({\n    selector: 'p-treeTableToggler',\n    template: `\n        <button\n            type=\"button\"\n            class=\"p-treetable-toggler p-link\"\n            (click)=\"onClick($event)\"\n            tabindex=\"-1\"\n            pRipple\n            [ngStyle]=\"{\n                visibility: rowNode.node.leaf === false || (rowNode.node.children && rowNode.node.children.length) ? 'visible' : 'hidden',\n                'margin-left': rowNode.level * 16 + 'px'\n            }\"\n            [attr.data-pc-section]=\"'rowtoggler'\"\n            [attr.data-pc-group-section]=\"'rowactionbutton'\"\n            [attr.aria-label]=\"toggleButtonAriaLabel\"\n        >\n            <ng-container *ngIf=\"!tt.togglerIconTemplate\">\n                <ChevronDownIcon *ngIf=\"rowNode.node.expanded\" [attr.aria-hidden]=\"true\" />\n                <ChevronRightIcon *ngIf=\"!rowNode.node.expanded\" [attr.aria-hidden]=\"true\" />\n            </ng-container>\n            <ng-template *ngTemplateOutlet=\"tt.togglerIconTemplate; context: { $implicit: rowNode.node.expanded }\"></ng-template>\n        </button>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TreeTableToggler {\n    @Input() rowNode: any;\n\n    constructor(public tt: TreeTable, private config: PrimeNGConfig) {}\n\n    get toggleButtonAriaLabel() {\n        return this.config.translation ? (this.rowNode.expanded ? this.config.translation.aria.collapseRow : this.config.translation.aria.expandRow) : undefined;\n    }\n\n    onClick(event: Event) {\n        this.rowNode.node.expanded = !this.rowNode.node.expanded;\n\n        if (this.rowNode.node.expanded) {\n            this.tt.onNodeExpand.emit({\n                originalEvent: event,\n                node: this.rowNode.node\n            });\n        } else {\n            this.tt.onNodeCollapse.emit({\n                originalEvent: event,\n                node: this.rowNode.node\n            });\n        }\n\n        this.tt.updateSerializedValue();\n        this.tt.tableService.onUIUpdate(this.tt.value);\n\n        event.preventDefault();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, PaginatorModule, RippleModule, ScrollerModule, SpinnerIcon, ArrowDownIcon, ArrowUpIcon, SortAltIcon, SortAmountUpAltIcon, SortAmountDownIcon, CheckIcon, MinusIcon, ChevronDownIcon, ChevronRightIcon],\n    exports: [\n        TreeTable,\n        SharedModule,\n        TreeTableToggler,\n        TTSortableColumn,\n        TTSortIcon,\n        TTResizableColumn,\n        TTRow,\n        TTReorderableColumn,\n        TTSelectableRow,\n        TTSelectableRowDblClick,\n        TTContextMenuRow,\n        TTCheckbox,\n        TTHeaderCheckbox,\n        TTEditableColumn,\n        TreeTableCellEditor,\n        ScrollerModule\n    ],\n    declarations: [\n        TreeTable,\n        TreeTableToggler,\n        TTScrollableView,\n        TTBody,\n        TTSortableColumn,\n        TTSortIcon,\n        TTResizableColumn,\n        TTRow,\n        TTReorderableColumn,\n        TTSelectableRow,\n        TTSelectableRowDblClick,\n        TTContextMenuRow,\n        TTCheckbox,\n        TTHeaderCheckbox,\n        TTEditableColumn,\n        TreeTableCellEditor\n    ]\n})\nexport class TreeTableModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;MAkEa,gBAAgB,CAAA;AACjB,IAAA,UAAU,GAAG,IAAI,OAAO,EAAgC,CAAC;AACzD,IAAA,eAAe,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC,IAAA,iBAAiB,GAAG,IAAI,OAAO,EAAO,CAAC;AACvC,IAAA,cAAc,GAAG,IAAI,OAAO,EAAO,CAAC;AACpC,IAAA,kBAAkB,GAAG,IAAI,OAAO,EAAO,CAAC;AAEhD,IAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;AAC7C,IAAA,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;AACvD,IAAA,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;AAC3D,IAAA,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;AACrD,IAAA,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AAE7D,IAAA,MAAM,CAAC,QAAsC,EAAA;AACzC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAClC;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACnC;AAED,IAAA,aAAa,CAAC,IAAS,EAAA;AACnB,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACrC;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnC;AAED,IAAA,oBAAoB,CAAC,KAAa,EAAA;AAC9B,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACvC;uGA/BQ,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAAhB,gBAAgB,EAAA,CAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;;AAkCX;;;AAGG;MA6JU,SAAS,CAAA;AAwsBY,IAAA,QAAA,CAAA;AAClB,IAAA,QAAA,CAAA;AACD,IAAA,EAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACA,IAAA,IAAA,CAAA;AACA,IAAA,YAAA,CAAA;AACA,IAAA,aAAA,CAAA;AACA,IAAA,MAAA,CAAA;AA9sBX;;;AAGG;AACM,IAAA,OAAO,CAAoB;AACpC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACqC,IAAA,UAAU,CAAsB;AACxE;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;IACqC,cAAc,GAAY,IAAI,CAAC;AACvE;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;AACoC,IAAA,IAAI,CAAqB;AAChE;;;AAGG;IACoC,KAAK,GAAW,CAAC,CAAC;AACzD;;;AAGG;IACoC,SAAS,GAAW,CAAC,CAAC;AAC7D;;;AAGG;AACM,IAAA,kBAAkB,CAAoB;AAC/C;;;AAGG;IACqC,mBAAmB,GAAY,IAAI,CAAC;AAC5E;;;AAGG;IACM,iBAAiB,GAA8B,QAAQ,CAAC;AACjE;;;AAGG;AACM,IAAA,mBAAmB,CAAqB;AACjD;;;AAGG;AACM,IAAA,yBAAyB,CAAgF;AAClH;;;AAGG;IACM,yBAAyB,GAAW,+BAA+B,CAAC;AAC7E;;;AAGG;AACqC,IAAA,qBAAqB,CAAsB;AACnF;;;AAGG;AACqC,IAAA,sBAAsB,CAAsB;AACpF;;;AAGG;IACqC,iBAAiB,GAAY,IAAI,CAAC;AAC1E;;;AAGG;IACqC,aAAa,GAAY,IAAI,CAAC;AACtE;;;AAGG;IACoC,gBAAgB,GAAW,CAAC,CAAC;AACpE;;;AAGG;IACM,QAAQ,GAA0B,QAAQ,CAAC;AACpD;;;AAGG;IACqC,eAAe,GAAY,IAAI,CAAC;AACxE;;;AAGG;AACqC,IAAA,UAAU,CAAsB;AACxE;;;AAGG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;AAGG;AACM,IAAA,oBAAoB,CAAM;AACnC;;;AAGG;IACM,wBAAwB,GAAW,UAAU,CAAC;AACvD;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACqC,gBAAgB,GAAwB,KAAK,CAAC;AACtF;;;AAGG;IACM,kBAAkB,GAAW,YAAY,CAAC;AACnD;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,OAAO,CAAsB;AACrE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;AACqC,IAAA,UAAU,CAAsB;AACxE;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACoC,IAAA,qBAAqB,CAAqB;AACjF;;;AAGG;AACM,IAAA,oBAAoB,CAA8B;AAC3D;;;AAGG;IACoC,kBAAkB,GAAW,GAAG,CAAC;AACxE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,aAAa,CAA8C;AACpE;;;AAGG;AACqC,IAAA,gBAAgB,CAAsB;AAC9E;;;AAGG;IACM,gBAAgB,GAAW,KAAK,CAAC;AAC1C;;;AAGG;AACqC,IAAA,kBAAkB,CAAsB;AAChF;;;AAGG;AACM,IAAA,WAAW,CAAM;AAC1B;;;AAGG;IACM,UAAU,GAAa,CAAC,KAAa,EAAE,IAAS,KAAK,IAAI,CAAC;AACnE;;;AAGG;IACM,OAAO,GAAgD,EAAE,CAAC;AACnE;;;AAGG;AACM,IAAA,kBAAkB,CAAuB;AAClD;;;AAGG;IACoC,WAAW,GAAW,GAAG,CAAC;AACjE;;;AAGG;IACM,UAAU,GAAW,SAAS,CAAC;AACxC;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACH,IAAA,IAAa,YAAY,GAAA;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC;KAC7B;IACD,IAAI,YAAY,CAAC,GAAW,EAAA;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAC9D;AACD;;;AAGG;AACH,IAAA,IAAa,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;IACD,IAAI,SAAS,CAAC,GAA8B,EAAA;AACxC,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;KACzB;AACD;;;;AAIG;AACH,IAAA,IAAa,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;IACD,IAAI,SAAS,CAAC,GAAW,EAAA;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;KACzB;AACD;;;;AAIG;AACH,IAAA,IAAa,aAAa,GAAA;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;KAC9B;IACD,IAAI,aAAa,CAAC,GAAkC,EAAA;AAChD,QAAA,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;KAC7B;AACD;;;;AAIG;AACH,IAAA,IAAa,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;IACD,IAAI,SAAS,CAAC,GAAQ,EAAA;AAClB,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;KACzB;AACD;;;;AAIG;AACH,IAAA,IAAa,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;IACD,IAAI,KAAK,CAAC,GAAgC,EAAA;AACtC,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;KACrB;AACD;;;;;AAKG;AACH,IAAA,IAAa,gBAAgB,GAAA;QACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC;KACjC;IACD,IAAI,gBAAgB,CAAC,GAAW,EAAA;AAC5B,QAAA,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC;AAC7B,QAAA,OAAO,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;KAC5G;AACD;;;AAGG;AACH,IAAA,IAAa,aAAa,GAAA;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;KAC9B;IACD,IAAI,aAAa,CAAC,KAAU,EAAA;AACxB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACtD;AACD;;;;AAIG;AACO,IAAA,eAAe,GAAmE,IAAI,YAAY,EAAoD,CAAC;AACjK;;;;AAIG;AACO,IAAA,0BAA0B,GAAgC,IAAI,YAAY,EAAiB,CAAC;AACtG;;;;AAIG;AACO,IAAA,QAAQ,GAAuC,IAAI,YAAY,EAAwB,CAAC;AAClG;;;;AAIG;AACO,IAAA,YAAY,GAA2C,IAAI,YAAY,EAA4B,CAAC;AAC9G;;;;AAIG;AACO,IAAA,cAAc,GAA6C,IAAI,YAAY,EAA8B,CAAC;AACpH;;;;AAIG;AACO,IAAA,MAAM,GAA0C,IAAI,YAAY,EAA2B,CAAC;AACtG;;;;AAIG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;;AAIG;AACO,IAAA,UAAU,GAAyC,IAAI,YAAY,EAA0B,CAAC;AACxG;;;;AAIG;AACO,IAAA,YAAY,GAAqC,IAAI,YAAY,EAAsB,CAAC;AAClG;;;;AAIG;AACO,IAAA,WAAW,GAA0C,IAAI,YAAY,EAA2B,CAAC;AAC3G;;;;AAIG;AACO,IAAA,YAAY,GAA8C,IAAI,YAAY,EAA+B,CAAC;AACpH;;;;AAIG;AACO,IAAA,YAAY,GAAgC,IAAI,YAAY,EAAiB,CAAC;AACxF;;;;AAIG;AACO,IAAA,cAAc,GAA6C,IAAI,YAAY,EAA8B,CAAC;AACpH;;;;AAIG;AACO,IAAA,mBAAmB,GAAkD,IAAI,YAAY,EAAmC,CAAC;AACnI;;;;AAIG;AACO,IAAA,sBAAsB,GAAqD,IAAI,YAAY,EAAsC,CAAC;AAC5I;;;;AAIG;AACO,IAAA,UAAU,GAAqC,IAAI,YAAY,EAAsB,CAAC;AAChG;;;;AAIG;AACO,IAAA,cAAc,GAAqC,IAAI,YAAY,EAAsB,CAAC;AACpG;;;;AAIG;AACO,IAAA,YAAY,GAAqC,IAAI,YAAY,EAAsB,CAAC;AAClG;;;;AAIG;AACO,IAAA,mBAAmB,GAAsB,IAAI,YAAY,EAAE,CAAC;AAE9C,IAAA,kBAAkB,CAAuB;AAEtC,IAAA,qBAAqB,CAAuB;AAEtC,IAAA,2BAA2B,CAAuB;AAEhD,IAAA,6BAA6B,CAAuB;AAEnE,IAAA,cAAc,CAAuB;AAE5B,IAAA,mBAAmB,CAAuB;AAEpC,IAAA,yBAAyB,CAAuB;AAEnD,IAAA,SAAS,CAAqC;IAE9E,MAAM,GAAgC,EAAE,CAAC;IAEzC,iBAAiB,GAAW,EAAE,CAAC;AAE/B,IAAA,cAAc,CAAM;AAEpB,IAAA,eAAe,CAA2B;IAE1C,aAAa,GAAW,CAAC,CAAC;AAE1B,IAAA,cAAc,CAAgC;AAE9C,IAAA,UAAU,CAA4B;IAEtC,UAAU,GAAW,CAAC,CAAC;AAEvB,IAAA,aAAa,CAAkB;AAE/B,IAAA,aAAa,CAAM;AAEnB,IAAA,gBAAgB,CAA6B;AAE7C,IAAA,eAAe,CAA6B;AAE5C,IAAA,cAAc,CAA6B;AAE3C,IAAA,YAAY,CAA6B;AAEzC,IAAA,cAAc,CAA6B;AAE3C,IAAA,eAAe,CAA6B;AAE5C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,qBAAqB,CAA6B;AAElD,IAAA,sBAAsB,CAA6B;AAEnD,IAAA,6BAA6B,CAA6B;AAE1D,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,sBAAsB,CAA6B;AAEnD,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,8BAA8B,CAA6B;AAE3D,IAAA,gCAAgC,CAA6B;AAE7D,IAAA,gBAAgB,CAA6B;AAE7C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,0BAA0B,CAA6B;AAEvD,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,kCAAkC,CAA6B;AAE/D,IAAA,iCAAiC,CAA6B;AAE9D,IAAA,qCAAqC,CAA6B;AAElE,IAAA,iCAAiC,CAA6B;AAE9D,IAAA,kBAAkB,CAAmB;AAErC,IAAA,gBAAgB,CAAmB;AAEnC,IAAA,iBAAiB,CAAmB;AAEpC,IAAA,aAAa,CAAkB;AAE/B,IAAA,YAAY,CAAmB;AAE/B,IAAA,iCAAiC,CAAoB;AAErD,IAAA,UAAU,CAAM;IAEhB,YAAY,GAAQ,EAAE,CAAC;AAEvB,IAAA,UAAU,CAAoB;AAE9B,IAAA,WAAW,CAAoB;AAE/B,IAAA,eAAe,CAAyB;AAExC,IAAA,gBAAgB,CAAyB;AAEzC,IAAA,gBAAgB,CAAoB;AAEpC,IAAA,oBAAoB,CAAe;AAEnC,IAAA,WAAW,CAAoB;AAE/B,IAAA,cAAc,CAAmB;IAEjC,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACzD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACvD,SAAA;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,eAAe;AAChB,oBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC3C,MAAM;AAEV,gBAAA,KAAK,gBAAgB;AACjB,oBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC5C,MAAM;AAEV,gBAAA,KAAK,uBAAuB;AACxB,oBAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnD,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,gBAAgB;AACjB,oBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC5C,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,wBAAwB;AACzB,oBAAA,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpD,MAAM;AAEV,gBAAA,KAAK,0BAA0B;AAC3B,oBAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtD,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,oBAAoB;AACrB,oBAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAChD,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,4BAA4B;AAC7B,oBAAA,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxD,MAAM;AAEV,gBAAA,KAAK,2BAA2B;AAC5B,oBAAA,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvD,MAAM;AAEV,gBAAA,KAAK,+BAA+B;AAChC,oBAAA,IAAI,CAAC,qCAAqC,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC3D,MAAM;AAEV,gBAAA,KAAK,2BAA2B;AAC5B,oBAAA,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvD,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,WAAA,CAC8B,QAAkB,EACpC,QAAmB,EACpB,EAAc,EACd,EAAqB,EACrB,IAAY,EACZ,YAA8B,EAC9B,aAA4B,EAC5B,MAAqB,EAAA;QAPF,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACpC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACpB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACrB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACZ,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAkB;QAC9B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QAC5B,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAC5B;AAEJ,IAAA,WAAW,CAAC,YAA2B,EAAA;QACnC,IAAI,YAAY,CAAC,KAAK,EAAE;YACpB,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC;AAE9C,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACZ,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEzD,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS;oBAAE,IAAI,CAAC,UAAU,EAAE,CAAC;qBAC9D,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,IAAI,CAAC,aAAa;oBAAE,IAAI,CAAC,YAAY,EAAE,CAAC;qBAC3E,IAAI,IAAI,CAAC,SAAS,EAAE;;oBAErB,IAAI,CAAC,OAAO,EAAE,CAAC;AACtB,aAAA;YAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5C,SAAA;QAED,IAAI,YAAY,CAAC,SAAS,EAAE;YACxB,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC;;YAGtD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AAChC,gBAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;oBAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,IAAI,YAAY,CAAC,SAAS,EAAE;YACxB,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC;;YAGtD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AAChC,gBAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;oBAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,IAAI,YAAY,CAAC,aAAa,EAAE;YAC5B,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC;AAC9D,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,EAAE;gBAC9B,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,aAAA;AACJ,SAAA;QAED,IAAI,YAAY,CAAC,SAAS,EAAE;YACxB,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC;AAEtD,YAAA,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE;gBACzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,gBAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;AACzC,aAAA;AACD,YAAA,IAAI,CAAC,iCAAiC,GAAG,KAAK,CAAC;AAClD,SAAA;KACJ;IAED,qBAAqB,GAAA;AACjB,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,SAAS;YAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC;;AACzC,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;KAC7E;AAED,IAAA,cAAc,CAAC,MAA+B,EAAE,KAA2B,EAAE,KAAuB,EAAE,OAA0B,EAAA;AAC5H,QAAA,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AACvB,YAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACpB,gBAAA,IAAI,CAAC,MAAM,GAAkB,MAAM,CAAC;AACpC,gBAAA,MAAM,OAAO,GAAG;AACZ,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,MAAM,EAAE,MAAM;AACd,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,OAAO,EAAE,OAAO,KAAK,MAAM,GAAG,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;iBACxD,CAAC;AACW,gBAAA,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAgB,OAAO,CAAC,CAAC;AAEhE,gBAAA,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAClC,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAU,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AAChF,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC1B,QAAA,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAEzC,YAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,GAAW,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;AACpD,gBAAA,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACnB,gBAAA,IAAI,IAAI,EAAE;AACN,oBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACtB,wBAAA,IAAI,EAAE,IAAI;AACV,wBAAA,MAAM,EAAO,IAAI;AACjB,wBAAA,KAAK,EAAE,CAAC;AACR,wBAAA,OAAO,EAAE,IAAI;AAChB,qBAAA,CAAC,CAAC;AAEH,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACrD,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;AACjC,YAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YACvB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAChC,gBAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;oBAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACxF,iBAAA;AACJ,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAO,IAAI,CAAC,UAAW,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1G,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAA8B,EAAA;AACvC,QAAA,IAAI,CAAC,KAAK,GAAW,KAAK,CAAC,KAAK,CAAC;AACjC,QAAA,IAAI,CAAC,IAAI,GAAW,KAAK,CAAC,IAAI,CAAC;QAE/B,IAAI,IAAI,CAAC,IAAI;YAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;;YAC9D,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAE/B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;AAClB,SAAA,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,SAAA;KACJ;AAED,IAAA,IAAI,CAAC,KAAyB,EAAA;AAC1B,QAAA,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;AAExC,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC/F,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,YAAA,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,EAAE;gBACzC,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,EAAE;YAC9B,IAAI,OAAO,GAAmB,aAAc,CAAC,OAAO,IAAoB,aAAc,CAAC,OAAO,CAAC;YAC/F,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAS,KAAK,CAAC,KAAK,CAAC,CAAC;AAErD,YAAA,IAAI,QAAQ,EAAE;gBACV,IAAI,CAAC,OAAO,EAAE;oBACV,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,KAAK,EAAU,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAEnF,oBAAA,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,EAAE;wBACzC,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,qBAAA;AACJ,iBAAA;AAAM,qBAAA;oBACH,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACxC,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACjC,oBAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAEzB,oBAAA,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,EAAE;wBACzC,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,qBAAA;AACJ,iBAAA;AACY,gBAAA,IAAI,CAAC,aAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAU,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;AACvG,aAAA;YAED,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;YAClC,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACvD,aAAA;iBAAM,IAAI,IAAI,CAAC,KAAK,EAAE;AACnB,gBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAE3B,gBAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;oBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,QAAQ,GAAa;gBACrB,KAAK,EAAE,IAAI,CAAC,SAAS;gBACrB,KAAK,EAAE,IAAI,CAAC,SAAS;aACxB,CAAC;AAEF,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3B,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACnC,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAiB,EAAA;QACvB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACnB,gBAAA,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,IAAI,CAAC,QAAQ;gBACnB,KAAK,EAAU,IAAI,CAAC,SAAS;gBAC7B,KAAK,EAAE,IAAI,CAAC,SAAS;AACxB,aAAA,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;YACH,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AACxB,gBAAA,IAAI,MAAM,GAAG,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACtE,gBAAA,IAAI,MAAM,GAAG,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtE,IAAI,MAAM,GAAG,IAAI,CAAC;AAElB,gBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,qBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC;AACjD,qBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC;qBACjD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ;AAAE,oBAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;oBAClI,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAE7D,gBAAA,OAAO,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;AACnC,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACpB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAsB,CAAC,CAAC;AAC/C,SAAA;KACJ;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACvD,aAAA;iBAAM,IAAI,IAAI,CAAC,KAAK,EAAE;AACnB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAEnC,gBAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;oBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;AAClB,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACb,aAAa,EAAE,IAAI,CAAC,aAAa;AACpC,aAAA,CAAC,CAAC;YACH,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAChD,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAiB,EAAA;QAC/B,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,IAAI,EAAE,IAAI,CAAC,QAAQ;gBACnB,aAAa,EAAE,IAAI,CAAC,aAAa;AACpC,aAAA,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;YACH,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AACxB,gBAAA,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,EAAc,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AAChF,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACpB,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAsB,CAAC,CAAC;AACvD,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAE,KAAoB,EAAE,aAAyB,EAAE,KAAa,EAAA;AAC/F,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;AACtF,YAAA,OAAO,CAAC,CAAC;AACZ,SAAA;AAED,QAAA,IAAI,MAAM,GAAG,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AAClF,QAAA,IAAI,MAAM,GAAG,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;QAClF,IAAI,MAAM,GAAG,IAAI,CAAC;AAElB,QAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;YAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,aAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;YAAE,MAAM,GAAG,CAAC,CAAC;AACjD,aAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;YAAE,MAAM,GAAG,CAAC,CAAC;QACtD,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,MAAM,YAAY,MAAM,EAAE;AACvD,YAAA,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,IAAI,MAAM,EAAE;gBAC1C,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAClG,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,MAAM,IAAI,MAAM,EAAE;AAClB,YAAA,OAAO,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7G,SAAA;QAED,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,GAAW,MAAM,CAAC;KACtD;AAED,IAAA,WAAW,CAAC,KAAa,EAAA;QACrB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;AACjD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE;AACvC,oBAAA,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AAChC,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED,IAAA,QAAQ,CAAC,KAAa,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC;AACrD,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,EAAE;YACrC,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAChD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,EAAE;wBACtC,MAAM,GAAG,IAAI,CAAC;wBACd,MAAM;AACT,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACD,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;KACJ;IAED,sBAAsB,GAAA;QAClB,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,YAAY,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,IAAI;YAC1F,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;SAC7C,CAAC;KACL;AAED,IAAA,cAAc,CAAC,KAA6B,EAAA;AACxC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACjB,GAAG,IAAI,CAAC,sBAAsB,EAAE;AAChC,YAAA,GAAG,KAAK;AACR,YAAA,IAAI,EAAE,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK;AACjC,SAAA,CAAC,CAAC;KACN;AACD;;;AAGG;IACI,cAAc,GAAA;QACjB,IAAI,IAAI,CAAC,aAAa;AAAE,YAAA,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;;YAChD,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;KAClC;AACD;;;;AAIG;AACI,IAAA,oBAAoB,CAAC,KAAa,EAAA;QACrC,IAAI,IAAI,CAAC,mBAAmB,EAAE;AACpB,YAAA,IAAI,CAAC,mBAAoB,CAAC,oBAAoB,CAAS,KAAK,CAAC,CAAC;AACvE,SAAA;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAC1B,YAAA,IAAI,CAAC,mBAAoB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAC/D,SAAA;KACJ;AACD;;;;AAIG;AACI,IAAA,QAAQ,CAAC,OAAwB,EAAA;QACpC,IAAI,IAAI,CAAC,mBAAmB,EAAE;AACpB,YAAA,IAAI,CAAC,mBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD,SAAA;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAC1B,YAAA,IAAI,CAAC,mBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrD,SAAA;KACJ;IAED,OAAO,GAAA;QACH,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC;QAC5C,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;KAC3C;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;AAED,IAAA,mBAAmB,CAAC,KAAiB,EAAA;AACjC,QAAA,IAAI,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC;AACtF,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,KAAK,GAAG,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,UAAU,CAAC;QAC1G,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;AAC5B,QAAA,IAAI,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC;QACtF,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE,qBAAqB,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,qBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC;AACpH,QAAA,IAAI,CAAC,qBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;QAC/D,IAAI,CAAC,qBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,GAAG,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC;QAE9I,IAAI,CAAC,qBAAsB,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;KAClF;IAED,iBAAiB,CAAC,KAAiB,EAAE,MAAW,EAAA;AAC5C,QAAA,IAAI,KAAK,GAAgB,IAAI,CAAC,qBAAsB,CAAC,aAAa,CAAC,UAAU,GAAW,IAAI,CAAC,kBAAkB,CAAC;AAChH,QAAA,IAAI,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;AACrC,QAAA,IAAI,cAAc,GAAG,WAAW,GAAG,KAAK,CAAC;QACzC,IAAI,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;QAE3C,IAAI,WAAW,GAAG,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC1C,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,EAAE;AACjC,gBAAA,IAAI,UAAU,GAAG,MAAM,CAAC,kBAAkB,CAAC;AAC3C,gBAAA,OAAO,CAAC,UAAU,CAAC,YAAY,EAAE;AAC7B,oBAAA,UAAU,GAAG,UAAU,CAAC,kBAAkB,CAAC;AAC9C,iBAAA;AAED,gBAAA,IAAI,UAAU,EAAE;AACZ,oBAAA,IAAI,eAAe,GAAG,UAAU,CAAC,WAAW,GAAG,KAAK,CAAC;oBACrD,IAAI,kBAAkB,GAAG,UAAU,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;oBAEzD,IAAI,cAAc,GAAG,EAAE,IAAI,eAAe,GAAG,QAAQ,CAAC,kBAAkB,CAAC,EAAE;wBACvE,IAAI,IAAI,CAAC,UAAU,EAAE;4BACjB,IAAI,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;AAC3D,4BAAA,IAAI,mBAAmB,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,oCAAoC,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,4BAA4B,CAAC,CAAC;4BAC7K,IAAI,qBAAqB,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,2CAA2C,CAAC,CAAC;4BAC/G,IAAI,qBAAqB,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,2CAA2C,CAAC,CAAC;4BAC/G,IAAI,iBAAiB,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;4BAEjD,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;4BAC/F,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;4BAC7F,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;AAClG,yBAAA;AAAM,6BAAA;4BACH,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,cAAc,GAAG,IAAI,CAAC;AAC3C,4BAAA,IAAI,UAAU,EAAE;gCACZ,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,GAAG,IAAI,CAAC;AACnD,6BAAA;AACJ,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ,EAAE;gBAC3C,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,IAAI,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;AAC3D,oBAAA,IAAI,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,8BAA8B,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,sBAAsB,CAAC,CAAC;oBAC5J,IAAI,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,gCAAgC,CAAC,CAAC;oBAC/F,IAAI,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,gCAAgC,CAAC,CAAC;AAC/F,oBAAA,IAAI,mBAAmB,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,oCAAoC,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,4BAA4B,CAAC,CAAC;oBAC7K,IAAI,qBAAqB,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,2CAA2C,CAAC,CAAC;oBAC/G,IAAI,qBAAqB,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,2CAA2C,CAAC,CAAC;oBAC/G,IAAI,iBAAiB,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEjD,oBAAA,MAAM,wBAAwB,GAAG,MAAM,GAAG,mBAAmB,CAAC,WAAW,GAAG,KAAK,GAAG,cAAc,CAAC;AACnG,oBAAA,MAAM,0BAA0B,GAAG,MAAM,GAAG,qBAAqB,CAAC,WAAW,GAAG,KAAK,GAAG,cAAc,CAAC;oBACvG,MAAM,qBAAqB,GAAG,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,WAAW,IAAI,wBAAwB,CAAC;oBAE7G,IAAI,QAAQ,GAAG,CAAC,SAAsB,EAAE,KAAkB,EAAE,KAAa,EAAE,qBAA8B,KAAI;wBACzG,IAAI,SAAS,IAAI,KAAK,EAAE;4BACpB,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,qBAAqB,GAAG,KAAK,GAAG,UAAU,CAAC,uBAAuB,CAAC,cAAc,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC;4BAC3H,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpC,yBAAA;AACL,qBAAC,CAAC;oBAEF,QAAQ,CAAC,cAAc,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,qBAAqB,CAAC,CAAC;oBAC/F,QAAQ,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,0BAA0B,EAAE,qBAAqB,CAAC,CAAC;oBACrG,QAAQ,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,0BAA0B,EAAE,qBAAqB,CAAC,CAAC;oBAErG,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;oBACpF,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;oBAClF,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;AACvF,iBAAA;AAAM,qBAAA;oBACU,IAAI,CAAC,cAAe,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC;oBAC5H,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,cAAc,GAAG,IAAI,CAAC;oBAC3C,IAAI,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC;AACvD,oBAAA,IAAI,CAAC,kBAAmB,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,cAAc,GAAG,IAAI,CAAC;AAC3F,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AAClB,gBAAA,OAAO,EAAE,MAAM;AACf,gBAAA,KAAK,EAAE,KAAK;AACf,aAAA,CAAC,CAAC;AACN,SAAA;QAEA,IAAI,CAAC,qBAAoC,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QAChF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE,qBAAqB,CAAC,CAAC;KACzF;AAED,IAAA,wBAAwB,CAAC,MAAW,EAAA;AAChC,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC;YAClC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,6BAA6B,CAAC,EAAE;AAC1E,gBAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC;AACjC,aAAA;AAED,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAA4B,EAAE,iBAAmC,EAAE,cAAgC,EAAE,eAAiC,EAAA;AACjJ,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAEpF,YAAA,IAAI,QAAQ,EAAE;gBACV,IAAI,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAS,iBAAiB,CAAC,CAAC;AACvD,gBAAA,IAAI,OAAO,GAAG,GAAG,CAAC,kBAAkB,CAAC;gBACvB,GAAI,CAAC,KAAK,CAAC,KAAK,GAAG,cAAc,GAAG,IAAI,CAAC;gBAEvD,IAAI,OAAO,IAAI,eAAe,EAAE;oBACd,OAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,GAAG,IAAI,CAAC;AAC/D,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,mEAAmE,CAAC;AAC7E,aAAA;AACJ,SAAA;KACJ;IAED,iBAAiB,CAAC,KAAgB,EAAE,aAAkB,EAAA;AAClD,QAAA,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,0BAA0B,CAAC,IAAI,CAAC,2BAA2B,EAAE,aAAa,CAAC,CAAC;AAC/G,QAAA,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,6BAA6B,EAAE,aAAa,CAAC,CAAC;AACnH,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAC7B,KAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAClD;IAED,iBAAiB,CAAC,KAAgB,EAAE,UAAe,EAAA;QAC/C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,EAAE;YAC7D,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,YAAA,IAAI,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;YACnF,IAAI,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAExD,YAAA,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,EAAE;gBAClC,IAAI,UAAU,GAAG,gBAAgB,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC;gBAC9D,IAAI,SAAS,GAAG,eAAe,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC;gBAC3D,IAAI,YAAY,GAAG,gBAAgB,CAAC,IAAI,GAAG,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC;gBAEzD,IAAI,CAAC,2BAA4B,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,IAAY,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;gBACrJ,IAAI,CAAC,6BAA8B,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,GAAG,eAAe,CAAC,GAAG,GAAG,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;AAEvJ,gBAAA,IAAI,KAAK,CAAC,KAAK,GAAG,YAAY,EAAE;oBACf,IAAI,CAAC,2BAA4B,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,GAAG,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAS,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;oBACvJ,IAAI,CAAC,6BAA8B,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,GAAG,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAS,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AACtK,oBAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;AACzB,iBAAA;AAAM,qBAAA;oBACU,IAAI,CAAC,2BAA4B,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,CAAS,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;oBAC9H,IAAI,CAAC,6BAA8B,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,CAAS,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC7I,oBAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AAC1B,iBAAA;gBAEY,IAAI,CAAC,2BAA4B,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;gBACxE,IAAI,CAAC,6BAA8B,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1F,aAAA;AAAM,iBAAA;AACG,gBAAA,KAAM,CAAC,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;AACjD,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAgB,EAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,aAAa,EAAE;YAC/C,KAAK,CAAC,cAAc,EAAE,CAAC;YACV,IAAI,CAAC,2BAA4B,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;YACvE,IAAI,CAAC,6BAA8B,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACzF,SAAA;KACJ;IAED,YAAY,CAAC,KAAgB,EAAE,UAAe,EAAA;QAC1C,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,SAAS,GAAG,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;YACvF,IAAI,SAAS,GAAG,UAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;AAC/E,YAAA,IAAI,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC;AACvC,YAAA,IAAI,SAAS,KAAK,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,MAAM,SAAS,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,EAAE;gBACpI,SAAS,GAAG,KAAK,CAAC;AACrB,aAAA;YAED,IAAI,SAAS,IAAI,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;AAC/D,gBAAA,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;AAC7B,aAAA;AAED,YAAA,IAAI,SAAS,IAAI,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE;AAChE,gBAAA,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;AAC7B,aAAA;AAED,YAAA,IAAI,SAAS,EAAE;gBACX,WAAW,CAAC,YAAY,CAAQ,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAEpE,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACnB,oBAAA,SAAS,EAAE,SAAS;AACpB,oBAAA,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,iBAAA,CAAC,CAAC;AACN,aAAA;YAEY,IAAI,CAAC,2BAA4B,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;YACvE,IAAI,CAAC,6BAA8B,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACrF,YAAA,IAAI,CAAC,aAAqB,CAAC,SAAS,GAAG,KAAK,CAAC;AAC9C,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC5B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAU,EAAA;QACrB,IAAI,UAAU,GAAiB,KAAK,CAAC,aAAa,CAAC,MAAO,CAAC,QAAQ,CAAC;QACpE,IAAI,UAAU,IAAI,OAAO,IAAI,UAAU,IAAI,QAAQ,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE;YACxI,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;AAC9C,YAAA,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAO,OAAQ,CAAC,IAAI,CAAC,CAAC;AACpD,YAAA,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACpE,YAAA,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAiB,OAAO,CAAC,IAAK,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;AAEhI,YAAA,IAAI,aAAa,EAAE;AACf,gBAAA,IAAI,aAAa,GAAkB,KAAK,CAAC,aAAa,CAAC;gBACvD,IAAI,OAAO,GAAG,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC;gBAE7D,IAAI,QAAQ,IAAI,OAAO,EAAE;AACrB,oBAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;AAC9B,wBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,wBAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AACvB,wBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,qBAAA;AAAM,yBAAA;wBACH,IAAI,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBAC7D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAkB,EAAE,CAAS,KAAK,CAAC,IAAI,cAAc,CAAC,CAAC;wBAChG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,wBAAA,IAAI,YAAY,EAAE;AACd,4BAAA,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,yBAAA;AACJ,qBAAA;oBAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,IAAI,EAAiB,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACpH,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;AAC9B,wBAAA,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;wBAC/B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACxC,wBAAA,IAAI,YAAY,EAAE;AACd,4BAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AACvB,4BAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACvC,yBAAA;AACJ,qBAAA;AAAM,yBAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE;AACvC,wBAAA,IAAI,OAAO,EAAE;4BACT,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;AAC1C,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACrB,4BAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC1B,yBAAA;AAED,wBAAA,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;wBACpD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,wBAAA,IAAI,YAAY,EAAE;AACd,4BAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACvC,yBAAA;AACJ,qBAAA;AAED,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAQ,KAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;AACjI,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,EAAE;AACjC,oBAAA,IAAI,QAAQ,EAAE;AACV,wBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,wBAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;wBACvB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC1C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,IAAI,EAAiB,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACpH,qBAAA;AAAM,yBAAA;AACH,wBAAA,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;wBAC/B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,wBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AACvH,wBAAA,IAAI,YAAY,EAAE;AACd,4BAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AACvB,4BAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACvC,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AAAM,qBAAA,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU,EAAE;AAC1C,oBAAA,IAAI,QAAQ,EAAE;wBACV,IAAI,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBAC7D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAkB,EAAE,CAAS,KAAK,CAAC,IAAI,cAAc,CAAC,CAAC;wBAChG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC1C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AAClG,wBAAA,IAAI,YAAY,EAAE;AACd,4BAAA,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,yBAAA;AACJ,qBAAA;AAAM,yBAAA;wBACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBACtF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,wBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AACvH,wBAAA,IAAI,YAAY,EAAE;AACd,4BAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACvC,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;AACzC,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,KAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B;AAED,IAAA,mBAAmB,CAAC,KAAU,EAAA;QAC1B,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAEhC,YAAA,IAAI,IAAI,CAAC,wBAAwB,KAAK,UAAU,EAAE;AAC9C,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,gBAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AAC3C,gBAAA,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACzC,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,wBAAwB,KAAK,OAAO,EAAE;AAClD,gBAAA,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;gBAC9C,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;gBAEvG,IAAI,CAAC,QAAQ,EAAE;AACX,oBAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;AAC9B,wBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,wBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,qBAAA;AAAM,yBAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE;AACvC,wBAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;wBACxB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,qBAAA;AAED,oBAAA,IAAI,YAAY,EAAE;AACd,wBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACvC,qBAAA;AACJ,iBAAA;gBAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AAC3C,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACrF,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,sBAAsB,CAAC,KAAU,EAAA;;QAE7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;AACtC,QAAA,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;AAC9C,QAAA,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAErC,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACzC,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;gBACtB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,aAAA;YACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAClE,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;gBACtB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAChD,aAAA;YACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAChE,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;KACzC;IAED,uBAAuB,CAAC,KAAY,EAAE,KAAc,EAAA;;QAEhD,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;AAEpD,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEtB,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACrB,YAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC;QAC9C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;AAEtC,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;KAC9E;AAED,IAAA,SAAS,CAAC,OAAgB,EAAA;QACtB,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC;AAE5C,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,gBAAA,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AACnB,oBAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9C,iBAAA;AACJ,aAAA;AACJ,SAAA;AAAM,aAAA;;AAEH,YAAA,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,gBAAA,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AACnB,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACrC,iBAAA;gBACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACrD,aAAA;AACJ,SAAA;KACJ;IAED,oBAAoB,CAAC,IAAmB,EAAE,MAAe,EAAA;;QAErD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACvC,IAAI,kBAAkB,GAAW,CAAC,CAAC;YACnC,IAAI,oBAAoB,GAAY,KAAK,CAAC;YAC1C,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;AAEvG,YAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC7B,gBAAA,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AAAE,oBAAA,kBAAkB,EAAE,CAAC;qBAC5C,IAAI,KAAK,CAAC,eAAe;oBAAE,oBAAoB,GAAG,IAAI,CAAC;AAC/D,aAAA;YAED,IAAI,MAAM,IAAI,kBAAkB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACtD,gBAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACpD,gBAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAC7B,gBAAA,IAAI,YAAY,EAAE;AACd,oBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACvC,iBAAA;AACJ,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,MAAM,EAAE;oBACT,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC5C,IAAI,KAAK,IAAI,CAAC,EAAE;wBACZ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AAE7E,wBAAA,IAAI,YAAY,EAAE;AACd,4BAAA,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,oBAAoB,KAAK,kBAAkB,GAAG,CAAC,IAAI,kBAAkB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAAE,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;;AAC3H,oBAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AACrC,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC7C,SAAA;KACJ;IAED,sBAAsB,CAAC,IAAmB,EAAE,MAAe,EAAA;;QAEvD,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;AAEvG,QAAA,IAAI,MAAM,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE;AACvB,YAAA,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACpD,YAAA,IAAI,YAAY,EAAE;AACd,gBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACvC,aAAA;AACJ,SAAA;AAAM,aAAA,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AAC7E,YAAA,IAAI,YAAY,EAAE;AACd,gBAAA,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAC7B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACvC,YAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC7B,gBAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC9C,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,IAAmB,EAAA;;AAE1B,QAAA,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACxB,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,gBAAA,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;AAChC,oBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1B,iBAAA;AAAM,qBAAA;AACH,oBAAA,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,SAAS,CAAC;AACjG,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;oBAAE,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;oBAC1E,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,cAAc,CAAC,IAAI,EAAA;AACf,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC;KACtH;AAED,IAAA,qBAAqB,CAAC,IAAI,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC;KAC7H;AAED,IAAA,OAAO,CAAC,IAAI,EAAA;QACR,OAAO,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KACrH;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAC9C,QAAA,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACxB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxC,aAAA;YAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACrD,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,sBAAsB,CAAC,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC;AAC3D,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;KACzC;IAED,aAAa,CAAC,IAAI,EAAE,KAAK,EAAA;AACrB,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;AACrF,SAAA;AAAM,aAAA;YACH,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACvC,YAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC7B,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACpC,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,CAAC,IAAI,EAAE,KAAK,EAAA;QACnB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,oBAAoB,GAAG,KAAK,CAAC;AAEjC,QAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC7B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO;AAAE,gBAAA,iBAAiB,EAAE,CAAC;iBAC/G,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc;gBAAE,oBAAoB,GAAG,IAAI,CAAC;AAC3I,SAAA;QAED,IAAI,KAAK,IAAI,iBAAiB,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACrD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;AACrF,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,KAAK,EAAE;gBACR,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,aAAA;AAED,YAAA,IAAI,oBAAoB,KAAK,iBAAiB,GAAG,CAAC,IAAI,iBAAiB,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC;;gBAChL,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;AAC3F,SAAA;AAED,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,IAAS,EAAA;AAC1B,QAAA,IAAI,KAAK,GAAW,CAAC,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,gBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;oBACtC,KAAK,GAAG,CAAC,CAAC;oBACV,MAAM;AACT,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,qBAAqB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,aAAa,KAAK,QAAQ,CAAC;KAC1C;IAED,uBAAuB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,aAAa,KAAK,UAAU,CAAC;KAC5C;IAED,MAAM,CAAC,KAAoB,EAAE,KAAoB,EAAA;AAC7C,QAAA,OAAO,IAAI,CAAC,kBAAkB,KAAK,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KAC7I;AAED,IAAA,MAAM,CAAC,KAAwB,EAAE,KAAa,EAAE,SAAiB,EAAA;QAC7D,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;AAChE,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;YACjC,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,SAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;KACxB;IAED,YAAY,CAAC,KAAa,EAAE,SAAiB,EAAA;QACzC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;KAC3C;AAED,IAAA,aAAa,CAAC,MAAW,EAAA;AACrB,QAAA,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE;AACzC,YAAA,IAAI,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;AAAE,gBAAA,OAAO,IAAI,CAAC;;AACvH,gBAAA,OAAO,KAAK,CAAC;AACrB,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;IAED,OAAO,GAAA;QACH,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACvD,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gBACb,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;AACnB,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC1B,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1D,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,uBAAuB,CAAC;AAC5B,gBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACxB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,kBAAkB;AAAE,wBAAA,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;;wBAC5I,uBAAuB,GAAG,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,OAAO,CAAC;AAC1E,iBAAA;AAED,gBAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AACxB,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC;gBAClD,IAAI,cAAc,GAAG,KAAK,CAAC;AAE3B,gBAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACzB,oBAAA,IAAI,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;oBAC3B,IAAI,UAAU,GAAG,IAAI,CAAC;oBACtB,IAAI,WAAW,GAAG,KAAK,CAAC;AACxB,oBAAA,IAAI,iBAAiB,CAAC;AAEtB,oBAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AAC3B,wBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,QAAQ,EAAE;4BACxD,IAAI,UAAU,GAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;4BACpD,IAAI,WAAW,GAAG,IAAI,CAAC;AACvB,4BAAA,IAAI,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC;AACnC,4BAAA,IAAI,eAAe,GAAG,UAAU,CAAC,SAAS,IAAI,YAAY,CAAC;4BAC3D,IAAI,gBAAgB,GAAS,IAAI,CAAC,aAAc,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;4BAC1E,iBAAiB,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,CAAC;4BACjF,IACI,CAAC,YAAY,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;iCAC3H,CAAC,YAAY,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAChI;gCACE,UAAU,GAAG,KAAK,CAAC;AACtB,6BAAA;4BAED,IAAI,CAAC,UAAU,EAAE;gCACb,MAAM;AACT,6BAAA;AACJ,yBAAA;AACJ,qBAAA;oBAED,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,IAAI,uBAAuB,EAAE;AACnE,wBAAA,IAAI,iBAAiB,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;wBACxC,IAAI,WAAW,GAAG,SAAS,CAAC;wBAC5B,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;AAC/C,wBAAA,IAAI,gBAAgB,GAAS,IAAI,CAAC,aAAc,CAAC,OAAO,CAAO,IAAI,CAAC,OAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;AAClG,wBAAA,iBAAiB,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,uBAAuB,EAAE,CAAC;wBAE1G,IACI,CAAC,YAAY,KAAK,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;6BAC5I,CAAC,YAAY,KAAK,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,CAAC,EACjJ;4BACE,WAAW,GAAG,IAAI,CAAC;4BACnB,QAAQ,GAAG,iBAAiB,CAAC;AAChC,yBAAA;AACJ,qBAAA;oBAED,IAAI,OAAO,GAAG,UAAU,CAAC;AACzB,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACxB,wBAAA,OAAO,GAAG,UAAU,IAAI,WAAW,CAAC;AACvC,qBAAA;AAED,oBAAA,IAAI,OAAO,EAAE;AACT,wBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrC,qBAAA;AAED,oBAAA,cAAc,GAAG,cAAc,IAAI,CAAC,UAAU,IAAI,WAAW,KAAK,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AACvK,iBAAA;gBAED,IAAI,CAAC,cAAc,EAAE;AACjB,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,iBAAA;gBAED,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3G,iBAAA;AACJ,aAAA;AACD,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAEf,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC;AAEvD,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,YAAA,aAAa,EAAE,aAAa;AAC/B,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QAC5C,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,SAAA;KACJ;IAED,iBAAiB,CAAC,IAAmB,EAAE,iBAAsB,EAAA;AACzD,QAAA,IAAI,IAAI,EAAE;YACN,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AACpC,gBAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACnB,gBAAA,KAAK,IAAI,SAAS,IAAI,UAAU,EAAE;AAC9B,oBAAA,IAAI,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;oBACrC,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,iBAAiB,CAAC,EAAE;wBACxD,OAAO,GAAG,IAAI,CAAC;AACf,wBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACrC,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACJ,SAAA;KACJ;IAED,eAAe,CAAC,IAAmB,EAAE,aAAqC,EAAA;AACtE,QAAA,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,uBAAuB,EAAE,GAAQ,aAAa,CAAC;QAC/G,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,SAAS,GAAG,CAAC,KAAa,KAAK,gBAAgB,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,WAAW,EAAU,IAAI,CAAC,YAAY,CAAC,CAAC;AAE9I,QAAA,OAAO,GAAG,uBAAuB,EAAE,MAAM,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC,iBAAiB,KAAK,SAAS,CAAC,iBAAiB,CAAC,KAAK,IAAI,iBAAiB,CAAC,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;AAElL,QAAA,IAAI,CAAC,OAAO,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;YACtD,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,uBAAuB,EAAE,CAAC,IAAI,OAAO,CAAC;AAC5I,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,UAAU,CAAC,IAAmB,EAAA;QAC1B,OAAO,IAAI,CAAC,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;KACjF;IAED,SAAS,GAAA;QACL,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,QAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBACnC,KAAK,GAAG,KAAK,CAAC;gBACd,MAAM;AACT,aAAA;AACJ,SAAA;QAED,OAAO,CAAC,KAAK,CAAC;KACjB;AACD;;;AAGG;IACI,KAAK,GAAA;AACR,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAE/B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AAElB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAEf,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACvD,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5D,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,IAAS,EAAE,IAAS,EAAE,KAAa,EAAA;AACjD,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACnC;IAED,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,sBAAsB,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;KACrG;IAED,wBAAwB,GAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;AAC/E,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;oBACzE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;AAC3D,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;AACvF,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,IAAI,CAAC,0BAA0B,EAAE,CAAC;AACrC,iBAAA;AAED,gBAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAClC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;AAh7DQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAS,kBAwsBN,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAxsBX,SAAS,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EA8BE,gBAAgB,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAKhB,gBAAgB,CAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAKhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,gBAAgB,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAKhB,eAAe,CAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAKf,eAAe,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKf,eAAe,CAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAUf,gBAAgB,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAyBhB,gBAAgB,CAAA,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAKhB,gBAAgB,CAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAKhB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAKhB,gBAAgB,CAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAKhB,eAAe,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAUf,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,gBAAgB,CAyBhB,EAAA,aAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,OAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAAA,gBAAgB,CAUhB,EAAA,kBAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKhB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAUhB,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAKhB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAUhB,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAKhB,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAAA,eAAe,CAUf,EAAA,oBAAA,EAAA,sBAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,eAAe,CAef,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAAA,gBAAgB,CAUhB,EAAA,gBAAA,EAAA,kBAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,gBAAgB,CAyBhB,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,eAAe,CA3QxB,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,0BAAA,EAAA,4BAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,cAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,gBAAgB,CAAC,EAqfZ,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAxoBpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,6BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,+BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,2BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,sBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkJT,EAo5GsE,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,ykGAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,cAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,cAAA,EAAA,eAAA,EAAA,UAAA,EAAA,sBAAA,EAAA,2BAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,MAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,qBAAA,EAAA,wBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,WAAW,6EAAE,aAAa,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,WAAW,CA71CrG,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,gBAAgB,4IA3GhB,MAAM,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,wBAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAp8DN,SAAS,EAAA,UAAA,EAAA,CAAA;kBA5JrB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACb,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkJT,IAAA,CAAA,EAAA,SAAA,EACU,CAAC,gBAAgB,CAAC,iBACd,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,ykGAAA,CAAA,EAAA,CAAA;;0BA0sBI,MAAM;2BAAC,QAAQ,CAAA;mOAnsBX,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKkC,mBAAmB,EAAA,CAAA;sBAA1D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,yBAAyB,EAAA,CAAA;sBAAjC,KAAK;gBAKG,yBAAyB,EAAA,CAAA;sBAAjC,KAAK;gBAKkC,qBAAqB,EAAA,CAAA;sBAA5D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,sBAAsB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,gBAAgB,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,wBAAwB,EAAA,CAAA;sBAAhC,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,qBAAqB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKiC,kBAAkB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKkC,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKkC,kBAAkB,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKiC,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKO,YAAY,EAAA,CAAA;sBAAxB,KAAK;gBAWO,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAWO,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAWO,aAAa,EAAA,CAAA;sBAAzB,KAAK;gBAWO,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAWO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAYO,gBAAgB,EAAA,CAAA;sBAA5B,KAAK;gBAWO,aAAa,EAAA,CAAA;sBAAzB,KAAK;gBAYI,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAMG,0BAA0B,EAAA,CAAA;sBAAnC,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,mBAAmB,EAAA,CAAA;sBAA5B,MAAM;gBAMG,sBAAsB,EAAA,CAAA;sBAA/B,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,mBAAmB,EAAA,CAAA;sBAA5B,MAAM;gBAEiB,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEK,qBAAqB,EAAA,CAAA;sBAA/C,SAAS;uBAAC,cAAc,CAAA;gBAEQ,2BAA2B,EAAA,CAAA;sBAA3D,SAAS;uBAAC,oBAAoB,CAAA;gBAEI,6BAA6B,EAAA,CAAA;sBAA/D,SAAS;uBAAC,sBAAsB,CAAA;gBAEb,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBAEW,mBAAmB,EAAA,CAAA;sBAA/C,SAAS;uBAAC,gBAAgB,CAAA;gBAEQ,yBAAyB,EAAA,CAAA;sBAA3D,SAAS;uBAAC,sBAAsB,CAAA;gBAED,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAs9CrB,MAAM,CAAA;AAaI,IAAA,EAAA,CAAA;AAAsB,IAAA,gBAAA,CAAA;AAA2C,IAAA,EAAA,CAAA;AAZ3D,IAAA,OAAO,CAAoB;AAEnB,IAAA,QAAQ,CAA6B;AAE9B,IAAA,MAAM,CAAsB;AAE3D,IAAA,eAAe,CAAM;AAErB,IAAA,eAAe,CAAM;AAE9B,IAAA,YAAY,CAAe;AAE3B,IAAA,WAAA,CAAmB,EAAa,EAAS,gBAAkC,EAAS,EAAqB,EAAA;QAAtF,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;AACrG,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,CAAC,SAAS,CAAC,MAAK;AACpE,YAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACvB,gBAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC3B,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,iBAAiB,CAAC,MAAW,EAAE,OAAa,EAAA;AACxC,QAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACvB,YAAA,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC;AAC1C,YAAA,OAAO,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC3C,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED,IAAA,WAAW,CAAC,QAAgB,EAAA;QACxB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AAChE,QAAA,OAAO,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC;KACrE;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGAvCQ,MAAM,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAN,MAAM,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,EAAA,SAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wBAAA,EAAA,UAAA,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAKK,gBAAgB,CApB1B,EAAA,eAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;AAST,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAMQ,MAAM,EAAA,UAAA,EAAA,CAAA;kBAjBlB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;AAST,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;uIAE4B,OAAO,EAAA,CAAA;sBAA/B,KAAK;uBAAC,gBAAgB,CAAA;gBAEU,QAAQ,EAAA,CAAA;sBAAxC,KAAK;uBAAC,wBAAwB,CAAA;gBAES,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAEG,eAAe,EAAA,CAAA;sBAAvB,KAAK;;MAkGG,gBAAgB,CAAA;AAsDgB,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAAsB,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AArDhH,IAAA,OAAO,CAAoB;AAEd,IAAA,MAAM,CAAsB;AAEzC,IAAA,qBAAqB,CAAuB;AAEzC,IAAA,wBAAwB,CAAuB;AAEpD,IAAA,mBAAmB,CAAuB;AAEzC,IAAA,oBAAoB,CAAuB;AAE1C,IAAA,2BAA2B,CAAuB;AAElD,IAAA,qBAAqB,CAAuB;AAEzC,IAAA,wBAAwB,CAAuB;AAE7C,IAAA,0BAA0B,CAAuB;AAE1D,IAAA,QAAQ,CAAqB;AAEpD,IAAA,oBAAoB,CAAe;AAEnC,IAAA,kBAAkB,CAAe;AAEjC,IAAA,oBAAoB,CAAe;AAEnC,IAAA,iBAAiB,CAAoB;AAErC,IAAA,wBAAwB,CAAyB;AAEjD,IAAA,aAAa,CAA4B;AAEzC,IAAA,4BAA4B,CAAsB;AAElD,IAAA,oBAAoB,CAAC,YAAY,EAAA;QAC7B,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU;AACrB,YAAA,GAAG,YAAY;SAClB,CAAC;KACL;AAED,IAAA,IAAa,YAAY,GAAA;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC;KAC7B;IACD,IAAI,YAAY,CAAC,GAA8B,EAAA;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;AACzB,QAAA,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;AAC5D,YAAA,OAAO,CAAC,GAAG,CAAC,uIAAuI,CAAC,CAAC;AACxJ,SAAA;KACJ;IAED,WAAyC,CAAA,UAAe,EAAU,QAAmB,EAAS,EAAa,EAAS,EAAc,EAAS,IAAY,EAAA;QAA9G,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAI;IAE3J,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE;oBACrD,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,2BAA2B,CAAC,CAAC;AAC3E,iBAAA;gBAED,IAAI,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,sBAAsB,CAAC;AAC9D,gBAAA,IAAI,UAAU,EAAE;AACZ,oBAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa;wBAAE,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,UAAU,CAAC,UAAU,EAAE,sBAAsB,CAAC,CAAC;;wBACzG,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,UAAU,CAAC,UAAU,EAAE,8BAA8B,CAAC,CAAC;AACnG,iBAAA;AAED,gBAAA,IAAI,cAAc,GAAG,UAAU,CAAC,uBAAuB,EAAE,CAAC;AACzD,gBAAA,IAAI,CAAC,wBAAuC,CAAC,aAAa,CAAC,KAAK,CAAC,YAAY,GAAG,cAAc,GAAG,IAAI,CAAC;gBAEvG,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE;AAC9E,oBAAA,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,KAAK,CAAC,YAAY,GAAG,cAAc,GAAG,IAAI,CAAC;AAC1F,iBAAA;AACJ,aAAA;AAAM,iBAAA;gBACH,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,0BAA0B,CAAC,aAAa,EAAE;AAClF,oBAAA,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,wBAAwB,EAAE,GAAG,IAAI,CAAC;AAC7G,iBAAA;AACJ,aAAA;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;oBACxE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5I,iBAAA;gBAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;oBACxE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxI,iBAAA;AAED,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,oBAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACvB,wBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAiB,EAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxJ,qBAAA;AAAM,yBAAA;wBACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACnI,qBAAA;AACJ,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,YAAY,GAAA;AACR,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;gBACxE,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,iBAAA;AACJ,aAAA;YAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;gBACxE,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,iBAAA;AACJ,aAAA;YAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE;gBACpE,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAClC,iBAAA;AACJ,aAAA;YAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE;gBAChD,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAClC,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,cAAc,GAAA;QACV,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,aAAa,CAAC,UAAU,CAAC;QAEvE,IAAI,CAAC,mBAAkC,CAAC,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;QAE/E,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;YACxE,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;AACpE,SAAA;AAED,QAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;KAC5C;IAED,cAAc,GAAA;QACV,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,aAAa,CAAC,UAAU,CAAC;QACvE,IAAI,CAAC,mBAAkC,CAAC,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;QAE/E,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;YACxE,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;AACpE,SAAA;AAED,QAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;KAC5C;AAED,IAAA,YAAY,CAAC,KAAU,EAAA;QACnB,IAAI,IAAI,CAAC,4BAA4B,EAAE;AACnC,YAAA,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;YAC1C,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;AACvE,YAAA,IAAI,CAAC,wBAAuC,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AACtH,SAAA;QAED,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE;AACvE,YAAA,IAAI,CAAC,wBAAuC,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AACtH,SAAA;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;AAC7D,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,KAAa,EAAA;QAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtC,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,OAAwB,EAAA;QAC7B,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACnC,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,QAAQ,EAAE;gBAClD,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC5D,aAAA;AAAM,iBAAA;gBACF,IAAI,CAAC,mBAAkC,CAAC,aAAa,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;gBAChF,IAAI,CAAC,mBAAkC,CAAC,aAAa,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;AAClF,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,YAAY,EAAE,CAAC;AAEpB,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;KACjC;AA5MQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,kBAsDL,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAtDtB,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAGL,gBAAgB,CAlE1B,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,0BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,6BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,0BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,4BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,aAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EArGQ,MAAM,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,wBAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA2GN,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAjE5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAuDgB,MAAM;2BAAC,WAAW,CAAA;oIArDJ,OAAO,EAAA,CAAA;sBAAjC,KAAK;uBAAC,kBAAkB,CAAA;gBAEe,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEX,qBAAqB,EAAA,CAAA;sBAA/C,SAAS;uBAAC,cAAc,CAAA;gBAEK,wBAAwB,EAAA,CAAA;sBAArD,SAAS;uBAAC,iBAAiB,CAAA;gBAEH,mBAAmB,EAAA,CAAA;sBAA3C,SAAS;uBAAC,YAAY,CAAA;gBAEG,oBAAoB,EAAA,CAAA;sBAA7C,SAAS;uBAAC,aAAa,CAAA;gBAEG,2BAA2B,EAAA,CAAA;sBAArD,SAAS;uBAAC,cAAc,CAAA;gBAEE,qBAAqB,EAAA,CAAA;sBAA/C,SAAS;uBAAC,cAAc,CAAA;gBAEK,wBAAwB,EAAA,CAAA;sBAArD,SAAS;uBAAC,iBAAiB,CAAA;gBAEI,0BAA0B,EAAA,CAAA;sBAAzD,SAAS;uBAAC,mBAAmB,CAAA;gBAEP,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;gBAuBR,YAAY,EAAA,CAAA;sBAAxB,KAAK;;MA8KG,gBAAgB,CAAA;AAeN,IAAA,EAAA,CAAA;AAdQ,IAAA,KAAK,CAAqB;AAEb,IAAA,wBAAwB,CAAsB;AAEtF,IAAA,MAAM,CAAsB;AAE5B,IAAA,YAAY,CAA2B;AAEvC,IAAA,IAAI,UAAU,GAAA;QACV,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;AAAE,YAAA,OAAO,YAAY,CAAC;aACzD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;AAAE,YAAA,OAAO,WAAW,CAAC;;AAC7D,YAAA,OAAO,MAAM,CAAC;KACtB;AAED,IAAA,WAAA,CAAmB,EAAa,EAAA;QAAb,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;AAC5B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,QAAQ,KAAI;gBACxE,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAClB,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAS,IAAI,CAAC,KAAK,CAAY,CAAC;KACjE;AAGD,IAAA,OAAO,CAAC,KAAiB,EAAA;AACrB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAClB,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;AACT,gBAAA,aAAa,EAAE,KAAK;gBACpB,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,aAAA,CAAC,CAAC;YAEH,UAAU,CAAC,cAAc,EAAE,CAAC;AAC/B,SAAA;KACJ;AAGD,IAAA,UAAU,CAAC,KAAiB,EAAA;AACxB,QAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACvB;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC;KACjD;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGA3DQ,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,qKAGL,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,yBAAA,EAAA,aAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,eAAA,EAAA,4BAAA,EAAA,WAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAH3B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAX5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,2BAA2B,EAAE,aAAa;AAC1C,wBAAA,qBAAqB,EAAE,QAAQ;AAC/B,wBAAA,iBAAiB,EAAE,0BAA0B;AAC7C,wBAAA,aAAa,EAAE,gBAAgB;AAC/B,wBAAA,kBAAkB,EAAE,YAAY;AACnC,qBAAA;AACJ,iBAAA,CAAA;2EAE8B,KAAK,EAAA,CAAA;sBAA/B,KAAK;uBAAC,kBAAkB,CAAA;gBAEe,wBAAwB,EAAA,CAAA;sBAA/D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBA+BtC,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;gBAcjC,UAAU,EAAA,CAAA;sBADT,YAAY;uBAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAgChC,UAAU,CAAA;AAWA,IAAA,EAAA,CAAA;AAAsB,IAAA,EAAA,CAAA;AAVhC,IAAA,KAAK,CAAqB;AAE1B,IAAA,aAAa,CAAqB;AAElC,IAAA,YAAY,CAAqB;AAE1C,IAAA,YAAY,CAA2B;AAEvC,IAAA,SAAS,CAAqB;IAE9B,WAAmB,CAAA,EAAa,EAAS,EAAqB,EAAA;QAA3C,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;AAC1D,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,QAAQ,KAAI;YACxE,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,eAAe,EAAE,CAAC;KAC1B;AAED,IAAA,OAAO,CAAC,KAAY,EAAA;QAChB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,eAAe,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAS,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC;AACjF,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,KAAK,UAAU,EAAE;AACxC,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAS,IAAI,CAAC,KAAK,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;AAClD,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGAvCQ,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EAdT,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;AAOE,eAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA4jCoG,WAAW,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,mBAAmB,CAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,kBAAkB,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FArjC3J,UAAU,EAAA,UAAA,EAAA,CAAA;kBAhBtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;AAOE,eAAA,CAAA;oBACZ,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;2GAEY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;;MA2CG,iBAAiB,CAAA;AAWY,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAAsB,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAVjJ,IAAA,yBAAyB,CAAsB;AAEvF,IAAA,OAAO,CAA8B;AAErC,IAAA,wBAAwB,CAAe;AAEvC,IAAA,yBAAyB,CAAe;AAExC,IAAA,uBAAuB,CAAe;IAEtC,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAU,QAAmB,EAAS,EAAa,EAAS,EAAc,EAAS,IAAY,EAAA;QAA/J,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAI;IAEzM,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;gBAClB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAC;gBACjE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBACnD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;AACzD,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAE/D,gBAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;oBAC7B,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACjH,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;YAC7B,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACvH,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACrH,SAAC,CAAC,CAAC;KACN;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACjC,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;AACzC,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACvC,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B;AAED,IAAA,mBAAmB,CAAC,KAAiB,EAAA;AACjC,QAAA,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;KACjC;AAED,IAAA,iBAAiB,CAAC,KAAiB,EAAA;AAC/B,QAAA,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QACxD,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,yBAAyB,KAAK,IAAI,CAAC;KAClD;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;AACxC,SAAA;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;uGAxEQ,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAWN,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAXpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,mIACN,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAD3B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAN7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAYgB,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;oIAVrC,yBAAyB,EAAA,CAAA;sBAAhE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;;MAgF7B,mBAAmB,CAAA;AAaU,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAAsB,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAZjJ,IAAA,2BAA2B,CAAsB;AAEzF,IAAA,iBAAiB,CAAe;AAEhC,IAAA,gBAAgB,CAAe;AAE/B,IAAA,iBAAiB,CAAe;AAEhC,IAAA,iBAAiB,CAAe;AAEhC,IAAA,iBAAiB,CAAe;IAEhC,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAU,QAAmB,EAAS,EAAa,EAAS,EAAc,EAAS,IAAY,EAAA;QAA/J,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAI;IAEzM,eAAe,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAClB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/G,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/G,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC7G,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/G,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACnH,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,YAAY,GAAA;AACR,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,aAAA;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAChC,aAAA;YAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,aAAA;YAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;QAClB,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,KAAK,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,KAAK,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,kBAAkB,CAAC;YAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,SAAS,GAAG,KAAK,CAAC;;YAC3K,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC;KAC/C;AAED,IAAA,WAAW,CAAC,KAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;KAC3D;AAED,IAAA,UAAU,CAAC,KAAgB,EAAA;QACvB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;KAC3D;AAED,IAAA,WAAW,CAAC,KAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;KACpC;AAGD,IAAA,MAAM,CAAC,KAAgB,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;AACtD,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,2BAA2B,KAAK,IAAI,CAAC;KACpD;IAED,WAAW,GAAA;QACP,IAAI,CAAC,YAAY,EAAE,CAAC;KACvB;uGA3FQ,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAaR,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAbpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,2IACR,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,MAAA,EAAA,gBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAD3B,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAN/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAcgB,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;oIAZrC,2BAA2B,EAAA,CAAA;sBAAlE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBA8EtC,MAAM,EAAA,CAAA;sBADL,YAAY;uBAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAyBvB,eAAe,CAAA;AASL,IAAA,EAAA,CAAA;AAAsB,IAAA,YAAA,CAAA;AARf,IAAA,OAAO,CAAM;AAEC,IAAA,uBAAuB,CAAsB;AAErF,IAAA,QAAQ,CAAsB;AAE9B,IAAA,YAAY,CAA2B;IAEvC,WAAmB,CAAA,EAAa,EAAS,YAA8B,EAAA;QAApD,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAkB;AACnE,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;AACrE,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1D,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzD,SAAA;KACJ;AAGD,IAAA,OAAO,CAAC,KAAY,EAAA;AAChB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;AACnB,gBAAA,aAAa,EAAE,KAAK;gBACpB,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,aAAA,CAAC,CAAC;AACN,SAAA;KACJ;AAGD,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAGD,IAAA,UAAU,CAAC,KAAY,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACpC,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,KAAK,UAAU,EAAE;AACtC,YAAA,IAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC;AAC3B,gBAAA,aAAa,EAAE,KAAK;gBACpB,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,aAAA,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvB,SAAA;QACD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC;KAChD;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGAzEQ,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,oKAGJ,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,oBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAH3B,eAAe,EAAA,UAAA,EAAA,CAAA;kBAT3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,qBAAqB,EAAE,UAAU;AACjC,wBAAA,yBAAyB,EAAE,UAAU;AACrC,wBAAA,qBAAqB,EAAE,UAAU;AACpC,qBAAA;AACJ,iBAAA,CAAA;uGAE6B,OAAO,EAAA,CAAA;sBAAhC,KAAK;uBAAC,iBAAiB,CAAA;gBAEgB,uBAAuB,EAAA,CAAA;sBAA9D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAqBtC,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;gBAWjC,SAAS,EAAA,CAAA;sBADR,YAAY;uBAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAA;gBAcnC,UAAU,EAAA,CAAA;sBADT,YAAY;uBAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAqC3B,uBAAuB,CAAA;AASb,IAAA,EAAA,CAAA;AAAsB,IAAA,YAAA,CAAA;AARP,IAAA,OAAO,CAAM;AAEP,IAAA,uBAAuB,CAAsB;AAErF,IAAA,QAAQ,CAAsB;AAE9B,IAAA,YAAY,CAA2B;IAEvC,WAAmB,CAAA,EAAa,EAAS,YAA8B,EAAA;QAApD,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAkB;AACnE,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;AACrE,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1D,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzD,SAAA;KACJ;AAGD,IAAA,OAAO,CAAC,KAAY,EAAA;AAChB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;AACnB,gBAAA,aAAa,EAAE,KAAK;gBACpB,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,aAAA,CAAC,CAAC;AACN,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC;KAChD;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGAzCQ,uBAAuB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,uBAAuB,oLAGZ,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAH3B,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAPnC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,qBAAqB,EAAE,UAAU;AACpC,qBAAA;AACJ,iBAAA,CAAA;uGAEqC,OAAO,EAAA,CAAA;sBAAxC,KAAK;uBAAC,yBAAyB,CAAA;gBAEQ,uBAAuB,EAAA,CAAA;sBAA9D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAqBtC,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAA;;MA6B3B,gBAAgB,CAAA;AASN,IAAA,EAAA,CAAA;AAAsB,IAAA,YAAA,CAAA;AAAwC,IAAA,EAAA,CAAA;AARtD,IAAA,OAAO,CAAkB;AAEZ,IAAA,wBAAwB,CAAsB;AAEtF,IAAA,QAAQ,CAAsB;AAE9B,IAAA,YAAY,CAA2B;AAEvC,IAAA,WAAA,CAAmB,EAAa,EAAS,YAA8B,EAAU,EAAc,EAAA;QAA5E,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAkB;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;AAC3F,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,IAAI,KAAI;AAC3E,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5D,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAGD,IAAA,aAAa,CAAC,KAAY,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC;AACxB,gBAAA,aAAa,EAAE,KAAK;gBACpB,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,aAAA,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAE9B,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC;KACjD;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGAvCQ,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,yKAGL,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,aAAA,EAAA,uBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,eAAA,EAAA,6BAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAH3B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAR5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,iBAAiB,EAAE,6BAA6B;AACnD,qBAAA;AACJ,iBAAA,CAAA;gIAE8B,OAAO,EAAA,CAAA;sBAAjC,KAAK;uBAAC,kBAAkB,CAAA;gBAEe,wBAAwB,EAAA,CAAA;sBAA/D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAetC,aAAa,EAAA,CAAA;sBADZ,YAAY;uBAAC,aAAa,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAiD9B,UAAU,CAAA;AAaA,IAAA,EAAA,CAAA;AAAsB,IAAA,YAAA,CAAA;AAAuC,IAAA,EAAA,CAAA;AAZxC,IAAA,QAAQ,CAAsB;AAEtD,IAAA,OAAO,CAAM;AAE7B,IAAA,OAAO,CAAsB;AAE7B,IAAA,cAAc,CAAsB;AAEpC,IAAA,OAAO,CAAsB;AAE7B,IAAA,YAAY,CAA2B;AAEvC,IAAA,WAAA,CAAmB,EAAa,EAAS,YAA8B,EAAS,EAAqB,EAAA;QAAlF,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAkB;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;AACjG,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;AACrE,YAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACvB,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzD,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1E,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;AAC3D,aAAA;AACD,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACvB,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACzD,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1E,SAAA;AAAM,aAAA;;AAEH,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;AAC3D,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,KAAY,EAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACvB,gBAAA,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7B,gBAAA,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;AACnB,oBAAA,aAAa,EAAE,KAAK;AACpB,oBAAA,KAAK,EAAE,MAAM;oBACb,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,iBAAA,CAAC,CAAC;AACN,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC;AAC3B,oBAAA,aAAa,EAAE,KAAK;oBACpB,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,iBAAA,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;QACD,UAAU,CAAC,cAAc,EAAE,CAAC;KAC/B;IAED,OAAO,GAAA;AACH,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGApEQ,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAV,UAAU,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EACC,gBAAgB,CAvB1B,EAAA,OAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;KAeT,EA2oBqK,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,SAAS,2EAAE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FApoBjL,UAAU,EAAA,UAAA,EAAA,CAAA;kBAxBtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;AAeT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;uIAE2C,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEtB,OAAO,EAAA,CAAA;sBAAtB,KAAK;uBAAC,OAAO,CAAA;;MA2FL,gBAAgB,CAAA;AAaN,IAAA,EAAA,CAAA;AAAsB,IAAA,YAAA,CAAA;AAAwC,IAAA,EAAA,CAAA;AAZ/D,IAAA,YAAY,CAAyB;AAEvD,IAAA,OAAO,CAAsB;AAE7B,IAAA,OAAO,CAAsB;AAE7B,IAAA,QAAQ,CAAsB;AAE9B,IAAA,2BAA2B,CAAe;AAE1C,IAAA,uBAAuB,CAAe;AAEtC,IAAA,WAAA,CAAmB,EAAa,EAAS,YAA8B,EAAU,EAAqB,EAAA;QAAnF,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAkB;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;AAClG,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,eAAe,CAAC,SAAS,CAAC,MAAK;AAC/E,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7C,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;AACpF,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7C,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC5C;IAED,OAAO,CAAC,KAAY,EAAE,OAAgB,EAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YAC5G,IAAI,CAAC,EAAE,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC;AACpD,SAAA;QAED,UAAU,CAAC,cAAc,EAAE,CAAC;KAC/B;IAED,OAAO,GAAA;AACH,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,2BAA2B,EAAE;AAClC,YAAA,IAAI,CAAC,2BAA2B,CAAC,WAAW,EAAE,CAAC;AAClD,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAC9B,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;AAC9C,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AACvB,QAAA,IAAI,OAAiB,CAAC;AACtB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;AAEpD,QAAA,IAAI,IAAI,EAAE;AACN,YAAA,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACvB,gBAAA,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;oBACnB,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;wBAC9B,OAAO,GAAG,IAAI,CAAC;AAClB,qBAAA;AAAM,yBAAA;wBACH,OAAO,GAAG,KAAK,CAAC;wBAChB,MAAM;AACT,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACD,YAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;;AAExB,gBAAA,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;oBACnB,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;wBAC1B,OAAO,GAAG,IAAI,CAAC;AAClB,qBAAA;AAAM,yBAAA;wBACH,OAAO,GAAG,KAAK,CAAC;wBAChB,MAAM;AACT,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,OAAO,GAAG,KAAK,CAAC;AACnB,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB;uGArFQ,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EArBf,QAAA,EAAA,2BAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,KAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;AAcT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA6iBqK,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAtiBtK,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAvB5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,2BAA2B;AACrC,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;AAcT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;uIAEqB,YAAY,EAAA,CAAA;sBAA7B,SAAS;uBAAC,KAAK,CAAA;;MA6FP,gBAAgB,CAAA;AAON,IAAA,EAAA,CAAA;AAAsB,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AANrC,IAAA,IAAI,CAAM;AAEL,IAAA,KAAK,CAAM;AAEH,IAAA,wBAAwB,CAAsB;AAEtF,IAAA,WAAA,CAAmB,EAAa,EAAS,EAAc,EAAS,IAAY,EAAA;QAAzD,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAI;IAEhF,eAAe,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAClB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;AACnE,SAAA;KACJ;AAGD,IAAA,OAAO,CAAC,KAAiB,EAAA;AACrB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,YAAA,IAAI,CAAC,EAAE,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAEhC,YAAA,IAAI,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE;gBACrB,IAAI,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AAC/C,oBAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,EAAE;wBAC/B,OAAO;AACV,qBAAA;oBAED,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;oBAC9D,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,iBAAA;AACJ,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,aAAA;AACJ,SAAA;KACJ;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACxE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAC7D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAChE,QAAA,IAAI,CAAC,EAAE,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;YAC7B,UAAU,CAAC,MAAK;AACZ,gBAAA,IAAI,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;AAChF,gBAAA,IAAI,SAAS,EAAE;oBACX,SAAS,CAAC,KAAK,EAAE,CAAC;AACrB,iBAAA;aACJ,EAAE,EAAE,CAAC,CAAC;AACX,SAAC,CAAC,CAAC;KACN;IAED,gBAAgB,GAAA;QACZ,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,EAAE,CAAC,0BAA0B,EAAE,CAAC;KACxC;AAGD,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAClB,IAAI,KAAK,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AAC1C,gBAAA,IAAI,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,EAAE;oBAC9B,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;oBAC9D,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACvE,iBAAA;gBAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,aAAA;AAAM,iBAAA,IAAI,KAAK,CAAC,IAAI,IAAI,QAAQ,EAAE;AAC/B,gBAAA,IAAI,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,EAAE;oBAC9B,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;oBAC9D,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACrE,iBAAA;gBAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,aAAA;AAAM,iBAAA,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,EAAE;gBAC5B,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAEpE,IAAI,KAAK,CAAC,QAAQ;AAAE,oBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;;AAC9C,oBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACnC,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,OAAY,EAAA;AACjB,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,IAAI,GAAG,OAAO,CAAC;YACnB,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE;AACzD,gBAAA,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;AAC7B,aAAA;AAED,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED,IAAA,kBAAkB,CAAC,KAAoB,EAAA;QACnC,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC9C,QAAA,IAAI,GAAG,GAAG,WAAW,CAAC,aAAa,CAAC;QACpC,IAAI,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;AAE9D,QAAA,IAAI,UAAU,EAAE;AACZ,YAAA,UAAU,CAAC,mBAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACpD,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;QAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC9C,QAAA,IAAI,GAAG,GAAG,WAAW,CAAC,aAAa,CAAC;QACpC,IAAI,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;AAE1D,QAAA,IAAI,UAAU,EAAE;AACZ,YAAA,UAAU,CAAC,mBAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACpD,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,0BAA0B,CAAC,IAAS,EAAA;AAChC,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAE3C,IAAI,CAAC,QAAQ,EAAE;AACX,YAAA,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACxF,YAAA,IAAI,WAAW,EAAE;AACb,gBAAA,QAAQ,GAAG,WAAW,CAAC,gBAAgB,CAAC;AAC3C,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,CAAC;AAAE,gBAAA,OAAO,QAAQ,CAAC;;AACnE,gBAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;AACzD,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED,IAAA,sBAAsB,CAAC,IAAa,EAAA;AAChC,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAEvC,IAAI,CAAC,QAAQ,EAAE;AACX,YAAA,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAChF,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC;AACxC,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,CAAC;AAAE,gBAAA,OAAO,QAAQ,CAAC;;AACnE,gBAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;AACrD,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC;KACjD;uGA7JQ,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,8MAKL,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAL3B,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAN5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;yHAE8B,IAAI,EAAA,CAAA;sBAA9B,KAAK;uBAAC,kBAAkB,CAAA;gBAEO,KAAK,EAAA,CAAA;sBAApC,KAAK;uBAAC,uBAAuB,CAAA;gBAEU,wBAAwB,EAAA,CAAA;sBAA/D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAWtC,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;gBA0CjC,SAAS,EAAA,CAAA;sBADR,YAAY;uBAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAuH1B,mBAAmB,CAAA;AAOT,IAAA,EAAA,CAAA;AAAsB,IAAA,cAAA,CAAA;AANT,IAAA,SAAS,CAAqC;AAE9E,IAAA,aAAa,CAA6B;AAE1C,IAAA,cAAc,CAA6B;IAE3C,WAAmB,CAAA,EAAa,EAAS,cAAgC,EAAA;QAAtD,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAc,CAAA,cAAA,GAAd,cAAc,CAAkB;KAAI;IAE7E,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;uGArBQ,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAnB,mBAAmB,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EACX,aAAa,EAdpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;AAOT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAMQ,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAf/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,QAAQ,EAAE,CAAA;;;;;;;AAOT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;uGAEmC,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAmCrB,KAAK,CAAA;AAeK,IAAA,EAAA,CAAA;AAAsB,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAdhE,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;KACtC;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KACjD;AAED,IAAA,IAAI,QAAQ,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;KACzC;AAEe,IAAA,OAAO,CAAM;AAE7B,IAAA,WAAA,CAAmB,EAAa,EAAS,EAAc,EAAS,IAAY,EAAA;QAAzD,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAI;AAGhF,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;QAC/B,IAAI,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,aAAa,EAAE,kBAAkB,CAAC;AACzD,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,cAAc,CAAc,KAAK,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAClE,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAoB,EAAA;QAC7B,IAAI,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,aAAa,EAAE,sBAAsB,CAAC;AAC7D,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,cAAc,CAAc,KAAK,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAClE,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;AAChC,QAAA,MAAM,aAAa,GAAgB,KAAK,CAAC,aAAa,CAAC;AACvD,QAAA,MAAM,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC;AAElG,QAAA,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAClE,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAEnB,YAAA,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC/B,SAAA;QACD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC;QAC5D,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;QAC1E,MAAM,eAAe,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxB,SAAA;AACD,QAAA,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,EAAE,CAAC,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AAC9D,SAAA;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,MAAM,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,aAAa,EAAE,CAAkB,eAAA,EAAA,IAAI,CAAC,KAAK,CAAA,EAAA,CAAI,CAAC,CAAC;AACxH,QAAA,YAAY,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC/C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,aAAa,EAAE,CAAkB,eAAA,EAAA,IAAI,CAAC,KAAK,CAAA,EAAA,CAAI,CAAC,CAAC;QAC3G,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,QAAA,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAC9B,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;AACzB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;QAE9G,IAAI,IAAI,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACtC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,UAAU,CAAC,YAAY,CAAC,GAAG,EAAE,kBAAkB,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,MAAM,CAAC,CAAC;AAC3I,YAAA,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACjB,gBAAA,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACtB,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,cAAc,EAAE;AAChB,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,kBAAkB,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,MAAM,CAAC,CAAC;AAC/I,gBAAA,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;gBAE9B,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;AACxB,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAY,EAAA;AACf,QAAA,IAAI,CAAC,EAAE,CAAC,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QACjE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;AAErC,QAAA,IAAI,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AAEpG,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;AACtB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;AAC1B,SAAA,CAAC,CAAC;KACN;AAED,IAAA,QAAQ,CAAC,KAAY,EAAA;QACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;AAEtC,QAAA,IAAI,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAE/C,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;KAClF;AAED,IAAA,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,qBAAsB,EAAA;AACvE,QAAA,iBAAiB,CAAC,QAAQ,GAAG,IAAI,CAAC;AAClC,QAAA,iBAAiB,CAAC,QAAQ,GAAG,GAAG,CAAC;AAEjC,QAAA,UAAU,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;KACvC;AAED,IAAA,YAAY,CAAC,KAAM,EAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;YAC7B,UAAU,CAAC,MAAK;gBACZ,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC;gBAC5D,MAAM,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC,QAAQ,CAAS,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;AACrH,gBAAA,MAAM,IAAI,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;gBAEnD,IAAI;AACA,oBAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;AACf,wBAAA,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;AACpB,4BAAA,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACnB,yBAAA;AACL,qBAAC,CAAC,CAAC;AAEP,gBAAA,IAAI,GAAG,EAAE;AACL,oBAAA,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC;oBACjB,GAAG,CAAC,KAAK,EAAE,CAAC;AACf,iBAAA;aACJ,EAAE,EAAE,CAAC,CAAC;AACX,SAAC,CAAC,CAAC;KACN;uGArLQ,KAAK,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAL,KAAK,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,2BAAA,EAAA,eAAA,EAAA,KAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,sBAAA,EAAA,KAAA,EAAA,WAAA,EAAA,KAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAL,KAAK,EAAA,UAAA,EAAA,CAAA;kBAZjB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,SAAS,EAAE,CAA2B,yBAAA,CAAA;AACtC,wBAAA,iBAAiB,EAAE,KAAK;AACxB,wBAAA,sBAAsB,EAAE,UAAU;AAClC,wBAAA,mBAAmB,EAAE,OAAO;AAC5B,wBAAA,wBAAwB,EAAE,KAAK;AAC/B,wBAAA,aAAa,EAAE,KAAK;AACvB,qBAAA;AACJ,iBAAA,CAAA;yHAcmB,OAAO,EAAA,CAAA;sBAAtB,KAAK;uBAAC,OAAO,CAAA;gBAKd,SAAS,EAAA,CAAA;sBADR,YAAY;uBAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAoM1B,gBAAgB,CAAA;AAGN,IAAA,EAAA,CAAA;AAAuB,IAAA,MAAA,CAAA;AAFjC,IAAA,OAAO,CAAM;IAEtB,WAAmB,CAAA,EAAa,EAAU,MAAqB,EAAA;QAA5C,IAAE,CAAA,EAAA,GAAF,EAAE,CAAW;QAAU,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;AAEnE,IAAA,IAAI,qBAAqB,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC;KAC5J;AAED,IAAA,OAAO,CAAC,KAAY,EAAA;AAChB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;AAEzD,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC5B,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;AACtB,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;AAC1B,aAAA,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC;AACxB,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;AAC1B,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAE/C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;uGA5BQ,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EA3Bf,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;KAqBT,EAsC2L,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,iFAAE,gBAAgB,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAhCpN,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBA7B5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;AAqBT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;uGAEY,OAAO,EAAA,CAAA;sBAAf,KAAK;;MAqEG,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,iBAl7Gf,SAAS,EA42GT,gBAAgB,EA7zChB,gBAAgB,EA3GhB,MAAM,EAqUN,gBAAgB,EA8EhB,UAAU,EAgDV,iBAAiB,EAgxBjB,KAAK,EA/rBL,mBAAmB,EAuGnB,eAAe,EAmFf,uBAAuB,EAoDvB,gBAAgB,EAkEhB,UAAU,EA8FV,gBAAgB,EA8FhB,gBAAgB,EA+KhB,mBAAmB,CAAA,EAAA,OAAA,EAAA,CAyRlB,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,aA54GpN,SAAS,EA+4Gd,YAAY,EAnCP,gBAAgB,EAnmChB,gBAAgB,EA8EhB,UAAU,EAgDV,iBAAiB,EAgxBjB,KAAK,EA/rBL,mBAAmB,EAuGnB,eAAe,EAmFf,uBAAuB,EAoDvB,gBAAgB,EAkEhB,UAAU,EA8FV,gBAAgB,EA8FhB,gBAAgB,EA+KhB,mBAAmB,EA0SxB,cAAc,CAAA,EAAA,CAAA,CAAA;AAqBT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAtCd,OAAA,EAAA,CAAA,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAGzN,YAAY;YAcZ,cAAc,CAAA,EAAA,CAAA,CAAA;;2FAqBT,eAAe,EAAA,UAAA,EAAA,CAAA;kBAvC3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,CAAC;AAC9N,oBAAA,OAAO,EAAE;wBACL,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,gBAAgB;wBAChB,UAAU;wBACV,iBAAiB;wBACjB,KAAK;wBACL,mBAAmB;wBACnB,eAAe;wBACf,uBAAuB;wBACvB,gBAAgB;wBAChB,UAAU;wBACV,gBAAgB;wBAChB,gBAAgB;wBAChB,mBAAmB;wBACnB,cAAc;AACjB,qBAAA;AACD,oBAAA,YAAY,EAAE;wBACV,SAAS;wBACT,gBAAgB;wBAChB,gBAAgB;wBAChB,MAAM;wBACN,gBAAgB;wBAChB,UAAU;wBACV,iBAAiB;wBACjB,KAAK;wBACL,mBAAmB;wBACnB,eAAe;wBACf,uBAAuB;wBACvB,gBAAgB;wBAChB,UAAU;wBACV,gBAAgB;wBAChB,gBAAgB;wBAChB,mBAAmB;AACtB,qBAAA;AACJ,iBAAA,CAAA;;;ACprHD;;AAEG;;;;"}