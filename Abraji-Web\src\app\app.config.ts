import { ApplicationConfig, provideZoneChangeDetection, isDevMode, APP_INITIALIZER } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { provideHttpClient } from '@angular/common/http';
import { TranslocoHttpLoader } from './transloco-loader';
import { provideTransloco } from '@jsverse/transloco';
import { TranslationService } from './core/common-services/services/translation.service';
import { LocationStrategy, HashLocationStrategy } from '@angular/common';
// initialize app lang
export function initializeApp(translationService: TranslationService) {
  return () => translationService.initializeLanguage();
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes), provideHttpClient(), provideTransloco({
        config: {
          availableLangs: ['en', 'ar'],
          defaultLang: 'en',
          // Remove this option if your application doesn't support changing language in runtime.
          reRenderOnLangChange: true,
          prodMode: !isDevMode(),
        },
        loader: TranslocoHttpLoader
      }),
      TranslationService,
      {
        provide: APP_INITIALIZER,
        useFactory: initializeApp,
        deps: [TranslationService],
        multi: true,
      },
      {
        provide: LocationStrategy,
        useClass: HashLocationStrategy
      }
    ],


};
