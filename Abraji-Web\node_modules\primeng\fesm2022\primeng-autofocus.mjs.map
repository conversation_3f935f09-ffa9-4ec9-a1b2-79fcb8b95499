{"version": 3, "file": "primeng-autofocus.mjs", "sources": ["../../src/app/components/autofocus/autofocus.ts", "../../src/app/components/autofocus/primeng-autofocus.ts"], "sourcesContent": ["import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { Directive, ElementRef, Input, NgModule, PLATFORM_ID, booleanAttribute, inject } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\n@Directive({\n    selector: '[pAutoFocus]',\n    standalone: true,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class AutoFocus {\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean = false;\n\n    focused: boolean = false;\n\n    platformId = inject(PLATFORM_ID);\n\n    document: Document = inject(DOCUMENT);\n\n    host: ElementRef = inject(ElementRef);\n\n    ngAfterContentChecked() {\n        // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n        if (this.autofocus === false) {\n            this.host.nativeElement.removeAttribute('autofocus');\n        } else {\n            this.host.nativeElement.setAttribute('autofocus', true);\n        }\n\n        if (!this.focused) {\n            this.autoFocus();\n        }\n    }\n\n    ngAfterViewChecked() {\n        if (!this.focused) {\n            this.autoFocus();\n        }\n    }\n\n    autoFocus() {\n        if (isPlatformBrowser(this.platformId) && this.autofocus) {\n            setTimeout(() => {\n                const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n\n                if (focusableElements.length === 0) {\n                    this.host.nativeElement.focus();\n                }\n                if (focusableElements.length > 0) {\n                    focusableElements[0].focus();\n                }\n\n                this.focused = true;\n            });\n        }\n    }\n}\n\n@NgModule({\n    imports: [AutoFocus],\n    exports: [AutoFocus]\n})\nexport class AutoFocusModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAGA;;;AAGG;MAQU,SAAS,CAAA;AAClB;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;IAEnE,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AAEjC,IAAA,QAAQ,GAAa,MAAM,CAAC,QAAQ,CAAC,CAAC;AAEtC,IAAA,IAAI,GAAe,MAAM,CAAC,UAAU,CAAC,CAAC;IAEtC,qBAAqB,GAAA;;AAEjB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AACxD,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC3D,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,SAAS,EAAE,CAAC;AACpB,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,SAAS,EAAE,CAAC;AACpB,SAAA;KACJ;IAED,SAAS,GAAA;QACL,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;YACtD,UAAU,CAAC,MAAK;AACZ,gBAAA,MAAM,iBAAiB,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAEpF,gBAAA,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,oBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACnC,iBAAA;AACD,gBAAA,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,oBAAA,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AAChC,iBAAA;AAED,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;uGAjDQ,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,gGAKE,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAL3B,SAAS,EAAA,UAAA,EAAA,CAAA;kBAPrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;8BAM2C,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;;MAmD7B,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAf,eAAe,EAAA,OAAA,EAAA,CAxDf,SAAS,CAAA,EAAA,OAAA,EAAA,CAAT,SAAS,CAAA,EAAA,CAAA,CAAA;wGAwDT,eAAe,EAAA,CAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,SAAS,CAAC;oBACpB,OAAO,EAAE,CAAC,SAAS,CAAC;AACvB,iBAAA,CAAA;;;ACrED;;AAEG;;;;"}