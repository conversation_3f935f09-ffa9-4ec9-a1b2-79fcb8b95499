import { Injectable } from "@angular/core";
import { HttpService } from "../../common-services/services/http.service";
import { Debt } from "./debts";

@Injectable({
    providedIn: 'root'
})
export class DebtsApi {
  private apiUrl = `debts`;

  constructor(private httpService: HttpService) { }

  getDebts(requestForm: any) {
    const url = `${this.apiUrl}`;
    return this.httpService.post(url, requestForm);
  }

  getDebtsByUser(id: number, requestForm: any) {
    const url = `${this.apiUrl}/user/${id}`;
    return this.httpService.post(url, requestForm);
  }

  getDebt(): any {
      throw new Error('Method not implemented.');
  }

  createDebt(dept: Debt): any {
    const url = `${this.apiUrl}/create`;
    return this.httpService.post(url, dept);
  }
  updateDebt(debt: any): any {
    const url = `${this.apiUrl}/update/${debt.id}`;
    return this.httpService.put(url, debt);
  }

  deleteDebt(id: number): any {
    throw new Error('Method not implemented.');
  }

  payDebt(id: number): any {
    //return this.httpService.put(`${this.apiUrl}/pay/${id}`, {});
    throw new Error('Method not implemented.');
  }

  payPartialDebt(id: number, amount: number): any {
    const url = `${this.apiUrl}/partial-payment/${id}`;
    return this.httpService.post(url, {amount});
  }

  getDebtStatisticsByUser(id: number) {
    const url = `${this.apiUrl}/statistics/user/${id}`;
    return this.httpService.get(url);
  }

  getDebtHistory(debtId: string) {
    const url = `${this.apiUrl}/partial-payment/${debtId}`;
    return this.httpService.get(url);
  }

}
