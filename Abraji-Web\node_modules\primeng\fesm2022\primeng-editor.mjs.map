{"version": 3, "file": "primeng-editor.mjs", "sources": ["../../src/app/components/editor/editor.ts", "../../src/app/components/editor/primeng-editor.ts"], "sourcesContent": ["import { CommonModule, isPlatformServer } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    Component,\n    ContentChild,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    TemplateRef,\n    ViewEncapsulation,\n    afterNextRender,\n    forwardRef\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { EditorInitEvent, EditorSelectionChangeEvent, EditorTextChangeEvent } from './editor.interface';\n\nexport const EDITOR_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Editor),\n    multi: true\n};\n/**\n * Editor groups a collection of contents in tabs.\n * @group Components\n */\n@Component({\n    selector: 'p-editor',\n    template: `\n        <div [ngClass]=\"'p-editor-container'\" [class]=\"styleClass\">\n            <div class=\"p-editor-toolbar\" *ngIf=\"toolbar || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-editor-toolbar\" *ngIf=\"!toolbar && !headerTemplate\">\n                <span class=\"ql-formats\">\n                    <select class=\"ql-header\">\n                        <option value=\"1\">Heading</option>\n                        <option value=\"2\">Subheading</option>\n                        <option selected>Normal</option>\n                    </select>\n                    <select class=\"ql-font\">\n                        <option selected>Sans Serif</option>\n                        <option value=\"serif\">Serif</option>\n                        <option value=\"monospace\">Monospace</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-bold\" aria-label=\"Bold\" type=\"button\"></button>\n                    <button class=\"ql-italic\" aria-label=\"Italic\" type=\"button\"></button>\n                    <button class=\"ql-underline\" aria-label=\"Underline\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <select class=\"ql-color\"></select>\n                    <select class=\"ql-background\"></select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-list\" value=\"ordered\" aria-label=\"Ordered List\" type=\"button\"></button>\n                    <button class=\"ql-list\" value=\"bullet\" aria-label=\"Unordered List\" type=\"button\"></button>\n                    <select class=\"ql-align\">\n                        <option selected></option>\n                        <option value=\"center\">center</option>\n                        <option value=\"right\">right</option>\n                        <option value=\"justify\">justify</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-link\" aria-label=\"Insert Link\" type=\"button\"></button>\n                    <button class=\"ql-image\" aria-label=\"Insert Image\" type=\"button\"></button>\n                    <button class=\"ql-code-block\" aria-label=\"Insert Code Block\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-clean\" aria-label=\"Remove Styles\" type=\"button\"></button>\n                </span>\n            </div>\n            <div class=\"p-editor-content\" [ngStyle]=\"style\"></div>\n        </div>\n    `,\n    providers: [EDITOR_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    styleUrls: ['./editor.css'],\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Editor implements AfterContentInit, ControlValueAccessor {\n    /**\n     * Inline style of the container.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the container.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Placeholder text to show when editor is empty.\n     * @group Props\n     */\n    @Input() placeholder: string | undefined;\n    /**\n     * Whitelist of formats to display, see here for available options.\n     * @group Props\n     */\n    @Input() formats: string[] | undefined;\n    /**\n     * Modules configuration of Editor, see here for available options.\n     * @group Props\n     */\n    @Input() modules: object | undefined;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, within which the editor’s p elements (i.e. tooltips, etc.) should be confined. Currently, it only considers left and right boundaries.\n     * @group Props\n     */\n    @Input() bounds: HTMLElement | string | undefined;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, specifying which container has the scrollbars (i.e. overflow-y: auto), if is has been changed from the default ql-editor with custom CSS. Necessary to fix scroll jumping bugs when Quill is set to auto grow its height, and another ancestor container is responsible from the scrolling..\n     * @group Props\n     */\n    @Input() scrollingContainer: HTMLElement | string | undefined;\n    /**\n     * Shortcut for debug. Note debug is a static method and will affect other instances of Quill editors on the page. Only warning and error messages are enabled by default.\n     * @group Props\n     */\n    @Input() debug: string | undefined;\n    /**\n     * Whether to instantiate the editor to read-only mode.\n     * @group Props\n     */\n    @Input() get readonly(): boolean {\n        return this._readonly;\n    }\n    set readonly(val: boolean) {\n        this._readonly = val;\n\n        if (this.quill) {\n            if (this._readonly) this.quill.disable();\n            else this.quill.enable();\n        }\n    }\n    /**\n     * Callback to invoke when the quill modules are loaded.\n     * @param {EditorInitEvent} event - custom event.\n     * @group Emits\n     */\n    @Output() onInit: EventEmitter<EditorInitEvent> = new EventEmitter<EditorInitEvent>();\n    /**\n     * Callback to invoke when text of editor changes.\n     * @param {EditorTextChangeEvent} event - custom event.\n     * @group Emits\n     */\n    @Output() onTextChange: EventEmitter<EditorTextChangeEvent> = new EventEmitter<EditorTextChangeEvent>();\n    /**\n     * Callback to invoke when selection of the text changes.\n     * @param {EditorSelectionChangeEvent} event - custom event.\n     * @group Emits\n     */\n    @Output() onSelectionChange: EventEmitter<EditorSelectionChangeEvent> = new EventEmitter<EditorSelectionChangeEvent>();\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    @ContentChild(Header) toolbar: any;\n\n    value: Nullable<string>;\n\n    delayedCommand: Function | null = null;\n\n    _readonly: boolean = false;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    quill: any;\n\n    dynamicQuill: any;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    private get isAttachedQuillEditorToDOM(): boolean | undefined {\n        return this.quillElements?.editorElement?.isConnected;\n    }\n\n    private quillElements!: { editorElement: HTMLElement; toolbarElement: HTMLElement };\n\n    constructor(public el: ElementRef, @Inject(PLATFORM_ID) private platformId: object) {\n        /**\n         * Read or write the DOM once, when initializing non-Angular (Quill) library.\n         */\n        afterNextRender(() => {\n            this.initQuillElements();\n            this.initQuillEditor();\n        });\n    }\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n\n        if (this.quill) {\n            if (value) {\n                const command = (): void => {\n                    this.quill.setContents(this.quill.clipboard.convert(this.dynamicQuill.version.startsWith('2') ? { html: this.value } : this.value));\n                };\n\n                if (this.isAttachedQuillEditorToDOM) {\n                    command();\n                } else {\n                    this.delayedCommand = command;\n                }\n            } else {\n                const command = (): void => {\n                    this.quill.setText('');\n                };\n\n                if (this.isAttachedQuillEditorToDOM) {\n                    command();\n                } else {\n                    this.delayedCommand = command;\n                }\n            }\n        }\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    getQuill() {\n        return this.quill;\n    }\n\n    private initQuillEditor(): void {\n        if (isPlatformServer(this.platformId)) {\n            return;\n        }\n\n        /**\n         * Importing Quill at top level, throws `document is undefined` error during when\n         * building for SSR, so this dynamically loads quill when it's in browser module.\n         */\n        if (!this.dynamicQuill) {\n            import('quill')\n                .then((quillModule: any) => {\n                    this.dynamicQuill = quillModule.default;\n                    this.createQuillEditor();\n                })\n                .catch((e) => console.error(e.message));\n        } else {\n            this.createQuillEditor();\n        }\n    }\n\n    private createQuillEditor(): void {\n        this.initQuillElements();\n\n        const { toolbarElement, editorElement } = this.quillElements;\n        let defaultModule = { toolbar: toolbarElement };\n        let modules = this.modules ? { ...defaultModule, ...this.modules } : defaultModule;\n        this.quill = new this.dynamicQuill(editorElement, {\n            modules: modules,\n            placeholder: this.placeholder,\n            readOnly: this.readonly,\n            theme: 'snow',\n            formats: this.formats,\n            bounds: this.bounds,\n            debug: this.debug,\n            scrollingContainer: this.scrollingContainer\n        });\n\n        const isQuill2 = this.dynamicQuill.version.startsWith('2');\n\n        if (this.value) {\n            this.quill.setContents(this.quill.clipboard.convert(isQuill2 ? { html: this.value } : this.value));\n        }\n\n        this.quill.on('text-change', (delta: any, oldContents: any, source: any) => {\n            if (source === 'user') {\n                let html = isQuill2 ? this.quill.getSemanticHTML() : DomHandler.findSingle(editorElement, '.ql-editor').innerHTML;\n                let text = this.quill.getText().trim();\n                if (html === '<p><br></p>') {\n                    html = null;\n                }\n\n                this.onTextChange.emit({\n                    htmlValue: html,\n                    textValue: text,\n                    delta: delta,\n                    source: source\n                });\n\n                this.onModelChange(html);\n                this.onModelTouched();\n            }\n        });\n\n        this.quill.on('selection-change', (range: string, oldRange: string, source: string) => {\n            this.onSelectionChange.emit({\n                range: range,\n                oldRange: oldRange,\n                source: source\n            });\n        });\n\n        this.onInit.emit({\n            editor: this.quill\n        });\n    }\n\n    private initQuillElements(): void {\n        if (!this.quillElements) {\n            this.quillElements = {\n                editorElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-content'),\n                toolbarElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-toolbar')\n            };\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Editor, SharedModule],\n    declarations: [Editor]\n})\nexport class EditorModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;AA0Ba,MAAA,qBAAqB,GAAQ;AACtC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,MAAM,CAAC;AACrC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MA6DU,MAAM,CAAA;AAqGI,IAAA,EAAA,CAAA;AAA6C,IAAA,UAAA,CAAA;AApGhE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,OAAO,CAAuB;AACvC;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,MAAM,CAAmC;AAClD;;;AAGG;AACM,IAAA,kBAAkB,CAAmC;AAC9D;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,GAAY,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QAErB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,IAAI,CAAC,SAAS;AAAE,gBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;;AACpC,gBAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;AAC5B,SAAA;KACJ;AACD;;;;AAIG;AACO,IAAA,MAAM,GAAkC,IAAI,YAAY,EAAmB,CAAC;AACtF;;;;AAIG;AACO,IAAA,YAAY,GAAwC,IAAI,YAAY,EAAyB,CAAC;AACxG;;;;AAIG;AACO,IAAA,iBAAiB,GAA6C,IAAI,YAAY,EAA8B,CAAC;AAEvF,IAAA,SAAS,CAA4B;AAE/C,IAAA,OAAO,CAAM;AAEnC,IAAA,KAAK,CAAmB;IAExB,cAAc,GAAoB,IAAI,CAAC;IAEvC,SAAS,GAAY,KAAK,CAAC;AAE3B,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,KAAK,CAAM;AAEX,IAAA,YAAY,CAAM;AAElB,IAAA,cAAc,CAA6B;AAE3C,IAAA,IAAY,0BAA0B,GAAA;AAClC,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC;KACzD;AAEO,IAAA,aAAa,CAA+D;IAEpF,WAAmB,CAAA,EAAc,EAA+B,UAAkB,EAAA;QAA/D,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;AAC9E;;AAEG;QACH,eAAe,CAAC,MAAK;YACjB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,KAAK,EAAE;gBACP,MAAM,OAAO,GAAG,MAAW;AACvB,oBAAA,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxI,iBAAC,CAAC;gBAEF,IAAI,IAAI,CAAC,0BAA0B,EAAE;AACjC,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;AACjC,iBAAA;AACJ,aAAA;AAAM,iBAAA;gBACH,MAAM,OAAO,GAAG,MAAW;AACvB,oBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC3B,iBAAC,CAAC;gBAEF,IAAI,IAAI,CAAC,0BAA0B,EAAE;AACjC,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;AACjC,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;IAED,QAAQ,GAAA;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;IAEO,eAAe,GAAA;AACnB,QAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACnC,OAAO;AACV,SAAA;AAED;;;AAGG;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,OAAO,CAAC;AACV,iBAAA,IAAI,CAAC,CAAC,WAAgB,KAAI;AACvB,gBAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC;gBACxC,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAC/C,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5B,SAAA;KACJ;IAEO,iBAAiB,GAAA;QACrB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC;AAC7D,QAAA,IAAI,aAAa,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;QAChD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,aAAa,CAAC;QACnF,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;AAC9C,YAAA,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,YAAA,KAAK,EAAE,MAAM;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;AAC9C,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAE3D,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACtG,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,KAAU,EAAE,WAAgB,EAAE,MAAW,KAAI;YACvE,IAAI,MAAM,KAAK,MAAM,EAAE;gBACnB,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,SAAS,CAAC;gBAClH,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;gBACvC,IAAI,IAAI,KAAK,aAAa,EAAE;oBACxB,IAAI,GAAG,IAAI,CAAC;AACf,iBAAA;AAED,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACnB,oBAAA,SAAS,EAAE,IAAI;AACf,oBAAA,SAAS,EAAE,IAAI;AACf,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,MAAM,EAAE,MAAM;AACjB,iBAAA,CAAC,CAAC;AAEH,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACzB,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,KAAa,EAAE,QAAgB,EAAE,MAAc,KAAI;AAClF,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AACxB,gBAAA,KAAK,EAAE,KAAK;AACZ,gBAAA,QAAQ,EAAE,QAAQ;AAClB,gBAAA,MAAM,EAAE,MAAM;AACjB,aAAA,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,MAAM,EAAE,IAAI,CAAC,KAAK;AACrB,SAAA,CAAC,CAAC;KACN;IAEO,iBAAiB,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG;AACjB,gBAAA,aAAa,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,sBAAsB,CAAC;AACnF,gBAAA,cAAc,EAAE,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,sBAAsB,CAAC;aACvF,CAAC;AACL,SAAA;KACJ;AArPQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAM,4CAqG4B,WAAW,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FArG7C,MAAM,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,YAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EARJ,CAAC,qBAAqB,CAAC,+DAqFpB,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAFH,aAAa,EArIpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,mIAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FASQ,MAAM,EAAA,UAAA,EAAA,CAAA;kBA5DlB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EACV,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiDT,EACU,SAAA,EAAA,CAAC,qBAAqB,CAAC,EACjB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAEhC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAC/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,mIAAA,CAAA,EAAA,CAAA;;0BAuGmC,MAAM;2BAAC,WAAW,CAAA;yCAhG7C,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAgBI,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAER,OAAO,EAAA,CAAA;sBAA5B,YAAY;uBAAC,MAAM,CAAA;;MAgLX,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,iBA7PZ,MAAM,CAAA,EAAA,OAAA,EAAA,CAyPL,YAAY,CAzPb,EAAA,OAAA,EAAA,CAAA,MAAM,EA0PG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGrB,YAAY,EAAA,OAAA,EAAA,CAJX,YAAY,EACJ,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGrB,YAAY,EAAA,UAAA,EAAA,CAAA;kBALxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;oBAC/B,YAAY,EAAE,CAAC,MAAM,CAAC;AACzB,iBAAA,CAAA;;;AC3VD;;AAEG;;;;"}