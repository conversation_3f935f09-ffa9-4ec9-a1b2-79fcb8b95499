import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SeriesApi } from './api/series.api';
import { SeriesData } from './api/series';
import { SeriesService } from './services/series.service';

const API = [SeriesApi];

const SERVICES = [
  {provide: SeriesData, useClass: SeriesService},
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ]
})
export class CardsServicesModule {
  static forRoot(): ModuleWithProviders<CardsServicesModule> {
    return {
      ngModule: CardsServicesModule,
      providers: [
        ...API,
        ...SERVICES
      ]
    };
  }
}
