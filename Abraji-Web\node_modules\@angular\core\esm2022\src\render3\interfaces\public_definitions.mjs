/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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