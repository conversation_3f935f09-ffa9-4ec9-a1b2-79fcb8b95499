/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { noSideEffects } from './closure';
export const ANNOTATIONS = '__annotations__';
export const PARAMETERS = '__parameters__';
export const PROP_METADATA = '__prop__metadata__';
/**
 * @suppress {globalThis}
 */
export function makeDecorator(name, props, parentClass, additionalProcessing, typeFn) {
    return noSideEffects(() => {
        const metaCtor = makeMetadataCtor(props);
        function DecoratorFactory(...args) {
            if (this instanceof DecoratorFactory) {
                metaCtor.call(this, ...args);
                return this;
            }
            const annotationInstance = new DecoratorFactory(...args);
            return function TypeDecorator(cls) {
                if (typeFn)
                    typeFn(cls, ...args);
                // Use of Object.defineProperty is important since it creates non-enumerable property which
                // prevents the property is copied during subclassing.
                const annotations = cls.hasOwnProperty(ANNOTATIONS)
                    ? cls[ANNOTATIONS]
                    : Object.defineProperty(cls, ANNOTATIONS, { value: [] })[ANNOTATIONS];
                annotations.push(annotationInstance);
                if (additionalProcessing)
                    additionalProcessing(cls);
                return cls;
            };
        }
        if (parentClass) {
            DecoratorFactory.prototype = Object.create(parentClass.prototype);
        }
        DecoratorFactory.prototype.ngMetadataName = name;
        DecoratorFactory.annotationCls = DecoratorFactory;
        return DecoratorFactory;
    });
}
function makeMetadataCtor(props) {
    return function ctor(...args) {
        if (props) {
            const values = props(...args);
            for (const propName in values) {
                this[propName] = values[propName];
            }
        }
    };
}
export function makeParamDecorator(name, props, parentClass) {
    return noSideEffects(() => {
        const metaCtor = makeMetadataCtor(props);
        function ParamDecoratorFactory(...args) {
            if (this instanceof ParamDecoratorFactory) {
                metaCtor.apply(this, args);
                return this;
            }
            const annotationInstance = new ParamDecoratorFactory(...args);
            ParamDecorator.annotation = annotationInstance;
            return ParamDecorator;
            function ParamDecorator(cls, unusedKey, index) {
                // Use of Object.defineProperty is important since it creates non-enumerable property which
                // prevents the property is copied during subclassing.
                const parameters = cls.hasOwnProperty(PARAMETERS)
                    ? cls[PARAMETERS]
                    : Object.defineProperty(cls, PARAMETERS, { value: [] })[PARAMETERS];
                // there might be gaps if some in between parameters do not have annotations.
                // we pad with nulls.
                while (parameters.length <= index) {
                    parameters.push(null);
                }
                (parameters[index] = parameters[index] || []).push(annotationInstance);
                return cls;
            }
        }
        if (parentClass) {
            ParamDecoratorFactory.prototype = Object.create(parentClass.prototype);
        }
        ParamDecoratorFactory.prototype.ngMetadataName = name;
        ParamDecoratorFactory.annotationCls = ParamDecoratorFactory;
        return ParamDecoratorFactory;
    });
}
export function makePropDecorator(name, props, parentClass, additionalProcessing) {
    return noSideEffects(() => {
        const metaCtor = makeMetadataCtor(props);
        function PropDecoratorFactory(...args) {
            if (this instanceof PropDecoratorFactory) {
                metaCtor.apply(this, args);
                return this;
            }
            const decoratorInstance = new PropDecoratorFactory(...args);
            function PropDecorator(target, name) {
                // target is undefined with standard decorators. This case is not supported and will throw
                // if this decorator is used in JIT mode with standard decorators.
                if (target === undefined) {
                    throw new Error('Standard Angular field decorators are not supported in JIT mode.');
                }
                const constructor = target.constructor;
                // Use of Object.defineProperty is important because it creates a non-enumerable property
                // which prevents the property from being copied during subclassing.
                const meta = constructor.hasOwnProperty(PROP_METADATA)
                    ? constructor[PROP_METADATA]
                    : Object.defineProperty(constructor, PROP_METADATA, { value: {} })[PROP_METADATA];
                meta[name] = (meta.hasOwnProperty(name) && meta[name]) || [];
                meta[name].unshift(decoratorInstance);
                if (additionalProcessing)
                    additionalProcessing(target, name, ...args);
            }
            return PropDecorator;
        }
        if (parentClass) {
            PropDecoratorFactory.prototype = Object.create(parentClass.prototype);
        }
        PropDecoratorFactory.prototype.ngMetadataName = name;
        PropDecoratorFactory.annotationCls = PropDecoratorFactory;
        return PropDecoratorFactory;
    });
}
//# sourceMappingURL=data:application/json;base64,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