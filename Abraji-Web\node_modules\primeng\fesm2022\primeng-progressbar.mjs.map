{"version": 3, "file": "primeng-progressbar.mjs", "sources": ["../../src/app/components/progressbar/progressbar.ts", "../../src/app/components/progressbar/primeng-progressbar.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, TemplateRef, ContentChildren, Input, NgModule, ViewEncapsulation, booleanAttribute, numberAttribute } from '@angular/core';\nimport { PrimeTemplate } from 'primeng/api';\nimport { QueryList } from '@angular/core';\n/**\n * ProgressBar is a process status indicator.\n * @group Components\n */\n@Component({\n    selector: 'p-progressBar',\n    template: `\n        <div\n            role=\"progressbar\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            [attr.aria-valuemin]=\"0\"\n            [attr.aria-valuenow]=\"value\"\n            [attr.aria-valuemax]=\"100\"\n            [attr.data-pc-name]=\"'progressbar'\"\n            [attr.data-pc-section]=\"'root'\"\n            [ngClass]=\"{ 'p-progressbar p-component': true, 'p-progressbar-determinate': mode === 'determinate', 'p-progressbar-indeterminate': mode === 'indeterminate' }\"\n        >\n            <div\n                *ngIf=\"mode === 'determinate'\"\n                class=\"p-progressbar-value p-progressbar-value-animate\"\n                [ngStyle]=\"{\n                    width: value + '%',\n                    display: 'flex',\n                    background: color\n                }\"\n                [attr.data-pc-section]=\"'value'\"\n            >\n                <div class=\"p-progressbar-label\">\n                    <div\n                        *ngIf=\"showValue && !contentTemplate\"\n                        [ngStyle]=\"{\n                            display: value != null && value !== 0 ? 'flex' : 'none'\n                        }\"\n                        [attr.data-pc-section]=\"'label'\"\n                    >\n                        {{ value }}{{ unit }}\n                    </div>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: value }\"></ng-container>\n                </div>\n            </div>\n            <div *ngIf=\"mode === 'indeterminate'\" class=\"p-progressbar-indeterminate-container\" [attr.data-pc-section]=\"'container'\">\n                <div\n                    class=\"p-progressbar-value p-progressbar-value-animate\"\n                    [ngStyle]=\"{\n                        background: color\n                    }\"\n                    [attr.data-pc-section]=\"'value'\"\n                ></div>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./progressbar.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ProgressBar {\n    /**\n     * Current value of the progress.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) value: number | undefined;\n    /**\n     * Whether to display the progress bar value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showValue: boolean = true;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Unit sign appended to the value.\n     * @group Props\n     */\n    @Input() unit: string = '%';\n    /**\n     * Defines the mode of the progress\n     * @group Props\n     */\n    @Input() mode: string = 'determinate';\n    /**\n     * Color for the background of the progress.\n     * @group Props\n     */\n    @Input() color: string | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n            }\n        });\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [ProgressBar],\n    declarations: [ProgressBar]\n})\nexport class ProgressBarModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAIA;;;AAGG;MAwDU,WAAW,CAAA;AACpB;;;AAGG;AACoC,IAAA,KAAK,CAAqB;AACjE;;;AAGG;IACqC,SAAS,GAAY,IAAI,CAAC;AAClE;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;IACM,IAAI,GAAW,GAAG,CAAC;AAC5B;;;AAGG;IACM,IAAI,GAAW,aAAa,CAAC;AACtC;;;AAGG;AACM,IAAA,KAAK,CAAqB;AAEH,IAAA,SAAS,CAAuC;AAEhF,IAAA,eAAe,CAA+B;IAE9C,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,aAAA;AACL,SAAC,CAAC,CAAC;KACN;uGAnDQ,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,iEAKA,eAAe,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKf,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA2BnB,aAAa,EA1FpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,unDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,WAAW,EAAA,UAAA,EAAA,CAAA;kBAvDvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,unDAAA,CAAA,EAAA,CAAA;8BAOsC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAE0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAsBrB,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EA3DjB,YAAA,EAAA,CAAA,WAAW,CAuDV,EAAA,OAAA,EAAA,CAAA,YAAY,aAvDb,WAAW,CAAA,EAAA,CAAA,CAAA;AA2DX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAJhB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,WAAW,CAAC;oBACtB,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;ACzHD;;AAEG;;;;"}