{"version": 3, "file": "primeng-galleria.mjs", "sources": ["../../src/app/components/galleria/galleria.ts", "../../src/app/components/galleria/primeng-galleria.ts"], "sourcesContent": ["import { AnimationEvent, animate, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentChecked,\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    DoCheck,\n    ElementRef,\n    EventEmitter,\n    HostListener,\n    Inject,\n    Input,\n    KeyValueDiffers,\n    NgModule,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { WindowMaximizeIcon } from 'primeng/icons/windowmaximize';\nimport { WindowMinimizeIcon } from 'primeng/icons/windowminimize';\nimport { RippleModule } from 'primeng/ripple';\nimport { VoidListener } from 'primeng/ts-helpers';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { GalleriaResponsiveOptions } from './galleria.interface';\nimport { FocusTrapModule } from 'primeng/focustrap';\n/**\n * Galleria is an advanced content gallery component.\n * @group Components\n */\n@Component({\n    selector: 'p-galleria',\n    template: `\n        <div *ngIf=\"fullScreen; else windowed\" #container>\n            <div\n                *ngIf=\"maskVisible\"\n                #mask\n                [ngClass]=\"{ 'p-galleria-mask p-component-overlay p-component-overlay-enter': true, 'p-galleria-visible': this.visible }\"\n                [class]=\"maskClass\"\n                [attr.role]=\"fullScreen ? 'dialog' : 'region'\"\n                [attr.aria-modal]=\"fullScreen ? 'true' : undefined\"\n            >\n                <p-galleriaContent\n                    *ngIf=\"visible\"\n                    [@animation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n                    (@animation.start)=\"onAnimationStart($event)\"\n                    (@animation.done)=\"onAnimationEnd($event)\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [numVisible]=\"numVisibleLimit || numVisible\"\n                    (maskHide)=\"onMaskHide()\"\n                    (activeItemChange)=\"onActiveItemChange($event)\"\n                    [ngStyle]=\"containerStyle\"\n                    [fullScreen]=\"fullScreen\"\n                ></p-galleriaContent>\n            </div>\n        </div>\n\n        <ng-template #windowed>\n            <p-galleriaContent [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisibleLimit || numVisible\" (activeItemChange)=\"onActiveItemChange($event)\"></p-galleriaContent>\n        </ng-template>\n    `,\n    animations: [\n        trigger('animation', [\n            transition('void => visible', [style({ transform: 'scale(0.7)', opacity: 0 }), animate('{{showTransitionParams}}')]),\n            transition('visible => void', [animate('{{hideTransitionParams}}', style({ transform: 'scale(0.7)', opacity: 0 }))])\n        ])\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./galleria.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Galleria implements OnChanges, OnDestroy {\n    /**\n     * Index of the first item.\n     * @group Props\n     */\n    @Input() get activeIndex(): number {\n        return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n        this._activeIndex = activeIndex;\n    }\n    /**\n     * Whether to display the component on fullscreen.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) fullScreen: boolean = false;\n    /**\n     * Unique identifier of the element.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    @Input() value: any[] | undefined;\n    /**\n     * Number of items per page.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) numVisible: number = 3;\n    /**\n     * An array of options for responsive design.\n     * @see {GalleriaResponsiveOptions}\n     * @group Props\n     */\n    @Input() responsiveOptions: GalleriaResponsiveOptions[] | undefined;\n    /**\n     * Whether to display navigation buttons in item section.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showItemNavigators: boolean = false;\n    /**\n     * Whether to display navigation buttons in thumbnail container.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showThumbnailNavigators: boolean = true;\n    /**\n     * Whether to display navigation buttons on item hover.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showItemNavigatorsOnHover: boolean = false;\n    /**\n     * When enabled, item is changed on indicator hover.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) changeItemOnIndicatorHover: boolean = false;\n    /**\n     * Defines if scrolling would be infinite.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) circular: boolean = false;\n    /**\n     * Items are displayed with a slideshow in autoPlay mode.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoPlay: boolean = false;\n    /**\n     * When enabled, autorun should stop by click.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) shouldStopAutoplayByClick: boolean = true;\n    /**\n     * Time in milliseconds to scroll items.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) transitionInterval: number = 4000;\n    /**\n     * Whether to display thumbnail container.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showThumbnails: boolean = true;\n    /**\n     * Position of thumbnails.\n     * @group Props\n     */\n    @Input() thumbnailsPosition: 'bottom' | 'top' | 'left' | 'right' | undefined = 'bottom';\n    /**\n     * Height of the viewport in vertical thumbnail.\n     * @group Props\n     */\n    @Input() verticalThumbnailViewPortHeight: string = '300px';\n    /**\n     * Whether to display indicator container.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showIndicators: boolean = false;\n    /**\n     * When enabled, indicator container is displayed on item container.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showIndicatorsOnItem: boolean = false;\n    /**\n     * Position of indicators.\n     * @group Props\n     */\n    @Input() indicatorsPosition: 'bottom' | 'top' | 'left' | 'right' | undefined = 'bottom';\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Style class of the mask on fullscreen mode.\n     * @group Props\n     */\n    @Input() maskClass: string | undefined;\n    /**\n     * Style class of the component on fullscreen mode. Otherwise, the 'class' property can be used.\n     * @group Props\n     */\n    @Input() containerClass: string | undefined;\n    /**\n     * Inline style of the component on fullscreen mode. Otherwise, the 'style' property can be used.\n     * @group Props\n     */\n    @Input() containerStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Specifies the visibility of the mask on fullscreen mode.\n     * @group Props\n     */\n    @Input() get visible(): boolean {\n        return this._visible;\n    }\n    set visible(visible: boolean) {\n        this._visible = visible;\n\n        if (this._visible && !this.maskVisible) {\n            this.maskVisible = true;\n        }\n    }\n    /**\n     * Callback to invoke on active index change.\n     * @param {number} number - Active index.\n     * @group Emits\n     */\n    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter<number>();\n    /**\n     * Callback to invoke on visiblity change.\n     * @param {boolean} boolean - Visible value.\n     * @group Emits\n     */\n    @Output() visibleChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n    @ViewChild('mask') mask: ElementRef | undefined;\n\n    @ViewChild('container') container: ElementRef | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    _visible: boolean = false;\n\n    _activeIndex: number = 0;\n\n    headerFacet: any;\n\n    footerFacet: any;\n\n    indicatorFacet: any;\n\n    captionFacet: any;\n\n    closeIconTemplate: TemplateRef<any> | undefined;\n\n    previousThumbnailIconTemplate: TemplateRef<any> | undefined;\n\n    nextThumbnailIconTemplate: TemplateRef<any> | undefined;\n\n    itemPreviousIconTemplate: TemplateRef<any> | undefined;\n\n    itemNextIconTemplate: TemplateRef<any> | undefined;\n\n    maskVisible: boolean = false;\n\n    numVisibleLimit = 0;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) public platformId: any, public element: ElementRef, public cd: ChangeDetectorRef, public config: PrimeNGConfig) {}\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerFacet = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerFacet = item.template;\n                    break;\n\n                case 'indicator':\n                    this.indicatorFacet = item.template;\n                    break;\n\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n\n                case 'itemnexticon':\n                    this.itemNextIconTemplate = item.template;\n                    break;\n\n                case 'itempreviousicon':\n                    this.itemPreviousIconTemplate = item.template;\n                    break;\n\n                case 'previousthumbnailicon':\n                    this.previousThumbnailIconTemplate = item.template;\n                    break;\n\n                case 'nextthumbnailicon':\n                    this.nextThumbnailIconTemplate = item.template;\n                    break;\n\n                case 'caption':\n                    this.captionFacet = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngOnChanges(simpleChanges: SimpleChanges) {\n        if (simpleChanges.value && simpleChanges.value.currentValue?.length < this.numVisible) {\n            this.numVisibleLimit = simpleChanges.value.currentValue.length;\n        } else {\n            this.numVisibleLimit = 0;\n        }\n    }\n\n    onMaskHide() {\n        this.visible = false;\n        this.visibleChange.emit(false);\n    }\n\n    onActiveItemChange(index: number) {\n        if (this.activeIndex !== index) {\n            this.activeIndex = index;\n            this.activeIndexChange.emit(index);\n        }\n    }\n\n    onAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                this.enableModality();\n                setTimeout(() => {\n                    DomHandler.focus(DomHandler.findSingle(this.container.nativeElement, '[data-pc-section=\"closebutton\"]'));\n                }, 25);\n                break;\n\n            case 'void':\n                DomHandler.addClass(this.mask?.nativeElement, 'p-component-overlay-leave');\n                break;\n        }\n    }\n\n    onAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                this.disableModality();\n                break;\n        }\n    }\n\n    enableModality() {\n        DomHandler.blockBodyScroll();\n        this.cd.markForCheck();\n\n        if (this.mask) {\n            ZIndexUtils.set('modal', this.mask.nativeElement, this.baseZIndex || this.config.zIndex.modal);\n        }\n    }\n\n    disableModality() {\n        DomHandler.unblockBodyScroll();\n        this.maskVisible = false;\n        this.cd.markForCheck();\n\n        if (this.mask) {\n            ZIndexUtils.clear(this.mask.nativeElement);\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.fullScreen) {\n            DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n        }\n\n        if (this.mask) {\n            this.disableModality();\n        }\n    }\n}\n\n@Component({\n    selector: 'p-galleriaContent',\n    template: `\n        <div\n            [attr.id]=\"id\"\n            [attr.role]=\"'region'\"\n            *ngIf=\"value && value.length > 0\"\n            [ngClass]=\"{\n                'p-galleria p-component': true,\n                'p-galleria-fullscreen': this.galleria.fullScreen,\n                'p-galleria-indicator-onitem': this.galleria.showIndicatorsOnItem,\n                'p-galleria-item-nav-onhover': this.galleria.showItemNavigatorsOnHover && !this.galleria.fullScreen\n            }\"\n            [ngStyle]=\"!galleria.fullScreen ? galleria.containerStyle : {}\"\n            [class]=\"galleriaClass()\"\n            pFocusTrap\n            [pFocusTrapDisabled]=\"!fullScreen\"\n        >\n            <button *ngIf=\"galleria.fullScreen\" type=\"button\" class=\"p-galleria-close p-link\" (click)=\"maskHide.emit()\" pRipple [attr.aria-label]=\"closeAriaLabel()\" [attr.data-pc-section]=\"'closebutton'\">\n                <TimesIcon *ngIf=\"!galleria.closeIconTemplate\" [styleClass]=\"'p-galleria-close-icon'\" />\n                <ng-template *ngTemplateOutlet=\"galleria.closeIconTemplate\"></ng-template>\n            </button>\n            <div *ngIf=\"galleria.templates && galleria.headerFacet\" class=\"p-galleria-header\">\n                <p-galleriaItemSlot type=\"header\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n            <div class=\"p-galleria-content\" [attr.aria-live]=\"galleria.autoPlay ? 'polite' : 'off'\">\n                <p-galleriaItem\n                    [id]=\"id\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [circular]=\"galleria.circular\"\n                    [templates]=\"galleria.templates\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [showIndicators]=\"galleria.showIndicators\"\n                    [changeItemOnIndicatorHover]=\"galleria.changeItemOnIndicatorHover\"\n                    [indicatorFacet]=\"galleria.indicatorFacet\"\n                    [captionFacet]=\"galleria.captionFacet\"\n                    [showItemNavigators]=\"galleria.showItemNavigators\"\n                    [autoPlay]=\"galleria.autoPlay\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (startSlideShow)=\"startSlideShow()\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaItem>\n\n                <p-galleriaThumbnails\n                    *ngIf=\"galleria.showThumbnails\"\n                    [containerId]=\"id\"\n                    [value]=\"value\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [activeIndex]=\"activeIndex\"\n                    [templates]=\"galleria.templates\"\n                    [numVisible]=\"numVisible\"\n                    [responsiveOptions]=\"galleria.responsiveOptions\"\n                    [circular]=\"galleria.circular\"\n                    [isVertical]=\"isVertical()\"\n                    [contentHeight]=\"galleria.verticalThumbnailViewPortHeight\"\n                    [showThumbnailNavigators]=\"galleria.showThumbnailNavigators\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaThumbnails>\n            </div>\n            <div *ngIf=\"galleria.templates && galleria.footerFacet\" class=\"p-galleria-footer\">\n                <p-galleriaItemSlot type=\"footer\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class GalleriaContent implements DoCheck {\n    @Input() get activeIndex(): number {\n        return this._activeIndex;\n    }\n    set activeIndex(activeIndex: number) {\n        this._activeIndex = activeIndex;\n    }\n\n    @Input() value: any[] = [];\n\n    @Input({ transform: numberAttribute }) numVisible: number | undefined;\n\n    @Input({ transform: booleanAttribute }) fullScreen: boolean;\n\n    @Output() maskHide: EventEmitter<boolean> = new EventEmitter();\n\n    @Output() activeItemChange: EventEmitter<number> = new EventEmitter();\n\n    @ViewChild('closeButton') closeButton: ElementRef | undefined;\n\n    id: string;\n\n    _activeIndex: number = 0;\n\n    slideShowActive: boolean = true;\n\n    interval: any;\n\n    styleClass: string | undefined;\n\n    private differ: any;\n\n    constructor(public galleria: Galleria, public cd: ChangeDetectorRef, private differs: KeyValueDiffers, public config: PrimeNGConfig, private elementRef: ElementRef) {\n        this.id = this.galleria.id || UniqueComponentId();\n        this.differ = this.differs.find(this.galleria).create();\n    }\n\n    // For custom fullscreen\n    @HostListener('document:fullscreenchange', ['$event'])\n    handleFullscreenChange(event: Event) {\n        if (document?.fullscreenElement === this.elementRef.nativeElement?.children[0]) {\n            this.fullScreen = true;\n        } else {\n            this.fullScreen = false;\n        }\n    }\n\n    ngDoCheck(): void {\n        if (isPlatformBrowser(this.galleria.platformId)) {\n            const changes = this.differ.diff(this.galleria as unknown as Record<string, unknown>);\n            if (changes && changes.forEachItem.length > 0) {\n                // Because we change the properties of the parent component,\n                // and the children take our entity from the injector.\n                // We can tell the children to redraw themselves when we change the properties of the parent component.\n                // Since we have an onPush strategy\n                this.cd.markForCheck();\n            }\n        }\n    }\n\n    galleriaClass() {\n        const thumbnailsPosClass = this.galleria.showThumbnails && this.getPositionClass('p-galleria-thumbnails', this.galleria.thumbnailsPosition as string);\n        const indicatorPosClass = this.galleria.showIndicators && this.getPositionClass('p-galleria-indicators', this.galleria.indicatorsPosition as string);\n\n        return (this.galleria.containerClass ? this.galleria.containerClass + ' ' : '') + (thumbnailsPosClass ? thumbnailsPosClass + ' ' : '') + (indicatorPosClass ? indicatorPosClass + ' ' : '');\n    }\n\n    startSlideShow() {\n        if (isPlatformBrowser(this.galleria.platformId)) {\n            this.interval = setInterval(() => {\n                let activeIndex = this.galleria.circular && this.value.length - 1 === this.activeIndex ? 0 : this.activeIndex + 1;\n                this.onActiveIndexChange(activeIndex);\n                this.activeIndex = activeIndex;\n            }, this.galleria.transitionInterval);\n\n            this.slideShowActive = true;\n        }\n    }\n\n    stopSlideShow() {\n        if (this.galleria.autoPlay && !this.galleria.shouldStopAutoplayByClick) {\n            return;\n        }\n\n        if (this.interval) {\n            clearInterval(this.interval);\n        }\n\n        this.slideShowActive = false;\n    }\n\n    getPositionClass(preClassName: string, position: string) {\n        const positions = ['top', 'left', 'bottom', 'right'];\n        const pos = positions.find((item) => item === position);\n\n        return pos ? `${preClassName}-${pos}` : '';\n    }\n\n    isVertical() {\n        return this.galleria.thumbnailsPosition === 'left' || this.galleria.thumbnailsPosition === 'right';\n    }\n\n    onActiveIndexChange(index: number) {\n        if (this.activeIndex !== index) {\n            this.activeIndex = index;\n            this.activeItemChange.emit(this.activeIndex);\n        }\n    }\n\n    closeAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n    }\n}\n\n@Component({\n    selector: 'p-galleriaItemSlot',\n    template: `\n        <ng-container *ngIf=\"contentTemplate\">\n            <ng-container *ngTemplateOutlet=\"contentTemplate; context: context\"></ng-container>\n        </ng-container>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class GalleriaItemSlot {\n    @Input() templates: QueryList<PrimeTemplate> | undefined;\n\n    @Input({ transform: numberAttribute }) index: number | undefined;\n\n    @Input() get item(): any {\n        return this._item;\n    }\n\n    set item(item: any) {\n        this._item = item;\n        if (this.templates) {\n            this.templates.forEach((item) => {\n                if (item.getType() === this.type) {\n                    switch (this.type) {\n                        case 'item':\n                        case 'caption':\n                        case 'thumbnail':\n                            this.context = { $implicit: this.item };\n                            this.contentTemplate = item.template;\n                            break;\n                    }\n                }\n            });\n        }\n    }\n\n    @Input() type: string | undefined;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    context: any;\n\n    _item: any;\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            if (item.getType() === this.type) {\n                switch (this.type) {\n                    case 'item':\n                    case 'caption':\n                    case 'thumbnail':\n                        this.context = { $implicit: this.item };\n                        this.contentTemplate = item.template;\n                        break;\n\n                    case 'indicator':\n                        this.context = { $implicit: this.index };\n                        this.contentTemplate = item.template;\n                        break;\n\n                    default:\n                        this.context = {};\n                        this.contentTemplate = item.template;\n                        break;\n                }\n            }\n        });\n    }\n}\n\n@Component({\n    selector: 'p-galleriaItem',\n    template: `\n        <div class=\"p-galleria-item-wrapper\">\n            <div class=\"p-galleria-item-container\">\n                <button\n                    *ngIf=\"showItemNavigators\"\n                    type=\"button\"\n                    role=\"navigation\"\n                    [ngClass]=\"{ 'p-galleria-item-prev p-galleria-item-nav p-link': true, 'p-galleria-item-nav-focused': leftButtonFocused, 'p-disabled': this.isNavBackwardDisabled() }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                    (focus)=\"onButtonFocus('left')\"\n                    (blur)=\"onButtonBlur('left')\"\n                >\n                    <ChevronLeftIcon *ngIf=\"!galleria.itemPreviousIconTemplate\" [styleClass]=\"'p-galleria-item-prev-icon'\" />\n                    <ng-template *ngTemplateOutlet=\"galleria.itemPreviousIconTemplate\"></ng-template>\n                </button>\n                <div [id]=\"id + '_item_' + activeIndex\" role=\"group\" [attr.aria-label]=\"ariaSlideNumber(activeIndex + 1)\" [attr.aria-roledescription]=\"ariaSlideLabel()\" [style.width]=\"'100%'\">\n                    <p-galleriaItemSlot type=\"item\" [item]=\"activeItem\" [templates]=\"templates\" class=\"p-galleria-item\"></p-galleriaItemSlot>\n                </div>\n                <button\n                    *ngIf=\"showItemNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-item-next p-galleria-item-nav p-link': true, 'p-galleria-item-nav-focused': rightButtonFocused, 'p-disabled': this.isNavForwardDisabled() }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    role=\"navigation\"\n                    (focus)=\"onButtonFocus('right')\"\n                    (blur)=\"onButtonBlur('right')\"\n                >\n                    <ChevronRightIcon *ngIf=\"!galleria.itemNextIconTemplate\" [styleClass]=\"'p-galleria-item-next-icon'\" />\n                    <ng-template *ngTemplateOutlet=\"galleria.itemNextIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-caption\" *ngIf=\"captionFacet\">\n                    <p-galleriaItemSlot type=\"caption\" [item]=\"activeItem\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </div>\n            </div>\n            <ul *ngIf=\"showIndicators\" class=\"p-galleria-indicators p-reset\">\n                <li\n                    *ngFor=\"let item of value; let index = index\"\n                    tabindex=\"0\"\n                    (click)=\"onIndicatorClick(index)\"\n                    (mouseenter)=\"onIndicatorMouseEnter(index)\"\n                    (keydown)=\"onIndicatorKeyDown($event, index)\"\n                    [ngClass]=\"{ 'p-galleria-indicator': true, 'p-highlight': isIndicatorItemActive(index) }\"\n                    [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                    [attr.aria-selected]=\"activeIndex === index\"\n                    [attr.aria-controls]=\"id + '_item_' + index\"\n                >\n                    <button type=\"button\" tabIndex=\"-1\" class=\"p-link\" *ngIf=\"!indicatorFacet\"></button>\n                    <p-galleriaItemSlot type=\"indicator\" [index]=\"index\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </li>\n            </ul>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class GalleriaItem implements OnChanges {\n    @Input() id: string | undefined;\n\n    @Input({ transform: booleanAttribute }) circular: boolean = false;\n\n    @Input() value: any[] | undefined;\n\n    @Input({ transform: booleanAttribute }) showItemNavigators: boolean = false;\n\n    @Input({ transform: booleanAttribute }) showIndicators: boolean = true;\n\n    @Input({ transform: booleanAttribute }) slideShowActive: boolean = true;\n\n    @Input({ transform: booleanAttribute }) changeItemOnIndicatorHover: boolean = true;\n\n    @Input({ transform: booleanAttribute }) autoPlay: boolean = false;\n\n    @Input() templates: QueryList<PrimeTemplate> | undefined;\n\n    @Input() indicatorFacet: any;\n\n    @Input() captionFacet: any;\n\n    @Output() startSlideShow: EventEmitter<Event> = new EventEmitter();\n\n    @Output() stopSlideShow: EventEmitter<Event> = new EventEmitter();\n\n    @Output() onActiveIndexChange: EventEmitter<number> = new EventEmitter();\n\n    @Input() get activeIndex(): number {\n        return this._activeIndex;\n    }\n\n    set activeIndex(activeIndex) {\n        this._activeIndex = activeIndex;\n    }\n\n    get activeItem() {\n        return this.value && this.value[this._activeIndex];\n    }\n\n    _activeIndex: number = 0;\n\n    leftButtonFocused: boolean = false;\n\n    rightButtonFocused: boolean = false;\n\n    constructor(public galleria: Galleria) {}\n\n    ngOnChanges({ autoPlay }: SimpleChanges): void {\n        if (autoPlay?.currentValue) {\n            this.startSlideShow.emit();\n        }\n\n        if (autoPlay && autoPlay.currentValue === false) {\n            this.stopTheSlideShow();\n        }\n    }\n\n    next() {\n        let nextItemIndex = this.activeIndex + 1;\n        let activeIndex = this.circular && (<any[]>this.value).length - 1 === this.activeIndex ? 0 : nextItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n    }\n\n    prev() {\n        let prevItemIndex = this.activeIndex !== 0 ? this.activeIndex - 1 : 0;\n        let activeIndex = this.circular && this.activeIndex === 0 ? (<any[]>this.value).length - 1 : prevItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n    }\n\n    onButtonFocus(pos: 'left' | 'right') {\n        if (pos === 'left') {\n            this.leftButtonFocused = true;\n        } else this.rightButtonFocused = true;\n    }\n\n    onButtonBlur(pos: 'left' | 'right') {\n        if (pos === 'left') {\n            this.leftButtonFocused = false;\n        } else this.rightButtonFocused = false;\n    }\n\n    stopTheSlideShow() {\n        if (this.slideShowActive && this.stopSlideShow) {\n            this.stopSlideShow.emit();\n        }\n    }\n\n    navForward(e: MouseEvent) {\n        this.stopTheSlideShow();\n        this.next();\n\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n\n    navBackward(e: MouseEvent) {\n        this.stopTheSlideShow();\n        this.prev();\n\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n\n    onIndicatorClick(index: number) {\n        this.stopTheSlideShow();\n        this.onActiveIndexChange.emit(index);\n    }\n\n    onIndicatorMouseEnter(index: number) {\n        if (this.changeItemOnIndicatorHover) {\n            this.stopTheSlideShow();\n            this.onActiveIndexChange.emit(index);\n        }\n    }\n\n    onIndicatorKeyDown(event, index: number) {\n        switch (event.code) {\n            case 'Enter':\n            case 'Space':\n                this.stopTheSlideShow();\n                this.onActiveIndexChange.emit(index);\n                event.preventDefault();\n                break;\n\n            case 'ArrowDown':\n            case 'ArrowUp':\n                event.preventDefault();\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    isNavForwardDisabled() {\n        return !this.circular && this.activeIndex === (<any[]>this.value).length - 1;\n    }\n\n    isNavBackwardDisabled() {\n        return !this.circular && this.activeIndex === 0;\n    }\n\n    isIndicatorItemActive(index: number) {\n        return this.activeIndex === index;\n    }\n\n    ariaSlideLabel() {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slide : undefined;\n    }\n\n    ariaSlideNumber(value) {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n    }\n\n    ariaPageLabel(value) {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n}\n\n@Component({\n    selector: 'p-galleriaThumbnails',\n    template: `\n        <div class=\"p-galleria-thumbnail-wrapper\">\n            <div class=\"p-galleria-thumbnail-container\">\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-thumbnail-prev p-link': true, 'p-disabled': this.isNavBackwardDisabled() }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.previousThumbnailIconTemplate\">\n                        <ChevronLeftIcon *ngIf=\"!isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                        <ChevronUpIcon *ngIf=\"isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.previousThumbnailIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-thumbnail-items-container\" [ngStyle]=\"{ height: isVertical ? contentHeight : '' }\">\n                    <div #itemsContainer class=\"p-galleria-thumbnail-items\" (transitionend)=\"onTransitionEnd()\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\" role=\"tablist\">\n                        <div\n                            *ngFor=\"let item of value; let index = index\"\n                            [ngClass]=\"{\n                                'p-galleria-thumbnail-item': true,\n                                'p-galleria-thumbnail-item-current': activeIndex === index,\n                                'p-galleria-thumbnail-item-active': isItemActive(index),\n                                'p-galleria-thumbnail-item-start': firstItemAciveIndex() === index,\n                                'p-galleria-thumbnail-item-end': lastItemActiveIndex() === index\n                            }\"\n                            [attr.aria-selected]=\"activeIndex === index\"\n                            [attr.aria-controls]=\"containerId + '_item_' + index\"\n                            [attr.data-pc-section]=\"'thumbnailitem'\"\n                            [attr.data-p-active]=\"activeIndex === index\"\n                            (keydown)=\"onThumbnailKeydown($event, index)\"\n                        >\n                            <div\n                                class=\"p-galleria-thumbnail-item-content\"\n                                [attr.tabindex]=\"activeIndex === index ? 0 : -1\"\n                                [attr.aria-current]=\"activeIndex === index ? 'page' : undefined\"\n                                [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                                (click)=\"onItemClick(index)\"\n                                (touchend)=\"onItemClick(index)\"\n                                (keydown.enter)=\"onItemClick(index)\"\n                            >\n                                <p-galleriaItemSlot type=\"thumbnail\" [item]=\"item\" [templates]=\"templates\"></p-galleriaItemSlot>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-thumbnail-next p-link': true, 'p-disabled': this.isNavForwardDisabled() }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaNextButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.nextThumbnailIconTemplate\">\n                        <ChevronRightIcon *ngIf=\"!isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                        <ChevronDownIcon *ngIf=\"isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.nextThumbnailIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class GalleriaThumbnails implements OnInit, AfterContentChecked, AfterViewInit, OnDestroy {\n    @Input() containerId: string | undefined;\n\n    @Input() value: any[] | undefined;\n\n    @Input({ transform: booleanAttribute }) isVertical: boolean = false;\n\n    @Input({ transform: booleanAttribute }) slideShowActive: boolean = false;\n\n    @Input({ transform: booleanAttribute }) circular: boolean = false;\n\n    @Input() responsiveOptions: GalleriaResponsiveOptions[] | undefined;\n\n    @Input() contentHeight: string = '300px';\n\n    @Input() showThumbnailNavigators = true;\n\n    @Input() templates: QueryList<PrimeTemplate> | undefined;\n\n    @Output() onActiveIndexChange: EventEmitter<number> = new EventEmitter();\n\n    @Output() stopSlideShow: EventEmitter<Event> = new EventEmitter();\n\n    @ViewChild('itemsContainer') itemsContainer: ElementRef | undefined;\n\n    @Input() get numVisible(): number {\n        return this._numVisible;\n    }\n\n    set numVisible(numVisible) {\n        this._numVisible = numVisible;\n        this._oldNumVisible = this.d_numVisible;\n        this.d_numVisible = numVisible;\n    }\n\n    @Input() get activeIndex(): number {\n        return this._activeIndex;\n    }\n\n    set activeIndex(activeIndex) {\n        this._oldactiveIndex = this._activeIndex;\n        this._activeIndex = activeIndex;\n    }\n\n    index: number | undefined;\n\n    startPos: { x: number; y: number } | null = null;\n\n    thumbnailsStyle: HTMLStyleElement | null = null;\n\n    sortedResponsiveOptions: GalleriaResponsiveOptions[] | null = null;\n\n    totalShiftedItems: number = 0;\n\n    page: number = 0;\n\n    documentResizeListener: VoidListener;\n\n    _numVisible: number = 0;\n\n    d_numVisible: number = 0;\n\n    _oldNumVisible: number = 0;\n\n    _activeIndex: number = 0;\n\n    _oldactiveIndex: number = 0;\n\n    constructor(public galleria: Galleria, @Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, private cd: ChangeDetectorRef) {}\n\n    ngOnInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.createStyle();\n\n            if (this.responsiveOptions) {\n                this.bindDocumentListeners();\n            }\n        }\n    }\n\n    ngAfterContentChecked() {\n        let totalShiftedItems = this.totalShiftedItems;\n\n        if ((this._oldNumVisible !== this.d_numVisible || this._oldactiveIndex !== this._activeIndex) && this.itemsContainer) {\n            if (this._activeIndex <= this.getMedianItemIndex()) {\n                totalShiftedItems = 0;\n            } else if ((<any[]>this.value).length - this.d_numVisible + this.getMedianItemIndex() < this._activeIndex) {\n                totalShiftedItems = this.d_numVisible - (<any[]>this.value).length;\n            } else if ((<any[]>this.value).length - this.d_numVisible < this._activeIndex && this.d_numVisible % 2 === 0) {\n                totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex() + 1;\n            } else {\n                totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex();\n            }\n\n            if (totalShiftedItems !== this.totalShiftedItems) {\n                this.totalShiftedItems = totalShiftedItems;\n            }\n\n            if (this.itemsContainer && this.itemsContainer.nativeElement) {\n                this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n            }\n\n            if (this._oldactiveIndex !== this._activeIndex) {\n                DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n                this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n            }\n\n            this._oldactiveIndex = this._activeIndex;\n            this._oldNumVisible = this.d_numVisible;\n        }\n    }\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.calculatePosition();\n        }\n    }\n\n    createStyle() {\n        if (!this.thumbnailsStyle) {\n            this.thumbnailsStyle = this.document.createElement('style');\n            this.document.body.appendChild(this.thumbnailsStyle);\n        }\n\n        let innerHTML = `\n            #${this.containerId} .p-galleria-thumbnail-item {\n                flex: 1 0 ${100 / this.d_numVisible}%\n            }\n        `;\n\n        if (this.responsiveOptions) {\n            this.sortedResponsiveOptions = [...this.responsiveOptions];\n            this.sortedResponsiveOptions.sort((data1, data2) => {\n                const value1 = data1.breakpoint;\n                const value2 = data2.breakpoint;\n                let result = null;\n\n                if (value1 == null && value2 != null) result = -1;\n                else if (value1 != null && value2 == null) result = 1;\n                else if (value1 == null && value2 == null) result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, { numeric: true });\n                else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n\n                return -1 * result;\n            });\n\n            for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n                let res = this.sortedResponsiveOptions[i];\n\n                innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.containerId} .p-galleria-thumbnail-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n            }\n        }\n\n        this.thumbnailsStyle.innerHTML = innerHTML;\n        DomHandler.setAttribute(this.thumbnailsStyle, 'nonce', this.galleria.config?.csp()?.nonce);\n    }\n\n    calculatePosition() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.itemsContainer && this.sortedResponsiveOptions) {\n                let windowWidth = window.innerWidth;\n                let matchedResponsiveData = {\n                    numVisible: this._numVisible\n                };\n\n                for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n                    let res = this.sortedResponsiveOptions[i];\n\n                    if (parseInt(res.breakpoint, 10) >= windowWidth) {\n                        matchedResponsiveData = res;\n                    }\n                }\n\n                if (this.d_numVisible !== matchedResponsiveData.numVisible) {\n                    this.d_numVisible = matchedResponsiveData.numVisible;\n                    this.cd.markForCheck();\n                }\n            }\n        }\n    }\n\n    getTabIndex(index: number) {\n        return this.isItemActive(index) ? 0 : null;\n    }\n\n    navForward(e: TouchEvent | MouseEvent) {\n        this.stopTheSlideShow();\n\n        let nextItemIndex = this._activeIndex + 1;\n        if (nextItemIndex + this.totalShiftedItems > this.getMedianItemIndex() && (-1 * this.totalShiftedItems < this.getTotalPageNumber() - 1 || this.circular)) {\n            this.step(-1);\n        }\n\n        let activeIndex = this.circular && (<any[]>this.value).length - 1 === this._activeIndex ? 0 : nextItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n\n    navBackward(e: TouchEvent | MouseEvent) {\n        this.stopTheSlideShow();\n\n        let prevItemIndex = this._activeIndex !== 0 ? this._activeIndex - 1 : 0;\n        let diff = prevItemIndex + this.totalShiftedItems;\n        if (this.d_numVisible - diff - 1 > this.getMedianItemIndex() && (-1 * this.totalShiftedItems !== 0 || this.circular)) {\n            this.step(1);\n        }\n\n        let activeIndex = this.circular && this._activeIndex === 0 ? (<any[]>this.value).length - 1 : prevItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n\n    onItemClick(index: number) {\n        this.stopTheSlideShow();\n\n        let selectedItemIndex = index;\n        if (selectedItemIndex !== this._activeIndex) {\n            const diff = selectedItemIndex + this.totalShiftedItems;\n            let dir = 0;\n            if (selectedItemIndex < this._activeIndex) {\n                dir = this.d_numVisible - diff - 1 - this.getMedianItemIndex();\n                if (dir > 0 && -1 * this.totalShiftedItems !== 0) {\n                    this.step(dir);\n                }\n            } else {\n                dir = this.getMedianItemIndex() - diff;\n                if (dir < 0 && -1 * this.totalShiftedItems < this.getTotalPageNumber() - 1) {\n                    this.step(dir);\n                }\n            }\n\n            this.activeIndex = selectedItemIndex;\n            this.onActiveIndexChange.emit(this.activeIndex);\n        }\n    }\n\n    onThumbnailKeydown(event: KeyboardEvent, index: number) {\n        if (event.code === 'Enter' || event.code === 'Space') {\n            this.onItemClick(index);\n            event.preventDefault();\n        }\n\n        switch (event.code) {\n            case 'ArrowRight':\n                this.onRightKey();\n                break;\n\n            case 'ArrowLeft':\n                this.onLeftKey();\n                break;\n\n            case 'Home':\n                this.onHomeKey();\n                event.preventDefault();\n                break;\n\n            case 'End':\n                this.onEndKey();\n                event.preventDefault();\n                break;\n\n            case 'ArrowUp':\n            case 'ArrowDown':\n                event.preventDefault();\n                break;\n\n            case 'Tab':\n                this.onTabKey();\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onRightKey() {\n        const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n        const activeIndex = this.findFocusedIndicatorIndex();\n\n        this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n    }\n\n    onLeftKey() {\n        const activeIndex = this.findFocusedIndicatorIndex();\n\n        this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n    }\n\n    onHomeKey() {\n        const activeIndex = this.findFocusedIndicatorIndex();\n\n        this.changedFocusedIndicator(activeIndex, 0);\n    }\n\n    onEndKey() {\n        const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n        const activeIndex = this.findFocusedIndicatorIndex();\n\n        this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n    }\n\n    onTabKey() {\n        const indicators = [...DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n        const highlightedIndex = indicators.findIndex((ind) => DomHandler.getAttribute(ind, 'data-p-active') === true);\n\n        const activeIndicator = DomHandler.findSingle(this.itemsContainer.nativeElement, '[tabindex=\"0\"]');\n\n        const activeIndex = indicators.findIndex((ind) => ind === activeIndicator.parentElement);\n\n        indicators[activeIndex].children[0].tabIndex = '-1';\n        indicators[highlightedIndex].children[0].tabIndex = '0';\n    }\n\n    findFocusedIndicatorIndex() {\n        const indicators = [...DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n        const activeIndicator = DomHandler.findSingle(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"] > [tabindex=\"0\"]');\n\n        return indicators.findIndex((ind) => ind === activeIndicator.parentElement);\n    }\n\n    changedFocusedIndicator(prevInd, nextInd) {\n        const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n\n        indicators[prevInd].children[0].tabIndex = '-1';\n        indicators[nextInd].children[0].tabIndex = '0';\n        indicators[nextInd].children[0].focus();\n    }\n\n    step(dir: number) {\n        let totalShiftedItems = this.totalShiftedItems + dir;\n\n        if (dir < 0 && -1 * totalShiftedItems + this.d_numVisible > (<any[]>this.value).length - 1) {\n            totalShiftedItems = this.d_numVisible - (<any[]>this.value).length;\n        } else if (dir > 0 && totalShiftedItems > 0) {\n            totalShiftedItems = 0;\n        }\n\n        if (this.circular) {\n            if (dir < 0 && (<any[]>this.value).length - 1 === this._activeIndex) {\n                totalShiftedItems = 0;\n            } else if (dir > 0 && this._activeIndex === 0) {\n                totalShiftedItems = this.d_numVisible - (<any[]>this.value).length;\n            }\n        }\n\n        if (this.itemsContainer) {\n            DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n            this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n            this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n        }\n\n        this.totalShiftedItems = totalShiftedItems;\n    }\n\n    stopTheSlideShow() {\n        if (this.slideShowActive && this.stopSlideShow) {\n            this.stopSlideShow.emit();\n        }\n    }\n\n    changePageOnTouch(e: TouchEvent, diff: number) {\n        if (diff < 0) {\n            // left\n            this.navForward(e);\n        } else {\n            // right\n            this.navBackward(e);\n        }\n    }\n\n    getTotalPageNumber() {\n        return (<any[]>this.value).length > this.d_numVisible ? (<any[]>this.value).length - this.d_numVisible + 1 : 0;\n    }\n\n    getMedianItemIndex() {\n        let index = Math.floor(this.d_numVisible / 2);\n\n        return this.d_numVisible % 2 ? index : index - 1;\n    }\n\n    onTransitionEnd() {\n        if (this.itemsContainer && this.itemsContainer.nativeElement) {\n            DomHandler.addClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n            this.itemsContainer.nativeElement.style.transition = '';\n        }\n    }\n\n    onTouchEnd(e: TouchEvent) {\n        let touchobj = e.changedTouches[0];\n\n        if (this.isVertical) {\n            this.changePageOnTouch(e, touchobj.pageY - (<{ x: number; y: number }>this.startPos).y);\n        } else {\n            this.changePageOnTouch(e, touchobj.pageX - (<{ x: number; y: number }>this.startPos).x);\n        }\n    }\n\n    onTouchMove(e: TouchEvent) {\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n\n    onTouchStart(e: TouchEvent) {\n        let touchobj = e.changedTouches[0];\n\n        this.startPos = {\n            x: touchobj.pageX,\n            y: touchobj.pageY\n        };\n    }\n\n    isNavBackwardDisabled() {\n        return (!this.circular && this._activeIndex === 0) || (<any[]>this.value).length <= this.d_numVisible;\n    }\n\n    isNavForwardDisabled() {\n        return (!this.circular && this._activeIndex === (<any[]>this.value).length - 1) || (<any[]>this.value).length <= this.d_numVisible;\n    }\n\n    firstItemAciveIndex() {\n        return this.totalShiftedItems * -1;\n    }\n\n    lastItemActiveIndex() {\n        return this.firstItemAciveIndex() + this.d_numVisible - 1;\n    }\n\n    isItemActive(index: number) {\n        return this.firstItemAciveIndex() <= index && this.lastItemActiveIndex() >= index;\n    }\n\n    bindDocumentListeners() {\n        if (isPlatformBrowser(this.platformId)) {\n            const window = this.document.defaultView || 'window';\n            this.documentResizeListener = this.renderer.listen(window, 'resize', () => {\n                this.calculatePosition();\n            });\n        }\n    }\n\n    unbindDocumentListeners() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.responsiveOptions) {\n            this.unbindDocumentListeners();\n        }\n\n        if (this.thumbnailsStyle) {\n            this.thumbnailsStyle.parentNode?.removeChild(this.thumbnailsStyle);\n        }\n    }\n\n    ariaPrevButtonLabel() {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.prevPageLabel : undefined;\n    }\n\n    ariaNextButtonLabel() {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.nextPageLabel : undefined;\n    }\n\n    ariaPageLabel(value) {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, RippleModule, TimesIcon, ChevronRightIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrapModule],\n    exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule],\n    declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails]\n})\nexport class GalleriaModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA2CA;;;AAGG;MA8CU,QAAQ,CAAA;AAoMqB,IAAA,QAAA,CAAA;AAAgD,IAAA,UAAA,CAAA;AAAwB,IAAA,OAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AAnMxK;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IACD,IAAI,WAAW,CAAC,WAAW,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;KACnC;AACD;;;AAGG;IACqC,UAAU,GAAY,KAAK,CAAC;AACpE;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;AACM,IAAA,KAAK,CAAoB;AAClC;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;;AAIG;AACM,IAAA,iBAAiB,CAA0C;AACpE;;;AAGG;IACqC,kBAAkB,GAAY,KAAK,CAAC;AAC5E;;;AAGG;IACqC,uBAAuB,GAAY,IAAI,CAAC;AAChF;;;AAGG;IACqC,yBAAyB,GAAY,KAAK,CAAC;AACnF;;;AAGG;IACqC,0BAA0B,GAAY,KAAK,CAAC;AACpF;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACqC,yBAAyB,GAAY,IAAI,CAAC;AAClF;;;AAGG;IACoC,kBAAkB,GAAW,IAAI,CAAC;AACzE;;;AAGG;IACqC,cAAc,GAAY,IAAI,CAAC;AACvE;;;AAGG;IACM,kBAAkB,GAAoD,QAAQ,CAAC;AACxF;;;AAGG;IACM,+BAA+B,GAAW,OAAO,CAAC;AAC3D;;;AAGG;IACqC,cAAc,GAAY,KAAK,CAAC;AACxE;;;AAGG;IACqC,oBAAoB,GAAY,KAAK,CAAC;AAC9E;;;AAGG;IACM,kBAAkB,GAAoD,QAAQ,CAAC;AACxF;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,cAAc,CAA8C;AACrE;;;AAGG;IACM,qBAAqB,GAAW,kCAAkC,CAAC;AAC5E;;;AAGG;IACM,qBAAqB,GAAW,kCAAkC,CAAC;AAC5E;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,OAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACpC,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B,SAAA;KACJ;AACD;;;;AAIG;AACO,IAAA,iBAAiB,GAAyB,IAAI,YAAY,EAAU,CAAC;AAC/E;;;;AAIG;AACO,IAAA,aAAa,GAA0B,IAAI,YAAY,EAAW,CAAC;AAE1D,IAAA,IAAI,CAAyB;AAExB,IAAA,SAAS,CAAyB;AAE1B,IAAA,SAAS,CAAuC;IAEhF,QAAQ,GAAY,KAAK,CAAC;IAE1B,YAAY,GAAW,CAAC,CAAC;AAEzB,IAAA,WAAW,CAAM;AAEjB,IAAA,WAAW,CAAM;AAEjB,IAAA,cAAc,CAAM;AAEpB,IAAA,YAAY,CAAM;AAElB,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,6BAA6B,CAA+B;AAE5D,IAAA,yBAAyB,CAA+B;AAExD,IAAA,wBAAwB,CAA+B;AAEvD,IAAA,oBAAoB,CAA+B;IAEnD,WAAW,GAAY,KAAK,CAAC;IAE7B,eAAe,GAAG,CAAC,CAAC;IAEpB,WAAsC,CAAA,QAAkB,EAA8B,UAAe,EAAS,OAAmB,EAAS,EAAqB,EAAS,MAAqB,EAAA;QAAvJ,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA8B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAO,CAAA,OAAA,GAAP,OAAO,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAEjM,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,kBAAkB;AACnB,oBAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC9C,MAAM;AAEV,gBAAA,KAAK,uBAAuB;AACxB,oBAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnD,MAAM;AAEV,gBAAA,KAAK,mBAAmB;AACpB,oBAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC/C,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,WAAW,CAAC,aAA4B,EAAA;AACpC,QAAA,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YACnF,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;AAClE,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAC5B,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAClC;AAED,IAAA,kBAAkB,CAAC,KAAa,EAAA;AAC5B,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;AAC5B,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtC,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAqB,EAAA;QAClC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;gBACV,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,UAAU,CAAC,MAAK;AACZ,oBAAA,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,iCAAiC,CAAC,CAAC,CAAC;iBAC5G,EAAE,EAAE,CAAC,CAAC;gBACP,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,2BAA2B,CAAC,CAAC;gBAC3E,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAqB,EAAA;QAChC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM;AACb,SAAA;KACJ;IAED,cAAc,GAAA;QACV,UAAU,CAAC,eAAe,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAClG,SAAA;KACJ;IAED,eAAe,GAAA;QACX,UAAU,CAAC,iBAAiB,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC9C,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;AACnE,SAAA;QAED,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;KACJ;uGAtTQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAoMG,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AApMpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,EAeG,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAehB,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,eAAe,CAWf,EAAA,iBAAA,EAAA,mBAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,gBAAgB,CAKhB,EAAA,uBAAA,EAAA,CAAA,yBAAA,EAAA,yBAAA,EAAA,gBAAgB,CAKhB,EAAA,yBAAA,EAAA,CAAA,2BAAA,EAAA,2BAAA,EAAA,gBAAgB,CAKhB,EAAA,0BAAA,EAAA,CAAA,4BAAA,EAAA,4BAAA,EAAA,gBAAgB,sCAKhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAAA,EAAA,yBAAA,EAAA,CAAA,2BAAA,EAAA,2BAAA,EAKhB,gBAAgB,CAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAKhB,eAAe,CAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAKf,gBAAgB,CAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,+BAAA,EAAA,iCAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAehB,gBAAgB,CAAA,EAAA,oBAAA,EAAA,CAAA,sBAAA,EAAA,sBAAA,EAKhB,gBAAgB,CAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAUhB,eAAe,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAyDlB,aAAa,EAnNpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,0+IAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA2YQ,eAAe,CA1YZ,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,EAAA,kBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,WAAW,EAAE;gBACjB,UAAU,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBACpH,UAAU,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACvH,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,QAAQ,EAAA,UAAA,EAAA,CAAA;kBA7CpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BT,EACW,UAAA,EAAA;wBACR,OAAO,CAAC,WAAW,EAAE;4BACjB,UAAU,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;4BACpH,UAAU,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;yBACvH,CAAC;AACL,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,0+IAAA,CAAA,EAAA,CAAA;;0BAsMY,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;8HA/LhE,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAUkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKiC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAM5B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKkC,kBAAkB,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,uBAAuB,EAAA,CAAA;sBAA9D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,yBAAyB,EAAA,CAAA;sBAAhE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,0BAA0B,EAAA,CAAA;sBAAjE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,yBAAyB,EAAA,CAAA;sBAAhE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,kBAAkB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKG,+BAA+B,EAAA,CAAA;sBAAvC,KAAK;gBAKkC,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,oBAAoB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKiC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAeI,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAMG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAEY,IAAI,EAAA,CAAA;sBAAtB,SAAS;uBAAC,MAAM,CAAA;gBAEO,SAAS,EAAA,CAAA;sBAAhC,SAAS;uBAAC,WAAW,CAAA;gBAEU,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAqNrB,eAAe,CAAA;AAgCL,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAA+B,IAAA,OAAA,CAAA;AAAiC,IAAA,MAAA,CAAA;AAA+B,IAAA,UAAA,CAAA;AA/B7I,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IACD,IAAI,WAAW,CAAC,WAAmB,EAAA;AAC/B,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;KACnC;IAEQ,KAAK,GAAU,EAAE,CAAC;AAEY,IAAA,UAAU,CAAqB;AAE9B,IAAA,UAAU,CAAU;AAElD,IAAA,QAAQ,GAA0B,IAAI,YAAY,EAAE,CAAC;AAErD,IAAA,gBAAgB,GAAyB,IAAI,YAAY,EAAE,CAAC;AAE5C,IAAA,WAAW,CAAyB;AAE9D,IAAA,EAAE,CAAS;IAEX,YAAY,GAAW,CAAC,CAAC;IAEzB,eAAe,GAAY,IAAI,CAAC;AAEhC,IAAA,QAAQ,CAAM;AAEd,IAAA,UAAU,CAAqB;AAEvB,IAAA,MAAM,CAAM;IAEpB,WAAmB,CAAA,QAAkB,EAAS,EAAqB,EAAU,OAAwB,EAAS,MAAqB,EAAU,UAAsB,EAAA;QAAhJ,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAU,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;QAC/J,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;AAClD,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;KAC3D;;AAID,IAAA,sBAAsB,CAAC,KAAY,EAAA;AAC/B,QAAA,IAAI,QAAQ,EAAE,iBAAiB,KAAK,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE;AAC5E,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC3B,SAAA;KACJ;IAED,SAAS,GAAA;QACL,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC7C,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAA8C,CAAC,CAAC;YACtF,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;;;;;AAK3C,gBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,aAAA;AACJ,SAAA;KACJ;IAED,aAAa,GAAA;QACT,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAA4B,CAAC,CAAC;QACtJ,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAA4B,CAAC,CAAC;QAErJ,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,GAAG,GAAG,EAAE,KAAK,kBAAkB,GAAG,kBAAkB,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,iBAAiB,GAAG,iBAAiB,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;KAC/L;IAED,cAAc,GAAA;QACV,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC7C,YAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,MAAK;AAC7B,gBAAA,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AAClH,gBAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;AACtC,gBAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,aAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;AAErC,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC/B,SAAA;KACJ;IAED,aAAa,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,EAAE;YACpE,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;KAChC;IAED,gBAAgB,CAAC,YAAoB,EAAE,QAAgB,EAAA;QACnD,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACrD,QAAA,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,QAAQ,CAAC,CAAC;AAExD,QAAA,OAAO,GAAG,GAAG,CAAG,EAAA,YAAY,CAAI,CAAA,EAAA,GAAG,CAAE,CAAA,GAAG,EAAE,CAAC;KAC9C;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,kBAAkB,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,KAAK,OAAO,CAAC;KACtG;AAED,IAAA,mBAAmB,CAAC,KAAa,EAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;AAC5B,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAChD,SAAA;KACJ;IAED,cAAc,GAAA;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;KACxF;uGA/GQ,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EAUJ,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,eAAe,CAEf,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CA9E1B,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,2BAAA,EAAA,gCAAA,EAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAq8BmD,SAAS,CAv0BpD,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,gBAAgB,CAyHhB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,WAAA,EAAA,OAAA,EAAA,MAAA,EAAA,MAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,YAAY,6VA0OZ,kBAAkB,CAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,OAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,WAAA,EAAA,YAAA,EAAA,aAAA,CAAA,EAAA,OAAA,EAAA,CAAA,qBAAA,EAAA,eAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA,CAAA;;2FA9dlB,eAAe,EAAA,UAAA,EAAA,CAAA;kBApE3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAClD,iBAAA,CAAA;6LAEgB,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAOG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEiC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE5B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEG,gBAAgB,EAAA,CAAA;sBAAzB,MAAM;gBAEmB,WAAW,EAAA,CAAA;sBAApC,SAAS;uBAAC,aAAa,CAAA;gBAqBxB,sBAAsB,EAAA,CAAA;sBADrB,YAAY;uBAAC,2BAA2B,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAqF5C,gBAAgB,CAAA;AAChB,IAAA,SAAS,CAAuC;AAElB,IAAA,KAAK,CAAqB;AAEjE,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;IAED,IAAI,IAAI,CAAC,IAAS,EAAA;AACd,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;gBAC5B,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE;oBAC9B,QAAQ,IAAI,CAAC,IAAI;AACb,wBAAA,KAAK,MAAM,CAAC;AACZ,wBAAA,KAAK,SAAS,CAAC;AACf,wBAAA,KAAK,WAAW;4BACZ,IAAI,CAAC,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AACxC,4BAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;4BACrC,MAAM;AACb,qBAAA;AACJ,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAEQ,IAAA,IAAI,CAAqB;AAElC,IAAA,eAAe,CAA+B;AAE9C,IAAA,OAAO,CAAM;AAEb,IAAA,KAAK,CAAM;IAEX,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;YAC7B,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE;gBAC9B,QAAQ,IAAI,CAAC,IAAI;AACb,oBAAA,KAAK,MAAM,CAAC;AACZ,oBAAA,KAAK,SAAS,CAAC;AACf,oBAAA,KAAK,WAAW;wBACZ,IAAI,CAAC,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AACxC,wBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;wBACrC,MAAM;AAEV,oBAAA,KAAK,WAAW;wBACZ,IAAI,CAAC,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACzC,wBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;wBACrC,MAAM;AAEV,oBAAA;AACI,wBAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AAClB,wBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;wBACrC,MAAM;AACb,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;KACN;uGA1DQ,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAhB,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAGL,eAAe,CAVzB,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA,CAAA;;2FAGQ,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAT5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,CAAA;;;;AAIT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAClD,iBAAA,CAAA;8BAEY,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAEiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAExB,IAAI,EAAA,CAAA;sBAAhB,KAAK;gBAsBG,IAAI,EAAA,CAAA;sBAAZ,KAAK;;MA8FG,YAAY,CAAA;AA+CF,IAAA,QAAA,CAAA;AA9CV,IAAA,EAAE,CAAqB;IAEQ,QAAQ,GAAY,KAAK,CAAC;AAEzD,IAAA,KAAK,CAAoB;IAEM,kBAAkB,GAAY,KAAK,CAAC;IAEpC,cAAc,GAAY,IAAI,CAAC;IAE/B,eAAe,GAAY,IAAI,CAAC;IAEhC,0BAA0B,GAAY,IAAI,CAAC;IAE3C,QAAQ,GAAY,KAAK,CAAC;AAEzD,IAAA,SAAS,CAAuC;AAEhD,IAAA,cAAc,CAAM;AAEpB,IAAA,YAAY,CAAM;AAEjB,IAAA,cAAc,GAAwB,IAAI,YAAY,EAAE,CAAC;AAEzD,IAAA,aAAa,GAAwB,IAAI,YAAY,EAAE,CAAC;AAExD,IAAA,mBAAmB,GAAyB,IAAI,YAAY,EAAE,CAAC;AAEzE,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IAED,IAAI,WAAW,CAAC,WAAW,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;KACnC;AAED,IAAA,IAAI,UAAU,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KACtD;IAED,YAAY,GAAW,CAAC,CAAC;IAEzB,iBAAiB,GAAY,KAAK,CAAC;IAEnC,kBAAkB,GAAY,KAAK,CAAC;AAEpC,IAAA,WAAA,CAAmB,QAAkB,EAAA;QAAlB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;KAAI;IAEzC,WAAW,CAAC,EAAE,QAAQ,EAAiB,EAAA;QACnC,IAAI,QAAQ,EAAE,YAAY,EAAE;AACxB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,YAAY,KAAK,KAAK,EAAE;YAC7C,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC3B,SAAA;KACJ;IAED,IAAI,GAAA;AACA,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACzC,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,IAAY,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,aAAa,CAAC;AAC3G,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAC9C;IAED,IAAI,GAAA;AACA,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;QACtE,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,GAAW,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC;AAC3G,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAC9C;AAED,IAAA,aAAa,CAAC,GAAqB,EAAA;QAC/B,IAAI,GAAG,KAAK,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,SAAA;;AAAM,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;KACzC;AAED,IAAA,YAAY,CAAC,GAAqB,EAAA;QAC9B,IAAI,GAAG,KAAK,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;AAClC,SAAA;;AAAM,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;KAC1C;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5C,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;AAC7B,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,CAAa,EAAA;QACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,EAAE,CAAC;AAEZ,QAAA,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE;YACnB,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,CAAa,EAAA;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,EAAE,CAAC;AAEZ,QAAA,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE;YACnB,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAa,EAAA;QAC1B,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACxC;AAED,IAAA,qBAAqB,CAAC,KAAa,EAAA;QAC/B,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC,SAAA;KACJ;IAED,kBAAkB,CAAC,KAAK,EAAE,KAAa,EAAA;QACnC,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;gBACR,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,SAAS;gBACV,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,KAAa,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,CAAC,CAAC;KAChF;IAED,qBAAqB,GAAA;QACjB,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC;KACnD;AAED,IAAA,qBAAqB,CAAC,KAAa,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC;KACrC;IAED,cAAc,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;KAC1G;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;AACjB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC;KACjJ;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC;KACxI;uGAhKQ,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,QAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAGD,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAIhB,EAAA,KAAA,EAAA,OAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,gBAAgB,CAEhB,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAgB,CAEhB,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,CAEhB,EAAA,0BAAA,EAAA,CAAA,4BAAA,EAAA,4BAAA,EAAA,gBAAgB,CAEhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAzE1B,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAitB8D,gBAAgB,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAv0BvF,gBAAgB,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,WAAA,EAAA,OAAA,EAAA,MAAA,EAAA,MAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA,CAAA;;2FAyHhB,YAAY,EAAA,UAAA,EAAA,CAAA;kBA5DxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAClD,iBAAA,CAAA;0EAEY,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAEkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEkC,kBAAkB,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,0BAA0B,EAAA,CAAA;sBAAjE,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEI,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAEG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAEG,mBAAmB,EAAA,CAAA;sBAA5B,MAAM;gBAEM,WAAW,EAAA,CAAA;sBAAvB,KAAK;;MA6MG,kBAAkB,CAAA;AAoER,IAAA,QAAA,CAAA;AAA8C,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA6B,IAAA,EAAA,CAAA;AAnE/J,IAAA,WAAW,CAAqB;AAEhC,IAAA,KAAK,CAAoB;IAEM,UAAU,GAAY,KAAK,CAAC;IAE5B,eAAe,GAAY,KAAK,CAAC;IAEjC,QAAQ,GAAY,KAAK,CAAC;AAEzD,IAAA,iBAAiB,CAA0C;IAE3D,aAAa,GAAW,OAAO,CAAC;IAEhC,uBAAuB,GAAG,IAAI,CAAC;AAE/B,IAAA,SAAS,CAAuC;AAE/C,IAAA,mBAAmB,GAAyB,IAAI,YAAY,EAAE,CAAC;AAE/D,IAAA,aAAa,GAAwB,IAAI,YAAY,EAAE,CAAC;AAErC,IAAA,cAAc,CAAyB;AAEpE,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IAED,IAAI,UAAU,CAAC,UAAU,EAAA;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AAC9B,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC;AACxC,QAAA,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;KAClC;AAED,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IAED,IAAI,WAAW,CAAC,WAAW,EAAA;AACvB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;AACzC,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;KACnC;AAED,IAAA,KAAK,CAAqB;IAE1B,QAAQ,GAAoC,IAAI,CAAC;IAEjD,eAAe,GAA4B,IAAI,CAAC;IAEhD,uBAAuB,GAAuC,IAAI,CAAC;IAEnE,iBAAiB,GAAW,CAAC,CAAC;IAE9B,IAAI,GAAW,CAAC,CAAC;AAEjB,IAAA,sBAAsB,CAAe;IAErC,WAAW,GAAW,CAAC,CAAC;IAExB,YAAY,GAAW,CAAC,CAAC;IAEzB,cAAc,GAAW,CAAC,CAAC;IAE3B,YAAY,GAAW,CAAC,CAAC;IAEzB,eAAe,GAAW,CAAC,CAAC;IAE5B,WAAmB,CAAA,QAAkB,EAA4B,QAAkB,EAA+B,UAAe,EAAU,QAAmB,EAAU,EAAqB,EAAA;QAA1K,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA4B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;IAEjM,QAAQ,GAAA;AACJ,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,WAAW,EAAE,CAAC;YAEnB,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAChC,aAAA;AACJ,SAAA;KACJ;IAED,qBAAqB,GAAA;AACjB,QAAA,IAAI,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,cAAc,EAAE;YAClH,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;gBAChD,iBAAiB,GAAG,CAAC,CAAC;AACzB,aAAA;AAAM,iBAAA,IAAY,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE;gBACvG,iBAAiB,GAAG,IAAI,CAAC,YAAY,GAAW,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC;AACtE,aAAA;iBAAM,IAAY,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1G,gBAAA,iBAAiB,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;AAC9E,aAAA;AAAM,iBAAA;AACH,gBAAA,iBAAiB,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1E,aAAA;AAED,YAAA,IAAI,iBAAiB,KAAK,IAAI,CAAC,iBAAiB,EAAE;AAC9C,gBAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC9C,aAAA;YAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;AAC1D,gBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAkB,eAAA,EAAA,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAe,YAAA,EAAA,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;AACzN,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,YAAY,EAAE;gBAC5C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;gBAC5E,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,yBAAyB,CAAC;AAClF,aAAA;AAED,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;AACzC,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC;AAC3C,SAAA;KACJ;IAED,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5B,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC5D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACxD,SAAA;AAED,QAAA,IAAI,SAAS,GAAG,CAAA;AACT,aAAA,EAAA,IAAI,CAAC,WAAW,CAAA;4BACH,GAAG,GAAG,IAAI,CAAC,YAAY,CAAA;;SAE1C,CAAC;QAEF,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,uBAAuB,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC3D,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AAC/C,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;AAChC,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;gBAChC,IAAI,MAAM,GAAG,IAAI,CAAC;AAElB,gBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,qBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC;AACjD,qBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC;qBACjD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ;AAAE,oBAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;oBAClI,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAE7D,gBAAA,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC;AACvB,aAAC,CAAC,CAAC;AAEH,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1D,IAAI,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;AAE1C,gBAAA,SAAS,IAAI,CAAA;AACuB,kDAAA,EAAA,GAAG,CAAC,UAAU,CAAA;AACvC,yBAAA,EAAA,IAAI,CAAC,WAAW,CAAA;wCACH,GAAG,GAAG,GAAG,CAAC,UAAU,CAAA;;;iBAG3C,CAAC;AACL,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3C,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;KAC9F;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,uBAAuB,EAAE;AACrD,gBAAA,IAAI,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AACpC,gBAAA,IAAI,qBAAqB,GAAG;oBACxB,UAAU,EAAE,IAAI,CAAC,WAAW;iBAC/B,CAAC;AAEF,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC1D,IAAI,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;oBAE1C,IAAI,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,WAAW,EAAE;wBAC7C,qBAAqB,GAAG,GAAG,CAAC;AAC/B,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,IAAI,CAAC,YAAY,KAAK,qBAAqB,CAAC,UAAU,EAAE;AACxD,oBAAA,IAAI,CAAC,YAAY,GAAG,qBAAqB,CAAC,UAAU,CAAC;AACrD,oBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAa,EAAA;AACrB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;KAC9C;AAED,IAAA,UAAU,CAAC,CAA0B,EAAA;QACjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAExB,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;AAC1C,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;AACtJ,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,SAAA;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,IAAY,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,aAAa,CAAC;AAC5G,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,CAAC,CAAC,UAAU,EAAE;YACd,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,CAA0B,EAAA;QAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAExB,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AACxE,QAAA,IAAI,IAAI,GAAG,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAClD,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;AAClH,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChB,SAAA;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,GAAW,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC;AAC5G,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,CAAC,CAAC,UAAU,EAAE;YACd,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAa,EAAA;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,iBAAiB,GAAG,KAAK,CAAC;AAC9B,QAAA,IAAI,iBAAiB,KAAK,IAAI,CAAC,YAAY,EAAE;AACzC,YAAA,MAAM,IAAI,GAAG,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;YACxD,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,YAAA,IAAI,iBAAiB,GAAG,IAAI,CAAC,YAAY,EAAE;AACvC,gBAAA,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC/D,gBAAA,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;AAC9C,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC;AACvC,gBAAA,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,EAAE;AACxE,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,WAAW,GAAG,iBAAiB,CAAC;YACrC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACnD,SAAA;KACJ;IAED,kBAAkB,CAAC,KAAoB,EAAE,KAAa,EAAA;QAClD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;AAClD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACxB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;QAED,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,YAAY;gBACb,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM;AAEV,YAAA,KAAK,WAAW;gBACZ,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,KAAK;gBACN,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,SAAS,CAAC;AACf,YAAA,KAAK,WAAW;gBACZ,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,KAAK;gBACN,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,mCAAmC,CAAC,CAAC;AAC3G,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAErD,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC,KAAK,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;KAC9H;IAED,SAAS,GAAA;AACL,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAErD,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;KACzF;IAED,SAAS,GAAA;AACL,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAErD,QAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KAChD;IAED,QAAQ,GAAA;AACJ,QAAA,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,mCAAmC,CAAC,CAAC;AAC3G,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAErD,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACpE;IAED,QAAQ,GAAA;AACJ,QAAA,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,mCAAmC,CAAC,CAAC,CAAC;QAChH,MAAM,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,UAAU,CAAC,YAAY,CAAC,GAAG,EAAE,eAAe,CAAC,KAAK,IAAI,CAAC,CAAC;AAE/G,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AAEnG,QAAA,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,eAAe,CAAC,aAAa,CAAC,CAAC;AAEzF,QAAA,UAAU,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AACpD,QAAA,UAAU,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;KAC3D;IAED,yBAAyB,GAAA;AACrB,QAAA,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,mCAAmC,CAAC,CAAC,CAAC;AAChH,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,oDAAoD,CAAC,CAAC;AAEvI,QAAA,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,eAAe,CAAC,aAAa,CAAC,CAAC;KAC/E;IAED,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAA;AACpC,QAAA,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,mCAAmC,CAAC,CAAC;AAE3G,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AAChD,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;QAC/C,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;KAC3C;AAED,IAAA,IAAI,CAAC,GAAW,EAAA;AACZ,QAAA,IAAI,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC;QAErD,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,iBAAiB,GAAG,IAAI,CAAC,YAAY,GAAW,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACxF,iBAAiB,GAAG,IAAI,CAAC,YAAY,GAAW,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC;AACtE,SAAA;AAAM,aAAA,IAAI,GAAG,GAAG,CAAC,IAAI,iBAAiB,GAAG,CAAC,EAAE;YACzC,iBAAiB,GAAG,CAAC,CAAC;AACzB,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,GAAG,GAAG,CAAC,IAAY,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC,YAAY,EAAE;gBACjE,iBAAiB,GAAG,CAAC,CAAC;AACzB,aAAA;iBAAM,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;gBAC3C,iBAAiB,GAAG,IAAI,CAAC,YAAY,GAAW,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC;AACtE,aAAA;AACJ,SAAA;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AAC5E,YAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAkB,eAAA,EAAA,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAe,YAAA,EAAA,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YACtN,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,yBAAyB,CAAC;AAClF,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5C,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;AAC7B,SAAA;KACJ;IAED,iBAAiB,CAAC,CAAa,EAAE,IAAY,EAAA;QACzC,IAAI,IAAI,GAAG,CAAC,EAAE;;AAEV,YAAA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACtB,SAAA;AAAM,aAAA;;AAEH,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACvB,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,OAAe,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAW,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;KAClH;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AAE9C,QAAA,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;KACpD;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;YAC1D,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;YACzE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;AAC3D,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,CAAa,EAAA;QACpB,IAAI,QAAQ,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAEnC,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,GAA8B,IAAI,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;AAC3F,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,GAA8B,IAAI,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;AAC3F,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,CAAa,EAAA;QACrB,IAAI,CAAC,CAAC,UAAU,EAAE;YACd,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,CAAa,EAAA;QACtB,IAAI,QAAQ,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAEnC,IAAI,CAAC,QAAQ,GAAG;YACZ,CAAC,EAAE,QAAQ,CAAC,KAAK;YACjB,CAAC,EAAE,QAAQ,CAAC,KAAK;SACpB,CAAC;KACL;IAED,qBAAqB,GAAA;QACjB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,KAAa,IAAI,CAAC,KAAM,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC;KACzG;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,KAAa,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,CAAC,KAAa,IAAI,CAAC,KAAM,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC;KACtI;IAED,mBAAmB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;KACtC;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;KAC7D;AAED,IAAA,YAAY,CAAC,KAAa,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,mBAAmB,EAAE,IAAI,KAAK,IAAI,IAAI,CAAC,mBAAmB,EAAE,IAAI,KAAK,CAAC;KACrF;IAED,qBAAqB,GAAA;AACjB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC;AACrD,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAK;gBACtE,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,uBAAuB,GAAA;QACnB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAClC,SAAA;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACtE,SAAA;KACJ;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;KAClH;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;KAClH;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC;KACxI;uGAheQ,kBAAkB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAoEoB,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AApE/F,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,mIAKP,gBAAgB,CAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAEhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAEhB,gBAAgB,CA9E1B,EAAA,iBAAA,EAAA,mBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkET,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAue8D,gBAAgB,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAv0BvF,gBAAgB,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,WAAA,EAAA,OAAA,EAAA,MAAA,EAAA,MAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA,CAAA;;2FAmWhB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAvE9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkET,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAClD,iBAAA,CAAA;;0BAqE2C,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;iGAnE/F,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAEG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAEG,uBAAuB,EAAA,CAAA;sBAA/B,KAAK;gBAEG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAEI,mBAAmB,EAAA,CAAA;sBAA5B,MAAM;gBAEG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAEsB,cAAc,EAAA,CAAA;sBAA1C,SAAS;uBAAC,gBAAgB,CAAA;gBAEd,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAUO,WAAW,EAAA,CAAA;sBAAvB,KAAK;;MAqcG,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,iBAn0Cd,QAAQ,EA6XR,eAAe,EA2Hf,gBAAgB,EAyHhB,YAAY,EA0OZ,kBAAkB,CAAA,EAAA,OAAA,EAAA,CAoejB,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,gBAAgB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,eAAe,aAC/I,YAAY,EAh0Cb,QAAQ,EA6XR,eAAe,EA2Hf,gBAAgB,EAyHhB,YAAY,EA0OZ,kBAAkB,EAqe4E,YAAY,CAAA,EAAA,CAAA,CAAA;wGAG1G,cAAc,EAAA,OAAA,EAAA,CAJb,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,gBAAgB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,eAAe,EAC/I,YAAY,EAAiF,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG1G,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,gBAAgB,EAAE,eAAe,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,eAAe,CAAC;AAC1J,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,kBAAkB,EAAE,YAAY,CAAC;oBACpH,YAAY,EAAE,CAAC,QAAQ,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,kBAAkB,CAAC;AAChG,iBAAA,CAAA;;;AC95CD;;AAEG;;;;"}