<section class="bg-gray-50 dark:bg-gray-900 p-3">
  <div *ngIf="isLoading">
    <app-table-skeleton></app-table-skeleton>
  </div>

  <div appFlowbiteInit *ngIf="!isLoading" class="overflow-x-auto mt-8">
    <section class="bg-gray-50 dark:bg-gray-900 rounded-lg border-2 mx-auto max-w-screen-2xl">
      <div class="">
        <!-- Start coding here -->
        <div class="bg-white dark:bg-gray-800 relative shadow-md overflow-x-hidden sm:rounded-lg">
          <div class="flex items-center m-4 mb-0">

            <i class="pi pi-money-bill me-2" style="font-size: 1.3rem"></i>

            <label for="">
              <span class="font-bold">{{ 'debts.debts' | transloco }} :</span> {{tableResponse.total}}
              {{'table.foundRecord' |
              transloco}}</label>
          </div>
          <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
            <div class="w-full md:w-1/2">
              <div class="flex items-center">
                <label for="simple-search" class="sr-only">{{'table.search' | transloco}}</label>
                <div class="relative w-full">
                  <div class="absolute inset-y-0 left-0 flex items-center p-3 pointer-events-none">
                    <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor"
                      viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd"
                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                        clip-rule="evenodd" />
                    </svg>
                  </div>
                  <input type="text" id="simple-search" (change)="searchChange($event)"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full ps-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                    placeholder="{{'table.search' | transloco}}..." required="" [value]="requestForm.search">
                </div>
              </div>
            </div>
            <div
              class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
              <div class="flex items-center space-x-3 w-full md:w-auto">
                <button id="coulmnsDropdownButton" data-dropdown-toggle="coulmnsDropdown"
                  class="w-full md:w-auto flex items-center justify-center me-2 py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  type="button">
                  <svg class="h-4 w-4 me-2 text-gray-800 dark:text-white" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 5v14M9 5v14M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z" />
                  </svg>

                  {{'table.columns' | transloco}}
                  <svg class="-me-1 ms-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path clip-rule="evenodd" fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>
                <div id="coulmnsDropdown" class="z-10 hidden w-48 p-3 bg-white rounded-lg shadow dark:bg-gray-700">
                  <h6 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">{{'table.showColumns' | transloco}}</h6>
                  <ul class="space-y-2 text-sm" aria-labelledby="filterDropdownButton">
                    <li *ngFor="let column of debtColumnsState" class="flex items-center">
                      <input title="{{column.key}}" type="checkbox" [id]="column.key" [checked]="!column.hidden"
                        (change)="toggleColumnSelection(column.key)"
                        class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                      <label [for]="column.key" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                        {{ ('debts.' + column.key) | transloco | titlecase }}
                      </label>
                    </li>
                  </ul>
                </div>
                <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown"
                  class="w-full md:w-auto flex items-center justify-center me-2 py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  type="button">
                  <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 me-2 text-gray-400"
                    viewbox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                      clip-rule="evenodd" />
                  </svg>
                  {{'table.filter' | transloco}}
                  <svg class="-me-1 ms-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <path clip-rule="evenodd" fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </button>
                <div id="filterDropdown" class="z-10 hidden w-60 p-3 bg-white rounded-lg shadow dark:bg-gray-700">
                  <h6 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">{{'table.advancedFilter' | transloco}}</h6>
                  <div class="max-w-sm mx-auto">
                    <div>
                      <label for="payFilter" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                        {{ 'debts.pay' | transloco }}
                      </label>
                      <select id="payFilter" (change)="onPayChange($event)" class="block w-full p-2 mb-6 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="-1" [selected]="requestForm.pay == null">{{ 'table.undefined' | transloco }}</option>
                        <option  value="1" [selected]="requestForm.pay == true">{{ 'debts.paid' | transloco }}</option>
                        <option value="0" [selected]="requestForm.pay == false">{{ 'debts.unpaid' | transloco }}</option>
                      </select>
                    </div>
                    <div class="my-2"></div>
                    <button type="submit" (click)="loadDebts()" class="w-full px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                      {{ 'table.search' | transloco }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto relative">
            <!-- data table -->
            <table class="w-full text-sm text-start text-gray-500 dark:text-gray-400">
              <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                  <th scope="col" class="px-2 py-3">#</th>
                  <ng-container *ngFor="let column of debtColumnsState">
                    <th scope="col" class="px-4 py-3" *ngIf="!column.hidden">
                      <!-- provide sorting column -->
                      @if (column.key === 'amount' || column.key === 'debt_timestamp' || column.key === 'description') {
                        <div class="cursor-pointer flex" (click)="sortByColumn(column.key)">
                          {{ ('debts.' + column.key) | transloco | titlecase }}
                          <svg title="sort" _ngcontent-ng-c3067077598="" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor" viewBox="0 0 24 24" class="w-3 h-3 ms-1.5">
                            <path _ngcontent-ng-c3067077598=""
                              d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z">
                            </path>
                          </svg>
                        </div>
                      }@else {
                        {{ ('debts.' + column.key) | transloco | titlecase }}
                      }
                    </th>
                  </ng-container>
                  <th scope="col" class="px-4 py-3">
                    <span scope="col"> {{'table.actions' | transloco}} </span>
                  </th>

                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let debt of tableResponse.data; let i = index" class="border-b dark:border-gray-700">
                  <td class="px-4 py-3">{{ tableResponse.from + i }}</td>
                  <ng-container *ngFor="let column of debtColumnsState">
                    <td class="px-4 py-3" *ngIf="!column.hidden">
                      <ng-container>
                        @if (column.key === 'pay') {
                          <span *ngIf="debt.pay == 1" class="text-xs font-medium me-2 px-2.5 py-0.5 rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 ">{{'debts.paid' |transloco}}</span>
                          <span *ngIf="debt.pay == 0" class="text-xs font-medium me-2 px-2.5 py-0.5 rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 ">{{'debts.unpaid' |transloco}}</span>
                        }@else if (column.key === 'debt_timestamp' || column.key === 'created_at' || column.key === 'paid_at') {
                          {{ getPropertyValue(debt, column.key) | date:"medium" }}
                        }@else if(column.key === 'username'){
                          <a [routerLink]="['/users/', debt.user_id, 'debts']" class="text-primary-600 dark:text-primary-400">{{ getPropertyValue(debt, column.key) }}</a>
                        }@else if(column.key === 'amount_paid' && debt.amount_paid > 0){
                          <a [routerLink]="['/debts/history', debt.id]" title="{{ 'debts.history_of_payments' | transloco }}" class="text-primary-600 dark:text-primary-400">{{ getPropertyValue(debt, column.key) }}</a>
                        }@else {
                          {{ getPropertyValue(debt, column.key) }}
                        }
                      </ng-container>
                    </td>
                  </ng-container>
                  <!-- operations -->
                  <td class="px-4 py-3">
                    <div class="flex justify-between">
                      <!-- Edit -->
                      <div>
                        <!-- Modal button -->
                        <div class="px-1">
                          <button (click)="selectDebt(debt)" data-modal-target="edit-modal" data-modal-toggle="edit-modal" data-tooltip-target="tooltip-edit" type="button" title="{{'common.edit' | transloco}}">
                            <svg class="w-6 h-6 text-blue-500 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"/>
                            </svg>
                          </button>
                          <div id="tooltip-edit" role="tooltip"
                          class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip">
                          {{'common.edit' | transloco}}
                          <div class="tooltip-arrow" data-popper-arrow></div>
                        </div>
                        </div>
                        <!-- Main modal -->
                        <div id="edit-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                        <div class="relative p-4 w-full max-w-md max-h-full">
                            <!-- Modal content -->
                            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                                <!-- Modal header -->
                                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                      {{'common.edit'| transloco}} {{'debts.debt' | transloco}}
                                    </h3>
                                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="edit-modal">
                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                        </svg>
                                        <span class="sr-only">Close</span>
                                    </button>
                                </div>
                                <!-- Modal body -->
                                <div [formGroup]="editForm" class="p-4 md:p-5 space-y-4">
                                  <div>
                                    <label for="amount" class="block text-gray-600">{{'debts.amount' | transloco}}</label>
                                    <input id="amount" formControlName="amount" type="number" class="w-full border border-gray-300 p-2 rounded-lg">
                                    <p class="error-message text-red-500 mt-2" *ngIf="amount && amount.invalid && amount.touched">
                                      <span *ngIf="amount.errors.required">{{ 'validationErrors.required' | transloco }}</span>
                                      <span *ngIf="amount.errors.min">{{ 'validationErrors.min' | transloco: { value: debt.amount_paid } }}</span>
                                    </p>
                                  </div>
                                  <div>
                                    <label for="date" class="block text-gray-600">{{'debts.debt_timestamp' | transloco}}</label> <!-- [value]="(selectedDebt?.debt_timestamp | date:'yyyy-MM-dd HH:mm:ss')" -->
                                    <input id="date" formControlName="debt_timestamp"  type="datetime-local" class="w-full border border-gray-300 p-2 rounded-lg">
                                    <p class="error-message text-red-500 mt-2" *ngIf="debt_timestamp && debt_timestamp.invalid && debt_timestamp.touched">
                                      <span *ngIf="debt_timestamp.errors.required">{{ 'validationErrors.required' | transloco }}</span>
                                    </p>
                                  </div>
                                  <div>
                                    <label for="description" class="block text-gray-600">{{'debts.description' | transloco}}</label>
                                    <textarea  id="description" formControlName="description" rows="3"
                                    class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                    placeholder="..."></textarea>
                                  </div>
                                </div>
                                <!-- Modal footer -->
                                <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                                    <button (click)="editDept()" data-modal-hide="edit-modal" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">{{'common.save' | transloco}}</button>
                                    <button data-modal-hide="edit-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"> {{'common.cancel' | transloco}} </button>
                                </div>

                            </div>
                        </div>
                        </div>
                      </div>
                      <!-- Pay -->
                      <div>
                      <button (click)="selectDebt(debt)" data-modal-target="popup-modal" data-modal-toggle="popup-modal" data-tooltip-target="tooltip-pay" type="button" title="{{'debts.pay' | transloco}}" [disabled]="debt.pay">
                        <svg class="w-6 h-6 text-lime-700" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                          height="24" fill="currentColor" viewBox="0 0 24 24">
                          <path fill-rule="evenodd"
                            d="M7 6a2 2 0 0 1 2-2h11a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2h-2v-4a3 3 0 0 0-3-3H7V6Z"
                            clip-rule="evenodd" />
                          <path fill-rule="evenodd"
                            d="M2 11a2 2 0 0 1 2-2h11a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-7Zm7.5 1a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z"
                            clip-rule="evenodd" />
                          <path d="M10.5 14.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" />
                        </svg>
                      </button>
                      <div id="tooltip-pay" role="tooltip"
                      class="inline-block absolute invisible z-10 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip">
                      {{'debts.pay' | transloco}}
                      <div class="tooltip-arrow" data-popper-arrow></div>
                      </div>
                        <!-- pay debt pop-up Modal-->
                        <div id="popup-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative p-4 w-full max-w-lg max-h-full">
                                <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                                    <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="popup-modal">
                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                        </svg>
                                        <span class="sr-only">Close modal</span>
                                    </button>
                                    <div [formGroup]="payForm" class="p-4 md:p-5">
                                        <!-- amount and payAll fields -->
                                        <div *ngIf="selectedDebt" class="my-3 flex justify-around">
                                          <div for="">
                                            {{'debts.totalAmount' | transloco}}
                                            <span class="font-bold">: {{selectedDebt.amount}} </span>
                                          </div>
                                          <div for="">
                                            {{'debts.amount_paid' | transloco}}
                                            <span class="font-bold">: {{selectedDebt.amount_paid}} </span>
                                          </div>
                                        </div>
                                        <div class="my-3">
                                          <label for="amountToPay" class="block text-gray-600">{{'debts.amount' | transloco}}</label>
                                          <input id="amountToPay" [formControl]="amountToPay" type="number" placeholder="0.00" class="w-full border border-gray-300 p-2 rounded-lg">
                                          <p class="error-message text-red-500 mt-2" *ngIf="amountToPay && amountToPay.invalid && amountToPay.touched">
                                            <span *ngIf="amountToPay.errors.required">{{ 'validationErrors.required' | transloco }}</span>
                                            <span *ngIf="amountToPay.errors.min">{{ 'validationErrors.min' | transloco: { value: debt.amount_paid } }}</span>
                                            <span *ngIf="amountToPay.errors.max">{{ 'validationErrors.max' | transloco: { value: getCalculatedMaxPaidValue() } }}</span>
                                          </p>
                                        </div>
                                        <div class="my-3">
                                          <label for="payAll" class="inline-flex my-2 items-center cursor-pointer">
                                            <input (change)="togglePayAll()" formControlName="payAll" id="payAll" type="checkbox" class="sr-only peer">
                                            <div
                                              class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
                                            </div>
                                            <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">{{'debts.payAll' | transloco}}</span>
                                          </label>
                                          </div>
                                        <!-- <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">{{'debts.confirmPayment' | transloco}}</h3> -->
                                        <button (click)="payPartialDept()" data-modal-hide="popup-modal" type="button" class="text-white bg-blue-500 hover:bg-blue-900 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                                            {{'common.confirm' | transloco}}
                                        </button>
                                        <button data-modal-hide="popup-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">{{'common.cancel' | transloco}}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>

                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <nav class="flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0 p-4"
            aria-label="Table navigation">
            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
              {{'table.showing' | transloco}}
              <span class="font-semibold text-gray-900 dark:text-white">{{(tableResponse.from != null? tableResponse.from
                :'0') + '-' + (tableResponse.to != null? tableResponse.to : '0')}}</span>
              {{'table.of' | transloco}}
              <span class="font-semibold text-gray-900 dark:text-white">{{tableResponse.total}}</span>
            </span>
            <!-- Pagination controls -->
            <ul dir="ltr" class="inline-flex items-stretch -space-x-px">
              <li>
                <button (click)="changePage((tableResponse.current_page - 1).toString())"
                  [disabled]="tableResponse.current_page === 1"
                  class="flex items-center justify-center h-full py-1.5 px-3 ms-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                  <span class="sr-only">Previous</span>
                  <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </li>
              <ng-container *ngFor="let page of getPagesToDisplay()">
                <li *ngIf="page === '...'">
                  <span
                    class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400">...</span>
                </li>
                <li *ngIf="page !== '...'">
                  <button (click)="changePage(page)" [class.bg-primary-50]="tableResponse.current_page === page"
                    [class.text-primary-600]="tableResponse.current_page === page"
                    [class.z-10]="tableResponse.current_page === page"
                    class="flex items-center justify-center text-sm py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                    {{ page }}
                  </button>
                </li>
              </ng-container>
              <li>
                <button (click)="changePage((tableResponse.current_page + 1).toString())"
                  [disabled]="tableResponse.current_page === tableResponse.last_page"
                  class="flex items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                  <span class="sr-only">Next</span>
                  <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 00-1.414 1.414L10.586 10l-3.293 3.293a1 1 0 000 1.414z"
                      clip-rule="evenodd" />
                  </svg>

                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </section>
  </div>
</section>
