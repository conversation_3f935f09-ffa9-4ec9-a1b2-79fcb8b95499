{"version": 3, "file": "primeng-icons-minus.mjs", "sources": ["../../src/app/components/icons/minus/minus.ts", "../../src/app/components/icons/minus/primeng-icons-minus.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/baseicon';\n\n@Component({\n    selector: 'MinusIcon',\n    standalone: true,\n    imports: [CommonModule, BaseIcon],\n    template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                d=\"M13.2222 7.77778H0.777778C0.571498 7.77778 0.373667 7.69584 0.227806 7.54998C0.0819442 7.40412 0 7.20629 0 7.00001C0 6.79373 0.0819442 6.5959 0.227806 6.45003C0.373667 6.30417 0.571498 6.22223 0.777778 6.22223H13.2222C13.4285 6.22223 13.6263 6.30417 13.7722 6.45003C13.9181 6.5959 14 6.79373 14 7.00001C14 7.20629 13.9181 7.40412 13.7722 7.54998C13.6263 7.69584 13.4285 7.77778 13.2222 7.77778Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `\n})\nexport class MinusIcon extends BaseIcon {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAiBM,MAAO,SAAU,SAAQ,QAAQ,CAAA;uGAA1B,SAAS,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EATR,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;AAOT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,UAAA,EAAA,IAAA,EARS,YAAY,EAAA,CAAA,EAAA,CAAA,CAAA;;2FAUb,SAAS,EAAA,UAAA,EAAA,CAAA;kBAbrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC;AACjC,oBAAA,QAAQ,EAAE,CAAA;;;;;;;AAOT,IAAA,CAAA;AACJ,iBAAA,CAAA;;;AChBD;;AAEG;;;;"}