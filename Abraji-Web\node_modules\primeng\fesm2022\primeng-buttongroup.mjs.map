{"version": 3, "file": "primeng-buttongroup.mjs", "sources": ["../../src/app/components/buttongroup/buttongroup.ts", "../../src/app/components/buttongroup/primeng-buttongroup.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, NgModule, ViewEncapsulation } from '@angular/core';\n\n@Component({\n    selector: 'p-buttonGroup',\n    template: `\n        <span class=\"p-button-group p-component\" role=\"group\">\n            <ng-content></ng-content>\n        </span>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None\n})\nexport class ButtonGroup {}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [ButtonGroup],\n    declarations: [ButtonGroup]\n})\nexport class ButtonGroupModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;MAaa,WAAW,CAAA;uGAAX,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EARV,QAAA,EAAA,eAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAIQ,WAAW,EAAA,UAAA,EAAA,CAAA;kBAVvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,CAAA;;;;AAIT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACxC,iBAAA,CAAA;;MAQY,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAPjB,YAAA,EAAA,CAAA,WAAW,CAGV,EAAA,OAAA,EAAA,CAAA,YAAY,aAHb,WAAW,CAAA,EAAA,CAAA,CAAA;AAOX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAJhB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,WAAW,CAAC;oBACtB,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;ACnBD;;AAEG;;;;"}