{"version": 3, "file": "primeng-toolbar.mjs", "sources": ["../../src/app/components/toolbar/toolbar.ts", "../../src/app/components/toolbar/primeng-toolbar.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, ElementRef, Input, NgModule, QueryList, TemplateRef, ViewEncapsulation } from '@angular/core';\nimport { BlockableUI, PrimeTemplate, SharedModule } from 'primeng/api';\n/**\n * Toolbar is a grouping component for buttons and other content.\n * @group Components\n */\n@Component({\n    selector: 'p-toolbar',\n    template: `\n        <div [ngClass]=\"'p-toolbar p-component'\" [attr.aria-labelledby]=\"ariaLabelledBy\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"toolbar\" [attr.data-pc-name]=\"'toolbar'\">\n            <ng-content></ng-content>\n            <div class=\"p-toolbar-group-left p-toolbar-group-start\" *ngIf=\"startTemplate\" [attr.data-pc-section]=\"'start'\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <div class=\"p-toolbar-group-center\" *ngIf=\"centerTemplate\" [attr.data-pc-section]=\"'center'\">\n                <ng-container *ngTemplateOutlet=\"centerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-toolbar-group-right p-toolbar-group-end\" *ngIf=\"endTemplate\" [attr.data-pc-section]=\"'end'\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./toolbar.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Toolbar implements AfterContentInit, BlockableUI {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    startTemplate: TemplateRef<any> | undefined;\n\n    endTemplate: TemplateRef<any> | undefined;\n\n    centerTemplate: TemplateRef<any> | undefined;\n\n    constructor(private el: ElementRef) {}\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                case 'left':\n                    this.startTemplate = item.template;\n                    break;\n\n                case 'end':\n                case 'right':\n                    this.endTemplate = item.template;\n                    break;\n\n                case 'center':\n                    this.centerTemplate = item.template;\n                    break;\n            }\n        });\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule],\n    exports: [Toolbar, SharedModule],\n    declarations: [Toolbar]\n})\nexport class ToolbarModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAGA;;;AAGG;MAwBU,OAAO,CAAA;AAyBI,IAAA,EAAA,CAAA;AAxBpB;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAEZ,IAAA,SAAS,CAAuC;AAEhF,IAAA,aAAa,CAA+B;AAE5C,IAAA,WAAW,CAA+B;AAE1C,IAAA,cAAc,CAA+B;AAE7C,IAAA,WAAA,CAAoB,EAAc,EAAA;QAAd,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;IAEtC,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO,CAAC;AACb,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,KAAK,CAAC;AACX,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;uGAjDQ,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAP,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAiBC,aAAa,EAtCpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;AAaT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,6PAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,OAAO,EAAA,UAAA,EAAA,CAAA;kBAvBnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;AAaT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,6PAAA,CAAA,EAAA,CAAA;+EAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAE0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAwCrB,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAb,aAAa,EAAA,YAAA,EAAA,CAzDb,OAAO,CAqDN,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CArD3B,OAAO,EAsDG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAJZ,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EACjB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGtB,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACrC,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC;oBAChC,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;ACtFD;;AAEG;;;;"}