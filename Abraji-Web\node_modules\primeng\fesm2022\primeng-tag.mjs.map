{"version": 3, "file": "primeng-tag.mjs", "sources": ["../../src/app/components/tag/tag.ts", "../../src/app/components/tag/primeng-tag.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, Input, NgModule, QueryList, TemplateRef, ViewEncapsulation, booleanAttribute } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\n/**\n * Tag component is used to categorize content.\n * @group Components\n */\n@Component({\n    selector: 'p-tag',\n    template: `\n        <span [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"!iconTemplate\">\n                <span class=\"p-tag-icon\" [ngClass]=\"icon\" *ngIf=\"icon\"></span>\n            </ng-container>\n            <span class=\"p-tag-icon\" *ngIf=\"iconTemplate\">\n                <ng-template *ngTemplateOutlet=\"iconTemplate\"></ng-template>\n            </span>\n            <span class=\"p-tag-value\">{{ value }}</span>\n        </span>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./tag.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Tag {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() get style(): { [klass: string]: any } | null | undefined {\n        return this._style;\n    }\n    set style(value: { [klass: string]: any } | null | undefined) {\n        this._style = value;\n        this.cd.markForCheck();\n    }\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Severity type of the tag.\n     * @group Props\n     */\n    @Input() severity: 'success' | 'secondary' | 'info' | 'warning' | 'danger' | 'contrast' | undefined;\n    /**\n     * Value to display inside the tag.\n     * @group Props\n     */\n    @Input() value: string | undefined;\n    /**\n     * Icon of the tag to display next to the value.\n     * @group Props\n     * @deprecated since 15.4.2. Use 'icon' template.\n     */\n    @Input() icon: string | undefined;\n    /**\n     * Whether the corners of the tag are rounded.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) rounded: boolean | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    iconTemplate: TemplateRef<any> | undefined;\n\n    _style: { [klass: string]: any } | null | undefined;\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    constructor(private cd: ChangeDetectorRef) {}\n\n    containerClass() {\n        return {\n            'p-tag p-component': true,\n            [`p-tag-${this.severity}`]: this.severity,\n            'p-tag-rounded': this.rounded\n        };\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule],\n    exports: [Tag, SharedModule],\n    declarations: [Tag]\n})\nexport class TagModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAGA;;;AAGG;MAsBU,GAAG,CAAA;AAuDQ,IAAA,EAAA,CAAA;AAtDpB;;;AAGG;AACH,IAAA,IAAa,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;IACD,IAAI,KAAK,CAAC,KAAkD,EAAA;AACxD,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AACD;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,QAAQ,CAAmF;AACpG;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;;AAIG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACqC,IAAA,OAAO,CAAsB;AAErC,IAAA,SAAS,CAAuC;AAEhF,IAAA,YAAY,CAA+B;AAE3C,IAAA,MAAM,CAA8C;IAEpD,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,WAAA,CAAoB,EAAqB,EAAA;QAArB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;IAE7C,cAAc,GAAA;QACV,OAAO;AACH,YAAA,mBAAmB,EAAE,IAAI;YACzB,CAAC,CAAA,MAAA,EAAS,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ;YACzC,eAAe,EAAE,IAAI,CAAC,OAAO;SAChC,CAAC;KACL;uGA/DQ,GAAG,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAH,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,GAAG,EAqCQ,QAAA,EAAA,OAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAEnB,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA1DpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;AAWT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,0LAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,GAAG,EAAA,UAAA,EAAA,CAAA;kBArBf,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,OAAO,EACP,QAAA,EAAA,CAAA;;;;;;;;;;;AAWT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,0LAAA,CAAA,EAAA,CAAA;sFAOY,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAWG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAMG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEN,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAgCrB,SAAS,CAAA;uGAAT,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAT,SAAS,EAAA,YAAA,EAAA,CAvET,GAAG,CAmEF,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CAnE3B,GAAG,EAoEG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAS,EAJR,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EACrB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGlB,SAAS,EAAA,UAAA,EAAA,CAAA;kBALrB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACrC,oBAAA,OAAO,EAAE,CAAC,GAAG,EAAE,YAAY,CAAC;oBAC5B,YAAY,EAAE,CAAC,GAAG,CAAC;AACtB,iBAAA,CAAA;;;AClGD;;AAEG;;;;"}