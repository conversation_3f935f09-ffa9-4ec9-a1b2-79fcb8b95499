{"version": 3, "file": "primeng-inputotp.mjs", "sources": ["../../src/app/components/inputotp/inputotp.ts", "../../src/app/components/inputotp/primeng-inputotp.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewEncapsulation, booleanAttribute, forwardRef } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { InputOtpChangeEvent } from './inputotp.interface';\n\nexport const INPUT_OTP_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputOtp),\n    multi: true\n};\n/**\n * Input Otp is used to enter one time passwords.\n * @group Components\n */\n@Component({\n    selector: 'p-inputOtp',\n    template: `\n        <ng-container *ngFor=\"let i of getRange(length); trackBy: trackByFn\">\n            <ng-container *ngIf=\"!inputTemplate\">\n                <input\n                    type=\"text\"\n                    pInputText\n                    [value]=\"getModelValue(i)\"\n                    [maxLength]=\"1\"\n                    [type]=\"inputType\"\n                    class=\"p-inputotp-input\"\n                    [inputmode]=\"inputMode\"\n                    [variant]=\"variant\"\n                    [readonly]=\"readonly\"\n                    [disabled]=\"disabled\"\n                    [invalid]=\"invalid\"\n                    [tabindex]=\"tabindex\"\n                    [unstyled]=\"unstyled\"\n                    (input)=\"onInput($event, i - 1)\"\n                    (focus)=\"onInputFocus($event)\"\n                    (blur)=\"onInputBlur($event)\"\n                    (paste)=\"onPaste($event)\"\n                    (keydown)=\"onKeyDown($event)\"\n                    pAutoFocus\n                    [autofocus]=\"getAutofocus(i)\"\n                />\n            </ng-container>\n            <ng-container *ngIf=\"inputTemplate\">\n                <ng-container *ngTemplateOutlet=\"inputTemplate; context: { $implicit: getToken(i - 1), events: getTemplateEvents(i - 1), index: i }\"> </ng-container>\n            </ng-container>\n        </ng-container>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-inputotp p-component'\n    },\n    providers: [INPUT_OTP_VALUE_ACCESSOR]\n})\nexport class InputOtp implements AfterContentInit {\n    /**\n     * When present, it specifies that the component should have invalid state style.\n     * @group Props\n     */\n    @Input() invalid: boolean = false;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n\n    @Input() disabled: boolean = false;\n    /**\n     * When present, it specifies that an input field is read-only.\n     * @group Props\n     */\n    @Input() readonly: boolean = false;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input() tabindex: number | null = null;\n    /**\n     * Number of characters to initiate.\n     * @group Props\n     */\n    @Input() length: number = 4;\n    /**\n     * Mask pattern.\n     * @group Props\n     */\n    @Input() mask: boolean = false;\n    /**\n     * When present, it specifies that an input field is integer-only.\n     * @group Props\n     */\n    @Input() integerOnly: boolean = false;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Callback to invoke on value change.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<InputOtpChangeEvent> = new EventEmitter<InputOtpChangeEvent>();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter();\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    inputTemplate: Nullable<TemplateRef<any>>;\n\n    tokens: any = [];\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    value: any;\n\n    get inputMode(): string {\n        return this.integerOnly ? 'numeric' : 'text';\n    }\n\n    get inputType(): string {\n        return this.mask ? 'password' : 'text';\n    }\n\n    constructor(public cd: ChangeDetectorRef) {}\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'input':\n                    this.inputTemplate = item.template;\n                    break;\n                default:\n                    this.inputTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    getToken(index) {\n        return this.tokens[index];\n    }\n\n    getTemplateEvents(index) {\n        return {\n            input: (event) => this.onInput(event, index),\n            keydown: (event) => this.onKeyDown(event),\n            focus: (event) => this.onFocus.emit(event),\n            blur: (event) => this.onBlur.emit(event),\n            paste: (event) => this.onPaste(event)\n        };\n    }\n\n    onInput(event, index) {\n        this.tokens[index] = event.target.value;\n        this.updateModel(event);\n\n        if (event.inputType === 'deleteContentBackward') {\n            this.moveToPrev(event);\n        } else if (event.inputType === 'insertText' || event.inputType === 'deleteContentForward') {\n            this.moveToNext(event);\n        }\n    }\n\n    updateModel(event: any) {\n        const newValue = this.tokens.join('');\n        this.onModelChange(newValue);\n\n        this.onChange.emit({\n            originalEvent: event,\n            value: newValue\n        });\n    }\n\n    writeValue(value: any): void {\n        if (value) {\n            if (Array.isArray(value) && value.length > 0) {\n                this.value = value.slice(0, this.length);\n            } else {\n                this.value = value.toString().split('').slice(0, this.length);\n            }\n        } else {\n            this.value = value;\n        }\n        this.updateTokens();\n        this.cd.markForCheck();\n    }\n\n    updateTokens() {\n        if (this.value !== null && this.value !== undefined) {\n            if (Array.isArray(this.value)) {\n                this.tokens = [...this.value];\n            } else {\n                this.tokens = this.value.toString().split('');\n            }\n        } else {\n            this.tokens = [];\n        }\n    }\n\n    getModelValue(i: number) {\n        return this.tokens[i - 1] || '';\n    }\n\n    getAutofocus(i: number): boolean {\n        if (i === 1) {\n            return this.autofocus;\n        }\n        return false;\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    moveToPrev(event) {\n        let prevInput = this.findPrevInput(event.target);\n\n        if (prevInput) {\n            prevInput.focus();\n            prevInput.select();\n        }\n    }\n\n    moveToNext(event) {\n        let nextInput = this.findNextInput(event.target);\n\n        if (nextInput) {\n            nextInput.focus();\n            nextInput.select();\n        }\n    }\n\n    findNextInput(element) {\n        let nextElement = element.nextElementSibling;\n\n        if (!nextElement) return;\n\n        return nextElement.nodeName === 'INPUT' ? nextElement : this.findNextInput(nextElement);\n    }\n\n    findPrevInput(element) {\n        let prevElement = element.previousElementSibling;\n\n        if (!prevElement) return;\n\n        return prevElement.nodeName === 'INPUT' ? prevElement : this.findPrevInput(prevElement);\n    }\n\n    onInputFocus(event) {\n        event.target.select();\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event) {\n        this.onBlur.emit(event);\n    }\n\n    onKeyDown(event) {\n        if (event.altKey || event.ctrlKey || event.metaKey) {\n            return;\n        }\n\n        switch (event.key) {\n            case 'ArrowLeft':\n                this.moveToPrev(event);\n                event.preventDefault();\n\n                break;\n\n            case 'ArrowUp':\n            case 'ArrowDown':\n                event.preventDefault();\n\n                break;\n\n            case 'Backspace':\n                if (event.target.value.length === 0) {\n                    this.moveToPrev(event);\n                    event.preventDefault();\n                }\n\n                break;\n\n            case 'ArrowRight':\n                this.moveToNext(event);\n                event.preventDefault();\n\n                break;\n\n            default:\n                if ((this.integerOnly && !((event.code.startsWith('Digit') || event.code.startsWith('Numpad')) && Number(event.key) >= 0 && Number(event.key) <= 9)) || (this.tokens.join('').length >= this.length && event.code !== 'Delete')) {\n                    event.preventDefault();\n                }\n\n                break;\n        }\n    }\n\n    onPaste(event) {\n        if (!this.disabled && !this.readonly) {\n            let paste = event.clipboardData.getData('text');\n\n            if (paste.length) {\n                let pastedCode = paste.substring(0, this.length + 1);\n\n                if (!this.integerOnly || !isNaN(pastedCode)) {\n                    this.tokens = pastedCode.split('');\n                    this.updateModel(event);\n                }\n            }\n\n            event.preventDefault();\n        }\n    }\n\n    getRange(n: number): number[] {\n        return Array.from({ length: n }, (_, index) => index + 1);\n    }\n\n    trackByFn(index: number) {\n        return index;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, InputTextModule, AutoFocusModule],\n    exports: [InputOtp, SharedModule],\n    declarations: [InputOtp]\n})\nexport class InputOtpModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;AASa,MAAA,wBAAwB,GAAQ;AACzC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,QAAQ,CAAC;AACvC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MAyCU,QAAQ,CAAA;AAqFE,IAAA,EAAA,CAAA;AApFnB;;;AAGG;IACM,OAAO,GAAY,KAAK,CAAC;AAClC;;;AAGG;IAEM,QAAQ,GAAY,KAAK,CAAC;AACnC;;;AAGG;IACM,QAAQ,GAAY,KAAK,CAAC;AACnC;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;AAGG;IACM,QAAQ,GAAkB,IAAI,CAAC;AACxC;;;AAGG;IACM,MAAM,GAAW,CAAC,CAAC;AAC5B;;;AAGG;IACM,IAAI,GAAY,KAAK,CAAC;AAC/B;;;AAGG;IACM,WAAW,GAAY,KAAK,CAAC;AACtC;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;AACO,IAAA,QAAQ,GAAsC,IAAI,YAAY,EAAuB,CAAC;AAChG;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAE,CAAC;AAC5D;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAE,CAAC;AAE3B,IAAA,SAAS,CAAqC;AAE9E,IAAA,aAAa,CAA6B;IAE1C,MAAM,GAAQ,EAAE,CAAC;AAEjB,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,KAAK,CAAM;AAEX,IAAA,IAAI,SAAS,GAAA;QACT,OAAO,IAAI,CAAC,WAAW,GAAG,SAAS,GAAG,MAAM,CAAC;KAChD;AAED,IAAA,IAAI,SAAS,GAAA;QACT,OAAO,IAAI,CAAC,IAAI,GAAG,UAAU,GAAG,MAAM,CAAC;KAC1C;AAED,IAAA,WAAA,CAAmB,EAAqB,EAAA;QAArB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;IAE5C,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AACV,gBAAA;AACI,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;AACV,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC7B;AAED,IAAA,iBAAiB,CAAC,KAAK,EAAA;QACnB,OAAO;AACH,YAAA,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;YAC5C,OAAO,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;AACzC,YAAA,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AAC1C,YAAA,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACxC,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;SACxC,CAAC;KACL;IAED,OAAO,CAAC,KAAK,EAAE,KAAK,EAAA;QAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AACxC,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAExB,QAAA,IAAI,KAAK,CAAC,SAAS,KAAK,uBAAuB,EAAE;AAC7C,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC1B,SAAA;aAAM,IAAI,KAAK,CAAC,SAAS,KAAK,YAAY,IAAI,KAAK,CAAC,SAAS,KAAK,sBAAsB,EAAE;AACvF,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAE7B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,KAAK,EAAE,QAAQ;AAClB,SAAA,CAAC,CAAC;KACN;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,KAAK,EAAE;AACP,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1C,gBAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACjE,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,SAAA;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YACjD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACpB,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,CAAS,EAAA;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;KACnC;AAED,IAAA,YAAY,CAAC,CAAS,EAAA;QAClB,IAAI,CAAC,KAAK,CAAC,EAAE;YACT,OAAO,IAAI,CAAC,SAAS,CAAC;AACzB,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;QACZ,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEjD,QAAA,IAAI,SAAS,EAAE;YACX,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,SAAS,CAAC,MAAM,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;QACZ,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEjD,QAAA,IAAI,SAAS,EAAE;YACX,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,SAAS,CAAC,MAAM,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,OAAO,EAAA;AACjB,QAAA,IAAI,WAAW,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAE7C,QAAA,IAAI,CAAC,WAAW;YAAE,OAAO;AAEzB,QAAA,OAAO,WAAW,CAAC,QAAQ,KAAK,OAAO,GAAG,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;KAC3F;AAED,IAAA,aAAa,CAAC,OAAO,EAAA;AACjB,QAAA,IAAI,WAAW,GAAG,OAAO,CAAC,sBAAsB,CAAC;AAEjD,QAAA,IAAI,CAAC,WAAW;YAAE,OAAO;AAEzB,QAAA,OAAO,WAAW,CAAC,QAAQ,KAAK,OAAO,GAAG,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;KAC3F;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;QACX,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE;YAChD,OAAO;AACV,SAAA;QAED,QAAQ,KAAK,CAAC,GAAG;AACb,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,MAAM;AAEV,YAAA,KAAK,SAAS,CAAC;AACf,YAAA,KAAK,WAAW;gBACZ,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,MAAM;AAEV,YAAA,KAAK,WAAW;gBACZ,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACjC,oBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oBACvB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBAED,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,MAAM;AAEV,YAAA;AACI,gBAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE;oBAC7N,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,KAAK,EAAA;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,IAAI,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAEhD,IAAI,KAAK,CAAC,MAAM,EAAE;AACd,gBAAA,IAAI,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAErD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;oBACzC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACnC,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3B,iBAAA;AACJ,aAAA;YAED,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,CAAS,EAAA;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;KAC7D;AAED,IAAA,SAAS,CAAC,KAAa,EAAA;AACnB,QAAA,OAAO,KAAK,CAAC;KAChB;uGA/RQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAR,QAAQ,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EA8CG,gBAAgB,CAhDzB,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,EAAA,SAAA,EAAA,CAAC,wBAAwB,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAmEpB,aAAa,EAvGpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAxCpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,wBAAwB;AAClC,qBAAA;oBACD,SAAS,EAAE,CAAC,wBAAwB,CAAC;AACxC,iBAAA,CAAA;sFAMY,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAMG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK5B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAsOrB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAvSd,YAAA,EAAA,CAAA,QAAQ,CAmSP,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,CAnS7D,EAAA,OAAA,EAAA,CAAA,QAAQ,EAoSG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGvB,cAAc,EAAA,OAAA,EAAA,CAJb,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAClD,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGvB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,CAAC;AACvE,oBAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;oBACjC,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;AChWD;;AAEG;;;;"}