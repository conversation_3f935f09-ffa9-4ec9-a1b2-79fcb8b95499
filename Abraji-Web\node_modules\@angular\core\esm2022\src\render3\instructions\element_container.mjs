/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { validateMatchingNode, validateNodeExists } from '../../hydration/error_handling';
import { locateNextRNode, siblingAfter } from '../../hydration/node_lookup_utils';
import { getNgContainerSize, markRNodeAsClaimedByHydration, setSegmentHead, } from '../../hydration/utils';
import { isDetachedByI18n } from '../../i18n/utils';
import { assertEqual, assertIndexInRange, assertNumber } from '../../util/assert';
import { assertHasParent } from '../assert';
import { attachPatchData } from '../context_discovery';
import { registerPostOrderHooks } from '../hooks';
import { isContentQueryHost, isDirectiveHost } from '../interfaces/type_checks';
import { HEADER_OFFSET, HYDRATION, RENDERER } from '../interfaces/view';
import { assertTNodeType } from '../node_assert';
import { appendChild, createCommentNode } from '../node_manipulation';
import { getBindingIndex, getCurrentTNode, getLView, getTView, isCurrentTNodeParent, isInSkipHydrationBlock, lastNodeWasCreated, setCurrentTNode, setCurrentTNodeAsNotParent, wasLastNodeCreated, } from '../state';
import { computeStaticStyling } from '../styling/static_styling';
import { getConstant } from '../util/view_utils';
import { createDirectivesInstances, executeContentQueries, getOrCreateTNode, resolveDirectives, saveResolvedLocalsInData, } from './shared';
function elementContainerStartFirstCreatePass(index, tView, lView, attrsIndex, localRefsIndex) {
    ngDevMode && ngDevMode.firstCreatePass++;
    const tViewConsts = tView.consts;
    const attrs = getConstant(tViewConsts, attrsIndex);
    const tNode = getOrCreateTNode(tView, index, 8 /* TNodeType.ElementContainer */, 'ng-container', attrs);
    // While ng-container doesn't necessarily support styling, we use the style context to identify
    // and execute directives on the ng-container.
    if (attrs !== null) {
        computeStaticStyling(tNode, attrs, true);
    }
    const localRefs = getConstant(tViewConsts, localRefsIndex);
    resolveDirectives(tView, lView, tNode, localRefs);
    if (tView.queries !== null) {
        tView.queries.elementStart(tView, tNode);
    }
    return tNode;
}
/**
 * Creates a logical container for other nodes (<ng-container>) backed by a comment node in the DOM.
 * The instruction must later be followed by `elementContainerEnd()` call.
 *
 * @param index Index of the element in the LView array
 * @param attrsIndex Index of the container attributes in the `consts` array.
 * @param localRefsIndex Index of the container's local references in the `consts` array.
 * @returns This function returns itself so that it may be chained.
 *
 * Even if this instruction accepts a set of attributes no actual attribute values are propagated to
 * the DOM (as a comment node can't have attributes). Attributes are here only for directive
 * matching purposes and setting initial inputs of directives.
 *
 * @codeGenApi
 */
export function ɵɵelementContainerStart(index, attrsIndex, localRefsIndex) {
    const lView = getLView();
    const tView = getTView();
    const adjustedIndex = index + HEADER_OFFSET;
    ngDevMode && assertIndexInRange(lView, adjustedIndex);
    ngDevMode &&
        assertEqual(getBindingIndex(), tView.bindingStartIndex, 'element containers should be created before any bindings');
    const tNode = tView.firstCreatePass
        ? elementContainerStartFirstCreatePass(adjustedIndex, tView, lView, attrsIndex, localRefsIndex)
        : tView.data[adjustedIndex];
    setCurrentTNode(tNode, true);
    const comment = _locateOrCreateElementContainerNode(tView, lView, tNode, index);
    lView[adjustedIndex] = comment;
    if (wasLastNodeCreated()) {
        appendChild(tView, lView, comment, tNode);
    }
    attachPatchData(comment, lView);
    if (isDirectiveHost(tNode)) {
        createDirectivesInstances(tView, lView, tNode);
        executeContentQueries(tView, tNode, lView);
    }
    if (localRefsIndex != null) {
        saveResolvedLocalsInData(lView, tNode);
    }
    return ɵɵelementContainerStart;
}
/**
 * Mark the end of the <ng-container>.
 * @returns This function returns itself so that it may be chained.
 *
 * @codeGenApi
 */
export function ɵɵelementContainerEnd() {
    let currentTNode = getCurrentTNode();
    const tView = getTView();
    if (isCurrentTNodeParent()) {
        setCurrentTNodeAsNotParent();
    }
    else {
        ngDevMode && assertHasParent(currentTNode);
        currentTNode = currentTNode.parent;
        setCurrentTNode(currentTNode, false);
    }
    ngDevMode && assertTNodeType(currentTNode, 8 /* TNodeType.ElementContainer */);
    if (tView.firstCreatePass) {
        registerPostOrderHooks(tView, currentTNode);
        if (isContentQueryHost(currentTNode)) {
            tView.queries.elementEnd(currentTNode);
        }
    }
    return ɵɵelementContainerEnd;
}
/**
 * Creates an empty logical container using {@link elementContainerStart}
 * and {@link elementContainerEnd}
 *
 * @param index Index of the element in the LView array
 * @param attrsIndex Index of the container attributes in the `consts` array.
 * @param localRefsIndex Index of the container's local references in the `consts` array.
 * @returns This function returns itself so that it may be chained.
 *
 * @codeGenApi
 */
export function ɵɵelementContainer(index, attrsIndex, localRefsIndex) {
    ɵɵelementContainerStart(index, attrsIndex, localRefsIndex);
    ɵɵelementContainerEnd();
    return ɵɵelementContainer;
}
let _locateOrCreateElementContainerNode = (tView, lView, tNode, index) => {
    lastNodeWasCreated(true);
    return createCommentNode(lView[RENDERER], ngDevMode ? 'ng-container' : '');
};
/**
 * Enables hydration code path (to lookup existing elements in DOM)
 * in addition to the regular creation mode of comment nodes that
 * represent <ng-container>'s anchor.
 */
function locateOrCreateElementContainerNode(tView, lView, tNode, index) {
    let comment;
    const hydrationInfo = lView[HYDRATION];
    const isNodeCreationMode = !hydrationInfo || isInSkipHydrationBlock() || isDetachedByI18n(tNode);
    lastNodeWasCreated(isNodeCreationMode);
    // Regular creation mode.
    if (isNodeCreationMode) {
        return createCommentNode(lView[RENDERER], ngDevMode ? 'ng-container' : '');
    }
    // Hydration mode, looking up existing elements in DOM.
    const currentRNode = locateNextRNode(hydrationInfo, tView, lView, tNode);
    ngDevMode && validateNodeExists(currentRNode, lView, tNode);
    const ngContainerSize = getNgContainerSize(hydrationInfo, index);
    ngDevMode &&
        assertNumber(ngContainerSize, 'Unexpected state: hydrating an <ng-container>, ' + 'but no hydration info is available.');
    setSegmentHead(hydrationInfo, index, currentRNode);
    comment = siblingAfter(ngContainerSize, currentRNode);
    if (ngDevMode) {
        validateMatchingNode(comment, Node.COMMENT_NODE, null, lView, tNode);
        markRNodeAsClaimedByHydration(comment);
    }
    return comment;
}
export function enableLocateOrCreateElementContainerNodeImpl() {
    _locateOrCreateElementContainerNode = locateOrCreateElementContainerNode;
}
//# sourceMappingURL=data:application/json;base64,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