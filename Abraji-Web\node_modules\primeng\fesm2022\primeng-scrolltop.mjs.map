{"version": 3, "file": "primeng-scrolltop.mjs", "sources": ["../../src/app/components/scrolltop/scrolltop.ts", "../../src/app/components/scrolltop/primeng-scrolltop.ts"], "sourcesContent": ["import { AnimationEvent, animate, state, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, ElementRef, Inject, Input, NgModule, OnDestroy, OnInit, PLATFORM_ID, QueryList, Renderer2, TemplateRef, ViewEncapsulation, numberAttribute } from '@angular/core';\nimport { PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronUpIcon } from 'primeng/icons/chevronup';\nimport { ZIndexUtils } from 'primeng/utils';\n/**\n * ScrollTop gets displayed after a certain scroll position and used to navigates to the top of the page quickly.\n * @group Components\n */\n@Component({\n    selector: 'p-scrollTop',\n    template: `\n        <button\n            *ngIf=\"visible\"\n            [@animation]=\"{ value: 'open', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            (@animation.start)=\"onEnter($event)\"\n            (@animation.done)=\"onLeave($event)\"\n            [attr.aria-label]=\"buttonAriaLabel\"\n            [ngClass]=\"containerClass()\"\n            (click)=\"onClick()\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            type=\"button\"\n        >\n            <ng-container *ngIf=\"!iconTemplate\">\n                <span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-scrolltop-icon'\"></span>\n                <ChevronUpIcon *ngIf=\"!icon\" [styleClass]=\"'p-scrolltop-icon'\" [ngStyle]=\"{ 'font-size': '1rem', scale: '1.5' }\" />\n            </ng-container>\n            <ng-template [ngIf]=\"!icon\" *ngTemplateOutlet=\"iconTemplate; context: { styleClass: 'p-scrolltop-icon' }\"></ng-template>\n        </button>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./scrolltop.css'],\n    animations: [\n        trigger('animation', [\n            state(\n                'void',\n                style({\n                    opacity: 0\n                })\n            ),\n            state(\n                'open',\n                style({\n                    opacity: 1\n                })\n            ),\n            transition('void => open', animate('{{showTransitionParams}}')),\n            transition('open => void', animate('{{hideTransitionParams}}'))\n        ])\n    ],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ScrollTop implements OnInit, OnDestroy {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Target of the ScrollTop.\n     * @group Props\n     */\n    @Input() target: 'window' | 'parent' | undefined = 'window';\n    /**\n     * Defines the threshold value of the vertical scroll position of the target to toggle the visibility.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) threshold: number = 400;\n    /**\n     * Name of the icon or JSX.Element for icon.\n     * @group Props\n     */\n    @Input() icon: string | undefined;\n    /**\n     * Defines the scrolling behavior, \"smooth\" adds an animation and \"auto\" scrolls with a jump.\n     * @group Props\n     */\n    @Input() behavior: 'auto' | 'smooth' | undefined = 'smooth';\n    /**\n     * A string value used to determine the display transition options.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.15s';\n    /**\n     * A string value used to determine the hiding transition options.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.15s';\n    /**\n     * Establishes a string value that labels the scroll-top button.\n     * @group Props\n     */\n    @Input() buttonAriaLabel: string | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    iconTemplate: TemplateRef<any> | undefined;\n\n    documentScrollListener: VoidFunction | null | undefined;\n\n    parentScrollListener: VoidFunction | null | undefined;\n\n    visible: boolean = false;\n\n    overlay: any;\n\n    private window: Window | null;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, public el: ElementRef, private cd: ChangeDetectorRef, public config: PrimeNGConfig) {\n        this.window = this.document.defaultView;\n    }\n\n    ngOnInit() {\n        if (this.target === 'window') this.bindDocumentScrollListener();\n        else if (this.target === 'parent') this.bindParentScrollListener();\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    onClick() {\n        let scrollElement = this.target === 'window' ? this.window : this.el.nativeElement.parentElement;\n        scrollElement.scroll({\n            top: 0,\n            behavior: this.behavior\n        });\n    }\n\n    onEnter(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'open':\n                this.overlay = event.element;\n                ZIndexUtils.set('overlay', this.overlay, this.config.zIndex.overlay);\n                break;\n            case 'void':\n                this.overlay = null;\n                break;\n        }\n    }\n\n    onLeave(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                ZIndexUtils.clear(event.element);\n                break;\n        }\n    }\n\n    checkVisibility(scrollY: number) {\n        if (scrollY > this.threshold) this.visible = true;\n        else this.visible = false;\n\n        this.cd.markForCheck();\n    }\n\n    bindParentScrollListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.parentScrollListener = this.renderer.listen(this.el.nativeElement.parentElement, 'scroll', () => {\n                this.checkVisibility(this.el.nativeElement.parentElement.scrollTop);\n            });\n        }\n    }\n\n    bindDocumentScrollListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.documentScrollListener = this.renderer.listen(this.window, 'scroll', () => {\n                this.checkVisibility(DomHandler.getWindowScrollTop());\n            });\n        }\n    }\n\n    unbindParentScrollListener() {\n        if (this.parentScrollListener) {\n            this.parentScrollListener();\n            this.parentScrollListener = null;\n        }\n    }\n\n    unbindDocumentScrollListener() {\n        if (this.documentScrollListener) {\n            this.documentScrollListener();\n            this.documentScrollListener = null;\n        }\n    }\n\n    containerClass() {\n        return {\n            'p-scrolltop p-link p-component': true,\n            'p-scrolltop-sticky': this.target !== 'window'\n        };\n    }\n\n    ngOnDestroy() {\n        if (this.target === 'window') this.unbindDocumentScrollListener();\n        else if (this.target === 'parent') this.unbindParentScrollListener();\n\n        if (this.overlay) {\n            ZIndexUtils.clear(this.overlay);\n            this.overlay = null;\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, ChevronUpIcon, SharedModule],\n    exports: [ScrollTop, SharedModule],\n    declarations: [ScrollTop]\n})\nexport class ScrollTopModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;AAOA;;;AAGG;MAgDU,SAAS,CAAA;AA6DoB,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AA5DlM;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;IACM,MAAM,GAAoC,QAAQ,CAAC;AAC5D;;;AAGG;IACoC,SAAS,GAAW,GAAG,CAAC;AAC/D;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;IACM,QAAQ,GAAkC,QAAQ,CAAC;AAC5D;;;AAGG;IACM,qBAAqB,GAAW,MAAM,CAAC;AAChD;;;AAGG;IACM,qBAAqB,GAAW,MAAM,CAAC;AAChD;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAEb,IAAA,SAAS,CAAuC;AAEhF,IAAA,YAAY,CAA+B;AAE3C,IAAA,sBAAsB,CAAkC;AAExD,IAAA,oBAAoB,CAAkC;IAEtD,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,OAAO,CAAM;AAEL,IAAA,MAAM,CAAgB;IAE9B,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAU,QAAmB,EAAS,EAAc,EAAU,EAAqB,EAAS,MAAqB,EAAA;QAAjL,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACnN,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;KAC3C;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAC3D,aAAA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAAE,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACtE;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,OAAO,GAAA;QACH,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC;QACjG,aAAa,CAAC,MAAM,CAAC;AACjB,YAAA,GAAG,EAAE,CAAC;YACN,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC1B,SAAA,CAAC,CAAC;KACN;AAED,IAAA,OAAO,CAAC,KAAqB,EAAA;QACzB,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC7B,gBAAA,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACrE,MAAM;AACV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,MAAM;AACb,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,KAAqB,EAAA;QACzB,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;AACP,gBAAA,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM;AACb,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,OAAe,EAAA;AAC3B,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS;AAAE,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;;AAC7C,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAE1B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,wBAAwB,GAAA;AACpB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,EAAE,QAAQ,EAAE,MAAK;AACjG,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AACxE,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAK;gBAC3E,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAC1D,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,cAAc,GAAA;QACV,OAAO;AACH,YAAA,gCAAgC,EAAE,IAAI;AACtC,YAAA,oBAAoB,EAAE,IAAI,CAAC,MAAM,KAAK,QAAQ;SACjD,CAAC;KACL;IAED,WAAW,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAAE,IAAI,CAAC,4BAA4B,EAAE,CAAC;AAC7D,aAAA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAErE,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACvB,SAAA;KACJ;uGAhKQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA6DE,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA7DpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EAoBE,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,eAAe,CA2BlB,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA5FpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;AAmBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,8MAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA8LuB,aAAa,CA1LzB,EAAA,QAAA,EAAA,eAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,WAAW,EAAE;AACjB,gBAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA,CAAC,CACL;AACD,gBAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC/D,gBAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;aAClE,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAKQ,SAAS,EAAA,UAAA,EAAA,CAAA;kBA/CrB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACb,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;AAmBT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAEzB,UAAA,EAAA;wBACR,OAAO,CAAC,WAAW,EAAE;AACjB,4BAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,gCAAA,OAAO,EAAE,CAAC;AACb,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,gCAAA,OAAO,EAAE,CAAC;AACb,6BAAA,CAAC,CACL;AACD,4BAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC/D,4BAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;yBAClE,CAAC;qBACL,EACK,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,8MAAA,CAAA,EAAA,CAAA;;0BA+DY,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;sJAxDpE,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKiC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAE0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAyHrB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAf,eAAe,EAAA,YAAA,EAAA,CAxKf,SAAS,CAAA,EAAA,OAAA,EAAA,CAoKR,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CApK1C,SAAS,EAqKG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGxB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAJd,YAAY,EAAE,aAAa,EAAE,YAAY,EAC9B,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGxB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;AACpD,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;oBAClC,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;ACjOD;;AAEG;;;;"}