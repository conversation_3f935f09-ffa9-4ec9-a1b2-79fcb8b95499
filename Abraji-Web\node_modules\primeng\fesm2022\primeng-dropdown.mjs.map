{"version": 3, "file": "primeng-dropdown.mjs", "sources": ["../../src/app/components/dropdown/dropdown.ts", "../../src/app/components/dropdown/primeng-dropdown.ts"], "sourcesContent": ["import { AnimationEvent } from '@angular/animations';\nimport { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewChecked,\n    AfterViewInit,\n    booleanAttribute,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    computed,\n    ContentChildren,\n    effect,\n    ElementRef,\n    EventEmitter,\n    forwardRef,\n    Input,\n    NgModule,\n    NgZone,\n    numberAttribute,\n    OnInit,\n    Output,\n    QueryList,\n    Renderer2,\n    Signal,\n    signal,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    ViewRef\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { FilterService, OverlayOptions, PrimeNGConfig, PrimeTemplate, SelectItem, SharedModule, TranslationKeys } from 'primeng/api';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { DomHandler } from 'primeng/dom';\nimport { Overlay, OverlayModule } from 'primeng/overlay';\nimport { RippleModule } from 'primeng/ripple';\nimport { <PERSON><PERSON><PERSON>, ScrollerModule } from 'primeng/scroller';\nimport { ScrollerOptions } from 'primeng/api';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { BlankIcon } from 'primeng/icons/blank';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { SearchIcon } from 'primeng/icons/search';\nimport { DropdownChangeEvent, DropdownFilterEvent, DropdownFilterOptions, DropdownLazyLoadEvent } from './dropdown.interface';\nimport { Nullable } from 'primeng/ts-helpers';\n\nexport const DROPDOWN_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Dropdown),\n    multi: true\n};\n\n@Component({\n    selector: 'p-dropdownItem',\n    template: `\n        <li\n            [id]=\"id\"\n            (click)=\"onOptionClick($event)\"\n            (mouseenter)=\"onOptionMouseEnter($event)\"\n            role=\"option\"\n            pRipple\n            [attr.aria-label]=\"label\"\n            [attr.aria-setsize]=\"ariaSetSize\"\n            [attr.aria-posinset]=\"ariaPosInset\"\n            [attr.aria-selected]=\"selected\"\n            [attr.data-p-focused]=\"focused\"\n            [attr.data-p-highlight]=\"selected\"\n            [attr.data-p-disabled]=\"disabled\"\n            [ngStyle]=\"{ height: itemSize + 'px' }\"\n            [ngClass]=\"{ 'p-dropdown-item': true, 'p-highlight': selected, 'p-disabled': disabled, 'p-focus': focused }\"\n        >\n            <ng-container *ngIf=\"checkmark\">\n                <CheckIcon *ngIf=\"selected\" [styleClass]=\"'p-dropdown-check-icon'\" />\n                <BlankIcon *ngIf=\"!selected\" [styleClass]=\"'p-dropdown-blank-icon'\" />\n            </ng-container>\n            <span *ngIf=\"!template\">{{ label ?? 'empty' }}</span>\n            <ng-container *ngTemplateOutlet=\"template; context: { $implicit: option }\"></ng-container>\n        </li>\n    `,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class DropdownItem {\n    @Input() id: string | undefined;\n\n    @Input() option: SelectItem | undefined;\n\n    @Input({ transform: booleanAttribute }) selected: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) focused: boolean | undefined;\n\n    @Input() label: string | undefined;\n\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) visible: boolean | undefined;\n\n    @Input({ transform: numberAttribute }) itemSize: number | undefined;\n\n    @Input() ariaPosInset: string | undefined;\n\n    @Input() ariaSetSize: string | undefined;\n\n    @Input() template: TemplateRef<any> | undefined;\n\n    @Input({ transform: booleanAttribute }) checkmark: boolean;\n\n    @Output() onClick: EventEmitter<any> = new EventEmitter();\n\n    @Output() onMouseEnter: EventEmitter<any> = new EventEmitter();\n\n    ngOnInit() {}\n\n    onOptionClick(event: Event) {\n        this.onClick.emit(event);\n    }\n\n    onOptionMouseEnter(event: Event) {\n        this.onMouseEnter.emit(event);\n    }\n}\n\n/**\n * Dropdown also known as Select, is used to choose an item from a collection of options.\n * @group Components\n */\n@Component({\n    selector: 'p-dropdown',\n    template: `\n        <div #container [attr.id]=\"id\" [ngClass]=\"containerClass\" (click)=\"onContainerClick($event)\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <span\n                #focusInput\n                [ngClass]=\"inputClass\"\n                *ngIf=\"!editable\"\n                [pTooltip]=\"tooltip\"\n                [tooltipPosition]=\"tooltipPosition\"\n                [positionStyle]=\"tooltipPositionStyle\"\n                [tooltipStyleClass]=\"tooltipStyleClass\"\n                [attr.aria-disabled]=\"disabled\"\n                [attr.id]=\"inputId\"\n                role=\"combobox\"\n                [attr.aria-label]=\"ariaLabel || (label() === 'p-emptylabel' ? undefined : label())\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-haspopup]=\"'listbox'\"\n                [attr.aria-expanded]=\"overlayVisible ?? false\"\n                [attr.aria-controls]=\"overlayVisible ? id + '_list' : null\"\n                [attr.tabindex]=\"!disabled ? tabindex : -1\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n                [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                [attr.aria-required]=\"required\"\n                [attr.required]=\"required\"\n            >\n                <ng-container *ngIf=\"!selectedItemTemplate; else defaultPlaceholder\">{{ label() === 'p-emptylabel' ? '&nbsp;' : label() }}</ng-container>\n                <ng-container *ngIf=\"selectedItemTemplate && !isSelectedOptionEmpty()\" [ngTemplateOutlet]=\"selectedItemTemplate\" [ngTemplateOutletContext]=\"{ $implicit: selectedOption }\"></ng-container>\n                <ng-template #defaultPlaceholder>\n                    <span *ngIf=\"isSelectedOptionEmpty()\">{{ label() === 'p-emptylabel' ? '&nbsp;' : label() }}</span>\n                </ng-template>\n            </span>\n            <input\n                *ngIf=\"editable\"\n                #editableInput\n                type=\"text\"\n                [attr.id]=\"inputId\"\n                [attr.maxlength]=\"maxlength\"\n                [ngClass]=\"inputClass\"\n                [disabled]=\"disabled\"\n                aria-haspopup=\"listbox\"\n                [attr.placeholder]=\"modelValue() === undefined || modelValue() === null ? placeholder() : undefined\"\n                [attr.aria-label]=\"ariaLabel || (label() === 'p-emptylabel' ? undefined : label())\"\n                (input)=\"onEditableInput($event)\"\n                (keydown)=\"onKeyDown($event)\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n                [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n            />\n            <ng-container *ngIf=\"isVisibleClearIcon\">\n                <TimesIcon [styleClass]=\"'p-dropdown-clear-icon'\" (click)=\"clear($event)\" *ngIf=\"!clearIconTemplate\" [attr.data-pc-section]=\"'clearicon'\" />\n                <span class=\"p-dropdown-clear-icon\" (click)=\"clear($event)\" *ngIf=\"clearIconTemplate\" [attr.data-pc-section]=\"'clearicon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n\n            <div class=\"p-dropdown-trigger\" role=\"button\" aria-label=\"dropdown trigger\" (mousedown)=\"onMouseDown($event)\" aria-haspopup=\"listbox\" [attr.aria-expanded]=\"overlayVisible ?? false\" [attr.data-pc-section]=\"'trigger'\">\n                <ng-container *ngIf=\"loading; else elseBlock\">\n                    <ng-container *ngIf=\"loadingIconTemplate\">\n                        <ng-container *ngTemplateOutlet=\"loadingIconTemplate\"></ng-container>\n                    </ng-container>\n                    <ng-container *ngIf=\"!loadingIconTemplate\">\n                        <span *ngIf=\"loadingIcon\" [ngClass]=\"'p-dropdown-trigger-icon pi-spin ' + loadingIcon\" aria-hidden=\"true\"></span>\n                        <span *ngIf=\"!loadingIcon\" [class]=\"'p-dropdown-trigger-icon pi pi-spinner pi-spin'\" aria-hidden=\"true\"></span>\n                    </ng-container>\n                </ng-container>\n\n                <ng-template #elseBlock>\n                    <ng-container *ngIf=\"!dropdownIconTemplate\">\n                        <span class=\"p-dropdown-trigger-icon\" *ngIf=\"dropdownIcon\" [ngClass]=\"dropdownIcon\"></span>\n                        <ChevronDownIcon *ngIf=\"!dropdownIcon\" [styleClass]=\"'p-dropdown-trigger-icon'\" />\n                    </ng-container>\n                    <span *ngIf=\"dropdownIconTemplate\" class=\"p-dropdown-trigger-icon\">\n                        <ng-template *ngTemplateOutlet=\"dropdownIconTemplate\"></ng-template>\n                    </span>\n                </ng-template>\n            </div>\n\n            <p-overlay\n                #overlay\n                [(visible)]=\"overlayVisible\"\n                [options]=\"overlayOptions\"\n                [target]=\"'@parent'\"\n                [appendTo]=\"appendTo\"\n                [autoZIndex]=\"autoZIndex\"\n                [baseZIndex]=\"baseZIndex\"\n                [showTransitionOptions]=\"showTransitionOptions\"\n                [hideTransitionOptions]=\"hideTransitionOptions\"\n                (onAnimationStart)=\"onOverlayAnimationStart($event)\"\n                (onHide)=\"hide()\"\n            >\n                <ng-template pTemplate=\"content\">\n                    <div [ngClass]=\"'p-dropdown-panel p-component'\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\">\n                        <span\n                            #firstHiddenFocusableEl\n                            role=\"presentation\"\n                            class=\"p-hidden-accessible p-hidden-focusable\"\n                            [attr.tabindex]=\"0\"\n                            (focus)=\"onFirstHiddenFocus($event)\"\n                            [attr.data-p-hidden-accessible]=\"true\"\n                            [attr.data-p-hidden-focusable]=\"true\"\n                        >\n                        </span>\n                        <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                        <div class=\"p-dropdown-header\" *ngIf=\"filter\" (click)=\"$event.stopPropagation()\">\n                            <ng-container *ngIf=\"filterTemplate; else builtInFilterElement\">\n                                <ng-container *ngTemplateOutlet=\"filterTemplate; context: { options: filterOptions }\"></ng-container>\n                            </ng-container>\n                            <ng-template #builtInFilterElement>\n                                <div class=\"p-dropdown-filter-container\">\n                                    <input\n                                        #filter\n                                        type=\"text\"\n                                        role=\"searchbox\"\n                                        autocomplete=\"off\"\n                                        [value]=\"_filterValue() || ''\"\n                                        class=\"p-dropdown-filter p-inputtext p-component\"\n                                        [ngClass]=\"{ 'p-variant-filled': variant === 'filled' || config.inputStyle() === 'filled' }\"\n                                        [attr.placeholder]=\"filterPlaceholder\"\n                                        [attr.aria-owns]=\"id + '_list'\"\n                                        (input)=\"onFilterInputChange($event)\"\n                                        [attr.aria-label]=\"ariaFilterLabel\"\n                                        [attr.aria-activedescendant]=\"focusedOptionId\"\n                                        (keydown)=\"onFilterKeyDown($event)\"\n                                        (blur)=\"onFilterBlur($event)\"\n                                    />\n                                    <SearchIcon *ngIf=\"!filterIconTemplate\" [styleClass]=\"'p-dropdown-filter-icon'\" />\n                                    <span *ngIf=\"filterIconTemplate\" class=\"p-dropdown-filter-icon\">\n                                        <ng-template *ngTemplateOutlet=\"filterIconTemplate\"></ng-template>\n                                    </span>\n                                </div>\n                            </ng-template>\n                        </div>\n                        <div\n                            class=\"p-dropdown-items-wrapper\"\n                            [ngStyle]=\"{\n                                'max-height': virtualScroll ? 'auto' : scrollHeight || 'auto'\n                            }\"\n                            tabindex=\"0\"\n                        >\n                            <p-scroller\n                                *ngIf=\"virtualScroll\"\n                                #scroller\n                                [items]=\"visibleOptions()\"\n                                [style]=\"{ height: scrollHeight }\"\n                                [itemSize]=\"virtualScrollItemSize || _itemSize\"\n                                [autoSize]=\"true\"\n                                [lazy]=\"lazy\"\n                                (onLazyLoad)=\"onLazyLoad.emit($event)\"\n                                [options]=\"virtualScrollOptions\"\n                            >\n                                <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                                    <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: items, options: scrollerOptions }\"></ng-container>\n                                </ng-template>\n                                <ng-container *ngIf=\"loaderTemplate\">\n                                    <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                                        <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: scrollerOptions }\"></ng-container>\n                                    </ng-template>\n                                </ng-container>\n                            </p-scroller>\n                            <ng-container *ngIf=\"!virtualScroll\">\n                                <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: visibleOptions(), options: {} }\"></ng-container>\n                            </ng-container>\n\n                            <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                                <ul #items [attr.id]=\"id + '_list'\" [attr.aria-label]=\"listLabel\" class=\"p-dropdown-items\" [ngClass]=\"scrollerOptions.contentStyleClass\" [ngStyle]=\"scrollerOptions.contentStyle\" role=\"listbox\">\n                                    <ng-template ngFor let-option [ngForOf]=\"items\" let-i=\"index\">\n                                        <ng-container *ngIf=\"isOptionGroup(option)\">\n                                            <li class=\"p-dropdown-item-group\" [attr.id]=\"id + '_' + getOptionIndex(i, scrollerOptions)\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                                <span *ngIf=\"!groupTemplate\">{{ getOptionGroupLabel(option.optionGroup) }}</span>\n                                                <ng-container *ngTemplateOutlet=\"groupTemplate; context: { $implicit: option.optionGroup }\"></ng-container>\n                                            </li>\n                                        </ng-container>\n                                        <ng-container *ngIf=\"!isOptionGroup(option)\">\n                                            <p-dropdownItem\n                                                [id]=\"id + '_' + getOptionIndex(i, scrollerOptions)\"\n                                                [option]=\"option\"\n                                                [checkmark]=\"checkmark\"\n                                                [selected]=\"isSelected(option)\"\n                                                [label]=\"getOptionLabel(option)\"\n                                                [disabled]=\"isOptionDisabled(option)\"\n                                                [template]=\"itemTemplate\"\n                                                [focused]=\"focusedOptionIndex() === getOptionIndex(i, scrollerOptions)\"\n                                                [ariaPosInset]=\"getAriaPosInset(getOptionIndex(i, scrollerOptions))\"\n                                                [ariaSetSize]=\"ariaSetSize\"\n                                                (onClick)=\"onOptionSelect($event, option)\"\n                                                (onMouseEnter)=\"onOptionMouseEnter($event, getOptionIndex(i, scrollerOptions))\"\n                                            ></p-dropdownItem>\n                                        </ng-container>\n                                    </ng-template>\n                                    <li *ngIf=\"filterValue && isEmpty()\" class=\"p-dropdown-empty-message\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                        <ng-container *ngIf=\"!emptyFilterTemplate && !emptyTemplate; else emptyFilter\">\n                                            {{ emptyFilterMessageLabel }}\n                                        </ng-container>\n                                        <ng-container #emptyFilter *ngTemplateOutlet=\"emptyFilterTemplate || emptyTemplate\"></ng-container>\n                                    </li>\n                                    <li *ngIf=\"!filterValue && isEmpty()\" class=\"p-dropdown-empty-message\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                        <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                            {{ emptyMessageLabel }}\n                                        </ng-container>\n                                        <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                                    </li>\n                                </ul>\n                            </ng-template>\n                        </div>\n                        <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n                        <span\n                            #lastHiddenFocusableEl\n                            role=\"presentation\"\n                            class=\"p-hidden-accessible p-hidden-focusable\"\n                            [attr.tabindex]=\"0\"\n                            (focus)=\"onLastHiddenFocus($event)\"\n                            [attr.data-p-hidden-accessible]=\"true\"\n                            [attr.data-p-hidden-focusable]=\"true\"\n                        ></span>\n                    </div>\n                </ng-template>\n            </p-overlay>\n        </div>\n    `,\n\n    host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled()',\n        '[class.p-inputwrapper-focus]': 'focused || overlayVisible'\n    },\n    providers: [DROPDOWN_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./dropdown.css']\n})\nexport class Dropdown implements OnInit, AfterViewInit, AfterContentInit, AfterViewChecked, ControlValueAccessor {\n    /**\n     * Unique identifier of the component\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    @Input() scrollHeight: string = '200px';\n    /**\n     * When specified, displays an input field to filter the items on keyup.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) filter: boolean | undefined;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Inline style of the overlay panel element.\n     * @group Props\n     */\n    @Input() panelStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Style class of the overlay panel element.\n     * @group Props\n     */\n    @Input() panelStyleClass: string | undefined;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean | undefined;\n    /**\n     * When present, it specifies that an input field must be filled out before submitting the form.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) required: boolean | undefined;\n    /**\n     * When present, custom value instead of predefined options can be entered using the editable input field.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) editable: boolean | undefined;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined = 0;\n    /**\n     * Default text to display when no option is selected.\n     * @group Props\n     */\n    @Input() set placeholder(val: string | undefined) {\n        this._placeholder.set(val);\n    }\n    get placeholder(): Signal<string | undefined> {\n        return this._placeholder.asReadonly();\n    }\n    /**\n     * Icon to display in loading state.\n     * @group Props\n     */\n    @Input() loadingIcon: string | undefined;\n    /**\n     * Placeholder text to show when filter input is empty.\n     * @group Props\n     */\n    @Input() filterPlaceholder: string | undefined;\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() filterLocale: string | undefined;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Identifier of the accessible input element.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    @Input() dataKey: string | undefined;\n    /**\n     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n     * @group Props\n     */\n    @Input() filterBy: string | undefined;\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    @Input() filterFields: any[] | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Clears the filter value when hiding the dropdown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) resetFilterOnHide: boolean = false;\n    /**\n     * Whether the selected option will be shown with a check mark.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) checkmark: boolean = false;\n    /**\n     * Icon class of the dropdown icon.\n     * @group Props\n     */\n    @Input() dropdownIcon: string | undefined;\n    /**\n     * Whether the dropdown is in loading state.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) loading: boolean | undefined = false;\n    /**\n     * Name of the label field of an option.\n     * @group Props\n     */\n    @Input() optionLabel: string | undefined;\n    /**\n     * Name of the value field of an option.\n     * @group Props\n     */\n    @Input() optionValue: string | undefined;\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    @Input() optionDisabled: string | undefined;\n    /**\n     * Name of the label field of an option group.\n     * @group Props\n     */\n    @Input() optionGroupLabel: string | undefined = 'label';\n    /**\n     * Name of the options field of an option group.\n     * @group Props\n     */\n    @Input() optionGroupChildren: string = 'items';\n    /**\n     * Whether to display the first item as the label if no placeholder is defined and value is null.\n     * @deprecated since v17.3.0, set initial value by model instead.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoDisplayFirst: boolean = true;\n    /**\n     * Whether to display options as grouped when nested options are provided.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) group: boolean | undefined;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean | undefined;\n    /**\n     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    @Input() emptyFilterMessage: string = '';\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    @Input() emptyMessage: string = '';\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazy: boolean = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) virtualScroll: boolean | undefined;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) virtualScrollItemSize: number | undefined;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() virtualScrollOptions: ScrollerOptions | undefined;\n    /**\n     * Whether to use overlay API feature. The properties of overlay API can be used like an object in it.\n     * @group Props\n     */\n    @Input() overlayOptions: OverlayOptions | undefined;\n    /**\n     * Defines a string that labels the filter input.\n     * @group Props\n     */\n    @Input() ariaFilterLabel: string | undefined;\n    /**\n     * Used to define a aria label attribute the current element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Defines how the items are filtered.\n     * @group Props\n     */\n    @Input() filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte' = 'contains';\n    /**\n     * Maximum number of character allows in the editable input field.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) maxlength: number | undefined;\n    /**\n     * Advisory information to display in a tooltip on hover.\n     * @group Props\n     */\n    @Input() tooltip: string = '';\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    @Input() tooltipPosition: 'top' | 'left' | 'right' | 'bottom' = 'right';\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    @Input() tooltipPositionStyle: string = 'absolute';\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    @Input() tooltipStyleClass: string | undefined;\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) focusOnHover: boolean = false;\n    /**\n     * Determines if the option will be selected on focus.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) selectOnFocus: boolean = false;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoOptionFocus: boolean = true;\n    /**\n     * Applies focus to the filter element when the overlay is shown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocusFilter: boolean = true;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input() get disabled(): boolean | undefined {\n        return this._disabled;\n    }\n    set disabled(_disabled: boolean | undefined) {\n        if (_disabled) {\n            this.focused = false;\n\n            if (this.overlayVisible) this.hide();\n        }\n\n        this._disabled = _disabled;\n        if (!(this.cd as ViewRef).destroyed) {\n            this.cd.detectChanges();\n        }\n    }\n    /**\n     * Item size of item to be virtual scrolled.\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    @Input() get itemSize(): number | undefined {\n        return this._itemSize;\n    }\n    set itemSize(val: number | undefined) {\n        this._itemSize = val;\n        console.warn('The itemSize property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    _itemSize: number | undefined;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    @Input() get autoZIndex(): boolean | undefined {\n        return this._autoZIndex;\n    }\n    set autoZIndex(val: boolean | undefined) {\n        this._autoZIndex = val;\n        console.warn('The autoZIndex property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    _autoZIndex: boolean | undefined;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    @Input() get baseZIndex(): number | undefined {\n        return this._baseZIndex;\n    }\n    set baseZIndex(val: number | undefined) {\n        this._baseZIndex = val;\n        console.warn('The baseZIndex property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    _baseZIndex: number | undefined;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    @Input() get showTransitionOptions(): string | undefined {\n        return this._showTransitionOptions;\n    }\n    set showTransitionOptions(val: string | undefined) {\n        this._showTransitionOptions = val;\n        console.warn('The showTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    _showTransitionOptions: string | undefined;\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     * @deprecated since v14.2.0, use overlayOptions property instead.\n     */\n    @Input() get hideTransitionOptions(): string | undefined {\n        return this._hideTransitionOptions;\n    }\n    set hideTransitionOptions(val: string | undefined) {\n        this._hideTransitionOptions = val;\n        console.warn('The hideTransitionOptions property is deprecated since v14.2.0, use overlayOptions property instead.');\n    }\n    _hideTransitionOptions: string | undefined;\n    /**\n     * When specified, filter displays with this value.\n     * @group Props\n     */\n    @Input() get filterValue(): string | undefined | null {\n        return this._filterValue();\n    }\n    set filterValue(val: string | undefined | null) {\n        setTimeout(() => {\n            this._filterValue.set(val);\n        });\n    }\n    /**\n     * An array of objects to display as the available options.\n     * @group Props\n     */\n    @Input() get options(): any[] | undefined {\n        const options = this._options();\n        return options;\n    }\n    set options(val: any[] | undefined) {\n        if (!ObjectUtils.deepEquals(val, this._options())) {\n            this._options.set(val);\n        }\n    }\n    /**\n     * Callback to invoke when value of dropdown changes.\n     * @param {DropdownChangeEvent} event - custom change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<DropdownChangeEvent> = new EventEmitter<DropdownChangeEvent>();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {DropdownFilterEvent} event - custom filter event.\n     * @group Emits\n     */\n    @Output() onFilter: EventEmitter<DropdownFilterEvent> = new EventEmitter<DropdownFilterEvent>();\n    /**\n     * Callback to invoke when dropdown gets focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when dropdown loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when component is clicked.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    @Output() onClick: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();\n    /**\n     * Callback to invoke when dropdown overlay gets visible.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<AnimationEvent> = new EventEmitter<AnimationEvent>();\n    /**\n     * Callback to invoke when dropdown overlay gets hidden.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<AnimationEvent> = new EventEmitter<AnimationEvent>();\n    /**\n     * Callback to invoke when dropdown clears the value.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {DropdownLazyLoadEvent} event - Lazy load event.\n     * @group Emits\n     */\n    @Output() onLazyLoad: EventEmitter<DropdownLazyLoadEvent> = new EventEmitter<DropdownLazyLoadEvent>();\n\n    @ViewChild('container') containerViewChild: Nullable<ElementRef>;\n\n    @ViewChild('filter') filterViewChild: Nullable<ElementRef>;\n\n    @ViewChild('focusInput') focusInputViewChild: Nullable<ElementRef>;\n\n    @ViewChild('editableInput') editableInputViewChild: Nullable<ElementRef>;\n\n    @ViewChild('items') itemsViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scroller') scroller: Nullable<Scroller>;\n\n    @ViewChild('overlay') overlayViewChild: Nullable<Overlay>;\n\n    @ViewChild('firstHiddenFocusableEl') firstHiddenFocusableElementOnOverlay: Nullable<ElementRef>;\n\n    @ViewChild('lastHiddenFocusableEl') lastHiddenFocusableElementOnOverlay: Nullable<ElementRef>;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    _disabled: boolean | undefined;\n\n    itemsWrapper: Nullable<HTMLDivElement>;\n\n    itemTemplate: Nullable<TemplateRef<any>>;\n\n    groupTemplate: Nullable<TemplateRef<any>>;\n\n    loaderTemplate: Nullable<TemplateRef<any>>;\n\n    selectedItemTemplate: Nullable<TemplateRef<any>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    filterTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    emptyFilterTemplate: Nullable<TemplateRef<any>>;\n\n    emptyTemplate: Nullable<TemplateRef<any>>;\n\n    dropdownIconTemplate: Nullable<TemplateRef<any>>;\n\n    loadingIconTemplate: Nullable<TemplateRef<any>>;\n\n    clearIconTemplate: Nullable<TemplateRef<any>>;\n\n    filterIconTemplate: Nullable<TemplateRef<any>>;\n\n    filterOptions: DropdownFilterOptions | undefined;\n\n    _options = signal<any[] | undefined>(null);\n\n    _placeholder = signal<string | undefined>(undefined);\n\n    modelValue = signal<any>(null);\n\n    value: any;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    hover: Nullable<boolean>;\n\n    focused: Nullable<boolean>;\n\n    overlayVisible: Nullable<boolean>;\n\n    optionsChanged: Nullable<boolean>;\n\n    panel: Nullable<HTMLDivElement>;\n\n    dimensionsUpdated: Nullable<boolean>;\n\n    hoveredItem: any;\n\n    selectedOptionUpdated: Nullable<boolean>;\n\n    _filterValue = signal<any>(null);\n\n    searchValue: Nullable<string>;\n\n    searchIndex: Nullable<number>;\n\n    searchTimeout: any;\n\n    previousSearchChar: Nullable<string>;\n\n    currentSearchChar: Nullable<string>;\n\n    preventModelTouched: Nullable<boolean>;\n\n    focusedOptionIndex = signal<number>(-1);\n\n    labelId: Nullable<string>;\n\n    listId: Nullable<string>;\n\n    clicked = signal<boolean>(false);\n\n    get emptyMessageLabel(): string {\n        return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n\n    get emptyFilterMessageLabel(): string {\n        return this.emptyFilterMessage || this.config.getTranslation(TranslationKeys.EMPTY_FILTER_MESSAGE);\n    }\n\n    get isVisibleClearIcon(): boolean | undefined {\n        return this.modelValue() != null && this.hasSelectedOption() && this.showClear && !this.disabled;\n    }\n\n    get listLabel(): string {\n        return this.config.getTranslation(TranslationKeys.ARIA)['listLabel'];\n    }\n\n    get containerClass() {\n        return {\n            'p-dropdown p-component p-inputwrapper': true,\n            'p-disabled': this.disabled,\n            'p-dropdown-clearable': this.showClear && !this.disabled,\n            'p-focus': this.focused,\n            'p-inputwrapper-filled': this.modelValue() !== undefined && this.modelValue() !== null && !this.modelValue().length,\n            'p-inputwrapper-focus': this.focused || this.overlayVisible,\n            'p-variant-filled': this.variant === 'filled' || this.config.inputStyle() === 'filled',\n            'p-dropdown-open': this.overlayVisible\n        };\n    }\n\n    get inputClass() {\n        const label = this.label();\n        return {\n            'p-dropdown-label p-inputtext': true,\n            'p-placeholder': this.placeholder() && label === this.placeholder(),\n            'p-dropdown-label-empty': !this.editable && !this.selectedItemTemplate && (label === undefined || label === null || label === 'p-emptylabel' || label.length === 0)\n        };\n    }\n\n    get panelClass() {\n        return {\n            'p-dropdown-panel p-component': true,\n            'p-input-filled': this.config.inputStyle() === 'filled',\n            'p-ripple-disabled': this.config.ripple === false\n        };\n    }\n\n    get focusedOptionId() {\n        return this.focusedOptionIndex() !== -1 ? `${this.id}_${this.focusedOptionIndex()}` : null;\n    }\n\n    visibleOptions = computed(() => {\n        const options = this.getAllVisibleAndNonVisibleOptions();\n\n        if (this._filterValue()) {\n            const _filterBy = this.filterBy || this.optionLabel;\n\n            const filteredOptions =\n                !_filterBy && !this.filterFields && !this.optionValue\n                    ? this.options.filter((option) => {\n                          if (option.label) {\n                              return option.label.toString().toLowerCase().indexOf(this._filterValue().toLowerCase().trim()) !== -1;\n                          }\n                          return option.toString().toLowerCase().indexOf(this._filterValue().toLowerCase().trim()) !== -1;\n                      })\n                    : this.filterService.filter(options, this.searchFields(), this._filterValue().trim(), this.filterMatchMode, this.filterLocale);\n\n            if (this.group) {\n                const optionGroups = this.options || [];\n                const filtered = [];\n\n                optionGroups.forEach((group) => {\n                    const groupChildren = this.getOptionGroupChildren(group);\n                    const filteredItems = groupChildren.filter((item) => filteredOptions.includes(item));\n\n                    if (filteredItems.length > 0) filtered.push({ ...group, [typeof this.optionGroupChildren === 'string' ? this.optionGroupChildren : 'items']: [...filteredItems] });\n                });\n\n                return this.flatOptions(filtered);\n            }\n            return filteredOptions;\n        }\n\n        return options;\n    });\n\n    label = computed(() => {\n        // use  getAllVisibleAndNonVisibleOptions verses just visible options\n        // this will find the selected option whether or not the user is currently filtering  because the filtered (i.e. visible) options, are a subset of all the options\n        const options = this.getAllVisibleAndNonVisibleOptions();\n        // use isOptionEqualsModelValue for the use case where the dropdown is initalized with a disabled option\n        const selectedOptionIndex = options.findIndex((option) => this.isOptionValueEqualsModelValue(option));\n\n        return selectedOptionIndex !== -1 ? this.getOptionLabel(options[selectedOptionIndex]) : this.placeholder() || 'p-emptylabel';\n    });\n\n    filled = computed(() => {\n        if (typeof this.modelValue() === 'string') return !!this.modelValue();\n        return this.label() !== 'p-emptylabel' && this.modelValue() !== undefined && this.modelValue() !== null;\n    });\n\n    selectedOption: any;\n\n    editableInputValue = computed(() => this.getOptionLabel(this.selectedOption) || this.modelValue() || '');\n\n    constructor(public el: ElementRef, public renderer: Renderer2, public cd: ChangeDetectorRef, public zone: NgZone, public filterService: FilterService, public config: PrimeNGConfig) {\n        effect(() => {\n            const modelValue = this.modelValue();\n            const visibleOptions = this.visibleOptions();\n\n            if (visibleOptions && ObjectUtils.isNotEmpty(visibleOptions)) {\n                const selectedOptionIndex = this.findSelectedOptionIndex();\n\n                if (selectedOptionIndex !== -1 || modelValue === undefined || (typeof modelValue === 'string' && modelValue.length === 0) || this.isModelValueNotSet() || this.editable) {\n                    this.selectedOption = visibleOptions[selectedOptionIndex];\n                }\n            }\n\n            if (ObjectUtils.isEmpty(visibleOptions) && (modelValue === undefined || this.isModelValueNotSet()) && ObjectUtils.isNotEmpty(this.selectedOption)) {\n                this.selectedOption = null;\n            }\n\n            if (modelValue !== undefined && this.editable) {\n                this.updateEditableLabel();\n            }\n            this.cd.markForCheck();\n        });\n    }\n\n    private isModelValueNotSet(): boolean {\n        return this.modelValue() === null && !this.isOptionValueEqualsModelValue(this.selectedOption);\n    }\n\n    private getAllVisibleAndNonVisibleOptions() {\n        return this.group ? this.flatOptions(this.options) : this.options || [];\n    }\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n        this.autoUpdateModel();\n\n        if (this.filterBy) {\n            this.filterOptions = {\n                filter: (value) => this.onFilterInputChange(value),\n                reset: () => this.resetFilter()\n            };\n        }\n    }\n\n    ngAfterViewChecked() {\n        if (this.optionsChanged && this.overlayVisible) {\n            this.optionsChanged = false;\n\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    if (this.overlayViewChild) {\n                        this.overlayViewChild.alignOverlay();\n                    }\n                }, 1);\n            });\n        }\n\n        if (this.selectedOptionUpdated && this.itemsWrapper) {\n            let selectedItem = DomHandler.findSingle(this.overlayViewChild?.overlayViewChild?.nativeElement, 'li.p-highlight');\n            if (selectedItem) {\n                DomHandler.scrollInView(this.itemsWrapper, selectedItem);\n            }\n            this.selectedOptionUpdated = false;\n        }\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'selectedItem':\n                    this.selectedItemTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'filter':\n                    this.filterTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'emptyfilter':\n                    this.emptyFilterTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyTemplate = item.template;\n                    break;\n\n                case 'group':\n                    this.groupTemplate = item.template;\n                    break;\n\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n\n                case 'dropdownicon':\n                    this.dropdownIconTemplate = item.template;\n                    break;\n\n                case 'loadingicon':\n                    this.loadingIconTemplate = item.template;\n                    break;\n\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n\n                case 'filtericon':\n                    this.filterIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    flatOptions(options) {\n        return (options || []).reduce((result, option, index) => {\n            result.push({ optionGroup: option, group: true, index });\n\n            const optionGroupChildren = this.getOptionGroupChildren(option);\n\n            optionGroupChildren && optionGroupChildren.forEach((o) => result.push(o));\n\n            return result;\n        }, []);\n    }\n\n    autoUpdateModel() {\n        if (this.selectOnFocus && this.autoOptionFocus && !this.hasSelectedOption()) {\n            this.focusedOptionIndex.set(this.findFirstFocusedOptionIndex());\n            this.onOptionSelect(null, this.visibleOptions()[this.focusedOptionIndex()], false);\n        }\n        if (this.autoDisplayFirst && (this.modelValue() === null || this.modelValue() === undefined)) {\n            if (!this.placeholder()) {\n                const ind = this.findFirstOptionIndex();\n                this.onOptionSelect(null, this.visibleOptions()[ind], false, true);\n            }\n        }\n    }\n\n    onOptionSelect(event, option, isHide = true, preventChange = false) {\n        if (!this.isSelected(option)) {\n            const value = this.getOptionValue(option);\n            this.updateModel(value, event);\n            this.focusedOptionIndex.set(this.findSelectedOptionIndex());\n            preventChange === false && this.onChange.emit({ originalEvent: event, value: value });\n        }\n        if (isHide) {\n            this.hide(true);\n        }\n    }\n\n    onOptionMouseEnter(event, index) {\n        if (this.focusOnHover) {\n            this.changeFocusedOptionIndex(event, index);\n        }\n    }\n\n    updateModel(value, event?) {\n        this.value = value;\n        this.onModelChange(value);\n        this.modelValue.set(value);\n        this.selectedOptionUpdated = true;\n    }\n\n    writeValue(value: any): void {\n        if (this.filter) {\n            this.resetFilter();\n        }\n\n        this.value = value;\n        this.allowModelChange() && this.onModelChange(value);\n        this.modelValue.set(this.value);\n        this.updateEditableLabel();\n        this.cd.markForCheck();\n    }\n\n    allowModelChange() {\n        return this.autoDisplayFirst && !this.placeholder() && (this.modelValue() === undefined || this.modelValue() === null) && !this.editable && this.options && this.options.length;\n    }\n\n    isSelectedOptionEmpty() {\n        return ObjectUtils.isEmpty(this.selectedOption);\n    }\n\n    isSelected(option) {\n        return this.isValidOption(option) && this.isOptionValueEqualsModelValue(option);\n    }\n\n    private isOptionValueEqualsModelValue(option: any) {\n        return ObjectUtils.equals(this.modelValue(), this.getOptionValue(option), this.equalityKey());\n    }\n\n    ngAfterViewInit() {\n        if (this.editable) {\n            this.updateEditableLabel();\n        }\n        this.updatePlaceHolderForFloatingLabel();\n    }\n\n    updatePlaceHolderForFloatingLabel(): void {\n        if (this._placeholder() !== null && this._placeholder() !== undefined) {\n            // We don't want to overwrite the placeholder if it's already set\n            return;\n        }\n        const parentElement = this.el.nativeElement.parentElement;\n        const isInFloatingLabel = parentElement?.classList.contains('p-float-label');\n        if (parentElement && isInFloatingLabel && !this.selectedOption) {\n            const label = parentElement.querySelector('label');\n            if (label) {\n                this._placeholder.set(label.textContent);\n            }\n        }\n    }\n\n    updateEditableLabel(): void {\n        if (this.editableInputViewChild) {\n            this.editableInputViewChild.nativeElement.value = this.getOptionLabel(this.selectedOption) || this.modelValue() || '';\n        }\n    }\n\n    clearEditableLabel(): void {\n        if (this.editableInputViewChild) {\n            this.editableInputViewChild.nativeElement.value = '';\n        }\n    }\n\n    getOptionIndex(index, scrollerOptions) {\n        return this.virtualScrollerDisabled ? index : scrollerOptions && scrollerOptions.getItemOptions(index)['index'];\n    }\n\n    getOptionLabel(option: any) {\n        return this.optionLabel !== undefined && this.optionLabel !== null ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option && option.label !== undefined ? option.label : option;\n    }\n\n    getOptionValue(option: any) {\n        return this.optionValue && this.optionValue !== null ? ObjectUtils.resolveFieldData(option, this.optionValue) : !this.optionLabel && option && option.value !== undefined ? option.value : option;\n    }\n\n    isOptionDisabled(option: any) {\n        if (this.getOptionValue(this.modelValue()) === this.getOptionValue(option) || (this.getOptionLabel(this.modelValue() === this.getOptionLabel(option)) && option.disabled === false)) {\n            return false;\n        } else {\n            return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : option && option.disabled !== undefined ? option.disabled : false;\n        }\n    }\n\n    getOptionGroupLabel(optionGroup: any) {\n        return this.optionGroupLabel !== undefined && this.optionGroupLabel !== null ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label !== undefined ? optionGroup.label : optionGroup;\n    }\n\n    getOptionGroupChildren(optionGroup: any) {\n        return this.optionGroupChildren !== undefined && this.optionGroupChildren !== null ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n\n    getAriaPosInset(index) {\n        return (\n            (this.optionGroupLabel\n                ? index -\n                  this.visibleOptions()\n                      .slice(0, index)\n                      .filter((option) => this.isOptionGroup(option)).length\n                : index) + 1\n        );\n    }\n\n    get ariaSetSize() {\n        return this.visibleOptions().filter((option) => !this.isOptionGroup(option)).length;\n    }\n\n    /**\n     * Callback to invoke on filter reset.\n     * @group Method\n     */\n    public resetFilter(): void {\n        this._filterValue.set(null);\n\n        if (this.filterViewChild && this.filterViewChild.nativeElement) {\n            this.filterViewChild.nativeElement.value = '';\n        }\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    onContainerClick(event: any) {\n        if (this.disabled || this.readonly || this.loading) {\n            return;\n        }\n\n        this.focusInputViewChild?.nativeElement.focus({ preventScroll: true });\n\n        if (event.target.tagName === 'INPUT' || event.target.getAttribute('data-pc-section') === 'clearicon' || event.target.closest('[data-pc-section=\"clearicon\"]')) {\n            return;\n        } else if (!this.overlayViewChild || !this.overlayViewChild.el.nativeElement.contains(event.target)) {\n            this.overlayVisible ? this.hide(true) : this.show(true);\n        }\n        this.onClick.emit(event);\n        this.clicked.set(true);\n        this.cd.detectChanges();\n    }\n\n    isEmpty() {\n        return !this._options() || (this.visibleOptions() && this.visibleOptions().length === 0);\n    }\n\n    onEditableInput(event: KeyboardEvent) {\n        const value = (event.target as HTMLInputElement).value;\n        this.searchValue = '';\n        const matched = this.searchOptions(event, value);\n        !matched && this.focusedOptionIndex.set(-1);\n\n        this.onModelChange(value);\n        this.updateModel(value, event);\n        setTimeout(() => {\n            this.onChange.emit({ originalEvent: event, value: value });\n        }, 1);\n\n        !this.overlayVisible && ObjectUtils.isNotEmpty(value) && this.show();\n    }\n    /**\n     * Displays the panel.\n     * @group Method\n     */\n    public show(isFocus?) {\n        this.overlayVisible = true;\n        const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : this.editable ? -1 : this.findSelectedOptionIndex();\n        this.focusedOptionIndex.set(focusedOptionIndex);\n\n        if (isFocus) {\n            DomHandler.focus(this.focusInputViewChild?.nativeElement);\n        }\n\n        this.cd.markForCheck();\n    }\n\n    onOverlayAnimationStart(event: AnimationEvent) {\n        if (event.toState === 'visible') {\n            this.itemsWrapper = DomHandler.findSingle(this.overlayViewChild?.overlayViewChild?.nativeElement, this.virtualScroll ? '.p-scroller' : '.p-dropdown-items-wrapper');\n            this.virtualScroll && this.scroller?.setContentEl(this.itemsViewChild?.nativeElement);\n\n            if (this.options && this.options.length) {\n                if (this.virtualScroll) {\n                    const selectedIndex = this.modelValue() ? this.focusedOptionIndex() : -1;\n                    if (selectedIndex !== -1) {\n                        this.scroller?.scrollToIndex(selectedIndex);\n                    }\n                } else {\n                    let selectedListItem = DomHandler.findSingle(this.itemsWrapper, '.p-dropdown-item.p-highlight');\n\n                    if (selectedListItem) {\n                        selectedListItem.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n                    }\n                }\n            }\n\n            if (this.filterViewChild && this.filterViewChild.nativeElement) {\n                this.preventModelTouched = true;\n\n                if (this.autofocusFilter && !this.editable) {\n                    this.filterViewChild.nativeElement.focus();\n                }\n            }\n\n            this.onShow.emit(event);\n        }\n        if (event.toState === 'void') {\n            this.itemsWrapper = null;\n            this.onModelTouched();\n            this.onHide.emit(event);\n        }\n    }\n    /**\n     * Hides the panel.\n     * @group Method\n     */\n    public hide(isFocus?) {\n        this.overlayVisible = false;\n        this.focusedOptionIndex.set(-1);\n        this.clicked.set(false);\n        this.searchValue = '';\n\n        if (this.overlayOptions?.mode === 'modal') {\n            DomHandler.unblockBodyScroll();\n        }\n        if (this.filter && this.resetFilterOnHide) {\n            this.resetFilter();\n        }\n        if (isFocus) {\n            if (this.focusInputViewChild) {\n                setTimeout(() => {\n                    DomHandler.focus(this.focusInputViewChild?.nativeElement);\n                });\n            }\n            if (this.editable && this.editableInputViewChild) {\n                setTimeout(() => {\n                    DomHandler.focus(this.editableInputViewChild?.nativeElement);\n                });\n            }\n        }\n        this.cd.markForCheck();\n    }\n\n    onInputFocus(event: Event) {\n        if (this.disabled) {\n            // For ScreenReaders\n            return;\n        }\n\n        this.focused = true;\n        const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.overlayVisible && this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n        this.focusedOptionIndex.set(focusedOptionIndex);\n        this.overlayVisible && this.scrollInView(this.focusedOptionIndex());\n\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event: Event) {\n        this.focused = false;\n        this.onBlur.emit(event);\n\n        if (!this.preventModelTouched) {\n            this.onModelTouched();\n        }\n        this.preventModelTouched = false;\n    }\n\n    onMouseDown(event: MouseEvent) {\n        event.preventDefault();\n    }\n\n    onKeyDown(event: KeyboardEvent, search: boolean) {\n        if (this.disabled || this.readonly || this.loading) {\n            return;\n        }\n\n        switch (event.code) {\n            //down\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            //up\n            case 'ArrowUp':\n                this.onArrowUpKey(event, this.editable);\n                break;\n\n            case 'ArrowLeft':\n            case 'ArrowRight':\n                this.onArrowLeftKey(event, this.editable);\n                break;\n\n            case 'Delete':\n                this.onDeleteKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event, this.editable);\n                break;\n\n            case 'End':\n                this.onEndKey(event, this.editable);\n                break;\n\n            case 'PageDown':\n                this.onPageDownKey(event);\n                break;\n\n            case 'PageUp':\n                this.onPageUpKey(event);\n                break;\n\n            //space\n            case 'Space':\n                this.onSpaceKey(event, search);\n                break;\n\n            //enter\n            case 'Enter':\n            case 'NumpadEnter':\n                this.onEnterKey(event);\n                break;\n\n            //escape and tab\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            case 'Backspace':\n                this.onBackspaceKey(event, this.editable);\n                break;\n\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n\n            default:\n                if (!event.metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    !this.overlayVisible && this.show();\n                    !this.editable && this.searchOptions(event, event.key);\n                }\n\n                break;\n        }\n\n        this.clicked.set(false);\n    }\n\n    onFilterKeyDown(event) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event, true);\n                break;\n\n            case 'ArrowLeft':\n            case 'ArrowRight':\n                this.onArrowLeftKey(event, true);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event, true);\n                break;\n\n            case 'End':\n                this.onEndKey(event, true);\n                break;\n\n            case 'Enter':\n            case 'NumpadEnter':\n                this.onEnterKey(event, true);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event, true);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onFilterBlur(event) {\n        this.focusedOptionIndex.set(-1);\n    }\n\n    onArrowDownKey(event: KeyboardEvent) {\n        if (!this.overlayVisible) {\n            this.show();\n            this.editable && this.changeFocusedOptionIndex(event, this.findSelectedOptionIndex());\n        } else {\n            const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.clicked() ? this.findFirstOptionIndex() : this.findFirstFocusedOptionIndex();\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n        }\n        // const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.findFirstFocusedOptionIndex();\n        // this.changeFocusedOptionIndex(event, optionIndex);\n\n        // !this.overlayVisible && this.show();\n        event.preventDefault();\n        event.stopPropagation();\n    }\n\n    changeFocusedOptionIndex(event, index) {\n        if (this.focusedOptionIndex() !== index) {\n            this.focusedOptionIndex.set(index);\n            this.scrollInView();\n\n            if (this.selectOnFocus) {\n                const option = this.visibleOptions()[index];\n                this.onOptionSelect(event, option, false);\n            }\n        }\n    }\n\n    get virtualScrollerDisabled() {\n        return !this.virtualScroll;\n    }\n\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n\n        if (this.itemsViewChild && this.itemsViewChild.nativeElement) {\n            const element = DomHandler.findSingle(this.itemsViewChild.nativeElement, `li[id=\"${id}\"]`);\n            if (element) {\n                element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n            } else if (!this.virtualScrollerDisabled) {\n                setTimeout(() => {\n                    this.virtualScroll && this.scroller?.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex());\n                }, 0);\n            }\n        }\n    }\n\n    hasSelectedOption() {\n        return this.modelValue() !== undefined;\n    }\n\n    isValidSelectedOption(option) {\n        return this.isValidOption(option) && this.isSelected(option);\n    }\n\n    equalityKey() {\n        return this.optionValue ? null : this.dataKey;\n    }\n\n    findFirstFocusedOptionIndex() {\n        const selectedIndex = this.findSelectedOptionIndex();\n        return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n\n    findFirstOptionIndex() {\n        return this.visibleOptions().findIndex((option) => this.isValidOption(option));\n    }\n\n    findSelectedOptionIndex() {\n        return this.hasSelectedOption() ? this.visibleOptions().findIndex((option) => this.isValidSelectedOption(option)) : -1;\n    }\n\n    findNextOptionIndex(index) {\n        const matchedOptionIndex =\n            index < this.visibleOptions().length - 1\n                ? this.visibleOptions()\n                      .slice(index + 1)\n                      .findIndex((option) => this.isValidOption(option))\n                : -1;\n        return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n\n    findPrevOptionIndex(index) {\n        const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), (option) => this.isValidOption(option)) : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n\n    findLastOptionIndex() {\n        return ObjectUtils.findLastIndex(this.visibleOptions(), (option) => this.isValidOption(option));\n    }\n\n    findLastFocusedOptionIndex() {\n        const selectedIndex = this.findSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n\n    isValidOption(option) {\n        return option !== undefined && option !== null && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n    }\n\n    isOptionGroup(option) {\n        return this.optionGroupLabel !== undefined && this.optionGroupLabel !== null && option.optionGroup !== undefined && option.optionGroup !== null && option.group;\n    }\n\n    onArrowUpKey(event: KeyboardEvent, pressedInInputText: boolean = false) {\n        if (event.altKey && !pressedInInputText) {\n            if (this.focusedOptionIndex() !== -1) {\n                const option = this.visibleOptions()[this.focusedOptionIndex()];\n                this.onOptionSelect(event, option);\n            }\n\n            this.overlayVisible && this.hide();\n        } else {\n            const optionIndex = this.focusedOptionIndex() !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex()) : this.clicked() ? this.findLastOptionIndex() : this.findLastFocusedOptionIndex();\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n\n            !this.overlayVisible && this.show();\n        }\n        event.preventDefault();\n        event.stopPropagation();\n    }\n\n    onArrowLeftKey(event: KeyboardEvent, pressedInInputText: boolean = false) {\n        pressedInInputText && this.focusedOptionIndex.set(-1);\n    }\n\n    onDeleteKey(event: KeyboardEvent) {\n        if (this.showClear) {\n            this.clear(event);\n            event.preventDefault();\n        }\n    }\n\n    onHomeKey(event: any, pressedInInputText: boolean = false) {\n        if (pressedInInputText) {\n            const target = event.currentTarget;\n            if (event.shiftKey) {\n                target.setSelectionRange(0, target.value.length);\n            } else {\n                target.setSelectionRange(0, 0);\n                this.focusedOptionIndex.set(-1);\n            }\n        } else {\n            this.changeFocusedOptionIndex(event, this.findFirstOptionIndex());\n\n            !this.overlayVisible && this.show();\n        }\n\n        event.preventDefault();\n    }\n\n    onEndKey(event: any, pressedInInputText = false) {\n        if (pressedInInputText) {\n            const target = event.currentTarget;\n\n            if (event.shiftKey) {\n                target.setSelectionRange(0, target.value.length);\n            } else {\n                const len = target.value.length;\n\n                target.setSelectionRange(len, len);\n                this.focusedOptionIndex.set(-1);\n            }\n        } else {\n            this.changeFocusedOptionIndex(event, this.findLastOptionIndex());\n\n            !this.overlayVisible && this.show();\n        }\n\n        event.preventDefault();\n    }\n\n    onPageDownKey(event: KeyboardEvent) {\n        this.scrollInView(this.visibleOptions().length - 1);\n        event.preventDefault();\n    }\n\n    onPageUpKey(event: KeyboardEvent) {\n        this.scrollInView(0);\n        event.preventDefault();\n    }\n\n    onSpaceKey(event: KeyboardEvent, pressedInInputText: boolean = false) {\n        !this.editable && !pressedInInputText && this.onEnterKey(event);\n    }\n\n    onEnterKey(event, pressedInInput = false) {\n        if (!this.overlayVisible) {\n            this.focusedOptionIndex.set(-1);\n            this.onArrowDownKey(event);\n        } else {\n            if (this.focusedOptionIndex() !== -1) {\n                const option = this.visibleOptions()[this.focusedOptionIndex()];\n                this.onOptionSelect(event, option);\n            }\n\n            !pressedInInput && this.hide();\n        }\n\n        event.preventDefault();\n    }\n\n    onEscapeKey(event: KeyboardEvent) {\n        this.overlayVisible && this.hide(true);\n        event.preventDefault();\n    }\n\n    onTabKey(event, pressedInInputText = false) {\n        if (!pressedInInputText) {\n            if (this.overlayVisible && this.hasFocusableElements()) {\n                DomHandler.focus(event.shiftKey ? this.lastHiddenFocusableElementOnOverlay.nativeElement : this.firstHiddenFocusableElementOnOverlay.nativeElement);\n                event.preventDefault();\n            } else {\n                if (this.focusedOptionIndex() !== -1 && this.overlayVisible) {\n                    const option = this.visibleOptions()[this.focusedOptionIndex()];\n                    this.onOptionSelect(event, option);\n                }\n                this.overlayVisible && this.hide(this.filter);\n            }\n        }\n        event.stopPropagation();\n    }\n\n    onFirstHiddenFocus(event) {\n        const focusableEl = event.relatedTarget === this.focusInputViewChild?.nativeElement ? DomHandler.getFirstFocusableElement(this.overlayViewChild.el?.nativeElement, ':not(.p-hidden-focusable)') : this.focusInputViewChild?.nativeElement;\n        DomHandler.focus(focusableEl);\n    }\n\n    onLastHiddenFocus(event) {\n        const focusableEl =\n            event.relatedTarget === this.focusInputViewChild?.nativeElement\n                ? DomHandler.getLastFocusableElement(this.overlayViewChild?.overlayViewChild?.nativeElement, ':not([data-p-hidden-focusable=\"true\"])')\n                : this.focusInputViewChild?.nativeElement;\n\n        DomHandler.focus(focusableEl);\n    }\n\n    hasFocusableElements() {\n        return DomHandler.getFocusableElements(this.overlayViewChild.overlayViewChild.nativeElement, ':not([data-p-hidden-focusable=\"true\"]):not([class=\"p-dropdown-items-wrapper\"])').length > 0;\n    }\n\n    onBackspaceKey(event: KeyboardEvent, pressedInInputText = false) {\n        if (pressedInInputText) {\n            !this.overlayVisible && this.show();\n        }\n    }\n\n    searchFields() {\n        return this.filterBy?.split(',') || this.filterFields || [this.optionLabel];\n    }\n\n    searchOptions(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let optionIndex = -1;\n        let matched = false;\n\n        if (this.focusedOptionIndex() !== -1) {\n            optionIndex = this.visibleOptions()\n                .slice(this.focusedOptionIndex())\n                .findIndex((option) => this.isOptionMatched(option));\n            optionIndex =\n                optionIndex === -1\n                    ? this.visibleOptions()\n                          .slice(0, this.focusedOptionIndex())\n                          .findIndex((option) => this.isOptionMatched(option))\n                    : optionIndex + this.focusedOptionIndex();\n        } else {\n            optionIndex = this.visibleOptions().findIndex((option) => this.isOptionMatched(option));\n        }\n\n        if (optionIndex !== -1) {\n            matched = true;\n        }\n\n        if (optionIndex === -1 && this.focusedOptionIndex() === -1) {\n            optionIndex = this.findFirstFocusedOptionIndex();\n        }\n\n        if (optionIndex !== -1) {\n            this.changeFocusedOptionIndex(event, optionIndex);\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n\n    isOptionMatched(option) {\n        return this.isValidOption(option) && this.getOptionLabel(option).toString().toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale));\n    }\n\n    onFilterInputChange(event: Event | any): void {\n        let value: string = (event.target as HTMLInputElement).value;\n        this._filterValue.set(value);\n        this.focusedOptionIndex.set(-1);\n        this.onFilter.emit({ originalEvent: event, filter: this._filterValue() });\n        !this.virtualScrollerDisabled && this.scroller.scrollToIndex(0);\n        setTimeout(() => {\n            this.overlayViewChild.alignOverlay();\n        });\n        this.cd.markForCheck();\n    }\n\n    applyFocus(): void {\n        if (this.editable) DomHandler.findSingle(this.el.nativeElement, '.p-dropdown-label.p-inputtext').focus();\n        else DomHandler.focus(this.focusInputViewChild?.nativeElement);\n    }\n    /**\n     * Applies focus.\n     * @group Method\n     */\n    public focus(): void {\n        this.applyFocus();\n    }\n    /**\n     * Clears the model.\n     * @group Method\n     */\n    public clear(event?: Event) {\n        this.updateModel(null, event);\n        this.clearEditableLabel();\n        this.onModelTouched();\n        this.onChange.emit({ originalEvent: event, value: this.value });\n        this.onClear.emit(event);\n        this.resetFilter();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, OverlayModule, SharedModule, TooltipModule, RippleModule, ScrollerModule, AutoFocusModule, TimesIcon, ChevronDownIcon, SearchIcon, BlankIcon, CheckIcon],\n    exports: [Dropdown, OverlayModule, SharedModule, ScrollerModule],\n    declarations: [Dropdown, DropdownItem]\n})\nexport class DropdownModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAkDa,MAAA,uBAAuB,GAAQ;AACxC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,QAAQ,CAAC;AACvC,IAAA,KAAK,EAAE,IAAI;EACb;MAiCW,YAAY,CAAA;AACZ,IAAA,EAAE,CAAqB;AAEvB,IAAA,MAAM,CAAyB;AAEA,IAAA,QAAQ,CAAsB;AAE9B,IAAA,OAAO,CAAsB;AAE5D,IAAA,KAAK,CAAqB;AAEK,IAAA,QAAQ,CAAsB;AAE9B,IAAA,OAAO,CAAsB;AAE9B,IAAA,QAAQ,CAAqB;AAE3D,IAAA,YAAY,CAAqB;AAEjC,IAAA,WAAW,CAAqB;AAEhC,IAAA,QAAQ,CAA+B;AAER,IAAA,SAAS,CAAU;AAEjD,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEhD,IAAA,YAAY,GAAsB,IAAI,YAAY,EAAE,CAAC;AAE/D,IAAA,QAAQ,MAAK;AAEb,IAAA,aAAa,CAAC,KAAY,EAAA;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,kBAAkB,CAAC,KAAY,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACjC;uGArCQ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAKD,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAEhB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAIhB,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAEhB,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAEhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,CAQf,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CApD1B,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;KAwBT,EAo0D0J,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,SAAS,2EAAE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;2FA/zDtK,YAAY,EAAA,UAAA,EAAA,CAAA;kBA/BxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;AAwBT,IAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;8BAEY,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAEG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAEG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAEkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE5B,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAEG,YAAY,EAAA,CAAA;sBAArB,MAAM;;AAaX;;;AAGG;MA8OU,QAAQ,CAAA;AA4oBE,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAA8B,IAAA,IAAA,CAAA;AAAqB,IAAA,aAAA,CAAA;AAAqC,IAAA,MAAA,CAAA;AA3oB9J;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;IACM,YAAY,GAAW,OAAO,CAAC;AACxC;;;AAGG;AACqC,IAAA,MAAM,CAAsB;AACpE;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;IACoC,QAAQ,GAAuB,CAAC,CAAC;AACxE;;;AAGG;IACH,IAAa,WAAW,CAAC,GAAuB,EAAA;AAC5C,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9B;AACD,IAAA,IAAI,WAAW,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;KACzC;AACD;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;AACM,IAAA,YAAY,CAAoB;AACzC;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;IACqC,iBAAiB,GAAY,KAAK,CAAC;AAC3E;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;IACqC,OAAO,GAAwB,KAAK,CAAC;AAC7E;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACM,gBAAgB,GAAuB,OAAO,CAAC;AACxD;;;AAGG;IACM,mBAAmB,GAAW,OAAO,CAAC;AAC/C;;;;AAIG;IACqC,gBAAgB,GAAY,IAAI,CAAC;AACzE;;;AAGG;AACqC,IAAA,KAAK,CAAsB;AACnE;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;IACM,kBAAkB,GAAW,EAAE,CAAC;AACzC;;;AAGG;IACM,YAAY,GAAW,EAAE,CAAC;AACnC;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACoC,IAAA,qBAAqB,CAAqB;AACjF;;;AAGG;AACM,IAAA,oBAAoB,CAA8B;AAC3D;;;AAGG;AACM,IAAA,cAAc,CAA6B;AACpD;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACM,eAAe,GAAyG,UAAU,CAAC;AAC5I;;;AAGG;AACoC,IAAA,SAAS,CAAqB;AACrE;;;AAGG;IACM,OAAO,GAAW,EAAE,CAAC;AAC9B;;;AAGG;IACM,eAAe,GAAwC,OAAO,CAAC;AACxE;;;AAGG;IACM,oBAAoB,GAAW,UAAU,CAAC;AACnD;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;IACqC,YAAY,GAAY,KAAK,CAAC;AACtE;;;AAGG;IACqC,aAAa,GAAY,KAAK,CAAC;AACvE;;;AAGG;IACqC,eAAe,GAAY,IAAI,CAAC;AACxE;;;AAGG;IACqC,eAAe,GAAY,IAAI,CAAC;AACxE;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,SAA8B,EAAA;AACvC,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YAErB,IAAI,IAAI,CAAC,cAAc;gBAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AACxC,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAE,IAAI,CAAC,EAAc,CAAC,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC3B,SAAA;KACJ;AACD;;;;AAIG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,GAAuB,EAAA;AAChC,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACrB,QAAA,OAAO,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;KACpG;AACD,IAAA,SAAS,CAAqB;AAC9B;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAwB,EAAA;AACnC,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;KAC7G;AACD,IAAA,WAAW,CAAsB;AACjC;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAuB,EAAA;AAClC,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;KAC7G;AACD,IAAA,WAAW,CAAqB;AAChC;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACtC;IACD,IAAI,qBAAqB,CAAC,GAAuB,EAAA;AAC7C,QAAA,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;KACxH;AACD,IAAA,sBAAsB,CAAqB;AAC3C;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACtC;IACD,IAAI,qBAAqB,CAAC,GAAuB,EAAA;AAC7C,QAAA,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,sGAAsG,CAAC,CAAC;KACxH;AACD,IAAA,sBAAsB,CAAqB;AAC3C;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;KAC9B;IACD,IAAI,WAAW,CAAC,GAA8B,EAAA;QAC1C,UAAU,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/B,SAAC,CAAC,CAAC;KACN;AACD;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;AAChB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChC,QAAA,OAAO,OAAO,CAAC;KAClB;IACD,IAAI,OAAO,CAAC,GAAsB,EAAA;AAC9B,QAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE;AAC/C,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1B,SAAA;KACJ;AACD;;;;AAIG;AACO,IAAA,QAAQ,GAAsC,IAAI,YAAY,EAAuB,CAAC;AAChG;;;;AAIG;AACO,IAAA,QAAQ,GAAsC,IAAI,YAAY,EAAuB,CAAC;AAChG;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC7E;;;;AAIG;AACO,IAAA,MAAM,GAAiC,IAAI,YAAY,EAAkB,CAAC;AACpF;;;;AAIG;AACO,IAAA,MAAM,GAAiC,IAAI,YAAY,EAAkB,CAAC;AACpF;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,UAAU,GAAwC,IAAI,YAAY,EAAyB,CAAC;AAE9E,IAAA,kBAAkB,CAAuB;AAE5C,IAAA,eAAe,CAAuB;AAElC,IAAA,mBAAmB,CAAuB;AAEvC,IAAA,sBAAsB,CAAuB;AAErD,IAAA,cAAc,CAAuB;AAElC,IAAA,QAAQ,CAAqB;AAE9B,IAAA,gBAAgB,CAAoB;AAErB,IAAA,oCAAoC,CAAuB;AAE5D,IAAA,mCAAmC,CAAuB;AAE9D,IAAA,SAAS,CAAqC;AAE9E,IAAA,SAAS,CAAsB;AAE/B,IAAA,YAAY,CAA2B;AAEvC,IAAA,YAAY,CAA6B;AAEzC,IAAA,aAAa,CAA6B;AAE1C,IAAA,cAAc,CAA6B;AAE3C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,cAAc,CAA6B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,aAAa,CAA6B;AAE1C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,aAAa,CAAoC;AAEjD,IAAA,QAAQ,GAAG,MAAM,CAAoB,IAAI,CAAC,CAAC;AAE3C,IAAA,YAAY,GAAG,MAAM,CAAqB,SAAS,CAAC,CAAC;AAErD,IAAA,UAAU,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAE/B,IAAA,KAAK,CAAM;AAEX,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,KAAK,CAAoB;AAEzB,IAAA,OAAO,CAAoB;AAE3B,IAAA,cAAc,CAAoB;AAElC,IAAA,cAAc,CAAoB;AAElC,IAAA,KAAK,CAA2B;AAEhC,IAAA,iBAAiB,CAAoB;AAErC,IAAA,WAAW,CAAM;AAEjB,IAAA,qBAAqB,CAAoB;AAEzC,IAAA,YAAY,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAEjC,IAAA,WAAW,CAAmB;AAE9B,IAAA,WAAW,CAAmB;AAE9B,IAAA,aAAa,CAAM;AAEnB,IAAA,kBAAkB,CAAmB;AAErC,IAAA,iBAAiB,CAAmB;AAEpC,IAAA,mBAAmB,CAAoB;AAEvC,IAAA,kBAAkB,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC,CAAC;AAExC,IAAA,OAAO,CAAmB;AAE1B,IAAA,MAAM,CAAmB;AAEzB,IAAA,OAAO,GAAG,MAAM,CAAU,KAAK,CAAC,CAAC;AAEjC,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;KACzF;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;KACtG;AAED,IAAA,IAAI,kBAAkB,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;KACpG;AAED,IAAA,IAAI,SAAS,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC;KACxE;AAED,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;AACH,YAAA,uCAAuC,EAAE,IAAI;YAC7C,YAAY,EAAE,IAAI,CAAC,QAAQ;YAC3B,sBAAsB,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACxD,SAAS,EAAE,IAAI,CAAC,OAAO;YACvB,uBAAuB,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM;AACnH,YAAA,sBAAsB,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc;AAC3D,YAAA,kBAAkB,EAAE,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ;YACtF,iBAAiB,EAAE,IAAI,CAAC,cAAc;SACzC,CAAC;KACL;AAED,IAAA,IAAI,UAAU,GAAA;AACV,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,OAAO;AACH,YAAA,8BAA8B,EAAE,IAAI;YACpC,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE;AACnE,YAAA,wBAAwB,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,oBAAoB,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,cAAc,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;SACtK,CAAC;KACL;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,8BAA8B,EAAE,IAAI;YACpC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,QAAQ;AACvD,YAAA,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,KAAK;SACpD,CAAC;KACL;AAED,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,GAAG,IAAI,CAAC;KAC9F;AAED,IAAA,cAAc,GAAG,QAAQ,CAAC,MAAK;AAC3B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iCAAiC,EAAE,CAAC;AAEzD,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACrB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC;AAEpD,YAAA,MAAM,eAAe,GACjB,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW;kBAC/C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAAI;oBAC3B,IAAI,MAAM,CAAC,KAAK,EAAE;wBACd,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACzG,qBAAA;oBACD,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACpG,iBAAC,CAAC;AACJ,kBAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEvI,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;gBACxC,MAAM,QAAQ,GAAG,EAAE,CAAC;AAEpB,gBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;oBAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AACzD,oBAAA,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAErF,oBAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC;AAAE,wBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,OAAO,IAAI,CAAC,mBAAmB,KAAK,QAAQ,GAAG,IAAI,CAAC,mBAAmB,GAAG,OAAO,GAAG,CAAC,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;AACvK,iBAAC,CAAC,CAAC;AAEH,gBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACrC,aAAA;AACD,YAAA,OAAO,eAAe,CAAC;AAC1B,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;AACnB,KAAC,CAAC,CAAC;AAEH,IAAA,KAAK,GAAG,QAAQ,CAAC,MAAK;;;AAGlB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iCAAiC,EAAE,CAAC;;AAEzD,QAAA,MAAM,mBAAmB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC,CAAC;QAEtG,OAAO,mBAAmB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,cAAc,CAAC;AACjI,KAAC,CAAC,CAAC;AAEH,IAAA,MAAM,GAAG,QAAQ,CAAC,MAAK;AACnB,QAAA,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,QAAQ;AAAE,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACtE,OAAO,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC;AAC5G,KAAC,CAAC,CAAC;AAEH,IAAA,cAAc,CAAM;IAEpB,kBAAkB,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IAEzG,WAAmB,CAAA,EAAc,EAAS,QAAmB,EAAS,EAAqB,EAAS,IAAY,EAAS,aAA4B,EAAS,MAAqB,EAAA;QAAhK,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAS,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAC/K,MAAM,CAAC,MAAK;AACR,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACrC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAE7C,IAAI,cAAc,IAAI,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;AAC1D,gBAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAE3D,gBAAA,IAAI,mBAAmB,KAAK,CAAC,CAAC,IAAI,UAAU,KAAK,SAAS,KAAK,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;AACrK,oBAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,CAAC;AAC7D,iBAAA;AACJ,aAAA;YAED,IAAI,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;AAC/I,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,aAAA;AAED,YAAA,IAAI,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC3C,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC9B,aAAA;AACD,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAEO,kBAAkB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACjG;IAEO,iCAAiC,GAAA;QACrC,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;KAC3E;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;QACzC,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,aAAa,GAAG;gBACjB,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;AAClD,gBAAA,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;aAClC,CAAC;AACL,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,EAAE;AAC5C,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAE5B,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,UAAU,CAAC,MAAK;oBACZ,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACvB,wBAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;AACxC,qBAAA;iBACJ,EAAE,CAAC,CAAC,CAAC;AACV,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,YAAY,EAAE;AACjD,YAAA,IAAI,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACnH,YAAA,IAAI,YAAY,EAAE;gBACd,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AAC5D,aAAA;AACD,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACtC,SAAA;KACJ;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,WAAW,CAAC,OAAO,EAAA;AACf,QAAA,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,KAAI;AACpD,YAAA,MAAM,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAEzD,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;AAEhE,YAAA,mBAAmB,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAE1E,YAAA,OAAO,MAAM,CAAC;SACjB,EAAE,EAAE,CAAC,CAAC;KACV;IAED,eAAe,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;YACzE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAC;AAChE,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACtF,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,EAAE;AAC1F,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AACrB,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AACxC,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACtE,aAAA;AACJ,SAAA;KACJ;IAED,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,aAAa,GAAG,KAAK,EAAA;AAC9D,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;AAC5D,YAAA,aAAa,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACzF,SAAA;AACD,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnB,SAAA;KACJ;IAED,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAA;QAC3B,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/C,SAAA;KACJ;IAED,WAAW,CAAC,KAAK,EAAE,KAAM,EAAA;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;KACrC;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,gBAAgB,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;KACnL;IAED,qBAAqB,GAAA;QACjB,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACnD;AAED,IAAA,UAAU,CAAC,MAAM,EAAA;AACb,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;KACnF;AAEO,IAAA,6BAA6B,CAAC,MAAW,EAAA;QAC7C,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KACjG;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC9B,SAAA;QACD,IAAI,CAAC,iCAAiC,EAAE,CAAC;KAC5C;IAED,iCAAiC,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,SAAS,EAAE;;YAEnE,OAAO;AACV,SAAA;QACD,MAAM,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC;QAC1D,MAAM,iBAAiB,GAAG,aAAa,EAAE,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAC7E,IAAI,aAAa,IAAI,iBAAiB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAC5D,MAAM,KAAK,GAAG,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACnD,YAAA,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC5C,aAAA;AACJ,SAAA;KACJ;IAED,mBAAmB,GAAA;QACf,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC;AACzH,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AACxD,SAAA;KACJ;IAED,cAAc,CAAC,KAAK,EAAE,eAAe,EAAA;QACjC,OAAO,IAAI,CAAC,uBAAuB,GAAG,KAAK,GAAG,eAAe,IAAI,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;KACnH;AAED,IAAA,cAAc,CAAC,MAAW,EAAA;QACtB,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;KAC9L;AAED,IAAA,cAAc,CAAC,MAAW,EAAA;QACtB,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;KACrM;AAED,IAAA,gBAAgB,CAAC,MAAW,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,KAAK,KAAK,CAAC,EAAE;AACjL,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,GAAG,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9J,SAAA;KACJ;AAED,IAAA,mBAAmB,CAAC,WAAgB,EAAA;QAChC,OAAO,IAAI,CAAC,gBAAgB,KAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,WAAW,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC;KACtO;AAED,IAAA,sBAAsB,CAAC,WAAgB,EAAA;AACnC,QAAA,OAAO,IAAI,CAAC,mBAAmB,KAAK,SAAS,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;KAChL;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;AACjB,QAAA,QACI,CAAC,IAAI,CAAC,gBAAgB;AAClB,cAAE,KAAK;gBACL,IAAI,CAAC,cAAc,EAAE;AAChB,qBAAA,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AACf,qBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;AAC5D,cAAE,KAAK,IAAI,CAAC,EAClB;KACL;AAED,IAAA,IAAI,WAAW,GAAA;QACX,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;KACvF;AAED;;;AAGG;IACI,WAAW,GAAA;AACd,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;YAC5D,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AACjD,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;YAChD,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;QAEvE,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,KAAK,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,EAAE;YAC3J,OAAO;AACV,SAAA;aAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACjG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3D,SAAA;AACD,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,OAAO,GAAA;QACH,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;KAC5F;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;AAChC,QAAA,MAAM,KAAK,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;AACvD,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAE5C,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC/B,UAAU,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;SAC9D,EAAE,CAAC,CAAC,CAAC;AAEN,QAAA,CAAC,IAAI,CAAC,cAAc,IAAI,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;KACxE;AACD;;;AAGG;AACI,IAAA,IAAI,CAAC,OAAQ,EAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC1M,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAEhD,QAAA,IAAI,OAAO,EAAE;YACT,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;AAC7D,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,uBAAuB,CAAC,KAAqB,EAAA;AACzC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;YAC7B,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,GAAG,aAAa,GAAG,2BAA2B,CAAC,CAAC;AACpK,YAAA,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;YAEtF,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACrC,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,oBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC;AACzE,oBAAA,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;AACtB,wBAAA,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC,CAAC;AAC/C,qBAAA;AACJ,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,8BAA8B,CAAC,CAAC;AAEhG,oBAAA,IAAI,gBAAgB,EAAE;AAClB,wBAAA,gBAAgB,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC5E,qBAAA;AACJ,iBAAA;AACJ,aAAA;YAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;AAC5D,gBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAEhC,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACxC,oBAAA,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC9C,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;AACD,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;AAC1B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;KACJ;AACD;;;AAGG;AACI,IAAA,IAAI,CAAC,OAAQ,EAAA;AAChB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAEtB,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,KAAK,OAAO,EAAE;YACvC,UAAU,CAAC,iBAAiB,EAAE,CAAC;AAClC,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACvC,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;AACD,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,UAAU,CAAC,MAAK;oBACZ,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;AAC9D,iBAAC,CAAC,CAAC;AACN,aAAA;AACD,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC9C,UAAU,CAAC,MAAK;oBACZ,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,CAAC,CAAC;AACjE,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAY,EAAA;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE;;YAEf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,CAAC;AAChL,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAEpE,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAExB,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,SAAA;AACD,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;KACpC;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;QACzB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,SAAS,CAAC,KAAoB,EAAE,MAAe,EAAA;QAC3C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE;YAChD,OAAO;AACV,SAAA;QAED,QAAQ,KAAK,CAAC,IAAI;;AAEd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;;AAGV,YAAA,KAAK,SAAS;gBACV,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACxC,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;gBACb,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1C,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrC,MAAM;AAEV,YAAA,KAAK,KAAK;gBACN,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACpC,MAAM;AAEV,YAAA,KAAK,UAAU;AACX,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;;AAGV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC/B,MAAM;;AAGV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;;AAGV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,WAAW;gBACZ,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1C,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;;gBAEb,MAAM;AAEV,YAAA;AACI,gBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBAC/D,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACpC,oBAAA,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1D,iBAAA;gBAED,MAAM;AACb,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC/B,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC7B,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;QACd,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACnC;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,YAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;AACzF,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAE/L,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,SAAA;;;;QAKD,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;IAED,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;YAEpB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC7C,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;KAC9B;AAED,IAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;QACnB,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,eAAe,CAAC;QAEvE,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;AAC1D,YAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAA,OAAA,EAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAC3F,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,aAAA;AAAM,iBAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACtC,UAAU,CAAC,MAAK;oBACZ,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;iBACxG,EAAE,CAAC,CAAC,CAAC;AACT,aAAA;AACJ,SAAA;KACJ;IAED,iBAAiB,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;KAC1C;AAED,IAAA,qBAAqB,CAAC,MAAM,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;KAChE;IAED,WAAW,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;KACjD;IAED,2BAA2B,GAAA;AACvB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;AACrD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,aAAa,CAAC;KAC1E;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KAClF;IAED,uBAAuB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1H;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;QACrB,MAAM,kBAAkB,GACpB,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC;AACpC,cAAE,IAAI,CAAC,cAAc,EAAE;AAChB,iBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChB,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC;AACb,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KAC3E;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAErJ,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,CAAC;KAC/D;IAED,mBAAmB,GAAA;QACf,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KACnG;IAED,0BAA0B,GAAA;AACtB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAErD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,aAAa,CAAC;KACzE;AAED,IAAA,aAAa,CAAC,MAAM,EAAA;QAChB,OAAO,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KACpH;AAED,IAAA,aAAa,CAAC,MAAM,EAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB,KAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,IAAI,MAAM,CAAC,WAAW,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC;KACnK;AAED,IAAA,YAAY,CAAC,KAAoB,EAAE,kBAAA,GAA8B,KAAK,EAAA;AAClE,QAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,kBAAkB,EAAE;AACrC,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAChE,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtC,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACtC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAE7L,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YAElD,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACvC,SAAA;QACD,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;AAED,IAAA,cAAc,CAAC,KAAoB,EAAE,kBAAA,GAA8B,KAAK,EAAA;QACpE,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACzD;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;QAC5B,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAClB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAU,EAAE,kBAAA,GAA8B,KAAK,EAAA;AACrD,QAAA,IAAI,kBAAkB,EAAE;AACpB,YAAA,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC;YACnC,IAAI,KAAK,CAAC,QAAQ,EAAE;gBAChB,MAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACpD,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;YAElE,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACvC,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAU,EAAE,kBAAkB,GAAG,KAAK,EAAA;AAC3C,QAAA,IAAI,kBAAkB,EAAE;AACpB,YAAA,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC;YAEnC,IAAI,KAAK,CAAC,QAAQ,EAAE;gBAChB,MAAM,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACpD,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAEhC,gBAAA,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBACnC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAEjE,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACvC,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,aAAa,CAAC,KAAoB,EAAA;AAC9B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;AAC5B,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAoB,EAAE,kBAAA,GAA8B,KAAK,EAAA;AAChE,QAAA,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KACnE;AAED,IAAA,UAAU,CAAC,KAAK,EAAE,cAAc,GAAG,KAAK,EAAA;AACpC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAChE,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtC,aAAA;AAED,YAAA,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AAClC,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;QAC5B,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAE,kBAAkB,GAAG,KAAK,EAAA;QACtC,IAAI,CAAC,kBAAkB,EAAE;YACrB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;gBACpD,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,mCAAmC,CAAC,aAAa,GAAG,IAAI,CAAC,oCAAoC,CAAC,aAAa,CAAC,CAAC;gBACpJ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,aAAA;AAAM,iBAAA;gBACH,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;AACzD,oBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAChE,oBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtC,iBAAA;gBACD,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;QACD,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;AAED,IAAA,kBAAkB,CAAC,KAAK,EAAA;AACpB,QAAA,MAAM,WAAW,GAAG,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,mBAAmB,EAAE,aAAa,GAAG,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,aAAa,EAAE,2BAA2B,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC;AAC1O,QAAA,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACjC;AAED,IAAA,iBAAiB,CAAC,KAAK,EAAA;QACnB,MAAM,WAAW,GACb,KAAK,CAAC,aAAa,KAAK,IAAI,CAAC,mBAAmB,EAAE,aAAa;AAC3D,cAAE,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,wCAAwC,CAAC;AACtI,cAAE,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC;AAElD,QAAA,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACjC;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,EAAE,gFAAgF,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KAC7L;AAED,IAAA,cAAc,CAAC,KAAoB,EAAE,kBAAkB,GAAG,KAAK,EAAA;AAC3D,QAAA,IAAI,kBAAkB,EAAE;YACpB,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACvC,SAAA;KACJ;IAED,YAAY,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAC/E;IAED,aAAa,CAAC,KAAK,EAAE,IAAI,EAAA;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;AAEnD,QAAA,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;QACrB,IAAI,OAAO,GAAG,KAAK,CAAC;AAEpB,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,YAAA,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE;AAC9B,iBAAA,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAChC,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;YACzD,WAAW;gBACP,WAAW,KAAK,CAAC,CAAC;AACd,sBAAE,IAAI,CAAC,cAAc,EAAE;AAChB,yBAAA,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACnC,yBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC1D,sBAAE,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACrD,SAAA;AAAM,aAAA;YACH,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3F,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;YACpB,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AACxD,YAAA,WAAW,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACpD,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;AACpB,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,eAAe,CAAC,MAAM,EAAA;AAClB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KACtL;AAED,IAAA,mBAAmB,CAAC,KAAkB,EAAA;AAClC,QAAA,IAAI,KAAK,GAAY,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;AAC7D,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAC1E,QAAA,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAChE,UAAU,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,UAAU,GAAA;QACN,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,+BAA+B,CAAC,CAAC,KAAK,EAAE,CAAC;;YACpG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;KAClE;AACD;;;AAGG;IACI,KAAK,GAAA;QACR,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB;AACD;;;AAGG;AACI,IAAA,KAAK,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AAChE,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,WAAW,EAAE,CAAC;KACtB;uGAliDQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAR,QAAQ,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAeG,gBAAgB,CA8BhB,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,sCAKhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAUhB,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,uRAuDf,gBAAgB,CAAA,EAAA,iBAAA,EAAA,CAAA,mBAAA,EAAA,mBAAA,EAKhB,gBAAgB,CAKhB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,iEAUhB,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EA+BhB,gBAAgB,CAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAKhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,gBAAgB,CAehB,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,qDAKhB,gBAAgB,CAAA,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAKhB,eAAe,CAmCf,EAAA,oBAAA,EAAA,sBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,eAAe,gMAyBf,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAKhB,gBAAgB,CAKhB,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAAA,gBAAgB,2DAKhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,2BAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,SAAA,EA9RzB,CAAC,uBAAuB,CAAC,EAqdnB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA3rBpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,wBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,sCAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,wBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,qCAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,uBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+NT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,iiCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,EAAA,cAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAkjDkH,SAAS,CAAE,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,CAAE,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,UAAU,4EA/zDhJ,YAAY,CAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,OAAA,EAAA,UAAA,EAAA,SAAA,EAAA,UAAA,EAAA,cAAA,EAAA,aAAA,EAAA,UAAA,EAAA,WAAA,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,cAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAyRZ,QAAQ,EAAA,UAAA,EAAA,CAAA;kBA7OpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+NT,EAEK,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACjC,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,8BAA8B,EAAE,2BAA2B;qBAC9D,EACU,SAAA,EAAA,CAAC,uBAAuB,CAAC,EACnB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,iiCAAA,CAAA,EAAA,CAAA;oNAQ5B,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKxB,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAUG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAMkC,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,qBAAqB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKiC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAoBO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAaO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAaO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAaO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAaO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAYO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAYO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAcI,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAEiB,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAED,eAAe,EAAA,CAAA;sBAAnC,SAAS;uBAAC,QAAQ,CAAA;gBAEM,mBAAmB,EAAA,CAAA;sBAA3C,SAAS;uBAAC,YAAY,CAAA;gBAEK,sBAAsB,EAAA,CAAA;sBAAjD,SAAS;uBAAC,eAAe,CAAA;gBAEN,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBAEK,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;gBAEC,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEiB,oCAAoC,EAAA,CAAA;sBAAxE,SAAS;uBAAC,wBAAwB,CAAA;gBAEC,mCAAmC,EAAA,CAAA;sBAAtE,SAAS;uBAAC,uBAAuB,CAAA;gBAEF,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA0lCrB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EA1iDd,YAAA,EAAA,CAAA,QAAQ,EAzRR,YAAY,aA+zDX,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAA,EAAA,OAAA,EAAA,CAtiDtK,QAAQ,EAuiDG,aAAa,EAAE,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;AAGtD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAJb,OAAA,EAAA,CAAA,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAC3J,aAAa,EAAE,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;;2FAGtD,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC;oBAChL,OAAO,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,CAAC;AAChE,oBAAA,YAAY,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;AACzC,iBAAA,CAAA;;;ACz5DD;;AAEG;;;;"}