/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createSignal, SIGNAL, signalSetFn, signalUpdateFn, } from '@angular/core/primitives/signals';
import { performanceMarkFeature } from '../../util/performance';
import { isSignal } from './api';
/** Symbol used distinguish `WritableSignal` from other non-writable signals and functions. */
export const ɵWRITABLE_SIGNAL = /* @__PURE__ */ Symbol('WRITABLE_SIGNAL');
/**
 * Utility function used during template type checking to extract the value from a `WritableSignal`.
 * @codeGenApi
 */
export function ɵunwrapWritableSignal(value) {
    // Note: the function uses `WRITABLE_SIGNAL` as a brand instead of `WritableSignal<T>`,
    // because the latter incorrectly unwraps non-signal getter functions.
    return null;
}
/**
 * Create a `Signal` that can be set or updated directly.
 */
export function signal(initialValue, options) {
    performanceMarkFeature('NgSignals');
    const signalFn = createSignal(initialValue);
    const node = signalFn[SIGNAL];
    if (options?.equal) {
        node.equal = options.equal;
    }
    signalFn.set = (newValue) => signalSetFn(node, newValue);
    signalFn.update = (updateFn) => signalUpdateFn(node, updateFn);
    signalFn.asReadonly = signalAsReadonlyFn.bind(signalFn);
    if (ngDevMode) {
        signalFn.toString = () => `[Signal: ${signalFn()}]`;
    }
    return signalFn;
}
export function signalAsReadonlyFn() {
    const node = this[SIGNAL];
    if (node.readonlyFn === undefined) {
        const readonlyFn = () => this();
        readonlyFn[SIGNAL] = node;
        node.readonlyFn = readonlyFn;
    }
    return node.readonlyFn;
}
/**
 * Checks if the given `value` is a writeable signal.
 */
export function isWritableSignal(value) {
    return isSignal(value) && typeof value.set === 'function';
}
//# sourceMappingURL=data:application/json;base64,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