import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { WalletRoutingModule } from './wallet-routing.module';
import { WalletHomeComponent } from './wallet-home/wallet-home.component';
import { TranslocoModule } from '@jsverse/transloco';
import { SharedModule } from '../shared/shared.module';
import { WalletTransactionsComponent } from './wallet-transactions/wallet-transactions.component';
import { TableSkeletonComponent } from "../shared/skeletons/table/table.component";
import { ReactiveFormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    WalletHomeComponent,
    WalletTransactionsComponent,
  ],
  imports: [
    CommonModule,
    WalletRoutingModule,
    TranslocoModule,
    SharedModule,
    TableSkeletonComponent,
    ReactiveFormsModule,
]
})
export class WalletModule { }
