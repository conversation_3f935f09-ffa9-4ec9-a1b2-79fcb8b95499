import { inject, Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { LocalStorageService } from '../../auth-services/services/local-storage.service';

@Injectable({
  providedIn: 'root',
})
export class HttpService {
  private lang!: string;
  private http = inject(HttpClient);
  private localStorageService = inject(LocalStorageService);
  private get apiUrl(): string {
    return environment.apiUrl;
  }


  constructor() {
    // get selected lang
    this.lang = this.localStorageService.getLanguage();
  }

  private getHeaders(options?: any): HttpHeaders {
    let headers = new HttpHeaders({
      'Accept-Language': this.lang,
    });

    const token = this.localStorageService.getToken();
    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    if (options && options.headers) {
      for (const key of Object.keys(options.headers)) {
        headers = headers.set(key, options.headers[key]);
      }
    }

    return headers;
  }

  private addHeaders(options?: any): any {
    if (!options) {
      options = {};
    }

    options.headers = this.getHeaders(options);
    return options;
  }

  private getUrl(endpoint: string): string {
    return `${this.apiUrl}/${endpoint}`;
  }

  get(endpoint: string, options?: any): Observable<any> {
    options = this.addHeaders(options);
    return this.http.get(this.getUrl(endpoint), options);
  }

  post(endpoint: string, data: any, options?: any): Observable<any> {
    options = this.addHeaders(options);
    return this.http.post(this.getUrl(endpoint), data, options);
  }

  put(endpoint: string, data: any, options?: any): Observable<any> {
    options = this.addHeaders(options);
    return this.http.put(this.getUrl(endpoint), data, options);
  }

  delete(endpoint: string, options?: any): Observable<any> {
    options = this.addHeaders(options);
    return this.http.delete(this.getUrl(endpoint), options);
  }

}

