import { inject, Injectable } from "@angular/core";
import { HttpService } from "../../common-services/services/http.service";
import { LocalStorageService } from "../services/local-storage.service";
import { loginDTO } from "./auth";
import { jwtDecode } from "jwt-decode";
import { EncryptionService } from "../../common-services/services/encryption.service";
import { tap } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class AuthApi {
  // class deal as a helper for authService
  private readonly endpoint: string = 'auth';
  private api = inject(HttpService);
  private encryption = inject(EncryptionService);
  private localStorageService = inject(LocalStorageService);

  login(loginDTO: loginDTO) {
    const payload = this.encryption.generatePayload(loginDTO);
    return this.api.post(`${this.endpoint}/login`, { payload }, { responseType: "json" })
      .pipe(
        tap((response: any) => {
          // Check if the response has a token
          if (response && response.token) {
            const token = response.token;
            // update local storage state with the new token
            this.localStorageService.setToken(token);
          } else {
            console.error('Token not found in the response');
          }
        })
      );
  }

  getAuthId(): string | null {
    if (!this.isAuthenticated()) {
      return null;
    }

    const token = this.localStorageService.getToken();
    try {
      // check if the token is expire
      const decodedToken: any = jwtDecode(token);
      return decodedToken.sub;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  isAuthenticated(){
    const token = this.localStorageService.getToken();
    if (!token || token == undefined) {
      return false;
    }

    try {
      // check if the token is expire
      const decodedToken: any = jwtDecode(token);
      const currentTime = Math.floor(Date.now() / 1000);
      return decodedToken.exp > currentTime;
    } catch (error) {
      console.error('Error decoding token:', error);
      return false;
    }
  }

}
