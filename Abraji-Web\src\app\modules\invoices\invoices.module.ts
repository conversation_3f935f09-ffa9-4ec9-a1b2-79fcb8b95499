import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { InvoicesRoutingModule } from './invoices-routing.module';
import { AllInvoicesComponent } from './all-invoices/all-invoices.component';
import { InvoiceDetailsComponent } from './invoice-details/invoice-details.component';
import { TranslocoModule } from '@jsverse/transloco';

@NgModule({
  declarations: [AllInvoicesComponent, InvoiceDetailsComponent],
  imports: [CommonModule, InvoicesRoutingModule, TranslocoModule],
})
export class InvoicesModule {}
