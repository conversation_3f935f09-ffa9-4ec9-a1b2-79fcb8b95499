{"version": 3, "file": "primeng-overlay.mjs", "sources": ["../../src/app/components/overlay/overlay.ts", "../../src/app/components/overlay/primeng-overlay.ts"], "sourcesContent": ["import { animate, animation, AnimationEvent, style, transition, trigger, useAnimation } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    forwardRef,\n    Inject,\n    Input,\n    NgModule,\n    NgZone,\n    OnDestroy,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation\n} from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { OverlayModeType, OverlayOnBeforeHideEvent, OverlayOnBeforeShowEvent, OverlayOnHideEvent, OverlayOnShowEvent, OverlayOptions, OverlayService, PrimeNGConfig, PrimeTemplate, ResponsiveOverlayOptions, SharedModule } from 'primeng/api';\nimport { ConnectedOverlaySc<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { ObjectUtils, ZIndexUtils } from 'primeng/utils';\nimport { VoidListener } from 'primeng/ts-helpers';\n\nexport const OVERLAY_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Overlay),\n    multi: true\n};\n\nconst showOverlayContentAnimation = animation([style({ transform: '{{transform}}', opacity: 0 }), animate('{{showTransitionParams}}')]);\n\nconst hideOverlayContentAnimation = animation([animate('{{hideTransitionParams}}', style({ transform: '{{transform}}', opacity: 0 }))]);\n/**\n * This API allows overlay components to be controlled from the PrimeNGConfig. In this way, all overlay components in the application can have the same behavior.\n * @group Components\n */\n@Component({\n    selector: 'p-overlay',\n    template: `\n        <div\n            *ngIf=\"modalVisible\"\n            #overlay\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [ngClass]=\"{\n                'p-overlay p-component': true,\n                'p-overlay-modal p-component-overlay p-component-overlay-enter': modal,\n                'p-overlay-center': modal && overlayResponsiveDirection === 'center',\n                'p-overlay-top': modal && overlayResponsiveDirection === 'top',\n                'p-overlay-top-start': modal && overlayResponsiveDirection === 'top-start',\n                'p-overlay-top-end': modal && overlayResponsiveDirection === 'top-end',\n                'p-overlay-bottom': modal && overlayResponsiveDirection === 'bottom',\n                'p-overlay-bottom-start': modal && overlayResponsiveDirection === 'bottom-start',\n                'p-overlay-bottom-end': modal && overlayResponsiveDirection === 'bottom-end',\n                'p-overlay-left': modal && overlayResponsiveDirection === 'left',\n                'p-overlay-left-start': modal && overlayResponsiveDirection === 'left-start',\n                'p-overlay-left-end': modal && overlayResponsiveDirection === 'left-end',\n                'p-overlay-right': modal && overlayResponsiveDirection === 'right',\n                'p-overlay-right-start': modal && overlayResponsiveDirection === 'right-start',\n                'p-overlay-right-end': modal && overlayResponsiveDirection === 'right-end'\n            }\"\n            (click)=\"onOverlayClick()\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #content\n                [ngStyle]=\"contentStyle\"\n                [class]=\"contentStyleClass\"\n                [ngClass]=\"'p-overlay-content'\"\n                (click)=\"onOverlayContentClick($event)\"\n                [@overlayContentAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions, transform: transformOptions[modal ? overlayResponsiveDirection : 'default'] } }\"\n                (@overlayContentAnimation.start)=\"onOverlayContentAnimationStart($event)\"\n                (@overlayContentAnimation.done)=\"onOverlayContentAnimationDone($event)\"\n            >\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: { mode: overlayMode } }\"></ng-container>\n            </div>\n        </div>\n    `,\n    animations: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    providers: [OVERLAY_VALUE_ACCESSOR],\n    styleUrls: ['./overlay.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Overlay implements AfterContentInit, OnDestroy {\n    /**\n     * The visible property is an input that determines the visibility of the component.\n     * @defaultValue false\n     * @group Props\n     */\n    @Input() get visible(): boolean {\n        return this._visible;\n    }\n    set visible(value: boolean) {\n        this._visible = value;\n\n        if (this._visible && !this.modalVisible) {\n            this.modalVisible = true;\n        }\n    }\n    /**\n     * The mode property is an input that determines the overlay mode type or string.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get mode(): OverlayModeType | string {\n        return this._mode || this.overlayOptions?.mode;\n    }\n    set mode(value: OverlayModeType | string) {\n        this._mode = value;\n    }\n    /**\n     * The style property is an input that determines the style object for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get style(): { [klass: string]: any } | null | undefined {\n        return ObjectUtils.merge(this._style, this.modal ? this.overlayResponsiveOptions?.style : this.overlayOptions?.style);\n    }\n    set style(value: { [klass: string]: any } | null | undefined) {\n        this._style = value;\n    }\n    /**\n     * The styleClass property is an input that determines the CSS class(es) for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get styleClass(): string {\n        return ObjectUtils.merge(this._styleClass, this.modal ? this.overlayResponsiveOptions?.styleClass : this.overlayOptions?.styleClass);\n    }\n    set styleClass(value: string) {\n        this._styleClass = value;\n    }\n    /**\n     * The contentStyle property is an input that determines the style object for the content of the component.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get contentStyle(): { [klass: string]: any } | null | undefined {\n        return ObjectUtils.merge(this._contentStyle, this.modal ? this.overlayResponsiveOptions?.contentStyle : this.overlayOptions?.contentStyle);\n    }\n    set contentStyle(value: { [klass: string]: any } | null | undefined) {\n        this._contentStyle = value;\n    }\n    /**\n     * The contentStyleClass property is an input that determines the CSS class(es) for the content of the component.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get contentStyleClass(): string {\n        return ObjectUtils.merge(this._contentStyleClass, this.modal ? this.overlayResponsiveOptions?.contentStyleClass : this.overlayOptions?.contentStyleClass);\n    }\n    set contentStyleClass(value: string) {\n        this._contentStyleClass = value;\n    }\n    /**\n     * The target property is an input that specifies the target element or selector for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get target(): string | null | undefined {\n        const value = this._target || this.overlayOptions?.target;\n        return value === undefined ? '@prev' : value;\n    }\n    set target(value: string | null | undefined) {\n        this._target = value;\n    }\n    /**\n     * Overlay can be mounted into its location, body or DOM element instance using this option.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get appendTo(): 'body' | HTMLElement | undefined {\n        return this._appendTo || this.overlayOptions?.appendTo;\n    }\n    set appendTo(value: 'body' | HTMLElement | undefined) {\n        this._appendTo = value;\n    }\n    /**\n     * The autoZIndex determines whether to automatically manage layering. Its default value is 'false'.\n     * @defaultValue false\n     * @group Props\n     */\n    @Input() get autoZIndex(): boolean {\n        const value = this._autoZIndex || this.overlayOptions?.autoZIndex;\n        return value === undefined ? true : value;\n    }\n    set autoZIndex(value: boolean) {\n        this._autoZIndex = value;\n    }\n    /**\n     * The baseZIndex is base zIndex value to use in layering.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get baseZIndex(): number {\n        const value = this._baseZIndex || this.overlayOptions?.baseZIndex;\n        return value === undefined ? 0 : value;\n    }\n    set baseZIndex(value: number) {\n        this._baseZIndex = value;\n    }\n    /**\n     * Transition options of the show or hide animation.\n     * @defaultValue .12s cubic-bezier(0, 0, 0.2, 1)\n     * @group Props\n     */\n    @Input() get showTransitionOptions(): string {\n        const value = this._showTransitionOptions || this.overlayOptions?.showTransitionOptions;\n        return value === undefined ? '.12s cubic-bezier(0, 0, 0.2, 1)' : value;\n    }\n    set showTransitionOptions(value: string) {\n        this._showTransitionOptions = value;\n    }\n    /**\n     * The hideTransitionOptions property is an input that determines the CSS transition options for hiding the component.\n     * @defaultValue .1s linear\n     * @group Props\n     */\n    @Input() get hideTransitionOptions(): string {\n        const value = this._hideTransitionOptions || this.overlayOptions?.hideTransitionOptions;\n        return value === undefined ? '.1s linear' : value;\n    }\n    set hideTransitionOptions(value: string) {\n        this._hideTransitionOptions = value;\n    }\n    /**\n     * The listener property is an input that specifies the listener object for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get listener(): any {\n        return this._listener || this.overlayOptions?.listener;\n    }\n    set listener(value: any) {\n        this._listener = value;\n    }\n    /**\n     * It is the option used to determine in which mode it should appear according to the given media or breakpoint.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get responsive(): ResponsiveOverlayOptions | undefined {\n        return this._responsive || this.overlayOptions?.responsive;\n    }\n    set responsive(val: ResponsiveOverlayOptions | undefined) {\n        this._responsive = val;\n    }\n    /**\n     * The options property is an input that specifies the overlay options for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get options(): OverlayOptions | undefined {\n        return this._options;\n    }\n    set options(val: OverlayOptions | undefined) {\n        this._options = val;\n    }\n    /**\n     * This EventEmitter is used to notify changes in the visibility state of a component.\n     * @param {Boolean} boolean - Value of visibility as boolean.\n     * @group Emits\n     */\n    @Output() visibleChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n    /**\n     * Callback to invoke before the overlay is shown.\n     * @param {OverlayOnBeforeShowEvent} event - Custom overlay before show event.\n     * @group Emits\n     */\n    @Output() onBeforeShow: EventEmitter<OverlayOnBeforeShowEvent> = new EventEmitter<OverlayOnBeforeShowEvent>();\n    /**\n     * Callback to invoke when the overlay is shown.\n     * @param {OverlayOnShowEvent} event - Custom overlay show event.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<OverlayOnShowEvent> = new EventEmitter<OverlayOnShowEvent>();\n    /**\n     * Callback to invoke before the overlay is hidden.\n     * @param {OverlayOnBeforeHideEvent} event - Custom overlay before hide event.\n     * @group Emits\n     */\n    @Output() onBeforeHide: EventEmitter<OverlayOnBeforeHideEvent> = new EventEmitter<OverlayOnBeforeHideEvent>();\n    /**\n     * Callback to invoke when the overlay is hidden\n     * @param {OverlayOnHideEvent} event - Custom hide event.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<OverlayOnHideEvent> = new EventEmitter<OverlayOnHideEvent>();\n    /**\n     * Callback to invoke when the animation is started.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    @Output() onAnimationStart: EventEmitter<AnimationEvent> = new EventEmitter<AnimationEvent>();\n    /**\n     * Callback to invoke when the animation is done.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    @Output() onAnimationDone: EventEmitter<AnimationEvent> = new EventEmitter<AnimationEvent>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<any> | undefined;\n\n    @ViewChild('overlay') overlayViewChild: ElementRef | undefined;\n\n    @ViewChild('content') contentViewChild: ElementRef | undefined;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    _visible: boolean = false;\n\n    _mode: OverlayModeType | string;\n\n    _style: { [klass: string]: any } | null | undefined;\n\n    _styleClass: string | undefined;\n\n    _contentStyle: { [klass: string]: any } | null | undefined;\n\n    _contentStyleClass: string | undefined;\n\n    _target: any;\n\n    _appendTo: 'body' | HTMLElement | undefined;\n\n    _autoZIndex: boolean | undefined;\n\n    _baseZIndex: number | undefined;\n\n    _showTransitionOptions: string | undefined;\n\n    _hideTransitionOptions: string | undefined;\n\n    _listener: any;\n\n    _responsive: ResponsiveOverlayOptions | undefined;\n\n    _options: OverlayOptions | undefined;\n\n    modalVisible: boolean = false;\n\n    isOverlayClicked: boolean = false;\n\n    isOverlayContentClicked: boolean = false;\n\n    scrollHandler: any;\n\n    documentClickListener: any;\n\n    documentResizeListener: any;\n\n    private documentKeyboardListener: VoidListener;\n\n    private window: Window | null;\n\n    protected transformOptions: any = {\n        default: 'scaleY(0.8)',\n        center: 'scale(0.7)',\n        top: 'translate3d(0px, -100%, 0px)',\n        'top-start': 'translate3d(0px, -100%, 0px)',\n        'top-end': 'translate3d(0px, -100%, 0px)',\n        bottom: 'translate3d(0px, 100%, 0px)',\n        'bottom-start': 'translate3d(0px, 100%, 0px)',\n        'bottom-end': 'translate3d(0px, 100%, 0px)',\n        left: 'translate3d(-100%, 0px, 0px)',\n        'left-start': 'translate3d(-100%, 0px, 0px)',\n        'left-end': 'translate3d(-100%, 0px, 0px)',\n        right: 'translate3d(100%, 0px, 0px)',\n        'right-start': 'translate3d(100%, 0px, 0px)',\n        'right-end': 'translate3d(100%, 0px, 0px)'\n    };\n\n    get modal() {\n        if (isPlatformBrowser(this.platformId)) {\n            return this.mode === 'modal' || (this.overlayResponsiveOptions && this.window?.matchMedia(this.overlayResponsiveOptions.media?.replace('@media', '') || `(max-width: ${this.overlayResponsiveOptions.breakpoint})`).matches);\n        }\n    }\n\n    get overlayMode() {\n        return this.mode || (this.modal ? 'modal' : 'overlay');\n    }\n\n    get overlayOptions(): OverlayOptions {\n        return { ...this.config?.overlayOptions, ...this.options }; // TODO: Improve performance\n    }\n\n    get overlayResponsiveOptions(): ResponsiveOverlayOptions {\n        return { ...this.overlayOptions?.responsive, ...this.responsive }; // TODO: Improve performance\n    }\n\n    get overlayResponsiveDirection() {\n        return this.overlayResponsiveOptions?.direction || 'center';\n    }\n\n    get overlayEl() {\n        return this.overlayViewChild?.nativeElement;\n    }\n\n    get contentEl() {\n        return this.contentViewChild?.nativeElement;\n    }\n\n    get targetEl() {\n        return DomHandler.getTargetElement(this.target, this.el?.nativeElement);\n    }\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        public el: ElementRef,\n        public renderer: Renderer2,\n        private config: PrimeNGConfig,\n        public overlayService: OverlayService,\n        public cd: ChangeDetectorRef,\n        private zone: NgZone\n    ) {\n        this.window = this.document.defaultView;\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                // TODO: new template types may be added.\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    show(overlay?: HTMLElement, isFocus: boolean = false) {\n        this.onVisibleChange(true);\n        this.handleEvents('onShow', { overlay: overlay || this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n\n        isFocus && DomHandler.focus(this.targetEl);\n        this.modal && DomHandler.addClass(this.document?.body, 'p-overflow-hidden');\n    }\n\n    hide(overlay?: HTMLElement, isFocus: boolean = false) {\n        if (!this.visible) {\n            return;\n        } else {\n            this.onVisibleChange(false);\n            this.handleEvents('onHide', { overlay: overlay || this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n            isFocus && DomHandler.focus(this.targetEl);\n            this.modal && DomHandler.removeClass(this.document?.body, 'p-overflow-hidden');\n        }\n    }\n\n    alignOverlay() {\n        !this.modal && DomHandler.alignOverlay(this.overlayEl, this.targetEl, this.appendTo);\n    }\n\n    onVisibleChange(visible: boolean) {\n        this._visible = visible;\n        this.visibleChange.emit(visible);\n    }\n\n    onOverlayClick() {\n        this.isOverlayClicked = true;\n    }\n\n    onOverlayContentClick(event: MouseEvent) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.targetEl\n        });\n\n        this.isOverlayContentClicked = true;\n    }\n\n    onOverlayContentAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                this.handleEvents('onBeforeShow', { overlay: this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n\n                if (this.autoZIndex) {\n                    ZIndexUtils.set(this.overlayMode, this.overlayEl, this.baseZIndex + this.config?.zIndex[this.overlayMode]);\n                }\n\n                DomHandler.appendOverlay(this.overlayEl, this.appendTo === 'body' ? this.document.body : this.appendTo, this.appendTo);\n                this.alignOverlay();\n\n                break;\n\n            case 'void':\n                this.handleEvents('onBeforeHide', { overlay: this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n\n                this.modal && DomHandler.addClass(this.overlayEl, 'p-component-overlay-leave');\n\n                break;\n        }\n\n        this.handleEvents('onAnimationStart', event);\n    }\n\n    onOverlayContentAnimationDone(event: AnimationEvent) {\n        const container = this.overlayEl || event.element.parentElement;\n\n        switch (event.toState) {\n            case 'visible':\n                if (this.visible) {\n                    this.show(container, true);\n                }\n                this.bindListeners();\n\n                break;\n\n            case 'void':\n                if (!this.visible) {\n                    this.hide(container, true);\n                }\n\n                this.unbindListeners();\n\n                DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n                ZIndexUtils.clear(container);\n                this.modalVisible = false;\n                this.cd.markForCheck();\n\n                break;\n        }\n\n        this.handleEvents('onAnimationDone', event);\n    }\n\n    handleEvents(name: string, params: any) {\n        (this as any)[name].emit(params);\n        this.options && (this.options as any)[name] && (this.options as any)[name](params);\n        this.config?.overlayOptions && (this.config?.overlayOptions as any)[name] && (this.config?.overlayOptions as any)[name](params);\n    }\n\n    bindListeners() {\n        this.bindScrollListener();\n        this.bindDocumentClickListener();\n        this.bindDocumentResizeListener();\n        this.bindDocumentKeyboardListener();\n    }\n\n    unbindListeners() {\n        this.unbindScrollListener();\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindDocumentKeyboardListener();\n    }\n\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.targetEl, (event: any) => {\n                const valid = this.listener ? this.listener(event, { type: 'scroll', mode: this.overlayMode, valid: true }) : true;\n\n                valid && this.hide(event, true);\n            });\n        }\n\n        this.scrollHandler.bindScrollListener();\n    }\n\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            this.documentClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                const isTargetClicked = this.targetEl && (this.targetEl.isSameNode(event.target) || (!this.isOverlayClicked && this.targetEl.contains(event.target)));\n                const isOutsideClicked = !isTargetClicked && !this.isOverlayContentClicked;\n                const valid = this.listener ? this.listener(event, { type: 'outside', mode: this.overlayMode, valid: event.which !== 3 && isOutsideClicked }) : isOutsideClicked;\n\n                valid && this.hide(event);\n                this.isOverlayClicked = this.isOverlayContentClicked = false;\n            });\n        }\n    }\n\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n\n    bindDocumentResizeListener() {\n        if (!this.documentResizeListener) {\n            this.documentResizeListener = this.renderer.listen(this.window, 'resize', (event) => {\n                const valid = this.listener ? this.listener(event, { type: 'resize', mode: this.overlayMode, valid: !DomHandler.isTouchDevice() }) : !DomHandler.isTouchDevice();\n\n                valid && this.hide(event, true);\n            });\n        }\n    }\n\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n\n    bindDocumentKeyboardListener(): void {\n        if (this.documentKeyboardListener) {\n            return;\n        }\n\n        this.zone.runOutsideAngular(() => {\n            this.documentKeyboardListener = this.renderer.listen(this.window, 'keydown', (event) => {\n                if (this.overlayOptions.hideOnEscape === false || event.code !== 'Escape') {\n                    return;\n                }\n\n                const valid = this.listener ? this.listener(event, { type: 'keydown', mode: this.overlayMode, valid: !DomHandler.isTouchDevice() }) : !DomHandler.isTouchDevice();\n\n                if (valid) {\n                    this.zone.run(() => {\n                        this.hide(event, true);\n                    });\n                }\n            });\n        });\n    }\n\n    unbindDocumentKeyboardListener(): void {\n        if (this.documentKeyboardListener) {\n            this.documentKeyboardListener();\n            this.documentKeyboardListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.hide(this.overlayEl, true);\n\n        if (this.overlayEl) {\n            DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n            ZIndexUtils.clear(this.overlayEl);\n        }\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        this.unbindListeners();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule],\n    exports: [Overlay, SharedModule],\n    declarations: [Overlay]\n})\nexport class OverlayModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;AA8Ba,MAAA,sBAAsB,GAAQ;AACvC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,OAAO,CAAC;AACtC,IAAA,KAAK,EAAE,IAAI;EACb;AAEF,MAAM,2BAA2B,GAAG,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;AAExI,MAAM,2BAA2B,GAAG,SAAS,CAAC,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACxI;;;AAGG;MAqDU,OAAO,CAAA;AAoUc,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACtB,IAAA,EAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACC,IAAA,MAAA,CAAA;AACD,IAAA,cAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACC,IAAA,IAAA,CAAA;AA1UZ;;;;AAIG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,KAAc,EAAA;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACrC,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC5B,SAAA;KACJ;AACD;;;;AAIG;AACH,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;KAClD;IACD,IAAI,IAAI,CAAC,KAA+B,EAAA;AACpC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;AACD;;;;AAIG;AACH,IAAA,IAAa,KAAK,GAAA;AACd,QAAA,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;KACzH;IACD,IAAI,KAAK,CAAC,KAAkD,EAAA;AACxD,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACvB;AACD;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;AACnB,QAAA,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;KACxI;IACD,IAAI,UAAU,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC5B;AACD;;;;AAIG;AACH,IAAA,IAAa,YAAY,GAAA;AACrB,QAAA,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;KAC9I;IACD,IAAI,YAAY,CAAC,KAAkD,EAAA;AAC/D,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;KAC9B;AACD;;;;AAIG;AACH,IAAA,IAAa,iBAAiB,GAAA;AAC1B,QAAA,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE,iBAAiB,GAAG,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;KAC7J;IACD,IAAI,iBAAiB,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;KACnC;AACD;;;;AAIG;AACH,IAAA,IAAa,MAAM,GAAA;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC;QAC1D,OAAO,KAAK,KAAK,SAAS,GAAG,OAAO,GAAG,KAAK,CAAC;KAChD;IACD,IAAI,MAAM,CAAC,KAAgC,EAAA;AACvC,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB;AACD;;;;AAIG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC;KAC1D;IACD,IAAI,QAAQ,CAAC,KAAuC,EAAA;AAChD,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KAC1B;AACD;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC;QAClE,OAAO,KAAK,KAAK,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;KAC7C;IACD,IAAI,UAAU,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC5B;AACD;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC;QAClE,OAAO,KAAK,KAAK,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC;KAC1C;IACD,IAAI,UAAU,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC5B;AACD;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,cAAc,EAAE,qBAAqB,CAAC;QACxF,OAAO,KAAK,KAAK,SAAS,GAAG,iCAAiC,GAAG,KAAK,CAAC;KAC1E;IACD,IAAI,qBAAqB,CAAC,KAAa,EAAA;AACnC,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;KACvC;AACD;;;;AAIG;AACH,IAAA,IAAa,qBAAqB,GAAA;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,cAAc,EAAE,qBAAqB,CAAC;QACxF,OAAO,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG,KAAK,CAAC;KACrD;IACD,IAAI,qBAAqB,CAAC,KAAa,EAAA;AACnC,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;KACvC;AACD;;;;AAIG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC;KAC1D;IACD,IAAI,QAAQ,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KAC1B;AACD;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC;KAC9D;IACD,IAAI,UAAU,CAAC,GAAyC,EAAA;AACpD,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KAC1B;AACD;;;;AAIG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,GAA+B,EAAA;AACvC,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;KACvB;AACD;;;;AAIG;AACO,IAAA,aAAa,GAA0B,IAAI,YAAY,EAAW,CAAC;AAC7E;;;;AAIG;AACO,IAAA,YAAY,GAA2C,IAAI,YAAY,EAA4B,CAAC;AAC9G;;;;AAIG;AACO,IAAA,MAAM,GAAqC,IAAI,YAAY,EAAsB,CAAC;AAC5F;;;;AAIG;AACO,IAAA,YAAY,GAA2C,IAAI,YAAY,EAA4B,CAAC;AAC9G;;;;AAIG;AACO,IAAA,MAAM,GAAqC,IAAI,YAAY,EAAsB,CAAC;AAC5F;;;;AAIG;AACO,IAAA,gBAAgB,GAAiC,IAAI,YAAY,EAAkB,CAAC;AAC9F;;;;AAIG;AACO,IAAA,eAAe,GAAiC,IAAI,YAAY,EAAkB,CAAC;AAE7D,IAAA,SAAS,CAA6B;AAEhD,IAAA,gBAAgB,CAAyB;AAEzC,IAAA,gBAAgB,CAAyB;AAE/D,IAAA,eAAe,CAA+B;IAE9C,QAAQ,GAAY,KAAK,CAAC;AAE1B,IAAA,KAAK,CAA2B;AAEhC,IAAA,MAAM,CAA8C;AAEpD,IAAA,WAAW,CAAqB;AAEhC,IAAA,aAAa,CAA8C;AAE3D,IAAA,kBAAkB,CAAqB;AAEvC,IAAA,OAAO,CAAM;AAEb,IAAA,SAAS,CAAmC;AAE5C,IAAA,WAAW,CAAsB;AAEjC,IAAA,WAAW,CAAqB;AAEhC,IAAA,sBAAsB,CAAqB;AAE3C,IAAA,sBAAsB,CAAqB;AAE3C,IAAA,SAAS,CAAM;AAEf,IAAA,WAAW,CAAuC;AAElD,IAAA,QAAQ,CAA6B;IAErC,YAAY,GAAY,KAAK,CAAC;IAE9B,gBAAgB,GAAY,KAAK,CAAC;IAElC,uBAAuB,GAAY,KAAK,CAAC;AAEzC,IAAA,aAAa,CAAM;AAEnB,IAAA,qBAAqB,CAAM;AAE3B,IAAA,sBAAsB,CAAM;AAEpB,IAAA,wBAAwB,CAAe;AAEvC,IAAA,MAAM,CAAgB;AAEpB,IAAA,gBAAgB,GAAQ;AAC9B,QAAA,OAAO,EAAE,aAAa;AACtB,QAAA,MAAM,EAAE,YAAY;AACpB,QAAA,GAAG,EAAE,8BAA8B;AACnC,QAAA,WAAW,EAAE,8BAA8B;AAC3C,QAAA,SAAS,EAAE,8BAA8B;AACzC,QAAA,MAAM,EAAE,6BAA6B;AACrC,QAAA,cAAc,EAAE,6BAA6B;AAC7C,QAAA,YAAY,EAAE,6BAA6B;AAC3C,QAAA,IAAI,EAAE,8BAA8B;AACpC,QAAA,YAAY,EAAE,8BAA8B;AAC5C,QAAA,UAAU,EAAE,8BAA8B;AAC1C,QAAA,KAAK,EAAE,6BAA6B;AACpC,QAAA,aAAa,EAAE,6BAA6B;AAC5C,QAAA,WAAW,EAAE,6BAA6B;KAC7C,CAAC;AAEF,IAAA,IAAI,KAAK,GAAA;AACL,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,OAAO,IAAI,CAAC,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAA,YAAA,EAAe,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAA,CAAA,CAAG,CAAC,CAAC,OAAO,CAAC,CAAC;AAChO,SAAA;KACJ;AAED,IAAA,IAAI,WAAW,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;KAC1D;AAED,IAAA,IAAI,cAAc,GAAA;AACd,QAAA,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;KAC9D;AAED,IAAA,IAAI,wBAAwB,GAAA;AACxB,QAAA,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;KACrE;AAED,IAAA,IAAI,0BAA0B,GAAA;AAC1B,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,SAAS,IAAI,QAAQ,CAAC;KAC/D;AAED,IAAA,IAAI,SAAS,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC;KAC/C;AAED,IAAA,IAAI,SAAS,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC;KAC/C;AAED,IAAA,IAAI,QAAQ,GAAA;AACR,QAAA,OAAO,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;KAC3E;AAED,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACrC,EAAc,EACd,QAAmB,EAClB,MAAqB,EACtB,cAA8B,EAC9B,EAAqB,EACpB,IAAY,EAAA;QAPM,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACrC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAClB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACtB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QAC9B,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACpB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAEpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;KAC3C;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,IAAI,CAAC,OAAqB,EAAE,OAAA,GAAmB,KAAK,EAAA;AAChD,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAEnH,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;KAC/E;AAED,IAAA,IAAI,CAAC,OAAqB,EAAE,OAAA,GAAmB,KAAK,EAAA;AAChD,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;AACV,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACnH,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3C,YAAA,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;AAClF,SAAA;KACJ;IAED,YAAY,GAAA;QACR,CAAC,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACxF;AAED,IAAA,eAAe,CAAC,OAAgB,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AACxB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACpC;IAED,cAAc,GAAA;AACV,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;KAChC;AAED,IAAA,qBAAqB,CAAC,KAAiB,EAAA;AACnC,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,YAAA,aAAa,EAAE,KAAK;YACpB,MAAM,EAAE,IAAI,CAAC,QAAQ;AACxB,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;KACvC;AAED,IAAA,8BAA8B,CAAC,KAAqB,EAAA;QAChD,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;gBACV,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBAE9G,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AAC9G,iBAAA;AAED,gBAAA,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAEpB,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAE9G,gBAAA,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,2BAA2B,CAAC,CAAC;gBAE/E,MAAM;AACb,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;KAChD;AAED,IAAA,6BAA6B,CAAC,KAAqB,EAAA;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC;QAEhE,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;gBACV,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,oBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC9B,iBAAA;gBACD,IAAI,CAAC,aAAa,EAAE,CAAC;gBAErB,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACf,oBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC9B,iBAAA;gBAED,IAAI,CAAC,eAAe,EAAE,CAAC;AAEvB,gBAAA,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvE,gBAAA,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC7B,gBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,gBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;gBAEvB,MAAM;AACb,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;KAC/C;IAED,YAAY,CAAC,IAAY,EAAE,MAAW,EAAA;QACjC,IAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,OAAO,IAAK,IAAI,CAAC,OAAe,CAAC,IAAI,CAAC,IAAK,IAAI,CAAC,OAAe,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;AACnF,QAAA,IAAI,CAAC,MAAM,EAAE,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAsB,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAsB,EAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;KACnI;IAED,aAAa,GAAA;QACT,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACvC;IAED,eAAe,GAAA;QACX,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,8BAA8B,EAAE,CAAC;KACzC;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAU,KAAI;AACjF,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;gBAEnH,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACpC,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;KAC3C;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;AAC7C,SAAA;KACJ;IAED,yBAAyB,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;AAChF,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACtJ,MAAM,gBAAgB,GAAG,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC3E,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,gBAAgB,EAAE,CAAC,GAAG,gBAAgB,CAAC;AAEjK,gBAAA,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;AACjE,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,2BAA2B,GAAA;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;KACJ;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,KAAK,KAAI;AAChF,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;gBAEjK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACpC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;AAC7B,YAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,KAAK,KAAI;AACnF,gBAAA,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,KAAK,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;oBACvE,OAAO;AACV,iBAAA;AAED,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;AAElK,gBAAA,IAAI,KAAK,EAAE;AACP,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACf,wBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC3B,qBAAC,CAAC,CAAC;AACN,iBAAA;AACL,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACN;IAED,8BAA8B,GAAA;QAC1B,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;AACxC,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvE,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,SAAA;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;KAC1B;uGApjBQ,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAoUJ,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AArUd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,gsBANL,CAAC,sBAAsB,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAgOlB,aAAa,EA5QpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,2+BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EACW,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAS/K,OAAO,EAAA,UAAA,EAAA,CAAA;kBApDnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwCT,EACW,UAAA,EAAA,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACvK,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,aAC1B,CAAC,sBAAsB,CAAC,EAE7B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,2+BAAA,CAAA,EAAA,CAAA;;0BAsUI,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;wMA/TV,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAeO,IAAI,EAAA,CAAA;sBAAhB,KAAK;gBAWO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAWO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAWO,YAAY,EAAA,CAAA;sBAAxB,KAAK;gBAWO,iBAAiB,EAAA,CAAA;sBAA7B,KAAK;gBAWO,MAAM,EAAA,CAAA;sBAAlB,KAAK;gBAYO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAWO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAYO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAYO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAYO,qBAAqB,EAAA,CAAA;sBAAjC,KAAK;gBAYO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAWO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAWO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAWI,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,gBAAgB,EAAA,CAAA;sBAAzB,MAAM;gBAMG,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAER,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEE,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;;MA8VX,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAb,aAAa,EAAA,YAAA,EAAA,CA5jBb,OAAO,CAwjBN,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CAxjB3B,OAAO,EAyjBG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAJZ,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EACjB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGtB,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AACrC,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC;oBAChC,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;AC1pBD;;AAEG;;;;"}