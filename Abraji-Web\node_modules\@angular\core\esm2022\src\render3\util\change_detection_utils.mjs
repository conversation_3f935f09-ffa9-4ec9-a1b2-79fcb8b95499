/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertDefined } from '../../util/assert';
import { getComponentViewByInstance } from '../context_discovery';
import { detectChangesInternal } from '../instructions/change_detection';
import { markViewDirty } from '../instructions/mark_view_dirty';
import { FLAGS } from '../interfaces/view';
import { getRootComponents } from './discovery_utils';
/**
 * Marks a component for check (in case of OnPush components) and synchronously
 * performs change detection on the application this component belongs to.
 *
 * @param component Component to {@link ChangeDetectorRef#markForCheck mark for check}.
 *
 * @publicApi
 * @globalApi ng
 */
export function applyChanges(component) {
    ngDevMode && assertDefined(component, 'component');
    markViewDirty(getComponentViewByInstance(component), 3 /* NotificationSource.DebugApplyChanges */);
    getRootComponents(component).forEach((rootComponent) => detectChanges(rootComponent));
}
/**
 * Synchronously perform change detection on a component (and possibly its sub-components).
 *
 * This function triggers change detection in a synchronous way on a component.
 *
 * @param component The component which the change detection should be performed on.
 */
function detectChanges(component) {
    const view = getComponentViewByInstance(component);
    view[FLAGS] |= 1024 /* LViewFlags.RefreshView */;
    detectChangesInternal(view);
}
//# sourceMappingURL=data:application/json;base64,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