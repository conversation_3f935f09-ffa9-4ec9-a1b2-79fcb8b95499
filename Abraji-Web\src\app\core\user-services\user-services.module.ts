import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserApi } from './api/users.api';
import { UsersData } from './api/users';
import { UsersService } from './services/users.service';
import { HeaderApi } from './api/header.api';
import { HeaderData } from './api/header';
import { HeaderService } from './services/header.service';

const API = [UserApi, HeaderApi]

const SERVICES = [
  {provide: UsersData, useClass: UsersService},
  {provide: HeaderData, useClass: HeaderService},
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ]
})
export class UserServicesModule {
  static forRoot(): ModuleWithProviders<UserServicesModule> {
    return {
      ngModule: UserServicesModule,
      providers: [
        ...API,
        ...SERVICES
      ]
    };
  }
}
