<section appFlowbiteInit
  class="mt-5 grid max-lg:grid-flow-row max-lg:grid-rows-2 lg:grid-flow-col lg:grid-cols-2 gap-12 justify-center items-center">
  <!-- Wallet -->
  <div>
    <div class="flex items-center justify-center lg:items-start mx-auto" *ngIf="isLoading">
      <div
        class="block p-10 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 animate-pulse">
        <div class="flex items-center space-x-4">
          <div class="w-20 h-10 bg-gray-300 rounded-full dark:bg-gray-700"></div>
          <div>
            <div class="h-8 bg-gray-300 rounded dark:bg-gray-700 w-40 mb-2"></div>
            <div class="h-6 bg-gray-300 rounded dark:bg-gray-700 w-32"></div>
          </div>
        </div>
        <div class="mt-6 flex space-x-2">
          <div class="h-10 bg-gray-300 rounded dark:bg-gray-700 w-20"></div>
          <div class="h-10 bg-gray-300 rounded dark:bg-gray-700 w-20"></div>
        </div>
      </div>
    </div>
    <div appFlowbiteInit class="flex max-lg:justify-center items-center" *ngIf="!isLoading">
      <div class="flex mx-auto">
        <div
          class="block p-8 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 transform transition-all hover:scale-105 duration-300">
          <div class="flex items-center space-x-4 p-8">
            <div class="text-6xl text-blue-500 me-3">
              <svg class="w-10 h-10 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M17 8H5m12 0a1 1 0 0 1 1 1v2.6M17 8l-4-4M5 8a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.6M5 8l4-4 4 4m6 4h-4a2 2 0 1 0 0 4h4a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1Z" />
              </svg>
            </div>
            <div>
              <span class="text-4xl text-gray-800 dark:text-gray-200">{{'wallet.currentBalance' | transloco}}</span>
              <p class="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-2">{{balance}} {{ 'wallet.iqd' |
                transloco }}</p>
            </div>
          </div>
          <!-- buttons -->
          <div class="grid grid-cols-2 gap-4 justify-items-center">
            <div
              class="w-full text-white bg-green-400 hover:bg-green-600 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center items-center  dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
              <button data-modal-target="deposit-modal" data-modal-toggle="deposit-modal" type="button"
                class="inline-flex">
                {{'wallet.deposit' | transloco}}
                <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                  fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 12h14m-7 7V5" />
                </svg>
              </button>
            </div>
            <div
              class="w-full text-white bg-purple-600 hover:bg-purple-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center  items-center  dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
              <button data-modal-target="withdraw-modal" data-modal-toggle="withdraw-modal" type="button"
                class="inline-flex">
                {{'wallet.withdraw' | transloco}}
                <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                  fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 12h14" />
                </svg>
              </button>
            </div>
            <div
              class="w-full text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center  items-center  dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800">
              <button data-modal-target="reset-balance-modal" data-modal-toggle="reset-balance-modal" type="button"
                class="inline-flex">
                {{'wallet.resetBalance' | transloco}}
                <svg class="w-6 h-6  dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                  height="24" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z" />
                </svg>
              </button>
            </div>
            <div
              class="w-full text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center  items-center  dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
              <button data-modal-target="edit-balance-modal" data-modal-toggle="edit-balance-modal" type="button"
                (click)="updateEditBalanceForm()" class="inline-flex">
                {{'wallet.editBalance' | transloco}}
                <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                  fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z" />
                </svg>

              </button>
            </div>
            <!-- <div class="mt-6 flex space-x-5">
             </div>
             <div class="mt-6 flex space-x-5">
             </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Transactions -->
  <div>
    <div
      class="flex flex-col w-full max-w-md p-4 m-auto bg-white border border-gray-200 rounded-lg shadow sm:p-8 dark:bg-gray-800 dark:border-gray-700">
      <div class="flex items-center justify-between mb-4">
        <h5 class="text-xl font-bold leading-none text-gray-900 dark:text-white">
          {{'wallet.walletTransactions' | transloco}} </h5>
        <a routerLink="transactions" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">
          {{'common.viewAll' | transloco}}
        </a>
      </div>
      <div *ngIf="transactions.data" class="flow-root">
        <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
          <li *ngFor="let item of transactions.data" class="py-3 sm:py-4">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                @if(item.type === 'credit') {
                <span title="{{'wallet.deposit' | transloco}}">
                  <svg class="w-6 h-6 text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                    height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 7.757v8.486M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                  </svg>
                </span>
                }@else {
                <span title="{{'wallet.withdraw' | transloco}}">
                  <svg class="w-6 h-6 text-purple-600 dark:text-white" aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M7.757 12h8.486M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                  </svg>
                </span>
                }
              </div>
              <div class="flex-1 min-w-0 ms-4">
                <p class=" font-medium text-gray-900 truncate dark:text-white">
                  @if(item.debt_id){
                  {{'debts.debt' | transloco}}
                  }@else if (item.transaction_id) {
                  {{'resources.expenses' | transloco}}
                  }@else {
                  {{'resources.other' | transloco}}
                  }
                </p>
                <p class=" text-gray-500 truncate dark:text-gray-400">
                  {{item.created_at | date:'medium'}}
                </p>
              </div>
              <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                {{item.amount}}
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div *ngIf="transactions.data.length==0"
        class="flex flex-col my-8  items-center justify-center text-gray-600 opacity-50 dark:text-gray-400">
        <svg class="w-56 h-56 text-center " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
          fill="none" viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
            d="M8 20V7m0 13-4-4m4 4 4-4m4-12v13m0-13 4 4m-4-4-4 4" />
        </svg>

        <h1 class="text-center text-2xl mb-3 font-bold">{{'wallet.TransactionEmpty'| transloco}}</h1>
        <h1 class="text-center font-bold ">{{'wallet.noTransactions'| transloco}}
        </h1>
      </div>
    </div>
  </div>

</section>

<section *ngIf="!isLoading">
  <!-- deposit-modal -->
  <div>
    <div id="deposit-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{'wallet.deposit'| transloco}}
            </h3>
            <button type="button"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
              data-modal-hide="deposit-modal">
              <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span class="sr-only">Close</span>
            </button>
          </div>
          <!-- Modal body -->
          <div class="p-4 md:p-5 space-y-4">
            <div [formGroup]="depositForm" class="">
              <div>
                <label for="deposit-form-amount" class="block text-gray-600">{{'debts.amount' | transloco}}</label>
                <input id="deposit-form-amount" formControlName="amount" type="number"
                  placeholder="0.00 {{ 'wallet.iqd' | transloco }}"
                  class="w-full border border-gray-300 p-2 rounded-lg">
                <p class="error-message text-red-500 mt-2"
                  *ngIf="depositFormAmount && depositFormAmount.invalid && depositFormAmount.touched">
                  <span *ngIf="depositFormAmount.errors.required">{{ 'validationErrors.required' | transloco }}</span>
                  <span *ngIf="depositFormAmount.errors.min">{{ 'validationErrors.min' | transloco: { value: 0}
                    }}</span>
                </p>
              </div>
            </div>
          </div>
          <!-- Modal footer -->
          <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
            <button (click)="deposit()" data-modal-hide="deposit-modal" type="button"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">{{'common.confirm'
              | transloco}}</button>
            <button data-modal-hide="deposit-modal" type="button"
              class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
              {{'common.cancel' | transloco}} </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- withdraw-modal -->
  <div>
    <div id="withdraw-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{'wallet.withdraw'| transloco}}
            </h3>
            <button type="button"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
              data-modal-hide="withdraw-modal">
              <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span class="sr-only">Close</span>
            </button>
          </div>
          <!-- Modal body -->
          <div class="p-4 md:p-5 space-y-4">
            <div [formGroup]="withdrawForm" class="p-4 md:p-5">
              <div class="grid gap-4 mb-4 grid-cols-2">
                <div class="col-span-2">
                  <label for="description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                    'resources.expensesDescription' | transloco }}</label>
                  <textarea id="description" rows="4" formControlName="description"
                    class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    placeholder="{{ 'resources.descriptionPlaceholder' |transloco }}"></textarea>
                </div>
                <div class="col-span-2 sm:col-span-1">
                  <label for="price" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{
                    'resources.price' | transloco }}</label>
                  <input type="number" name="price" id="price" formControlName="amount"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                    placeholder="2999 {{ 'resources.iqd'| transloco }}" required="">
                  <p class="error-message text-red-500 mt-2"
                    *ngIf="withdrawFormAmount && withdrawFormAmount.invalid && withdrawFormAmount.touched">
                    <span *ngIf="withdrawFormAmount.errors.required">{{ 'validationErrors.required' | transloco
                      }}</span>
                    <span *ngIf="withdrawFormAmount.errors.min">{{ 'validationErrors.min' | transloco: { value: 0}
                      }}</span>
                  </p>
                </div>
                <div class="col-span-2 sm:col-span-1">
                  <label for="category"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'resources.category' |
                    transloco}}</label>
                  <select id="category" formControlName="category"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    <option selected="" disabled>{{ 'resources.selectCategory' | transloco }}</option>
                    <option value="expense">{{ 'resources.cardsPurchase' | transloco }}</option> <!-- card -->
                    <option value="maintenance">{{ 'resources.maintenance' | transloco }}</option>
                    <option value="general">{{ 'resources.generalExpenses' | transloco }}</option>
                  </select>
                  <p class="error-message text-red-500 mt-2"
                    *ngIf="withdrawFormCategory && withdrawFormCategory.invalid && withdrawFormCategory.touched">
                    <span *ngIf="withdrawFormCategory.errors.required">{{ 'validationErrors.required' | transloco
                      }}</span>
                  </p>
                </div>
                <!-- ['maintenance', 'expense', 'general'] -->
              </div>
            </div>
          </div>
          <!-- Modal footer -->
          <div
            class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
            <button data-modal-hide="withdraw-modal" type="button" (click)="withdraw()"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">{{'common.confirm' | transloco}}</button>
            <button data-modal-hide="withdraw-modal" type="button"
              class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
              {{'common.cancel' | transloco}} </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- reset-balance-modal -->
  <div>
    <div id="reset-balance-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{'wallet.resetBalance'| transloco}}
            </h3>
            <button type="button"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
              data-modal-hide="reset-balance-modal">
              <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span class="sr-only">Close</span>
            </button>
          </div>
          <!-- Modal body -->
          <div class="p-4 md:p-5 text-center">
            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">{{ 'wallet.resetConfirmation' |
              transloco }}</h3>

          </div>
          <!-- Modal footer -->
          <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
            <button data-modal-hide="reset-balance-modal" type="button" (click)="resetBalance()"
              class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800">{{'common.confirm'
              | transloco}}</button>
            <button data-modal-hide="reset-balance-modal" type="button"
              class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
              {{'common.cancel' | transloco}} </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- edit-balance-modal -->
  <div>
    <div id="edit-balance-modal" data-modal-backdrop="static" tabindex="-1" aria-hidden="true"
      class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
      <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{'wallet.deposit'| transloco}}
            </h3>
            <button type="button"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
              data-modal-hide="edit-balance-modal">
              <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
              </svg>
              <span class="sr-only">Close</span>
            </button>
          </div>
          <!-- Modal body -->
          <div class="p-4 md:p-5 space-y-4">
            <div [formGroup]="editBalanceForm" class="">
              <div>
                <label for="balance" class="block text-gray-600">{{'wallet.currentBalance' | transloco}}</label>
                <input id="balance" formControlName="balance" type="number"
                  placeholder="0.00 {{ 'wallet.iqd' | transloco }}"
                  class="w-full border border-gray-300 p-2 rounded-lg">
                <p class="error-message text-red-500 mt-2"
                  *ngIf="editBalanceFormBalance && editBalanceFormBalance.invalid && editBalanceFormBalance.touched">
                  <span *ngIf="editBalanceFormBalance.errors.required">{{ 'validationErrors.required' | transloco
                    }}</span>
                  <span *ngIf="editBalanceFormBalance.errors.min">{{ 'validationErrors.min' | transloco: { value: 0}
                    }}</span>
                </p>
              </div>
            </div>
          </div>
          <!-- Modal footer -->
          <div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
            <button data-modal-hide="edit-balance-modal" type="button" (click)="editBalance()"
              class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">{{'common.confirm'
              | transloco}}</button>
            <button data-modal-hide="edit-balance-modal" type="button"
              class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
              {{'common.cancel' | transloco}} </button>
          </div>
        </div>
      </div>
    </div>
  </div>

</section>
