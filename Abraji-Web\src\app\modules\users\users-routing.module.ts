import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AllUsersComponent } from './all-users/all-users.component';
import { UserDetailsComponent } from './user-details/user-details.component';
import { OnlineUsersComponent } from './online-users/online-users.component';
import { UserOverviewComponent } from './user-overview/user-overview.component';
import { UserEditComponent } from './user-edit/user-edit.component';
import { UserSessionsComponent } from './user-sessions/user-sessions.component';
import { UserTrafficComponent } from './user-traffic/user-traffic.component';
import { UserInvoicesComponent } from './user-invoices/user-invoices.component';
import { CreateUserComponent } from './create-user/create-user.component';
import { validateIdNumberGuard } from '../../core/guards/validate-id-number.guard';
import { ActivateUserComponent } from './activate-user/activate-user.component';
import { UserDebtsComponent } from '../debts/user-debts/user-debts.component';
import { EditProfileComponent } from './edit-profile/edit-profile.component';

const routes: Routes = [
  {
    path: 'users-list',
    component: AllUsersComponent,
  },
  {
    path: 'online-users',
    component: OnlineUsersComponent,
  },
  {
    path: 'create',
    component: CreateUserComponent,
  },
  {
    path: 'activate/:id',
    component: ActivateUserComponent,
  },
  {
    path: 'profile/:id',
    component: EditProfileComponent,
  },
  {
    path: ':id',
    component: UserDetailsComponent,
    canActivate: [validateIdNumberGuard],
    children: [
      { path: 'overview', component: UserOverviewComponent },
      { path: 'edit', component: UserEditComponent },
      { path: 'sessions', component: UserSessionsComponent },
      { path: 'traffic', component: UserTrafficComponent },
      { path: 'invoices', component: UserInvoicesComponent },
      { path: 'debts', component: UserDebtsComponent },
      { path: '', redirectTo: 'overview', pathMatch: 'full' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UsersRoutingModule {}
