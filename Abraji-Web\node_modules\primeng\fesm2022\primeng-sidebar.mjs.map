{"version": 3, "file": "primeng-sidebar.mjs", "sources": ["../../src/app/components/sidebar/sidebar.ts", "../../src/app/components/sidebar/primeng-sidebar.ts"], "sourcesContent": ["import { animate, animation, style, transition, trigger, useAnimation } from '@angular/animations';\nimport { CommonModule, DOCUMENT } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { RippleModule } from 'primeng/ripple';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { ZIndexUtils } from 'primeng/utils';\n\nconst showAnimation = animation([style({ transform: '{{transform}}', opacity: 0 }), animate('{{transition}}')]);\n\nconst hideAnimation = animation([animate('{{transition}}', style({ transform: '{{transform}}', opacity: 0 }))]);\n/**\n * Sidebar is a panel component displayed as an overlay at the edges of the screen.\n * @group Components\n */\n@Component({\n    selector: 'p-sidebar',\n    template: `\n        <div\n            #container\n            [ngClass]=\"{\n                'p-sidebar': true,\n                'p-sidebar-active': visible,\n                'p-sidebar-left': position === 'left' && !fullScreen,\n                'p-sidebar-right': position === 'right' && !fullScreen,\n                'p-sidebar-top': position === 'top' && !fullScreen,\n                'p-sidebar-bottom': position === 'bottom' && !fullScreen,\n                'p-sidebar-full': fullScreen\n            }\"\n            *ngIf=\"visible\"\n            [@panelState]=\"{ value: 'visible', params: { transform: transformOptions, transition: transitionOptions } }\"\n            (@panelState.start)=\"onAnimationStart($event)\"\n            (@panelState.done)=\"onAnimationEnd($event)\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            role=\"complementary\"\n            [attr.data-pc-name]=\"'sidebar'\"\n            [attr.data-pc-section]=\"'root'\"\n            (keydown)=\"onKeyDown($event)\"\n        >\n            <ng-container *ngIf=\"headlessTemplate; else notHeadless\">\n                <ng-container *ngTemplateOutlet=\"headlessTemplate\"></ng-container>\n            </ng-container>\n            <ng-template #notHeadless>\n                <div class=\"p-sidebar-header\" [attr.data-pc-section]=\"'header'\">\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <button\n                        type=\"button\"\n                        class=\"p-sidebar-close p-sidebar-icon p-link\"\n                        (click)=\"close($event)\"\n                        (keydown.enter)=\"close($event)\"\n                        [attr.aria-label]=\"ariaCloseLabel\"\n                        *ngIf=\"showCloseIcon\"\n                        pRipple\n                        [attr.data-pc-section]=\"'closebutton'\"\n                        [attr.data-pc-group-section]=\"'iconcontainer'\"\n                    >\n                        <TimesIcon *ngIf=\"!closeIconTemplate\" [styleClass]=\"'p-sidebar-close-icon'\" [attr.data-pc-section]=\"'closeicon'\" />\n                        <span *ngIf=\"closeIconTemplate\" class=\"p-sidebar-close-icon\" [attr.data-pc-section]=\"'closeicon'\">\n                            <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                </div>\n                <div class=\"p-sidebar-content\" [attr.data-pc-section]=\"'content'\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n                <ng-container *ngIf=\"footerTemplate\">\n                    <div class=\"p-sidebar-footer\" [attr.data-pc-section]=\"'footer'\">\n                        <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n                    </div>\n                </ng-container>\n            </ng-template>\n        </div>\n    `,\n    animations: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./sidebar.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Sidebar implements AfterViewInit, AfterContentInit, OnDestroy {\n    /**\n     *  Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Whether to block scrolling of the document when sidebar is active.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) blockScroll: boolean = false;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Aria label of the close icon.\n     * @group Props\n     */\n    @Input() ariaCloseLabel: string | undefined;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Whether an overlay mask is displayed behind the sidebar.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) modal: boolean = true;\n    /**\n     * Whether to dismiss sidebar on click of the mask.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) dismissible: boolean = true;\n    /**\n     * Whether to display the close icon.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showCloseIcon: boolean = true;\n    /**\n     * Specifies if pressing escape key should hide the sidebar.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) closeOnEscape: boolean = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    @Input() transitionOptions: string = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Specifies the visibility of the dialog.\n     * @group Props\n     */\n    @Input() get visible(): boolean {\n        return this._visible as boolean;\n    }\n    set visible(val: boolean) {\n        this._visible = val;\n    }\n    /**\n     * Specifies the position of the sidebar, valid values are \"left\", \"right\", \"bottom\" and \"top\".\n     * @group Props\n     */\n    @Input() get position(): string {\n        return this._position;\n    }\n    set position(value: string) {\n        this._position = value;\n\n        switch (value) {\n            case 'left':\n                this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n                break;\n            case 'right':\n                this.transformOptions = 'translate3d(100%, 0px, 0px)';\n                break;\n            case 'bottom':\n                this.transformOptions = 'translate3d(0px, 100%, 0px)';\n                break;\n            case 'top':\n                this.transformOptions = 'translate3d(0px, -100%, 0px)';\n                break;\n        }\n    }\n    /**\n     * Adds a close icon to the header to hide the dialog.\n     * @group Props\n     */\n    @Input() get fullScreen(): boolean {\n        return this._fullScreen;\n    }\n    set fullScreen(value: boolean) {\n        this._fullScreen = value;\n\n        if (value) this.transformOptions = 'none';\n    }\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n    /**\n     * Callback to invoke when dialog is shown.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when dialog is hidden.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when dialog visibility is changed.\n     * @param {boolean} value - Visible value.\n     * @group Emits\n     */\n    @Output() visibleChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n    initialized: boolean | undefined;\n\n    _visible: boolean | undefined;\n\n    _position: string = 'left';\n\n    _fullScreen: boolean = false;\n\n    container: Nullable<HTMLDivElement>;\n\n    transformOptions: any = 'translate3d(-100%, 0px, 0px)';\n\n    mask: Nullable<HTMLDivElement>;\n\n    maskClickListener: VoidListener;\n\n    documentEscapeListener: VoidListener;\n\n    animationEndListener: VoidListener;\n\n    contentTemplate: Nullable<TemplateRef<any>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    closeIconTemplate: Nullable<TemplateRef<any>>;\n\n    headlessTemplate: Nullable<TemplateRef<any>>;\n\n    constructor(@Inject(DOCUMENT) private document: Document, public el: ElementRef, public renderer: Renderer2, public cd: ChangeDetectorRef, public config: PrimeNGConfig) {}\n\n    ngAfterViewInit() {\n        this.initialized = true;\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n                case 'headless':\n                    this.headlessTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        if (event.code === 'Escape') {\n            this.hide(false);\n        }\n    }\n\n    show() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('modal', this.container, this.baseZIndex || this.config.zIndex.modal);\n        }\n\n        if (this.modal) {\n            this.enableModality();\n        }\n\n        this.onShow.emit({});\n        this.visibleChange.emit(true);\n    }\n\n    hide(emit: boolean = true) {\n        if (emit) {\n            this.onHide.emit({});\n        }\n\n        if (this.modal) {\n            this.disableModality();\n        }\n    }\n\n    close(event: Event) {\n        this.hide();\n        this.visibleChange.emit(false);\n        event.preventDefault();\n    }\n\n    enableModality() {\n        if (!this.mask) {\n            this.mask = this.renderer.createElement('div');\n            this.renderer.setStyle(this.mask, 'zIndex', String(parseInt((this.container as HTMLDivElement).style.zIndex) - 1));\n            DomHandler.addMultipleClasses(this.mask, 'p-component-overlay p-sidebar-mask p-component-overlay p-component-overlay-enter');\n\n            if (this.dismissible) {\n                this.maskClickListener = this.renderer.listen(this.mask, 'click', (event: any) => {\n                    if (this.dismissible) {\n                        this.close(event);\n                    }\n                });\n            }\n\n            this.renderer.appendChild(this.document.body, this.mask);\n            if (this.blockScroll) {\n                DomHandler.blockBodyScroll();\n            }\n        }\n    }\n\n    disableModality() {\n        if (this.mask) {\n            DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n            this.animationEndListener = this.renderer.listen(this.mask, 'animationend', this.destroyModal.bind(this));\n        }\n    }\n\n    destroyModal() {\n        this.unbindMaskClickListener();\n\n        if (this.mask) {\n            this.renderer.removeChild(this.document.body, this.mask);\n        }\n\n        if (this.blockScroll) {\n            DomHandler.unblockBodyScroll();\n        }\n\n        this.unbindAnimationEndListener();\n        this.mask = null;\n    }\n\n    onAnimationStart(event: any) {\n        switch (event.toState) {\n            case 'visible':\n                this.container = event.element;\n                this.appendContainer();\n                this.show();\n\n                if (this.closeOnEscape) {\n                    this.bindDocumentEscapeListener();\n                }\n                break;\n        }\n    }\n\n    onAnimationEnd(event: any) {\n        switch (event.toState) {\n            case 'void':\n                this.hide(false);\n                ZIndexUtils.clear(this.container);\n                this.unbindGlobalListeners();\n                break;\n        }\n    }\n\n    appendContainer() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);\n            else DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n\n    bindDocumentEscapeListener() {\n        const documentTarget: any = this.el ? this.el.nativeElement.ownerDocument : this.document;\n\n        this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', (event) => {\n            if (event.which == 27) {\n                if (parseInt((this.container as HTMLDivElement).style.zIndex) === ZIndexUtils.get(this.container)) {\n                    this.close(event);\n                }\n            }\n        });\n    }\n\n    unbindDocumentEscapeListener() {\n        if (this.documentEscapeListener) {\n            this.documentEscapeListener();\n            this.documentEscapeListener = null;\n        }\n    }\n\n    unbindMaskClickListener() {\n        if (this.maskClickListener) {\n            this.maskClickListener();\n            this.maskClickListener = null;\n        }\n    }\n\n    unbindGlobalListeners() {\n        this.unbindMaskClickListener();\n        this.unbindDocumentEscapeListener();\n    }\n\n    unbindAnimationEndListener() {\n        if (this.animationEndListener && this.mask) {\n            this.animationEndListener();\n            this.animationEndListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.initialized = false;\n\n        if (this.visible && this.modal) {\n            this.destroyModal();\n        }\n\n        if (this.appendTo && this.container) {\n            this.renderer.appendChild(this.el.nativeElement, this.container);\n        }\n\n        if (this.container && this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n\n        this.container = null;\n        this.unbindGlobalListeners();\n        this.unbindAnimationEndListener();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RippleModule, SharedModule, TimesIcon],\n    exports: [Sidebar, SharedModule],\n    declarations: [Sidebar]\n})\nexport class SidebarModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA8BA,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAEhH,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChH;;;AAGG;MAqEU,OAAO,CAAA;AA6JsB,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AA5JlJ;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;IACqC,WAAW,GAAY,KAAK,CAAC;AACrE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;IACqC,KAAK,GAAY,IAAI,CAAC;AAC9D;;;AAGG;IACqC,WAAW,GAAY,IAAI,CAAC;AACpE;;;AAGG;IACqC,aAAa,GAAY,IAAI,CAAC;AACtE;;;AAGG;IACqC,aAAa,GAAY,IAAI,CAAC;AACtE;;;AAGG;IACM,iBAAiB,GAAW,kCAAkC,CAAC;AACxE;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAmB,CAAC;KACnC;IACD,IAAI,OAAO,CAAC,GAAY,EAAA;AACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;KACvB;AACD;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAEvB,QAAA,QAAQ,KAAK;AACT,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,gBAAgB,GAAG,8BAA8B,CAAC;gBACvD,MAAM;AACV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,gBAAgB,GAAG,6BAA6B,CAAC;gBACtD,MAAM;AACV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,gBAAgB,GAAG,6BAA6B,CAAC;gBACtD,MAAM;AACV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,gBAAgB,GAAG,8BAA8B,CAAC;gBACvD,MAAM;AACb,SAAA;KACJ;AACD;;;AAGG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAEzB,QAAA,IAAI,KAAK;AAAE,YAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;KAC7C;AAE+B,IAAA,SAAS,CAAuC;AAChF;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;;AAIG;AACO,IAAA,aAAa,GAA0B,IAAI,YAAY,EAAW,CAAC;AAE7E,IAAA,WAAW,CAAsB;AAEjC,IAAA,QAAQ,CAAsB;IAE9B,SAAS,GAAW,MAAM,CAAC;IAE3B,WAAW,GAAY,KAAK,CAAC;AAE7B,IAAA,SAAS,CAA2B;IAEpC,gBAAgB,GAAQ,8BAA8B,CAAC;AAEvD,IAAA,IAAI,CAA2B;AAE/B,IAAA,iBAAiB,CAAe;AAEhC,IAAA,sBAAsB,CAAe;AAErC,IAAA,oBAAoB,CAAe;AAEnC,IAAA,eAAe,CAA6B;AAE5C,IAAA,cAAc,CAA6B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,gBAAgB,CAA6B;IAE7C,WAAsC,CAAA,QAAkB,EAAS,EAAc,EAAS,QAAmB,EAAS,EAAqB,EAAS,MAAqB,EAAA;QAAjI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAE3K,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AACV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AACV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AACV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC1B,QAAA,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;AACzB,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpB,SAAA;KACJ;IAED,IAAI,GAAA;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzF,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACjC;IAED,IAAI,CAAC,OAAgB,IAAI,EAAA;AACrB,QAAA,IAAI,IAAI,EAAE;AACN,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,KAAK,CAAC,KAAY,EAAA;QACd,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,cAAc,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAE,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACnH,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,kFAAkF,CAAC,CAAC;YAE7H,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,KAAU,KAAI;oBAC7E,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,wBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrB,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AAED,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,UAAU,CAAC,eAAe,EAAE,CAAC;AAChC,aAAA;AACJ,SAAA;KACJ;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,2BAA2B,CAAC,CAAC;YAC5D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7G,SAAA;KACJ;IAED,YAAY,GAAA;QACR,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5D,SAAA;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,UAAU,CAAC,iBAAiB,EAAE,CAAC;AAClC,SAAA;QAED,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;QACvB,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,IAAI,EAAE,CAAC;gBAEZ,IAAI,IAAI,CAAC,aAAa,EAAE;oBACpB,IAAI,CAAC,0BAA0B,EAAE,CAAC;AACrC,iBAAA;gBACD,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAU,EAAA;QACrB,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjB,gBAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClC,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,MAAM;AACb,SAAA;KACJ;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;AAAE,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;gBACvF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9D,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,MAAM,cAAc,GAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;AAE1F,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,SAAS,EAAE,CAAC,KAAK,KAAI;AACpF,YAAA,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE;AACnB,gBAAA,IAAI,QAAQ,CAAE,IAAI,CAAC,SAA4B,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC/F,oBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrB,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,uBAAuB,GAAA;QACnB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACjC,SAAA;KACJ;IAED,qBAAqB,GAAA;QACjB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACvC;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,IAAI,EAAE;YACxC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAEzB,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE;YAC5B,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,0BAA0B,EAAE,CAAC;KACrC;AAlWQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kBA6JI,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA7JnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,qGAUI,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAoBhB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,eAAe,CAKf,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,gBAAgB,CAKhB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,qDAKhB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAKhB,gBAAgB,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAsDnB,aAAa,EA/KpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,iuCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA+WmD,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,UAAA,EA9WjD,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQxJ,OAAO,EAAA,UAAA,EAAA,CAAA;kBApEnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDT,IAAA,CAAA,EAAA,UAAA,EACW,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAChJ,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,iuCAAA,CAAA,EAAA,CAAA;;0BA+JY,MAAM;2BAAC,QAAQ,CAAA;sJAxJnB,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAUO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAyBO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAS0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAKpB,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,aAAa,EAAA,CAAA;sBAAtB,MAAM;;MA6OE,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EA1Wb,YAAA,EAAA,CAAA,OAAO,CAsWN,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,CAtWpD,EAAA,OAAA,EAAA,CAAA,OAAO,EAuWG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGtB,aAAa,EAAA,OAAA,EAAA,CAJZ,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAC1C,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGtB,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,CAAC;AAC9D,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC;oBAChC,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;ACldD;;AAEG;;;;"}