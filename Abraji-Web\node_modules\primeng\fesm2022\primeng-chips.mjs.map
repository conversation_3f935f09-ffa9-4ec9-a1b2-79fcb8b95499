{"version": 3, "file": "primeng-chips.mjs", "sources": ["../../src/app/components/chips/chips.ts", "../../src/app/components/chips/primeng-chips.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    Output,\n    QueryList,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    forwardRef,\n    numberAttribute\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { ChipsAddEvent, ChipsClickEvent, ChipsRemoveEvent } from './chips.interface';\n\nexport const CHIPS_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Chips),\n    multi: true\n};\n/**\n * Chips groups a collection of contents in tabs.\n * @group Components\n */\n@Component({\n    selector: 'p-chips',\n    template: `\n        <div\n            [ngClass]=\"{\n                'p-chips p-component p-input-wrapper': true,\n                'p-disabled': disabled,\n                'p-focus': focused,\n                'p-inputwrapper-filled': (value && value.length) || (this.inputViewChild?.nativeElement.value && this.inputViewChild?.nativeElement.value.length),\n                'p-inputwrapper-focus': focused\n            }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'chips'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <ul\n                #container\n                [ngClass]=\"{ 'p-inputtext p-chips-multiple-container': true, 'p-variant-filled': variant === 'filled' || config.inputStyle() === 'filled' }\"\n                tabindex=\"-1\"\n                role=\"listbox\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                [attr.aria-orientation]=\"'horizontal'\"\n                (click)=\"onWrapperClick()\"\n                (focus)=\"onContainerFocus()\"\n                (blur)=\"onContainerBlur()\"\n                (keydown)=\"onContainerKeyDown($event)\"\n                [attr.data-pc-section]=\"'container'\"\n            >\n                <li\n                    #token\n                    *ngFor=\"let item of value; let i = index\"\n                    [attr.id]=\"id + '_chips_item_' + i\"\n                    role=\"option\"\n                    [attr.ariaLabel]=\"item\"\n                    [attr.aria-selected]=\"true\"\n                    [attr.aria-setsize]=\"value.length\"\n                    [attr.aria-posinset]=\"i + 1\"\n                    [attr.data-p-focused]=\"focusedIndex === i\"\n                    [ngClass]=\"{ 'p-chips-token': true, 'p-focus': focusedIndex === i }\"\n                    (click)=\"onItemClick($event, item)\"\n                    [attr.data-pc-section]=\"'token'\"\n                >\n                    <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                    <span *ngIf=\"!itemTemplate\" class=\"p-chips-token-label\" [attr.data-pc-section]=\"'label'\">{{ field ? resolveFieldData(item, field) : item }}</span>\n                    <ng-container *ngIf=\"!disabled\">\n                        <TimesCircleIcon [styleClass]=\"'p-chips-token-icon'\" *ngIf=\"!removeTokenIconTemplate\" (click)=\"removeItem($event, i)\" [attr.data-pc-section]=\"'removeTokenIcon'\" [attr.aria-hidden]=\"true\" />\n                        <span *ngIf=\"removeTokenIconTemplate\" class=\"p-chips-token-icon\" (click)=\"removeItem($event, i)\" [attr.data-pc-section]=\"'removeTokenIcon'\" [attr.aria-hidden]=\"true\">\n                            <ng-template *ngTemplateOutlet=\"removeTokenIconTemplate\"></ng-template>\n                        </span>\n                    </ng-container>\n                </li>\n                <li class=\"p-chips-input-token\" [ngClass]=\"{ 'p-chips-clearable': showClear && !disabled }\" [attr.data-pc-section]=\"'inputToken'\" role=\"option\">\n                    <input\n                        #inputtext\n                        type=\"text\"\n                        [attr.id]=\"inputId\"\n                        [attr.maxlength]=\"maxLength\"\n                        [attr.placeholder]=\"value && value.length ? null : placeholder\"\n                        [attr.tabindex]=\"tabindex\"\n                        (keydown)=\"onKeyDown($event)\"\n                        (input)=\"onInput()\"\n                        (paste)=\"onPaste($event)\"\n                        (focus)=\"onInputFocus($event)\"\n                        (blur)=\"onInputBlur($event)\"\n                        [disabled]=\"disabled || isMaxedOut\"\n                        [ngStyle]=\"inputStyle\"\n                        [class]=\"inputStyleClass\"\n                        pAutoFocus\n                        [autofocus]=\"autofocus\"\n                    />\n                </li>\n                <li *ngIf=\"value != null && filled && !disabled && showClear\">\n                    <TimesIcon *ngIf=\"!clearIconTemplate\" [styleClass]=\"'p-chips-clear-icon'\" (click)=\"clear()\" />\n                    <span *ngIf=\"clearIconTemplate\" class=\"p-chips-clear-icon\" (click)=\"clear()\">\n                        <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                    </span>\n                </li>\n            </ul>\n        </div>\n    `,\n    host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': 'focused',\n        '[class.p-chips-clearable]': 'showClear'\n    },\n    providers: [CHIPS_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./chips.css']\n})\nexport class Chips implements AfterContentInit, ControlValueAccessor {\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Name of the property to display on a chip.\n     * @group Props\n     */\n    @Input() field: string | undefined;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    @Input() placeholder: string | undefined;\n    /**\n     * Maximum number of entries allowed.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) max: number | undefined;\n    /**\n     * Maximum length of a chip.\n     * @group Props\n     */\n    @Input() maxLength: number | undefined;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Whether to allow duplicate values or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) allowDuplicate: boolean = true;\n    /**\n     * Defines whether duplication check should be case-sensitive\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) caseSensitiveDuplication: boolean = true;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    @Input() inputStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    @Input() inputStyleClass: string | undefined;\n    /**\n     * Whether to add an item on tab key press.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) addOnTab: boolean | undefined;\n    /**\n     * Whether to add an item when the input loses focus.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) addOnBlur: boolean | undefined;\n    /**\n     * Separator char to add an item when pressed in addition to the enter key.\n     * @group Props\n     */\n    @Input() separator: string | RegExp | undefined;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean = false;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Callback to invoke on chip add.\n     * @param {ChipsAddEvent} event - Custom chip add event.\n     * @group Emits\n     */\n    @Output() onAdd: EventEmitter<ChipsAddEvent> = new EventEmitter<ChipsAddEvent>();\n    /**\n     * Callback to invoke on chip remove.\n     * @param {ChipsRemoveEvent} event - Custom chip remove event.\n     * @group Emits\n     */\n    @Output() onRemove: EventEmitter<ChipsRemoveEvent> = new EventEmitter<ChipsRemoveEvent>();\n    /**\n     * Callback to invoke on focus of input field.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke on blur of input field.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke on chip clicked.\n     * @param {ChipsClickEvent} event - Custom chip click event.\n     * @group Emits\n     */\n    @Output() onChipClick: EventEmitter<ChipsClickEvent> = new EventEmitter<ChipsClickEvent>();\n    /**\n     * Callback to invoke on clear token clicked.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<any> = new EventEmitter<any>();\n\n    @ViewChild('inputtext') inputViewChild!: ElementRef;\n\n    @ViewChild('container') containerViewChild!: ElementRef;\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<any>;\n\n    public itemTemplate: Nullable<TemplateRef<any>>;\n\n    removeTokenIconTemplate: Nullable<TemplateRef<any>>;\n\n    clearIconTemplate: Nullable<TemplateRef<any>>;\n\n    value: any;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    valueChanged: Nullable<boolean>;\n\n    id = UniqueComponentId();\n\n    focused: Nullable<boolean>;\n\n    focusedIndex: Nullable<number>;\n\n    filled: Nullable<boolean>;\n\n    get focusedOptionId() {\n        return this.focusedIndex !== null ? `${this.id}_chips_item_${this.focusedIndex}` : null;\n    }\n\n    get isMaxedOut(): boolean {\n        return this.max && this.value && this.max === this.value.length;\n    }\n\n    constructor(@Inject(DOCUMENT) private document: Document, public el: ElementRef, public cd: ChangeDetectorRef, public config: PrimeNGConfig) {}\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'removetokenicon':\n                    this.removeTokenIconTemplate = item.template;\n                    break;\n\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n\n        this.updateFilledState();\n    }\n\n    onWrapperClick() {\n        this.inputViewChild?.nativeElement.focus();\n    }\n\n    onContainerFocus() {\n        this.focused = true;\n    }\n\n    onContainerBlur() {\n        this.focusedIndex = -1;\n        this.focused = false;\n    }\n\n    onContainerKeyDown(event) {\n        switch (event.code) {\n            case 'ArrowLeft':\n                this.onArrowLeftKeyOn();\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKeyOn();\n                break;\n\n            case 'Backspace':\n                this.onBackspaceKeyOn(event);\n                break;\n\n            case 'Space':\n                if (this.focusedIndex !== null && this.value && this.value.length > 0) {\n                    this.onItemClick(event, this.value[this.focusedIndex]);\n                }\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onArrowLeftKeyOn() {\n        if (this.inputViewChild.nativeElement.value.length === 0 && this.value && this.value.length > 0) {\n            this.focusedIndex = this.focusedIndex === null ? this.value.length - 1 : this.focusedIndex - 1;\n            if (this.focusedIndex < 0) this.focusedIndex = 0;\n        }\n    }\n\n    onArrowRightKeyOn() {\n        if (this.inputViewChild.nativeElement.value.length === 0 && this.value && this.value.length > 0) {\n            if (this.focusedIndex === this.value.length - 1) {\n                this.focusedIndex = null;\n                this.inputViewChild?.nativeElement.focus();\n            } else {\n                this.focusedIndex++;\n            }\n        }\n    }\n\n    onBackspaceKeyOn(event) {\n        if (this.focusedIndex !== null) {\n            this.removeItem(event, this.focusedIndex);\n        }\n    }\n\n    onInput() {\n        this.updateFilledState();\n        this.focusedIndex = null;\n    }\n\n    onPaste(event: any) {\n        if (!this.disabled) {\n            if (this.separator) {\n                const pastedData: string = (event.clipboardData || (this.document.defaultView as any)['clipboardData']).getData('Text');\n                pastedData.split(this.separator).forEach((val: any) => {\n                    this.addItem(event, val, true);\n                });\n                this.inputViewChild.nativeElement.value = '';\n            }\n\n            this.updateFilledState();\n        }\n    }\n\n    updateFilledState() {\n        if (!this.value || this.value.length === 0) {\n            this.filled = this.inputViewChild && this.inputViewChild.nativeElement && this.inputViewChild.nativeElement.value != '';\n        } else {\n            this.filled = true;\n        }\n    }\n\n    onItemClick(event: Event, item: any) {\n        this.onChipClick.emit({\n            originalEvent: event,\n            value: item\n        });\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.updateMaxedOut();\n        this.updateFilledState();\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    resolveFieldData(data: any, field: string): any {\n        if (data && field) {\n            if (field.indexOf('.') == -1) {\n                return data[field];\n            } else {\n                let fields: string[] = field.split('.');\n                let value = data;\n                for (var i = 0, len = fields.length; i < len; ++i) {\n                    value = value[fields[i]];\n                }\n                return value;\n            }\n        } else {\n            return null;\n        }\n    }\n\n    onInputFocus(event: FocusEvent) {\n        this.focused = true;\n        this.focusedIndex = null;\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event: FocusEvent) {\n        this.focused = false;\n        this.focusedIndex = null;\n        if (this.addOnBlur && this.inputViewChild.nativeElement.value) {\n            this.addItem(event, this.inputViewChild.nativeElement.value, false);\n        }\n        this.onModelTouched();\n        this.onBlur.emit(event);\n    }\n\n    removeItem(event: Event, index: number): void {\n        if (this.disabled) {\n            return;\n        }\n\n        let removedItem = this.value[index];\n        this.value = this.value.filter((val: any, i: number) => i != index);\n        this.focusedIndex = null;\n        this.inputViewChild.nativeElement.focus();\n\n        this.onModelChange(this.value);\n        this.onRemove.emit({\n            originalEvent: event,\n            value: removedItem\n        });\n        this.updateFilledState();\n        this.updateMaxedOut();\n    }\n\n    addItem(event: Event, item: string, preventDefault: boolean): void {\n        this.value = this.value || [];\n\n        if (item && item.trim().length) {\n            const newItemIsDuplicate = this.caseSensitiveDuplication ? this.value.includes(item) : this.value.some((val) => val.toLowerCase() === item.toLowerCase());\n\n            if ((this.allowDuplicate || !newItemIsDuplicate) && !this.isMaxedOut) {\n                this.value = [...this.value, item];\n                this.onModelChange(this.value);\n                this.onAdd.emit({\n                    originalEvent: event,\n                    value: item\n                });\n            }\n        }\n\n        this.updateFilledState();\n        this.updateMaxedOut();\n        this.inputViewChild.nativeElement.value = '';\n\n        if (preventDefault) {\n            event.preventDefault();\n        }\n    }\n\n    /**\n     * Callback to invoke on filter reset.\n     * @group Method\n     */\n    public clear() {\n        this.value = null;\n        this.updateFilledState();\n        this.onModelChange(this.value);\n        this.updateMaxedOut();\n        this.onClear.emit();\n    }\n\n    onKeyDown(event) {\n        const inputValue = event.target.value;\n\n        switch (event.code) {\n            case 'Backspace':\n                if (inputValue.length === 0 && this.value && this.value.length > 0) {\n                    if (this.focusedIndex !== null) {\n                        this.removeItem(event, this.focusedIndex);\n                    } else this.removeItem(event, this.value.length - 1);\n                }\n\n                break;\n\n            case 'Enter':\n            case 'NumpadEnter':\n                if (inputValue && inputValue.trim().length && !this.isMaxedOut) {\n                    this.addItem(event, inputValue, true);\n                }\n\n                break;\n\n            case 'Tab':\n                if (this.addOnTab && inputValue && inputValue.trim().length && !this.isMaxedOut) {\n                    this.addItem(event, inputValue, true);\n                    event.preventDefault();\n                }\n\n                break;\n\n            case 'ArrowLeft':\n                if (inputValue.length === 0 && this.value && this.value.length > 0) {\n                    this.containerViewChild?.nativeElement.focus();\n                }\n\n                break;\n\n            case 'ArrowRight':\n                event.stopPropagation();\n                break;\n\n            default:\n                if (this.separator) {\n                    if (this.separator === event.key || event.key.match(this.separator)) {\n                        this.addItem(event, inputValue, true);\n                    }\n                }\n\n                break;\n        }\n    }\n\n    updateMaxedOut(): void {\n        if (this.inputViewChild && this.inputViewChild.nativeElement) {\n            if (this.isMaxedOut) {\n                // Calling `blur` is necessary because firefox does not call `onfocus` events\n                // for disabled inputs, unlike chromium browsers.\n                this.inputViewChild.nativeElement.blur();\n                this.inputViewChild.nativeElement.disabled = true;\n            } else {\n                if (this.disabled) {\n                    this.inputViewChild.nativeElement.blur();\n                }\n\n                this.inputViewChild.nativeElement.disabled = this.disabled || false;\n            }\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, InputTextModule, SharedModule, AutoFocusModule, TimesCircleIcon, TimesIcon],\n    exports: [Chips, InputTextModule, SharedModule],\n    declarations: [Chips]\n})\nexport class ChipsModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AA+Ba,MAAA,oBAAoB,GAAQ;AACrC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,KAAK,CAAC;AACpC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MA+FU,KAAK,CAAA;AAkLwB,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AAjLtH;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACoC,IAAA,GAAG,CAAqB;AAC/D;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACqC,cAAc,GAAY,IAAI,CAAC;AACvE;;;AAGG;IACqC,wBAAwB,GAAY,IAAI,CAAC;AACjF;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;AACM,IAAA,SAAS,CAA8B;AAChD;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;;AAIG;AACO,IAAA,KAAK,GAAgC,IAAI,YAAY,EAAiB,CAAC;AACjF;;;;AAIG;AACO,IAAA,QAAQ,GAAmC,IAAI,YAAY,EAAoB,CAAC;AAC1F;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,WAAW,GAAkC,IAAI,YAAY,EAAmB,CAAC;AAC3F;;;AAGG;AACO,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAO,CAAC;AAEvC,IAAA,cAAc,CAAc;AAE5B,IAAA,kBAAkB,CAAc;AAExB,IAAA,SAAS,CAAkB;AAEpD,IAAA,YAAY,CAA6B;AAEhD,IAAA,uBAAuB,CAA6B;AAEpD,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,KAAK,CAAM;AAEX,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,YAAY,CAAoB;IAEhC,EAAE,GAAG,iBAAiB,EAAE,CAAC;AAEzB,IAAA,OAAO,CAAoB;AAE3B,IAAA,YAAY,CAAmB;AAE/B,IAAA,MAAM,CAAoB;AAE1B,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,IAAI,CAAC,YAAY,KAAK,IAAI,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,YAAA,EAAe,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC;KAC3F;AAED,IAAA,IAAI,UAAU,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;KACnE;AAED,IAAA,WAAA,CAAsC,QAAkB,EAAS,EAAc,EAAS,EAAqB,EAAS,MAAqB,EAAA;QAArG,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAE/I,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,iBAAiB;AAClB,oBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC7C,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC5B;IAED,cAAc,GAAA;AACV,QAAA,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;KAC9C;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;KACvB;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;KACxB;AAED,IAAA,kBAAkB,CAAC,KAAK,EAAA;QACpB,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;gBACZ,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,YAAY;gBACb,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAC7B,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACnE,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AAC1D,iBAAA;gBACD,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;IAED,gBAAgB,GAAA;QACZ,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7F,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,KAAK,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;AAC/F,YAAA,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC;AAAE,gBAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;AACpD,SAAA;KACJ;IAED,iBAAiB,GAAA;QACb,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7F,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7C,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,gBAAA,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;AAC9C,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAK,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7C,SAAA;KACJ;IAED,OAAO,GAAA;QACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;KAC5B;AAED,IAAA,OAAO,CAAC,KAAU,EAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,MAAM,UAAU,GAAW,CAAC,KAAK,CAAC,aAAa,IAAK,IAAI,CAAC,QAAQ,CAAC,WAAmB,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACxH,gBAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,GAAQ,KAAI;oBAClD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACnC,iBAAC,CAAC,CAAC;gBACH,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AAChD,aAAA;YAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5B,SAAA;KACJ;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;AAC3H,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;KACJ;IAED,WAAW,CAAC,KAAY,EAAE,IAAS,EAAA;AAC/B,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AAClB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,KAAK,EAAE,IAAI;AACd,SAAA,CAAC,CAAC;KACN;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,gBAAgB,CAAC,IAAS,EAAE,KAAa,EAAA;QACrC,IAAI,IAAI,IAAI,KAAK,EAAE;YACf,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;AAC1B,gBAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;AACtB,aAAA;AAAM,iBAAA;gBACH,IAAI,MAAM,GAAa,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACxC,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;oBAC/C,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,iBAAA;AACD,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAiB,EAAA;AAC1B,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE;AAC3D,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACvE,SAAA;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;IAED,UAAU,CAAC,KAAY,EAAE,KAAa,EAAA;QAClC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AACpE,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAE1C,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,KAAK,EAAE,WAAW;AACrB,SAAA,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB;AAED,IAAA,OAAO,CAAC,KAAY,EAAE,IAAY,EAAE,cAAuB,EAAA;QACvD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;QAE9B,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE;AAC5B,YAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAE1J,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,kBAAkB,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClE,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACnC,gBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,gBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACZ,oBAAA,aAAa,EAAE,KAAK;AACpB,oBAAA,KAAK,EAAE,IAAI;AACd,iBAAA,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AAE7C,QAAA,IAAI,cAAc,EAAE;YAChB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED;;;AAGG;IACI,KAAK,GAAA;AACR,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACvB;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;AACX,QAAA,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;QAEtC,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAChE,oBAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;wBAC5B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7C,qBAAA;;AAAM,wBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACxD,iBAAA;gBAED,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBAC5D,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AACzC,iBAAA;gBAED,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBAC7E,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;oBACtC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBAED,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAChE,oBAAA,IAAI,CAAC,kBAAkB,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;AAClD,iBAAA;gBAED,MAAM;AAEV,YAAA,KAAK,YAAY;gBACb,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,MAAM;AAEV,YAAA;gBACI,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,oBAAA,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;wBACjE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AACzC,qBAAA;AACJ,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;IAED,cAAc,GAAA;QACV,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;YAC1D,IAAI,IAAI,CAAC,UAAU,EAAE;;;AAGjB,gBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBACzC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrD,aAAA;AAAM,iBAAA;gBACH,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,oBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;AAC5C,iBAAA;AAED,gBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;AACvE,aAAA;AACJ,SAAA;KACJ;AA3dQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAK,kBAkLM,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAlLnB,KAAK,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAeM,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAehB,eAAe,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAoBf,eAAe,CAUf,EAAA,OAAA,EAAA,SAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAgB,CAKhB,EAAA,wBAAA,EAAA,CAAA,0BAAA,EAAA,0BAAA,EAAA,gBAAgB,CAehB,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,yCAKhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAUhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,SAAA,EAAA,yBAAA,EAAA,WAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,SAAA,EAzGzB,CAAC,oBAAoB,CAAC,EAuJhB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA9OpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgFT,EA2euE,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,oxBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,iFAAE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA/dzF,KAAK,EAAA,UAAA,EAAA,CAAA;kBA9FjB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,SAAS,EACT,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgFT,EACK,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACjC,wBAAA,+BAA+B,EAAE,QAAQ;AACzC,wBAAA,8BAA8B,EAAE,SAAS;AACzC,wBAAA,2BAA2B,EAAE,WAAW;qBAC3C,EACU,SAAA,EAAA,CAAC,oBAAoB,CAAC,EAChB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,oxBAAA,CAAA,EAAA,CAAA;;0BAqLxB,MAAM;2BAAC,QAAQ,CAAA;8HA7KnB,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKiC,GAAG,EAAA,CAAA;sBAAzC,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,wBAAwB,EAAA,CAAA;sBAA/D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAMI,KAAK,EAAA,CAAA;sBAAd,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAKG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAEiB,cAAc,EAAA,CAAA;sBAArC,SAAS;uBAAC,WAAW,CAAA;gBAEE,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEU,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAiVrB,WAAW,CAAA;uGAAX,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,iBAneX,KAAK,CAAA,EAAA,OAAA,EAAA,CA+dJ,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,CAAA,EAAA,OAAA,EAAA,CA/dzF,KAAK,EAgeG,eAAe,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;AAGrC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,EAJV,OAAA,EAAA,CAAA,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,EACjF,eAAe,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGrC,WAAW,EAAA,UAAA,EAAA,CAAA;kBALvB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,CAAC;AACnG,oBAAA,OAAO,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,YAAY,CAAC;oBAC/C,YAAY,EAAE,CAAC,KAAK,CAAC;AACxB,iBAAA,CAAA;;;ACxmBD;;AAEG;;;;"}