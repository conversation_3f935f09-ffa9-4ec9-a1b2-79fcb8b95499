/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { setActiveConsumer } from '@angular/core/primitives/signals';
import { hasInSkipHydrationBlockFlag } from '../hydration/skip_hydration';
import { assertDefined } from '../util/assert';
import { assertLContainer, assertLView, assertTNodeForLView } from './assert';
import { renderView } from './instructions/render';
import { createLView } from './instructions/shared';
import { CONTAINER_HEADER_OFFSET, NATIVE } from './interfaces/container';
import { DECLARATION_LCONTAINER, FLAGS, HYDRATION, QUERIES, RENDERER, T_HOST, TVIEW, } from './interfaces/view';
import { addViewToDOM, destroyLView, detachView, getBeforeNodeForView, insertView, nativeParentNode, } from './node_manipulation';
export function createAndRenderEmbeddedLView(declarationLView, templateTNode, context, options) {
    const prevConsumer = setActiveConsumer(null);
    try {
        const embeddedTView = templateTNode.tView;
        ngDevMode && assertDefined(embeddedTView, 'TView must be defined for a template node.');
        ngDevMode && assertTNodeForLView(templateTNode, declarationLView);
        // Embedded views follow the change detection strategy of the view they're declared in.
        const isSignalView = declarationLView[FLAGS] & 4096 /* LViewFlags.SignalView */;
        const viewFlags = isSignalView ? 4096 /* LViewFlags.SignalView */ : 16 /* LViewFlags.CheckAlways */;
        const embeddedLView = createLView(declarationLView, embeddedTView, context, viewFlags, null, templateTNode, null, null, options?.injector ?? null, options?.embeddedViewInjector ?? null, options?.dehydratedView ?? null);
        const declarationLContainer = declarationLView[templateTNode.index];
        ngDevMode && assertLContainer(declarationLContainer);
        embeddedLView[DECLARATION_LCONTAINER] = declarationLContainer;
        const declarationViewLQueries = declarationLView[QUERIES];
        if (declarationViewLQueries !== null) {
            embeddedLView[QUERIES] = declarationViewLQueries.createEmbeddedView(embeddedTView);
        }
        // execute creation mode of a view
        renderView(embeddedTView, embeddedLView, context);
        return embeddedLView;
    }
    finally {
        setActiveConsumer(prevConsumer);
    }
}
export function getLViewFromLContainer(lContainer, index) {
    const adjustedIndex = CONTAINER_HEADER_OFFSET + index;
    // avoid reading past the array boundaries
    if (adjustedIndex < lContainer.length) {
        const lView = lContainer[adjustedIndex];
        ngDevMode && assertLView(lView);
        return lView;
    }
    return undefined;
}
/**
 * Returns whether an elements that belong to a view should be
 * inserted into the DOM. For client-only cases, DOM elements are
 * always inserted. For hydration cases, we check whether serialized
 * info is available for a view and the view is not in a "skip hydration"
 * block (in which case view contents was re-created, thus needing insertion).
 */
export function shouldAddViewToDom(tNode, dehydratedView) {
    return (!dehydratedView || dehydratedView.firstChild === null || hasInSkipHydrationBlockFlag(tNode));
}
export function addLViewToLContainer(lContainer, lView, index, addToDOM = true) {
    const tView = lView[TVIEW];
    // Insert into the view tree so the new view can be change-detected
    insertView(tView, lView, lContainer, index);
    // Insert elements that belong to this view into the DOM tree
    if (addToDOM) {
        const beforeNode = getBeforeNodeForView(index, lContainer);
        const renderer = lView[RENDERER];
        const parentRNode = nativeParentNode(renderer, lContainer[NATIVE]);
        if (parentRNode !== null) {
            addViewToDOM(tView, lContainer[T_HOST], renderer, lView, parentRNode, beforeNode);
        }
    }
    // When in hydration mode, reset the pointer to the first child in
    // the dehydrated view. This indicates that the view was hydrated and
    // further attaching/detaching should work with this view as normal.
    const hydrationInfo = lView[HYDRATION];
    if (hydrationInfo !== null && hydrationInfo.firstChild !== null) {
        hydrationInfo.firstChild = null;
    }
}
export function removeLViewFromLContainer(lContainer, index) {
    const lView = detachView(lContainer, index);
    if (lView !== undefined) {
        destroyLView(lView[TVIEW], lView);
    }
    return lView;
}
//# sourceMappingURL=data:application/json;base64,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