/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export class AsyncStackTaggingZoneSpec {
    constructor(namePrefix, consoleAsyncStackTaggingImpl = console) {
        this.name = 'asyncStackTagging for ' + namePrefix;
        this.createTask = consoleAsyncStackTaggingImpl?.createTask ?? (() => null);
    }
    onScheduleTask(delegate, _current, target, task) {
        task.consoleTask = this.createTask(`Zone - ${task.source || task.type}`);
        return delegate.scheduleTask(target, task);
    }
    onInvokeTask(delegate, _currentZone, targetZone, task, applyThis, applyArgs) {
        let ret;
        if (task.consoleTask) {
            ret = task.consoleTask.run(() => delegate.invokeTask(targetZone, task, applyThis, applyArgs));
        }
        else {
            ret = delegate.invokeTask(targetZone, task, applyThis, applyArgs);
        }
        return ret;
    }
}
//# sourceMappingURL=data:application/json;base64,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