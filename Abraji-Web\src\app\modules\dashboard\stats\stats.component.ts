import { Component, inject, OnInit } from '@angular/core';
import {
  Statistics,
  CardsResponse,
  CardStats,
  TransactionsResponse,
} from '../../../core/dashboard-services/api/stats';
import { StatsService } from '../../../core/dashboard-services/services/stats.service';
import { ToastService } from '../../shared/toast/toast.service';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { Router } from '@angular/router';
import { UserForm } from '../../../core/user-services/api/users';

@Component({
  selector: 'app-stats',
  templateUrl: './stats.component.html',
  styleUrls: ['./stats.component.scss'],
})
export class StatsComponent implements OnInit {
  isLoading = false;
  statistics: Statistics | null = null;
  cards: CardStats[] | null = null;
  transactions: TransactionsResponse | null = null;

  constructor(
    private statsService: StatsService,
    private toastService: ToastService,
    private localStorageService: LocalStorageService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.statsService.getStats().subscribe((data) => {
      this.statistics = data;
    });

    this.statsService.getCards().subscribe((data) => {
      this.cards = data.cards;
    });

    this.statsService.getTransactions().subscribe((data: any) => {
      this.transactions = data.original;
    });
  }

  getStatus(): void {
    this.isLoading = true;
    this.statsService.getStats().subscribe({
      next: (data: any) => {
        this.statistics = data;
        console.log('data: ' + data);
        // Create a toast message
        this.toastService.addToast(
          'success',
          'Success',
          'Statistics fetched successfully'
        );
        console.log('toastService: ' + this.toastService);
      },
      complete: () => {
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error fetching statistics', error);
      },
    });
  }

  // Navigation
  navigateToAllUsers(): void {
    const filterData = {};
    this.router.navigate(['/users/users-list'], { state: { data: filterData }});
  }

  navigateToActiveUsers(): void {
    const filterData = {
        status: 1,
    };
    this.router.navigate(['/users/users-list'], { state: { data: filterData }});
  }

  navigateToExpiredUsers(): void {
    const filterData = {
        status: 2,
    };
    this.router.navigate(['/users/users-list'], { state: { data: filterData }});
  }

  navigateToExpiringSoonUsers(): void {
    const filterData = {
        status: 4,
    };
    this.router.navigate(['/users/users-list'], { state: { data: filterData }});
  }

  navigateToExpiringTodayUsers(): void {
    const filterData = {
        status: 5,
    };
    this.router.navigate(['/users/users-list'], { state: { data: filterData }});
  }

  navigateToOnlineUsers(): void {
    const filterData = {
    };
    this.router.navigate(['/users/online-users'], { state: { data: filterData }});
  }

  navigateToCardsProfile(profile_id: number): void {
    this.router.navigate(['/cards'], { state: { data: { profile_id }}});
  }

}
