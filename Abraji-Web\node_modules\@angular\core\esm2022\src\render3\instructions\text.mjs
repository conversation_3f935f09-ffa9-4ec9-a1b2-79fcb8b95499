/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { validateMatchingNode } from '../../hydration/error_handling';
import { locateNextRNode } from '../../hydration/node_lookup_utils';
import { isDisconnectedNode, markRNodeAsClaimedByHydration } from '../../hydration/utils';
import { isDetachedByI18n } from '../../i18n/utils';
import { assertEqual, assertIndexInRange } from '../../util/assert';
import { HEADER_OFFSET, HYDRATION, RENDERER } from '../interfaces/view';
import { appendChild, createTextNode } from '../node_manipulation';
import { getBindingIndex, getLView, getTView, isInSkipHydrationBlock, lastNodeWasCreated, setCurrentTNode, wasLastNodeCreated, } from '../state';
import { getOrCreateTNode } from './shared';
/**
 * Create static text node
 *
 * @param index Index of the node in the data array
 * @param value Static string value to write.
 *
 * @codeGenApi
 */
export function ɵɵtext(index, value = '') {
    const lView = getLView();
    const tView = getTView();
    const adjustedIndex = index + HEADER_OFFSET;
    ngDevMode &&
        assertEqual(getBindingIndex(), tView.bindingStartIndex, 'text nodes should be created before any bindings');
    ngDevMode && assertIndexInRange(lView, adjustedIndex);
    const tNode = tView.firstCreatePass
        ? getOrCreateTNode(tView, adjustedIndex, 1 /* TNodeType.Text */, value, null)
        : tView.data[adjustedIndex];
    const textNative = _locateOrCreateTextNode(tView, lView, tNode, value, index);
    lView[adjustedIndex] = textNative;
    if (wasLastNodeCreated()) {
        appendChild(tView, lView, textNative, tNode);
    }
    // Text nodes are self closing.
    setCurrentTNode(tNode, false);
}
let _locateOrCreateTextNode = (tView, lView, tNode, value, index) => {
    lastNodeWasCreated(true);
    return createTextNode(lView[RENDERER], value);
};
/**
 * Enables hydration code path (to lookup existing elements in DOM)
 * in addition to the regular creation mode of text nodes.
 */
function locateOrCreateTextNodeImpl(tView, lView, tNode, value, index) {
    const hydrationInfo = lView[HYDRATION];
    const isNodeCreationMode = !hydrationInfo ||
        isInSkipHydrationBlock() ||
        isDetachedByI18n(tNode) ||
        isDisconnectedNode(hydrationInfo, index);
    lastNodeWasCreated(isNodeCreationMode);
    // Regular creation mode.
    if (isNodeCreationMode) {
        return createTextNode(lView[RENDERER], value);
    }
    // Hydration mode, looking up an existing element in DOM.
    const textNative = locateNextRNode(hydrationInfo, tView, lView, tNode);
    ngDevMode && validateMatchingNode(textNative, Node.TEXT_NODE, null, lView, tNode);
    ngDevMode && markRNodeAsClaimedByHydration(textNative);
    return textNative;
}
export function enableLocateOrCreateTextNodeImpl() {
    _locateOrCreateTextNode = locateOrCreateTextNodeImpl;
}
//# sourceMappingURL=data:application/json;base64,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