{"hash": "faaa1143", "configHash": "e6e47b23", "lockfileHash": "bd40ee74", "browserHash": "44ef0f83", "optimized": {"@angular/common": {"src": "../../../../../node_modules/@angular/common/fesm2022/common.mjs", "file": "@angular_common.js", "fileHash": "fb0f66fa", "needsInterop": false}, "@angular/common/http": {"src": "../../../../../node_modules/@angular/common/fesm2022/http.mjs", "file": "@angular_common_http.js", "fileHash": "a642fdbb", "needsInterop": false}, "@angular/core": {"src": "../../../../../node_modules/@angular/core/fesm2022/core.mjs", "file": "@angular_core.js", "fileHash": "cf06faf0", "needsInterop": false}, "@angular/forms": {"src": "../../../../../node_modules/@angular/forms/fesm2022/forms.mjs", "file": "@angular_forms.js", "fileHash": "5b5adbc1", "needsInterop": false}, "@angular/platform-browser": {"src": "../../../../../node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs", "file": "@angular_platform-browser.js", "fileHash": "a7e5c334", "needsInterop": false}, "@angular/router": {"src": "../../../../../node_modules/@angular/router/fesm2022/router.mjs", "file": "@angular_router.js", "fileHash": "5dd080e0", "needsInterop": false}, "@jsverse/transloco": {"src": "../../../../../node_modules/@jsverse/transloco/fesm2022/jsverse-transloco.mjs", "file": "@jsverse_transloco.js", "fileHash": "796898cb", "needsInterop": false}, "angular-highcharts": {"src": "../../../../../node_modules/angular-highcharts/fesm2022/angular-highcharts.mjs", "file": "angular-highcharts.js", "fileHash": "7dc0f616", "needsInterop": false}, "crypto-js": {"src": "../../../../../node_modules/crypto-js/index.js", "file": "crypto-js.js", "fileHash": "9ba4b5a6", "needsInterop": true}, "date-fns": {"src": "../../../../../node_modules/date-fns/index.mjs", "file": "date-fns.js", "fileHash": "01fed011", "needsInterop": false}, "exceljs": {"src": "../../../../../node_modules/exceljs/dist/exceljs.min.js", "file": "exceljs.js", "fileHash": "38aece2f", "needsInterop": true}, "file-saver": {"src": "../../../../../node_modules/file-saver/dist/FileSaver.min.js", "file": "file-saver.js", "fileHash": "e5cf6219", "needsInterop": true}, "flowbite": {"src": "../../../../../node_modules/flowbite/lib/esm/index.js", "file": "flowbite.js", "fileHash": "e790fac3", "needsInterop": false}, "jwt-decode": {"src": "../../../../../node_modules/jwt-decode/build/esm/index.js", "file": "jwt-decode.js", "fileHash": "5f898187", "needsInterop": false}, "ng-qrcode": {"src": "../../../../../node_modules/ng-qrcode/fesm2022/ng-qrcode.mjs", "file": "ng-qrcode.js", "fileHash": "b5e77dcf", "needsInterop": false}, "rxjs": {"src": "../../../../../node_modules/rxjs/dist/esm5/index.js", "file": "rxjs.js", "fileHash": "9ac16551", "needsInterop": false}, "rxjs/operators": {"src": "../../../../../node_modules/rxjs/dist/esm5/operators/index.js", "file": "rxjs_operators.js", "fileHash": "907a0f6a", "needsInterop": false}}, "chunks": {"chunk-BUGLPVFK": {"file": "chunk-BUGLPVFK.js"}, "chunk-XGOP4KJ6": {"file": "chunk-XGOP4KJ6.js"}, "chunk-GBXENHSV": {"file": "chunk-GBXENHSV.js"}, "chunk-PW2N57HR": {"file": "chunk-PW2N57HR.js"}, "chunk-5X3OOUUX": {"file": "chunk-5X3OOUUX.js"}, "chunk-TFMRLFGK": {"file": "chunk-TFMRLFGK.js"}, "chunk-KQP4K3F6": {"file": "chunk-KQP4K3F6.js"}, "chunk-V4F5PRXT": {"file": "chunk-V4F5PRXT.js"}}}