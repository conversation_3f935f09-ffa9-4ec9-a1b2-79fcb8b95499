{"version": 3, "file": "primeng-skeleton.mjs", "sources": ["../../src/app/components/skeleton/skeleton.ts", "../../src/app/components/skeleton/primeng-skeleton.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, Input, NgModule, ViewEncapsulation } from '@angular/core';\n/**\n * Skeleton is a placeholder to display instead of the actual content.\n * @group Components\n */\n@Component({\n    selector: 'p-skeleton',\n    template: ` <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle\" [attr.data-pc-name]=\"'skeleton'\" [attr.aria-hidden]=\"true\" [attr.data-pc-section]=\"'root'\"></div> `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./skeleton.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Skeleton {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Shape of the element.\n     * @group Props\n     */\n    @Input() shape: string = 'rectangle';\n    /**\n     * Type of the animation.\n     * @gruop Props\n     */\n    @Input() animation: string = 'wave';\n    /**\n     * Border radius of the element, defaults to value from theme.\n     * @group Props\n     */\n    @Input() borderRadius: string | undefined;\n    /**\n     * Size of the skeleton.\n     * @group Props\n     */\n    @Input() size: string | undefined;\n    /**\n     * Width of the element.\n     * @group Props\n     */\n    @Input() width: string = '100%';\n    /**\n     * Height of the element.\n     * @group Props\n     */\n    @Input() height: string = '1rem';\n\n    containerClass() {\n        return {\n            'p-skeleton p-component': true,\n            'p-skeleton-circle': this.shape === 'circle',\n            'p-skeleton-none': this.animation === 'none'\n        };\n    }\n\n    get containerStyle() {\n        if (this.size) return { ...this.style, width: this.size, height: this.size, borderRadius: this.borderRadius };\n        else return { width: this.width, height: this.height, borderRadius: this.borderRadius, ...this.style };\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Skeleton],\n    declarations: [Skeleton]\n})\nexport class SkeletonModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAEA;;;AAGG;MAWU,QAAQ,CAAA;AACjB;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;IACM,KAAK,GAAW,WAAW,CAAC;AACrC;;;AAGG;IACM,SAAS,GAAW,MAAM,CAAC;AACpC;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;IACM,KAAK,GAAW,MAAM,CAAC;AAChC;;;AAGG;IACM,MAAM,GAAW,MAAM,CAAC;IAEjC,cAAc,GAAA;QACV,OAAO;AACH,YAAA,wBAAwB,EAAE,IAAI;AAC9B,YAAA,mBAAmB,EAAE,IAAI,CAAC,KAAK,KAAK,QAAQ;AAC5C,YAAA,iBAAiB,EAAE,IAAI,CAAC,SAAS,KAAK,MAAM;SAC/C,CAAC;KACL;AAED,IAAA,IAAI,cAAc,GAAA;QACd,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;;YACzG,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;KAC1G;uGArDQ,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,qQARP,CAAuL,qLAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,wZAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQxL,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAVpB,SAAS;+BACI,YAAY,EAAA,QAAA,EACZ,CAAuL,qLAAA,CAAA,EAAA,eAAA,EAChL,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,wZAAA,CAAA,EAAA,CAAA;8BAOQ,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;;MAqBG,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EA7Dd,YAAA,EAAA,CAAA,QAAQ,CAyDP,EAAA,OAAA,EAAA,CAAA,YAAY,aAzDb,QAAQ,CAAA,EAAA,CAAA,CAAA;AA6DR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAJb,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,QAAQ,CAAC;oBACnB,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;AC5ED;;AAEG;;;;"}