{"version": 3, "file": "primeng-accordion.mjs", "sources": ["../../src/app/components/accordion/accordion.ts", "../../src/app/components/accordion/primeng-accordion.ts"], "sourcesContent": ["import { animate, state, style, transition, trigger } from '@angular/animations';\nimport { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    HostListener,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    QueryList,\n    TemplateRef,\n    ViewEncapsulation,\n    booleanAttribute,\n    forwardRef,\n    numberAttribute\n} from '@angular/core';\nimport { BlockableUI, Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { Subscription } from 'rxjs';\nimport { AccordionTabCloseEvent, AccordionTabOpenEvent } from './accordion.interface';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * AccordionTab is a helper component for Accordion.\n * @group Components\n */\n@Component({\n    selector: 'p-accordionTab',\n    template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\" [attr.data-pc-name]=\"'accordiontab'\">\n            <div class=\"p-accordion-header\" role=\"heading\" [attr.aria-level]=\"headerAriaLevel\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\" [attr.data-p-disabled]=\"disabled\" [attr.data-pc-section]=\"'header'\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [ngStyle]=\"headerStyle\"\n                    role=\"button\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"getTabHeaderActionId(id)\"\n                    [attr.aria-controls]=\"getTabContentId(id)\"\n                    [attr.aria-expanded]=\"selected\"\n                    [attr.aria-disabled]=\"disabled\"\n                    [attr.data-pc-section]=\"'headeraction'\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"getTabContentId(id)\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"getTabHeaderActionId(id)\"\n                [attr.data-pc-section]=\"'toggleablecontent'\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n    animations: [\n        trigger('tabContent', [\n            state(\n                'hidden',\n                style({\n                    height: '0',\n                    visibility: 'hidden'\n                })\n            ),\n            state(\n                'visible',\n                style({\n                    height: '*',\n                    visibility: 'visible'\n                })\n            ),\n            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./accordion.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class AccordionTab implements AfterContentInit, OnDestroy {\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Used to define the header of the tab.\n     * @group Props\n     */\n    @Input() header: string | undefined;\n    /**\n     * Inline style of the tab header.\n     * @group Props\n     */\n    @Input() headerStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Inline style of the tab.\n     * @group Props\n     */\n    @Input() tabStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Inline style of the tab content.\n     * @group Props\n     */\n    @Input() contentStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the tab.\n     * @group Props\n     */\n    @Input() tabStyleClass: string | undefined;\n    /**\n     * Style class of the tab header.\n     * @group Props\n     */\n    @Input() headerStyleClass: string | undefined;\n    /**\n     * Style class of the tab content.\n     * @group Props\n     */\n    @Input() contentStyleClass: string | undefined;\n    /**\n     * Whether the tab is disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) cache: boolean = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    @Input() transitionOptions: string = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    @Input() iconPos: 'end' | 'start' = 'start';\n    /**\n     * The value that returns the selection.\n     * @group Props\n     */\n    @Input() get selected(): boolean {\n        return this._selected;\n    }\n    set selected(val: boolean) {\n        this._selected = val;\n\n        if (!this.loaded) {\n            if (this._selected && this.cache) {\n                this.loaded = true;\n            }\n\n            this.changeDetector.detectChanges();\n        }\n    }\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) headerAriaLevel: number = 2;\n    /**\n     * Event triggered by changing the choice.\n     * @param {boolean} value - Boolean value indicates that the option is changed.\n     * @group Emits\n     */\n    @Output() selectedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n    @ContentChildren(Header) headerFacet!: QueryList<Header>;\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    private _selected: boolean = false;\n\n    get iconClass() {\n        if (this.iconPos === 'end') {\n            return 'p-accordion-toggle-icon-end';\n        } else {\n            return 'p-accordion-toggle-icon';\n        }\n    }\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    headerTemplate: TemplateRef<any> | undefined;\n\n    iconTemplate: TemplateRef<any> | undefined;\n\n    loaded: boolean = false;\n\n    accordion: Accordion;\n\n    constructor(@Inject(forwardRef(() => Accordion)) accordion: Accordion, public el: ElementRef, public changeDetector: ChangeDetectorRef) {\n        this.accordion = accordion as Accordion;\n        this.id = UniqueComponentId();\n    }\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    toggle(event?: MouseEvent | KeyboardEvent) {\n        if (this.disabled) {\n            return false;\n        }\n\n        let index = this.findTabIndex();\n\n        if (this.selected) {\n            this.selected = false;\n            this.accordion.onClose.emit({ originalEvent: event, index: index });\n        } else {\n            if (!this.accordion.multiple) {\n                for (var i = 0; i < this.accordion.tabs.length; i++) {\n                    if (this.accordion.tabs[i].selected) {\n                        this.accordion.tabs[i].selected = false;\n                        this.accordion.tabs[i].selectedChange.emit(false);\n                        this.accordion.tabs[i].changeDetector.markForCheck();\n                    }\n                }\n            }\n\n            this.selected = true;\n            this.loaded = true;\n            this.accordion.onOpen.emit({ originalEvent: event, index: index });\n        }\n\n        this.selectedChange.emit(this.selected);\n        this.accordion.updateActiveIndex();\n        this.changeDetector.markForCheck();\n\n        event?.preventDefault();\n    }\n\n    findTabIndex() {\n        let index = -1;\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n            if (this.accordion.tabs[i] == this) {\n                index = i;\n                break;\n            }\n        }\n        return index;\n    }\n\n    get hasHeaderFacet(): boolean {\n        return (this.headerFacet as QueryList<Header>) && (this.headerFacet as QueryList<Header>).length > 0;\n    }\n\n    onKeydown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'Enter':\n            case 'Space':\n                this.toggle(event);\n                event.preventDefault();\n                break;\n            default:\n                break;\n        }\n    }\n\n    getTabHeaderActionId(tabId) {\n        return `${tabId}_header_action`;\n    }\n\n    getTabContentId(tabId) {\n        return `${tabId}_content`;\n    }\n\n    ngOnDestroy() {\n        this.accordion.tabs.splice(this.findTabIndex(), 1);\n    }\n}\n\n/**\n * Accordion groups a collection of contents in tabs.\n * @group Components\n */\n@Component({\n    selector: 'p-accordion',\n    template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ng-content></ng-content>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Accordion implements BlockableUI, AfterContentInit, OnDestroy {\n    /**\n     * When enabled, multiple tabs can be activated at the same time.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) multiple: boolean = false;\n    /**\n     * Inline style of the tab header and content.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Icon of a collapsed tab.\n     * @group Props\n     */\n    @Input() expandIcon: string | undefined;\n    /**\n     * Icon of an expanded tab.\n     * @group Props\n     */\n    @Input() collapseIcon: string | undefined;\n    /**\n     * Index of the active tab or an array of indexes in multiple mode.\n     * @group Props\n     */\n    @Input() get activeIndex(): number | number[] | null | undefined {\n        return this._activeIndex;\n    }\n    set activeIndex(val: number | number[] | null | undefined) {\n        this._activeIndex = val;\n        if (this.preventActiveIndexPropagation) {\n            this.preventActiveIndexPropagation = false;\n            return;\n        }\n\n        this.updateSelectionState();\n    }\n    /**\n     * When enabled, the focused tab is activated.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) selectOnFocus: boolean = false;\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    @Input() get headerAriaLevel(): number {\n        return this._headerAriaLevel;\n    }\n    set headerAriaLevel(val: number) {\n        if (typeof val === 'number' && val > 0) {\n            this._headerAriaLevel = val;\n        } else if (this._headerAriaLevel !== 2) {\n            this._headerAriaLevel = 2;\n        }\n    }\n    /**\n     * Callback to invoke when an active tab is collapsed by clicking on the header.\n     * @param {AccordionTabCloseEvent} event - Custom tab close event.\n     * @group Emits\n     */\n    @Output() onClose: EventEmitter<AccordionTabCloseEvent> = new EventEmitter();\n    /**\n     * Callback to invoke when a tab gets expanded.\n     * @param {AccordionTabOpenEvent} event - Custom tab open event.\n     * @group Emits\n     */\n    @Output() onOpen: EventEmitter<AccordionTabOpenEvent> = new EventEmitter();\n    /**\n     * Returns the active index.\n     * @param {number | number[]} value - New index.\n     * @group Emits\n     */\n    @Output() activeIndexChange: EventEmitter<number | number[]> = new EventEmitter<number | number[]>();\n\n    @ContentChildren(AccordionTab, { descendants: true }) tabList: QueryList<AccordionTab> | undefined;\n\n    tabListSubscription: Subscription | null = null;\n\n    private _activeIndex: any;\n    private _headerAriaLevel: number = 2;\n\n    preventActiveIndexPropagation: boolean = false;\n\n    public tabs: AccordionTab[] = [];\n\n    constructor(public el: ElementRef, public changeDetector: ChangeDetectorRef) {}\n\n    @HostListener('keydown', ['$event'])\n    onKeydown(event) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onTabArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onTabArrowUpKey(event);\n                break;\n\n            case 'Home':\n                if (!event.shiftKey) {\n                    this.onTabHomeKey(event);\n                }\n                break;\n\n            case 'End':\n                if (!event.shiftKey) {\n                    this.onTabEndKey(event);\n                }\n                break;\n        }\n    }\n\n    focusedElementIsAccordionHeader() {\n        return document.activeElement.tagName.toLowerCase() === 'a' && document.activeElement.classList.contains('p-accordion-header-link');\n    }\n\n    onTabArrowDownKey(event) {\n        if (this.focusedElementIsAccordionHeader()) {\n            const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement.parentElement.parentElement);\n            nextHeaderAction ? this.changeFocusedTab(nextHeaderAction) : this.onTabHomeKey(event);\n\n            event.preventDefault();\n        }\n    }\n\n    onTabArrowUpKey(event) {\n        if (this.focusedElementIsAccordionHeader()) {\n            const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement.parentElement.parentElement);\n            prevHeaderAction ? this.changeFocusedTab(prevHeaderAction) : this.onTabEndKey(event);\n\n            event.preventDefault();\n        }\n    }\n\n    onTabHomeKey(event) {\n        const firstHeaderAction = this.findFirstHeaderAction();\n        this.changeFocusedTab(firstHeaderAction);\n        event.preventDefault();\n    }\n\n    changeFocusedTab(element) {\n        if (element) {\n            DomHandler.focus(element);\n\n            if (this.selectOnFocus) {\n                this.tabs.forEach((tab, i) => {\n                    let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n\n                    if (this.multiple) {\n                        if (!this._activeIndex) {\n                            this._activeIndex = [];\n                        }\n                        if (tab.id == element.id) {\n                            tab.selected = !tab.selected;\n                            if (!this._activeIndex.includes(i)) {\n                                this._activeIndex.push(i);\n                            } else {\n                                this._activeIndex = this._activeIndex.filter((ind) => ind !== i);\n                            }\n                        }\n                    } else {\n                        if (tab.id == element.id) {\n                            tab.selected = !tab.selected;\n                            this._activeIndex = i;\n                        } else {\n                            tab.selected = false;\n                        }\n                    }\n\n                    tab.selectedChange.emit(selected);\n                    this.activeIndexChange.emit(this._activeIndex);\n                    tab.changeDetector.markForCheck();\n                });\n            }\n        }\n    }\n\n    findNextHeaderAction(tabElement, selfCheck = false) {\n        const nextTabElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n        const headerElement = DomHandler.findSingle(nextTabElement, '[data-pc-section=\"header\"]');\n\n        return headerElement ? (DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]')) : null;\n    }\n\n    findPrevHeaderAction(tabElement, selfCheck = false) {\n        const prevTabElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n        const headerElement = DomHandler.findSingle(prevTabElement, '[data-pc-section=\"header\"]');\n\n        return headerElement ? (DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]')) : null;\n    }\n\n    findFirstHeaderAction() {\n        const firstEl = this.el.nativeElement.firstElementChild.childNodes[0];\n        return this.findNextHeaderAction(firstEl, true);\n    }\n\n    findLastHeaderAction() {\n        const childNodes = this.el.nativeElement.firstElementChild.childNodes;\n        const lastEl = childNodes[childNodes.length - 1];\n\n        return this.findPrevHeaderAction(lastEl, true);\n    }\n\n    onTabEndKey(event) {\n        const lastHeaderAction = this.findLastHeaderAction();\n        this.changeFocusedTab(lastHeaderAction);\n        event.preventDefault();\n    }\n\n    ngAfterContentInit() {\n        this.initTabs();\n\n        this.tabListSubscription = (this.tabList as QueryList<AccordionTab>).changes.subscribe((_) => {\n            this.initTabs();\n        });\n    }\n\n    initTabs() {\n        this.tabs = (this.tabList as QueryList<AccordionTab>).toArray();\n\n        this.tabs.forEach((tab) => {\n            tab.headerAriaLevel = this._headerAriaLevel;\n        });\n\n        this.updateSelectionState();\n        this.changeDetector.markForCheck();\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    updateSelectionState() {\n        if (this.tabs && this.tabs.length && this._activeIndex != null) {\n            for (let i = 0; i < this.tabs.length; i++) {\n                let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n                let changed = selected !== this.tabs[i].selected;\n\n                if (changed) {\n                    this.tabs[i].selected = selected;\n                    this.tabs[i].selectedChange.emit(selected);\n                    this.tabs[i].changeDetector.markForCheck();\n                }\n            }\n        }\n    }\n\n    isTabActive(index) {\n        return this.multiple ? this._activeIndex && (<number[]>this._activeIndex).includes(index) : this._activeIndex === index;\n    }\n\n    getTabProp(tab, name) {\n        return tab.props ? tab.props[name] : undefined;\n    }\n\n    updateActiveIndex() {\n        let index: number | number[] | null = this.multiple ? [] : null;\n        this.tabs.forEach((tab, i) => {\n            if (tab.selected) {\n                if (this.multiple) {\n                    (index as number[]).push(i);\n                } else {\n                    index = i;\n                    return;\n                }\n            }\n        });\n        this.preventActiveIndexPropagation = true;\n        this._activeIndex = index;\n        this.activeIndexChange.emit(index as number[] | number);\n    }\n\n    ngOnDestroy() {\n        if (this.tabListSubscription) {\n            this.tabListSubscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, ChevronRightIcon, ChevronDownIcon],\n    exports: [Accordion, AccordionTab, SharedModule],\n    declarations: [Accordion, AccordionTab]\n})\nexport class AccordionModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;AA+BA;;;AAGG;MAmFU,YAAY,CAAA;AAmHyD,IAAA,EAAA,CAAA;AAAuB,IAAA,cAAA,CAAA;AAlHrG;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACM,IAAA,WAAW,CAA8C;AAClE;;;AAGG;AACM,IAAA,QAAQ,CAA8C;AAC/D;;;AAGG;AACM,IAAA,YAAY,CAA8C;AACnE;;;AAGG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;AAGG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACqC,KAAK,GAAY,IAAI,CAAC;AAC9D;;;AAGG;IACM,iBAAiB,GAAW,sCAAsC,CAAC;AAC5E;;;AAGG;IACM,OAAO,GAAoB,OAAO,CAAC;AAC5C;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,GAAY,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AAErB,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9B,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;AACvC,SAAA;KACJ;AACD;;;AAGG;IACoC,eAAe,GAAW,CAAC,CAAC;AACnE;;;;AAIG;AACO,IAAA,cAAc,GAA0B,IAAI,YAAY,EAAW,CAAC;AAErD,IAAA,WAAW,CAAqB;AAEzB,IAAA,SAAS,CAA4B;IAE7D,SAAS,GAAY,KAAK,CAAC;AAEnC,IAAA,IAAI,SAAS,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;AACxB,YAAA,OAAO,6BAA6B,CAAC;AACxC,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,yBAAyB,CAAC;AACpC,SAAA;KACJ;AAED,IAAA,eAAe,CAA+B;AAE9C,IAAA,cAAc,CAA+B;AAE7C,IAAA,YAAY,CAA+B;IAE3C,MAAM,GAAY,KAAK,CAAC;AAExB,IAAA,SAAS,CAAY;AAErB,IAAA,WAAA,CAAiD,SAAoB,EAAS,EAAc,EAAS,cAAiC,EAAA;QAAxD,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAc,CAAA,cAAA,GAAd,cAAc,CAAmB;AAClI,QAAA,IAAI,CAAC,SAAS,GAAG,SAAsB,CAAC;AACxC,QAAA,IAAI,CAAC,EAAE,GAAG,iBAAiB,EAAE,CAAC;KACjC;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,MAAM,CAAC,KAAkC,EAAA;QACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACvE,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AAC1B,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACjD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;wBACjC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC;AACxC,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClD,wBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;AACxD,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACtE,SAAA;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QAEnC,KAAK,EAAE,cAAc,EAAE,CAAC;KAC3B;IAED,YAAY,GAAA;AACR,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACf,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;gBAChC,KAAK,GAAG,CAAC,CAAC;gBACV,MAAM;AACT,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,IAAI,cAAc,GAAA;QACd,OAAQ,IAAI,CAAC,WAAiC,IAAK,IAAI,CAAC,WAAiC,CAAC,MAAM,GAAG,CAAC,CAAC;KACxG;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,KAAK,EAAA;QACtB,OAAO,CAAA,EAAG,KAAK,CAAA,cAAA,CAAgB,CAAC;KACnC;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,OAAO,CAAA,EAAG,KAAK,CAAA,QAAA,CAAU,CAAC;KAC7B;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;KACtD;AApNQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBAmHD,UAAU,CAAC,MAAM,SAAS,CAAC,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAnHtC,YAAY,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EA6CD,gBAAgB,CAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAKhB,gBAAgB,CAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAiChB,eAAe,CAQlB,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,SAAA,EAAA,MAAM,EAEN,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA7KpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoDT,EAiiBuB,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,seAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,gBAAgB,CAAE,EAAA,QAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,CAhiB7C,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,YAAY,EAAE;AAClB,gBAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACX,oBAAA,UAAU,EAAE,QAAQ;AACvB,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,oBAAA,MAAM,EAAE,GAAG;AACX,oBAAA,UAAU,EAAE,SAAS;AACxB,iBAAA,CAAC,CACL;gBACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACnE,gBAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;aACtC,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAlFxB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,gBAAgB,EAChB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoDT,EACW,UAAA,EAAA;wBACR,OAAO,CAAC,YAAY,EAAE;AAClB,4BAAA,KAAK,CACD,QAAQ,EACR,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACX,gCAAA,UAAU,EAAE,QAAQ;AACvB,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,SAAS,EACT,KAAK,CAAC;AACF,gCAAA,MAAM,EAAE,GAAG;AACX,gCAAA,UAAU,EAAE,SAAS;AACxB,6BAAA,CAAC,CACL;4BACD,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC;AACnE,4BAAA,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;yBACtC,CAAC;AACL,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,seAAA,CAAA,EAAA,CAAA;;0BAqHY,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,SAAS,CAAC,CAAA;kGA9GtC,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAkBiC,eAAe,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAM3B,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAEkB,WAAW,EAAA,CAAA;sBAAnC,eAAe;uBAAC,MAAM,CAAA;gBAES,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;AA0HlC;;;AAGG;MAaU,SAAS,CAAA;AA2FC,IAAA,EAAA,CAAA;AAAuB,IAAA,cAAA,CAAA;AA1F1C;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IACD,IAAI,WAAW,CAAC,GAAyC,EAAA;AACrD,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,IAAI,CAAC,6BAA6B,EAAE;AACpC,YAAA,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;YAC3C,OAAO;AACV,SAAA;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;AACD;;;AAGG;IACqC,aAAa,GAAY,KAAK,CAAC;AACvE;;;AAGG;AACH,IAAA,IAAa,eAAe,GAAA;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAChC;IACD,IAAI,eAAe,CAAC,GAAW,EAAA;QAC3B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,GAAG,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;AAC/B,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC7B,SAAA;KACJ;AACD;;;;AAIG;AACO,IAAA,OAAO,GAAyC,IAAI,YAAY,EAAE,CAAC;AAC7E;;;;AAIG;AACO,IAAA,MAAM,GAAwC,IAAI,YAAY,EAAE,CAAC;AAC3E;;;;AAIG;AACO,IAAA,iBAAiB,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAE/C,IAAA,OAAO,CAAsC;IAEnG,mBAAmB,GAAwB,IAAI,CAAC;AAExC,IAAA,YAAY,CAAM;IAClB,gBAAgB,GAAW,CAAC,CAAC;IAErC,6BAA6B,GAAY,KAAK,CAAC;IAExC,IAAI,GAAmB,EAAE,CAAC;IAEjC,WAAmB,CAAA,EAAc,EAAS,cAAiC,EAAA;QAAxD,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAc,CAAA,cAAA,GAAd,cAAc,CAAmB;KAAI;AAG/E,IAAA,SAAS,CAAC,KAAK,EAAA;QACX,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC9B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACjB,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5B,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACjB,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3B,iBAAA;gBACD,MAAM;AACb,SAAA;KACJ;IAED,+BAA+B,GAAA;QAC3B,OAAO,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;KACvI;AAED,IAAA,iBAAiB,CAAC,KAAK,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,+BAA+B,EAAE,EAAE;AACxC,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC3G,YAAA,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAEtF,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;AACjB,QAAA,IAAI,IAAI,CAAC,+BAA+B,EAAE,EAAE;AACxC,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC3G,YAAA,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAErF,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACvD,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;QACzC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,OAAO,EAAA;AACpB,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAE1B,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,KAAI;oBACzB,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC;oBAEvF,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,wBAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACpB,4BAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC1B,yBAAA;AACD,wBAAA,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,EAAE;AACtB,4BAAA,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC;4BAC7B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAChC,gCAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7B,6BAAA;AAAM,iCAAA;AACH,gCAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;AACpE,6BAAA;AACJ,yBAAA;AACJ,qBAAA;AAAM,yBAAA;AACH,wBAAA,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,EAAE;AACtB,4BAAA,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC7B,4BAAA,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;AACzB,yBAAA;AAAM,6BAAA;AACH,4BAAA,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC;AACxB,yBAAA;AACJ,qBAAA;AAED,oBAAA,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC/C,oBAAA,GAAG,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;AACtC,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,UAAU,EAAE,SAAS,GAAG,KAAK,EAAA;AAC9C,QAAA,MAAM,cAAc,GAAG,SAAS,GAAG,UAAU,GAAG,UAAU,CAAC,kBAAkB,CAAC;QAC9E,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,4BAA4B,CAAC,CAAC;AAE1F,QAAA,OAAO,aAAa,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,kCAAkC,CAAC,IAAI,IAAI,CAAC;KAC/O;AAED,IAAA,oBAAoB,CAAC,UAAU,EAAE,SAAS,GAAG,KAAK,EAAA;AAC9C,QAAA,MAAM,cAAc,GAAG,SAAS,GAAG,UAAU,GAAG,UAAU,CAAC,sBAAsB,CAAC;QAClF,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,cAAc,EAAE,4BAA4B,CAAC,CAAC;AAE1F,QAAA,OAAO,aAAa,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,kCAAkC,CAAC,IAAI,IAAI,CAAC;KAC/O;IAED,qBAAqB,GAAA;AACjB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KACnD;IAED,oBAAoB,GAAA;QAChB,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,iBAAiB,CAAC,UAAU,CAAC;QACtE,MAAM,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEjD,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;KAClD;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AACrD,QAAA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QACxC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,QAAQ,EAAE,CAAC;AAEhB,QAAA,IAAI,CAAC,mBAAmB,GAAI,IAAI,CAAC,OAAmC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;YACzF,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpB,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC,OAAmC,CAAC,OAAO,EAAE,CAAC;QAEhE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACtB,YAAA,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAChD,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;KACtC;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;AAC5D,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACvC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC;AACvF,gBAAA,IAAI,OAAO,GAAG,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AAEjD,gBAAA,IAAI,OAAO,EAAE;oBACT,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,oBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC3C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;AAC9C,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;QACb,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,IAAe,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC;KAC3H;IAED,UAAU,CAAC,GAAG,EAAE,IAAI,EAAA;AAChB,QAAA,OAAO,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;KAClD;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,KAAK,GAA6B,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC;QAChE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,KAAI;YACzB,IAAI,GAAG,CAAC,QAAQ,EAAE;gBACd,IAAI,IAAI,CAAC,QAAQ,EAAE;AACd,oBAAA,KAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,iBAAA;AAAM,qBAAA;oBACH,KAAK,GAAG,CAAC,CAAC;oBACV,OAAO;AACV,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;AAC1C,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAA0B,CAAC,CAAC;KAC3D;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC1B,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AAC1C,SAAA;KACJ;uGA1RQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,wEAKE,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAyChB,gBAAgB,CAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAkCnB,YAAY,EA1FnB,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA,CAAA;;2FAMQ,SAAS,EAAA,UAAA,EAAA,CAAA;kBAZrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,QAAQ,EAAE,CAAA;;;;AAIT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;+GAM2C,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAgBkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,eAAe,EAAA,CAAA;sBAA3B,KAAK;gBAeI,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAE+C,OAAO,EAAA,CAAA;sBAA5D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,YAAY,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAA;gBAcpD,SAAS,EAAA,CAAA;sBADR,YAAY;uBAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAqM1B,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAlSf,YAAA,EAAA,CAAA,SAAS,EAvOT,YAAY,aAqgBX,YAAY,EAAE,gBAAgB,EAAE,eAAe,CA9RhD,EAAA,OAAA,EAAA,CAAA,SAAS,EAvOT,YAAY,EAsgBc,YAAY,CAAA,EAAA,CAAA,CAAA;AAGtC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAJd,YAAY,EAAE,gBAAgB,EAAE,eAAe,EACtB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGtC,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,eAAe,CAAC;AAC1D,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC;AAChD,oBAAA,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;AAC1C,iBAAA,CAAA;;;AC7nBD;;AAEG;;;;"}