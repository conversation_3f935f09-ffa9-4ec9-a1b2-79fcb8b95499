// src/app/core/profile-services/services/profile.service.ts

import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ProfileData, Service } from '../api/profile';
import { ProfileApi } from '../api/profile.api';

@Injectable({
  providedIn: 'root',
})
export class ProfileService implements ProfileData {
  private profileApi = inject(ProfileApi);

  getServices(profileId: number): Observable<Service[]> {
    return this.profileApi.getServices(profileId);
  }

}
