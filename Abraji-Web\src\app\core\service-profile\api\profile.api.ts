import { inject, Injectable } from '@angular/core';
import { HttpService } from '../../common-services/services/http.service';
import { Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Service } from './profile';
import { CacheService } from '../../common-services/services/cache.service';

@Injectable({
  providedIn: 'root',
})
export class ProfileApi {
  private apiUrl = `profile/services`;
  private httpService = inject(HttpService);
  private cacheService = inject(CacheService);

  // Cache object to store results
  private cache: { [profileId: number]: Service[] } = {};

  constructor() {
    this.cacheService.registerCacheClearCallback(() => this.clearCache());
  }

  getServices(profileId: number): Observable<Service[]> {
    // Check if the result is already in the cache
    if (this.cache[profileId]) {
      // Return the cached result as an observable
      console.log("from cache");

      return of(this.cache[profileId]);
    }

    // If not in the cache, make an HTTP request
    return this.httpService
      .get(`${this.apiUrl}/${profileId}`)
      .pipe(
        map((response: any) => response.data as Service[]),
        tap((services: Service[]) => {
          // Store the result in the cache
          this.cache[profileId] = services;
        })
      );
  }

  clearCache(): void {
    this.cache = {};
  }
}
