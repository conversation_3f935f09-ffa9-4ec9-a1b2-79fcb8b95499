import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardHomeComponent } from './dashboard-home/dashboard-home.component';
import { StatsComponent } from './stats/stats.component';
import { TranslocoModule } from '@jsverse/transloco';
import { SharedModule } from '../shared/shared.module';


@NgModule({
  declarations: [
    DashboardHomeComponent,
    StatsComponent,
  ],
  imports: [
    CommonModule,
    DashboardRoutingModule,
    TranslocoModule,
    SharedModule
]
})
export class DashboardModule { }
