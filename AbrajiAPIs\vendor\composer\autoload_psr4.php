<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'voku\\' => array($vendorDir . '/voku/portable-ascii/src/voku'),
    'phpseclib3\\' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'Wikimedia\\Composer\\Merge\\V2\\' => array($vendorDir . '/wikimedia/composer-merge-plugin/src'),
    'Whoops\\' => array($vendorDir . '/filp/whoops/src/Whoops'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'TijsVerkoyen\\CssToInlineStyles\\' => array($vendorDir . '/tijsverkoyen/css-to-inline-styles/src'),
    'Tests\\' => array($baseDir . '/tests'),
    'Termwind\\' => array($vendorDir . '/nunomaduro/termwind/src'),
    'Symfony\\Polyfill\\Uuid\\' => array($vendorDir . '/symfony/polyfill-uuid'),
    'Symfony\\Polyfill\\Php83\\' => array($vendorDir . '/symfony/polyfill-php83'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\Yaml\\' => array($vendorDir . '/symfony/yaml'),
    'Symfony\\Component\\VarDumper\\' => array($vendorDir . '/symfony/var-dumper'),
    'Symfony\\Component\\Uid\\' => array($vendorDir . '/symfony/uid'),
    'Symfony\\Component\\Translation\\' => array($vendorDir . '/symfony/translation'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Routing\\' => array($vendorDir . '/symfony/routing'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\Mime\\' => array($vendorDir . '/symfony/mime'),
    'Symfony\\Component\\Mailer\\' => array($vendorDir . '/symfony/mailer'),
    'Symfony\\Component\\HttpKernel\\' => array($vendorDir . '/symfony/http-kernel'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\ErrorHandler\\' => array($vendorDir . '/symfony/error-handler'),
    'Symfony\\Component\\CssSelector\\' => array($vendorDir . '/symfony/css-selector'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Symfony\\Component\\Clock\\' => array($vendorDir . '/symfony/clock'),
    'Spatie\\Permission\\' => array($vendorDir . '/spatie/laravel-permission/src'),
    'Ramsey\\Uuid\\' => array($vendorDir . '/ramsey/uuid/src'),
    'Ramsey\\Collection\\' => array($vendorDir . '/ramsey/collection/src'),
    'Psy\\' => array($vendorDir . '/psy/psysh/src'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Clock\\' => array($vendorDir . '/psr/clock/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'ParagonIE\\ConstantTime\\' => array($vendorDir . '/paragonie/constant_time_encoding/src'),
    'Nwidart\\Modules\\' => array($vendorDir . '/nwidart/laravel-modules/src'),
    'NunoMaduro\\Collision\\' => array($vendorDir . '/nunomaduro/collision/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Modules\\Wallet\\Tests\\' => array($baseDir . '/Modules/Wallet/tests'),
    'Modules\\Wallet\\Database\\Seeders\\' => array($baseDir . '/Modules/Wallet/database/seeders'),
    'Modules\\Wallet\\Database\\Factories\\' => array($baseDir . '/Modules/Wallet/database/factories'),
    'Modules\\Wallet\\' => array($baseDir . '/Modules/Wallet/app'),
    'Modules\\Users\\Tests\\' => array($baseDir . '/Modules/Users/<USER>'),
    'Modules\\Users\\Database\\Seeders\\' => array($baseDir . '/Modules/Users/<USER>/seeders'),
    'Modules\\Users\\Database\\Factories\\' => array($baseDir . '/Modules/Users/<USER>/factories'),
    'Modules\\Users\\' => array($baseDir . '/Modules/Users/<USER>'),
    'Modules\\Transaction\\Tests\\' => array($baseDir . '/Modules/Transaction/tests'),
    'Modules\\Transaction\\Database\\Seeders\\' => array($baseDir . '/Modules/Transaction/database/seeders'),
    'Modules\\Transaction\\Database\\Factories\\' => array($baseDir . '/Modules/Transaction/database/factories'),
    'Modules\\Transaction\\' => array($baseDir . '/Modules/Transaction/app'),
    'Modules\\Manager\\Tests\\' => array($baseDir . '/Modules/Manager/tests'),
    'Modules\\Manager\\Database\\Seeders\\' => array($baseDir . '/Modules/Manager/database/seeders'),
    'Modules\\Manager\\Database\\Factories\\' => array($baseDir . '/Modules/Manager/database/factories'),
    'Modules\\Manager\\' => array($baseDir . '/Modules/Manager/app'),
    'Modules\\Invoice\\Tests\\' => array($baseDir . '/Modules/Invoice/tests'),
    'Modules\\Invoice\\Database\\Seeders\\' => array($baseDir . '/Modules/Invoice/database/seeders'),
    'Modules\\Invoice\\Database\\Factories\\' => array($baseDir . '/Modules/Invoice/database/factories'),
    'Modules\\Invoice\\' => array($baseDir . '/Modules/Invoice/app'),
    'Modules\\Debts\\Tests\\' => array($baseDir . '/Modules/Debts/tests'),
    'Modules\\Debts\\Database\\Seeders\\' => array($baseDir . '/Modules/Debts/database/seeders'),
    'Modules\\Debts\\Database\\Factories\\' => array($baseDir . '/Modules/Debts/database/factories'),
    'Modules\\Debts\\' => array($baseDir . '/Modules/Debts/app'),
    'Modules\\Dashboard\\Tests\\' => array($baseDir . '/Modules/Dashboard/tests'),
    'Modules\\Dashboard\\Database\\Seeders\\' => array($baseDir . '/Modules/Dashboard/database/seeders'),
    'Modules\\Dashboard\\Database\\Factories\\' => array($baseDir . '/Modules/Dashboard/database/factories'),
    'Modules\\Dashboard\\' => array($baseDir . '/Modules/Dashboard/app'),
    'Modules\\Card\\Tests\\' => array($baseDir . '/Modules/Card/tests'),
    'Modules\\Card\\Database\\Seeders\\' => array($baseDir . '/Modules/Card/database/seeders'),
    'Modules\\Card\\Database\\Factories\\' => array($baseDir . '/Modules/Card/database/factories'),
    'Modules\\Card\\' => array($baseDir . '/Modules/Card/app'),
    'Modules\\Authentication\\Tests\\' => array($baseDir . '/Modules/Authentication/tests'),
    'Modules\\Authentication\\Database\\Seeders\\' => array($baseDir . '/Modules/Authentication/database/seeders'),
    'Modules\\Authentication\\Database\\Factories\\' => array($baseDir . '/Modules/Authentication/database/factories'),
    'Modules\\Authentication\\' => array($baseDir . '/Modules/Authentication/app'),
    'Mockery\\' => array($vendorDir . '/mockery/mockery/library/Mockery'),
    'League\\Uri\\' => array($vendorDir . '/league/uri', $vendorDir . '/league/uri-interfaces'),
    'League\\MimeTypeDetection\\' => array($vendorDir . '/league/mime-type-detection/src'),
    'League\\Flysystem\\Local\\' => array($vendorDir . '/league/flysystem-local'),
    'League\\Flysystem\\' => array($vendorDir . '/league/flysystem/src'),
    'League\\Config\\' => array($vendorDir . '/league/config/src'),
    'League\\CommonMark\\' => array($vendorDir . '/league/commonmark/src'),
    'Laravel\\Tinker\\' => array($vendorDir . '/laravel/tinker/src'),
    'Laravel\\SerializableClosure\\' => array($vendorDir . '/laravel/serializable-closure/src'),
    'Laravel\\Sanctum\\' => array($vendorDir . '/laravel/sanctum/src'),
    'Laravel\\Sail\\' => array($vendorDir . '/laravel/sail/src'),
    'Laravel\\Prompts\\' => array($vendorDir . '/laravel/prompts/src'),
    'Illuminate\\Support\\' => array($vendorDir . '/laravel/framework/src/Illuminate/Macroable', $vendorDir . '/laravel/framework/src/Illuminate/Collections', $vendorDir . '/laravel/framework/src/Illuminate/Conditionable'),
    'Illuminate\\' => array($vendorDir . '/laravel/framework/src/Illuminate'),
    'GuzzleHttp\\UriTemplate\\' => array($vendorDir . '/guzzlehttp/uri-template/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'GrahamCampbell\\ResultType\\' => array($vendorDir . '/graham-campbell/result-type/src'),
    'Fruitcake\\Cors\\' => array($vendorDir . '/fruitcake/php-cors/src'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'Faker\\' => array($vendorDir . '/fakerphp/faker/src/Faker'),
    'Egulias\\EmailValidator\\' => array($vendorDir . '/egulias/email-validator/src'),
    'Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
    'Doctrine\\Inflector\\' => array($vendorDir . '/doctrine/inflector/lib/Doctrine/Inflector'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/src'),
    'Dflydev\\DotAccessData\\' => array($vendorDir . '/dflydev/dot-access-data/src'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Database\\Seeders\\' => array($baseDir . '/database/seeders', $vendorDir . '/laravel/pint/database/seeders'),
    'Database\\Factories\\' => array($baseDir . '/database/factories', $vendorDir . '/laravel/pint/database/factories'),
    'Cviebrock\\EloquentSluggable\\' => array($vendorDir . '/cviebrock/eloquent-sluggable/src'),
    'Cron\\' => array($vendorDir . '/dragonmantank/cron-expression/src/Cron'),
    'Cocur\\Slugify\\' => array($vendorDir . '/cocur/slugify/src'),
    'Carbon\\Doctrine\\' => array($vendorDir . '/carbonphp/carbon-doctrine-types/src/Carbon/Doctrine'),
    'Carbon\\' => array($vendorDir . '/nesbot/carbon/src/Carbon'),
    'Brick\\Math\\' => array($vendorDir . '/brick/math/src'),
    'App\\' => array($baseDir . '/app', $vendorDir . '/laravel/pint/app'),
);
