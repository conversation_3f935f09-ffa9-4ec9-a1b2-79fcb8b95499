<?php

use App\Http\Controllers\SeedController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::get('/test', function () {
    return response()->json([
        'message' => 'API is working!',
        'timestamp' => now(),
        'status' => 'success'
    ]);
});

Route::post('/test-login', function (Request $request) {
    return response()->json([
        'message' => 'Login endpoint working!',
        'received_payload' => $request->input('payload'),
        'token' => 'test-token-123',
        'status' => 'success'
    ]);
});

// Local auth login for testing (simulates real login)
Route::post('/auth/login-local', function (Request $request) {
    $request->validate([
        'payload' => 'required',
    ]);

    try {
        // Decrypt the payload (simulate real decryption)
        $encryptionKey = 'abcdefghijuklmno0123456789012345';

        // For testing, we'll accept any payload and return success
        // In real scenario, this would decrypt and validate credentials

        $fakeToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************.test-signature';

        return response()->json([
            'status' => 200,
            'message' => 'Login successful!',
            'token' => $fakeToken,
            'user' => [
                'id' => 1,
                'name' => 'Test Admin',
                'email' => '<EMAIL>',
                'username' => 'admin'
            ],
            'success' => true
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'status' => 401,
            'error' => 'Invalid credentials',
            'message' => $e->getMessage()
        ], 401);
    }
});

// Local dashboard endpoints for testing
Route::get('/dashboard-local', function (Request $request) {
    return response()->json([
        'status' => 200,
        'data' => [
            'users_count' => 150,
            'active_users' => 120,
            'online_users' => 45,
            'expired_users' => 30,
            'users_expire_in_3_days' => 15,
            'balance' => '25000.50',
            'expiring_today' => 5,
            'fup_users' => 8
        ],
        'success' => true
    ]);
});

Route::get('/dashboard/cards-local', function (Request $request) {
    return response()->json([
        'status' => 200,
        'cards' => [
            [
                'id' => 1,
                'profile' => 'Basic Plan',
                'total' => 100,
                'used' => 75,
                'remaining' => 25
            ],
            [
                'id' => 2,
                'profile' => 'Premium Plan',
                'total' => 50,
                'used' => 30,
                'remaining' => 20
            ],
            [
                'id' => 3,
                'profile' => 'Enterprise Plan',
                'total' => 25,
                'used' => 20,
                'remaining' => 5
            ]
        ],
        'success' => true
    ]);
});

Route::get('/dashboard/transactions-local', function (Request $request) {
    return response()->json([
        'status' => 200,
        'data' => [
            'maintenance' => [
                'total_transactions' => 45,
                'total_amount' => 15000.00
            ],
            'expense' => [
                'total_transactions' => 32,
                'total_amount' => 8500.00
            ],
            'general' => [
                'total_transactions' => 78,
                'total_amount' => 23500.00
            ]
        ],
        'success' => true
    ]);
});

Route::get('/seed-all', [SeedController::class, 'seedAllModules']);
Route::delete('/delete-all', [SeedController::class, 'deleteAllData']);

