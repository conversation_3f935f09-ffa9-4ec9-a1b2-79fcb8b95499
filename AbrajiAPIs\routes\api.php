<?php

use App\Http\Controllers\SeedController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::get('/test', function () {
    return response()->json([
        'message' => 'API is working!',
        'timestamp' => now(),
        'status' => 'success'
    ]);
});

Route::post('/test-login', function (Request $request) {
    return response()->json([
        'message' => 'Login endpoint working!',
        'received_payload' => $request->input('payload'),
        'token' => 'test-token-123',
        'status' => 'success'
    ]);
});

// Simple auth login for testing
Route::post('/auth/login', function (Request $request) {
    return response()->json([
        'message' => 'Login successful!',
        'user' => [
            'id' => 1,
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ],
        'token' => 'test-auth-token-' . time(),
        'status' => 'success'
    ]);
});

Route::get('/seed-all', [SeedController::class, 'seedAllModules']);
Route::delete('/delete-all', [SeedController::class, 'deleteAllData']);

