{"version": 3, "file": "primeng-floatlabel.mjs", "sources": ["../../src/app/components/floatlabel/floatlabel.ts", "../../src/app/components/floatlabel/primeng-floatlabel.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, NgModule, ViewEncapsulation } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { RouterModule } from '@angular/router';\n\n/**\n * Float<PERSON>abel appears on top of the input field when focused.\n * @group Components\n */\n@Component({\n    selector: 'p-floatLabel',\n    template: `\n        <span class=\"p-float-label\">\n            <ng-content></ng-content>\n        </span>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None\n})\nexport class FloatLabel {}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, RouterModule],\n    exports: [FloatLabel, SharedModule],\n    declarations: [FloatLabel]\n})\nexport class FloatLabelModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAKA;;;AAGG;MAWU,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EART,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAIQ,UAAU,EAAA,UAAA,EAAA,CAAA;kBAVtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,CAAA;;;;AAIT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,MAAM;oBAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACxC,iBAAA,CAAA;;MAQY,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAhB,gBAAgB,EAAA,YAAA,EAAA,CAPhB,UAAU,CAAA,EAAA,OAAA,EAAA,CAGT,YAAY,EAAE,YAAY,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CAHzC,UAAU,EAIG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGzB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAJf,YAAY,EAAE,YAAY,EAAE,YAAY,EAC5B,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGzB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;AACnD,oBAAA,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;oBACnC,YAAY,EAAE,CAAC,UAAU,CAAC;AAC7B,iBAAA,CAAA;;;ACzBD;;AAEG;;;;"}