{"version": 3, "file": "primeng-ripple.mjs", "sources": ["../../src/app/components/ripple/ripple.ts", "../../src/app/components/ripple/primeng-ripple.ts"], "sourcesContent": ["import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { AfterViewInit, Directive, ElementRef, Inject, NgModule, NgZone, OnDestroy, Optional, PLATFORM_ID, Renderer2 } from '@angular/core';\nimport { PrimeNGConfig } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { VoidListener } from 'primeng/ts-helpers';\n/**\n * Ripple directive adds ripple effect to the host element.\n * @group Components\n */\n@Directive({\n    selector: '[pRipple]',\n    standalone: true,\n    host: {\n        class: 'p-ripple p-element'\n    }\n})\nexport class Ripple implements AfterViewInit, OnDestroy {\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, public el: ElementRef, public zone: NgZone, @Optional() public config: PrimeNGConfig) {}\n\n    animationListener: VoidListener;\n\n    mouseDownListener: VoidListener;\n\n    timeout: any;\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.config && this.config.ripple) {\n                this.zone.runOutsideAngular(() => {\n                    this.create();\n                    this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n                });\n            }\n        }\n    }\n\n    onMouseDown(event: MouseEvent) {\n        let ink = this.getInk();\n        if (!ink || this.document.defaultView?.getComputedStyle(ink, null).display === 'none') {\n            return;\n        }\n\n        DomHandler.removeClass(ink, 'p-ink-active');\n        if (!DomHandler.getHeight(ink) && !DomHandler.getWidth(ink)) {\n            let d = Math.max(DomHandler.getOuterWidth(this.el.nativeElement), DomHandler.getOuterHeight(this.el.nativeElement));\n            ink.style.height = d + 'px';\n            ink.style.width = d + 'px';\n        }\n\n        let offset = DomHandler.getOffset(this.el.nativeElement);\n        let x = event.pageX - offset.left + this.document.body.scrollTop - DomHandler.getWidth(ink) / 2;\n        let y = event.pageY - offset.top + this.document.body.scrollLeft - DomHandler.getHeight(ink) / 2;\n\n        this.renderer.setStyle(ink, 'top', y + 'px');\n        this.renderer.setStyle(ink, 'left', x + 'px');\n        DomHandler.addClass(ink, 'p-ink-active');\n\n        this.timeout = setTimeout(() => {\n            let ink = this.getInk();\n            if (ink) {\n                DomHandler.removeClass(ink, 'p-ink-active');\n            }\n        }, 401);\n    }\n\n    getInk() {\n        const children = this.el.nativeElement.children;\n        for (let i = 0; i < children.length; i++) {\n            if (typeof children[i].className === 'string' && children[i].className.indexOf('p-ink') !== -1) {\n                return children[i];\n            }\n        }\n        return null;\n    }\n\n    resetInk() {\n        let ink = this.getInk();\n        if (ink) {\n            DomHandler.removeClass(ink, 'p-ink-active');\n        }\n    }\n\n    onAnimationEnd(event: Event) {\n        if (this.timeout) {\n            clearTimeout(this.timeout);\n        }\n        DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n    }\n\n    create() {\n        let ink = this.renderer.createElement('span');\n        this.renderer.addClass(ink, 'p-ink');\n        this.renderer.appendChild(this.el.nativeElement, ink);\n        this.renderer.setAttribute(ink, 'aria-hidden', 'true');\n        this.renderer.setAttribute(ink, 'role', 'presentation');\n\n        if (!this.animationListener) {\n            this.animationListener = this.renderer.listen(ink, 'animationend', this.onAnimationEnd.bind(this));\n        }\n    }\n\n    remove() {\n        let ink = this.getInk();\n        if (ink) {\n            this.mouseDownListener && this.mouseDownListener();\n            this.animationListener && this.animationListener();\n            this.mouseDownListener = null;\n            this.animationListener = null;\n\n            DomHandler.removeElement(ink);\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.config && this.config.ripple) {\n            this.remove();\n        }\n    }\n}\n\n@NgModule({\n    imports: [Ripple],\n    exports: [Ripple]\n})\nexport class RippleModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAKA;;;AAGG;MAQU,MAAM,CAAA;AACuB,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAAiC,IAAA,MAAA,CAAA;IAApM,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAU,QAAmB,EAAS,EAAc,EAAS,IAAY,EAAqB,MAAqB,EAAA;QAAnL,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAqB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;AAE7N,IAAA,iBAAiB,CAAe;AAEhC,IAAA,iBAAiB,CAAe;AAEhC,IAAA,OAAO,CAAM;IAEb,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACnC,gBAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;oBAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;oBACd,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACnH,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;AACzB,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM,EAAE;YACnF,OAAO;AACV,SAAA;AAED,QAAA,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACzD,YAAA,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;YACpH,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC;YAC5B,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QACzD,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAChG,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAEjG,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AAC9C,QAAA,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AAEzC,QAAA,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,MAAK;AAC3B,YAAA,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACxB,YAAA,IAAI,GAAG,EAAE;AACL,gBAAA,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AAC/C,aAAA;SACJ,EAAE,GAAG,CAAC,CAAC;KACX;IAED,MAAM,GAAA;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC;AAChD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AAC5F,gBAAA,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACtB,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACxB,QAAA,IAAI,GAAG,EAAE;AACL,YAAA,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AAC/C,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAY,EAAA;QACvB,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9B,SAAA;QACD,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;KAC/D;IAED,MAAM,GAAA;QACF,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;AAExD,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtG,SAAA;KACJ;IAED,MAAM,GAAA;AACF,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACxB,QAAA,IAAI,GAAG,EAAE;AACL,YAAA,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACnD,YAAA,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACnD,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC9B,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAE9B,YAAA,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACjC,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB,SAAA;KACJ;uGArGQ,MAAM,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EACK,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FADpE,MAAM,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAN,MAAM,EAAA,UAAA,EAAA,CAAA;kBAPlB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,oBAAoB;AAC9B,qBAAA;AACJ,iBAAA,CAAA;;0BAEgB,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;;0BAAqG,QAAQ;;MA2GjL,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAZ,YAAY,EAAA,OAAA,EAAA,CA5GZ,MAAM,CAAA,EAAA,OAAA,EAAA,CAAN,MAAM,CAAA,EAAA,CAAA,CAAA;wGA4GN,YAAY,EAAA,CAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,MAAM,CAAC;oBACjB,OAAO,EAAE,CAAC,MAAM,CAAC;AACpB,iBAAA,CAAA;;;AC3HD;;AAEG;;;;"}