{"version": 3, "file": "primeng-animateonscroll.mjs", "sources": ["../../src/app/components/animateonscroll/animateonscroll.ts", "../../src/app/components/animateonscroll/primeng-animateonscroll.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { AfterViewInit, Directive, ElementRef, Input, NgModule, Renderer2, OnInit, Inject, PLATFORM_ID, booleanAttribute, numberAttribute } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n\ninterface AnimateOnScrollOptions {\n    root?: HTMLElement;\n    rootMargin?: string;\n    threshold?: number;\n}\n\n/**\n * AnimateOnScroll is used to apply animations to elements when entering or leaving the viewport during scrolling.\n * @group Components\n */\n@Directive({\n    selector: '[pAnimateOnScroll]',\n    host: {\n        '[class.p-animateonscroll]': 'true'\n    }\n})\nexport class AnimateOnScroll implements OnInit, AfterViewInit {\n    /**\n     * Selector to define the CSS class for enter animation.\n     * @group Props\n     */\n    @Input() enterClass: string | undefined;\n    /**\n     * Selector to define the CSS class for leave animation.\n     * @group Props\n     */\n    @Input() leaveClass: string | undefined;\n    /**\n     * Specifies the root option of the IntersectionObserver API.\n     * @group Props\n     */\n    @Input() root: HTMLElement | undefined | null;\n    /**\n     * Specifies the rootMargin option of the IntersectionObserver API.\n     * @group Props\n     */\n    @Input() rootMargin: string | undefined;\n    /**\n     * Specifies the threshold option of the IntersectionObserver API\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) threshold: number | undefined;\n    /**\n     * Whether the scroll event listener should be removed after initial run.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) once: boolean = true;\n\n    observer: IntersectionObserver | undefined;\n\n    resetObserver: any;\n\n    isObserverActive: boolean = false;\n\n    animationState: any;\n\n    animationEndListener: VoidFunction | undefined;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private host: ElementRef, public el: ElementRef, public renderer: Renderer2) {}\n\n    ngOnInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.renderer.setStyle(this.host.nativeElement, 'opacity', this.enterClass ? '0' : '');\n        }\n    }\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.bindIntersectionObserver();\n        }\n    }\n\n    get options(): AnimateOnScrollOptions {\n        return {\n            root: this.root,\n            rootMargin: this.rootMargin,\n            threshold: this.threshold\n        };\n    }\n\n    bindIntersectionObserver() {\n        this.observer = new IntersectionObserver(([entry]) => {\n            if (this.isObserverActive) {\n                if (entry.boundingClientRect.top > 0) {\n                    entry.isIntersecting ? this.enter() : this.leave();\n                }\n            } else if (entry.isIntersecting) {\n                this.enter();\n            }\n\n            this.isObserverActive = true;\n        }, this.options);\n\n        setTimeout(() => this.observer.observe(this.host.nativeElement), 0);\n\n        // Reset\n\n        this.resetObserver = new IntersectionObserver(\n            ([entry]) => {\n                if (entry.boundingClientRect.top > 0 && !entry.isIntersecting) {\n                    this.host.nativeElement.style.opacity = this.enterClass ? '0' : '';\n                    DomHandler.removeMultipleClasses(this.host.nativeElement, [this.enterClass, this.leaveClass]);\n\n                    this.resetObserver.unobserve(this.host.nativeElement);\n                }\n\n                this.animationState = undefined;\n            },\n            { ...this.options, threshold: 0 }\n        );\n    }\n\n    enter() {\n        if (this.animationState !== 'enter' && this.enterClass) {\n            this.host.nativeElement.style.opacity = '';\n            DomHandler.removeMultipleClasses(this.host.nativeElement, this.leaveClass);\n            DomHandler.addMultipleClasses(this.host.nativeElement, this.enterClass);\n\n            this.once && this.unbindIntersectionObserver();\n\n            this.bindAnimationEvents();\n            this.animationState = 'enter';\n        }\n    }\n\n    leave() {\n        if (this.animationState !== 'leave' && this.leaveClass) {\n            this.host.nativeElement.style.opacity = this.enterClass ? '0' : '';\n            DomHandler.removeMultipleClasses(this.host.nativeElement, this.enterClass);\n            DomHandler.addMultipleClasses(this.host.nativeElement, this.leaveClass);\n\n            this.bindAnimationEvents();\n            this.animationState = 'leave';\n        }\n    }\n\n    bindAnimationEvents() {\n        if (!this.animationEndListener) {\n            this.animationEndListener = this.renderer.listen(this.host.nativeElement, 'animationend', () => {\n                DomHandler.removeMultipleClasses(this.host.nativeElement, [this.enterClass, this.leaveClass]);\n                !this.once && this.resetObserver.observe(this.host.nativeElement);\n                this.unbindAnimationEvents();\n            });\n        }\n    }\n\n    unbindAnimationEvents() {\n        if (this.animationEndListener) {\n            this.animationEndListener();\n            this.animationEndListener = null;\n        }\n    }\n\n    unbindIntersectionObserver() {\n        this.observer?.unobserve(this.host.nativeElement);\n        this.resetObserver?.unobserve(this.host.nativeElement);\n        this.isObserverActive = false;\n    }\n\n    ngOnDestroy() {\n        this.unbindAnimationEvents();\n        this.unbindIntersectionObserver();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [AnimateOnScroll],\n    declarations: [AnimateOnScroll]\n})\nexport class AnimateOnScrollModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAUA;;;AAGG;MAOU,eAAe,CAAA;AA0Cc,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,IAAA,CAAA;AAAyB,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAzChK;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,IAAI,CAAiC;AAC9C;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACoC,IAAA,SAAS,CAAqB;AACrE;;;AAGG;IACqC,IAAI,GAAY,IAAI,CAAC;AAE7D,IAAA,QAAQ,CAAmC;AAE3C,IAAA,aAAa,CAAM;IAEnB,gBAAgB,GAAY,KAAK,CAAC;AAElC,IAAA,cAAc,CAAM;AAEpB,IAAA,oBAAoB,CAA2B;IAE/C,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAU,IAAgB,EAAS,EAAc,EAAS,QAAmB,EAAA;QAA7I,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAAI;IAEvL,QAAQ,GAAA;AACJ,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAC1F,SAAA;KACJ;IAED,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACnC,SAAA;KACJ;AAED,IAAA,IAAI,OAAO,GAAA;QACP,OAAO;YACH,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;SAC5B,CAAC;KACL;IAED,wBAAwB,GAAA;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,KAAI;YACjD,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACvB,gBAAA,IAAI,KAAK,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,EAAE;AAClC,oBAAA,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AACtD,iBAAA;AACJ,aAAA;iBAAM,IAAI,KAAK,CAAC,cAAc,EAAE;gBAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,aAAA;AAED,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,SAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAEjB,QAAA,UAAU,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;;QAIpE,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAoB,CACzC,CAAC,CAAC,KAAK,CAAC,KAAI;AACR,YAAA,IAAI,KAAK,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBAC3D,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,EAAE,CAAC;AACnE,gBAAA,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBAE9F,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzD,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;AACpC,SAAC,EACD,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,CACpC,CAAC;KACL;IAED,KAAK,GAAA;QACD,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;YACpD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AAC3C,YAAA,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC3E,YAAA,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAExE,YAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAE/C,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;AACjC,SAAA;KACJ;IAED,KAAK,GAAA;QACD,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;YACpD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,EAAE,CAAC;AACnE,YAAA,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AAC3E,YAAA,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAExE,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;AACjC,SAAA;KACJ;IAED,mBAAmB,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,MAAK;AAC3F,gBAAA,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9F,gBAAA,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClE,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACjC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,qBAAqB,GAAA;QACjB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACvD,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;KACjC;IAED,WAAW,GAAA;QACP,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,0BAA0B,EAAE,CAAC;KACrC;uGAlJQ,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA0CJ,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA1CpE,eAAe,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAyBJ,eAAe,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAKf,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FA9B3B,eAAe,EAAA,UAAA,EAAA,CAAA;kBAN3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,IAAI,EAAE;AACF,wBAAA,2BAA2B,EAAE,MAAM;AACtC,qBAAA;AACJ,iBAAA,CAAA;;0BA2CgB,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;mHArCpE,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKiC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;;MA4H7B,qBAAqB,CAAA;uGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,EA1JrB,YAAA,EAAA,CAAA,eAAe,CAsJd,EAAA,OAAA,EAAA,CAAA,YAAY,aAtJb,eAAe,CAAA,EAAA,CAAA,CAAA;AA0Jf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YAJpB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBALjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,eAAe,CAAC;oBAC1B,YAAY,EAAE,CAAC,eAAe,CAAC;AAClC,iBAAA,CAAA;;;AC7KD;;AAEG;;;;"}