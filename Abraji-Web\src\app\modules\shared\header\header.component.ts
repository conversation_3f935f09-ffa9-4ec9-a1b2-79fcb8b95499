import { Component, inject, OnInit } from '@angular/core';
import { HeaderService } from '../../../core/user-services/services/header.service';
import { ManagerModel } from '../../../core/user-services/api/header';
import { AuthService } from '../../../core/auth-services/services/auth.service';
import { HttpErrorResponse } from '@angular/common/http';
import { CacheService } from '../../../core/common-services/services/cache.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit {
  private router = inject(Router);
  private authService = inject(AuthService);
  private headerService = inject(HeaderService);
  private cashService = inject(CacheService);
  user: ManagerModel | null = null;
  error: string | null = null;
  currentUser = {
    username: '', // Initial empty state
    email: '',
  };

  constructor() {}
  dropdownOpen = false;

  users: ManagerModel[] = [];

  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  ngOnInit(): void {
    this.getManager();
  }

  getManager(): void {
    this.headerService.getManager().subscribe({
      next: (data: any) => {
        this.user = data;
        this.headerService.saveCredentials(this.user as ManagerModel);
        this.currentUser = this.headerService.getCredentials();        // get user accounts
        this.users = this.authService.getAccounts().filter((value, index, self) => value.username != this.currentUser.username);
      },
      error: (error: HttpErrorResponse) => {
        this.error = `Error: ${error.message}`;
      },
    });
  }

  switchAccount(user: ManagerModel): void {
    this.cashService.clearAllCaches();
    this.authService.performLogin({ username: user.username, password: user.email }).subscribe({
      next: (response) => {
        this.headerService.saveCredentials(user as ManagerModel);
        this.getManager();
        window.location.reload();
        this.error = null;
        console.log('Switched account to', user);
      },
      error: (error) => {
        this.error = 'Error switching account';
      },
    });

  }

  logout(): void {
    // remove storage data
    this.authService.logout();
  }

}
