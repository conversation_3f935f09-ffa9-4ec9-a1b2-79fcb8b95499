/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// Below are constants for LView indices to help us look up LView members
// without having to remember the specific indices.
// Uglify will inline these when minifying so there shouldn't be a cost.
export const HOST = 0;
export const TVIEW = 1;
// Shared with LContainer
export const FLAGS = 2;
export const PARENT = 3;
export const NEXT = 4;
export const T_HOST = 5;
// End shared with LContainer
export const HYDRATION = 6;
export const CLEANUP = 7;
export const CONTEXT = 8;
export const INJECTOR = 9;
export const ENVIRONMENT = 10;
export const RENDERER = 11;
export const CHILD_HEAD = 12;
export const CHILD_TAIL = 13;
// FIXME(misko): Investigate if the three declarations aren't all same thing.
export const DECLARATION_VIEW = 14;
export const DECLARATION_COMPONENT_VIEW = 15;
export const DECLARATION_LCONTAINER = 16;
export const PREORDER_HOOK_FLAGS = 17;
export const QUERIES = 18;
export const ID = 19;
export const EMBEDDED_VIEW_INJECTOR = 20;
export const ON_DESTROY_HOOKS = 21;
export const EFFECTS_TO_SCHEDULE = 22;
export const REACTIVE_TEMPLATE_CONSUMER = 23;
/**
 * Size of LView's header. Necessary to adjust for it when setting slots.
 *
 * IMPORTANT: `HEADER_OFFSET` should only be referred to the in the `ɵɵ*` instructions to translate
 * instruction index into `LView` index. All other indexes should be in the `LView` index space and
 * there should be no need to refer to `HEADER_OFFSET` anywhere else.
 */
export const HEADER_OFFSET = 25;
//# sourceMappingURL=data:application/json;base64,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