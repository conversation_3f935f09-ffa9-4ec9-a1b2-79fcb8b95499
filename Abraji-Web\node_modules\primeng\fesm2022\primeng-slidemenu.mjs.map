{"version": 3, "file": "primeng-slidemenu.mjs", "sources": ["../../src/app/components/slidemenu/slidemenu.ts", "../../src/app/components/slidemenu/primeng-slidemenu.ts"], "sourcesContent": ["import { AnimationEvent, animate, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    ViewRef,\n    booleanAttribute,\n    effect,\n    forwardRef,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { MenuItem, OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { CaretLeftIcon } from 'primeng/icons/caretleft';\n\n@Component({\n    selector: 'p-slideMenuSub',\n    template: `\n        <ul\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-slidemenu-root-list': root, 'p-active-submenu': isActive }\"\n            [id]=\"menuId + '_list'\"\n            [ngStyle]=\"{\n                'width.px': menuWidth,\n                'left.px': root ? slideMenu.left : slideMenu.menuWidth,\n                'transition-property': root ? 'left' : 'none',\n                'transition-duration': effectDuration + 'ms',\n                'transition-timing-function': easing\n            }\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focusin)=\"menuFocus.emit($event)\"\n            [attr.data-pc-state]=\"isActive ? 'active' : 'inactive'\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"itemMouseEnter.emit({ originalEvent: $event, processedItem })\">\n                        <a\n                            *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                            [attr.href]=\"getItemProp(processedItem, 'url')\"\n                            [attr.aria-hidden]=\"true\"\n                            [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                            [attr.data-pc-section]=\"'action'\"\n                            [target]=\"getItemProp(processedItem, 'target')\"\n                            [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                            [attr.tabindex]=\"-1\"\n                            pRipple\n                        >\n                            <span\n                                *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                class=\"p-menuitem-icon\"\n                                [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                [attr.data-pc-section]=\"'icon'\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.tabindex]=\"-1\"\n                            >\n                            </span>\n                            <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                {{ getItemLabel(processedItem) }}\n                            </span>\n                            <ng-template #htmlLabel>\n                                <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                            </ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                            <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                <AngleRightIcon *ngIf=\"!slideMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                <ng-template *ngTemplateOutlet=\"slideMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                            </ng-container>\n                        </a>\n                        <a\n                            *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                            [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                            [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                            [attr.tabindex]=\"-1\"\n                            [attr.aria-hidden]=\"true\"\n                            [attr.data-pc-section]=\"'action'\"\n                            [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                            [routerLinkActive]=\"'p-menuitem-link-active'\"\n                            [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                            [target]=\"getItemProp(processedItem, 'target')\"\n                            [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                            [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                            [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                            [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                            [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                            [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                            [state]=\"getItemProp(processedItem, 'state')\"\n                            pRipple\n                        >\n                            <span\n                                *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                class=\"p-menuitem-icon\"\n                                [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                [attr.data-pc-section]=\"'icon'\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.tabindex]=\"-1\"\n                            >\n                            </span>\n                            <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                {{ getItemLabel(processedItem) }}\n                            </span>\n                            <ng-template #htmlLabel>\n                                <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                            </ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                            <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                <AngleRightIcon *ngIf=\"!slideMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                <ng-template *ngTemplateOutlet=\"slideMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                            </ng-container>\n                        </a>\n                    </div>\n\n                    <p-slideMenuSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        class=\"p-submenu\"\n                        [items]=\"processedItem.items\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        [menuWidth]=\"menuWidth\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"itemMouseEnter.emit($event)\"\n                    ></p-slideMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class SlideMenuSub {\n    @Input() items: any[];\n\n    @Input({ transform: numberAttribute }) menuWidth: number;\n\n    @Input({ transform: booleanAttribute }) root: boolean | undefined = false;\n\n    @Input() easing: string = 'ease-out';\n\n    @Input({ transform: numberAttribute }) effectDuration: number;\n\n    @Input({ transform: booleanAttribute }) autoDisplay: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n\n    @Input({ transform: booleanAttribute }) popup: boolean | undefined;\n\n    @Input() menuId: string | undefined;\n\n    @Input() ariaLabel: string | undefined;\n\n    @Input() ariaLabelledBy: string | undefined;\n\n    @Input({ transform: numberAttribute }) level: number = 0;\n\n    @Input() focusedItemId: string | undefined;\n\n    @Input() activeItemPath: any[];\n\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n\n    @Output() itemClick: EventEmitter<any> = new EventEmitter();\n\n    @Output() itemMouseEnter: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuFocus: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuBlur: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuKeydown: EventEmitter<any> = new EventEmitter();\n\n    @ViewChild('sublist', { static: true }) sublistViewChild: ElementRef;\n\n    get isActive() {\n        return -this.slideMenu.left == this.level * this.menuWidth;\n    }\n\n    constructor(@Inject(DOCUMENT) private document: Document, public el: ElementRef, public renderer: Renderer2, private cd: ChangeDetectorRef, @Inject(forwardRef(() => SlideMenu)) public slideMenu: SlideMenu) {}\n\n    getItemProp(processedItem: any, name: string, params: any | null = null) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n\n    getItemId(processedItem: any): string {\n        return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n    }\n\n    getItemKey(processedItem: any): string {\n        return this.getItemId(processedItem);\n    }\n\n    getItemClass(processedItem: any) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem': true,\n            'p-menuitem-active': this.isItemActive(processedItem),\n            'p-focus': this.isItemFocused(processedItem),\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n\n    getItemLabel(processedItem: any): string {\n        return this.getItemProp(processedItem, 'label');\n    }\n\n    getSeparatorItemClass(processedItem: any) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem-separator': true\n        };\n    }\n\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n\n    getAriaPosInset(index: number) {\n        return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n\n    isItemVisible(processedItem: any): boolean {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n\n    isItemActive(processedItem: any): boolean {\n        if (this.activeItemPath) {\n            return this.activeItemPath.some((path) => path.key === processedItem.key);\n        }\n    }\n\n    isItemDisabled(processedItem: any): boolean {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n\n    isItemFocused(processedItem: any): boolean {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n\n    isItemGroup(processedItem: any): boolean {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    onItemClick(event: any, processedItem: any) {\n        this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n        this.itemClick.emit({ originalEvent: event, processedItem, isFocus: true });\n        event.preventDefault();\n    }\n\n    onMenuKeyDown(event: KeyboardEvent) {\n        this.menuKeydown.emit(event);\n    }\n}\n/**\n * SlideMenu displays submenus with slide animation.\n * @group Components\n */\n@Component({\n    selector: 'p-slideMenu',\n    template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'slidemenu'\"\n            [id]=\"id\"\n            [ngClass]=\"{ 'p-slidemenu p-component': true, 'p-slidemenu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"!popup || visible\"\n        >\n            <div\n                class=\"p-slidemenu-wrapper\"\n                [ngStyle]=\"{\n                    height: left ? viewportHeight + 'px' : 'auto',\n                    width: menuWidth + 'px'\n                }\"\n            >\n                <div #slideMenuContent class=\"p-slidemenu-content\" (focus)=\"logFocus($event, slideMenuContent)\">\n                    <p-slideMenuSub\n                        #rootmenu\n                        [root]=\"true\"\n                        [items]=\"processedItems\"\n                        [menuId]=\"id\"\n                        [tabindex]=\"!disabled ? tabindex : -1\"\n                        [ariaLabel]=\"ariaLabel\"\n                        [ariaLabelledBy]=\"ariaLabelledBy\"\n                        [baseZIndex]=\"baseZIndex\"\n                        [autoZIndex]=\"autoZIndex\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuWidth]=\"menuWidth\"\n                        [popup]=\"popup\"\n                        [effectDuration]=\"effectDuration\"\n                        [easing]=\"easing\"\n                        [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                        [activeItemPath]=\"activeItemPath()\"\n                        (itemClick)=\"onItemClick($event)\"\n                        (menuFocus)=\"onMenuFocus($event)\"\n                        (menuKeydown)=\"onKeyDown($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    ></p-slideMenuSub>\n                </div>\n                <a\n                    #backward\n                    class=\"p-slidemenu-backward p-menuitem-link\"\n                    tabindex=\"0\"\n                    [ngStyle]=\"{\n                        display: left ? 'block' : 'none'\n                    }\"\n                    (click)=\"goBack($event)\"\n                    (keydown)=\"onNavigationKeyDown($event)\"\n                    [attr.data-pc-section]=\"'navigation'\"\n                >\n                    <CaretLeftIcon *ngIf=\"!backIconTemplate\" [styleClass]=\"'p-slidemenu-backward-icon'\" [ngStyle]=\"{ 'vertical-align': 'middle' }\" />\n                    <ng-template *ngTemplateOutlet=\"backIconTemplate\"></ng-template>\n                    <span>{{ backLabel }}</span>\n                </a>\n            </div>\n        </div>\n    `,\n    animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0, transform: 'scaleY(0.8)' }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({ opacity: 0 }))])])],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./slidemenu.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class SlideMenu implements OnInit, AfterContentInit, OnDestroy {\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    @Input() set model(value: MenuItem[] | undefined) {\n        this._model = value;\n        this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model(): MenuItem[] | undefined {\n        return this._model;\n    }\n    /**\n     * Width of the submenus.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) menuWidth: number = 190;\n    /**\n     * Height of the scrollable area, a scrollbar appears if a menu height is longer than this value.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) viewportHeight: number = 180;\n    /**\n     * Duration of the sliding animation in milliseconds.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) effectDuration: number = 250;\n    /**\n     * Easing animation to use for sliding.\n     * @group Props\n     */\n    @Input() easing: string = 'ease-out';\n    /**\n     * Label of element to navigate back.\n     * @group Props\n     */\n    @Input() backLabel: string = 'Back';\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean = false;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n    /**\n     * Defines if menu would displayed as a popup.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) popup: boolean | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element.\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Whether to show a root submenu on mouse over.\n     * @defaultValue true\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoDisplay: boolean | undefined = true;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.1s linear';\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Callback to invoke when overlay menu is shown.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when overlay menu is hidden.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<any> = new EventEmitter<any>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @ViewChild('rootmenu') rootmenu: SlideMenuSub | undefined;\n\n    @ViewChild('container') containerViewChild: ElementRef<any> | undefined;\n\n    @ViewChild('backward') set backward(element: ElementRef) {\n        this.backwardViewChild = element;\n    }\n\n    @ViewChild('slideMenuContent') slideMenuContentViewChild: ElementRef<any> | undefined;\n\n    submenuIconTemplate: Nullable<TemplateRef<any>>;\n\n    backIconTemplate: TemplateRef<any>;\n\n    outsideClickListener: VoidListener;\n\n    resizeListener: VoidListener;\n\n    transitionEndListener: VoidListener;\n\n    transitionStartListener: VoidListener;\n\n    backwardViewChild: ElementRef;\n\n    transition: boolean = false;\n\n    left: number = 0;\n\n    animating: boolean = false;\n\n    target: any;\n\n    visible: boolean | undefined;\n\n    relativeAlign: boolean | undefined;\n\n    private window: Window;\n\n    focused: boolean = false;\n\n    activeItemPath = signal<any>([]);\n\n    focusedItemInfo = signal<any>({ index: -1, level: 0, parentKey: '' });\n\n    searchValue: string = '';\n\n    searchTimeout: any;\n\n    _processedItems: any[];\n\n    _model: MenuItem[] | undefined;\n\n    container: any;\n\n    itemClick: boolean = false;\n\n    get visibleItems() {\n        const processedItem = this.activeItemPath().find((p) => p.key === this.focusedItemInfo().parentKey);\n\n        return processedItem ? processedItem.items : this.processedItems;\n    }\n\n    get processedItems() {\n        if (!this._processedItems || !this._processedItems.length) {\n            this._processedItems = this.createProcessedItems(this.model || []);\n        }\n        return this._processedItems;\n    }\n\n    get focusedItemId() {\n        const focusedItem = this.focusedItemInfo();\n        return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n    }\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        public el: ElementRef,\n        public renderer: Renderer2,\n        public cd: ChangeDetectorRef,\n        public config: PrimeNGConfig,\n        public overlayService: OverlayService\n    ) {\n        this.window = this.document.defaultView as Window;\n        effect(() => {\n            const path = this.activeItemPath();\n            if (this.popup) {\n                if (ObjectUtils.isNotEmpty(path)) {\n                    this.bindOutsideClickListener();\n                    this.bindResizeListener();\n                } else {\n                    this.unbindOutsideClickListener();\n                    this.unbindResizeListener();\n                }\n            }\n        });\n    }\n\n    documentFocusListener: any;\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n    }\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'backicon':\n                    this.backIconTemplate = item.template;\n                    break;\n\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    createProcessedItems(items: any, level: number = 0, parent: any = {}, parentKey: any = '') {\n        const processedItems = [];\n\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newItem = {\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n\n                newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n\n        return processedItems;\n    }\n\n    getItemProp(item: any, name: string) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n\n    getProccessedItemLabel(processedItem: any) {\n        return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n\n    getItemLabel(item: any) {\n        return this.getItemProp(item, 'label');\n    }\n\n    isProcessedItemGroup(processedItem: any): boolean {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    isSelected(processedItem: any): boolean {\n        return this.activeItemPath().some((p) => p.key === processedItem.key);\n    }\n\n    isValidSelectedItem(processedItem: any): boolean {\n        return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n\n    isValidItem(processedItem: any): boolean {\n        return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n    }\n\n    isItemDisabled(item: any): boolean {\n        return this.getItemProp(item, 'disabled');\n    }\n\n    isItemSeparator(item: any): boolean {\n        return this.getItemProp(item, 'separator');\n    }\n\n    isItemMatched(processedItem: any): boolean {\n        return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n\n    isProccessedItemGroup(processedItem: any): boolean {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    onOverlayClick(event: MouseEvent) {\n        if (this.popup) {\n            this.overlayService.add({\n                originalEvent: event,\n                target: this.el.nativeElement\n            });\n        }\n    }\n\n    goBack(event) {\n        this.animate('left');\n\n        event.stopPropagation();\n        event.preventDefault();\n    }\n\n    onItemClick(event: any) {\n        if (this.transition) {\n            return;\n        } else {\n            if (!this.itemClick) {\n                this.itemClick = true;\n                this.onMenuFocus();\n            }\n            const { originalEvent, processedItem } = event;\n            const grouped = this.isProcessedItemGroup(processedItem);\n            const focusedItemInfo = this.focusedItemInfo();\n\n            if (grouped) {\n                this.focusedItemInfo.set({ ...focusedItemInfo, index: -1, level: focusedItemInfo.level + 1, parentKey: processedItem.key, item: processedItem.item });\n                this.animate('right');\n            } else {\n                this.onItemChange(event);\n                this.popup && this.hide();\n            }\n        }\n    }\n\n    onItemMouseEnter(event: any) {\n        this.onItemChange(event);\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        if (!this.transition) {\n            const metaKey = event.metaKey || event.ctrlKey;\n            switch (event.code) {\n                case 'ArrowDown':\n                    this.onArrowDownKey(event);\n                    break;\n\n                case 'ArrowUp':\n                    this.onArrowUpKey(event);\n                    break;\n\n                case 'ArrowLeft':\n                    this.onArrowLeftKey(event);\n                    break;\n\n                case 'ArrowRight':\n                    this.onArrowRightKey(event);\n                    break;\n\n                case 'Home':\n                    this.onHomeKey(event);\n                    break;\n\n                case 'End':\n                    this.onEndKey(event);\n                    break;\n\n                case 'Space':\n                    this.onSpaceKey(event);\n                    break;\n\n                case 'Enter':\n                    this.onEnterKey(event);\n                    break;\n\n                case 'Escape':\n                    this.onEscapeKey(event);\n                    break;\n\n                case 'Tab':\n                    this.onTabKey(event);\n                    break;\n\n                case 'PageDown':\n                case 'PageUp':\n                case 'Backspace':\n                case 'ShiftLeft':\n                case 'ShiftRight':\n                    //NOOP\n                    break;\n\n                default:\n                    if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                        this.searchItems(event, event.key);\n                    }\n\n                    break;\n            }\n        }\n    }\n\n    onNavigationKeyDown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'Enter':\n            case 'Space':\n                this.onArrowLeftKey(event);\n                const focusedItemInfo = this.focusedItemInfo();\n                this.focusedItemInfo.set({\n                    ...focusedItemInfo,\n                    index: -1,\n                    item: null\n                });\n                break;\n            default:\n                break;\n        }\n    }\n\n    animate(to: string) {\n        switch (to) {\n            case 'right':\n                this.left -= this.menuWidth;\n                break;\n            case 'left':\n                this.left += this.menuWidth;\n                break;\n\n            default:\n                break;\n        }\n\n        this.animating = true;\n        setTimeout(() => (this.animating = false), this.effectDuration);\n    }\n\n    onArrowDownKey(event: KeyboardEvent) {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n\n        this.changeFocusedItemIndex(event, itemIndex);\n        event.preventDefault();\n    }\n\n    onArrowRightKey(event: KeyboardEvent) {\n        const focusedItemInfo = this.focusedItemInfo();\n\n        if (focusedItemInfo.index === -1) {\n            focusedItemInfo.index = 0;\n        }\n\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        if (grouped) {\n            let { index, level, key, item } = processedItem;\n            this.onItemChange({ originalEvent: event, processedItem });\n            this.focusedItemInfo.set({ index: 0, level: level, parentKey: key });\n\n            this.searchValue = '';\n            this.animate('right');\n        }\n\n        event.preventDefault();\n    }\n\n    onArrowUpKey(event: KeyboardEvent) {\n        if (event.altKey) {\n            if (this.focusedItemInfo().index !== -1) {\n                const processedItem = this.visibleItems[this.focusedItemInfo().index];\n                const grouped = this.isProccessedItemGroup(processedItem);\n\n                !grouped && this.onItemChange({ originalEvent: event, processedItem });\n            }\n\n            this.popup && this.hide(event, true);\n            event.preventDefault();\n        } else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n\n            event.preventDefault();\n        }\n    }\n\n    onArrowLeftKey(event: KeyboardEvent) {\n        const focusedItemInfo = this.focusedItemInfo();\n        if (focusedItemInfo.index === -1) {\n            focusedItemInfo.index = 0;\n        }\n\n        const processedItem = this.visibleItems[focusedItemInfo.index];\n        const parentItem = this.activeItemPath().find((p) => p.key === processedItem.parentKey);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n\n        if (!root) {\n            let { level, index, parentKey } = parentItem;\n            this.focusedItemInfo.set({ index, level, parentKey, item: parentItem.item });\n            this.searchValue = '';\n        }\n\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== focusedItemInfo.parentKey);\n        this.activeItemPath.set(activeItemPath);\n        parentItem && this.animate('left');\n        event.preventDefault();\n    }\n\n    onHomeKey(event: KeyboardEvent) {\n        this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n        event.preventDefault();\n    }\n\n    onEndKey(event: KeyboardEvent) {\n        this.changeFocusedItemIndex(event, this.findLastItemIndex());\n        event.preventDefault();\n    }\n\n    onSpaceKey(event: KeyboardEvent) {\n        this.onEnterKey(event);\n    }\n\n    onEscapeKey(event: KeyboardEvent) {\n        if (this.popup) {\n            this.hide(event, true);\n            const focusedItemInfo = this.focusedItemInfo();\n            this.focusedItemInfo.set({\n                ...focusedItemInfo,\n                index: this.findLastFocusedItemIndex(),\n                item: null\n            });\n\n            event.preventDefault();\n        }\n    }\n\n    onTabKey(event: KeyboardEvent) {\n        if (this.backwardViewChild.nativeElement.style.display !== 'none') {\n            this.backwardViewChild.nativeElement.focus();\n        }\n\n        if (this.popup && !this.containerViewChild.nativeElement.contains(event.target)) {\n            this.hide();\n        }\n        event.preventDefault();\n    }\n\n    onEnterKey(event: KeyboardEvent) {\n        if (this.focusedItemInfo().index !== -1) {\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            if (grouped) {\n                this.onArrowRightKey(event);\n            } else {\n                const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n                const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n\n                anchorElement ? anchorElement.click() : element && element.click();\n\n                const focusedItemInfo = this.focusedItemInfo();\n                this.focusedItemInfo.set({\n                    ...focusedItemInfo,\n                    index: processedItem.index,\n                    item: processedItem.item\n                });\n            }\n        }\n\n        event.preventDefault();\n    }\n\n    onItemChange(event: any) {\n        const { processedItem, isFocus } = event;\n        if (ObjectUtils.isEmpty(processedItem)) return;\n\n        const { index, key, level, parentKey, items, item } = processedItem;\n        const grouped = ObjectUtils.isNotEmpty(items);\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n\n        grouped && activeItemPath.push(processedItem);\n        this.focusedItemInfo.set({ index, level, parentKey, item });\n        this.activeItemPath.set(activeItemPath);\n        isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n    }\n\n    onMenuFocus() {\n        this.focused = true;\n\n        this.bindOutsideClickListener();\n        this.bindTransitionListeners();\n\n        if (!this.left && this.focusedItemInfo().level > 0) {\n            this.focusedItemInfo.set({ index: 0, level: 0, parentKey: '', item: this.findVisibleItem(0).item });\n        }\n\n        if (this.focusedItemInfo().index === -1 && this.left < 0) {\n            this.focusedItemInfo.set({ ...this.focusedItemInfo(), index: 0 });\n        }\n\n        if (this.focusedItemInfo().index === -1 && !this.left) {\n            this.focusedItemInfo.set({ index: 0, level: 0, parentKey: '', item: this.findVisibleItem(0).item });\n        }\n    }\n\n    onMenuBlur() {\n        this.focused = false;\n        this.popup && this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        if (!this.popup) {\n            this.focusedItemInfo.set({\n                ...this.focusedItemInfo(),\n                index: -1,\n                item: null\n            });\n        }\n        this.searchValue = '';\n        !this.popup && this.unbindOutsideClickListener();\n    }\n\n    activeLevel = signal<number>(0);\n\n    bindTransitionListeners() {\n        if (!this.transitionStartListener) {\n            this.transitionStartListener = this.renderer.listen(this.rootmenu.sublistViewChild.nativeElement, 'transitionstart', (event) => {\n                this.transition = true;\n                event.preventDefault();\n            });\n        }\n        if (!this.transitionEndListener) {\n            this.transitionEndListener = this.renderer.listen(this.rootmenu.sublistViewChild.nativeElement, 'transitionend', (event) => {\n                const activeMenu = DomHandler.findSingle(this.rootmenu.el.nativeElement, `ul[data-pc-state=\"active\"]`);\n                const activeLevel = DomHandler.getAttribute(activeMenu.firstElementChild, 'aria-level') - 1;\n                this.activeLevel.set(activeLevel);\n\n                if (!this.left) {\n                    this.rootmenu.sublistViewChild.nativeElement.focus();\n                } else {\n                    const activeLevel = DomHandler.getAttribute(activeMenu.firstElementChild, 'aria-level') - 1;\n                    this.activeLevel.set(activeLevel);\n\n                    if (this.focusedItemInfo().level > this.activeLevel()) {\n                        let newActiveItemPath = this.activeItemPath().slice(0, this.activeItemPath().length - 1);\n                        let lastActiveParent = newActiveItemPath[newActiveItemPath.length - 1];\n                        this.focusedItemInfo.set({ index: -1, level: this.activeLevel(), parentKey: lastActiveParent.key });\n                        this.activeItemPath.set(newActiveItemPath);\n                    }\n                }\n                this.transition = false;\n                event.preventDefault();\n            });\n        }\n    }\n\n    unbindTransitionListeners() {\n        if (this.transitionEndListener) {\n            this.transitionEndListener();\n            this.transitionEndListener = null;\n        }\n\n        if (this.transitionStartListener) {\n            this.transitionStartListener();\n            this.transitionStartListener = null;\n        }\n    }\n\n    onOverlayAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                if (this.popup) {\n                    this.container = event.element;\n                    this.moveOnTop();\n                    this.onShow.emit({});\n                    this.appendOverlay();\n                    this.alignOverlay();\n                    this.bindOutsideClickListener();\n                    this.bindResizeListener();\n\n                    DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n                    this.scrollInView();\n                }\n                break;\n\n            case 'void':\n                this.onOverlayHide();\n                this.onHide.emit({});\n                break;\n        }\n    }\n\n    alignOverlay() {\n        if (this.relativeAlign) DomHandler.relativePosition(this.container, this.target);\n        else DomHandler.absolutePosition(this.container, this.target);\n    }\n\n    onOverlayAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                ZIndexUtils.clear(event.element);\n                break;\n        }\n    }\n\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.containerViewChild.nativeElement);\n            else DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n\n    restoreOverlayAppend() {\n        if (this.containerViewChild && this.appendTo) {\n            this.renderer.appendChild(this.el.nativeElement, this.container);\n        }\n    }\n\n    moveOnTop() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n        }\n    }\n\n    /**\n     * Hides the popup menu.\n     * @group Method\n     */\n    hide(event?, isFocus?: boolean) {\n        if (this.popup) {\n            this.onHide.emit({});\n            this.visible = false;\n        }\n        isFocus && DomHandler.focus(this.target || this.rootmenu.sublistViewChild.nativeElement);\n    }\n\n    /**\n     * Toggles the visibility of the popup menu.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    toggle(event: any) {\n        this.visible ? this.hide(event, true) : this.show(event);\n    }\n\n    /**\n     * Displays the popup menu.\n     * @param {Event} even - Browser event.\n     * @group Method\n     */\n    show(event: any, isFocus?) {\n        if (this.popup) {\n            this.visible = true;\n            this.target = event.currentTarget;\n        }\n\n        this.focusedItemInfo.set({ index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '' });\n\n        if (!this.popup) {\n            isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n        }\n        this.cd.markForCheck();\n    }\n\n    searchItems(event: any, char: string) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let itemIndex = -1;\n        let matched = false;\n\n        if (this.focusedItemInfo().index !== -1) {\n            itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem));\n            itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n        } else {\n            itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n        }\n\n        if (itemIndex !== -1) {\n            matched = true;\n        }\n\n        if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n            itemIndex = this.findFirstFocusedItemIndex();\n        }\n\n        if (itemIndex !== -1) {\n            this.changeFocusedItemIndex(event, itemIndex);\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n\n    findVisibleItem(index) {\n        return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n    }\n\n    findLastFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n\n    findLastItemIndex() {\n        return ObjectUtils.findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n    }\n\n    findPrevItemIndex(index: number) {\n        const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n\n        return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n\n    findNextItemIndex(index: number) {\n        const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n\n        return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n\n    findFirstFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n\n        return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n\n    findFirstItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n    }\n\n    findSelectedItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n    }\n\n    changeFocusedItemIndex(event: any, index: number) {\n        if (this.focusedItemInfo().index !== index) {\n            this.focusedItemInfo.set({ ...this.focusedItemInfo(), index });\n            this.scrollInView();\n        }\n    }\n\n    scrollInView(index: number = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n        const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    if (!DomHandler.isTouchDevice()) {\n                        this.hide(event, true);\n                    }\n                });\n            }\n        }\n    }\n\n    bindOutsideClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                    const isOutsideContainer = this.containerViewChild && !this.containerViewChild.nativeElement.contains(event.target);\n                    const isOutsideTarget = this.popup ? !(this.target && (this.target === event.target || this.target.contains(event.target))) : true;\n\n                    if (this.popup) {\n                        if (isOutsideContainer && isOutsideTarget) {\n                            this.onMenuBlur();\n                            this.hide();\n                        }\n                    } else {\n                        if (isOutsideContainer && isOutsideTarget && this.focused) {\n                            this.onMenuBlur();\n                        }\n                    }\n                });\n            }\n        }\n    }\n\n    unbindOutsideClickListener() {\n        if (this.outsideClickListener) {\n            this.outsideClickListener();\n            this.outsideClickListener = null;\n        }\n    }\n\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n\n    onOverlayHide() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n        this.left = 0;\n\n        if (!(this.cd as ViewRef).destroyed) {\n            this.target = null;\n        }\n\n        if (this.container) {\n            this.container = null;\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.popup) {\n            if (this.container && this.autoZIndex) {\n                ZIndexUtils.clear(this.container);\n            }\n\n            this.restoreOverlayAppend();\n            this.onOverlayHide();\n        }\n\n        this.unbindTransitionListeners();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule, CaretLeftIcon],\n    exports: [SlideMenu, RouterModule, TooltipModule, SharedModule],\n    declarations: [SlideMenu, SlideMenuSub]\n})\nexport class SlideMenuModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;MAqMa,YAAY,CAAA;AAiDiB,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA6B,IAAA,EAAA,CAAA;AAAmE,IAAA,SAAA,CAAA;AAhD/K,IAAA,KAAK,CAAQ;AAEiB,IAAA,SAAS,CAAS;IAEjB,IAAI,GAAwB,KAAK,CAAC;IAEjE,MAAM,GAAW,UAAU,CAAC;AAEE,IAAA,cAAc,CAAS;AAEtB,IAAA,WAAW,CAAsB;IAEjC,UAAU,GAAY,IAAI,CAAC;IAE5B,UAAU,GAAW,CAAC,CAAC;AAEtB,IAAA,KAAK,CAAsB;AAE1D,IAAA,MAAM,CAAqB;AAE3B,IAAA,SAAS,CAAqB;AAE9B,IAAA,cAAc,CAAqB;IAEL,KAAK,GAAW,CAAC,CAAC;AAEhD,IAAA,aAAa,CAAqB;AAElC,IAAA,cAAc,CAAQ;IAEQ,QAAQ,GAAW,CAAC,CAAC;AAElD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElD,IAAA,cAAc,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEvD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElD,IAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEjD,IAAA,WAAW,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEtB,IAAA,gBAAgB,CAAa;AAErE,IAAA,IAAI,QAAQ,GAAA;AACR,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;KAC9D;IAED,WAAsC,CAAA,QAAkB,EAAS,EAAc,EAAS,QAAmB,EAAU,EAAqB,EAA8C,SAAoB,EAAA;QAAtK,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAA8C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAW;KAAI;AAEhN,IAAA,WAAW,CAAC,aAAkB,EAAE,IAAY,EAAE,SAAqB,IAAI,EAAA;QACnE,OAAO,aAAa,IAAI,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC;KACvH;AAED,IAAA,SAAS,CAAC,aAAkB,EAAA;AACxB,QAAA,OAAO,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAI,CAAA,EAAA,aAAa,CAAC,GAAG,EAAE,CAAC;KACvH;AAED,IAAA,UAAU,CAAC,aAAkB,EAAA;AACzB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KACxC;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;AAC3C,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,mBAAmB,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AACrD,YAAA,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;AAC5C,YAAA,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;SACnD,CAAC;KACL;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;KACnD;AAED,IAAA,qBAAqB,CAAC,aAAkB,EAAA;QACpC,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;AAC3C,YAAA,sBAAsB,EAAE,IAAI;SAC/B,CAAC;KACL;IAED,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;KAC1I;AAED,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACrK;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK,CAAC;KAC/D;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;AAC7E,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,aAAkB,EAAA;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KACtD;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KAC/D;AAED,IAAA,WAAW,CAAC,aAAkB,EAAA;QAC1B,OAAO,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACtD;IAED,WAAW,CAAC,KAAU,EAAE,aAAkB,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/F,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5E,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,aAAa,CAAC,KAAoB,EAAA;AAC9B,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAChC;uGA1HQ,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAiDD,QAAQ,EAAwH,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,UAAU,CAAC,MAAM,SAAS,CAAC,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAjDtK,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,8FAGD,eAAe,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAEf,gBAAgB,CAIhB,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,eAAe,+CAEf,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAEhB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAEhB,eAAe,CAEf,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,gBAAgB,yGAQhB,eAAe,CAAA,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAMf,eAAe,CA3LzB,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsJT,EAunCkE,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,cAAc,gFAjnCxE,YAAY,CAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,YAAA,EAAA,YAAA,EAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBA9JxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsJT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAkDgB,MAAM;2BAAC,QAAQ,CAAA;;0BAAiH,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,SAAS,CAAC,CAAA;yCAhDtK,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEiC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEiC,cAAc,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE3B,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAEG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAEiC,gBAAgB,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAA;;AAiF1C;;;AAGG;MA4EU,SAAS,CAAA;AAoMY,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACtB,IAAA,EAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACA,IAAA,MAAA,CAAA;AACA,IAAA,cAAA,CAAA;AAzMX;;;AAGG;IACH,IAAa,KAAK,CAAC,KAA6B,EAAA;AAC5C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;KACvE;AACD,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AACD;;;AAGG;IACoC,SAAS,GAAW,GAAG,CAAC;AAC/D;;;AAGG;IACoC,cAAc,GAAW,GAAG,CAAC;AACpE;;;AAGG;IACoC,cAAc,GAAW,GAAG,CAAC;AACpE;;;AAGG;IACM,MAAM,GAAW,UAAU,CAAC;AACrC;;;AAGG;IACM,SAAS,GAAW,MAAM,CAAC;AACpC;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACoC,QAAQ,GAAW,CAAC,CAAC;AAC5D;;;AAGG;AACqC,IAAA,KAAK,CAAsB;AACnE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;;AAIG;IACqC,WAAW,GAAwB,IAAI,CAAC;AAChF;;;AAGG;IACM,qBAAqB,GAAW,iCAAiC,CAAC;AAC3E;;;AAGG;IACM,qBAAqB,GAAW,YAAY,CAAC;AACtD;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAE9B,IAAA,SAAS,CAAuC;AAEzD,IAAA,QAAQ,CAA2B;AAElC,IAAA,kBAAkB,CAA8B;IAExE,IAA2B,QAAQ,CAAC,OAAmB,EAAA;AACnD,QAAA,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;KACpC;AAE8B,IAAA,yBAAyB,CAA8B;AAEtF,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,gBAAgB,CAAmB;AAEnC,IAAA,oBAAoB,CAAe;AAEnC,IAAA,cAAc,CAAe;AAE7B,IAAA,qBAAqB,CAAe;AAEpC,IAAA,uBAAuB,CAAe;AAEtC,IAAA,iBAAiB,CAAa;IAE9B,UAAU,GAAY,KAAK,CAAC;IAE5B,IAAI,GAAW,CAAC,CAAC;IAEjB,SAAS,GAAY,KAAK,CAAC;AAE3B,IAAA,MAAM,CAAM;AAEZ,IAAA,OAAO,CAAsB;AAE7B,IAAA,aAAa,CAAsB;AAE3B,IAAA,MAAM,CAAS;IAEvB,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,cAAc,GAAG,MAAM,CAAM,EAAE,CAAC,CAAC;AAEjC,IAAA,eAAe,GAAG,MAAM,CAAM,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;IAEtE,WAAW,GAAW,EAAE,CAAC;AAEzB,IAAA,aAAa,CAAM;AAEnB,IAAA,eAAe,CAAQ;AAEvB,IAAA,MAAM,CAAyB;AAE/B,IAAA,SAAS,CAAM;IAEf,SAAS,GAAY,KAAK,CAAC;AAE3B,IAAA,IAAI,YAAY,GAAA;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC;AAEpG,QAAA,OAAO,aAAa,GAAG,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;KACpE;AAED,IAAA,IAAI,cAAc,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AACvD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACtE,SAAA;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;AAED,IAAA,IAAI,aAAa,GAAA;AACb,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3C,QAAA,OAAO,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAG,EAAA,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC,SAAS,GAAG,EAAE,CAAI,CAAA,EAAA,WAAW,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC;KAClO;AAED,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACrC,EAAc,EACd,QAAmB,EACnB,EAAqB,EACrB,MAAqB,EACrB,cAA8B,EAAA;QANX,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACrC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACrB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACrB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QAErC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;QAClD,MAAM,CAAC,MAAK;AACR,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,gBAAA,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;oBAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,iBAAA;AAAM,qBAAA;oBACH,IAAI,CAAC,0BAA0B,EAAE,CAAC;oBAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,qBAAqB,CAAM;IAE3B,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;KAC5C;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,oBAAoB,CAAC,KAAU,EAAE,KAAgB,GAAA,CAAC,EAAE,MAAc,GAAA,EAAE,EAAE,SAAA,GAAiB,EAAE,EAAA;QACrF,MAAM,cAAc,GAAG,EAAE,CAAC;QAE1B,KAAK;YACD,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AAC1B,gBAAA,MAAM,GAAG,GAAG,CAAC,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,GAAG,EAAE,IAAI,KAAK,CAAC;AAC9D,gBAAA,MAAM,OAAO,GAAG;oBACZ,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,GAAG;oBACH,MAAM;oBACN,SAAS;iBACZ,CAAC;gBAEF,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AAClF,gBAAA,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;AAEP,QAAA,OAAO,cAAc,CAAC;KACzB;IAED,WAAW,CAAC,IAAS,EAAE,IAAY,EAAA;AAC/B,QAAA,OAAO,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;KAClE;AAED,IAAA,sBAAsB,CAAC,aAAkB,EAAA;AACrC,QAAA,OAAO,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;KAC5E;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC1C;AAED,IAAA,oBAAoB,CAAC,aAAkB,EAAA;QACnC,OAAO,aAAa,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvE;AAED,IAAA,UAAU,CAAC,aAAkB,EAAA;QACzB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;KACzE;AAED,IAAA,mBAAmB,CAAC,aAAkB,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KAC5E;AAED,IAAA,WAAW,CAAC,aAAkB,EAAA;QAC1B,OAAO,CAAC,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KACnH;AAED,IAAA,cAAc,CAAC,IAAS,EAAA;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC7C;AAED,IAAA,eAAe,CAAC,IAAS,EAAA;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;KAC9C;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,iBAAiB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC;KAC7J;AAED,IAAA,qBAAqB,CAAC,aAAkB,EAAA;QACpC,OAAO,aAAa,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvE;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;QAC5B,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa;AAChC,aAAA,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAK,EAAA;AACR,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAErB,KAAK,CAAC,eAAe,EAAE,CAAC;QACxB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;QAClB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;AACV,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACjB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,aAAA;AACD,YAAA,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;YAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;AACzD,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAE/C,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,eAAe,CAAC,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AACtJ,gBAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACzB,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACzB,gBAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AAC7B,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;YAC/C,QAAQ,KAAK,CAAC,IAAI;AACd,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;oBAC3B,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oBACzB,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;oBAC3B,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC5B,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBACtB,MAAM;AAEV,gBAAA,KAAK,KAAK;AACN,oBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACrB,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oBACvB,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oBACvB,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBACxB,MAAM;AAEV,gBAAA,KAAK,KAAK;AACN,oBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACrB,MAAM;AAEV,gBAAA,KAAK,UAAU,CAAC;AAChB,gBAAA,KAAK,QAAQ,CAAC;AACd,gBAAA,KAAK,WAAW,CAAC;AACjB,gBAAA,KAAK,WAAW,CAAC;AACjB,gBAAA,KAAK,YAAY;;oBAEb,MAAM;AAEV,gBAAA;oBACI,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACzD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,qBAAA;oBAED,MAAM;AACb,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,mBAAmB,CAAC,KAAoB,EAAA;QACpC,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC3B,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/C,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;AACrB,oBAAA,GAAG,eAAe;oBAClB,KAAK,EAAE,CAAC,CAAC;AACT,oBAAA,IAAI,EAAE,IAAI;AACb,iBAAA,CAAC,CAAC;gBACH,MAAM;AACV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,EAAU,EAAA;AACd,QAAA,QAAQ,EAAE;AACN,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC;gBAC5B,MAAM;AACV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC;gBAC5B,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,UAAU,CAAC,OAAO,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;KACnE;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAEhJ,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC9C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;AAChC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAE/C,QAAA,IAAI,eAAe,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AAC9B,YAAA,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC;AAC7B,SAAA;AAED,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAC1D,QAAA,IAAI,OAAO,EAAE;YACT,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AAC3D,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;AAErE,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACzB,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAoB,EAAA;QAC7B,IAAI,KAAK,CAAC,MAAM,EAAE;YACd,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;gBACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,gBAAA,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AAC1E,aAAA;YAED,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACrC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAC/I,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAE9C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/C,QAAA,IAAI,eAAe,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AAC9B,YAAA,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC;AAC7B,SAAA;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,CAAC,SAAS,CAAC,CAAC;QACxF,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,EAAE;YACP,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC;AAC7C,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;AAC7E,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACzB,SAAA;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,eAAe,CAAC,SAAS,CAAC,CAAC;AACtG,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACxC,QAAA,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACnC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC9D,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAC7D,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;QAC5B,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACvB,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/C,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;AACrB,gBAAA,GAAG,eAAe;AAClB,gBAAA,KAAK,EAAE,IAAI,CAAC,wBAAwB,EAAE;AACtC,gBAAA,IAAI,EAAE,IAAI;AACb,aAAA,CAAC,CAAC;YAEH,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;AAC/D,YAAA,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAChD,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YAC7E,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;QACD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;QAC3B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC/B,aAAA;AAAM,iBAAA;gBACH,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,CAAG,EAAA,IAAI,CAAC,aAAa,CAAA,CAAE,CAAI,EAAA,CAAA,CAAC,CAAC;AAC7G,gBAAA,MAAM,aAAa,GAAG,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC;AAE/F,gBAAA,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;AAEnE,gBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/C,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;AACrB,oBAAA,GAAG,eAAe;oBAClB,KAAK,EAAE,aAAa,CAAC,KAAK;oBAC1B,IAAI,EAAE,aAAa,CAAC,IAAI;AAC3B,iBAAA,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAU,EAAA;AACnB,QAAA,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;AACzC,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC;YAAE,OAAO;AAE/C,QAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;QACpE,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,CAAC,SAAS,KAAK,GAAG,CAAC,CAAC;AAE7G,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACxC,QAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;KAC7E;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAE/B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE;AAChD,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AACvG,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;AACtD,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AACrE,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACnD,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AACvG,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3F,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACb,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBACrB,GAAG,IAAI,CAAC,eAAe,EAAE;gBACzB,KAAK,EAAE,CAAC,CAAC;AACT,gBAAA,IAAI,EAAE,IAAI;AACb,aAAA,CAAC,CAAC;AACN,SAAA;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;KACpD;AAED,IAAA,WAAW,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC;IAEhC,uBAAuB,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,iBAAiB,EAAE,CAAC,KAAK,KAAI;AAC3H,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,aAAC,CAAC,CAAC;AACN,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,eAAe,EAAE,CAAC,KAAK,KAAI;AACvH,gBAAA,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,CAAA,0BAAA,CAA4B,CAAC,CAAC;AACvG,gBAAA,MAAM,WAAW,GAAG,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,iBAAiB,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5F,gBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAElC,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;oBACZ,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACxD,iBAAA;AAAM,qBAAA;AACH,oBAAA,MAAM,WAAW,GAAG,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,iBAAiB,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5F,oBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;oBAElC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE;wBACnD,IAAI,iBAAiB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBACzF,IAAI,gBAAgB,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBACvE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC;AACpG,wBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAC9C,qBAAA;AACJ,iBAAA;AACD,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBACxB,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,yBAAyB,GAAA;QACrB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACvC,SAAA;KACJ;AAED,IAAA,uBAAuB,CAAC,KAAqB,EAAA;QACzC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;gBACV,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;oBAC/B,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACrB,IAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAE1B,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;oBAC/D,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrB,MAAM;AACb,SAAA;KACJ;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,aAAa;YAAE,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;YAC5E,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACjE;AAED,IAAA,qBAAqB,CAAC,KAAqB,EAAA;QACvC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;AACP,gBAAA,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM;AACb,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;AAAE,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;;gBAC9G,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9D,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC1C,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACpE,SAAA;KACJ;IAED,SAAS,GAAA;QACL,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtF,SAAA;KACJ;AAED;;;AAGG;IACH,IAAI,CAAC,KAAM,EAAE,OAAiB,EAAA;QAC1B,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrB,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACxB,SAAA;AACD,QAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;KAC5F;AAED;;;;AAIG;AACH,IAAA,MAAM,CAAC,KAAU,EAAA;QACb,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5D;AAED;;;;AAIG;IACH,IAAI,CAAC,KAAU,EAAE,OAAQ,EAAA;QACrB,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC;AACrC,SAAA;QAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;AAE/F,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACb,YAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAC7E,SAAA;AACD,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,WAAW,CAAC,KAAU,EAAE,IAAY,EAAA;AAChC,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;AAEnD,QAAA,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;QACnB,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;YAClI,SAAS,GAAG,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC;AACtM,SAAA;AAAM,aAAA;AACH,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;AACjG,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;YAClB,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACzD,YAAA,SAAS,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAChD,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;AAClB,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACjD,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACtF;IAED,wBAAwB,GAAA;AACpB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACnD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,aAAa,CAAC;KACvE;IAED,iBAAiB,GAAA;QACb,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC3G;AAED,IAAA,iBAAiB,CAAC,KAAa,EAAA;AAC3B,QAAA,MAAM,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE3J,QAAA,OAAO,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,GAAG,KAAK,CAAC;KAC3D;AAED,IAAA,iBAAiB,CAAC,KAAa,EAAA;QAC3B,MAAM,gBAAgB,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEtK,QAAA,OAAO,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KACvE;IAED,yBAAyB,GAAA;AACrB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAEnD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,aAAa,CAAC;KACxE;IAED,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC1F;IAED,qBAAqB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC;KAClG;IAED,sBAAsB,CAAC,KAAU,EAAE,KAAa,EAAA;QAC5C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,KAAK,EAAE;AACxC,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC/D,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;KACJ;IAED,YAAY,CAAC,KAAgB,GAAA,CAAC,CAAC,EAAA;QAC3B,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,aAAa,CAAC;AACrE,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAExF,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC,KAAK,KAAI;AACtF,oBAAA,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;AAC7B,wBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1B,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,wBAAwB,GAAA;AACpB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;AAC/E,oBAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACpH,oBAAA,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAEnI,IAAI,IAAI,CAAC,KAAK,EAAE;wBACZ,IAAI,kBAAkB,IAAI,eAAe,EAAE;4BACvC,IAAI,CAAC,UAAU,EAAE,CAAC;4BAClB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,yBAAA;AACJ,qBAAA;AAAM,yBAAA;AACH,wBAAA,IAAI,kBAAkB,IAAI,eAAe,IAAI,IAAI,CAAC,OAAO,EAAE;4BACvD,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,yBAAA;AACJ,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAEd,QAAA,IAAI,CAAE,IAAI,CAAC,EAAc,CAAC,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACzB,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,gBAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,aAAA;YAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,SAAA;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;KACpC;uGAl6BQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAoMN,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FArMd,SAAS,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAgBE,eAAe,CAKf,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,eAAe,wDAKf,eAAe,CAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAef,gBAAgB,CAKhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,6BAKf,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAoBhB,gBAAgB,CAKhB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,eAAe,+CAMf,gBAAgB,CAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAqCnB,aAAa,EAhMpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,2BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgET,EA+6BgG,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,w8CAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,aAAa,CAjnCrG,EAAA,QAAA,EAAA,eAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,YAAY,CAmMT,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,aAAA,EAAA,YAAA,EAAA,YAAA,EAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQpO,SAAS,EAAA,UAAA,EAAA,CAAA;kBA3ErB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACb,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgET,IAAA,CAAA,EAAA,UAAA,EACW,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAC5N,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,w8CAAA,CAAA,EAAA,CAAA;;0BAsMI,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;mLAhMV,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAWiC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,cAAc,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,cAAc,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAMG,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKI,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEP,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;gBAEG,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEK,QAAQ,EAAA,CAAA;sBAAlC,SAAS;uBAAC,UAAU,CAAA;gBAIU,yBAAyB,EAAA,CAAA;sBAAvD,SAAS;uBAAC,kBAAkB,CAAA;;MAyyBpB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAf,eAAe,EAAA,YAAA,EAAA,CA16Bf,SAAS,EA3MT,YAAY,CAAA,EAAA,OAAA,EAAA,CAinCX,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,CAAA,EAAA,OAAA,EAAA,CAt6BrG,SAAS,EAu6BG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;AAGrD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAJd,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,EACzF,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGrD,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,aAAa,CAAC;oBAC/G,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;AAC/D,oBAAA,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;AAC1C,iBAAA,CAAA;;;ACzzCD;;AAEG;;;;"}