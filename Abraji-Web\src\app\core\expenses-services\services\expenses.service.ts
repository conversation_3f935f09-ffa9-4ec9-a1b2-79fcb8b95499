// src/app/core/expenses-services/services/expenses.service.ts
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ExpensesApi } from '../api/expenses.api';
import { Expense, ExpensesForm } from '../api/expenses';
import { TableResponse } from '../../common-services/interfaces/table-response';

@Injectable({
  providedIn: 'root',
})
export class ExpensesService {
  private expensesApi = inject(ExpensesApi);

  getExpenses(requestForm: ExpensesForm): Observable<TableResponse<any>> {
    return this.expensesApi.getExpenses(requestForm);
  }

  createExpense(expense: any): Observable<any> {
    return this.expensesApi.createExpense(expense);
  }
}
