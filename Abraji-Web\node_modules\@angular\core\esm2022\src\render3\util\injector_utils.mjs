/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertGreaterThan, assertNotEqual, assertNumber } from '../../util/assert';
import { ChainedInjector } from '../chained_injector';
import { NO_PARENT_INJECTOR, } from '../interfaces/injector';
import { DECLARATION_VIEW, HEADER_OFFSET } from '../interfaces/view';
/// Parent Injector Utils ///////////////////////////////////////////////////////////////
export function hasParentInjector(parentLocation) {
    return parentLocation !== NO_PARENT_INJECTOR;
}
export function getParentInjectorIndex(parentLocation) {
    if (ngDevMode) {
        assertNumber(parentLocation, 'Number expected');
        assertNotEqual(parentLocation, -1, 'Not a valid state.');
        const parentInjectorIndex = parentLocation & 32767 /* RelativeInjectorLocationFlags.InjectorIndexMask */;
        assertGreaterThan(parentInjectorIndex, HEADER_OFFSET, 'Parent injector must be pointing past HEADER_OFFSET.');
    }
    return parentLocation & 32767 /* RelativeInjectorLocationFlags.InjectorIndexMask */;
}
export function getParentInjectorViewOffset(parentLocation) {
    return parentLocation >> 16 /* RelativeInjectorLocationFlags.ViewOffsetShift */;
}
/**
 * Unwraps a parent injector location number to find the view offset from the current injector,
 * then walks up the declaration view tree until the view is found that contains the parent
 * injector.
 *
 * @param location The location of the parent injector, which contains the view offset
 * @param startView The LView instance from which to start walking up the view tree
 * @returns The LView instance that contains the parent injector
 */
export function getParentInjectorView(location, startView) {
    let viewOffset = getParentInjectorViewOffset(location);
    let parentView = startView;
    // For most cases, the parent injector can be found on the host node (e.g. for component
    // or container), but we must keep the loop here to support the rarer case of deeply nested
    // <ng-template> tags or inline views, where the parent injector might live many views
    // above the child injector.
    while (viewOffset > 0) {
        parentView = parentView[DECLARATION_VIEW];
        viewOffset--;
    }
    return parentView;
}
/**
 * Detects whether an injector is an instance of a `ChainedInjector`,
 * created based on the `OutletInjector`.
 */
export function isRouterOutletInjector(currentInjector) {
    return (currentInjector instanceof ChainedInjector &&
        typeof currentInjector.injector.__ngOutletInjector === 'function');
}
//# sourceMappingURL=data:application/json;base64,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