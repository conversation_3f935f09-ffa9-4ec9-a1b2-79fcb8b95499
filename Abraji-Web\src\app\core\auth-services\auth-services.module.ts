import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthApi } from './api/auth.api';
import { AuthData } from './api/auth';
import { AuthService } from './services/auth.service';
import { LocalStorageService } from './services/local-storage.service';

const API = [AuthApi]

const SERVICES = [
  LocalStorageService,
  {provide: AuthData, useClass: AuthService}
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ]
})
export class AuthServicesModule {
  static forRoot(): ModuleWithProviders<AuthServicesModule> {
    return {
      ngModule: AuthServicesModule,
      providers: [
        ...API,
        ...SERVICES
      ]
    };
  }
}
