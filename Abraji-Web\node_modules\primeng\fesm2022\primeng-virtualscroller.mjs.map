{"version": 3, "file": "primeng-virtualscroller.mjs", "sources": ["../../src/app/components/virtualscroller/virtualscroller.ts", "../../src/app/components/virtualscroller/primeng-virtualscroller.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChild,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Input,\n    NgModule,\n    Output,\n    QueryList,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { BlockableUI, Footer, Header, PrimeTemplate, ScrollerOptions, SharedModule } from 'primeng/api';\nimport { Scroller, ScrollerModule } from 'primeng/scroller';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { VirtualScrollerLazyLoadEvent } from './virtualscroller.interface';\n/**\n * VirtualScroller is a performant approach to handle huge data efficiently.\n * @group Components\n */\n@Component({\n    selector: 'p-virtualScroller',\n    template: `\n        <div [ngClass]=\"'p-virtualscroller p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" [attr.data-pc-name]=\"'virtualscroller'\" [attr.data-pc-section]=\"'root'\">\n            <div class=\"p-virtualscroller-header\" *ngIf=\"header || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div #content class=\"p-virtualscroller-content\" [attr.data-pc-section]=\"'content'\">\n                <p-scroller #scroller [items]=\"value\" styleClass=\"p-virtualscroller-list\" [style]=\"{ height: scrollHeight }\" [itemSize]=\"itemSize\" [lazy]=\"lazy\" (onLazyLoad)=\"onLazyItemLoad($event)\" [options]=\"options\">\n                    <ng-template pTemplate=\"item\" let-item let-scrollerOptions=\"options\">\n                        <div [ngStyle]=\"{ height: itemSize + 'px' }\" class=\"p-virtualscroller-item\">\n                            <ng-container *ngTemplateOutlet=\"item ? itemTemplate : loadingItemTemplate; context: { $implicit: item, options: scrollerOptions }\"></ng-container>\n                        </div>\n                    </ng-template>\n                </p-scroller>\n            </div>\n            <div class=\"p-virtualscroller-footer\" *ngIf=\"footer || footerTemplate\" [attr.data-pc-section]=\"'footer'\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.Default,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class VirtualScroller implements AfterContentInit, BlockableUI {\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    @Input() value: any[] | undefined;\n    /**\n     * Height of an item in the list.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) itemSize: number | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Max height of the content area in inline mode.\n     * @group Props\n     */\n    @Input() scrollHeight: any;\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazy: boolean | undefined;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() options: ScrollerOptions | undefined;\n    /**\n     * Threshold in milliseconds to delay lazy loading during scrolling.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) delay: number = 250;\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {VirtualScrollerLazyLoadEvent} event - custom lazy load event.\n     * @group Emits\n     */\n    @Output() onLazyLoad: EventEmitter<VirtualScrollerLazyLoadEvent> = new EventEmitter<VirtualScrollerLazyLoadEvent>();\n\n    @ContentChild(Header) header: Header | undefined;\n\n    @ContentChild(Footer) footer: Footer | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    @ViewChild('scroller') scroller: Nullable<Scroller>;\n\n    itemTemplate: Nullable<TemplateRef<any>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    loadingItemTemplate: Nullable<TemplateRef<any>>;\n\n    virtualScrollTimeout: any;\n\n    constructor(public el: ElementRef, public cd: ChangeDetectorRef) {}\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'loadingItem':\n                    this.loadingItemTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    onLazyItemLoad(event: VirtualScrollerLazyLoadEvent) {\n        if (this.virtualScrollTimeout) {\n            clearTimeout(this.virtualScrollTimeout);\n        }\n\n        this.virtualScrollTimeout = setTimeout(() => {\n            this.onLazyLoad.emit({\n                ...event,\n                rows: <number>event.last - <number>event.first,\n                forceUpdate: () => this.cd.detectChanges()\n            });\n        }, this.delay);\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    scrollToIndex(index: number, mode?: ScrollBehavior): void {\n        this.scroller?.scrollToIndex(index, mode);\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, ScrollerModule],\n    exports: [VirtualScroller, SharedModule, ScrollerModule],\n    declarations: [VirtualScroller]\n})\nexport class VirtualScrollerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;AAwBA;;;AAGG;MA8BU,eAAe,CAAA;AAkEL,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAjE1C;;;AAGG;AACM,IAAA,KAAK,CAAoB;AAClC;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,YAAY,CAAM;AAC3B;;;AAGG;AACqC,IAAA,IAAI,CAAsB;AAClE;;;AAGG;AACM,IAAA,OAAO,CAA8B;AAC9C;;;AAGG;IACoC,KAAK,GAAW,GAAG,CAAC;AAC3D;;;;AAIG;AACO,IAAA,UAAU,GAA+C,IAAI,YAAY,EAAgC,CAAC;AAE9F,IAAA,MAAM,CAAqB;AAE3B,IAAA,MAAM,CAAqB;AAEjB,IAAA,SAAS,CAAqC;AAEvD,IAAA,QAAQ,CAAqB;AAEpD,IAAA,YAAY,CAA6B;AAEzC,IAAA,cAAc,CAA6B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,oBAAoB,CAAM;IAE1B,WAAmB,CAAA,EAAc,EAAS,EAAqB,EAAA;QAA5C,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;IAEnE,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,cAAc,CAAC,KAAmC,EAAA;QAC9C,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAC3C,SAAA;AAED,QAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,MAAK;AACxC,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACjB,gBAAA,GAAG,KAAK;AACR,gBAAA,IAAI,EAAU,KAAK,CAAC,IAAI,GAAW,KAAK,CAAC,KAAK;gBAC9C,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AAC7C,aAAA,CAAC,CAAC;AACP,SAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;KAClB;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,aAAa,CAAC,KAAa,EAAE,IAAqB,EAAA;QAC9C,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;KAC7C;uGAlHQ,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EAUJ,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,CAoBf,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,CAUhB,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,CAQrB,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,MAAM,EAEN,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,MAAM,EAEH,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA/EpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;AAoBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,aAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAOQ,eAAe,EAAA,UAAA,EAAA,CAAA;kBA7B3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;AAoBT,IAAA,CAAA;oBACD,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;+GAMY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAM3B,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAEe,MAAM,EAAA,CAAA;sBAA3B,YAAY;uBAAC,MAAM,CAAA;gBAEE,MAAM,EAAA,CAAA;sBAA3B,YAAY;uBAAC,MAAM,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEP,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;;MAoEZ,qBAAqB,CAAA;uGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,EA1HrB,YAAA,EAAA,CAAA,eAAe,CAsHd,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,cAAc,CAtH3C,EAAA,OAAA,EAAA,CAAA,eAAe,EAuHG,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;wGAG9C,qBAAqB,EAAA,OAAA,EAAA,CAJpB,YAAY,EAAE,YAAY,EAAE,cAAc,EACzB,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;;2FAG9C,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBALjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC;AACrD,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,cAAc,CAAC;oBACxD,YAAY,EAAE,CAAC,eAAe,CAAC;AAClC,iBAAA,CAAA;;;AClLD;;AAEG;;;;"}