/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createInjectorWithoutInjectorInstances } from '../di/create_injector';
import { getNullInjector, R3Injector } from '../di/r3_injector';
import { ComponentFactoryResolver as viewEngine_ComponentFactoryResolver } from '../linker/component_factory_resolver';
import { NgModuleFactory as viewEngine_NgModuleFactory, NgModuleRef as viewEngine_NgModuleRef, } from '../linker/ng_module_factory';
import { assertDefined } from '../util/assert';
import { stringify } from '../util/stringify';
import { ComponentFactoryResolver } from './component_ref';
import { getNgModuleDef } from './definition';
import { maybeUnwrapFn } from './util/misc_utils';
/**
 * Returns a new NgModuleRef instance based on the NgModule class and parent injector provided.
 *
 * @param ngModule NgModule class.
 * @param parentInjector Optional injector instance to use as a parent for the module injector. If
 *     not provided, `NullInjector` will be used instead.
 * @returns NgModuleRef that represents an NgModule instance.
 *
 * @publicApi
 */
export function createNgModule(ngModule, parentInjector) {
    return new NgModuleRef(ngModule, parentInjector ?? null, []);
}
/**
 * The `createNgModule` function alias for backwards-compatibility.
 * Please avoid using it directly and use `createNgModule` instead.
 *
 * @deprecated Use `createNgModule` instead.
 */
export const createNgModuleRef = createNgModule;
export class NgModuleRef extends viewEngine_NgModuleRef {
    constructor(ngModuleType, _parent, additionalProviders) {
        super();
        this._parent = _parent;
        // tslint:disable-next-line:require-internal-with-underscore
        this._bootstrapComponents = [];
        this.destroyCbs = [];
        // When bootstrapping a module we have a dependency graph that looks like this:
        // ApplicationRef -> ComponentFactoryResolver -> NgModuleRef. The problem is that if the
        // module being resolved tries to inject the ComponentFactoryResolver, it'll create a
        // circular dependency which will result in a runtime error, because the injector doesn't
        // exist yet. We work around the issue by creating the ComponentFactoryResolver ourselves
        // and providing it, rather than letting the injector resolve it.
        this.componentFactoryResolver = new ComponentFactoryResolver(this);
        const ngModuleDef = getNgModuleDef(ngModuleType);
        ngDevMode &&
            assertDefined(ngModuleDef, `NgModule '${stringify(ngModuleType)}' is not a subtype of 'NgModuleType'.`);
        this._bootstrapComponents = maybeUnwrapFn(ngModuleDef.bootstrap);
        this._r3Injector = createInjectorWithoutInjectorInstances(ngModuleType, _parent, [
            { provide: viewEngine_NgModuleRef, useValue: this },
            {
                provide: viewEngine_ComponentFactoryResolver,
                useValue: this.componentFactoryResolver,
            },
            ...additionalProviders,
        ], stringify(ngModuleType), new Set(['environment']));
        // We need to resolve the injector types separately from the injector creation, because
        // the module might be trying to use this ref in its constructor for DI which will cause a
        // circular error that will eventually error out, because the injector isn't created yet.
        this._r3Injector.resolveInjectorInitializers();
        this.instance = this._r3Injector.get(ngModuleType);
    }
    get injector() {
        return this._r3Injector;
    }
    destroy() {
        ngDevMode && assertDefined(this.destroyCbs, 'NgModule already destroyed');
        const injector = this._r3Injector;
        !injector.destroyed && injector.destroy();
        this.destroyCbs.forEach((fn) => fn());
        this.destroyCbs = null;
    }
    onDestroy(callback) {
        ngDevMode && assertDefined(this.destroyCbs, 'NgModule already destroyed');
        this.destroyCbs.push(callback);
    }
}
export class NgModuleFactory extends viewEngine_NgModuleFactory {
    constructor(moduleType) {
        super();
        this.moduleType = moduleType;
    }
    create(parentInjector) {
        return new NgModuleRef(this.moduleType, parentInjector, []);
    }
}
export function createNgModuleRefWithProviders(moduleType, parentInjector, additionalProviders) {
    return new NgModuleRef(moduleType, parentInjector, additionalProviders);
}
export class EnvironmentNgModuleRefAdapter extends viewEngine_NgModuleRef {
    constructor(config) {
        super();
        this.componentFactoryResolver = new ComponentFactoryResolver(this);
        this.instance = null;
        const injector = new R3Injector([
            ...config.providers,
            { provide: viewEngine_NgModuleRef, useValue: this },
            { provide: viewEngine_ComponentFactoryResolver, useValue: this.componentFactoryResolver },
        ], config.parent || getNullInjector(), config.debugName, new Set(['environment']));
        this.injector = injector;
        if (config.runEnvironmentInitializers) {
            injector.resolveInjectorInitializers();
        }
    }
    destroy() {
        this.injector.destroy();
    }
    onDestroy(callback) {
        this.injector.onDestroy(callback);
    }
}
/**
 * Create a new environment injector.
 *
 * @param providers An array of providers.
 * @param parent A parent environment injector.
 * @param debugName An optional name for this injector instance, which will be used in error
 *     messages.
 *
 * @publicApi
 */
export function createEnvironmentInjector(providers, parent, debugName = null) {
    const adapter = new EnvironmentNgModuleRefAdapter({
        providers,
        parent,
        debugName,
        runEnvironmentInitializers: true,
    });
    return adapter.injector;
}
//# sourceMappingURL=data:application/json;base64,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