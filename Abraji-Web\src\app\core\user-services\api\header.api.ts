import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpService } from '../../common-services/services/http.service';
import { ManagerModel } from './header';

@Injectable({
  providedIn: 'root',
})
export class HeaderApi {
  private httpService = inject(HttpService);
  private apiUrl = `get-manager`;

  constructor() {}

  getManager(): Observable<ManagerModel> {
    // Make an HTTP request to get the manager data
    //return this.httpService.get(this.apiUrl);
    return this.httpService.get(this.apiUrl).pipe(
      map((response: any) => response.data[0] as ManagerModel)
    );
  }

  getAllManagers(): Observable<any> {
    return this.httpService.get(this.apiUrl);
  }
}
