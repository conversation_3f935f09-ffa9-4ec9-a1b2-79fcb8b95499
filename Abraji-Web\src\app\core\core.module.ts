import { ModuleWithProviders, NgModule, Optional, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthServicesModule } from './auth-services/auth-services.module';
import { CommonServicesModule } from './common-services/common-services.module';
import { throwIfAlreadyLoaded } from './module-import-guard';
import { DashboardServicesModule } from './dashboard-services/dashboard-services.module';
import { UserServicesModule } from './user-services/user-services.module';

export const CORE_PROVIDERS = [
  ...AuthServicesModule.forRoot().providers!,
  ...CommonServicesModule.forRoot().providers!,
  ...DashboardServicesModule.forRoot().providers!,
  ...UserServicesModule.forRoot().providers!,
]

// const SERVICES = [
//   AuthService,
// ]

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ]
})
export class CoreModule {
  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
    throwIfAlreadyLoaded(parentModule, 'CoreModule');
  }

static forRoot(): ModuleWithProviders<CoreModule>{
  return {
    ngModule: CoreModule,
    providers: [
      ...CORE_PROVIDERS,
      //...SERVICES,
    ]
  }
}
}

