{"version": 3, "file": "primeng-avatar.mjs", "sources": ["../../src/app/components/avatar/avatar.ts", "../../src/app/components/avatar/primeng-avatar.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, EventEmitter, Input, NgModule, Output, ViewEncapsulation } from '@angular/core';\n/**\n * Avatar represents people using icons, labels and images.\n * @group Components\n */\n@Component({\n    selector: 'p-avatar',\n    template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-label]=\"ariaLabel\" [attr.data-pc-name]=\"'avatar'\">\n            <ng-content></ng-content>\n            <span class=\"p-avatar-text\" *ngIf=\"label; else iconTemplate\">{{ label }}</span>\n            <ng-template #iconTemplate><span [class]=\"icon\" [ngClass]=\"'p-avatar-icon'\" *ngIf=\"icon; else imageTemplate\"></span></ng-template>\n            <ng-template #imageTemplate><img [src]=\"image\" *ngIf=\"image\" (error)=\"imageError($event)\" [attr.aria-label]=\"ariaLabel\" /></ng-template>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./avatar.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Avatar {\n    /**\n     * Defines the text to display.\n     * @group Props\n     */\n    @Input() label: string | undefined;\n    /**\n     * Defines the icon to display.\n     * @group Props\n     */\n    @Input() icon: string | undefined;\n    /**\n     * Defines the image to display.\n     * @group Props\n     */\n    @Input() image: string | undefined;\n    /**\n     * Size of the element.\n     * @group Props\n     */\n    @Input() size: 'normal' | 'large' | 'xlarge' | undefined = 'normal';\n    /**\n     * Shape of the element.\n     * @group Props\n     */\n    @Input() shape: 'square' | 'circle' | undefined = 'square';\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Establishes a string value that labels the component.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * This event is triggered if an error occurs while loading an image file.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onImageError: EventEmitter<Event> = new EventEmitter<Event>();\n\n    containerClass() {\n        return {\n            'p-avatar p-component': true,\n            'p-avatar-image': this.image != null,\n            'p-avatar-circle': this.shape === 'circle',\n            'p-avatar-lg': this.size === 'large',\n            'p-avatar-xl': this.size === 'xlarge'\n        };\n    }\n\n    imageError(event: Event) {\n        this.onImageError.emit(event);\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Avatar],\n    declarations: [Avatar]\n})\nexport class AvatarModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAEA;;;AAGG;MAkBU,MAAM,CAAA;AACf;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;IACM,IAAI,GAA8C,QAAQ,CAAC;AACpE;;;AAGG;IACM,KAAK,GAAoC,QAAQ,CAAC;AAC3D;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;;AAIG;AACO,IAAA,YAAY,GAAwB,IAAI,YAAY,EAAS,CAAC;IAExE,cAAc,GAAA;QACV,OAAO;AACH,YAAA,sBAAsB,EAAE,IAAI;AAC5B,YAAA,gBAAgB,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI;AACpC,YAAA,iBAAiB,EAAE,IAAI,CAAC,KAAK,KAAK,QAAQ;AAC1C,YAAA,aAAa,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO;AACpC,YAAA,aAAa,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ;SACxC,CAAC;KACL;AAED,IAAA,UAAU,CAAC,KAAY,EAAA;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACjC;uGAjEQ,MAAM,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAN,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAM,EAfL,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;AAOT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,iUAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,MAAM,EAAA,UAAA,EAAA,CAAA;kBAjBlB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EACV,QAAA,EAAA,CAAA;;;;;;;AAOT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,iUAAA,CAAA,EAAA,CAAA;8BAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMI,YAAY,EAAA,CAAA;sBAArB,MAAM;;MAsBE,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,EAzEZ,YAAA,EAAA,CAAA,MAAM,CAqEL,EAAA,OAAA,EAAA,CAAA,YAAY,aArEb,MAAM,CAAA,EAAA,CAAA,CAAA;AAyEN,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,YAJX,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,YAAY,EAAA,UAAA,EAAA,CAAA;kBALxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,MAAM,CAAC;oBACjB,YAAY,EAAE,CAAC,MAAM,CAAC;AACzB,iBAAA,CAAA;;;AC/FD;;AAEG;;;;"}