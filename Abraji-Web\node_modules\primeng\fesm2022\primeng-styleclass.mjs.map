{"version": 3, "file": "primeng-styleclass.mjs", "sources": ["../../src/app/components/styleclass/styleclass.ts", "../../src/app/components/styleclass/primeng-styleclass.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { Directive, ElementRef, HostListener, Input, NgModule, NgZone, OnDestroy, Renderer2, booleanAttribute } from '@angular/core';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { VoidListener } from 'primeng/ts-helpers';\n/**\n * StyleClass manages css classes declaratively to during enter/leave animations or just to toggle classes on an element.\n * @group Components\n */\n@Directive({\n    selector: '[pStyleClass]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class StyleClass implements OnDestroy {\n    constructor(public el: ElementRef, public renderer: Renderer2, private zone: NgZone) {}\n    /**\n     * Selector to define the target element. Available selectors are '@next', '@prev', '@parent' and '@grandparent'.\n     * @group Props\n     */\n    @Input('pStyleClass') selector: string | undefined;\n    /**\n     * Style class to add when item begins to get displayed.\n     * @group Props\n     * @deprecated Use enterFromClass instead\n     */\n    @Input() set enterClass(value: string) {\n        this._enterClass = value;\n        console.warn('enterClass is deprecated, use enterFromClass instead');\n    }\n    get enterClass() {\n        return this._enterClass;\n    }\n    /**\n     * Style class to add when item begins to get displayed.\n     * @group Props\n     */\n    @Input() enterFromClass: string | undefined;\n    /**\n     * Style class to add during enter animation.\n     * @group Props\n     */\n    @Input() enterActiveClass: string | undefined;\n    /**\n     * Style class to add when item begins to get displayed.\n     * @group Props\n     */\n    @Input() enterToClass: string | undefined;\n    /**\n     * Style class to add when item begins to get hidden.\n     * @group Props\n     * @deprecated Use leaveFromClass instead\n     */\n    @Input() set leaveClass(value: string) {\n        this._leaveClass = value;\n        console.warn('leaveClass is deprecated, use leaveFromClass instead');\n    }\n    get leaveClass() {\n        return this._leaveClass;\n    }\n    /**\n     * Style class to add when item begins to get hidden.\n     * @group Props\n     */\n    @Input() leaveFromClass: string | undefined;\n    /**\n     * Style class to add during leave animation.\n     * @group Props\n     */\n    @Input() leaveActiveClass: string | undefined;\n    /**\n     * Style class to add when leave animation is completed.\n     * @group Props\n     */\n    @Input() leaveToClass: string | undefined;\n    /**\n     * Whether to trigger leave animation when outside of the element is clicked.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) hideOnOutsideClick: boolean | undefined;\n    /**\n     * Adds or removes a class when no enter-leave animation is required.\n     * @group Props\n     */\n    @Input() toggleClass: string | undefined;\n    /**\n     * Whether to trigger leave animation when escape key pressed.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) hideOnEscape: boolean | undefined;\n\n    eventListener: VoidListener;\n\n    documentClickListener: VoidListener;\n\n    documentKeydownListener: VoidListener;\n\n    target: HTMLElement | null | undefined;\n\n    enterListener: VoidListener;\n\n    leaveListener: VoidListener;\n\n    animating: boolean | undefined;\n\n    _enterClass: string | undefined;\n\n    _leaveClass: string | undefined;\n\n    @HostListener('click', ['$event'])\n    clickListener() {\n        this.target = this.resolveTarget();\n\n        if (this.toggleClass) {\n            this.toggle();\n        } else {\n            if ((this.target as HTMLElement).offsetParent === null) this.enter();\n            else this.leave();\n        }\n    }\n\n    toggle() {\n        if (DomHandler.hasClass(this.target, this.toggleClass as string)) DomHandler.removeClass(this.target, this.toggleClass as string);\n        else DomHandler.addClass(this.target, this.toggleClass as string);\n    }\n\n    enter() {\n        if (this.enterActiveClass) {\n            if (!this.animating) {\n                this.animating = true;\n\n                if (this.enterActiveClass === 'slidedown') {\n                    (this.target as HTMLElement).style.height = '0px';\n                    DomHandler.removeClass(this.target, 'hidden');\n                    (this.target as HTMLElement).style.maxHeight = (this.target as HTMLElement).scrollHeight + 'px';\n                    DomHandler.addClass(this.target, 'hidden');\n                    (this.target as HTMLElement).style.height = '';\n                }\n\n                DomHandler.addClass(this.target, this.enterActiveClass);\n                if (this.enterClass || this.enterFromClass) {\n                    DomHandler.removeClass(this.target, this.enterClass || this.enterFromClass);\n                }\n\n                this.enterListener = this.renderer.listen(this.target, 'animationend', () => {\n                    DomHandler.removeClass(this.target, this.enterActiveClass as string);\n                    if (this.enterToClass) {\n                        DomHandler.addClass(this.target, this.enterToClass);\n                    }\n                    this.enterListener && this.enterListener();\n\n                    if (this.enterActiveClass === 'slidedown') {\n                        (this.target as HTMLElement).style.maxHeight = '';\n                    }\n                    this.animating = false;\n                });\n            }\n        } else {\n            if (this.enterClass || this.enterFromClass) {\n                DomHandler.removeClass(this.target, this.enterClass || this.enterFromClass);\n            }\n\n            if (this.enterToClass) {\n                DomHandler.addClass(this.target, this.enterToClass);\n            }\n        }\n\n        if (this.hideOnOutsideClick) {\n            this.bindDocumentClickListener();\n        }\n\n        if (this.hideOnEscape) {\n            this.bindDocumentKeydownListener();\n        }\n    }\n\n    leave() {\n        if (this.leaveActiveClass) {\n            if (!this.animating) {\n                this.animating = true;\n                DomHandler.addClass(this.target, this.leaveActiveClass);\n                if (this.leaveClass || this.leaveFromClass) {\n                    DomHandler.removeClass(this.target, this.leaveClass || this.leaveFromClass);\n                }\n\n                this.leaveListener = this.renderer.listen(this.target, 'animationend', () => {\n                    DomHandler.removeClass(this.target, this.leaveActiveClass as string);\n                    if (this.leaveToClass) {\n                        DomHandler.addClass(this.target, this.leaveToClass);\n                    }\n                    this.leaveListener && this.leaveListener();\n                    this.animating = false;\n                });\n            }\n        } else {\n            if (this.leaveClass || this.leaveFromClass) {\n                DomHandler.removeClass(this.target, this.leaveClass || this.leaveFromClass);\n            }\n\n            if (this.leaveToClass) {\n                DomHandler.addClass(this.target, this.leaveToClass);\n            }\n        }\n\n        if (this.hideOnOutsideClick) {\n            this.unbindDocumentClickListener();\n        }\n\n        if (this.hideOnEscape) {\n            this.unbindDocumentKeydownListener();\n        }\n    }\n\n    resolveTarget() {\n        if (this.target) {\n            return this.target;\n        }\n\n        switch (this.selector) {\n            case '@next':\n                return this.el.nativeElement.nextElementSibling;\n\n            case '@prev':\n                return this.el.nativeElement.previousElementSibling;\n\n            case '@parent':\n                return this.el.nativeElement.parentElement;\n\n            case '@grandparent':\n                return this.el.nativeElement.parentElement.parentElement;\n\n            default:\n                return document.querySelector(this.selector as string);\n        }\n    }\n\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            this.documentClickListener = this.renderer.listen(this.el.nativeElement.ownerDocument, 'click', (event) => {\n                if (!this.isVisible() || getComputedStyle(this.target as HTMLElement).getPropertyValue('position') === 'static') this.unbindDocumentClickListener();\n                else if (this.isOutsideClick(event)) this.leave();\n            });\n        }\n    }\n\n    bindDocumentKeydownListener() {\n        if (!this.documentKeydownListener) {\n            this.zone.runOutsideAngular(() => {\n                this.documentKeydownListener = this.renderer.listen(this.el.nativeElement.ownerDocument, 'keydown', (event) => {\n                    const { key, keyCode, which, type } = event;\n                    if (!this.isVisible() || getComputedStyle(this.target as HTMLElement).getPropertyValue('position') === 'static') this.unbindDocumentKeydownListener();\n                    if (this.isVisible() && key === 'Escape' && keyCode === 27 && which === 27) this.leave();\n                });\n            });\n        }\n    }\n\n    isVisible() {\n        return (this.target as HTMLElement).offsetParent !== null;\n    }\n\n    isOutsideClick(event: MouseEvent) {\n        return !this.el.nativeElement.isSameNode(event.target) && !this.el.nativeElement.contains(event.target) && !(this.target as HTMLElement).contains(<HTMLElement>event.target);\n    }\n\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n\n    unbindDocumentKeydownListener() {\n        if (this.documentKeydownListener) {\n            this.documentKeydownListener();\n            this.documentKeydownListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.target = null;\n        if (this.eventListener) {\n            this.eventListener();\n        }\n        this.unbindDocumentClickListener();\n        this.unbindDocumentKeydownListener();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [StyleClass],\n    declarations: [StyleClass]\n})\nexport class StyleClassModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAIA;;;AAGG;MAOU,UAAU,CAAA;AACA,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA6B,IAAA,IAAA,CAAA;AAAvE,IAAA,WAAA,CAAmB,EAAc,EAAS,QAAmB,EAAU,IAAY,EAAA;QAAhE,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAU,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAI;AACvF;;;AAGG;AACmB,IAAA,QAAQ,CAAqB;AACnD;;;;AAIG;IACH,IAAa,UAAU,CAAC,KAAa,EAAA;AACjC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;KACxE;AACD,IAAA,IAAI,UAAU,GAAA;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;AACD;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;;AAIG;IACH,IAAa,UAAU,CAAC,KAAa,EAAA;AACjC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;KACxE;AACD,IAAA,IAAI,UAAU,GAAA;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;AACD;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACqC,IAAA,kBAAkB,CAAsB;AAChF;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAE1E,IAAA,aAAa,CAAe;AAE5B,IAAA,qBAAqB,CAAe;AAEpC,IAAA,uBAAuB,CAAe;AAEtC,IAAA,MAAM,CAAiC;AAEvC,IAAA,aAAa,CAAe;AAE5B,IAAA,aAAa,CAAe;AAE5B,IAAA,SAAS,CAAsB;AAE/B,IAAA,WAAW,CAAqB;AAEhC,IAAA,WAAW,CAAqB;IAGhC,aAAa,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAEnC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB,SAAA;AAAM,aAAA;AACH,YAAA,IAAK,IAAI,CAAC,MAAsB,CAAC,YAAY,KAAK,IAAI;gBAAE,IAAI,CAAC,KAAK,EAAE,CAAC;;gBAChE,IAAI,CAAC,KAAK,EAAE,CAAC;AACrB,SAAA;KACJ;IAED,MAAM,GAAA;QACF,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAqB,CAAC;YAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAqB,CAAC,CAAC;;YAC7H,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAqB,CAAC,CAAC;KACrE;IAED,KAAK,GAAA;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACvB,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACjB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAEtB,gBAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,WAAW,EAAE;oBACtC,IAAI,CAAC,MAAsB,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;oBAClD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC7C,oBAAA,IAAI,CAAC,MAAsB,CAAC,KAAK,CAAC,SAAS,GAAI,IAAI,CAAC,MAAsB,CAAC,YAAY,GAAG,IAAI,CAAC;oBAChG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;oBAC1C,IAAI,CAAC,MAAsB,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;AAClD,iBAAA;gBAED,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACxD,gBAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;AACxC,oBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC;AAC/E,iBAAA;AAED,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE,MAAK;oBACxE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,gBAA0B,CAAC,CAAC;oBACrE,IAAI,IAAI,CAAC,YAAY,EAAE;wBACnB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACvD,qBAAA;AACD,oBAAA,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AAE3C,oBAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,WAAW,EAAE;wBACtC,IAAI,CAAC,MAAsB,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;AACrD,qBAAA;AACD,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;AACxC,gBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC;AAC/E,aAAA;YAED,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACvD,aAAA;AACJ,SAAA;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACpC,SAAA;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,SAAA;KACJ;IAED,KAAK,GAAA;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACvB,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACjB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACxD,gBAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;AACxC,oBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC;AAC/E,iBAAA;AAED,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE,MAAK;oBACxE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,gBAA0B,CAAC,CAAC;oBACrE,IAAI,IAAI,CAAC,YAAY,EAAE;wBACnB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACvD,qBAAA;AACD,oBAAA,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3C,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;AACxC,gBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC;AAC/E,aAAA;YAED,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACvD,aAAA;AACJ,SAAA;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,SAAA;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACxC,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,IAAI,CAAC,MAAM,CAAC;AACtB,SAAA;QAED,QAAQ,IAAI,CAAC,QAAQ;AACjB,YAAA,KAAK,OAAO;AACR,gBAAA,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,kBAAkB,CAAC;AAEpD,YAAA,KAAK,OAAO;AACR,gBAAA,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,sBAAsB,CAAC;AAExD,YAAA,KAAK,SAAS;AACV,gBAAA,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC;AAE/C,YAAA,KAAK,cAAc;gBACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC;AAE7D,YAAA;gBACI,OAAO,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,QAAkB,CAAC,CAAC;AAC9D,SAAA;KACJ;IAED,yBAAyB,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;AACtG,gBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAqB,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,QAAQ;oBAAE,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAC/I,qBAAA,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;oBAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACtD,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,2BAA2B,GAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AAC/B,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC,KAAK,KAAI;oBAC1G,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AAC5C,oBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAqB,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,QAAQ;wBAAE,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACtJ,oBAAA,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,KAAK,QAAQ,IAAI,OAAO,KAAK,EAAE,IAAI,KAAK,KAAK,EAAE;wBAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAC7F,iBAAC,CAAC,CAAC;AACP,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,OAAQ,IAAI,CAAC,MAAsB,CAAC,YAAY,KAAK,IAAI,CAAC;KAC7D;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;AAC5B,QAAA,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAE,IAAI,CAAC,MAAsB,CAAC,QAAQ,CAAc,KAAK,CAAC,MAAM,CAAC,CAAC;KAChL;IAED,2BAA2B,GAAA;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;KACJ;IAED,6BAA6B,GAAA;QACzB,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACvC,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,SAAA;QACD,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,6BAA6B,EAAE,CAAC;KACxC;uGAhRQ,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAV,UAAU,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,EAAA,UAAA,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAiEC,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAUhB,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,uBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FA3E3B,UAAU,EAAA,UAAA,EAAA,CAAA;kBANtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;4HAOyB,QAAQ,EAAA,CAAA;sBAA7B,KAAK;uBAAC,aAAa,CAAA;gBAMP,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAWG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAMO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAWG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,kBAAkB,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAqBtC,aAAa,EAAA,CAAA;sBADZ,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAyLxB,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EAxRhB,YAAA,EAAA,CAAA,UAAU,CAoRT,EAAA,OAAA,EAAA,CAAA,YAAY,aApRb,UAAU,CAAA,EAAA,CAAA,CAAA;AAwRV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAJf,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,UAAU,CAAC;oBACrB,YAAY,EAAE,CAAC,UAAU,CAAC;AAC7B,iBAAA,CAAA;;;ACrSD;;AAEG;;;;"}