import { Observable } from "rxjs";
import { ColumnState, RequestForm, TableResponse } from "../../common-services/interfaces/table-response";

export abstract class SeriesData {
  abstract getSeries(requestForm: SeriesForm): Observable<TableResponse<SeriesDetails>>;
  abstract getCardById(id: string): Observable<any>;
  abstract getListCardForSeries(id: string, requestForm: any): Observable<TableResponse<CardDetails>>;
  abstract mapCardDetailsToExportFormat(data: CardDetails[]): any[];
}

export interface SeriesDetails {
  series: string;
  type: number;
  owner: number;
  value: string;
  expiration: string;
  qty: number;
  used: string;
  profile_id: number;
  series_date: string;
  suspended: number;
  owner_details: {  id: number; username: string;};
  profile_details: {id: number; name: string;};
  created_at: string;
  created_by: string;
}

export interface CardDetails {
  id: number;
  serialnumber: string;
  pin: string;
  username: string;
  password: string;
  used_at: string;
  manager_id: number;
  user_id: number;
  manager_details: { id: number; username: string;};
  user_details: { id: number; username: string; parent_username: string;};
}

export const initialCardColumnsState: ColumnState[] = [
  { key: 'series_date', hidden: false },
  { key: 'series', hidden: false },
  { key: 'type', hidden: false },
  { key: 'owner', hidden: false },
  { key: 'value', hidden: false },
  { key: 'qty', hidden: false },
  { key: 'used', hidden: false },
  { key: 'expiration', hidden: false },
  { key: 'profile', hidden: false },
];

export const initialCardDetailsColumnsState: ColumnState[] = [
  {key: 'id', hidden: false},
  {key: 'serialnumber', hidden: false},
  {key: 'pin', hidden: false},
  {key: 'username', hidden: false},
  {key: 'password', hidden: false},
  {key: 'used_at', hidden: false},
  {key: 'used_by_user', hidden: false},
  {key: 'used_by_manager', hidden: false},
];

export interface SeriesForm extends RequestForm {
  type?: number;
  profile_id?: number;
}

export interface SeriesDetailsForm extends RequestForm {
}
