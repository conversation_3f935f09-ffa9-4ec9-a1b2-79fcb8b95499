import { Component, inject } from '@angular/core';
import { AllDebtsComponent } from '../all-debts/all-debts.component';
import { ActivatedRoute } from '@angular/router';
import { DebtStatistics, initialDeptColumnsState } from '../../../core/debts-services/api/debts';
import { FormGroup, Validators } from '@angular/forms';
import { User } from '../../../core/user-services/api/users';
import { UsersService } from '../../../core/user-services/services/users.service';

@Component({
  selector: 'app-user-debts',
  templateUrl: './user-debts.component.html',
  styleUrl: './user-debts.component.scss'
})
export class UserDebtsComponent extends AllDebtsComponent {
  private userId!: string | null;
  private username: string | null = null;
  private route = inject(ActivatedRoute);
  debtStatistics!: DebtStatistics;
  loadingStatistics = false;
  createDebtForm!: FormGroup;
  private usersService = inject(UsersService);

  override ngOnInit(): void {
    // Access parent route to get the id parameter
    this.route.parent?.paramMap.subscribe((params) => {
      this.userId = params.get('id');
      console.log('User ID:', this.userId); // Check if the ID is correctly retrieved

    if (this.userId) {
      // initiate columns state
      this.debtColumnsState = this.localStorageService
        .loadColumnsState(this.localStorageService.DeptColumnsState)
        || initialDeptColumnsState;

      this.requestForm = {
        page: 1,
        count: 10,
        sortBy: "",
        direction: "",
        search: "",
        columns: [...this.getVisibleColumns(), 'id', 'pay', 'amount', 'amount_paid'],
        pay: null,
      };
      console.log(this.requestForm);

      // fetch users
      this.loadDebts();
      this.loadDebtStatistics();
      // initiate forms
      super.initEditForm();
      super.initPayForm();
      this.initCreateDebtForm();
    }
    });
  }

  override loadDebts(): void {
    console.log("Loading Debts", this.requestForm);
    this.isLoading = true;
    this.debtsService.getDebtsByUser(Number(this.userId), this.requestForm).subscribe({
      next: (response: any) => {
        console.log(response);
        this.tableResponse = response;
        this.isLoading = false;
        // set the username
        if (this.tableResponse.data.length > 0 && !this.username) {
          this.username = this.tableResponse.data[0].username;
        }
    },
    error: (error: any) => {
      this.isLoading = false;
      this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the data');
      console.error('There was an error!', error);
    },
    });
  }

  loadDebtStatistics(): void {
    this.loadingStatistics = true;
    this.debtsService.getDebtStatisticsByUser(Number(this.userId)).subscribe({
      next: (response: DebtStatistics) => {
        this.debtStatistics = response;
        this.loadingStatistics = false;
      },
      error: (error: any) => {
        this.loadingStatistics = false;
        this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the data');
        console.error('There was an error!', error);
      },
    });
  }

  initCreateDebtForm(): void {
    this.createDebtForm = this.fb.group({
      user_id: [this.userId],
      username: [''],
      debt_timestamp: ['', Validators.required],
      amount: ['', Validators.required, Validators.min(0)],
      description: [''],
      pay: [false],
    });
  }

  createDebt(): void {
    if (this.createDebtForm.invalid) {
      this.toastService.addToast('error', 'Error Message', 'Please fill all required fields');
      return;
    }
    // prepare the data
    if (this.username) {
      this.createDebtForm.patchValue({
        username: this.username,
      });
    }
    else {
      // fetch the user and set the username
      this.usersService.getUser(String(this.userId)).subscribe({
        next: (response: any) => {
          console.log(response);
          this.username = response.data.username;
          this.createDebtForm.patchValue({
            username: this.username,
          });
        },
        error: (error: any) => {
          this.toastService.addToast('error', 'Error Message', 'There was an error on fetching the user data');
          console.error('There was an error!', error);
        },
      });
    }
    this.debtsService.createDebt(this.createDebtForm.value).subscribe({
      next: (response: any) => {
        console.log(response);
        this.toastService.addToast('success', 'Success Message', 'Debt created successfully');
        this.loadDebts();
        this.loadDebtStatistics();
        this.createDebtForm.reset();
      },
      error: (error: any) => {
        this.toastService.addToast('error', 'Error Message', 'There was an error on creating the debt');
        console.error('There was an error!', error);
      }
    });
  }

  // getters
  get createFormAmount(): any {
    return this.createDebtForm.get('amount');
  }

  get createFormDebtTimestamp(): any {
    return this.createDebtForm.get('debt_timestamp');
  }

}
