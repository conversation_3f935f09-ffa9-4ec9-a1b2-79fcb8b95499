/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertDomNode, assertNumber, assertNumberInRange } from '../../util/assert';
import { EMPTY_ARRAY } from '../../util/empty';
import { assertTIcu, assertTNodeForLView } from '../assert';
import { getCurrentICUCaseIndex } from '../i18n/i18n_util';
import { TVIEW } from '../interfaces/view';
function enterIcu(state, tIcu, lView) {
    state.index = 0;
    const currentCase = getCurrentICUCaseIndex(tIcu, lView);
    if (currentCase !== null) {
        ngDevMode && assertNumberInRange(currentCase, 0, tIcu.cases.length - 1);
        state.removes = tIcu.remove[currentCase];
    }
    else {
        state.removes = EMPTY_ARRAY;
    }
}
function icuContainerIteratorNext(state) {
    if (state.index < state.removes.length) {
        const removeOpCode = state.removes[state.index++];
        ngDevMode && assertNumber(removeOpCode, 'Expecting OpCode number');
        if (removeOpCode > 0) {
            const rNode = state.lView[removeOpCode];
            ngDevMode && assertDomNode(rNode);
            return rNode;
        }
        else {
            state.stack.push(state.index, state.removes);
            // ICUs are represented by negative indices
            const tIcuIndex = ~removeOpCode;
            const tIcu = state.lView[TVIEW].data[tIcuIndex];
            ngDevMode && assertTIcu(tIcu);
            enterIcu(state, tIcu, state.lView);
            return icuContainerIteratorNext(state);
        }
    }
    else {
        if (state.stack.length === 0) {
            return null;
        }
        else {
            state.removes = state.stack.pop();
            state.index = state.stack.pop();
            return icuContainerIteratorNext(state);
        }
    }
}
export function loadIcuContainerVisitor() {
    const _state = {
        stack: [],
        index: -1,
    };
    /**
     * Retrieves a set of root nodes from `TIcu.remove`. Used by `TNodeType.ICUContainer`
     * to determine which root belong to the ICU.
     *
     * Example of usage.
     * ```
     * const nextRNode = icuContainerIteratorStart(tIcuContainerNode, lView);
     * let rNode: RNode|null;
     * while(rNode = nextRNode()) {
     *   console.log(rNode);
     * }
     * ```
     *
     * @param tIcuContainerNode Current `TIcuContainerNode`
     * @param lView `LView` where the `RNode`s should be looked up.
     */
    function icuContainerIteratorStart(tIcuContainerNode, lView) {
        _state.lView = lView;
        while (_state.stack.length)
            _state.stack.pop();
        ngDevMode && assertTNodeForLView(tIcuContainerNode, lView);
        enterIcu(_state, tIcuContainerNode.value, lView);
        return icuContainerIteratorNext.bind(null, _state);
    }
    return icuContainerIteratorStart;
}
export function createIcuIterator(tIcu, lView) {
    const state = {
        stack: [],
        index: -1,
        lView,
    };
    ngDevMode && assertTIcu(tIcu);
    enterIcu(state, tIcu, lView);
    return icuContainerIteratorNext.bind(null, state);
}
//# sourceMappingURL=data:application/json;base64,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