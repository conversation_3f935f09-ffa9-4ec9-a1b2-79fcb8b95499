{"version": 3, "file": "primeng-inputtext.mjs", "sources": ["../../src/app/components/inputtext/inputtext.ts", "../../src/app/components/inputtext/primeng-inputtext.ts"], "sourcesContent": ["import { NgModule, Directive, ElementRef, HostListener, Do<PERSON>he<PERSON>, Optional, ChangeDetectorRef, AfterViewInit, Input } from '@angular/core';\nimport { NgModel } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { PrimeNGConfig } from 'primeng/api';\n\n/**\n * InputText directive is an extension to standard input element with theming.\n * @group Components\n */\n@Directive({\n    selector: '[pInputText]',\n    host: {\n        class: 'p-inputtext p-component p-element',\n        '[class.p-filled]': 'filled',\n        '[class.p-variant-filled]': 'variant === \"filled\" || config.inputStyle() === \"filled\"'\n    }\n})\nexport class InputText implements DoCheck, AfterViewInit {\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n\n    filled: Nullable<boolean>;\n\n    constructor(public el: ElementRef, @Optional() public ngModel: NgModel, private cd: ChangeDetectorRef, public config: PrimeNGConfig) {}\n\n    ngAfterViewInit() {\n        this.updateFilledState();\n        this.cd.detectChanges();\n    }\n\n    ngDoCheck() {\n        this.updateFilledState();\n    }\n\n    @HostListener('input', ['$event'])\n    onInput() {\n        this.updateFilledState();\n    }\n\n    updateFilledState() {\n        this.filled = (this.el.nativeElement.value && this.el.nativeElement.value.length) || (this.ngModel && this.ngModel.model);\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [InputText],\n    declarations: [InputText]\n})\nexport class InputTextModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAMA;;;AAGG;MASU,SAAS,CAAA;AASC,IAAA,EAAA,CAAA;AAAmC,IAAA,OAAA,CAAA;AAA0B,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AAR9G;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AAErD,IAAA,MAAM,CAAoB;AAE1B,IAAA,WAAA,CAAmB,EAAc,EAAqB,OAAgB,EAAU,EAAqB,EAAS,MAAqB,EAAA;QAAhH,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAqB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAEvI,eAAe,GAAA;QACX,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,SAAS,GAAA;QACL,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC5B;IAGD,OAAO,GAAA;QACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC5B;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC7H;uGA3BQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAT,SAAS,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,8DAAA,EAAA,EAAA,cAAA,EAAA,mCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBARrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,mCAAmC;AAC1C,wBAAA,kBAAkB,EAAE,QAAQ;AAC5B,wBAAA,0BAA0B,EAAE,0DAA0D;AACzF,qBAAA;AACJ,iBAAA,CAAA;;0BAUuC,QAAQ;qGAJnC,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAgBN,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAexB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAnCf,YAAA,EAAA,CAAA,SAAS,CA+BR,EAAA,OAAA,EAAA,CAAA,YAAY,aA/Bb,SAAS,CAAA,EAAA,CAAA,CAAA;AAmCT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAJd,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,SAAS,CAAC;oBACpB,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;ACpDD;;AAEG;;;;"}