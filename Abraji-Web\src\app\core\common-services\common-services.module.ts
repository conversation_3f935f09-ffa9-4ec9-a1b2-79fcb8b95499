import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpService } from './services/http.service';
import { EncryptionService } from './services/encryption.service';
import { TranslationService } from './services/translation.service';
import { ErrorInterceptor } from './interceptors/error-interceptor';
import { SharedModule } from '../../modules/shared/shared.module';
import { ExcelService } from './services/excel.service';
import { CacheService } from './services/cache.service';

const SERVICES = [
  TranslationService,
  HttpService,
  EncryptionService,
  ExcelService,
  ErrorInterceptor,
  CacheService,
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    SharedModule,
  ],
})
export class CommonServicesModule {
  static forRoot(): ModuleWithProviders<CommonServicesModule> {
    return {
      ngModule: CommonServicesModule,
      providers: [
        ...SERVICES,
      ],
    };
  }
}
