{"version": 3, "file": "primeng-splitbutton.mjs", "sources": ["../../src/app/components/splitbutton/splitbutton.ts", "../../src/app/components/splitbutton/primeng-splitbutton.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, ContentChildren, ElementRef, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewChild, ViewEncapsulation, booleanAttribute, numberAttribute, signal } from '@angular/core';\nimport { MenuItem, PrimeTemplate } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { TieredMenu, TieredMenuModule } from 'primeng/tieredmenu';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { AutoFocusModule } from 'primeng/autofocus';\n\nimport { ButtonProps, MenuButtonProps } from './splitbutton.interface';\n\ntype SplitButtonIconPosition = 'left' | 'right';\n/**\n * SplitButton groups a set of commands in an overlay with a default command.\n * @group Components\n */\n@Component({\n    selector: 'p-splitButton',\n    template: `\n        <div #container [ngClass]=\"containerClass\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-container *ngIf=\"contentTemplate; else defaultButton\">\n                <button\n                    class=\"p-splitbutton-defaultbutton\"\n                    type=\"button\"\n                    pButton\n                    [severity]=\"severity\"\n                    [text]=\"text\"\n                    [outlined]=\"outlined\"\n                    [size]=\"size\"\n                    [icon]=\"icon\"\n                    [iconPos]=\"iconPos\"\n                    (click)=\"onDefaultButtonClick($event)\"\n                    [disabled]=\"disabled\"\n                    [attr.tabindex]=\"tabindex\"\n                    [ariaLabel]=\"buttonProps?.['ariaLabel'] || label\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                    [pTooltip]=\"tooltip\"\n                    [tooltipOptions]=\"tooltipOptions\"\n                >\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </button>\n            </ng-container>\n            <ng-template #defaultButton>\n                <button\n                    #defaultbtn\n                    class=\"p-splitbutton-defaultbutton\"\n                    type=\"button\"\n                    pButton\n                    [severity]=\"severity\"\n                    [text]=\"text\"\n                    [outlined]=\"outlined\"\n                    [size]=\"size\"\n                    [icon]=\"icon\"\n                    [iconPos]=\"iconPos\"\n                    [label]=\"label\"\n                    (click)=\"onDefaultButtonClick($event)\"\n                    [disabled]=\"buttonDisabled\"\n                    [attr.tabindex]=\"tabindex\"\n                    [ariaLabel]=\"buttonProps?.['ariaLabel']\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                    [pTooltip]=\"tooltip\"\n                    [tooltipOptions]=\"tooltipOptions\"\n                ></button>\n            </ng-template>\n            <button\n                type=\"button\"\n                pButton\n                [size]=\"size\"\n                [severity]=\"severity\"\n                [text]=\"text\"\n                [outlined]=\"outlined\"\n                class=\"p-splitbutton-menubutton p-button-icon-only\"\n                (click)=\"onDropdownButtonClick($event)\"\n                (keydown)=\"onDropdownButtonKeydown($event)\"\n                [disabled]=\"menuButtonDisabled\"\n                [ariaLabel]=\"menuButtonProps?.['ariaLabel'] || expandAriaLabel\"\n                [attr.aria-haspopup]=\"menuButtonProps?.['ariaHasPopup'] || true\"\n                [attr.aria-expanded]=\"menuButtonProps?.['ariaExpanded'] || isExpanded()\"\n                [attr.aria-controls]=\"menuButtonProps?.['ariaControls'] || ariaId\"\n            >\n                <ChevronDownIcon *ngIf=\"!dropdownIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"dropdownIconTemplate\"></ng-template>\n            </button>\n            <p-tieredMenu\n                [id]=\"ariaId\"\n                #menu\n                [popup]=\"true\"\n                [model]=\"model\"\n                [style]=\"menuStyle\"\n                [styleClass]=\"menuStyleClass\"\n                [appendTo]=\"appendTo\"\n                [showTransitionOptions]=\"showTransitionOptions\"\n                [hideTransitionOptions]=\"hideTransitionOptions\"\n                (onHide)=\"onHide()\"\n                (onShow)=\"onShow()\"\n            ></p-tieredMenu>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./splitbutton.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class SplitButton {\n    /**\n     * MenuModel instance to define the overlay items.\n     * @group Props\n     */\n    @Input() model: MenuItem[] | undefined;\n    /**\n     * Defines the style of the button.\n     * @group Props\n     */\n    @Input() severity: 'success' | 'info' | 'warning' | 'danger' | 'help' | 'primary' | 'secondary' | 'contrast' | null | undefined;\n    /**\n     * Add a shadow to indicate elevation.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) raised: boolean = false;\n    /**\n     * Add a circular border radius to the button.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) rounded: boolean = false;\n    /**\n     * Add a textual class to the button without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) text: boolean = false;\n    /**\n     * Add a border class without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) outlined: boolean = false;\n    /**\n     * Defines the size of the button.\n     * @group Props\n     */\n    @Input() size: 'small' | 'large' | undefined | null = null;\n    /**\n     * Add a plain textual class to the button without a background initially.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) plain: boolean = false;\n    /**\n     * Name of the icon.\n     * @group Props\n     */\n    @Input() icon: string | undefined;\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    @Input() iconPos: SplitButtonIconPosition = 'left';\n    /**\n     * Text of the button.\n     * @group Props\n     */\n    @Input() label: string | undefined;\n    /**\n     * Tooltip for the main button.\n     * @group Props\n     */\n    @Input() tooltip: string | undefined;\n    /**\n     * Tooltip options for the main button.\n     * @group Props\n     */\n    @Input() tooltipOptions: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the overlay menu.\n     * @group Props\n     */\n    @Input() menuStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the overlay menu.\n     * @group Props\n     */\n    @Input() menuStyleClass: string | undefined;\n\n    /**\n     *  Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Indicates the direction of the element.\n     * @group Props\n     */\n    @Input() dir: string | undefined;\n    /**\n     * Defines a string that labels the expand button for accessibility.\n     * @group Props\n     */\n    @Input() expandAriaLabel: string | undefined;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.1s linear';\n    /**\n     * Button Props\n     */\n    @Input() buttonProps: ButtonProps | undefined;\n    /**\n     * Menu Button Props\n     */\n    @Input() menuButtonProps: MenuButtonProps | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) set disabled(v: boolean | undefined) {\n        this._disabled = v;\n        this._buttonDisabled = v;\n        this.menuButtonDisabled = v;\n    }\n    public get disabled(): boolean | undefined {\n        return this._disabled;\n    }\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n    /**\n     * When present, it specifies that the menu button element should be disabled.\n     * @group Props\n     */\n    @Input('menuButtonDisabled') set menuButtonDisabled(v: boolean | undefined) {\n        if (this.disabled) {\n            this._menuButtonDisabled = this.disabled;\n        } else this._menuButtonDisabled = v;\n    }\n    public get menuButtonDisabled(): boolean | undefined {\n        return this._menuButtonDisabled;\n    }\n    /**\n     * When present, it specifies that the button element should be disabled.\n     * @group Props\n     */\n    @Input() set buttonDisabled(v: boolean | undefined) {\n        if (this.disabled) {\n            this.buttonDisabled = this.disabled;\n        } else this._buttonDisabled = v;\n    }\n    public get buttonDisabled(): boolean {\n        return this._buttonDisabled;\n    }\n    /**\n     * Callback to invoke when default command button is clicked.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    @Output() onClick: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();\n    /**\n     * Callback to invoke when overlay menu is hidden.\n     * @group Emits\n     */\n    @Output() onMenuHide: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when overlay menu is shown.\n     * @group Emits\n     */\n    @Output() onMenuShow: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when dropdown button is clicked.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    @Output() onDropdownClick: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();\n\n    @ViewChild('container') containerViewChild: ElementRef | undefined;\n\n    @ViewChild('defaultbtn') buttonViewChild: ElementRef | undefined;\n\n    @ViewChild('menu') menu: TieredMenu | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    dropdownIconTemplate: TemplateRef<any> | undefined;\n\n    ariaId: string | undefined;\n\n    isExpanded = signal<boolean>(false);\n\n    private _disabled: boolean | undefined;\n\n    private _buttonDisabled: boolean | undefined;\n\n    private _menuButtonDisabled: boolean | undefined;\n\n    ngOnInit() {\n        this.ariaId = UniqueComponentId();\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'dropdownicon':\n                    this.dropdownIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    get containerClass() {\n        const cls = {\n            'p-splitbutton p-component': true,\n            'p-button-raised': this.raised,\n            'p-button-rounded': this.rounded,\n            'p-button-outlined': this.outlined,\n            'p-button-text': this.text,\n            [`p-button-${this.size === 'small' ? 'sm' : 'lg'}`]: this.size\n        };\n\n        return { ...cls };\n    }\n\n    onDefaultButtonClick(event: MouseEvent) {\n        this.onClick.emit(event);\n        this.menu.hide();\n    }\n\n    onDropdownButtonClick(event?: MouseEvent) {\n        this.onDropdownClick.emit(event);\n        this.menu?.toggle({ currentTarget: this.containerViewChild?.nativeElement, relativeAlign: this.appendTo == null });\n    }\n\n    onDropdownButtonKeydown(event: KeyboardEvent) {\n        if (event.code === 'ArrowDown' || event.code === 'ArrowUp') {\n            this.onDropdownButtonClick();\n            event.preventDefault();\n        }\n    }\n\n    onHide() {\n        this.isExpanded.set(false);\n        this.onMenuHide.emit();\n    }\n\n    onShow() {\n        this.isExpanded.set(true);\n        this.onMenuShow.emit();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, ButtonModule, TieredMenuModule, AutoFocusModule, ChevronDownIcon],\n    exports: [SplitButton, ButtonModule, TieredMenuModule],\n    declarations: [SplitButton]\n})\nexport class SplitButtonModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAYA;;;AAGG;MA4FU,WAAW,CAAA;AACpB;;;AAGG;AACM,IAAA,KAAK,CAAyB;AACvC;;;AAGG;AACM,IAAA,QAAQ,CAA+G;AAChI;;;AAGG;IACqC,MAAM,GAAY,KAAK,CAAC;AAChE;;;AAGG;IACqC,OAAO,GAAY,KAAK,CAAC;AACjE;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACM,IAAI,GAAyC,IAAI,CAAC;AAC3D;;;AAGG;IACqC,KAAK,GAAY,KAAK,CAAC;AAC/D;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;IACM,OAAO,GAA4B,MAAM,CAAC;AACnD;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,SAAS,CAA8C;AAChE;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAE5C;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;AACM,IAAA,GAAG,CAAqB;AACjC;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;IACM,qBAAqB,GAAW,iCAAiC,CAAC;AAC3E;;;AAGG;IACM,qBAAqB,GAAW,YAAY,CAAC;AACtD;;AAEG;AACM,IAAA,WAAW,CAA0B;AAC9C;;AAEG;AACM,IAAA,eAAe,CAA8B;AACtD;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;IACH,IAA4C,QAAQ,CAAC,CAAsB,EAAA;AACvE,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACnB,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AACzB,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;KAC/B;AACD,IAAA,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;AACD;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;IACH,IAAiC,kBAAkB,CAAC,CAAsB,EAAA;QACtE,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,SAAA;;AAAM,YAAA,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;KACvC;AACD,IAAA,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;KACnC;AACD;;;AAGG;IACH,IAAa,cAAc,CAAC,CAAsB,EAAA;QAC9C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvC,SAAA;;AAAM,YAAA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;KACnC;AACD,IAAA,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;AACD;;;;AAIG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC7E;;;AAGG;AACO,IAAA,UAAU,GAAsB,IAAI,YAAY,EAAO,CAAC;AAClE;;;AAGG;AACO,IAAA,UAAU,GAAsB,IAAI,YAAY,EAAO,CAAC;AAClE;;;;AAIG;AACO,IAAA,eAAe,GAA6B,IAAI,YAAY,EAAc,CAAC;AAE7D,IAAA,kBAAkB,CAAyB;AAE1C,IAAA,eAAe,CAAyB;AAE9C,IAAA,IAAI,CAAyB;AAEhB,IAAA,SAAS,CAAuC;AAEhF,IAAA,eAAe,CAA+B;AAE9C,IAAA,oBAAoB,CAA+B;AAEnD,IAAA,MAAM,CAAqB;AAE3B,IAAA,UAAU,GAAG,MAAM,CAAU,KAAK,CAAC,CAAC;AAE5B,IAAA,SAAS,CAAsB;AAE/B,IAAA,eAAe,CAAsB;AAErC,IAAA,mBAAmB,CAAsB;IAEjD,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,MAAM,GAAG,iBAAiB,EAAE,CAAC;KACrC;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,IAAI,cAAc,GAAA;AACd,QAAA,MAAM,GAAG,GAAG;AACR,YAAA,2BAA2B,EAAE,IAAI;YACjC,iBAAiB,EAAE,IAAI,CAAC,MAAM;YAC9B,kBAAkB,EAAE,IAAI,CAAC,OAAO;YAChC,mBAAmB,EAAE,IAAI,CAAC,QAAQ;YAClC,eAAe,EAAE,IAAI,CAAC,IAAI;AAC1B,YAAA,CAAC,YAAY,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,CAAA,CAAE,GAAG,IAAI,CAAC,IAAI;SACjE,CAAC;AAEF,QAAA,OAAO,EAAE,GAAG,GAAG,EAAE,CAAC;KACrB;AAED,IAAA,oBAAoB,CAAC,KAAiB,EAAA;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;KACpB;AAED,IAAA,qBAAqB,CAAC,KAAkB,EAAA;AACpC,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,kBAAkB,EAAE,aAAa,EAAE,aAAa,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC,CAAC;KACtH;AAED,IAAA,uBAAuB,CAAC,KAAoB,EAAA;QACxC,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;YACxD,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;KAC1B;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;KAC1B;uGA/QQ,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,0GAeA,gBAAgB,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAKhB,gBAAgB,CAKhB,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,sCAKhB,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAUhB,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,OAAA,EAAA,SAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,GAAA,EAAA,KAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAoFhB,gBAAgB,CAKhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,sCAYhB,eAAe,CAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAsDlB,aAAa,EA5RpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,mrBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,aAAA,EAAA,uBAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,QAAA,EAAA,QAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA2RwE,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAnR/E,WAAW,EAAA,UAAA,EAAA,CAAA;kBA3FvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,mrBAAA,CAAA,EAAA,CAAA;8BAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAIG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAIG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKM,QAAQ,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAYC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKJ,kBAAkB,EAAA,CAAA;sBAAlD,KAAK;uBAAC,oBAAoB,CAAA;gBAYd,cAAc,EAAA,CAAA;sBAA1B,KAAK;gBAaI,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAKG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAKG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAEiB,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEG,eAAe,EAAA,CAAA;sBAAvC,SAAS;uBAAC,YAAY,CAAA;gBAEJ,IAAI,EAAA,CAAA;sBAAtB,SAAS;uBAAC,MAAM,CAAA;gBAEe,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAoFrB,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,iBAvRjB,WAAW,CAAA,EAAA,OAAA,EAAA,CAmRV,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,CAAA,EAAA,OAAA,EAAA,CAnR/E,WAAW,EAoRG,YAAY,EAAE,gBAAgB,CAAA,EAAA,CAAA,CAAA;AAG5C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAJhB,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EACjE,YAAY,EAAE,gBAAgB,CAAA,EAAA,CAAA,CAAA;;2FAG5C,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,CAAC;AACzF,oBAAA,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,gBAAgB,CAAC;oBACtD,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;ACjYD;;AAEG;;;;"}