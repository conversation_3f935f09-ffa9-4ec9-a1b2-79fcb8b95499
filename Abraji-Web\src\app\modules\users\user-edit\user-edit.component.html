<!-- Edit -->
<div class="p-8 rounded-lg bg-gray-50 dark:bg-gray-800">
  <form [formGroup]="userForm">
    <!-- Basic Information Section -->
    <section class="bg-white p-6 mb-8">
      <h2 class="text-xl font-semibold mb-4">{{'user.edit.basicInformation' | transloco}}</h2>
      <div class="md:flex md:space-x-6">
        <div class="md:w-1/2 space-y-4 me-5">
          <div>
            <label for="username" class="flex items-center text-gray-600">{{'user.overview.username' | transloco}} <span
                class="ms-2 text-red-700">*</span> </label>
            @if (username_editable) {
            <input [formControl]="username" [value]="this.userDetails?.data?.username" title="username" type="text"
              class="bg-gray-300 w-full border border-gray-300 p-2 rounded-lg">
            }@else {
            <input disabled [value]="this.userDetails?.data?.username" id="username" type="text"
              class="bg-gray-300 w-full border border-gray-300 p-2 rounded-lg">
            }
            <p class="error-message text-red-500 mt-2" *ngIf="username && username.invalid && username.touched">
              <span *ngIf="username.errors.required">{{ 'validationErrors.required' | transloco }}</span>
            </p>
          </div>
          <div>
            <label for="pass" class="block text-gray-600">{{'user.overview.password' | transloco}} <span
                *ngIf="username_editable" class="ms-2 text-red-700">*</span> </label>
            <input formControlName="password" id="pass" type="password"
              class="w-full border border-gray-300 p-2 rounded-lg">
            <p class="error-message text-red-500 mt-2" *ngIf="password && password.invalid && password.touched">
              <span *ngIf="password.errors.required">{{ 'validationErrors.required' | transloco }}</span>
            </p>
          </div>
          <div>
            <label for="portal" class="block text-gray-600">{{'user.edit.portalPassword' | transloco}}</label>
            <input formControlName="portal_password" id="portal" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
        </div>
        <div class="md:w-1/2 space-y-4 me-5">
          <div>
            <label for="profile" class="flex items-center text-gray-600">
              {{'user.overview.profile' | transloco}} <span class="ms-2 text-red-700">*</span>
            </label>
            <select id="profile" formControlName="profile_id" class="w-full border border-gray-300 p-2 rounded-lg">
              <option [disabled]="!profile_editable" *ngFor="let service of services" [value]="service.id"
                [selected]="service.id === userDetails?.data?.profile_id">
                {{ service.name }}
              </option>
            </select>
            <p class="error-message text-red-500 mt-2" *ngIf="username && username.invalid && username.touched">
              <span *ngIf="username.errors.required">{{ 'validationErrors.required' | transloco }}</span>
            </p>
          </div>
          <div>
            <label for="confirm" class="block text-gray-600">{{'user.edit.confirmPassword' | transloco}} <span
                *ngIf="username_editable" class="ms-2 text-red-700">*</span> </label>
            <input formControlName="confirm_password" id="confirm" type="password"
              class="w-full border border-gray-300 p-2 rounded-lg">
            <p class="error-message text-red-500 mt-2"
              *ngIf="confirmPassword && confirmPassword.invalid && confirmPassword.touched">
              <span *ngIf="confirmPassword.errors.required">{{ 'validationErrors.required' | transloco }}</span>
              <span *ngIf="confirmPassword.errors.passwordMismatch">{{ 'validationErrors.passwordMismatch' | transloco
                }}</span>
            </p>
          </div>
          <div class=" flex max-lg:flex-col justify-between items-start">
            <label for="use_separate_portal_password" class="inline-flex items-center cursor-pointer">
              <input formControlName="use_separate_portal_password" id="use_separate_portal_password" type="checkbox"
                class="sr-only peer">
              <div
                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
              </div>
              <span
                class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">{{'user.edit.useSeparatePortalPassword'
                | transloco}}</span>
            </label>
            <label for="enabled" class="inline-flex my-2 items-center cursor-pointer">
              <input formControlName="enabled" id="enabled" type="checkbox" class="sr-only peer">
              <div
                class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
              </div>
              <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">{{'user.edit.enableAccount' |
                transloco}}</span>
            </label>
          </div>
        </div>
      </div>
    </section>

    <!-- Personal Information Section -->
    <section class="bg-white p-6 mb-8">
      <h2 class="text-xl font-semibold mb-4">{{'user.edit.personalInformation' | transloco}}</h2>
      <div class="md:flex md:space-x-6">
        <div class="md:w-1/2 space-y-4 me-5">
          <div>
            <label for="firstname" class="block text-gray-600">{{'user.overview.firstname' | transloco}}</label>
            <input formControlName="firstname" id="firstname" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
          <div>
            <label for="lastname" class="block text-gray-600">{{'user.overview.lastname' | transloco}}</label>
            <input formControlName="lastname" id="lastname" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
          <div>
            <label for="email" class="block text-gray-600">{{'user.overview.email' | transloco}}</label>
            <input formControlName="email" id="email" type="email" class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
          <div>
            <label for="phone" class="block text-gray-600">{{'user.overview.phone' | transloco}}</label>
            <input formControlName="phone" id="phone" type="text" class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
          <div>
            <label for="apartment" class="block text-gray-600">{{'user.overview.apartment' | transloco}}</label>
            <input formControlName="apartment" id="apartment" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
        </div>
        <div class="md:w-1/2 space-y-4 me-5 mt-4">
          <div>
            <label for="city" class="block text-gray-600">{{'user.overview.city' | transloco}}</label>
            <input formControlName="city" id="city" type="text" class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
          <div>
            <label for="address" class="block text-gray-600">{{'user.overview.address' | transloco}}</label>
            <input formControlName="address" id="address" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
          <div>
            <label for="street" class="block text-gray-600">{{'user.overview.street' | transloco}}</label>
            <input formControlName="street" id="street" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
          <div>
            <label for="company" class="block text-gray-600">{{'user.overview.company' | transloco}}</label>
            <input formControlName="company" id="company" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>

        </div>
      </div>
    </section>

    <!-- Additional Information Section -->
    <section class="bg-white p-6 mb-8">
      <h2 class="text-xl font-semibold mb-4">{{'user.edit.additionalInformation' | transloco}}</h2>
      <div class="md:flex md:space-x-6">
        <div class="md:w-1/2 space-y-4 me-5">
          <div>
            <label for="contract_id" class="block text-gray-600">{{'user.overview.contract_id' | transloco}}</label>
            <input formControlName="contract_id" id="contract_id" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
          <div>
            <label for="national_id" class="block text-gray-600">{{'user.overview.national_id' | transloco}}</label>
            <input formControlName="national_id" id="national_id" type="text"
              class="w-full border border-gray-300 p-2 rounded-lg">
          </div>
        </div>
        <div class="md:w-1/2 space-y-4 me-5">
          <div>
            <label for="notes" class="block text-gray-600">{{'user.overview.notes' | transloco}}</label>
            <textarea formControlName="notes" id="notes" rows="5"
              class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              placeholder="..."></textarea>
            <!-- <input type="text" class="w-full border border-gray-300 p-2 rounded-lg"> -->
          </div>
        </div>
      </div>
    </section>
    <div class="flex justify-end">
      <button (click)="onSubmit()" *ngIf="!isLoading" type="button"
        class="cursor-pointer text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">{{
        'user.edit.save' | transloco }}
      </button>
      <button *ngIf="isLoading" disabled type="button"
        class="text-white bg-blue-400 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 inline-flex justify-center items-center">
        {{'common.loading' | transloco}}
        <svg aria-hidden="true" role="status" class="inline w-4 h-4 ms-3 text-white animate-spin" viewBox="0 0 100 101"
          fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
            fill="#E5E7EB" />
          <path
            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
            fill="currentColor" />
        </svg>
      </button>
    </div>
  </form>

</div>
