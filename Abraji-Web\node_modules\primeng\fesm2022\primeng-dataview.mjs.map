{"version": 3, "file": "primeng-dataview.mjs", "sources": ["../../src/app/components/dataview/dataview.ts", "../../src/app/components/dataview/primeng-dataview.ts"], "sourcesContent": ["import {\n    NgModule,\n    Component,\n    ElementRef,\n    OnInit,\n    AfterContentInit,\n    Input,\n    Output,\n    EventEmitter,\n    ContentChild,\n    ContentChildren,\n    QueryList,\n    TemplateRef,\n    OnChanges,\n    SimpleChanges,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    ViewEncapsulation,\n    OnDestroy,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ObjectUtils } from 'primeng/utils';\nimport { Header, Footer, PrimeTemplate, SharedModule, FilterService, TranslationKeys, PrimeNGConfig } from 'primeng/api';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { BlockableUI } from 'primeng/api';\nimport { Subscription } from 'rxjs';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { ThLargeIcon } from 'primeng/icons/thlarge';\nimport { BarsIcon } from 'primeng/icons/bars';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { DataViewLayoutChangeEvent, DataViewLazyLoadEvent, DataViewPageEvent, DataViewPaginatorState, DataViewSortEvent } from './dataview.interface';\n/**\n * DataView displays data in grid or list layout with pagination and sorting features.\n * @group Components\n */\n@Component({\n    selector: 'p-dataView',\n    template: `\n        <div [ngClass]=\"{ 'p-dataview p-component': true, 'p-dataview-list': layout === 'list', 'p-dataview-grid': layout === 'grid' }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-dataview-loading\" *ngIf=\"loading\">\n                <div class=\"p-dataview-loading-overlay p-component-overlay\">\n                    <i *ngIf=\"loadingIcon\" [class]=\"'p-dataview-loading-icon pi-spin ' + loadingIcon\"></i>\n                    <ng-container *ngIf=\"!loadingIcon\">\n                        <SpinnerIcon *ngIf=\"!loadingIconTemplate\" [spin]=\"true\" [styleClass]=\"'p-dataview-loading-icon'\" />\n                        <ng-template *ngTemplateOutlet=\"loadingIconTemplate\"></ng-template>\n                    </ng-container>\n                </div>\n            </div>\n            <div class=\"p-dataview-header\" *ngIf=\"header || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <p-paginator\n                [rows]=\"rows\"\n                [first]=\"first\"\n                [totalRecords]=\"totalRecords\"\n                [pageLinkSize]=\"pageLinks\"\n                [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"paginate($event)\"\n                styleClass=\"p-paginator-top\"\n                [rowsPerPageOptions]=\"rowsPerPageOptions\"\n                *ngIf=\"paginator && (paginatorPosition === 'top' || paginatorPosition == 'both')\"\n                [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [dropdownScrollHeight]=\"paginatorDropdownScrollHeight\"\n                [templateLeft]=\"paginatorLeftTemplate\"\n                [templateRight]=\"paginatorRightTemplate\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\"\n                [showFirstLastIcon]=\"showFirstLastIcon\"\n                [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\"\n                [showCurrentPageReport]=\"showCurrentPageReport\"\n                [showJumpToPageDropdown]=\"showJumpToPageDropdown\"\n                [showPageLinks]=\"showPageLinks\"\n                [styleClass]=\"paginatorStyleClass\"\n            ></p-paginator>\n\n            <div class=\"p-dataview-content\">\n                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: paginator ? (filteredValue || value | slice : (lazy ? 0 : first) : (lazy ? 0 : first) + rows) : filteredValue || value }\"></ng-container>\n\n                <div *ngIf=\"isEmpty() && !loading\">\n                    <div class=\"p-dataview-emptymessage\">\n                        <ng-container *ngIf=\"!emptyMessageTemplate; else empty\">\n                            {{ emptyMessageLabel }}\n                        </ng-container>\n                        <ng-container #empty *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n                    </div>\n                </div>\n            </div>\n            <p-paginator\n                [rows]=\"rows\"\n                [first]=\"first\"\n                [totalRecords]=\"totalRecords\"\n                [pageLinkSize]=\"pageLinks\"\n                [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"paginate($event)\"\n                styleClass=\"p-paginator-bottom\"\n                [rowsPerPageOptions]=\"rowsPerPageOptions\"\n                *ngIf=\"paginator && (paginatorPosition === 'bottom' || paginatorPosition == 'both')\"\n                [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [dropdownScrollHeight]=\"paginatorDropdownScrollHeight\"\n                [templateLeft]=\"paginatorLeftTemplate\"\n                [templateRight]=\"paginatorRightTemplate\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\"\n                [showFirstLastIcon]=\"showFirstLastIcon\"\n                [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\"\n                [showCurrentPageReport]=\"showCurrentPageReport\"\n                [showJumpToPageDropdown]=\"showJumpToPageDropdown\"\n                [showPageLinks]=\"showPageLinks\"\n                [styleClass]=\"paginatorStyleClass\"\n            ></p-paginator>\n            <div class=\"p-dataview-footer\" *ngIf=\"footer || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./dataview.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class DataView implements OnInit, AfterContentInit, OnDestroy, BlockableUI, OnChanges {\n    /**\n     * When specified as true, enables the pagination.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) paginator: boolean | undefined;\n    /**\n     * Number of rows to display per page.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) rows: number | undefined;\n    /**\n     * Number of total records, defaults to length of value when not defined.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) totalRecords: number | undefined;\n    /**\n     * Number of page links to display in paginator.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) pageLinks: number = 5;\n    /**\n     * Array of integer/object values to display inside rows per page dropdown of paginator\n     * @group Props\n     */\n    @Input() rowsPerPageOptions: number[] | any[] | undefined;\n    /**\n     * Position of the paginator.\n     * @group Props\n     */\n    @Input() paginatorPosition: 'top' | 'bottom' | 'both' = 'bottom';\n    /**\n     * Custom style class for paginator\n     * @group Props\n     */\n    @Input() paginatorStyleClass: string | undefined;\n    /**\n     * Whether to show it even there is only one page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) alwaysShowPaginator: boolean = true;\n    /**\n     * Target element to attach the paginator dropdown overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() paginatorDropdownAppendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Paginator dropdown height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    @Input() paginatorDropdownScrollHeight: string = '200px';\n    /**\n     * Template of the current page report element. Available placeholders are {currentPage},{totalPages},{rows},{first},{last} and {totalRecords}\n     * @group Props\n     */\n    @Input() currentPageReportTemplate: string = '{currentPage} of {totalPages}';\n    /**\n     * Whether to display current page report.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showCurrentPageReport: boolean | undefined;\n    /**\n     * Whether to display a dropdown to navigate to any page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showJumpToPageDropdown: boolean | undefined;\n    /**\n     * When enabled, icons are displayed on paginator to go first and last page.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showFirstLastIcon: boolean = true;\n    /**\n     * Whether to show page links.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showPageLinks: boolean = true;\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazy: boolean | undefined;\n    /**\n     * Whether to call lazy loading on initialization.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazyLoadOnInit: boolean = true;\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    @Input() emptyMessage: string = '';\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Style class of the grid.\n     * @group Props\n     */\n    @Input() gridStyleClass: string = '';\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity.\n     * @group Props\n     */\n    @Input() trackBy: Function = (index: number, item: any) => item;\n    /**\n     * Comma separated list of fields in the object graph to search against.\n     * @group Props\n     */\n    @Input() filterBy: string | undefined;\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() filterLocale: string | undefined;\n    /**\n     * Displays a loader to indicate data load is in progress.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) loading: boolean | undefined;\n    /**\n     * The icon to show while indicating data load is in progress.\n     * @group Props\n     */\n    @Input() loadingIcon: string | undefined;\n    /**\n     * Index of the first row to be displayed.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) first: number | undefined = 0;\n    /**\n     * Property name of data to use in sorting by default.\n     * @group Props\n     */\n    @Input() sortField: string | undefined;\n    /**\n     * Order to sort the data by default.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) sortOrder: number | undefined;\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    @Input() value: any[] | undefined;\n    /**\n     * Defines the layout mode.\n     * @group Props\n     */\n    @Input() get layout(): 'list' | 'grid' {\n        return this._layout;\n    }\n    set layout(layout: 'list' | 'grid') {\n        this._layout = layout;\n\n        if (this.initialized) {\n            this.changeLayout(layout);\n        }\n    }\n    /**\n     * Callback to invoke when paging, sorting or filtering happens in lazy mode.\n     * @param {DataViewLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    @Output() onLazyLoad: EventEmitter<DataViewLazyLoadEvent> = new EventEmitter<DataViewLazyLoadEvent>();\n    /**\n     * Callback to invoke when pagination occurs.\n     * @param {DataViewPageEvent} event - Custom page event.\n     * @group Emits\n     */\n    @Output() onPage: EventEmitter<DataViewPageEvent> = new EventEmitter<DataViewPageEvent>();\n    /**\n     * Callback to invoke when sorting occurs.\n     * @param {DataViewSortEvent} event - Custom sort event.\n     * @group Emits\n     */\n    @Output() onSort: EventEmitter<DataViewSortEvent> = new EventEmitter<DataViewSortEvent>();\n    /**\n     * Callback to invoke when changing layout.\n     * @param {DataViewLayoutChangeEvent} event - Custom layout change event.\n     * @group Emits\n     */\n    @Output() onChangeLayout: EventEmitter<DataViewLayoutChangeEvent> = new EventEmitter<DataViewLayoutChangeEvent>();\n\n    @ContentChild(Header) header: any;\n\n    @ContentChild(Footer) footer: any;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    _value: Nullable<any[]>;\n\n    listTemplate: Nullable<TemplateRef<any>>;\n\n    gridTemplate: Nullable<TemplateRef<any>>;\n\n    itemTemplate: Nullable<TemplateRef<any>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    emptyMessageTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorLeftTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorRightTemplate: Nullable<TemplateRef<any>>;\n\n    paginatorDropdownItemTemplate: Nullable<TemplateRef<any>>;\n\n    loadingIconTemplate: Nullable<TemplateRef<any>>;\n\n    listIconTemplate: Nullable<TemplateRef<any>>;\n\n    gridIconTemplate: Nullable<TemplateRef<any>>;\n\n    filteredValue: Nullable<any[]>;\n\n    filterValue: Nullable<string>;\n\n    initialized: Nullable<boolean>;\n\n    _layout: 'list' | 'grid' = 'list';\n\n    translationSubscription: Nullable<Subscription>;\n\n    get emptyMessageLabel(): string {\n        return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n\n    constructor(public el: ElementRef, public cd: ChangeDetectorRef, public filterService: FilterService, public config: PrimeNGConfig) {}\n\n    ngOnInit() {\n        if (this.lazy && this.lazyLoadOnInit) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        }\n\n        this.translationSubscription = this.config.translationObserver.subscribe(() => {\n            this.cd.markForCheck();\n        });\n        this.initialized = true;\n    }\n\n    ngOnChanges(simpleChanges: SimpleChanges) {\n        if (simpleChanges.value) {\n            this._value = simpleChanges.value.currentValue;\n            this.updateTotalRecords();\n\n            if (!this.lazy && this.hasFilter()) {\n                this.filter(this.filterValue as string);\n            }\n        }\n\n        if (simpleChanges.sortField || simpleChanges.sortOrder) {\n            //avoid triggering lazy load prior to lazy initialization at onInit\n            if (!this.lazy || this.initialized) {\n                this.sort();\n            }\n        }\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'listItem':\n                case 'list':\n                    this.listTemplate = item.template;\n                    break;\n\n                case 'gridItem':\n                case 'grid':\n                    this.gridTemplate = item.template;\n                    break;\n\n                case 'paginatorleft':\n                    this.paginatorLeftTemplate = item.template;\n                    break;\n\n                case 'paginatorright':\n                    this.paginatorRightTemplate = item.template;\n                    break;\n\n                case 'paginatordropdownitem':\n                    this.paginatorDropdownItemTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyMessageTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'loadingicon':\n                    this.loadingIconTemplate = item.template;\n                    break;\n\n                case 'listicon':\n                    this.listIconTemplate = item.template;\n                    break;\n\n                case 'gridicon':\n                    this.gridIconTemplate = item.template;\n                    break;\n            }\n        });\n\n        this.updateItemTemplate();\n    }\n\n    updateItemTemplate() {\n        switch (this.layout) {\n            case 'list':\n                this.itemTemplate = this.listTemplate;\n                break;\n\n            case 'grid':\n                this.itemTemplate = this.gridTemplate;\n                break;\n        }\n    }\n\n    changeLayout(layout: 'list' | 'grid') {\n        this._layout = layout;\n        this.onChangeLayout.emit({\n            layout: this.layout\n        });\n        this.updateItemTemplate();\n\n        this.cd.markForCheck();\n    }\n\n    updateTotalRecords() {\n        this.totalRecords = this.lazy ? this.totalRecords : this._value ? this._value.length : 0;\n    }\n\n    paginate(event: DataViewPaginatorState) {\n        this.first = event.first;\n        this.rows = event.rows;\n\n        if (this.lazy) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        }\n\n        this.onPage.emit({\n            first: <number>this.first,\n            rows: <number>this.rows\n        });\n    }\n\n    sort() {\n        this.first = 0;\n\n        if (this.lazy) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        } else if (this.value) {\n            this.value.sort((data1, data2) => {\n                let value1 = ObjectUtils.resolveFieldData(data1, this.sortField);\n                let value2 = ObjectUtils.resolveFieldData(data2, this.sortField);\n                let result = null;\n\n                if (value1 == null && value2 != null) result = -1;\n                else if (value1 != null && value2 == null) result = 1;\n                else if (value1 == null && value2 == null) result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2);\n                else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n\n                return (this.sortOrder as number) * result;\n            });\n\n            if (this.hasFilter()) {\n                this.filter(this.filterValue as string);\n            }\n        }\n\n        this.onSort.emit({\n            sortField: <string>this.sortField,\n            sortOrder: <number>this.sortOrder\n        });\n    }\n\n    isEmpty() {\n        let data = this.filteredValue || this.value;\n        return data == null || data.length == 0;\n    }\n\n    createLazyLoadMetadata(): DataViewLazyLoadEvent {\n        return {\n            first: <number>this.first,\n            rows: <number>this.rows,\n            sortField: <string>this.sortField,\n            sortOrder: <number>this.sortOrder\n        };\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    filter(filter: string, filterMatchMode: string = 'contains') {\n        this.filterValue = filter;\n\n        if (this.value && this.value.length) {\n            let searchFields = (this.filterBy as string).split(',');\n            this.filteredValue = this.filterService.filter(this.value, searchFields, filter, filterMatchMode, this.filterLocale);\n\n            if (this.filteredValue.length === this.value.length) {\n                this.filteredValue = null;\n            }\n\n            if (this.paginator) {\n                this.first = 0;\n                this.totalRecords = this.filteredValue ? this.filteredValue.length : this.value ? this.value.length : 0;\n            }\n\n            this.cd.markForCheck();\n        }\n    }\n\n    hasFilter() {\n        return this.filterValue && this.filterValue.trim().length > 0;\n    }\n\n    ngOnDestroy() {\n        if (this.translationSubscription) {\n            this.translationSubscription.unsubscribe();\n        }\n    }\n}\n\n@Component({\n    selector: 'p-dataViewLayoutOptions',\n    template: `\n        <div [ngClass]=\"'p-dataview-layout-options p-selectbutton p-buttonset'\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <button type=\"button\" class=\"p-button p-button-icon-only\" [ngClass]=\"{ 'p-highlight': dv.layout === 'list' }\" (click)=\"changeLayout($event, 'list')\" (keydown.enter)=\"changeLayout($event, 'list')\">\n                <BarsIcon *ngIf=\"!dv.listIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"dv.listIconTemplate\"></ng-template></button\n            ><button type=\"button\" class=\"p-button p-button-icon-only\" [ngClass]=\"{ 'p-highlight': dv.layout === 'grid' }\" (click)=\"changeLayout($event, 'grid')\" (keydown.enter)=\"changeLayout($event, 'grid')\">\n                <ThLargeIcon *ngIf=\"!dv.gridIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"dv.gridIconTemplate\"></ng-template>\n            </button>\n        </div>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class DataViewLayoutOptions {\n    @Input() style: { [klass: string]: any } | null | undefined;\n\n    @Input() styleClass: string | undefined;\n\n    constructor(public dv: DataView) {}\n\n    changeLayout(event: Event, layout: 'list' | 'grid') {\n        this.dv.changeLayout(layout);\n        event.preventDefault();\n    }\n}\n@NgModule({\n    imports: [CommonModule, SharedModule, PaginatorModule, SpinnerIcon, BarsIcon, ThLargeIcon],\n    exports: [DataView, SharedModule, DataViewLayoutOptions],\n    declarations: [DataView, DataViewLayoutOptions]\n})\nexport class DataViewModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAiCA;;;AAGG;MAwFU,QAAQ,CAAA;AA4OE,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAA8B,IAAA,aAAA,CAAA;AAAqC,IAAA,MAAA,CAAA;AA3O7G;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;AACoC,IAAA,IAAI,CAAqB;AAChE;;;AAGG;AACoC,IAAA,YAAY,CAAqB;AACxE;;;AAGG;IACoC,SAAS,GAAW,CAAC,CAAC;AAC7D;;;AAGG;AACM,IAAA,kBAAkB,CAA+B;AAC1D;;;AAGG;IACM,iBAAiB,GAA8B,QAAQ,CAAC;AACjE;;;AAGG;AACM,IAAA,mBAAmB,CAAqB;AACjD;;;AAGG;IACqC,mBAAmB,GAAY,IAAI,CAAC;AAC5E;;;AAGG;AACM,IAAA,yBAAyB,CAAgF;AAClH;;;AAGG;IACM,6BAA6B,GAAW,OAAO,CAAC;AACzD;;;AAGG;IACM,yBAAyB,GAAW,+BAA+B,CAAC;AAC7E;;;AAGG;AACqC,IAAA,qBAAqB,CAAsB;AACnF;;;AAGG;AACqC,IAAA,sBAAsB,CAAsB;AACpF;;;AAGG;IACqC,iBAAiB,GAAY,IAAI,CAAC;AAC1E;;;AAGG;IACqC,aAAa,GAAY,IAAI,CAAC;AACtE;;;AAGG;AACqC,IAAA,IAAI,CAAsB;AAClE;;;AAGG;IACqC,cAAc,GAAY,IAAI,CAAC;AACvE;;;AAGG;IACM,YAAY,GAAW,EAAE,CAAC;AACnC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACM,cAAc,GAAW,EAAE,CAAC;AACrC;;;AAGG;IACM,OAAO,GAAa,CAAC,KAAa,EAAE,IAAS,KAAK,IAAI,CAAC;AAChE;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACqC,IAAA,OAAO,CAAsB;AACrE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;IACoC,KAAK,GAAuB,CAAC,CAAC;AACrE;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACoC,IAAA,SAAS,CAAqB;AACrE;;;AAGG;AACM,IAAA,KAAK,CAAoB;AAClC;;;AAGG;AACH,IAAA,IAAa,MAAM,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;IACD,IAAI,MAAM,CAAC,MAAuB,EAAA;AAC9B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAC7B,SAAA;KACJ;AACD;;;;AAIG;AACO,IAAA,UAAU,GAAwC,IAAI,YAAY,EAAyB,CAAC;AACtG;;;;AAIG;AACO,IAAA,MAAM,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAC1F;;;;AAIG;AACO,IAAA,MAAM,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAC1F;;;;AAIG;AACO,IAAA,cAAc,GAA4C,IAAI,YAAY,EAA6B,CAAC;AAE5F,IAAA,MAAM,CAAM;AAEZ,IAAA,MAAM,CAAM;AAEF,IAAA,SAAS,CAAqC;AAE9E,IAAA,MAAM,CAAkB;AAExB,IAAA,YAAY,CAA6B;AAEzC,IAAA,YAAY,CAA6B;AAEzC,IAAA,YAAY,CAA6B;AAEzC,IAAA,cAAc,CAA6B;AAE3C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,cAAc,CAA6B;AAE3C,IAAA,qBAAqB,CAA6B;AAElD,IAAA,sBAAsB,CAA6B;AAEnD,IAAA,6BAA6B,CAA6B;AAE1D,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,gBAAgB,CAA6B;AAE7C,IAAA,gBAAgB,CAA6B;AAE7C,IAAA,aAAa,CAAkB;AAE/B,IAAA,WAAW,CAAmB;AAE9B,IAAA,WAAW,CAAoB;IAE/B,OAAO,GAAoB,MAAM,CAAC;AAElC,IAAA,uBAAuB,CAAyB;AAEhD,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;KACzF;AAED,IAAA,WAAA,CAAmB,EAAc,EAAS,EAAqB,EAAS,aAA4B,EAAS,MAAqB,EAAA;QAA/G,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAEtI,QAAQ,GAAA;AACJ,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YAClC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACvD,SAAA;AAED,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAK;AAC1E,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;AAED,IAAA,WAAW,CAAC,aAA4B,EAAA;QACpC,IAAI,aAAa,CAAC,KAAK,EAAE;YACrB,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,YAAY,CAAC;YAC/C,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAChC,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAqB,CAAC,CAAC;AAC3C,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,EAAE;;YAEpD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;gBAChC,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,aAAA;AACJ,SAAA;KACJ;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,UAAU,CAAC;AAChB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,UAAU,CAAC;AAChB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,eAAe;AAChB,oBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC3C,MAAM;AAEV,gBAAA,KAAK,gBAAgB;AACjB,oBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC5C,MAAM;AAEV,gBAAA,KAAK,uBAAuB;AACxB,oBAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnD,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B;IAED,kBAAkB,GAAA;QACd,QAAQ,IAAI,CAAC,MAAM;AACf,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;gBACtC,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;gBACtC,MAAM;AACb,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,MAAuB,EAAA;AAChC,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;AACtB,SAAA,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAE1B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;KAC5F;AAED,IAAA,QAAQ,CAAC,KAA6B,EAAA;AAClC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAEvB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACvD,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,KAAK,EAAU,IAAI,CAAC,KAAK;YACzB,IAAI,EAAU,IAAI,CAAC,IAAI;AAC1B,SAAA,CAAC,CAAC;KACN;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAEf,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACvD,SAAA;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AAC7B,gBAAA,IAAI,MAAM,GAAG,WAAW,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACjE,gBAAA,IAAI,MAAM,GAAG,WAAW,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACjE,IAAI,MAAM,GAAG,IAAI,CAAC;AAElB,gBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,qBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC;AACjD,qBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC;qBACjD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ;AAAE,oBAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;;oBACpG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAE7D,gBAAA,OAAQ,IAAI,CAAC,SAAoB,GAAG,MAAM,CAAC;AAC/C,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AAClB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAqB,CAAC,CAAC;AAC3C,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,SAAS,EAAU,IAAI,CAAC,SAAS;YACjC,SAAS,EAAU,IAAI,CAAC,SAAS;AACpC,SAAA,CAAC,CAAC;KACN;IAED,OAAO,GAAA;QACH,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC;QAC5C,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;KAC3C;IAED,sBAAsB,GAAA;QAClB,OAAO;YACH,KAAK,EAAU,IAAI,CAAC,KAAK;YACzB,IAAI,EAAU,IAAI,CAAC,IAAI;YACvB,SAAS,EAAU,IAAI,CAAC,SAAS;YACjC,SAAS,EAAU,IAAI,CAAC,SAAS;SACpC,CAAC;KACL;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;AAED,IAAA,MAAM,CAAC,MAAc,EAAE,eAAA,GAA0B,UAAU,EAAA;AACvD,QAAA,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAE1B,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjC,IAAI,YAAY,GAAI,IAAI,CAAC,QAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAErH,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACjD,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,aAAA;YAED,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,gBAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACf,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3G,aAAA;AAED,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;KACjE;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAC9B,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;AAC9C,SAAA;KACJ;uGAtbQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,EAKG,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAKhB,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,kDAKf,eAAe,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKf,eAAe,CAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAoBf,gBAAgB,CAoBhB,EAAA,yBAAA,EAAA,2BAAA,EAAA,6BAAA,EAAA,+BAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAAA,gBAAgB,CAKhB,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAAA,gBAAgB,iEAKhB,gBAAgB,CAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAKhB,gBAAgB,CAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAKhB,gBAAgB,CAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAKhB,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAwChB,gBAAgB,CAUhB,EAAA,WAAA,EAAA,aAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,CAUf,EAAA,SAAA,EAAA,WAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,eAAe,uPA6CrB,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAEN,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAEH,aAAa,EAvRpB,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6ET,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,6KAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,cAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,cAAA,EAAA,eAAA,EAAA,UAAA,EAAA,sBAAA,EAAA,2BAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,cAAA,EAAA,MAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,qBAAA,EAAA,wBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAgesD,WAAW,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,IAAA,EAAA,OAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAxdzD,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAvFpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6ET,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,6KAAA,CAAA,EAAA,CAAA;uKAOuC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,YAAY,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKkC,mBAAmB,EAAA,CAAA;sBAA1D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,yBAAyB,EAAA,CAAA;sBAAjC,KAAK;gBAKG,6BAA6B,EAAA,CAAA;sBAArC,KAAK;gBAKG,yBAAyB,EAAA,CAAA;sBAAjC,KAAK;gBAKkC,qBAAqB,EAAA,CAAA;sBAA5D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,sBAAsB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,iBAAiB,EAAA,CAAA;sBAAxD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKiC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKO,MAAM,EAAA,CAAA;sBAAlB,KAAK;gBAeI,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAEe,MAAM,EAAA,CAAA;sBAA3B,YAAY;uBAAC,MAAM,CAAA;gBAEE,MAAM,EAAA,CAAA;sBAA3B,YAAY;uBAAC,MAAM,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAyQrB,qBAAqB,CAAA;AAKX,IAAA,EAAA,CAAA;AAJV,IAAA,KAAK,CAA8C;AAEnD,IAAA,UAAU,CAAqB;AAExC,IAAA,WAAA,CAAmB,EAAY,EAAA;QAAZ,IAAE,CAAA,EAAA,GAAF,EAAE,CAAU;KAAI;IAEnC,YAAY,CAAC,KAAY,EAAE,MAAuB,EAAA;AAC9C,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC7B,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;uGAVQ,qBAAqB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,QAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,EAhBpB,QAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;KAUT,EAmBmE,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,QAAQ,0EAAE,WAAW,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAbhF,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAlBjC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,yBAAyB;AACnC,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;AAUT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;0EAEY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,UAAU,EAAA,CAAA;sBAAlB,KAAK;;MAcG,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAd,cAAc,EAAA,YAAA,EAAA,CA5dd,QAAQ,EA2cR,qBAAqB,aAapB,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,aAxdhF,QAAQ,EAydG,YAAY,EAdvB,qBAAqB,CAAA,EAAA,CAAA,CAAA;AAiBrB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAJb,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EACrE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGvB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC;AAC1F,oBAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,qBAAqB,CAAC;AACxD,oBAAA,YAAY,EAAE,CAAC,QAAQ,EAAE,qBAAqB,CAAC;AAClD,iBAAA,CAAA;;;ACvlBD;;AAEG;;;;"}