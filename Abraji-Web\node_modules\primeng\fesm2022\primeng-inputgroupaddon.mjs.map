{"version": 3, "file": "primeng-inputgroupaddon.mjs", "sources": ["../../src/app/components/inputgroupaddon/inputgroupaddon.ts", "../../src/app/components/inputgroupaddon/primeng-inputgroupaddon.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { Component, Input, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n/**\n * InputGroupAddon displays text, icon, buttons and other content can be grouped next to an input.\n * @group Components\n */\n@Component({\n    selector: 'p-inputGroupAddon',\n    template: `\n        <div [attr.data-pc-name]=\"'inputgroupaddon'\" [ngClass]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `,\n    host: {\n        class: 'p-element p-inputgroup-addon'\n    }\n})\nexport class InputGroupAddon {\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [InputGroupAddon, SharedModule],\n    declarations: [InputGroupAddon]\n})\nexport class InputGroupAddonModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAGA;;;AAGG;MAYU,eAAe,CAAA;AACxB;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;uGAV/B,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EATd,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,8BAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;2FAKQ,eAAe,EAAA,UAAA,EAAA,CAAA;kBAX3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,CAAA;;;;AAIT,IAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,8BAA8B;AACxC,qBAAA;AACJ,iBAAA,CAAA;8BAMY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;;MAQG,qBAAqB,CAAA;uGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,iBAlBrB,eAAe,CAAA,EAAA,OAAA,EAAA,CAcd,YAAY,CAdb,EAAA,OAAA,EAAA,CAAA,eAAe,EAeG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAG9B,qBAAqB,EAAA,OAAA,EAAA,CAJpB,YAAY,EACK,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG9B,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBALjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC;oBACxC,YAAY,EAAE,CAAC,eAAe,CAAC;AAClC,iBAAA,CAAA;;;ACnCD;;AAEG;;;;"}