{"info": {"_postman_id": "9fabefd5-486c-4623-8d73-9054a90203e1", "name": "Abraj APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "25069920", "_collection_link": "https://rootech-software.postman.co/workspace/Abraji-workspace~e3a993d0-1cdc-4a67-b943-5b1c9834c7e9/collection/18268571-9fabefd5-486c-4623-8d73-9054a90203e1?action=share&source=collection_link&creator=25069920"}, "item": [{"name": "Authentication", "item": [{"name": "login", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"payload\": \"U2FsdGVkX1+fwLfSeoQO4sbJv89j2WaKCt9t8BrIboIA2/yPTXh0ezVmaOHB3+jxWdVMM1k2ugAsMjvepfEAkw==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/auth/login", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "auth", "login"]}}, "response": []}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Users", "item": [{"name": "profile", "item": [{"name": "GetManagerTree", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMDg4ODgxMCwiZXhwIjoxNzIwOTMyMDEwLCJuYmYiOjE3MjA4ODg4MTAsImp0aSI6IjE1am5WWmo4dmMyNTVXbnciLCJzdWIiOjcxMjYsInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.gLHcJ_MXzBJQA_rqLZeL8iDZ6Uo8mjJp3UJpJ7IDz28", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/profile/manager-tree", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "profile", "manager-tree"]}}, "response": []}, {"name": "change user service", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX19TfcQek1eBCGJaE0e31zTaCzvIZe/hOLdSUP8wV5Cklo5BPbwlxOUq11arti39DtUYoKwQcRr8PvgXzpWOLiuJhkGw/jNm7ek=\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/profile/change-service", "host": ["{{app_url}}"], "path": ["api", "profile", "change-service"]}}, "response": []}, {"name": "get services", "request": {"method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/profile/services/222", "host": ["{{app_url}}"], "path": ["api", "profile", "services", "222"]}}, "response": []}, {"name": "change-profile", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\":\"U2FsdGVkX19ngUDjTj7OeWwpaP9w021PIW5osAB6KppNJo1cLyYt7axT+rYc8k8xGS0IIke/B0JkQDRliMmNX5Qixs3N3//379NVm+AdKHA=\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/profile/change-profile", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "profile", "change-profile"]}, "description": "This endpoint is used to change the profile information. The request should be sent as an HTTP POST to the specified URL. The request body should contain a payload in JSON format.\n\n### Request Body\n\n- payload (string, required): The payload should be provided as a string in the request body.\n    \n- **{\"user_id\":\"319827\",\"profile_id\":2,\"change_type\":\"immediate\"}**\n    \n\n### Response\n\nThe response of this request is a JSON schema. It includes the schema definition for the response data structure."}, "response": []}, {"name": "activation Data By user id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTc3MTcxOSwiZXhwIjoxNzIxODE0OTE5LCJuYmYiOjE3MjE3NzE3MTksImp0aSI6IjRBczJLT0RXUTF2Umhhc20iLCJzdWIiOjcxMjYsInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.IIM0aLKWXMVTwpVeH5aYz7fZB_4PMSb1A05YfpsRfGA", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/profile/active-data/85492", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "profile", "active-data", "85492"]}, "description": "The endpoint retrieves activation data for a specific user. The response returns a JSON object with the following schema:\n\n``` json\n{\n    \"type\": \"object\",\n    \"properties\": {\n        \"data\": {\n            \"type\": \"object\",\n            \"properties\": {\n                \"username\": {\"type\": \"string\"},\n                \"profile_name\": {\"type\": \"string\"},\n                \"profile_id\": {\"type\": \"integer\"},\n                \"parent_id\": {\"type\": \"integer\"},\n                \"manager_balance\": {\"type\": \"string\"},\n                \"user_balance\": {\"type\": \"string\"},\n                \"user_expiration\": {\"type\": \"string\"},\n                \"unit_price\": {\"type\": \"string\"},\n                \"user_price\": {\"type\": \"integer\"},\n                \"profile_duration\": {\"type\": \"string\"},\n                \"profile_traffic\": {\"type\": \"string\"},\n                \"profile_dl_traffic\": {\"type\": \"string\"},\n                \"profile_ul_traffic\": {\"type\": \"string\"},\n                \"profile_description\": {\"type\": [\"string\", \"null\"]},\n                \"vat\": {\"type\": \"string\"},\n                \"units\": {\"type\": \"integer\"},\n                \"required_amount\": {\"type\": \"string\"},\n                \"n_required_amount\": {\"type\": \"integer\"},\n                \"reward_points\": {\"type\": \"integer\"},\n                \"required_points\": {\"type\": \"integer\"},\n                \"reward_points_balance\": {\"type\": \"integer\"}\n            },\n            \"required\": [\"profile_id\", \"parent_id\", \"user_price\", \"n_required_amount\", \"reward_points\", \"required_points\", \"reward_points_balance\"],\n            \"additionalProperties\": false\n        },\n        \"status\": {\n            \"type\": \"integer\"\n        }\n    },\n    \"required\": [\"data\", \"status\"],\n    \"additionalProperties\": false\n}\n\n ```"}, "response": []}]}, {"name": "traffic", "item": [{"name": "user traffic", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\":\"U2FsdGVkX18VMuzfiuYWOvLMsbNt4rG1Z+q/30tEnap3+i/4aRhWJCIzgan54/bFakXLba2RGzCCMKiu5TsPaaCv82W5PUxZx+1PmA0uOGc=\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/users/traffic", "host": ["{{app_url}}"], "path": ["api", "users", "traffic"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTEzMzYwNSwiZXhwIjoxNzIxMTc2ODA1LCJuYmYiOjE3MjExMzM2MDUsImp0aSI6Ik5DUG92N0hMcXlidlNCVFEiLCJzdWIiOjI1NDksInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.CIDe9vp582Gq82QhG7isBF7N2tjUstEn_nNPTXI-L1c", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "users table", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX19JFZr6cR/ChICwOqKBCmyTJlgz4uAMV7WalaAlhaO6SKib922yltwKWu8e6B6waYbOKkPVYuCmTIA3BBhTShHCktsv4S/k/UgVN2zVtGoG7kdfxce+mbRbvEYOQegm3ECN+cyKjQb9DO0jXDYZ+So1zC4GDQ2oGvSlb1NV0g/hsqRhmV9rdI7YnFKO6ur+GZmVFHl0tDcKW9F/fP4aeMd258N5QQTQF/NK//f0N10WiX9ju6W9ybQf1uMRC9Wa5X87F2jvtcNs6Pp/Tr0C4uwpc59BQR/NqI9HGwZKGVKaNto818ClIHt3\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/users/table", "host": ["{{app_url}}"], "path": ["api", "users", "table"]}}, "response": []}, {"name": "online users", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{global_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX19JFZr6cR/ChICwOqKBCmyTJlgz4uAMV7WalaAlhaO6SKib922yltwKWu8e6B6waYbOKkPVYuCmTIA3BBhTShHCktsv4S/k/UgVN2zVtGoG7kdfxce+mbRbvEYOQegm3ECN+cyKjQb9DO0jXDYZ+So1zC4GDQ2oGvSlb1NV0g/hsqRhmV9rdI7YnFKO6ur+GZmVFHl0tDcKW9F/fP4aeMd258N5QQTQF/NK//f0N10WiX9ju6W9ybQf1uMRC9Wa5X87F2jvtcNs6Pp/Tr0C4uwpc59BQR/NqI9HGwZKGVKaNto818ClIHt3\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/users/online?page=1", "host": ["{{app_url}}"], "path": ["api", "users", "online"], "query": [{"key": "page", "value": "1"}]}}, "response": []}, {"name": "create user", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX1/pVUhy8rKkQ70ffDOkfOy/x7DpvLEC4tqIrrkXiis3tWZFGrF27WemC095CsULhjX5PC8Enb9iaGkF6WXdDb6qjaYujDavgrmgY9nfFaY1eC04FD/mX6ZrgXgVG7ALJpMBqsfKEzs32vjN7LvNiAwt2FIPMY64vHKTUO0uI4D5XAGIsv9JWB9BCpmlhUTrsqOGwkWb6oi1Y+OeQvhZYrru3N15UN3rnHWIUrtd01I52H9szyaDUb5OuJYS9lSKiS6Iai94b7W6+55c/T9eEJsS86tIvlu0tsCZkGGWyKoyAPtZa6icbAOesQrC1vf60fEissuEBtvVB3jCfQfV+WTnemu04A15oj+1OFOYELEd0yXyjn6ZZo8RhS6sn6yD+5Paqf4AZ6CO9wqMgVAdJElYXU8qzDmkBMI6ohdtkmUsLP1d3JC0ew10IFfgkiP7M7nHRu4xjqFfRpgPv+anIv0l3nrboQlrXt94h/6JWglCdiw8ycFm6/nj\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/users/create", "host": ["{{app_url}}"], "path": ["api", "users", "create"]}}, "response": []}, {"name": "activate user", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX1/nkDD8y4J0Tc4bJ9gdCMJo+JSuZNP/lKuLh8/QMx3CMgWNjQ2BzkcVaAiiC5AkiJ0N172rct4wsgIkhHSZGcp+wyGoonU8KB41woFymmjtikCPV5WE36tmmchtMb2rl2ebV+****************************/woVBDHYog8+88r/bXQwAfyGVl4JviQv/KC+/nSVcngT2EB0ShjN/YSKbzm6v8wI9IPkFhtewp8DTvjm4CKEZYmvDhXmbxdFbD55no4X6FE0oBfgdZ7+w8a3dbHwFidMbhat2b190Ws7y8HWZMnfct\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/users/activate", "host": ["{{app_url}}"], "path": ["api", "users", "activate"]}}, "response": []}, {"name": "user sessions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{global_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX19NytMCKTyHLcGxl7ggyxXSWknfWFBAf66eqzFBllL52QWKmEvUiLRo57JJjRxRpwF7XLGfSUven7hmWY859A2Q9Fi4LmIsLasuz5hMQ2l/c9QJTvPhbEvcvCy8O52nZ3xUnEBIMsgM7zC1zKYBhuKTQYGLj8qBz8rKiHVyaFOy0MPeFJuZwTa/ge8W7+9HdFAlrijaCIQllLUMQxcB5mSVom7OqTdrDB7ulYUbuxhoceAYcdumpjQlIL2WxIlc95HCbIpsCDdx+eVA3QMY2DTOC1flHRHOqLgiQPRN86e/mfGtLYcARakxLxDa1manj0LtTiFIUZiI+NqZQFf8wXervKkZ4bopUcM=\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/users/sessions/85468", "host": ["{{app_url}}"], "path": ["api", "users", "sessions", "85468"]}}, "response": []}, {"name": "getUserID", "request": {"method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/users/get/318820", "host": ["{{app_url}}"], "path": ["api", "users", "get", "318820"]}}, "response": []}, {"name": "disconnect user", "request": {"method": "GET", "header": [], "url": {"raw": "{{app_url\n}}/api/users/disconnect/324983", "host": ["{{app_url\n}}"], "path": ["api", "users", "disconnect", "324983"]}}, "response": []}, {"name": "edit user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{global_token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\" : \"U2FsdGVkX1/ZzryWOO9HJNQjen3aYP3AvOtDEb+kiKe0m+gswU3XI3Uvyph7qTC4VpRtAM/7iBcjXaA4r0f/IvGYjfyUfzkfXuZIeMWULwH3LMRNv2rJQH66ZrnNDMTCPDMkCsNCZbDQAmE1PQCRfCKxiPzWbw83ZE5ifg6Mcli8fahL9X4ep1VFP8wE4LUE5eKRb18jLFLDLzbnJcpy5zg0ZOnCrL+j1hmI/crsuSEgmQjGj8hcV9RKt6LLrjD+WQI86mtF6eFU2FS35xAy7HXSZfL5JdLLfbDWKB+qJB3H+Q15deOZaV6BzNwRnMcMTh1AvIS9Vcl0+s8+JIYgP2R9dHhmKl+dVmA4ExHFPhOkJJEnUS6FdlJHmDvylgGNL1QBFze3wl+3puYuI2q8QI99d4BI1Q+HMQwfZ0zJEv8aKgdpLro9iRfX5jbT4u1hYnBVpZshtouFW91W3u0kpaHDIV+6a9aYo32vxBj+ZEqqlcFXFwzH499egv18YgZjfnVYiGhBA40slZQpmQPj8aE8HWWReBCOtY1oTpDuWPDwtTihkHlECh3oDkganJMUPn6LDZKQVDH6VQyWm2dTQZi0EexfeAPmztwmfU7T2iEG7mCXIaD23XBejX3RbeZ0MYgXFW1pFWBKc1ra8RYqHg==\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/users/edit/324983", "host": ["{{app_url}}"], "path": ["api", "users", "edit", "324983"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTQ5NDEwNCwiZXhwIjoxNzIxNTM3MzA0LCJuYmYiOjE3MjE0OTQxMDQsImp0aSI6ImNsY3puWjY0b0p6WU1ZbloiLCJzdWIiOjcxMjYsInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.nwDEMi44O_7ofYOWljedG0mWlPCK1EMpZDMmJWmU5zo", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Dashboard", "item": [{"name": "get dashboard", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{global_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/dashboard", "host": ["{{app_url}}"], "path": ["api", "dashboard"]}}, "response": []}, {"name": "get dashboard cards", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTU5ODY3OSwiZXhwIjoxNzIxNjQxODc5LCJuYmYiOjE3MjE1OTg2NzksImp0aSI6Impnalg2bkMycGpub3pvMlgiLCJzdWIiOjcxMjYsInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.HmUsSRL7y-_Z1fXJePt4_LJBKa2Coq9dkAyDnYPgyCI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/dashboard/cards", "host": ["{{app_url}}"], "path": ["api", "dashboard", "cards"]}}, "response": []}, {"name": "get dashboard transactions", "request": {"method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/dashboard/transactions", "host": ["{{app_url}}"], "path": ["api", "dashboard", "transactions"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTAxNjYzMSwiZXhwIjoxNzIxMDU5ODMxLCJuYmYiOjE3MjEwMTY2MzEsImp0aSI6ImdjRFUyQzFkM0lmdXdqU0siLCJzdWIiOjcxMjYsInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.b9o_tUlfegVNI-i3ROrTOvXgQoRbHAQWsV3r368uXIQ", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Manager", "item": [{"name": "get-manager", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTQ5NDEwNCwiZXhwIjoxNzIxNTM3MzA0LCJuYmYiOjE3MjE0OTQxMDQsImp0aSI6ImNsY3puWjY0b0p6WU1ZbloiLCJzdWIiOjcxMjYsInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.nwDEMi44O_7ofYOWljedG0mWlPCK1EMpZDMmJWmU5zo", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/get-manager", "host": ["{{app_url}}"], "path": ["api", "get-manager"]}}, "response": []}]}, {"name": "maintenance", "item": [{"name": "Get-maintenance", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTIyMjYwOCwiZXhwIjoxNzIxMjY1ODA4LCJuYmYiOjE3MjEyMjI2MDgsImp0aSI6IkpGTVNuY1lCanBBRnF4T1giLCJzdWIiOjI1NDksInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.M54Yt382wUIgnL9X6mDGlkFLv_QAaOqgIMEf8TX8y-4", "type": "string"}]}, "method": "GET", "header": [{"key": "PageNumber", "value": "1", "type": "text"}, {"key": "PageSize", "value": "10", "type": "text"}], "url": {"raw": "http://127.0.0.1:8000/api/maintenance", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "maintenance"]}}, "response": []}, {"name": "Add-maintenance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n             \"cost\": \"100.00\",\r\n            \"details\": \"update\",\r\n            \"date\": \"2024-07-15\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/maintenance", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "maintenance"]}}, "response": []}, {"name": "GetCount", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTIyODI3MCwiZXhwIjoxNzIxMjcxNDcwLCJuYmYiOjE3MjEyMjgyNzAsImp0aSI6IlR0NU1rRHJMSW1vbG9QYTQiLCJzdWIiOjI1NDksInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.lC3ieUjn_RivR0JBbgs5vzegJQUX0ae_Rix7VrCfnRc", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/maintenance/getCount", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "maintenance", "getCount"]}}, "response": []}, {"name": "ByID", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/maintenance/getById/6", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "maintenance", "getById", "6"]}}, "response": []}, {"name": "GetByDate", "request": {"method": "GET", "header": [{"key": "PageNumber", "value": "1", "type": "text"}, {"key": "PageSize", "value": "10", "type": "text"}], "url": {"raw": "http://127.0.0.1:8000/api/maintenance/date/2024-7-14/2024-7-16", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "maintenance", "date", "2024-7-14", "2024-7-16"]}}, "response": []}, {"name": "updateMaintenance", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n            \r\n             \"cost\": \"100.00\",\r\n            \"details\": \"update\",\r\n            \"date\": \"2024-07-15\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/maintenance/2", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "maintenance", "2"]}}, "response": []}, {"name": "delete", "request": {"method": "DELETE", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/maintenance/1", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "maintenance", "1"]}}, "response": []}]}, {"name": "Card", "item": [{"name": "getAllCards", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{global_token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\": \"U2FsdGVkX1+KCTug0+v2bWnAN/qaTUt1ZEtWSHE3LN3zgedQtS/3o1/B0JLp+AFST9y/Ek2THnvo1Ekmn7yq1aFjus8pdy0n8pC8cCHp7AEcBOUEpViB/dQemattt5oZA0kFY9WyU2bFLi9p1L/D2aeXoE3/lMmBLFAvc8/Tc8hQgAYW/Z9f59yCzj5JbhH0diD8naBJDO6rVBCVBWWbskfJJYGbshOAwccUCdBABho=\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/card/getAllCards", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "card", "getAllCards"]}}, "response": []}, {"name": "getCardById", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTI5MzE4NywiZXhwIjoxNzIxMzM2Mzg3LCJuYmYiOjE3MjEyOTMxODcsImp0aSI6IkRMZkk5U25rVzZrSTR4T2siLCJzdWIiOjcxMjYsInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.-4hfKaLvlVjYy5JOuRt90pna5FLceVVN1oxph9f-Kiw", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/card/getCardById/2024-88920", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "card", "getCardById", "2024-88920"]}}, "response": []}, {"name": "getListCardForSeries", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9zYXMubmJ0ZWwuaXFcL2FkbWluXC9hcGlcL2luZGV4LnBocFwvYXBpXC9sb2dpbiIsImlhdCI6MTcyMTI5MzE4NywiZXhwIjoxNzIxMzM2Mzg3LCJuYmYiOjE3MjEyOTMxODcsImp0aSI6IkRMZkk5U25rVzZrSTR4T2siLCJzdWIiOjcxMjYsInBydiI6ImQ3OTc3YzQ3ZTkxNjk2NTEwMTA3MzRkMmZiZjhjYzEzOWYzZTUwMzQifQ.-4hfKaLvlVjYy5JOuRt90pna5FLceVVN1oxph9f-Kiw", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"payload\":\"U2FsdGVkX18k8IqVgE+JgYoi3D12w05xqQGRGo6/3HssSDsNlkBm1IXbNnQnPVrhj0VXzf26FFN/0GIlsg/RTceVVzEcFwofvCknb+x+ayXFLZaBlz00Ui4DKJbpja9TvJJIwC1bjbGpRlvZC4WR9wjicEloaYJIReVuWNvtebOSD1M7u2gbJyc9mi/VQs6GazhD6XOqV0mhZNnidDWnIspvQYpnt/ZsieqengMkGXU=\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/card/getListCardForSeries/2024-88920", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "card", "getListCardForSeries", "2024-88920"]}}, "response": []}]}, {"name": "wallet", "item": [{"name": "Add Income", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"amount\": 100.50\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/wallet/add-income", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "wallet", "add-income"]}}, "response": []}, {"name": "get-balance", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/wallet/get-balance", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "wallet", "get-balance"]}}, "response": []}, {"name": "update-balance", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"amount\": 800\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/wallet/update-balance", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "wallet", "update-balance"]}}, "response": []}, {"name": "get-all-transaction", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/wallet/get-history", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "wallet", "get-history"]}}, "response": []}]}, {"name": "Invoices", "item": [{"name": "create invoice", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"318820\",\r\n    \"due_date\": \"2024-07-15\",\r\n    \"username\": \"johndoe\",\r\n    \"type\": \"service\",\r\n    \"amount\": 1500.00,\r\n    \"description\": \"Consulting services\",\r\n    \"created_by\": \"admin\",\r\n    \"discount\": 5,\r\n    \"discount_value\": 75.00,\r\n    \"total\": 1425.00,\r\n    \"payment_method\": \"credit_card\",\r\n    \"payment_date\": \"2024-07-05\",\r\n    \"invoice_items\":[\r\n        {\r\n            \"name\":\"item1\",\r\n            \"price\":\"50\",\r\n            \"quantity\":\"5\",\r\n            \"total\":\"250\"\r\n        }\r\n    ]\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/invoices", "host": ["{{app_url}}"], "path": ["api", "invoices"]}}, "response": []}, {"name": "get invoices", "request": {"method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/invoices?per_page=1&page=1", "host": ["{{app_url}}"], "path": ["api", "invoices"], "query": [{"key": "per_page", "value": "1"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "get invoice by id", "request": {"method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/invoices/1", "host": ["{{app_url}}"], "path": ["api", "invoices", "1"]}}, "response": []}, {"name": "update invoice", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"318820\",\r\n    \"due_date\": \"2024-07-15\",\r\n    \"username\": \"johndoe\",\r\n    \"type\": \"service\",\r\n    \"amount\": \"1500.00\",\r\n    \"description\": \"Consulting services\",\r\n    \"created_by\": \"admin\",\r\n    \"discount\": 5,\r\n    \"discount_value\": \"75.00\",\r\n    \"total\": \"1425.00\",\r\n    \"payment_method\": \"credit_card\",\r\n    \"payment_date\": \"2024-07-05\",\r\n    \"invoice_items\": [\r\n        {\r\n        \"id\": 6,\r\n        \"name\": \"item2 update\",\r\n        \"quantity\": \"2\",\r\n        \"price\": \"100.00\",\r\n        \"total\": \"200.00\"\r\n        }, \r\n        {\r\n        \"name\": \"new item\",\r\n        \"quantity\": \"2\",\r\n        \"price\": \"100.00\",\r\n        \"total\": \"200.00\"\r\n        }\r\n    ]\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{app_url}}/api/invoices/3", "host": ["{{app_url}}"], "path": ["api", "invoices", "3"]}}, "response": []}, {"name": "delete invoice", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{app_url}}/api/invoices/4", "host": ["{{app_url}}"], "path": ["api", "invoices", "4"]}}, "response": []}, {"name": "approve invoice", "request": {"method": "GET", "header": [], "url": {"raw": "{{app_url}}/api/invoices/3/approve", "host": ["{{app_url}}"], "path": ["api", "invoices", "3", "approve"]}}, "response": []}]}, {"name": "Transaction", "item": [{"name": "Create Transaction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"created_by\": \"admin\",\n    \"cost\": 150.75,\n    \"description\": \"Payment for services\",\n    \"date\": \"2024-07-04\",\n    \"type\": \"in\",\n    \"category\": \"general\"\n}"}, "url": {"raw": "{{app_url}}/api/transactions", "host": ["{{app_url}}"], "path": ["api", "transactions"]}}, "response": []}, {"name": "Get All Transactions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{app_url}}/api/transactions", "host": ["{{app_url}}"], "path": ["api", "transactions"]}}, "response": []}, {"name": "Get Transaction By ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{app_url}}/api/transactions/2", "host": ["{{app_url}}"], "path": ["api", "transactions", "2"]}}, "response": []}, {"name": "Update Transaction", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"created_by\": \"admin\",\n    \"cost\": 175.00,\n    \"description\": \"Updated payment for services\",\n    \"date\": \"2024-07-05\",\n    \"type\": \"in\",\n    \"category\": \"general\"\n}"}, "url": {"raw": "{{app_url}}/api/transactions/2", "host": ["{{app_url}}"], "path": ["api", "transactions", "2"]}}, "response": []}, {"name": "Delete Transaction", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{app_url}}/api/transactions/2", "host": ["{{app_url}}"], "path": ["api", "transactions", "2"]}}, "response": []}]}, {"name": "Debits", "item": [{"name": "Get All", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"page\": 1,\r\n    \"pagesize\": 10,\r\n    \"count\": 100,\r\n    \"sortBy\": \"date\",\r\n    \"direction\": \"\",\r\n    \"search\": \"\",\r\n    \"columns\": [\"id\", \"amount\", \"date\", \"description\",\"username\"]\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/debts", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "debts"]}}, "response": []}, {"name": "Create Debt", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": 1,\r\n    \"username\":\"<PERSON><PERSON><PERSON>\",\r\n    \"date\": \"2024-07-24\",\r\n    \"amount\": 100.50,\r\n    \"description\": \"test\",\r\n    \"pay\": false\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/debts/create", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "debts", "create"]}}, "response": []}, {"name": "get debt  is 0 or 1  by user id", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "pay", "value": "1", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"page\": 1,\r\n    \"pagesize\": 10,\r\n    \"count\": 15,\r\n    \"sortBy\": \"date\",\r\n    \"direction\": \"asc\",\r\n    \"search\": \"\",\r\n    \"columns\": [\"id\", \"amount\", \"date\"],\r\n    \"pay\":0\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/debts/user/2", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "debts", "user", "2"], "query": [{"key": "", "value": "0", "disabled": true}, {"key": "page", "value": "1", "disabled": true}, {"key": "pagesize", "value": "10", "disabled": true}]}}, "response": []}, {"name": "pay", "request": {"method": "PUT", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/debts/pay/1", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "debts", "pay", "1"]}}, "response": []}, {"name": "total debt for user", "request": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/debts/user/1/total", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "debts", "user", "1", "total"]}}, "response": []}, {"name": "get total by year and month  and pay 0 or 1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "month", "value": "02", "type": "text", "disabled": true}, {"key": "year", "value": "2023", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"month\": \"07\",\r\n    \"year\": \"2024\",\r\n    \"pay\": 0\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/debts/total/pay/year-month", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "debts", "total", "pay", "year-month"], "query": [{"key": "pay", "value": "0", "disabled": true}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "app_url", "value": "http://127.0.0.1:8000"}]}