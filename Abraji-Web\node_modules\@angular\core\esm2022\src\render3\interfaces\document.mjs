/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { RuntimeError } from '../../errors';
/**
 * Most of the use of `document` in <PERSON><PERSON> is from within the DI system so it is possible to simply
 * inject the `DOCUMENT` token and are done.
 *
 * Ivy is special because it does not rely upon the DI and must get hold of the document some other
 * way.
 *
 * The solution is to define `getDocument()` and `setDocument()` top-level functions for ivy.
 * Wherever ivy needs the global document, it calls `getDocument()` instead.
 *
 * When running ivy outside of a browser environment, it is necessary to call `setDocument()` to
 * tell ivy what the global `document` is.
 *
 * <PERSON>ular does this for us in each of the standard platforms (`Browser` and `Server`)
 * by calling `setDocument()` when providing the `DOCUMENT` token.
 */
let DOCUMENT = undefined;
/**
 * Tell ivy what the `document` is for this platform.
 *
 * It is only necessary to call this if the current platform is not a browser.
 *
 * @param document The object representing the global `document` in this environment.
 */
export function setDocument(document) {
    DOCUMENT = document;
}
/**
 * Access the object that represents the `document` for this platform.
 *
 * Ivy calls this whenever it needs to access the `document` object.
 * For example to create the renderer or to do sanitization.
 */
export function getDocument() {
    if (DOCUMENT !== undefined) {
        return DOCUMENT;
    }
    else if (typeof document !== 'undefined') {
        return document;
    }
    throw new RuntimeError(210 /* RuntimeErrorCode.MISSING_DOCUMENT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&
        `The document object is not available in this context. Make sure the DOCUMENT injection token is provided.`);
    // No "document" can be found. This should only happen if we are running ivy outside Angular and
    // the current platform is not a browser. Since this is not a supported scenario at the moment
    // this should not happen in Angular apps.
    // Once we support running ivy outside of Angular we will need to publish `setDocument()` as a
    // public API.
}
//# sourceMappingURL=data:application/json;base64,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