<div class="container mx-auto p-4">
  <div class="bg-white shadow-md rounded-lg p-6">
    <div class="mb-4">
      <h4>&nbsp;</h4>
      <h4 class="text-xl font-semibold">{{ 'user.quota.changeQuota' | transloco }}</h4>
      <hr class="my-4">
    </div>
    <form class="space-y-6" id="myForm" novalidate>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="form-group row mb-4">
          <label for="username" class="col-form-label col-sm-5 block font-medium">{{ 'user.quota.username'| transloco
            }}</label>
          <div class="col-sm-7">
            <p class="form-control-static">amarNawras</p>
          </div>
        </div>
        <div class="form-group row mb-4">
          <label for="expiration" class="col-form-label col-sm-5  block font-medium">{{ 'user.quota.endsDate' |
            transloco
            }}</label>
          <div class="col-sm-7">
            <p class="form-control-static">2024-06-27 12:00:00</p>
          </div>
        </div>
        <div class="form-group row mb-4">
          <label for="new_profile_id" class="col-form-label col-sm-5  block font-medium">
            {{ 'user.quota.newQuota'| transloco }} <span class="text-red-500">*</span>
          </label>
          <div class="col-sm-7">
            <select id="new_profile_id"
              class="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
              <option value="" selected>empty</option>
              <!-- Add your options here -->
            </select>
          </div>
        </div>
        <div class="form-group row mb-4">
          <label for="profile_name" class="col-form-label col-sm-5  block font-medium">{{ 'user.quota.currentQuota'|
            transloco
            }}</label>
          <div class="col-sm-7">
            <p class="form-control-static">NB MAX</p>
          </div>
        </div>
        <div class="form-group row mb-4">
          <label for="change_type" class="col-form-label col-sm-5  block font-medium">
            {{ 'user.quota.changeTime' | transloco}} <span class="text-red-500">*</span>
          </label>
          <div class="col-sm-7">
            <select id="change_type"
              class="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
              <option value="now">{{ 'user.quota.now' | transloco }}</option>
              <!-- Add your options here -->
            </select>
          </div>
        </div>
      </div>
      <div class="mt-10">
        <hr class="my-4">
        <div class="flex justify-end">
          <button type="button" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">{{
            'user.quota.change'| transloco }}</button>
        </div>
      </div>
    </form>
  </div>
</div>
