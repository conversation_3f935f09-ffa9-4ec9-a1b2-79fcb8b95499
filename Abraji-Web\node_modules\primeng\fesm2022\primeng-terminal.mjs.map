{"version": 3, "file": "primeng-terminal.mjs", "sources": ["../../src/app/components/terminal/terminalservice.ts", "../../src/app/components/terminal/terminal.ts", "../../src/app/components/terminal/primeng-terminal.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\n\n@Injectable()\nexport class TerminalService {\n    private commandSource = new Subject<string>();\n    private responseSource = new Subject<string>();\n\n    commandHandler = this.commandSource.asObservable();\n    responseHandler = this.responseSource.asObservable();\n\n    sendCommand(command: string) {\n        if (command) {\n            this.commandSource.next(command);\n        }\n    }\n\n    sendResponse(response: string) {\n        if (response) {\n            this.responseSource.next(response);\n        }\n    }\n}\n", "import { NgModule, Component, <PERSON><PERSON>iew<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Input, ElementRef, ChangeDetectionStrategy, ViewEncapsulation, ChangeDetectorRef } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { TerminalService } from './terminalservice';\nimport { Subscription } from 'rxjs';\n/**\n * Terminal is a text based user interface.\n * @group Components\n */\n@Component({\n    selector: 'p-terminal',\n    template: `\n        <div [ngClass]=\"'p-terminal p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"focus(in)\">\n            <div *ngIf=\"welcomeMessage\">{{ welcomeMessage }}</div>\n            <div class=\"p-terminal-content\">\n                <div *ngFor=\"let command of commands\">\n                    <span class=\"p-terminal-prompt\">{{ prompt }}</span>\n                    <span class=\"p-terminal-command\">{{ command.text }}</span>\n                    <div class=\"p-terminal-response\" [attr.aria-live]=\"'polite'\">{{ command.response }}</div>\n                </div>\n            </div>\n            <div class=\"p-terminal-prompt-container\">\n                <span class=\"p-terminal-content-prompt\">{{ prompt }}</span>\n                <input #in type=\"text\" [(ngModel)]=\"command\" class=\"p-terminal-input\" autocomplete=\"off\" (keydown)=\"handleCommand($event)\" autofocus />\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./terminal.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Terminal implements AfterViewInit, AfterViewChecked, OnDestroy {\n    /**\n     * Initial text to display on terminal.\n     * @group Props\n     */\n    @Input() welcomeMessage: string | undefined;\n    /**\n     * Prompt text for each command.\n     * @group Props\n     */\n    @Input() prompt: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n\n    commands: any[] = [];\n\n    command!: string;\n\n    container!: Element;\n\n    commandProcessed!: boolean;\n\n    subscription: Subscription;\n\n    constructor(public el: ElementRef, public terminalService: TerminalService, public cd: ChangeDetectorRef) {\n        this.subscription = terminalService.responseHandler.subscribe((response) => {\n            this.commands[this.commands.length - 1].response = response;\n            this.commandProcessed = true;\n        });\n    }\n\n    ngAfterViewInit() {\n        this.container = DomHandler.find(this.el.nativeElement, '.p-terminal')[0];\n    }\n\n    ngAfterViewChecked() {\n        if (this.commandProcessed) {\n            this.container.scrollTop = this.container.scrollHeight;\n            this.commandProcessed = false;\n        }\n    }\n\n    @Input()\n    set response(value: string) {\n        if (value) {\n            this.commands[this.commands.length - 1].response = value;\n            this.commandProcessed = true;\n        }\n    }\n\n    handleCommand(event: KeyboardEvent) {\n        if (event.keyCode == 13) {\n            this.commands.push({ text: this.command });\n            this.terminalService.sendCommand(this.command);\n            this.command = '';\n        }\n    }\n\n    focus(element: HTMLElement) {\n        element.focus();\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, FormsModule],\n    exports: [Terminal],\n    declarations: [Terminal]\n})\nexport class TerminalModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": ["i1.TerminalService"], "mappings": ";;;;;;;;;MAIa,eAAe,CAAA;AAChB,IAAA,aAAa,GAAG,IAAI,OAAO,EAAU,CAAC;AACtC,IAAA,cAAc,GAAG,IAAI,OAAO,EAAU,CAAC;AAE/C,IAAA,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;AACnD,IAAA,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;AAErD,IAAA,WAAW,CAAC,OAAe,EAAA;AACvB,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpC,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,QAAgB,EAAA;AACzB,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtC,SAAA;KACJ;uGAjBQ,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAAf,eAAe,EAAA,CAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;;;ACGX;;;AAGG;MA0BU,QAAQ,CAAA;AAgCE,IAAA,EAAA,CAAA;AAAuB,IAAA,eAAA,CAAA;AAAyC,IAAA,EAAA,CAAA;AA/BnF;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;IAExC,QAAQ,GAAU,EAAE,CAAC;AAErB,IAAA,OAAO,CAAU;AAEjB,IAAA,SAAS,CAAW;AAEpB,IAAA,gBAAgB,CAAW;AAE3B,IAAA,YAAY,CAAe;AAE3B,IAAA,WAAA,CAAmB,EAAc,EAAS,eAAgC,EAAS,EAAqB,EAAA;QAArF,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAe,CAAA,eAAA,GAAf,eAAe,CAAiB;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;AACpG,QAAA,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,QAAQ,KAAI;AACvE,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5D,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;KAC7E;IAED,kBAAkB,GAAA;QACd,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;AACvD,YAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AACjC,SAAA;KACJ;IAED,IACI,QAAQ,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,KAAK,EAAE;AACP,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC;AACzD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,KAAoB,EAAA;AAC9B,QAAA,IAAI,KAAK,CAAC,OAAO,IAAI,EAAE,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/C,YAAA,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,KAAK,CAAC,OAAoB,EAAA;QACtB,OAAO,CAAC,KAAK,EAAE,CAAC;KACnB;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,SAAA;KACJ;uGA1EQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,EAvBP,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;AAeT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,oRAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,oBAAA,EAAA,QAAA,EAAA,8MAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,qDAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,SAAA,EAAA,gBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAzBpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;AAeT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,oRAAA,CAAA,EAAA,CAAA;0IAOQ,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBA+BF,QAAQ,EAAA,CAAA;sBADX,KAAK;;MAgCG,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,iBAlFd,QAAQ,CAAA,EAAA,OAAA,EAAA,CA8EP,YAAY,EAAE,WAAW,aA9E1B,QAAQ,CAAA,EAAA,CAAA,CAAA;wGAkFR,cAAc,EAAA,OAAA,EAAA,CAJb,YAAY,EAAE,WAAW,CAAA,EAAA,CAAA,CAAA;;2FAI1B,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC;oBACpC,OAAO,EAAE,CAAC,QAAQ,CAAC;oBACnB,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;ACpHD;;AAEG;;;;"}