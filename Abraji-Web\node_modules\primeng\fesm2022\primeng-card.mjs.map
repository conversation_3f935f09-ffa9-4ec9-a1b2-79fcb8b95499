{"version": 3, "file": "primeng-card.mjs", "sources": ["../../src/app/components/card/card.ts", "../../src/app/components/card/primeng-card.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChild, ContentChildren, ElementRef, Input, NgModule, QueryList, SimpleChange, TemplateRef, ViewEncapsulation, signal } from '@angular/core';\nimport { BlockableUI, Footer, Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ObjectUtils } from 'primeng/utils';\n/**\n * Card is a flexible container component.\n * @group Components\n */\n@Component({\n    selector: 'p-card',\n    template: `\n        <div [ngClass]=\"'p-card p-component'\" [ngStyle]=\"_style()\" [class]=\"styleClass\" [attr.data-pc-name]=\"'card'\">\n            <div class=\"p-card-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-card-body\">\n                <div class=\"p-card-title\" *ngIf=\"header || titleTemplate\">\n                    {{ header }}\n                    <ng-container *ngTemplateOutlet=\"titleTemplate\"></ng-container>\n                </div>\n                <div class=\"p-card-subtitle\" *ngIf=\"subheader || subtitleTemplate\">\n                    {{ subheader }}\n                    <ng-container *ngTemplateOutlet=\"subtitleTemplate\"></ng-container>\n                </div>\n                <div class=\"p-card-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n                <div class=\"p-card-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                    <ng-content select=\"p-footer\"></ng-content>\n                    <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./card.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Card implements AfterContentInit, BlockableUI {\n    /**\n     * Header of the card.\n     * @group Props\n     */\n    @Input() header: string | undefined;\n    /**\n     * Subheader of the card.\n     * @group Props\n     */\n    @Input() subheader: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() set style(value: { [klass: string]: any } | null | undefined) {\n        if (!ObjectUtils.equals(this._style(), value)) {\n            this._style.set(value);\n        }\n    }\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n\n    @ContentChild(Header) headerFacet: TemplateRef<any> | undefined;\n\n    @ContentChild(Footer) footerFacet: TemplateRef<any> | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    headerTemplate: TemplateRef<any> | undefined;\n\n    titleTemplate: TemplateRef<any> | undefined;\n\n    subtitleTemplate: TemplateRef<any> | undefined;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    footerTemplate: TemplateRef<any> | undefined;\n\n    _style = signal<{ [klass: string]: any } | null | undefined>(null);\n\n    constructor(private el: ElementRef) {}\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'title':\n                    this.titleTemplate = item.template;\n                    break;\n\n                case 'subtitle':\n                    this.subtitleTemplate = item.template;\n                    break;\n\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Card, SharedModule],\n    declarations: [Card]\n})\nexport class CardModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;AAIA;;;AAGG;MAoCU,IAAI,CAAA;AA4CO,IAAA,EAAA,CAAA;AA3CpB;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;IACH,IAAa,KAAK,CAAC,KAAkD,EAAA;AACjE,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE;AAC3C,YAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,SAAA;KACJ;AACD;;;AAGG;AACM,IAAA,UAAU,CAAqB;AAElB,IAAA,WAAW,CAA+B;AAE1C,IAAA,WAAW,CAA+B;AAEhC,IAAA,SAAS,CAAuC;AAEhF,IAAA,cAAc,CAA+B;AAE7C,IAAA,aAAa,CAA+B;AAE5C,IAAA,gBAAgB,CAA+B;AAE/C,IAAA,eAAe,CAA+B;AAE9C,IAAA,cAAc,CAA+B;AAE7C,IAAA,MAAM,GAAG,MAAM,CAA8C,IAAI,CAAC,CAAC;AAEnE,IAAA,WAAA,CAAoB,EAAc,EAAA;QAAd,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;IAEtC,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;uGA9EQ,IAAI,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAJ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAI,8NA0BC,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAEN,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAEH,aAAa,EA/DpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,kDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,IAAI,EAAA,UAAA,EAAA,CAAA;kBAnChB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,QAAQ,EACR,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,kDAAA,CAAA,EAAA,CAAA;+EAOQ,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBASG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAEgB,WAAW,EAAA,CAAA;sBAAhC,YAAY;uBAAC,MAAM,CAAA;gBAEE,WAAW,EAAA,CAAA;sBAAhC,YAAY;uBAAC,MAAM,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAwDrB,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,iBAtFV,IAAI,CAAA,EAAA,OAAA,EAAA,CAkFH,YAAY,CAlFb,EAAA,OAAA,EAAA,CAAA,IAAI,EAmFG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGnB,UAAU,EAAA,OAAA,EAAA,CAJT,YAAY,EACN,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGnB,UAAU,EAAA,UAAA,EAAA,CAAA;kBALtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC;oBAC7B,YAAY,EAAE,CAAC,IAAI,CAAC;AACvB,iBAAA,CAAA;;;AChID;;AAEG;;;;"}