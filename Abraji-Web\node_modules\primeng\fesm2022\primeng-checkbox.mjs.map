{"version": 3, "file": "primeng-checkbox.mjs", "sources": ["../../src/app/components/checkbox/checkbox.ts", "../../src/app/components/checkbox/primeng-checkbox.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport {\n    booleanAttribute,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    forwardRef,\n    Injector,\n    Input,\n    NgModule,\n    numberAttribute,\n    Output,\n    QueryList,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation\n} from '@angular/core';\nimport { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport { PrimeTemplate, SharedModule, PrimeNGConfig } from 'primeng/api';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { ObjectUtils } from 'primeng/utils';\nimport { CheckboxChangeEvent } from './checkbox.interface';\n\nexport const CHECKBOX_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Checkbox),\n    multi: true\n};\n/**\n * Checkbox is an extension to standard checkbox element with theming.\n * @group Components\n */\n@Component({\n    selector: 'p-checkbox',\n    template: `\n        <div\n            [ngStyle]=\"style\"\n            [ngClass]=\"{\n                'p-checkbox p-component': true,\n                'p-checkbox-checked': checked(),\n                'p-checkbox-disabled': disabled,\n                'p-checkbox-focused': focused,\n                'p-variant-filled': variant === 'filled' || config.inputStyle() === 'filled'\n            }\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'checkbox'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"checkbox\"\n                    [value]=\"value\"\n                    [attr.name]=\"name\"\n                    [checked]=\"checked()\"\n                    [attr.tabindex]=\"tabindex\"\n                    [disabled]=\"disabled\"\n                    [readonly]=\"readonly\"\n                    [attr.required]=\"required\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.aria-checked]=\"checked()\"\n                    (change)=\"handleChange($event)\"\n                    (focus)=\"onInputFocus($event)\"\n                    (blur)=\"onInputBlur($event)\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                />\n            </div>\n            <div\n                class=\"p-checkbox-box\"\n                [ngClass]=\"{ 'p-highlight': checked(), 'p-disabled': disabled, 'p-focus': focused }\"\n                (click)=\"onClick($event, input, true)\"\n                [attr.data-p-highlight]=\"checked()\"\n                [attr.data-p-disabled]=\"disabled\"\n                [attr.data-p-focused]=\"focused\"\n                [attr.data-pc-section]=\"'input'\"\n            >\n                <ng-container *ngIf=\"checked()\">\n                    <ng-container *ngIf=\"!checkboxIconTemplate\">\n                        <span *ngIf=\"checkboxIcon\" class=\"p-checkbox-icon\" [ngClass]=\"checkboxIcon\" [attr.data-pc-section]=\"'icon'\"></span>\n                        <CheckIcon *ngIf=\"!checkboxIcon\" [styleClass]=\"'p-checkbox-icon'\" [attr.data-pc-section]=\"'icon'\" />\n                    </ng-container>\n                    <span *ngIf=\"checkboxIconTemplate\" class=\"p-checkbox-icon\" [attr.data-pc-section]=\"'icon'\">\n                        <ng-template *ngTemplateOutlet=\"checkboxIconTemplate\"></ng-template>\n                    </span>\n                </ng-container>\n            </div>\n        </div>\n        <label\n            (click)=\"onClick($event, input, true)\"\n            [class]=\"labelStyleClass\"\n            [ngClass]=\"{ 'p-checkbox-label': true, 'p-checkbox-label-active': checked(), 'p-disabled': disabled, 'p-checkbox-label-focus': focused }\"\n            *ngIf=\"label\"\n            [attr.for]=\"inputId\"\n            [attr.data-pc-section]=\"'label'\"\n        >\n            {{ label }}</label\n        >\n    `,\n    providers: [CHECKBOX_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./checkbox.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Checkbox implements ControlValueAccessor {\n    /**\n     * Value of the checkbox.\n     * @group Props\n     */\n    @Input() value: any;\n    /**\n     * Name of the checkbox group.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Allows to select a boolean value instead of multiple values.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) binary: boolean | undefined;\n    /**\n     * Label of the checkbox.\n     * @group Props\n     */\n    @Input() label: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Style class of the label.\n     * @group Props\n     */\n    @Input() labelStyleClass: string | undefined;\n    /**\n     * Form control value.\n     * @group Props\n     */\n    @Input() formControl: FormControl | undefined;\n    /**\n     * Icon class of the checkbox icon.\n     * @group Props\n     */\n    @Input() checkboxIcon: string | undefined;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean | undefined;\n    /**\n     * When present, it specifies that checkbox must be checked before submitting the form.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) required: boolean | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Value in checked state.\n     * @group Props\n     */\n    @Input() trueValue: any = true;\n    /**\n     * Value in unchecked state.\n     * @group Props\n     */\n    @Input() falseValue: any = false;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Callback to invoke on value change.\n     * @param {CheckboxChangeEvent} event - Custom value change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<CheckboxChangeEvent> = new EventEmitter();\n    /**\n     * Callback to invoke when the receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when the loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n\n    @ViewChild('input') inputViewChild: Nullable<ElementRef>;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    checkboxIconTemplate: TemplateRef<any>;\n\n    model: any;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    focused: boolean = false;\n\n    constructor(public cd: ChangeDetectorRef, private readonly injector: Injector, public config: PrimeNGConfig) {}\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'icon':\n                    this.checkboxIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    onClick(event, checkbox, focus: boolean) {\n        event.preventDefault();\n\n        if (this.disabled || this.readonly) {\n            return;\n        }\n\n        this.updateModel(event);\n\n        if (focus) {\n            checkbox.focus();\n        }\n    }\n\n    updateModel(event) {\n        let newModelValue;\n\n        /*\n         * When `formControlName` or `formControl` is used - `writeValue` is not called after control changes.\n         * Otherwise it is causing multiple references to the actual value: there is one array reference inside the component and another one in the control value.\n         * `selfControl` is the source of truth of references, it is made to avoid reference loss.\n         * */\n        const selfControl = this.injector.get<NgControl | null>(NgControl, null, { optional: true, self: true });\n\n        const currentModelValue = selfControl && !this.formControl ? selfControl.value : this.model;\n\n        if (!this.binary) {\n            if (this.checked()) newModelValue = currentModelValue.filter((val) => !ObjectUtils.equals(val, this.value));\n            else newModelValue = currentModelValue ? [...currentModelValue, this.value] : [this.value];\n\n            this.onModelChange(newModelValue);\n            this.model = newModelValue;\n\n            if (this.formControl) {\n                this.formControl.setValue(newModelValue);\n            }\n        } else {\n            newModelValue = this.checked() ? this.falseValue : this.trueValue;\n            this.model = newModelValue;\n            this.onModelChange(newModelValue);\n        }\n\n        this.onChange.emit({ checked: newModelValue, originalEvent: event });\n    }\n\n    handleChange(event) {\n        if (!this.readonly) {\n            this.updateModel(event);\n        }\n    }\n\n    onInputFocus(event) {\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event) {\n        this.focused = false;\n        this.onBlur.emit(event);\n        this.onModelTouched();\n    }\n\n    focus() {\n        this.inputViewChild.nativeElement.focus();\n    }\n\n    writeValue(model: any): void {\n        this.model = model;\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        setTimeout(() => {\n            this.disabled = val;\n            this.cd.markForCheck();\n        });\n    }\n\n    checked() {\n        return this.binary ? this.model === this.trueValue : ObjectUtils.contains(this.value, this.model);\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, AutoFocusModule, CheckIcon],\n    exports: [Checkbox, SharedModule],\n    declarations: [Checkbox]\n})\nexport class CheckboxModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;AA4Ba,MAAA,uBAAuB,GAAQ;AACxC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,QAAQ,CAAC;AACvC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MA+EU,QAAQ,CAAA;AAsIE,IAAA,EAAA,CAAA;AAAwC,IAAA,QAAA,CAAA;AAA2B,IAAA,MAAA,CAAA;AArItF;;;AAGG;AACM,IAAA,KAAK,CAAM;AACpB;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,MAAM,CAAsB;AACpE;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,WAAW,CAA0B;AAC9C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;IACM,SAAS,GAAQ,IAAI,CAAC;AAC/B;;;AAGG;IACM,UAAU,GAAQ,KAAK,CAAC;AACjC;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;;AAIG;AACO,IAAA,QAAQ,GAAsC,IAAI,YAAY,EAAE,CAAC;AAC3E;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAE9C,IAAA,cAAc,CAAuB;AAEzB,IAAA,SAAS,CAAqC;AAE9E,IAAA,oBAAoB,CAAmB;AAEvC,IAAA,KAAK,CAAM;AAEX,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;IAEpC,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,WAAA,CAAmB,EAAqB,EAAmB,QAAkB,EAAS,MAAqB,EAAA;QAAxF,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAmB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAE/G,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,OAAO,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAc,EAAA;QACnC,KAAK,CAAC,cAAc,EAAE,CAAC;AAEvB,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAExB,QAAA,IAAI,KAAK,EAAE;YACP,QAAQ,CAAC,KAAK,EAAE,CAAC;AACpB,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,aAAa,CAAC;AAElB;;;;AAIK;QACL,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAmB,SAAS,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QAEzG,MAAM,iBAAiB,GAAG,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAE5F,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,IAAI,CAAC,OAAO,EAAE;gBAAE,aAAa,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;gBACvG,aAAa,GAAG,iBAAiB,GAAG,CAAC,GAAG,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAE3F,YAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAClC,YAAA,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;YAE3B,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,gBAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC5C,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;AAClE,YAAA,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;AAC3B,YAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;KACxE;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB;IAED,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KAC7C;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;QACzB,UAAU,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAED,OAAO,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;KACrG;uGA1OQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,qGAeG,gBAAgB,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAKhB,gBAAgB,CAoBhB,EAAA,KAAA,EAAA,OAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,kMAmCf,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,gBAAgB,CA7FzB,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,uBAAuB,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAkInB,aAAa,EAtMpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmET,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,sYAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAuPwC,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA9OzC,QAAQ,EAAA,UAAA,EAAA,CAAA;kBA9EpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmET,EACU,SAAA,EAAA,CAAC,uBAAuB,CAAC,EACnB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,sYAAA,CAAA,EAAA,CAAA;yIAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAMI,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEa,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;gBAEc,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAwHrB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAd,cAAc,EAAA,YAAA,EAAA,CAlPd,QAAQ,CAAA,EAAA,OAAA,EAAA,CA8OP,YAAY,EAAE,eAAe,EAAE,SAAS,CAAA,EAAA,OAAA,EAAA,CA9OzC,QAAQ,EA+OG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAJb,YAAY,EAAE,eAAe,EAAE,SAAS,EAC9B,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGvB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,SAAS,CAAC;AACnD,oBAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;oBACjC,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;ACpWD;;AAEG;;;;"}