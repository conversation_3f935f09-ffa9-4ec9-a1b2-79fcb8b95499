{"version": 3, "file": "primeng-inputmask.mjs", "sources": ["../../src/app/components/inputmask/inputmask.ts", "../../src/app/components/inputmask/primeng-inputmask.ts"], "sourcesContent": ["/*\n    Port of jQuery MaskedInput by DigitalBush as a Native Angular2 Component in Typescript without jQuery\n    https://github.com/digitalBush/jquery.maskedinput/\n\n    Copyright (c) 2007-2014 <PERSON> (digitalbush.com)\n\n    Permission is hereby granted, free of charge, to any person\n    obtaining a copy of this software and associated documentation\n    files (the \"Software\"), to deal in the Software without\n    restriction, including without limitation the rights to use,\n    copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the\n    Software is furnished to do so, subject to the following\n    conditions:\n\n    The above copyright notice and this permission notice shall be\n    included in all copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n    OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n    HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n    WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n    OTHER DEALINGS IN THE SOFTWARE.\n*/\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    forwardRef,\n    numberAttribute\n} from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { DomHandler } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { Caret } from './inputmask.interface';\n\nexport const INPUTMASK_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputMask),\n    multi: true\n};\n/**\n * InputMask component is used to enter input in a certain format such as numeric, date, currency, email and phone.\n * @group Components\n */\n@Component({\n    selector: 'p-inputMask',\n    template: `\n        <input\n            #input\n            pInputText\n            [class]=\"styleClass\"\n            [ngClass]=\"inputClass\"\n            [attr.id]=\"inputId\"\n            [attr.type]=\"type\"\n            [attr.name]=\"name\"\n            [ngStyle]=\"style\"\n            [attr.placeholder]=\"placeholder\"\n            [attr.title]=\"title\"\n            [attr.size]=\"size\"\n            [attr.autocomplete]=\"autocomplete\"\n            [attr.maxlength]=\"maxlength\"\n            [attr.tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-required]=\"ariaRequired\"\n            [disabled]=\"disabled\"\n            [readonly]=\"readonly\"\n            [attr.required]=\"required\"\n            (focus)=\"onInputFocus($event)\"\n            (blur)=\"onInputBlur($event)\"\n            (keydown)=\"onInputKeydown($event)\"\n            (keypress)=\"onKeyPress($event)\"\n            pAutoFocus\n            [variant]=\"variant\"\n            [autofocus]=\"autofocus\"\n            (input)=\"onInputChange($event)\"\n            (paste)=\"handleInputChange($event)\"\n            [attr.data-pc-name]=\"'inputmask'\"\n            [attr.data-pc-section]=\"'root'\"\n        />\n        <ng-container *ngIf=\"value != null && filled && showClear && !disabled\">\n            <TimesIcon *ngIf=\"!clearIconTemplate\" [styleClass]=\"'p-inputmask-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n            <span *ngIf=\"clearIconTemplate\" class=\"p-inputmask-clear-icon\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\">\n                <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n            </span>\n        </ng-container>\n    `,\n    host: {\n        class: 'p-element',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': 'focused',\n        '[class.p-inputmask-clearable]': 'showClear && !disabled'\n    },\n    providers: [INPUTMASK_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./inputmask.css']\n})\nexport class InputMask implements OnInit, ControlValueAccessor {\n    /**\n     * HTML5 input type.\n     * @group Props\n     */\n    @Input() type: string = 'text';\n    /**\n     * Placeholder character in mask, default is underscore.\n     * @group Props\n     */\n    @Input() slotChar: string = '_';\n    /**\n     * Clears the incomplete value on blur.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoClear: boolean = true;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean = false;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    @Input() placeholder: string | undefined;\n    /**\n     * Size of the input field.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) size: number | undefined;\n    /**\n     * Maximum number of character allows in the input field.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) maxlength: number | undefined;\n    /**\n     * Specifies tab order of the element.\n     * @group Props\n     */\n    @Input() tabindex: string | undefined;\n    /**\n     * Title text of the input text.\n     * @group Props\n     */\n    @Input() title: string | undefined;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Used to indicate that user input is required on an element before a form can be submitted.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) ariaRequired: boolean | undefined;\n    /**\n     * When present, it specifies that the element value cannot be altered.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * When present, it specifies that an input field is read-only.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean | undefined;\n    /**\n     * Defines if ngModel sets the raw unmasked value to bound value or the formatted mask value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) unmask: boolean | undefined;\n    /**\n     * Name of the input field.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * When present, it specifies that an input field must be filled out before submitting the form.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) required: boolean | undefined;\n    /**\n     * Regex pattern for alpha characters\n     * @group Props\n     */\n    @Input() characterPattern: string = '[A-Za-z]';\n    /**\n     * When present, the input gets a focus automatically on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * When present, the input gets a focus automatically on load.\n     * @group Props\n     * @deprecated Use autofocus property instead.\n     */\n    @Input({ transform: booleanAttribute }) set autoFocus(value: boolean | undefined) {\n        this.autofocus = value;\n        console.warn('autoFocus is deprecated. Use autofocus property instead.');\n    }\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    @Input() autocomplete: string | undefined;\n    /**\n     * When present, it specifies that whether to clean buffer value from model.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) keepBuffer: boolean = false;\n    /**\n     * Mask pattern.\n     * @group Props\n     */\n    @Input() get mask(): string | undefined | null {\n        return this._mask;\n    }\n    set mask(val: string | undefined | null) {\n        this._mask = val;\n\n        this.initMask();\n        this.writeValue('');\n        this.onModelChange(this.value);\n    }\n    /**\n     * Callback to invoke when the mask is completed.\n     * @group Emits\n     */\n    @Output() onComplete: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke on input.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onInput: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke on input key press.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onKeydown: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when input field is cleared.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<any> = new EventEmitter<any>();\n\n    @ViewChild('input', { static: true }) inputViewChild: Nullable<ElementRef>;\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    clearIconTemplate: Nullable<TemplateRef<any>>;\n\n    value: Nullable<string>;\n\n    _mask: Nullable<string>;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    input: Nullable<HTMLInputElement>;\n\n    filled: Nullable<boolean>;\n\n    defs: Nullable<{ [klass: string]: any }>;\n\n    tests: RegExp[] | any;\n\n    partialPosition: Nullable<number>;\n\n    firstNonMaskPos: Nullable<number>;\n\n    lastRequiredNonMaskPos: Nullable<number>;\n\n    len: Nullable<number>;\n\n    oldVal: Nullable<string>;\n\n    buffer: string[] | any;\n\n    defaultBuffer: Nullable<string>;\n\n    focusText: Nullable<string>;\n\n    caretTimeoutId: any;\n\n    androidChrome: boolean = true;\n\n    focused: Nullable<boolean>;\n\n    _variant: 'filled' | 'outlined' = 'outlined';\n\n    get inputClass() {\n        return {\n            'p-inputmask': true\n        };\n    }\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, public el: ElementRef, public cd: ChangeDetectorRef, public config: PrimeNGConfig) {}\n\n    ngOnInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            let ua = navigator.userAgent;\n            this.androidChrome = /chrome/i.test(ua) && /android/i.test(ua);\n        }\n\n        this.initMask();\n    }\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    initMask() {\n        this.tests = [];\n        this.partialPosition = (this.mask as string).length;\n        this.len = (this.mask as string).length;\n        this.firstNonMaskPos = null;\n        this.defs = {\n            '9': '[0-9]',\n            a: this.characterPattern,\n            '*': `${this.characterPattern}|[0-9]`\n        };\n\n        let maskTokens = (this.mask as string).split('');\n        for (let i = 0; i < maskTokens.length; i++) {\n            let c = maskTokens[i];\n            if (c == '?') {\n                this.len--;\n                this.partialPosition = i;\n            } else if (this.defs[c]) {\n                this.tests.push(new RegExp(this.defs[c]));\n                if (this.firstNonMaskPos === null) {\n                    this.firstNonMaskPos = this.tests.length - 1;\n                }\n                if (i < this.partialPosition) {\n                    this.lastRequiredNonMaskPos = this.tests.length - 1;\n                }\n            } else {\n                this.tests.push(null);\n            }\n        }\n\n        this.buffer = [];\n        for (let i = 0; i < maskTokens.length; i++) {\n            let c = maskTokens[i];\n            if (c != '?') {\n                if (this.defs[c]) this.buffer.push(this.getPlaceholder(i));\n                else this.buffer.push(c);\n            }\n        }\n        this.defaultBuffer = this.buffer.join('');\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n\n        if (this.inputViewChild && this.inputViewChild.nativeElement) {\n            if (this.value == undefined || this.value == null) this.inputViewChild.nativeElement.value = '';\n            else this.inputViewChild.nativeElement.value = this.value;\n\n            this.checkVal();\n            this.focusText = this.inputViewChild.nativeElement.value;\n            this.updateFilledState();\n        }\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    caret(first?: number, last?: number): Caret | undefined {\n        let range, begin, end;\n\n        if (!this.inputViewChild?.nativeElement.offsetParent || this.inputViewChild.nativeElement !== this.inputViewChild.nativeElement.ownerDocument.activeElement) {\n            return;\n        }\n\n        if (typeof first == 'number') {\n            begin = first;\n            end = typeof last === 'number' ? last : begin;\n            if (this.inputViewChild.nativeElement.setSelectionRange) {\n                this.inputViewChild.nativeElement.setSelectionRange(begin, end);\n            } else if (this.inputViewChild.nativeElement['createTextRange']) {\n                range = this.inputViewChild.nativeElement['createTextRange']();\n                range.collapse(true);\n                range.moveEnd('character', end);\n                range.moveStart('character', begin);\n                range.select();\n            }\n        } else {\n            if (this.inputViewChild.nativeElement.setSelectionRange) {\n                begin = this.inputViewChild.nativeElement.selectionStart;\n                end = this.inputViewChild.nativeElement.selectionEnd;\n            } else if ((this.document as any['selection']) && (this.document as any)['selection'].createRange) {\n                range = (this.document as any['selection']).createRange();\n                begin = 0 - range.duplicate().moveStart('character', -100000);\n                end = begin + range.text.length;\n            }\n\n            return { begin: begin, end: end };\n        }\n    }\n\n    isCompleted(): boolean {\n        let completed: boolean;\n        for (let i = this.firstNonMaskPos as number; i <= (this.lastRequiredNonMaskPos as number); i++) {\n            if (this.tests[i] && (this.buffer as string[])[i] === this.getPlaceholder(i)) {\n                return false;\n            }\n        }\n\n        return true;\n    }\n\n    getPlaceholder(i: number) {\n        if (i < this.slotChar.length) {\n            return this.slotChar.charAt(i);\n        }\n        return this.slotChar.charAt(0);\n    }\n\n    seekNext(pos: number) {\n        while (++pos < (this.len as number) && !this.tests[pos]);\n        return pos;\n    }\n\n    seekPrev(pos: number) {\n        while (--pos >= 0 && !this.tests[pos]);\n        return pos;\n    }\n\n    shiftL(begin: number, end: number) {\n        let i, j;\n\n        if (begin < 0) {\n            return;\n        }\n\n        for (i = begin, j = this.seekNext(end); i < (this.len as number); i++) {\n            if (this.tests[i]) {\n                if (j < (this.len as number) && this.tests[i].test(this.buffer[j])) {\n                    this.buffer[i] = this.buffer[j];\n                    this.buffer[j] = this.getPlaceholder(j);\n                } else {\n                    break;\n                }\n\n                j = this.seekNext(j);\n            }\n        }\n        this.writeBuffer();\n        this.caret(Math.max(this.firstNonMaskPos as number, begin));\n    }\n\n    shiftR(pos: number) {\n        let i, c, j, t;\n\n        for (i = pos, c = this.getPlaceholder(pos); i < (this.len as number); i++) {\n            if (this.tests[i]) {\n                j = this.seekNext(i);\n                t = this.buffer[i];\n                this.buffer[i] = c;\n                if (j < (this.len as number) && this.tests[j].test(t)) {\n                    c = t;\n                } else {\n                    break;\n                }\n            }\n        }\n    }\n\n    handleAndroidInput(e: Event) {\n        var curVal = this.inputViewChild?.nativeElement.value;\n        var pos = this.caret() as Caret;\n        if (this.oldVal && this.oldVal.length && this.oldVal.length > curVal.length) {\n            // a deletion or backspace happened\n            this.checkVal(true);\n            while (pos.begin > 0 && !this.tests[pos.begin - 1]) pos.begin--;\n            if (pos.begin === 0) {\n                while (pos.begin < (this.firstNonMaskPos as number) && !this.tests[pos.begin]) pos.begin++;\n            }\n\n            setTimeout(() => {\n                this.caret(pos.begin, pos.begin);\n                this.updateModel(e);\n                if (this.isCompleted()) {\n                    this.onComplete.emit();\n                }\n            }, 0);\n        } else {\n            this.checkVal(true);\n            while (pos.begin < (this.len as number) && !this.tests[pos.begin]) pos.begin++;\n\n            setTimeout(() => {\n                this.caret(pos.begin, pos.begin);\n                this.updateModel(e);\n                if (this.isCompleted()) {\n                    this.onComplete.emit();\n                }\n            }, 0);\n        }\n    }\n\n    onInputBlur(e: Event) {\n        this.focused = false;\n        this.onModelTouched();\n        if (!this.keepBuffer) {\n            this.checkVal();\n        }\n        this.updateFilledState();\n        this.onBlur.emit(e);\n\n        if (this.inputViewChild?.nativeElement.value != this.focusText || this.inputViewChild?.nativeElement.value != this.value) {\n            this.updateModel(e);\n            let event = this.document.createEvent('HTMLEvents');\n            event.initEvent('change', true, false);\n            this.inputViewChild?.nativeElement.dispatchEvent(event);\n        }\n    }\n\n    onInputKeydown(e: KeyboardEvent) {\n        if (this.readonly) {\n            return;\n        }\n\n        let k = e.which || e.keyCode,\n            pos,\n            begin,\n            end;\n        let iPhone;\n        if (isPlatformBrowser(this.platformId)) {\n            iPhone = /iphone/i.test(DomHandler.getUserAgent());\n        }\n        this.oldVal = this.inputViewChild?.nativeElement.value;\n\n        this.onKeydown.emit(e);\n\n        //backspace, delete, and escape get special treatment\n        if (k === 8 || k === 46 || (iPhone && k === 127)) {\n            pos = this.caret() as Caret;\n            begin = pos.begin;\n            end = pos.end;\n\n            if (end - begin === 0) {\n                begin = k !== 46 ? this.seekPrev(begin) : (end = this.seekNext(begin - 1));\n                end = k === 46 ? this.seekNext(end) : end;\n            }\n\n            this.clearBuffer(begin, end);\n            if (this.keepBuffer) {\n                this.shiftL(begin, end - 2);\n            } else {\n                this.shiftL(begin, end - 1);\n            }\n            this.updateModel(e);\n            this.onInput.emit(e);\n\n            e.preventDefault();\n        } else if (k === 13) {\n            // enter\n            this.onInputBlur(e);\n            this.updateModel(e);\n        } else if (k === 27) {\n            // escape\n            (this.inputViewChild as ElementRef).nativeElement.value = this.focusText;\n            this.caret(0, this.checkVal());\n            this.updateModel(e);\n\n            e.preventDefault();\n        }\n    }\n\n    onKeyPress(e: KeyboardEvent) {\n        if (this.readonly) {\n            return;\n        }\n\n        var k = e.which || e.keyCode,\n            pos = this.caret() as Caret,\n            p: number,\n            c: string,\n            next: number,\n            completed!: boolean;\n\n        if (e.ctrlKey || e.altKey || e.metaKey || k < 32 || (k > 34 && k < 41)) {\n            //Ignore\n            return;\n        } else if (k && k !== 13) {\n            if (pos.end - pos.begin !== 0) {\n                this.clearBuffer(pos.begin, pos.end);\n                this.shiftL(pos.begin, pos.end - 1);\n            }\n\n            p = this.seekNext(pos.begin - 1);\n            if (p < (this.len as number)) {\n                c = String.fromCharCode(k);\n                if (this.tests[p].test(c)) {\n                    this.shiftR(p);\n\n                    this.buffer[p] = c;\n                    this.writeBuffer();\n                    next = this.seekNext(p);\n\n                    if (DomHandler.isClient() && /android/i.test(DomHandler.getUserAgent())) {\n                        let proxy = () => {\n                            this.caret(next);\n                        };\n\n                        setTimeout(proxy, 0);\n                    } else {\n                        this.caret(next);\n                    }\n\n                    if (pos.begin <= (this.lastRequiredNonMaskPos as number)) {\n                        completed = this.isCompleted();\n                    }\n\n                    this.onInput.emit(e);\n                }\n            }\n            e.preventDefault();\n        }\n\n        this.updateModel(e);\n\n        this.updateFilledState();\n\n        if (completed) {\n            this.onComplete.emit();\n        }\n    }\n\n    clearBuffer(start: number, end: number) {\n        if (!this.keepBuffer) {\n            let i;\n            for (i = start; i < end && i < (this.len as number); i++) {\n                if (this.tests[i]) {\n                    this.buffer[i] = this.getPlaceholder(i);\n                }\n            }\n        }\n    }\n\n    writeBuffer() {\n        (this.inputViewChild as ElementRef).nativeElement.value = this.buffer.join('');\n    }\n\n    checkVal(allow?: boolean): number {\n        //try to place characters where they belong\n        let test = this.inputViewChild?.nativeElement.value,\n            lastMatch = -1,\n            i,\n            c,\n            pos;\n\n        for (i = 0, pos = 0; i < (this.len as number); i++) {\n            if (this.tests[i]) {\n                this.buffer[i] = this.getPlaceholder(i);\n                while (pos++ < test.length) {\n                    c = test.charAt(pos - 1);\n                    if (this.tests[i].test(c)) {\n                        if (!this.keepBuffer) {\n                            this.buffer[i] = c;\n                        }\n                        lastMatch = i;\n                        break;\n                    }\n                }\n                if (pos > test.length) {\n                    this.clearBuffer(i + 1, this.len as number);\n                    break;\n                }\n            } else {\n                if (this.buffer[i] === test.charAt(pos)) {\n                    pos++;\n                }\n                if (i < (this.partialPosition as number)) {\n                    lastMatch = i;\n                }\n            }\n        }\n        if (allow) {\n            this.writeBuffer();\n        } else if (lastMatch + 1 < (this.partialPosition as number)) {\n            if (this.autoClear || this.buffer.join('') === this.defaultBuffer) {\n                // Invalid value. Remove it and replace it with the\n                // mask, which is the default behavior.\n                if (this.inputViewChild?.nativeElement.value) this.inputViewChild.nativeElement.value = '';\n                this.clearBuffer(0, this.len as number);\n            } else {\n                // Invalid value, but we opt to show the value to the\n                // user and allow them to correct their mistake.\n                this.writeBuffer();\n            }\n        } else {\n            this.writeBuffer();\n            (this.inputViewChild as ElementRef).nativeElement.value = this.inputViewChild?.nativeElement.value.substring(0, lastMatch + 1);\n        }\n        return (this.partialPosition ? i : this.firstNonMaskPos) as number;\n    }\n\n    onInputFocus(event: Event) {\n        if (this.readonly) {\n            return;\n        }\n\n        this.focused = true;\n\n        clearTimeout(this.caretTimeoutId);\n        let pos: number;\n\n        this.focusText = this.inputViewChild?.nativeElement.value;\n\n        pos = this.keepBuffer ? this.inputViewChild?.nativeElement.value.length : this.checkVal();\n\n        this.caretTimeoutId = setTimeout(() => {\n            if (this.inputViewChild?.nativeElement !== this.inputViewChild?.nativeElement.ownerDocument.activeElement) {\n                return;\n            }\n            this.writeBuffer();\n            if (pos == this.mask?.replace('?', '').length) {\n                this.caret(0, pos);\n            } else {\n                this.caret(pos);\n            }\n        }, 10);\n\n        this.onFocus.emit(event);\n    }\n\n    onInputChange(event: Event) {\n        if (this.androidChrome) this.handleAndroidInput(event);\n        else this.handleInputChange(event);\n\n        this.onInput.emit(event);\n    }\n\n    handleInputChange(event: Event) {\n        if (this.readonly || this.disabled) {\n            return;\n        }\n\n        setTimeout(() => {\n            var pos = this.checkVal(true);\n            this.caret(pos);\n            this.updateModel(event);\n            if (this.isCompleted()) {\n                this.onComplete.emit();\n            }\n        }, 0);\n    }\n\n    getUnmaskedValue() {\n        let unmaskedBuffer = [];\n        for (let i = 0; i < this.buffer.length; i++) {\n            let c = this.buffer[i];\n            if (this.tests[i] && c != this.getPlaceholder(i)) {\n                unmaskedBuffer.push(c);\n            }\n        }\n\n        return unmaskedBuffer.join('');\n    }\n\n    updateModel(e: Event) {\n        const updatedValue = this.unmask ? this.getUnmaskedValue() : (e.target as HTMLInputElement).value;\n        if (updatedValue !== null || updatedValue !== undefined) {\n            this.value = updatedValue;\n            this.onModelChange(this.value);\n        }\n    }\n\n    updateFilledState() {\n        this.filled = this.inputViewChild?.nativeElement && this.inputViewChild.nativeElement.value != '';\n    }\n\n    focus() {\n        this.inputViewChild?.nativeElement.focus();\n    }\n\n    clear() {\n        (this.inputViewChild as ElementRef).nativeElement.value = '';\n        this.value = null;\n        this.onModelChange(this.value);\n        this.onClear.emit();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, InputTextModule, AutoFocusModule, TimesIcon],\n    exports: [InputMask, SharedModule],\n    declarations: [InputMask]\n})\nexport class InputMaskModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BE;AAgCW,MAAA,wBAAwB,GAAQ;AACzC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,SAAS,CAAC;AACxC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MAuDU,SAAS,CAAA;AA4OoB,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AA3OpK;;;AAGG;IACM,IAAI,GAAW,MAAM,CAAC;AAC/B;;;AAGG;IACM,QAAQ,GAAW,GAAG,CAAC;AAChC;;;AAGG;IACqC,SAAS,GAAY,IAAI,CAAC;AAClE;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACoC,IAAA,IAAI,CAAqB;AAChE;;;AAGG;AACoC,IAAA,SAAS,CAAqB;AACrE;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,MAAM,CAAsB;AACpE;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACM,gBAAgB,GAAW,UAAU,CAAC;AAC/C;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;IACH,IAA4C,SAAS,CAAC,KAA0B,EAAA;AAC5E,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;KAC5E;AACD;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;IACqC,UAAU,GAAY,KAAK,CAAC;AACpE;;;AAGG;AACH,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;IACD,IAAI,IAAI,CAAC,GAA8B,EAAA;AACnC,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QAEjB,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChB,QAAA,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;AACpB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAClC;AACD;;;AAGG;AACO,IAAA,UAAU,GAAsB,IAAI,YAAY,EAAO,CAAC;AAClE;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,SAAS,GAAwB,IAAI,YAAY,EAAS,CAAC;AACrE;;;AAGG;AACO,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAO,CAAC;AAEzB,IAAA,cAAc,CAAuB;AAE3C,IAAA,SAAS,CAA4B;AAErE,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,KAAK,CAAmB;AAExB,IAAA,KAAK,CAAmB;AAExB,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,KAAK,CAA6B;AAElC,IAAA,MAAM,CAAoB;AAE1B,IAAA,IAAI,CAAqC;AAEzC,IAAA,KAAK,CAAiB;AAEtB,IAAA,eAAe,CAAmB;AAElC,IAAA,eAAe,CAAmB;AAElC,IAAA,sBAAsB,CAAmB;AAEzC,IAAA,GAAG,CAAmB;AAEtB,IAAA,MAAM,CAAmB;AAEzB,IAAA,MAAM,CAAiB;AAEvB,IAAA,aAAa,CAAmB;AAEhC,IAAA,SAAS,CAAmB;AAE5B,IAAA,cAAc,CAAM;IAEpB,aAAa,GAAY,IAAI,CAAC;AAE9B,IAAA,OAAO,CAAoB;IAE3B,QAAQ,GAA0B,UAAU,CAAC;AAE7C,IAAA,IAAI,UAAU,GAAA;QACV,OAAO;AACH,YAAA,aAAa,EAAE,IAAI;SACtB,CAAC;KACL;IAED,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAS,EAAc,EAAS,EAAqB,EAAS,MAAqB,EAAA;QAAnJ,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAE7L,QAAQ,GAAA;AACJ,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClE,SAAA;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;KACnB;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,eAAe,GAAI,IAAI,CAAC,IAAe,CAAC,MAAM,CAAC;QACpD,IAAI,CAAC,GAAG,GAAI,IAAI,CAAC,IAAe,CAAC,MAAM,CAAC;AACxC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG;AACR,YAAA,GAAG,EAAE,OAAO;YACZ,CAAC,EAAE,IAAI,CAAC,gBAAgB;AACxB,YAAA,GAAG,EAAE,CAAA,EAAG,IAAI,CAAC,gBAAgB,CAAQ,MAAA,CAAA;SACxC,CAAC;QAEF,IAAI,UAAU,GAAI,IAAI,CAAC,IAAe,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACjD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,IAAI,GAAG,EAAE;gBACV,IAAI,CAAC,GAAG,EAAE,CAAC;AACX,gBAAA,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAC5B,aAAA;AAAM,iBAAA,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACrB,gBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,gBAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;oBAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAChD,iBAAA;AACD,gBAAA,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE;oBAC1B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACvD,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzB,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACjB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,IAAI,GAAG,EAAE;AACV,gBAAA,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAAE,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;;AACtD,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,aAAA;AACJ,SAAA;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC7C;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;YAC1D,IAAI,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI;gBAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;;gBAC3F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAE1D,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC;YACzD,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5B,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,KAAK,CAAC,KAAc,EAAE,IAAa,EAAA;AAC/B,QAAA,IAAI,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC;QAEtB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,KAAK,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,EAAE;YACzJ,OAAO;AACV,SAAA;AAED,QAAA,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;YAC1B,KAAK,GAAG,KAAK,CAAC;AACd,YAAA,GAAG,GAAG,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AAC9C,YAAA,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,iBAAiB,EAAE;gBACrD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACnE,aAAA;iBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,EAAE;gBAC7D,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,EAAE,CAAC;AAC/D,gBAAA,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrB,gBAAA,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AAChC,gBAAA,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBACpC,KAAK,CAAC,MAAM,EAAE,CAAC;AAClB,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,iBAAiB,EAAE;gBACrD,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC;gBACzD,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,YAAY,CAAC;AACxD,aAAA;AAAM,iBAAA,IAAK,IAAI,CAAC,QAA6B,IAAK,IAAI,CAAC,QAAgB,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE;AAC/F,gBAAA,KAAK,GAAI,IAAI,CAAC,QAA6B,CAAC,WAAW,EAAE,CAAC;AAC1D,gBAAA,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC9D,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;AACnC,aAAA;YAED,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrC,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,SAAkB,CAAC;AACvB,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,eAAyB,EAAE,CAAC,IAAK,IAAI,CAAC,sBAAiC,EAAE,CAAC,EAAE,EAAE;YAC5F,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAK,IAAI,CAAC,MAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;AAC1E,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf;AAED,IAAA,cAAc,CAAC,CAAS,EAAA;AACpB,QAAA,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,SAAA;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAClC;AAED,IAAA,QAAQ,CAAC,GAAW,EAAA;AAChB,QAAA,OAAO,EAAE,GAAG,GAAI,IAAI,CAAC,GAAc,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAAC,CAAC;AACzD,QAAA,OAAO,GAAG,CAAC;KACd;AAED,IAAA,QAAQ,CAAC,GAAW,EAAA;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAAC,CAAC;AACvC,QAAA,OAAO,GAAG,CAAC;KACd;IAED,MAAM,CAAC,KAAa,EAAE,GAAW,EAAA;QAC7B,IAAI,CAAC,EAAE,CAAC,CAAC;QAET,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,OAAO;AACV,SAAA;QAED,KAAK,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,GAAI,IAAI,CAAC,GAAc,EAAE,CAAC,EAAE,EAAE;AACnE,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBACf,IAAI,CAAC,GAAI,IAAI,CAAC,GAAc,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;AAChE,oBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChC,oBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAC3C,iBAAA;AAAM,qBAAA;oBACH,MAAM;AACT,iBAAA;AAED,gBAAA,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACxB,aAAA;AACJ,SAAA;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAyB,EAAE,KAAK,CAAC,CAAC,CAAC;KAC/D;AAED,IAAA,MAAM,CAAC,GAAW,EAAA;AACd,QAAA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEf,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,GAAI,IAAI,CAAC,GAAc,EAAE,CAAC,EAAE,EAAE;AACvE,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AACf,gBAAA,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACrB,gBAAA,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,gBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACnB,gBAAA,IAAI,CAAC,GAAI,IAAI,CAAC,GAAc,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;oBACnD,CAAC,GAAG,CAAC,CAAC;AACT,iBAAA;AAAM,qBAAA;oBACH,MAAM;AACT,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,kBAAkB,CAAC,CAAQ,EAAA;QACvB,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC;AACtD,QAAA,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,EAAW,CAAC;AAChC,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE;;AAEzE,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpB,YAAA,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;gBAAE,GAAG,CAAC,KAAK,EAAE,CAAC;AAChE,YAAA,IAAI,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;AACjB,gBAAA,OAAO,GAAG,CAAC,KAAK,GAAI,IAAI,CAAC,eAA0B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;oBAAE,GAAG,CAAC,KAAK,EAAE,CAAC;AAC9F,aAAA;YAED,UAAU,CAAC,MAAK;gBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AACjC,gBAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACpB,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACpB,oBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AAC1B,iBAAA;aACJ,EAAE,CAAC,CAAC,CAAC;AACT,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpB,YAAA,OAAO,GAAG,CAAC,KAAK,GAAI,IAAI,CAAC,GAAc,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;gBAAE,GAAG,CAAC,KAAK,EAAE,CAAC;YAE/E,UAAU,CAAC,MAAK;gBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AACjC,gBAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACpB,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACpB,oBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AAC1B,iBAAA;aACJ,EAAE,CAAC,CAAC,CAAC;AACT,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,CAAQ,EAAA;AAChB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,SAAA;QACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEpB,IAAI,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE;AACtH,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YACpD,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3D,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,CAAgB,EAAA;QAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,EACxB,GAAG,EACH,KAAK,EACL,GAAG,CAAC;AACR,QAAA,IAAI,MAAM,CAAC;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC;AACtD,SAAA;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC;AAEvD,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;AAGvB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;AAC9C,YAAA,GAAG,GAAG,IAAI,CAAC,KAAK,EAAW,CAAC;AAC5B,YAAA,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AAClB,YAAA,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AAEd,YAAA,IAAI,GAAG,GAAG,KAAK,KAAK,CAAC,EAAE;AACnB,gBAAA,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3E,gBAAA,GAAG,GAAG,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC7C,aAAA;AAED,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAC7B,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC/B,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC/B,aAAA;AACD,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACpB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAErB,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;aAAM,IAAI,CAAC,KAAK,EAAE,EAAE;;AAEjB,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACvB,SAAA;aAAM,IAAI,CAAC,KAAK,EAAE,EAAE;;YAEhB,IAAI,CAAC,cAA6B,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;YACzE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAEpB,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,CAAgB,EAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;QAED,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,EACxB,GAAG,GAAG,IAAI,CAAC,KAAK,EAAW,EAC3B,CAAS,EACT,CAAS,EACT,IAAY,EACZ,SAAmB,CAAC;QAExB,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE;;YAEpE,OAAO;AACV,SAAA;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;YACtB,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACvC,aAAA;YAED,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACjC,YAAA,IAAI,CAAC,GAAI,IAAI,CAAC,GAAc,EAAE;AAC1B,gBAAA,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACvB,oBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAEf,oBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACnB,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,oBAAA,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAExB,oBAAA,IAAI,UAAU,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,EAAE;wBACrE,IAAI,KAAK,GAAG,MAAK;AACb,4BAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACrB,yBAAC,CAAC;AAEF,wBAAA,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACxB,qBAAA;AAAM,yBAAA;AACH,wBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACpB,qBAAA;AAED,oBAAA,IAAI,GAAG,CAAC,KAAK,IAAK,IAAI,CAAC,sBAAiC,EAAE;AACtD,wBAAA,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAClC,qBAAA;AAED,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,iBAAA;AACJ,aAAA;YACD,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAEzB,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,WAAW,CAAC,KAAa,EAAE,GAAW,EAAA;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAClB,YAAA,IAAI,CAAC,CAAC;AACN,YAAA,KAAK,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAI,IAAI,CAAC,GAAc,EAAE,CAAC,EAAE,EAAE;AACtD,gBAAA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AACf,oBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAC3C,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;AACN,QAAA,IAAI,CAAC,cAA6B,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAClF;AAED,IAAA,QAAQ,CAAC,KAAe,EAAA;;QAEpB,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,EAC/C,SAAS,GAAG,CAAC,CAAC,EACd,CAAC,EACD,CAAC,EACD,GAAG,CAAC;AAER,QAAA,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAI,IAAI,CAAC,GAAc,EAAE,CAAC,EAAE,EAAE;AAChD,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AACf,gBAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACxC,gBAAA,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE;oBACxB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;oBACzB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACvB,wBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAClB,4BAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB,yBAAA;wBACD,SAAS,GAAG,CAAC,CAAC;wBACd,MAAM;AACT,qBAAA;AACJ,iBAAA;AACD,gBAAA,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;oBACnB,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAa,CAAC,CAAC;oBAC5C,MAAM;AACT,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AACrC,oBAAA,GAAG,EAAE,CAAC;AACT,iBAAA;AACD,gBAAA,IAAI,CAAC,GAAI,IAAI,CAAC,eAA0B,EAAE;oBACtC,SAAS,GAAG,CAAC,CAAC;AACjB,iBAAA;AACJ,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;AAAM,aAAA,IAAI,SAAS,GAAG,CAAC,GAAI,IAAI,CAAC,eAA0B,EAAE;AACzD,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE;;;AAG/D,gBAAA,IAAI,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK;oBAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC3F,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,GAAa,CAAC,CAAC;AAC3C,aAAA;AAAM,iBAAA;;;gBAGH,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;YAClB,IAAI,CAAC,cAA6B,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;AAClI,SAAA;AACD,QAAA,QAAQ,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,EAAY;KACtE;AAED,IAAA,YAAY,CAAC,KAAY,EAAA;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAEpB,QAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAClC,QAAA,IAAI,GAAW,CAAC;QAEhB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC;QAE1D,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAE1F,QAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,MAAK;AAClC,YAAA,IAAI,IAAI,CAAC,cAAc,EAAE,aAAa,KAAK,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,aAAa,CAAC,aAAa,EAAE;gBACvG,OAAO;AACV,aAAA;YACD,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,YAAA,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;AAC3C,gBAAA,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACnB,aAAA;SACJ,EAAE,EAAE,CAAC,CAAC;AAEP,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,aAAa,CAAC,KAAY,EAAA;QACtB,IAAI,IAAI,CAAC,aAAa;AAAE,YAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;;AAClD,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAEnC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,iBAAiB,CAAC,KAAY,EAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,OAAO;AACV,SAAA;QAED,UAAU,CAAC,MAAK;YACZ,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAChB,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACxB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACpB,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AAC1B,aAAA;SACJ,EAAE,CAAC,CAAC,CAAC;KACT;IAED,gBAAgB,GAAA;QACZ,IAAI,cAAc,GAAG,EAAE,CAAC;AACxB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACvB,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;AAC9C,gBAAA,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAClC;AAED,IAAA,WAAW,CAAC,CAAQ,EAAA;QAChB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAI,CAAC,CAAC,MAA2B,CAAC,KAAK,CAAC;AAClG,QAAA,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,SAAS,EAAE;AACrD,YAAA,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;AAC1B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,SAAA;KACJ;IAED,iBAAiB,GAAA;AACb,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;KACrG;IAED,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC;KAC9C;IAED,KAAK,GAAA;QACA,IAAI,CAAC,cAA6B,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AAC7D,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACvB;uGAnuBQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA4OE,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA5OpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EAeE,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAKhB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAyBhB,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,CAKf,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,eAAe,CA8Bf,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAKhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,sCAKhB,gBAAgB,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAKhB,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAUhB,gBAAgB,CAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAUhB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAMhB,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAahB,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,QAAA,EAAA,4BAAA,EAAA,SAAA,EAAA,6BAAA,EAAA,wBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EA3IzB,CAAC,wBAAwB,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA+LpB,aAAa,EA9OpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,gJAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAmvByD,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAvuB1D,SAAS,EAAA,UAAA,EAAA,CAAA;kBAtDrB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACb,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwCT,EACK,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AAClB,wBAAA,+BAA+B,EAAE,QAAQ;AACzC,wBAAA,8BAA8B,EAAE,SAAS;AACzC,wBAAA,+BAA+B,EAAE,wBAAwB;qBAC5D,EACU,SAAA,EAAA,CAAC,wBAAwB,CAAC,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,gJAAA,CAAA,EAAA,CAAA;;0BA+OxB,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;8HAvOpE,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKiC,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAMM,SAAS,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAQ7B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,IAAI,EAAA,CAAA;sBAAhB,KAAK;gBAcI,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAKG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAE+B,cAAc,EAAA,CAAA;sBAAnD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAA;gBAEJ,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAijBrB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EA3uBf,YAAA,EAAA,CAAA,SAAS,CAuuBR,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,CAvuB1D,EAAA,OAAA,EAAA,CAAA,SAAS,EAwuBG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGxB,eAAe,EAAA,OAAA,EAAA,CAJd,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,EAC9C,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGxB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,CAAC;AACpE,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;oBAClC,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;ACn2BD;;AAEG;;;;"}