/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { setInjectImplementation } from '../di/inject_switch';
import { formatRuntimeError, RuntimeError } from '../errors';
import { setInjectorProfilerContext } from './debug/injector_profiler';
import { getFactoryDef } from './definition_factory';
import { NodeInjector, setIncludeViewProviders } from './di';
import { store, ɵɵdirectiveInject } from './instructions/all';
import { isHostComponentStandalone } from './instructions/element_validation';
import { CONTEXT, DECLARATION_COMPONENT_VIEW, HEADER_OFFSET, TVIEW } from './interfaces/view';
import { pureFunction1Internal, pureFunction2Internal, pureFunction3Internal, pureFunction4Internal, pureFunctionVInternal, } from './pure_function';
import { getBindingRoot, getCurrentTNode, getLView, getTView } from './state';
import { load } from './util/view_utils';
/**
 * Create a pipe.
 *
 * @param index Pipe index where the pipe will be stored.
 * @param pipeName The name of the pipe
 * @returns T the instance of the pipe.
 *
 * @codeGenApi
 */
export function ɵɵpipe(index, pipeName) {
    const tView = getTView();
    let pipeDef;
    const adjustedIndex = index + HEADER_OFFSET;
    if (tView.firstCreatePass) {
        // The `getPipeDef` throws if a pipe with a given name is not found
        // (so we use non-null assertion below).
        pipeDef = getPipeDef(pipeName, tView.pipeRegistry);
        tView.data[adjustedIndex] = pipeDef;
        if (pipeDef.onDestroy) {
            (tView.destroyHooks ??= []).push(adjustedIndex, pipeDef.onDestroy);
        }
    }
    else {
        pipeDef = tView.data[adjustedIndex];
    }
    const pipeFactory = pipeDef.factory || (pipeDef.factory = getFactoryDef(pipeDef.type, true));
    let previousInjectorProfilerContext;
    if (ngDevMode) {
        previousInjectorProfilerContext = setInjectorProfilerContext({
            injector: new NodeInjector(getCurrentTNode(), getLView()),
            token: pipeDef.type,
        });
    }
    const previousInjectImplementation = setInjectImplementation(ɵɵdirectiveInject);
    try {
        // DI for pipes is supposed to behave like directives when placed on a component
        // host node, which means that we have to disable access to `viewProviders`.
        const previousIncludeViewProviders = setIncludeViewProviders(false);
        const pipeInstance = pipeFactory();
        setIncludeViewProviders(previousIncludeViewProviders);
        store(tView, getLView(), adjustedIndex, pipeInstance);
        return pipeInstance;
    }
    finally {
        // we have to restore the injector implementation in finally, just in case the creation of the
        // pipe throws an error.
        setInjectImplementation(previousInjectImplementation);
        ngDevMode && setInjectorProfilerContext(previousInjectorProfilerContext);
    }
}
/**
 * Searches the pipe registry for a pipe with the given name. If one is found,
 * returns the pipe. Otherwise, an error is thrown because the pipe cannot be resolved.
 *
 * @param name Name of pipe to resolve
 * @param registry Full list of available pipes
 * @returns Matching PipeDef
 */
function getPipeDef(name, registry) {
    if (registry) {
        if (ngDevMode) {
            const pipes = registry.filter((pipe) => pipe.name === name);
            // TODO: Throw an error in the next major
            if (pipes.length > 1) {
                console.warn(formatRuntimeError(313 /* RuntimeErrorCode.MULTIPLE_MATCHING_PIPES */, getMultipleMatchingPipesMessage(name)));
            }
        }
        for (let i = registry.length - 1; i >= 0; i--) {
            const pipeDef = registry[i];
            if (name === pipeDef.name) {
                return pipeDef;
            }
        }
    }
    if (ngDevMode) {
        throw new RuntimeError(-302 /* RuntimeErrorCode.PIPE_NOT_FOUND */, getPipeNotFoundErrorMessage(name));
    }
    return;
}
/**
 * Generates a helpful error message for the user when multiple pipes match the name.
 *
 * @param name Name of the pipe
 * @returns The error message
 */
function getMultipleMatchingPipesMessage(name) {
    const lView = getLView();
    const declarationLView = lView[DECLARATION_COMPONENT_VIEW];
    const context = declarationLView[CONTEXT];
    const hostIsStandalone = isHostComponentStandalone(lView);
    const componentInfoMessage = context ? ` in the '${context.constructor.name}' component` : '';
    const verifyMessage = `check ${hostIsStandalone ? "'@Component.imports' of this component" : 'the imports of this module'}`;
    const errorMessage = `Multiple pipes match the name \`${name}\`${componentInfoMessage}. ${verifyMessage}`;
    return errorMessage;
}
/**
 * Generates a helpful error message for the user when a pipe is not found.
 *
 * @param name Name of the missing pipe
 * @returns The error message
 */
function getPipeNotFoundErrorMessage(name) {
    const lView = getLView();
    const declarationLView = lView[DECLARATION_COMPONENT_VIEW];
    const context = declarationLView[CONTEXT];
    const hostIsStandalone = isHostComponentStandalone(lView);
    const componentInfoMessage = context ? ` in the '${context.constructor.name}' component` : '';
    const verifyMessage = `Verify that it is ${hostIsStandalone
        ? "included in the '@Component.imports' of this component"
        : 'declared or imported in this module'}`;
    const errorMessage = `The pipe '${name}' could not be found${componentInfoMessage}. ${verifyMessage}`;
    return errorMessage;
}
/**
 * Invokes a pipe with 1 arguments.
 *
 * This instruction acts as a guard to {@link PipeTransform#transform} invoking
 * the pipe only when an input to the pipe changes.
 *
 * @param index Pipe index where the pipe was stored on creation.
 * @param offset the binding offset
 * @param v1 1st argument to {@link PipeTransform#transform}.
 *
 * @codeGenApi
 */
export function ɵɵpipeBind1(index, offset, v1) {
    const adjustedIndex = index + HEADER_OFFSET;
    const lView = getLView();
    const pipeInstance = load(lView, adjustedIndex);
    return isPure(lView, adjustedIndex)
        ? pureFunction1Internal(lView, getBindingRoot(), offset, pipeInstance.transform, v1, pipeInstance)
        : pipeInstance.transform(v1);
}
/**
 * Invokes a pipe with 2 arguments.
 *
 * This instruction acts as a guard to {@link PipeTransform#transform} invoking
 * the pipe only when an input to the pipe changes.
 *
 * @param index Pipe index where the pipe was stored on creation.
 * @param slotOffset the offset in the reserved slot space
 * @param v1 1st argument to {@link PipeTransform#transform}.
 * @param v2 2nd argument to {@link PipeTransform#transform}.
 *
 * @codeGenApi
 */
export function ɵɵpipeBind2(index, slotOffset, v1, v2) {
    const adjustedIndex = index + HEADER_OFFSET;
    const lView = getLView();
    const pipeInstance = load(lView, adjustedIndex);
    return isPure(lView, adjustedIndex)
        ? pureFunction2Internal(lView, getBindingRoot(), slotOffset, pipeInstance.transform, v1, v2, pipeInstance)
        : pipeInstance.transform(v1, v2);
}
/**
 * Invokes a pipe with 3 arguments.
 *
 * This instruction acts as a guard to {@link PipeTransform#transform} invoking
 * the pipe only when an input to the pipe changes.
 *
 * @param index Pipe index where the pipe was stored on creation.
 * @param slotOffset the offset in the reserved slot space
 * @param v1 1st argument to {@link PipeTransform#transform}.
 * @param v2 2nd argument to {@link PipeTransform#transform}.
 * @param v3 4rd argument to {@link PipeTransform#transform}.
 *
 * @codeGenApi
 */
export function ɵɵpipeBind3(index, slotOffset, v1, v2, v3) {
    const adjustedIndex = index + HEADER_OFFSET;
    const lView = getLView();
    const pipeInstance = load(lView, adjustedIndex);
    return isPure(lView, adjustedIndex)
        ? pureFunction3Internal(lView, getBindingRoot(), slotOffset, pipeInstance.transform, v1, v2, v3, pipeInstance)
        : pipeInstance.transform(v1, v2, v3);
}
/**
 * Invokes a pipe with 4 arguments.
 *
 * This instruction acts as a guard to {@link PipeTransform#transform} invoking
 * the pipe only when an input to the pipe changes.
 *
 * @param index Pipe index where the pipe was stored on creation.
 * @param slotOffset the offset in the reserved slot space
 * @param v1 1st argument to {@link PipeTransform#transform}.
 * @param v2 2nd argument to {@link PipeTransform#transform}.
 * @param v3 3rd argument to {@link PipeTransform#transform}.
 * @param v4 4th argument to {@link PipeTransform#transform}.
 *
 * @codeGenApi
 */
export function ɵɵpipeBind4(index, slotOffset, v1, v2, v3, v4) {
    const adjustedIndex = index + HEADER_OFFSET;
    const lView = getLView();
    const pipeInstance = load(lView, adjustedIndex);
    return isPure(lView, adjustedIndex)
        ? pureFunction4Internal(lView, getBindingRoot(), slotOffset, pipeInstance.transform, v1, v2, v3, v4, pipeInstance)
        : pipeInstance.transform(v1, v2, v3, v4);
}
/**
 * Invokes a pipe with variable number of arguments.
 *
 * This instruction acts as a guard to {@link PipeTransform#transform} invoking
 * the pipe only when an input to the pipe changes.
 *
 * @param index Pipe index where the pipe was stored on creation.
 * @param slotOffset the offset in the reserved slot space
 * @param values Array of arguments to pass to {@link PipeTransform#transform} method.
 *
 * @codeGenApi
 */
export function ɵɵpipeBindV(index, slotOffset, values) {
    const adjustedIndex = index + HEADER_OFFSET;
    const lView = getLView();
    const pipeInstance = load(lView, adjustedIndex);
    return isPure(lView, adjustedIndex)
        ? pureFunctionVInternal(lView, getBindingRoot(), slotOffset, pipeInstance.transform, values, pipeInstance)
        : pipeInstance.transform.apply(pipeInstance, values);
}
function isPure(lView, index) {
    return lView[TVIEW].data[index].pure;
}
//# sourceMappingURL=data:application/json;base64,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