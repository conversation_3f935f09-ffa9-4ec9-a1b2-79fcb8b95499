/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { isForwardRef, resolveForwardRef } from '../../di/forward_ref';
import { getComponentDef, getDirectiveDef, getNgModuleDef, getPipeDef } from '../definition';
import { stringifyForError } from '../util/stringify_utils';
export function isModuleWithProviders(value) {
    return value.ngModule !== undefined;
}
export function isNgModule(value) {
    return !!getNgModuleDef(value);
}
export function isPipe(value) {
    return !!getPipeDef(value);
}
export function isDirective(value) {
    return !!getDirectiveDef(value);
}
export function isComponent(value) {
    return !!getComponentDef(value);
}
function getDependencyTypeForError(type) {
    if (getComponentDef(type))
        return 'component';
    if (getDirectiveDef(type))
        return 'directive';
    if (getPipeDef(type))
        return 'pipe';
    return 'type';
}
export function verifyStandaloneImport(depType, importingType) {
    if (isForwardRef(depType)) {
        depType = resolveForwardRef(depType);
        if (!depType) {
            throw new Error(`Expected forwardRef function, imported from "${stringifyForError(importingType)}", to return a standalone entity or NgModule but got "${stringifyForError(depType) || depType}".`);
        }
    }
    if (getNgModuleDef(depType) == null) {
        const def = getComponentDef(depType) || getDirectiveDef(depType) || getPipeDef(depType);
        if (def != null) {
            // if a component, directive or pipe is imported make sure that it is standalone
            if (!def.standalone) {
                throw new Error(`The "${stringifyForError(depType)}" ${getDependencyTypeForError(depType)}, imported from "${stringifyForError(importingType)}", is not standalone. Did you forget to add the standalone: true flag?`);
            }
        }
        else {
            // it can be either a module with provider or an unknown (not annotated) type
            if (isModuleWithProviders(depType)) {
                throw new Error(`A module with providers was imported from "${stringifyForError(importingType)}". Modules with providers are not supported in standalone components imports.`);
            }
            else {
                throw new Error(`The "${stringifyForError(depType)}" type, imported from "${stringifyForError(importingType)}", must be a standalone component / directive / pipe or an NgModule. Did you forget to add the required @Component / @Directive / @Pipe or @NgModule annotation?`);
            }
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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