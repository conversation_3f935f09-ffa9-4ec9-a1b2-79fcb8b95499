{"version": 3, "file": "primeng-image.mjs", "sources": ["../../src/app/components/image/image.ts", "../../src/app/components/image/primeng-image.ts"], "sourcesContent": ["import { AnimationEvent, animate, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    HostListener,\n    Inject,\n    Input,\n    NgModule,\n    Output,\n    QueryList,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute\n} from '@angular/core';\nimport { SafeUrl } from '@angular/platform-browser';\nimport { PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { EyeIcon } from 'primeng/icons/eye';\nimport { RefreshIcon } from 'primeng/icons/refresh';\nimport { SearchMinusIcon } from 'primeng/icons/searchminus';\nimport { SearchPlusIcon } from 'primeng/icons/searchplus';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { UndoIcon } from 'primeng/icons/undo';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { FocusTrapModule } from 'primeng/focustrap';\n\n/**\n * Displays an image with preview and tranformation options. For multiple image, see Galleria.\n * @group Components\n */\n@Component({\n    selector: 'p-image',\n    template: `\n        <span [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <img [attr.src]=\"src\" [attr.srcset]=\"srcSet\" [attr.sizes]=\"sizes\" [attr.alt]=\"alt\" [attr.width]=\"width\" [attr.height]=\"height\" [attr.loading]=\"loading\" [ngStyle]=\"imageStyle\" [class]=\"imageClass\" (error)=\"imageError($event)\" />\n            <button *ngIf=\"preview\" [attr.aria-label]=\"zoomImageAriaLabel\" type=\"button\" class=\"p-image-preview-indicator\" (click)=\"onImageClick()\" #previewButton [ngStyle]=\"{ height: height + 'px', width: width + 'px', border: 'none' }\">\n                <ng-container *ngIf=\"indicatorTemplate; else defaultTemplate\">\n                    <ng-container *ngTemplateOutlet=\"indicatorTemplate\"></ng-container>\n                </ng-container>\n                <ng-template #defaultTemplate>\n                    <EyeIcon [styleClass]=\"'p-image-preview-icon'\" />\n                </ng-template>\n            </button>\n            <div #mask class=\"p-image-mask p-component-overlay p-component-overlay-enter\" *ngIf=\"maskVisible\" [attr.aria-modal]=\"maskVisible\" role=\"dialog\" (click)=\"onMaskClick()\" (keydown)=\"onMaskKeydown($event)\" pFocusTrap>\n                <div class=\"p-image-toolbar\" (click)=\"handleToolbarClick($event)\">\n                    <button class=\"p-image-action p-link\" (click)=\"rotateRight()\" type=\"button\" [attr.aria-label]=\"rightAriaLabel()\">\n                        <RefreshIcon *ngIf=\"!rotateRightIconTemplate\" />\n                        <ng-template *ngTemplateOutlet=\"rotateRightIconTemplate\"></ng-template>\n                    </button>\n                    <button class=\"p-image-action p-link\" (click)=\"rotateLeft()\" type=\"button\" [attr.aria-label]=\"leftAriaLabel()\">\n                        <UndoIcon *ngIf=\"!rotateLeftIconTemplate\" />\n                        <ng-template *ngTemplateOutlet=\"rotateLeftIconTemplate\"></ng-template>\n                    </button>\n                    <button class=\"p-image-action p-link\" (click)=\"zoomOut()\" type=\"button\" [disabled]=\"isZoomOutDisabled\" [attr.aria-label]=\"zoomOutAriaLabel()\">\n                        <SearchMinusIcon *ngIf=\"!zoomOutIconTemplate\" />\n                        <ng-template *ngTemplateOutlet=\"zoomOutIconTemplate\"></ng-template>\n                    </button>\n                    <button class=\"p-image-action p-link\" (click)=\"zoomIn()\" type=\"button\" [disabled]=\"isZoomInDisabled\" [attr.aria-label]=\"zoomInAriaLabel()\">\n                        <SearchPlusIcon *ngIf=\"!zoomInIconTemplate\" />\n                        <ng-template *ngTemplateOutlet=\"zoomInIconTemplate\"></ng-template>\n                    </button>\n                    <button class=\"p-image-action p-link\" type=\"button\" (click)=\"closePreview()\" [attr.aria-label]=\"closeAriaLabel()\" #closeButton>\n                        <TimesIcon *ngIf=\"!closeIconTemplate\" />\n                        <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                    </button>\n                </div>\n                <div\n                    *ngIf=\"previewVisible\"\n                    [@animation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n                    (@animation.start)=\"onAnimationStart($event)\"\n                    (@animation.done)=\"onAnimationEnd($event)\"\n                >\n                    <img [attr.src]=\"previewImageSrc ? previewImageSrc : src\" [attr.srcset]=\"previewImageSrcSet\" [attr.sizes]=\"previewImageSizes\" class=\"p-image-preview\" [ngStyle]=\"imagePreviewStyle()\" (click)=\"onPreviewImageClick()\" />\n                </div>\n            </div>\n        </span>\n    `,\n    animations: [\n        trigger('animation', [\n            transition('void => visible', [style({ transform: 'scale(0.7)', opacity: 0 }), animate('{{showTransitionParams}}')]),\n            transition('visible => void', [animate('{{hideTransitionParams}}', style({ transform: 'scale(0.7)', opacity: 0 }))])\n        ])\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./image.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Image implements AfterContentInit {\n    /**\n     * Style class of the image element.\n     * @group Props\n     */\n    @Input() imageClass: string | undefined;\n    /**\n     * Inline style of the image element.\n     * @group Props\n     */\n    @Input() imageStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * The source path for the main image.\n     * @group Props\n     */\n    @Input() src: string | SafeUrl | undefined;\n    /**\n     * The srcset definition for the main image.\n     * @group Props\n     */\n    @Input() srcSet: string | SafeUrl | undefined;\n    /**\n     * The sizes definition for the main image.\n     * @group Props\n     */\n    @Input() sizes: string | undefined;\n    /**\n     * The source path for the preview image.\n     * @group Props\n     */\n    @Input() previewImageSrc: string | SafeUrl | undefined;\n    /**\n     * The srcset definition for the preview image.\n     * @group Props\n     */\n    @Input() previewImageSrcSet: string | SafeUrl | undefined;\n    /**\n     * The sizes definition for the preview image.\n     * @group Props\n     */\n    @Input() previewImageSizes: string | undefined;\n    /**\n     * Attribute of the preview image element.\n     * @group Props\n     */\n    @Input() alt: string | undefined;\n    /**\n     * Attribute of the image element.\n     * @group Props\n     */\n    @Input() width: string | undefined;\n    /**\n     * Attribute of the image element.\n     * @group Props\n     */\n    @Input() height: string | undefined;\n    /**\n     * Attribute of the image element.\n     * @group Props\n     */\n    @Input() loading: 'lazy' | 'eager' | undefined;\n    /**\n     * Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Controls the preview functionality.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) preview: boolean = false;\n    /**\n     * Transition options of the show animation\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Triggered when the preview overlay is shown.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Triggered when the preview overlay is hidden.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * This event is triggered if an error occurs while loading an image file.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onImageError: EventEmitter<Event> = new EventEmitter<Event>();\n\n    @ViewChild('mask') mask: ElementRef | undefined;\n\n    @ViewChild('previewButton') previewButton: ElementRef | undefined;\n\n    @ViewChild('closeButton') closeButton: ElementRef | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    indicatorTemplate: TemplateRef<any> | undefined;\n\n    rotateRightIconTemplate: TemplateRef<any> | undefined;\n\n    rotateLeftIconTemplate: TemplateRef<any> | undefined;\n\n    zoomOutIconTemplate: TemplateRef<any> | undefined;\n\n    zoomInIconTemplate: TemplateRef<any> | undefined;\n\n    closeIconTemplate: TemplateRef<any> | undefined;\n\n    maskVisible: boolean = false;\n\n    previewVisible: boolean = false;\n\n    rotate: number = 0;\n\n    scale: number = 1;\n\n    previewClick: boolean = false;\n\n    container: Nullable<HTMLElement>;\n\n    wrapper: Nullable<HTMLElement>;\n\n    public get isZoomOutDisabled(): boolean {\n        return this.scale - this.zoomSettings.step <= this.zoomSettings.min;\n    }\n\n    public get isZoomInDisabled(): boolean {\n        return this.scale + this.zoomSettings.step >= this.zoomSettings.max;\n    }\n\n    private zoomSettings = {\n        default: 1,\n        step: 0.1,\n        max: 1.5,\n        min: 0.5\n    };\n\n    constructor(@Inject(DOCUMENT) private document: Document, private config: PrimeNGConfig, private cd: ChangeDetectorRef, public el: ElementRef) {}\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'indicator':\n                    this.indicatorTemplate = item.template;\n                    break;\n\n                case 'rotaterighticon':\n                    this.rotateRightIconTemplate = item.template;\n                    break;\n\n                case 'rotatelefticon':\n                    this.rotateLeftIconTemplate = item.template;\n                    break;\n\n                case 'zoomouticon':\n                    this.zoomOutIconTemplate = item.template;\n                    break;\n\n                case 'zoominicon':\n                    this.zoomInIconTemplate = item.template;\n                    break;\n\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.indicatorTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    onImageClick() {\n        if (this.preview) {\n            this.maskVisible = true;\n            this.previewVisible = true;\n            DomHandler.blockBodyScroll();\n        }\n    }\n\n    onMaskClick() {\n        if (!this.previewClick) {\n            this.closePreview();\n        }\n\n        this.previewClick = false;\n    }\n\n    onMaskKeydown(event) {\n        switch (event.code) {\n            case 'Escape':\n                this.onMaskClick();\n                setTimeout(() => {\n                    DomHandler.focus(this.previewButton.nativeElement);\n                }, 25);\n                event.preventDefault();\n\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onPreviewImageClick() {\n        this.previewClick = true;\n    }\n\n    rotateRight() {\n        this.rotate += 90;\n        this.previewClick = true;\n    }\n\n    rotateLeft() {\n        this.rotate -= 90;\n        this.previewClick = true;\n    }\n\n    zoomIn() {\n        this.scale = this.scale + this.zoomSettings.step;\n        this.previewClick = true;\n    }\n\n    zoomOut() {\n        this.scale = this.scale - this.zoomSettings.step;\n        this.previewClick = true;\n    }\n\n    onAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                this.container = event.element;\n                this.wrapper = this.container?.parentElement;\n                this.appendContainer();\n                this.moveOnTop();\n\n                setTimeout(() => {\n                    DomHandler.focus(this.closeButton.nativeElement);\n                }, 25);\n                break;\n\n            case 'void':\n                DomHandler.addClass(this.wrapper, 'p-component-overlay-leave');\n                break;\n        }\n    }\n\n    onAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                ZIndexUtils.clear(this.wrapper);\n                this.maskVisible = false;\n                this.container = null;\n                this.wrapper = null;\n                this.cd.markForCheck();\n                this.onHide.emit({});\n                break;\n            case 'visible':\n                this.onShow.emit({});\n                break;\n        }\n    }\n\n    moveOnTop() {\n        ZIndexUtils.set('modal', this.wrapper, this.config.zIndex.modal);\n    }\n\n    appendContainer() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body') this.document.body.appendChild(this.wrapper as HTMLElement);\n            else DomHandler.appendChild(this.wrapper, this.appendTo);\n        }\n    }\n\n    imagePreviewStyle() {\n        return { transform: 'rotate(' + this.rotate + 'deg) scale(' + this.scale + ')' };\n    }\n\n    get zoomImageAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.zoomImage : undefined;\n    }\n\n    containerClass() {\n        return {\n            'p-image p-component': true,\n            'p-image-preview-container': this.preview\n        };\n    }\n\n    handleToolbarClick(event: MouseEvent): void {\n        event.stopPropagation();\n    }\n\n    closePreview(): void {\n        this.previewVisible = false;\n        this.rotate = 0;\n        this.scale = this.zoomSettings.default;\n        DomHandler.unblockBodyScroll();\n    }\n\n    imageError(event: Event) {\n        this.onImageError.emit(event);\n    }\n\n    rightAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.rotateRight : undefined;\n    }\n\n    leftAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.rotateLeft : undefined;\n    }\n\n    zoomInAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.zoomIn : undefined;\n    }\n\n    zoomOutAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.zoomOut : undefined;\n    }\n\n    closeAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n    }\n\n    @HostListener('document:keydown.escape', ['$event']) onKeydownHandler(event: KeyboardEvent) {\n        if (this.previewVisible) {\n            this.closePreview();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, RefreshIcon, EyeIcon, UndoIcon, SearchMinusIcon, SearchPlusIcon, TimesIcon, FocusTrapModule],\n    exports: [Image, SharedModule],\n    declarations: [Image]\n})\nexport class ImageModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkCA;;;AAGG;MA6DU,KAAK,CAAA;AA6JwB,IAAA,QAAA,CAAA;AAA4B,IAAA,MAAA,CAAA;AAA+B,IAAA,EAAA,CAAA;AAA8B,IAAA,EAAA,CAAA;AA5J/H;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,GAAG,CAA+B;AAC3C;;;AAGG;AACM,IAAA,MAAM,CAA+B;AAC9C;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,eAAe,CAA+B;AACvD;;;AAGG;AACM,IAAA,kBAAkB,CAA+B;AAC1D;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACM,IAAA,GAAG,CAAqB;AACjC;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACM,IAAA,OAAO,CAA+B;AAC/C;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;IACqC,OAAO,GAAY,KAAK,CAAC;AACjE;;;AAGG;IACM,qBAAqB,GAAW,kCAAkC,CAAC;AAC5E;;;AAGG;IACM,qBAAqB,GAAW,kCAAkC,CAAC;AAC5E;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;;AAIG;AACO,IAAA,YAAY,GAAwB,IAAI,YAAY,EAAS,CAAC;AAErD,IAAA,IAAI,CAAyB;AAEpB,IAAA,aAAa,CAAyB;AAExC,IAAA,WAAW,CAAyB;AAE9B,IAAA,SAAS,CAAuC;AAEhF,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,uBAAuB,CAA+B;AAEtD,IAAA,sBAAsB,CAA+B;AAErD,IAAA,mBAAmB,CAA+B;AAElD,IAAA,kBAAkB,CAA+B;AAEjD,IAAA,iBAAiB,CAA+B;IAEhD,WAAW,GAAY,KAAK,CAAC;IAE7B,cAAc,GAAY,KAAK,CAAC;IAEhC,MAAM,GAAW,CAAC,CAAC;IAEnB,KAAK,GAAW,CAAC,CAAC;IAElB,YAAY,GAAY,KAAK,CAAC;AAE9B,IAAA,SAAS,CAAwB;AAEjC,IAAA,OAAO,CAAwB;AAE/B,IAAA,IAAW,iBAAiB,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;KACvE;AAED,IAAA,IAAW,gBAAgB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;KACvE;AAEO,IAAA,YAAY,GAAG;AACnB,QAAA,OAAO,EAAE,CAAC;AACV,QAAA,IAAI,EAAE,GAAG;AACT,QAAA,GAAG,EAAE,GAAG;AACR,QAAA,GAAG,EAAE,GAAG;KACX,CAAC;AAEF,IAAA,WAAA,CAAsC,QAAkB,EAAU,MAAqB,EAAU,EAAqB,EAAS,EAAc,EAAA;QAAvG,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAU,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;IAEjJ,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,iBAAiB;AAClB,oBAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC7C,MAAM;AAEV,gBAAA,KAAK,gBAAgB;AACjB,oBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC5C,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,UAAU,CAAC,eAAe,EAAE,CAAC;AAChC,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;KAC7B;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;QACf,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,QAAQ;gBACT,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,UAAU,CAAC,MAAK;oBACZ,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;iBACtD,EAAE,EAAE,CAAC,CAAC;gBACP,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;IAED,mBAAmB,GAAA;AACf,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;KAC5B;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;KAC5B;IAED,UAAU,GAAA;AACN,QAAA,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;KAC5B;IAED,MAAM,GAAA;AACF,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACjD,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;KAC5B;IAED,OAAO,GAAA;AACH,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;AACjD,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,KAAqB,EAAA;QAClC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC;gBAC7C,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,SAAS,EAAE,CAAC;gBAEjB,UAAU,CAAC,MAAK;oBACZ,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;iBACpD,EAAE,EAAE,CAAC,CAAC;gBACP,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAAC;gBAC/D,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAqB,EAAA;QAChC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;AACP,gBAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,gBAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,gBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AACvB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrB,MAAM;AACV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrB,MAAM;AACb,SAAA;KACJ;IAED,SAAS,GAAA;AACL,QAAA,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACpE;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;gBAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAsB,CAAC,CAAC;;gBACrF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5D,SAAA;KACJ;IAED,iBAAiB,GAAA;AACb,QAAA,OAAO,EAAE,SAAS,EAAE,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;KACpF;AAED,IAAA,IAAI,kBAAkB,GAAA;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5F;IAED,cAAc,GAAA;QACV,OAAO;AACH,YAAA,qBAAqB,EAAE,IAAI;YAC3B,2BAA2B,EAAE,IAAI,CAAC,OAAO;SAC5C,CAAC;KACL;AAED,IAAA,kBAAkB,CAAC,KAAiB,EAAA;QAChC,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;IAED,YAAY,GAAA;AACR,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;QACvC,UAAU,CAAC,iBAAiB,EAAE,CAAC;KAClC;AAED,IAAA,UAAU,CAAC,KAAY,EAAA;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACjC;IAED,cAAc,GAAA;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;KAC9F;IAED,aAAa,GAAA;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7F;IAED,eAAe,GAAA;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;KACzF;IAED,gBAAgB,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;KAC1F;IAED,cAAc,GAAA;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;KACxF;AAEoD,IAAA,gBAAgB,CAAC,KAAoB,EAAA;QACtF,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;KACJ;AA7VQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAK,kBA6JM,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA7JnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,KAAK,EAgFM,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,GAAA,EAAA,KAAA,EAAA,MAAA,EAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,gBAAgB,CAkCnB,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,0BAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA5KpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,eAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4CT,EA+WqC,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,u1BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,WAAW,CAAE,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,OAAO,CAAE,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,QAAQ,CAAE,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,CAAE,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,cAAc,CAAE,EAAA,QAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,SAAS,CA9WpG,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,WAAW,EAAE;gBACjB,UAAU,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBACpH,UAAU,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACvH,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,KAAK,EAAA,UAAA,EAAA,CAAA;kBA5DjB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,SAAS,EACT,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4CT,EACW,UAAA,EAAA;wBACR,OAAO,CAAC,WAAW,EAAE;4BACjB,UAAU,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;4BACpH,UAAU,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;yBACvH,CAAC;AACL,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,u1BAAA,CAAA,EAAA,CAAA;;0BA+JY,MAAM;2BAAC,QAAQ,CAAA;8HAxJnB,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKI,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAEY,IAAI,EAAA,CAAA;sBAAtB,SAAS;uBAAC,MAAM,CAAA;gBAEW,aAAa,EAAA,CAAA;sBAAxC,SAAS;uBAAC,eAAe,CAAA;gBAEA,WAAW,EAAA,CAAA;sBAApC,SAAS;uBAAC,aAAa,CAAA;gBAEQ,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAuOuB,gBAAgB,EAAA,CAAA;sBAApE,YAAY;uBAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAY1C,WAAW,CAAA;uGAAX,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAX,WAAW,EAAA,YAAA,EAAA,CArWX,KAAK,CAiWJ,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,CAAA,EAAA,OAAA,EAAA,CAjWxH,KAAK,EAkWG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGpB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,YAJV,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAChH,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGpB,WAAW,EAAA,UAAA,EAAA,CAAA;kBALvB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,CAAC;AAClI,oBAAA,OAAO,EAAE,CAAC,KAAK,EAAE,YAAY,CAAC;oBAC9B,YAAY,EAAE,CAAC,KAAK,CAAC;AACxB,iBAAA,CAAA;;;ACtcD;;AAEG;;;;"}