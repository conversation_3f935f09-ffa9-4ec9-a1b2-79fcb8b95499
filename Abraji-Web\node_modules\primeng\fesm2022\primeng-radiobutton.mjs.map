{"version": 3, "file": "primeng-radiobutton.mjs", "sources": ["../../src/app/components/radiobutton/radiobutton.ts", "../../src/app/components/radiobutton/primeng-radiobutton.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Injectable, Injector, Input, NgModule, OnDestroy, OnInit, Output, ViewChild, booleanAttribute, forwardRef, numberAttribute } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR, NgControl } from '@angular/forms';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { AutoFocusModule } from 'primeng/autofocus';\n\nimport { RadioButtonClickEvent } from './radiobutton.interface';\nimport { PrimeNGConfig } from 'primeng/api';\n\nexport const RADIO_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => RadioButton),\n    multi: true\n};\n\n@Injectable({\n    providedIn: 'root'\n})\nexport class RadioControlRegistry {\n    private accessors: any[] = [];\n\n    add(control: NgControl, accessor: RadioButton) {\n        this.accessors.push([control, accessor]);\n    }\n\n    remove(accessor: RadioButton) {\n        this.accessors = this.accessors.filter((c) => {\n            return c[1] !== accessor;\n        });\n    }\n\n    select(accessor: RadioButton) {\n        this.accessors.forEach((c) => {\n            if (this.isSameGroup(c, accessor) && c[1] !== accessor) {\n                c[1].writeValue(accessor.value);\n            }\n        });\n    }\n\n    private isSameGroup(controlPair: [NgControl, RadioButton], accessor: RadioButton): boolean {\n        if (!controlPair[0].control) {\n            return false;\n        }\n\n        return controlPair[0].control.root === (accessor as any).control.control.root && controlPair[1].name === accessor.name;\n    }\n}\n/**\n * RadioButton is an extension to standard radio button element with theming.\n * @group Components\n */\n@Component({\n    selector: 'p-radioButton',\n    template: `\n        <div\n            [ngStyle]=\"style\"\n            [ngClass]=\"{\n                'p-radiobutton p-component': true,\n                'p-radiobutton-checked': checked,\n                'p-radiobutton-disabled': disabled,\n                'p-radiobutton-focused': focused,\n                'p-variant-filled': variant === 'filled' || config.inputStyle() === 'filled'\n            }\"\n            [class]=\"styleClass\"\n            [attr.data-pc-name]=\"'radiobutton'\"\n            [attr.data-pc-section]=\"'root'\"\n            (click)=\"handleClick($event, input, true)\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"radio\"\n                    [attr.name]=\"name\"\n                    [checked]=\"checked\"\n                    [disabled]=\"disabled\"\n                    [value]=\"value\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.tabindex]=\"tabindex\"\n                    [attr.aria-checked]=\"checked\"\n                    (focus)=\"onInputFocus($event)\"\n                    (blur)=\"onInputBlur($event)\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                />\n            </div>\n            <div [ngClass]=\"{ 'p-radiobutton-box': true, 'p-highlight': checked, 'p-disabled': disabled, 'p-focus': focused }\" [attr.data-pc-section]=\"'input'\">\n                <span class=\"p-radiobutton-icon\" [attr.data-pc-section]=\"'icon'\"></span>\n            </div>\n        </div>\n        <label\n            (click)=\"select($event)\"\n            [class]=\"labelStyleClass\"\n            [ngClass]=\"{ 'p-radiobutton-label': true, 'p-radiobutton-label-active': input.checked, 'p-disabled': disabled, 'p-radiobutton-label-focus': focused }\"\n            *ngIf=\"label\"\n            [attr.for]=\"inputId\"\n            [attr.data-pc-section]=\"'label'\"\n            >{{ label }}</label\n        >\n    `,\n    providers: [RADIO_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class RadioButton implements ControlValueAccessor, OnInit, OnDestroy {\n    /**\n     * Value of the radiobutton.\n     * @group Props\n     */\n    @Input() value: any;\n    /**\n     * The name of the form control.\n     * @group Props\n     */\n    @Input() formControlName: string | undefined;\n    /**\n     * Name of the radiobutton group.\n     * @group Props\n     */\n    @Input() name: string | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Label of the radiobutton.\n     * @group Props\n     */\n    @Input() label: string | undefined;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Used to define a string that labels the input element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Style class of the label.\n     * @group Props\n     */\n    @Input() labelStyleClass: string | undefined;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Callback to invoke on radio button click.\n     * @param {RadioButtonClickEvent} event - Custom click event.\n     * @group Emits\n     */\n    @Output() onClick: EventEmitter<RadioButtonClickEvent> = new EventEmitter<RadioButtonClickEvent>();\n    /**\n     * Callback to invoke when the receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when the loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n\n    @ViewChild('input') inputViewChild!: ElementRef;\n\n    public onModelChange: Function = () => {};\n\n    public onModelTouched: Function = () => {};\n\n    public checked: Nullable<boolean>;\n\n    public focused: Nullable<boolean>;\n\n    control: Nullable<NgControl>;\n\n    constructor(public cd: ChangeDetectorRef, private injector: Injector, private registry: RadioControlRegistry, public config: PrimeNGConfig) {}\n\n    ngOnInit() {\n        this.control = this.injector.get(NgControl);\n        this.checkName();\n        this.registry.add(this.control, this);\n    }\n\n    handleClick(event: Event, radioButton: HTMLElement, focus: boolean) {\n        event.preventDefault();\n\n        if (this.disabled) {\n            return;\n        }\n\n        this.select(event);\n\n        if (focus) {\n            radioButton.focus();\n        }\n    }\n\n    select(event: Event) {\n        if (!this.disabled) {\n            this.inputViewChild.nativeElement.checked = true;\n            this.checked = true;\n            this.onModelChange(this.value);\n            this.registry.select(this);\n            this.onClick.emit({ originalEvent: event, value: this.value });\n        }\n    }\n\n    writeValue(value: any): void {\n        this.checked = value == this.value;\n\n        if (this.inputViewChild && this.inputViewChild.nativeElement) {\n            this.inputViewChild.nativeElement.checked = this.checked;\n        }\n\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    onInputFocus(event: Event) {\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event: Event) {\n        this.focused = false;\n        this.onModelTouched();\n        this.onBlur.emit(event);\n    }\n\n    /**\n     * Applies focus to input field.\n     * @group Method\n     */\n    public focus() {\n        this.inputViewChild.nativeElement.focus();\n    }\n\n    ngOnDestroy() {\n        this.registry.remove(this);\n    }\n\n    private checkName() {\n        if (this.name && this.formControlName && this.name !== this.formControlName) {\n            this.throwNameError();\n        }\n        if (!this.name && this.formControlName) {\n            this.name = this.formControlName;\n        }\n    }\n\n    private throwNameError() {\n        throw new Error(`\n          If you define both a name and a formControlName attribute on your radio button, their values\n          must match. Ex: <p-radioButton formControlName=\"food\" name=\"food\"></p-radioButton>\n        `);\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, AutoFocusModule],\n    exports: [RadioButton],\n    declarations: [RadioButton]\n})\nexport class RadioButtonModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;AASa,MAAA,oBAAoB,GAAQ;AACrC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,WAAW,CAAC;AAC1C,IAAA,KAAK,EAAE,IAAI;EACb;MAKW,oBAAoB,CAAA;IACrB,SAAS,GAAU,EAAE,CAAC;IAE9B,GAAG,CAAC,OAAkB,EAAE,QAAqB,EAAA;QACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC5C;AAED,IAAA,MAAM,CAAC,QAAqB,EAAA;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAI;AACzC,YAAA,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC7B,SAAC,CAAC,CAAC;KACN;AAED,IAAA,MAAM,CAAC,QAAqB,EAAA;QACxB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI;AACzB,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnC,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAEO,WAAW,CAAC,WAAqC,EAAE,QAAqB,EAAA;AAC5E,QAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;AACzB,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,KAAM,QAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC;KAC1H;uGA3BQ,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAApB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,cAFjB,MAAM,EAAA,CAAA,CAAA;;2FAET,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAHhC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,UAAU,EAAE,MAAM;AACrB,iBAAA,CAAA;;AA8BD;;;AAGG;MA0DU,WAAW,CAAA;AAsGD,IAAA,EAAA,CAAA;AAA+B,IAAA,QAAA,CAAA;AAA4B,IAAA,QAAA,CAAA;AAAuC,IAAA,MAAA,CAAA;AArGrH;;;AAGG;AACM,IAAA,KAAK,CAAM;AACpB;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AACpE;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;AACO,IAAA,OAAO,GAAwC,IAAI,YAAY,EAAyB,CAAC;AACnG;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAE9C,IAAA,cAAc,CAAc;AAEzC,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,OAAO,CAAoB;AAE3B,IAAA,OAAO,CAAoB;AAElC,IAAA,OAAO,CAAsB;AAE7B,IAAA,WAAA,CAAmB,EAAqB,EAAU,QAAkB,EAAU,QAA8B,EAAS,MAAqB,EAAA;QAAvH,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAE9I,QAAQ,GAAA;QACJ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KACzC;AAED,IAAA,WAAW,CAAC,KAAY,EAAE,WAAwB,EAAE,KAAc,EAAA;QAC9D,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAEnB,QAAA,IAAI,KAAK,EAAE;YACP,WAAW,CAAC,KAAK,EAAE,CAAC;AACvB,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAY,EAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC;AACjD,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AAClE,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;QACjB,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;QAEnC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;YAC1D,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5D,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAY,EAAA;AACrB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED;;;AAGG;IACI,KAAK,GAAA;AACR,QAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;KAC7C;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC9B;IAEO,SAAS,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE;YACzE,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,SAAA;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;AACpC,SAAA;KACJ;IAEO,cAAc,GAAA;QAClB,MAAM,IAAI,KAAK,CAAC,CAAA;;;AAGf,QAAA,CAAA,CAAC,CAAC;KACN;uGAlMQ,WAAW,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,oBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAX,WAAW,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAoBA,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAehB,eAAe,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAmCf,gBAAgB,CA5EzB,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,oBAAoB,CAAC,EAjDvB,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA,CAAA;;2FAOQ,WAAW,EAAA,UAAA,EAAA,CAAA;kBAzDvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDT,IAAA,CAAA;oBACD,SAAS,EAAE,CAAC,oBAAoB,CAAC;oBACjC,eAAe,EAAE,uBAAuB,CAAC,MAAM;AAC/C,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;yKAMY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEa,cAAc,EAAA,CAAA;sBAAjC,SAAS;uBAAC,OAAO,CAAA;;MAgHT,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,iBA1MjB,WAAW,CAAA,EAAA,OAAA,EAAA,CAsMV,YAAY,EAAE,eAAe,aAtM9B,WAAW,CAAA,EAAA,CAAA,CAAA;wGA0MX,iBAAiB,EAAA,OAAA,EAAA,CAJhB,YAAY,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA;;2FAI9B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC;oBACxC,OAAO,EAAE,CAAC,WAAW,CAAC;oBACtB,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;ACrTD;;AAEG;;;;"}