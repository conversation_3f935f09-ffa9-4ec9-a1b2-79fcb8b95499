import { Component, OnInit } from '@angular/core';
import { ToastService } from './toast.service';

@Component({
  selector: 'app-toast',
  templateUrl: './toast.component.html',
  styleUrls: ['./toast.component.scss']
})
export class ToastComponent implements OnInit {
  toasts: Toast[] = [];

  constructor(private toastService: ToastService) {}

  ngOnInit() {
    this.toastService.toasts$.subscribe(toasts => this.toasts = toasts);
  }

  removeToast(id: number) {
    this.toastService.removeToast(id);
  }
}

interface Toast {
  type: 'success' | 'warn' | 'error';
  title: string;
  body: string;
  id: number;
}
