/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createContentQuery, createViewQuery } from '../query';
import { bindQueryToSignal } from '../query_reactive';
import { getCurrentQueryIndex, setCurrentQueryIndex } from '../state';
/**
 * Creates a new content query and binds it to a signal created by an authoring function.
 *
 * @param directiveIndex Current directive index
 * @param target The target signal to which the query should be bound
 * @param predicate The type for which the query will search
 * @param flags Flags associated with the query
 * @param read What to save in the query
 *
 * @codeGenApi
 */
export function ɵɵcontentQuerySignal(directiveIndex, target, predicate, flags, read) {
    bindQueryToSignal(target, createContentQuery(directiveIndex, predicate, flags, read));
}
/**
 * Creates a new view query by initializing internal data structures and binding a new query to the
 * target signal.
 *
 * @param target The target signal to assign the query results to.
 * @param predicate The type or label that should match a given query
 * @param flags Flags associated with the query
 * @param read What to save in the query
 *
 * @codeGenApi
 */
export function ɵɵviewQuerySignal(target, predicate, flags, read) {
    bindQueryToSignal(target, createViewQuery(predicate, flags, read));
}
/**
 * Advances the current query index by a specified offset.
 *
 * Adjusting the current query index is necessary in cases where a given directive has a mix of
 * zone-based and signal-based queries. The signal-based queries don't require tracking of the
 * current index (those are refreshed on demand and not during change detection) so this instruction
 * is only necessary for backward-compatibility.
 *
 * @param index offset to apply to the current query index (defaults to 1)
 *
 * @codeGenApi
 */
export function ɵɵqueryAdvance(indexOffset = 1) {
    setCurrentQueryIndex(getCurrentQueryIndex() + indexOffset);
}
//# sourceMappingURL=data:application/json;base64,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