/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertNumber, assertNumberInRange } from '../../util/assert';
export function toTStylingRange(prev, next) {
    ngDevMode && assertNumberInRange(prev, 0, 32767 /* StylingRange.UNSIGNED_MASK */);
    ngDevMode && assertNumberInRange(next, 0, 32767 /* StylingRange.UNSIGNED_MASK */);
    return ((prev << 17 /* StylingRange.PREV_SHIFT */) | (next << 2 /* StylingRange.NEXT_SHIFT */));
}
export function getTStylingRangePrev(tStylingRange) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    return (tStylingRange >> 17 /* StylingRange.PREV_SHIFT */) & 32767 /* StylingRange.UNSIGNED_MASK */;
}
export function getTStylingRangePrevDuplicate(tStylingRange) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    return (tStylingRange & 2 /* StylingRange.PREV_DUPLICATE */) == 2 /* StylingRange.PREV_DUPLICATE */;
}
export function setTStylingRangePrev(tStylingRange, previous) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    ngDevMode && assertNumberInRange(previous, 0, 32767 /* StylingRange.UNSIGNED_MASK */);
    return ((tStylingRange & ~4294836224 /* StylingRange.PREV_MASK */) |
        (previous << 17 /* StylingRange.PREV_SHIFT */));
}
export function setTStylingRangePrevDuplicate(tStylingRange) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    return (tStylingRange | 2 /* StylingRange.PREV_DUPLICATE */);
}
export function getTStylingRangeNext(tStylingRange) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    return (tStylingRange & 131068 /* StylingRange.NEXT_MASK */) >> 2 /* StylingRange.NEXT_SHIFT */;
}
export function setTStylingRangeNext(tStylingRange, next) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    ngDevMode && assertNumberInRange(next, 0, 32767 /* StylingRange.UNSIGNED_MASK */);
    return ((tStylingRange & ~131068 /* StylingRange.NEXT_MASK */) | //
        (next << 2 /* StylingRange.NEXT_SHIFT */));
}
export function getTStylingRangeNextDuplicate(tStylingRange) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    return (tStylingRange & 1 /* StylingRange.NEXT_DUPLICATE */) === 1 /* StylingRange.NEXT_DUPLICATE */;
}
export function setTStylingRangeNextDuplicate(tStylingRange) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    return (tStylingRange | 1 /* StylingRange.NEXT_DUPLICATE */);
}
export function getTStylingRangeTail(tStylingRange) {
    ngDevMode && assertNumber(tStylingRange, 'expected number');
    const next = getTStylingRangeNext(tStylingRange);
    return next === 0 ? getTStylingRangePrev(tStylingRange) : next;
}
//# sourceMappingURL=data:application/json;base64,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