/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @module
 * @description
 * Entry point for all public APIs of the core/testing package.
 */
export * from './async';
export { ComponentFixture } from './component_fixture';
export { resetFakeAsyncZone, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, tick, } from './fake_async';
export { TestBed, getTestBed, inject, InjectSetupWrapper, withModule, } from './test_bed';
export { TestComponentRenderer, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, } from './test_bed_common';
export * from './test_hooks';
export * from './metadata_override';
export { MetadataOverrider as ɵMetadataOverrider } from './metadata_overrider';
export { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState, } from '@angular/core';
export { DeferBlockFixture } from './defer';
//# sourceMappingURL=data:application/json;base64,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