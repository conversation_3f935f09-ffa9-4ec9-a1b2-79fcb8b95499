{"version": 3, "file": "primeng-tooltip.mjs", "sources": ["../../src/app/components/tooltip/tooltip.ts", "../../src/app/components/tooltip/primeng-tooltip.ts"], "sourcesContent": ["import { CommonModule, isPlatformBrowser } from '@angular/common';\nimport { AfterViewInit, Directive, ElementRef, HostListener, Inject, Input, NgModule, NgZone, OnDestroy, PLATFORM_ID, Renderer2, SimpleChanges, TemplateRef, ViewContainerRef, booleanAttribute, numberAttribute } from '@angular/core';\nimport { PrimeNGConfig, TooltipOptions } from 'primeng/api';\nimport { ConnectedOverlayScrollHandler, DomHandler } from 'primeng/dom';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\n\n/**\n * Tooltip directive provides advisory information for a component.\n * @group Components\n */\n@Directive({\n    selector: '[pTooltip]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Tooltip implements AfterViewInit, OnDestroy {\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    @Input() tooltipPosition: 'right' | 'left' | 'top' | 'bottom' | string | undefined;\n    /**\n     * Event to show the tooltip.\n     * @group Props\n     */\n    @Input() tooltipEvent: 'hover' | 'focus' | 'both' | string | any = 'hover';\n    /**\n     *  Target element to attach the overlay, valid values are \"body\", \"target\" or a local ng-F variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    @Input() positionStyle: string | undefined;\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    @Input() tooltipStyleClass: string | undefined;\n    /**\n     * Whether the z-index should be managed automatically to always go on top or have a fixed value.\n     * @group Props\n     */\n    @Input() tooltipZIndex: string | undefined;\n    /**\n     * By default the tooltip contents are rendered as text. Set to false to support html tags in the content.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) escape: boolean = true;\n    /**\n     * Delay to show the tooltip in milliseconds.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) showDelay: number | undefined;\n    /**\n     * Delay to hide the tooltip in milliseconds.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) hideDelay: number | undefined;\n    /**\n     * Time to wait in milliseconds to hide the tooltip even it is active.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) life: number | undefined;\n    /**\n     * Specifies the additional vertical offset of the tooltip from its default position.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) positionTop: number | undefined;\n    /**\n     * Specifies the additional horizontal offset of the tooltip from its default position.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) positionLeft: number | undefined;\n    /**\n     * Whether to hide tooltip when hovering over tooltip content.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoHide: boolean = true;\n    /**\n     * Automatically adjusts the element position when there is not enough space on the selected position.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) fitContent: boolean = true;\n    /**\n     * Whether to hide tooltip on escape key press.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) hideOnEscape: boolean = true;\n    /**\n     * Content of the tooltip.\n     * @group Props\n     */\n    @Input('pTooltip') content: string | TemplateRef<HTMLElement> | undefined;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @defaultValue false\n     * @group Props\n     */\n    @Input('tooltipDisabled') get disabled(): boolean {\n        return this._disabled as boolean;\n    }\n    set disabled(val: boolean) {\n        this._disabled = val;\n        this.deactivate();\n    }\n    /**\n     * Specifies the tooltip configuration options for the component.\n     * @group Props\n     */\n    @Input() tooltipOptions: TooltipOptions | undefined;\n\n    _tooltipOptions = {\n        tooltipLabel: null,\n        tooltipPosition: 'right',\n        tooltipEvent: 'hover',\n        appendTo: 'body',\n        positionStyle: null,\n        tooltipStyleClass: null,\n        tooltipZIndex: 'auto',\n        escape: true,\n        disabled: null,\n        showDelay: null,\n        hideDelay: null,\n        positionTop: null,\n        positionLeft: null,\n        life: null,\n        autoHide: true,\n        hideOnEscape: true,\n        id: UniqueComponentId() + '_tooltip'\n    };\n\n    _disabled: boolean | undefined;\n\n    container: any;\n\n    styleClass: string | undefined;\n\n    tooltipText: any;\n\n    showTimeout: any;\n\n    hideTimeout: any;\n\n    active: boolean | undefined;\n\n    mouseEnterListener: Nullable<Function>;\n\n    mouseLeaveListener: Nullable<Function>;\n\n    containerMouseleaveListener: Nullable<Function>;\n\n    clickListener: Nullable<Function>;\n\n    focusListener: Nullable<Function>;\n\n    blurListener: Nullable<Function>;\n\n    scrollHandler: any;\n\n    resizeListener: any;\n\n    interactionInProgress = false;\n\n    constructor(@Inject(PLATFORM_ID) private platformId: any, public el: ElementRef, public zone: NgZone, public config: PrimeNGConfig, private renderer: Renderer2, private viewContainer: ViewContainerRef) {}\n\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.zone.runOutsideAngular(() => {\n                const tooltipEvent = this.getOption('tooltipEvent');\n\n                if (tooltipEvent === 'hover' || tooltipEvent === 'both') {\n                    this.mouseEnterListener = this.onMouseEnter.bind(this);\n                    this.mouseLeaveListener = this.onMouseLeave.bind(this);\n                    this.clickListener = this.onInputClick.bind(this);\n                    this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n                    this.el.nativeElement.addEventListener('click', this.clickListener);\n                    this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n                }\n                if (tooltipEvent === 'focus' || tooltipEvent === 'both') {\n                    this.focusListener = this.onFocus.bind(this);\n                    this.blurListener = this.onBlur.bind(this);\n\n                    let target = this.el.nativeElement.querySelector('.p-component');\n\n                    if (!target) {\n                        target = this.getTarget(this.el.nativeElement);\n                    }\n\n                    target.addEventListener('focus', this.focusListener);\n                    target.addEventListener('blur', this.blurListener);\n                }\n            });\n        }\n    }\n\n    ngOnChanges(simpleChange: SimpleChanges) {\n        if (simpleChange.tooltipPosition) {\n            this.setOption({ tooltipPosition: simpleChange.tooltipPosition.currentValue });\n        }\n\n        if (simpleChange.tooltipEvent) {\n            this.setOption({ tooltipEvent: simpleChange.tooltipEvent.currentValue });\n        }\n\n        if (simpleChange.appendTo) {\n            this.setOption({ appendTo: simpleChange.appendTo.currentValue });\n        }\n\n        if (simpleChange.positionStyle) {\n            this.setOption({ positionStyle: simpleChange.positionStyle.currentValue });\n        }\n\n        if (simpleChange.tooltipStyleClass) {\n            this.setOption({ tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue });\n        }\n\n        if (simpleChange.tooltipZIndex) {\n            this.setOption({ tooltipZIndex: simpleChange.tooltipZIndex.currentValue });\n        }\n\n        if (simpleChange.escape) {\n            this.setOption({ escape: simpleChange.escape.currentValue });\n        }\n\n        if (simpleChange.showDelay) {\n            this.setOption({ showDelay: simpleChange.showDelay.currentValue });\n        }\n\n        if (simpleChange.hideDelay) {\n            this.setOption({ hideDelay: simpleChange.hideDelay.currentValue });\n        }\n\n        if (simpleChange.life) {\n            this.setOption({ life: simpleChange.life.currentValue });\n        }\n\n        if (simpleChange.positionTop) {\n            this.setOption({ positionTop: simpleChange.positionTop.currentValue });\n        }\n\n        if (simpleChange.positionLeft) {\n            this.setOption({ positionLeft: simpleChange.positionLeft.currentValue });\n        }\n\n        if (simpleChange.disabled) {\n            this.setOption({ disabled: simpleChange.disabled.currentValue });\n        }\n\n        if (simpleChange.content) {\n            this.setOption({ tooltipLabel: simpleChange.content.currentValue });\n\n            if (this.active) {\n                if (simpleChange.content.currentValue) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    } else {\n                        this.show();\n                    }\n                } else {\n                    this.hide();\n                }\n            }\n        }\n\n        if (simpleChange.autoHide) {\n            this.setOption({ autoHide: simpleChange.autoHide.currentValue });\n        }\n\n        if (simpleChange.id) {\n            this.setOption({ id: simpleChange.id.currentValue });\n        }\n\n        if (simpleChange.tooltipOptions) {\n            this._tooltipOptions = { ...this._tooltipOptions, ...simpleChange.tooltipOptions.currentValue };\n            this.deactivate();\n\n            if (this.active) {\n                if (this.getOption('tooltipLabel')) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    } else {\n                        this.show();\n                    }\n                } else {\n                    this.hide();\n                }\n            }\n        }\n    }\n\n    isAutoHide(): boolean {\n        return this.getOption('autoHide');\n    }\n\n    onMouseEnter(e: Event) {\n        if (!this.container && !this.showTimeout) {\n            this.activate();\n        }\n    }\n\n    onMouseLeave(e: MouseEvent) {\n        if (!this.isAutoHide()) {\n            const valid = DomHandler.hasClass(e.relatedTarget, 'p-tooltip') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-text') || DomHandler.hasClass(e.relatedTarget, 'p-tooltip-arrow');\n            !valid && this.deactivate();\n        } else {\n            this.deactivate();\n        }\n    }\n\n    onFocus(e: Event) {\n        this.activate();\n    }\n\n    onBlur(e: Event) {\n        this.deactivate();\n    }\n\n    onInputClick(e: Event) {\n        this.deactivate();\n    }\n\n    @HostListener('document:keydown.escape', ['$event'])\n    onPressEscape() {\n        if (this.hideOnEscape) {\n            this.deactivate();\n        }\n    }\n\n    activate() {\n        if (!this.interactionInProgress) {\n            this.active = true;\n            this.clearHideTimeout();\n\n            if (this.getOption('showDelay'))\n                this.showTimeout = setTimeout(() => {\n                    this.show();\n                }, this.getOption('showDelay'));\n            else this.show();\n\n            if (this.getOption('life')) {\n                let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n                this.hideTimeout = setTimeout(() => {\n                    this.hide();\n                }, duration);\n            }\n        }\n        this.interactionInProgress = true;\n    }\n\n    deactivate() {\n        this.interactionInProgress = false;\n        this.active = false;\n        this.clearShowTimeout();\n\n        if (this.getOption('hideDelay')) {\n            this.clearHideTimeout(); //life timeout\n            this.hideTimeout = setTimeout(() => {\n                this.hide();\n            }, this.getOption('hideDelay'));\n        } else {\n            this.hide();\n        }\n    }\n\n    create() {\n        if (this.container) {\n            this.clearHideTimeout();\n            this.remove();\n        }\n\n        this.container = document.createElement('div');\n        this.container.setAttribute('id', this.getOption('id'));\n        this.container.setAttribute('role', 'tooltip');\n\n        let tooltipArrow = document.createElement('div');\n        tooltipArrow.className = 'p-tooltip-arrow';\n        this.container.appendChild(tooltipArrow);\n\n        this.tooltipText = document.createElement('div');\n        this.tooltipText.className = 'p-tooltip-text';\n\n        this.updateText();\n\n        if (this.getOption('positionStyle')) {\n            this.container.style.position = this.getOption('positionStyle');\n        }\n\n        this.container.appendChild(this.tooltipText);\n\n        if (this.getOption('appendTo') === 'body') document.body.appendChild(this.container);\n        else if (this.getOption('appendTo') === 'target') DomHandler.appendChild(this.container, this.el.nativeElement);\n        else DomHandler.appendChild(this.container, this.getOption('appendTo'));\n\n        this.container.style.display = 'inline-block';\n\n        if (this.fitContent) {\n            this.container.style.width = 'fit-content';\n        }\n\n        if (this.isAutoHide()) {\n            this.container.style.pointerEvents = 'none';\n        } else {\n            this.container.style.pointerEvents = 'unset';\n            this.bindContainerMouseleaveListener();\n        }\n    }\n\n    bindContainerMouseleaveListener() {\n        if (!this.containerMouseleaveListener) {\n            const targetEl: any = this.container ?? this.container.nativeElement;\n\n            this.containerMouseleaveListener = this.renderer.listen(targetEl, 'mouseleave', (e) => {\n                this.deactivate();\n            });\n        }\n    }\n\n    unbindContainerMouseleaveListener() {\n        if (this.containerMouseleaveListener) {\n            this.bindContainerMouseleaveListener();\n            this.containerMouseleaveListener = null;\n        }\n    }\n\n    show() {\n        if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n            return;\n        }\n\n        this.create();\n\n        const nativeElement = this.el.nativeElement;\n        const pDialogWrapper = nativeElement.closest('p-dialog');\n\n        if (pDialogWrapper) {\n            setTimeout(() => {\n                this.container && this.align();\n            }, 100);\n        } else {\n            this.align();\n        }\n\n        DomHandler.fadeIn(this.container, 250);\n\n        if (this.getOption('tooltipZIndex') === 'auto') ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);\n        else this.container.style.zIndex = this.getOption('tooltipZIndex');\n\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n    }\n\n    hide() {\n        if (this.getOption('tooltipZIndex') === 'auto') {\n            ZIndexUtils.clear(this.container);\n        }\n\n        this.remove();\n    }\n\n    updateText() {\n        const content = this.getOption('tooltipLabel');\n        if (content instanceof TemplateRef) {\n            const embeddedViewRef = this.viewContainer.createEmbeddedView(content);\n            embeddedViewRef.detectChanges();\n            embeddedViewRef.rootNodes.forEach((node) => this.tooltipText.appendChild(node));\n        } else if (this.getOption('escape')) {\n            this.tooltipText.innerHTML = '';\n            this.tooltipText.appendChild(document.createTextNode(content));\n        } else {\n            this.tooltipText.innerHTML = content;\n        }\n    }\n\n    align() {\n        let position = this.getOption('tooltipPosition');\n\n        switch (position) {\n            case 'top':\n                this.alignTop();\n                if (this.isOutOfBounds()) {\n                    this.alignBottom();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n\n            case 'bottom':\n                this.alignBottom();\n                if (this.isOutOfBounds()) {\n                    this.alignTop();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n\n            case 'left':\n                this.alignLeft();\n                if (this.isOutOfBounds()) {\n                    this.alignRight();\n\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n\n            case 'right':\n                this.alignRight();\n                if (this.isOutOfBounds()) {\n                    this.alignLeft();\n\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n        }\n    }\n\n    getHostOffset() {\n        if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n            let offset = this.el.nativeElement.getBoundingClientRect();\n            let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n            let targetTop = offset.top + DomHandler.getWindowScrollTop();\n\n            return { left: targetLeft, top: targetTop };\n        } else {\n            return { left: 0, top: 0 };\n        }\n    }\n\n    alignRight() {\n        this.preAlign('right');\n        const el = this.activeElement;\n\n        const hostOffset = this.getHostOffset();\n        const left = hostOffset.left + DomHandler.getOuterWidth(el);\n        const top = hostOffset.top + (DomHandler.getOuterHeight(el) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n\n    private get activeElement(): HTMLElement {\n        return this.el.nativeElement.nodeName.includes('P-') ? DomHandler.findSingle(this.el.nativeElement, '.p-component') || this.el.nativeElement : this.el.nativeElement;\n    }\n\n    alignLeft() {\n        this.preAlign('left');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n        let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n\n    alignTop() {\n        this.preAlign('top');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n\n    alignBottom() {\n        this.preAlign('bottom');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n\n    setOption(option: any) {\n        this._tooltipOptions = { ...this._tooltipOptions, ...option };\n    }\n\n    getOption(option: string) {\n        return this._tooltipOptions[option as keyof typeof this.tooltipOptions];\n    }\n\n    getTarget(el: Element) {\n        return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n    }\n\n    preAlign(position: string) {\n        this.container.style.left = -999 + 'px';\n        this.container.style.top = -999 + 'px';\n\n        let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n        this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n    }\n\n    isOutOfBounds(): boolean {\n        let offset = this.container.getBoundingClientRect();\n        let targetTop = offset.top;\n        let targetLeft = offset.left;\n        let width = DomHandler.getOuterWidth(this.container);\n        let height = DomHandler.getOuterHeight(this.container);\n        let viewport = DomHandler.getViewport();\n\n        return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n    }\n\n    onWindowResize(e: Event) {\n        this.hide();\n    }\n\n    bindDocumentResizeListener() {\n        this.zone.runOutsideAngular(() => {\n            this.resizeListener = this.onWindowResize.bind(this);\n            window.addEventListener('resize', this.resizeListener);\n        });\n    }\n\n    unbindDocumentResizeListener() {\n        if (this.resizeListener) {\n            window.removeEventListener('resize', this.resizeListener);\n            this.resizeListener = null;\n        }\n    }\n\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n                if (this.container) {\n                    this.hide();\n                }\n            });\n        }\n\n        this.scrollHandler.bindScrollListener();\n    }\n\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n\n    unbindEvents() {\n        const tooltipEvent = this.getOption('tooltipEvent');\n\n        if (tooltipEvent === 'hover' || tooltipEvent === 'both') {\n            this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n            this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n            this.el.nativeElement.removeEventListener('click', this.clickListener);\n        }\n        if (tooltipEvent === 'focus' || tooltipEvent === 'both') {\n            let target = this.el.nativeElement.querySelector('.p-component');\n\n            if (!target) {\n                target = this.getTarget(this.el.nativeElement);\n            }\n\n            target.removeEventListener('focus', this.focusListener);\n            target.removeEventListener('blur', this.blurListener);\n        }\n        this.unbindDocumentResizeListener();\n    }\n\n    remove() {\n        if (this.container && this.container.parentElement) {\n            if (this.getOption('appendTo') === 'body') document.body.removeChild(this.container);\n            else if (this.getOption('appendTo') === 'target') this.el.nativeElement.removeChild(this.container);\n            else DomHandler.removeChild(this.container, this.getOption('appendTo'));\n        }\n\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.unbindContainerMouseleaveListener();\n        this.clearTimeouts();\n        this.container = null;\n        this.scrollHandler = null;\n    }\n\n    clearShowTimeout() {\n        if (this.showTimeout) {\n            clearTimeout(this.showTimeout);\n            this.showTimeout = null;\n        }\n    }\n\n    clearHideTimeout() {\n        if (this.hideTimeout) {\n            clearTimeout(this.hideTimeout);\n            this.hideTimeout = null;\n        }\n    }\n\n    clearTimeouts() {\n        this.clearShowTimeout();\n        this.clearHideTimeout();\n    }\n\n    ngOnDestroy() {\n        this.unbindEvents();\n\n        if (this.container) {\n            ZIndexUtils.clear(this.container);\n        }\n\n        this.remove();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Tooltip],\n    declarations: [Tooltip]\n})\nexport class TooltipModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;AAOA;;;AAGG;MAOU,OAAO,CAAA;AAuJyB,IAAA,UAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAAqB,IAAA,MAAA,CAAA;AAA+B,IAAA,QAAA,CAAA;AAA6B,IAAA,aAAA,CAAA;AAtJzK;;;AAGG;AACM,IAAA,eAAe,CAA2D;AACnF;;;AAGG;IACM,YAAY,GAA8C,OAAO,CAAC;AAC3E;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;AAGG;IACqC,MAAM,GAAY,IAAI,CAAC;AAC/D;;;AAGG;AACoC,IAAA,SAAS,CAAqB;AACrE;;;AAGG;AACoC,IAAA,SAAS,CAAqB;AACrE;;;AAGG;AACoC,IAAA,IAAI,CAAqB;AAChE;;;AAGG;AACoC,IAAA,WAAW,CAAqB;AACvE;;;AAGG;AACoC,IAAA,YAAY,CAAqB;AACxE;;;AAGG;IACqC,QAAQ,GAAY,IAAI,CAAC;AACjE;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACqC,YAAY,GAAY,IAAI,CAAC;AACrE;;;AAGG;AACgB,IAAA,OAAO,CAAgD;AAC1E;;;;AAIG;AACH,IAAA,IAA8B,QAAQ,GAAA;QAClC,OAAO,IAAI,CAAC,SAAoB,CAAC;KACpC;IACD,IAAI,QAAQ,CAAC,GAAY,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QACrB,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB;AACD;;;AAGG;AACM,IAAA,cAAc,CAA6B;AAEpD,IAAA,eAAe,GAAG;AACd,QAAA,YAAY,EAAE,IAAI;AAClB,QAAA,eAAe,EAAE,OAAO;AACxB,QAAA,YAAY,EAAE,OAAO;AACrB,QAAA,QAAQ,EAAE,MAAM;AAChB,QAAA,aAAa,EAAE,IAAI;AACnB,QAAA,iBAAiB,EAAE,IAAI;AACvB,QAAA,aAAa,EAAE,MAAM;AACrB,QAAA,MAAM,EAAE,IAAI;AACZ,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,WAAW,EAAE,IAAI;AACjB,QAAA,YAAY,EAAE,IAAI;AAClB,QAAA,IAAI,EAAE,IAAI;AACV,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,YAAY,EAAE,IAAI;AAClB,QAAA,EAAE,EAAE,iBAAiB,EAAE,GAAG,UAAU;KACvC,CAAC;AAEF,IAAA,SAAS,CAAsB;AAE/B,IAAA,SAAS,CAAM;AAEf,IAAA,UAAU,CAAqB;AAE/B,IAAA,WAAW,CAAM;AAEjB,IAAA,WAAW,CAAM;AAEjB,IAAA,WAAW,CAAM;AAEjB,IAAA,MAAM,CAAsB;AAE5B,IAAA,kBAAkB,CAAqB;AAEvC,IAAA,kBAAkB,CAAqB;AAEvC,IAAA,2BAA2B,CAAqB;AAEhD,IAAA,aAAa,CAAqB;AAElC,IAAA,aAAa,CAAqB;AAElC,IAAA,YAAY,CAAqB;AAEjC,IAAA,aAAa,CAAM;AAEnB,IAAA,cAAc,CAAM;IAEpB,qBAAqB,GAAG,KAAK,CAAC;IAE9B,WAAyC,CAAA,UAAe,EAAS,EAAc,EAAS,IAAY,EAAS,MAAqB,EAAU,QAAmB,EAAU,aAA+B,EAAA;QAA/J,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAU,IAAa,CAAA,aAAA,GAAb,aAAa,CAAkB;KAAI;IAE5M,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AAEpD,gBAAA,IAAI,YAAY,KAAK,OAAO,IAAI,YAAY,KAAK,MAAM,EAAE;oBACrD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClD,oBAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC9E,oBAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACpE,oBAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACjF,iBAAA;AACD,gBAAA,IAAI,YAAY,KAAK,OAAO,IAAI,YAAY,KAAK,MAAM,EAAE;oBACrD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE3C,oBAAA,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;oBAEjE,IAAI,CAAC,MAAM,EAAE;wBACT,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;AAClD,qBAAA;oBAED,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBACrD,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACtD,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,YAA2B,EAAA;QACnC,IAAI,YAAY,CAAC,eAAe,EAAE;AAC9B,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,eAAe,EAAE,YAAY,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,CAAC;AAClF,SAAA;QAED,IAAI,YAAY,CAAC,YAAY,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC;AAC5E,SAAA;QAED,IAAI,YAAY,CAAC,QAAQ,EAAE;AACvB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;AACpE,SAAA;QAED,IAAI,YAAY,CAAC,aAAa,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,EAAE,YAAY,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC;AAC9E,SAAA;QAED,IAAI,YAAY,CAAC,iBAAiB,EAAE;AAChC,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,iBAAiB,EAAE,YAAY,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC,CAAC;AACtF,SAAA;QAED,IAAI,YAAY,CAAC,aAAa,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,aAAa,EAAE,YAAY,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC;AAC9E,SAAA;QAED,IAAI,YAAY,CAAC,MAAM,EAAE;AACrB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;AAChE,SAAA;QAED,IAAI,YAAY,CAAC,SAAS,EAAE;AACxB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC;AACtE,SAAA;QAED,IAAI,YAAY,CAAC,SAAS,EAAE;AACxB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC;AACtE,SAAA;QAED,IAAI,YAAY,CAAC,IAAI,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;AAC5D,SAAA;QAED,IAAI,YAAY,CAAC,WAAW,EAAE;AAC1B,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,WAAW,EAAE,YAAY,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC;AAC1E,SAAA;QAED,IAAI,YAAY,CAAC,YAAY,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC;AAC5E,SAAA;QAED,IAAI,YAAY,CAAC,QAAQ,EAAE;AACvB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;AACpE,SAAA;QAED,IAAI,YAAY,CAAC,OAAO,EAAE;AACtB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC;YAEpE,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,gBAAA,IAAI,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE;oBACnC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;wBAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;wBAClB,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,qBAAA;AAAM,yBAAA;wBACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AACJ,iBAAA;AAAM,qBAAA;oBACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,IAAI,YAAY,CAAC,QAAQ,EAAE;AACvB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;AACpE,SAAA;QAED,IAAI,YAAY,CAAC,EAAE,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC;AACxD,SAAA;QAED,IAAI,YAAY,CAAC,cAAc,EAAE;AAC7B,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,YAAY,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;YAChG,IAAI,CAAC,UAAU,EAAE,CAAC;YAElB,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;oBAChC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;wBAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;wBAClB,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,qBAAA;AAAM,yBAAA;wBACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AACJ,iBAAA;AAAM,qBAAA;oBACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;KACrC;AAED,IAAA,YAAY,CAAC,CAAQ,EAAA;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtC,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,CAAa,EAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AACpB,YAAA,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,EAAE,gBAAgB,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;AACrL,YAAA,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AAC/B,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,CAAQ,EAAA;QACZ,IAAI,CAAC,QAAQ,EAAE,CAAC;KACnB;AAED,IAAA,MAAM,CAAC,CAAQ,EAAA;QACX,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB;AAED,IAAA,YAAY,CAAC,CAAQ,EAAA;QACjB,IAAI,CAAC,UAAU,EAAE,CAAC;KACrB;IAGD,aAAa,GAAA;QACT,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,UAAU,EAAE,CAAC;AACrB,SAAA;KACJ;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AAC7B,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAExB,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AAC3B,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,MAAK;oBAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;iBACf,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;;gBAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;AAEjB,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AACxB,gBAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3H,gBAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,MAAK;oBAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;iBACf,EAAE,QAAQ,CAAC,CAAC;AAChB,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;KACrC;IAED,UAAU,GAAA;AACN,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACnC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAExB,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxB,YAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,MAAK;gBAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;aACf,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;AACnC,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;KACJ;IAED,MAAM,GAAA;QACF,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB,SAAA;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAE/C,IAAI,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACjD,QAAA,YAAY,CAAC,SAAS,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAEzC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,gBAAgB,CAAC;QAE9C,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;AACnE,SAAA;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAE7C,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM;YAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChF,aAAA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ;AAAE,YAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;;AAC3G,YAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QAExE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC;QAE9C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC;AAC9C,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACnB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;AAC/C,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,GAAG,OAAO,CAAC;YAC7C,IAAI,CAAC,+BAA+B,EAAE,CAAC;AAC1C,SAAA;KACJ;IAED,+BAA+B,GAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACnC,MAAM,QAAQ,GAAQ,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;AAErE,YAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC,KAAI;gBAClF,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,iCAAiC,GAAA;QAC7B,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,IAAI,CAAC,+BAA+B,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;AAC3C,SAAA;KACJ;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;YAC/D,OAAO;AACV,SAAA;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;AAEd,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC;QAC5C,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAEzD,QAAA,IAAI,cAAc,EAAE;YAChB,UAAU,CAAC,MAAK;AACZ,gBAAA,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;aAClC,EAAE,GAAG,CAAC,CAAC;AACX,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,SAAA;QAED,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAEvC,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,MAAM;AAAE,YAAA,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;;AAClH,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAEnE,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B;IAED,IAAI,GAAA;QACA,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;AAC5C,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;KACjB;IAED,UAAU,GAAA;QACN,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAC/C,IAAI,OAAO,YAAY,WAAW,EAAE;YAChC,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACvE,eAAe,CAAC,aAAa,EAAE,CAAC;AAChC,YAAA,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AACnF,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;AAClE,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,OAAO,CAAC;AACxC,SAAA;KACJ;IAED,KAAK,GAAA;QACD,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AAEjD,QAAA,QAAQ,QAAQ;AACZ,YAAA,KAAK,KAAK;gBACN,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChB,gBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;oBACtB,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,oBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;wBACtB,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,wBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;4BACtB,IAAI,CAAC,SAAS,EAAE,CAAC;AACpB,yBAAA;AACJ,qBAAA;AACJ,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,QAAQ;gBACT,IAAI,CAAC,WAAW,EAAE,CAAC;AACnB,gBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;oBACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;AAChB,oBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;wBACtB,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,wBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;4BACtB,IAAI,CAAC,SAAS,EAAE,CAAC;AACpB,yBAAA;AACJ,qBAAA;AACJ,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,gBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;oBACtB,IAAI,CAAC,UAAU,EAAE,CAAC;AAElB,oBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;wBACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;AAEhB,wBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;4BACtB,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,yBAAA;AACJ,qBAAA;AACJ,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,OAAO;gBACR,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,gBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;oBACtB,IAAI,CAAC,SAAS,EAAE,CAAC;AAEjB,oBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;wBACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;AAEhB,wBAAA,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;4BACtB,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,yBAAA;AACJ,qBAAA;AACJ,iBAAA;gBACD,MAAM;AACb,SAAA;KACJ;IAED,aAAa,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;YAClF,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;YAC3D,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,mBAAmB,EAAE,CAAC;YAChE,IAAI,SAAS,GAAG,MAAM,CAAC,GAAG,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAE7D,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;AAC/C,SAAA;AAAM,aAAA;YACH,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAC9B,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvB,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC;AAE9B,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AACxC,QAAA,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAC5D,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC7G,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;AACzE,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;KACzE;AAED,IAAA,IAAY,aAAa,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC;KACxK;IAED,SAAS,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtB,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AACtC,QAAA,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACtE,QAAA,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9H,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;AACzE,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;KACzE;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACrB,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AACtC,QAAA,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9H,QAAA,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;AACzE,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;KACzE;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACxB,QAAA,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AACtC,QAAA,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9H,QAAA,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;AACzE,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;KACzE;AAED,IAAA,SAAS,CAAC,MAAW,EAAA;AACjB,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,MAAM,EAAE,CAAC;KACjE;AAED,IAAA,SAAS,CAAC,MAAc,EAAA;AACpB,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAA0C,CAAC,CAAC;KAC3E;AAED,IAAA,SAAS,CAAC,EAAW,EAAA;QACjB,OAAO,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,gBAAgB,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;KAC9F;AAED,IAAA,QAAQ,CAAC,QAAgB,EAAA;QACrB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;AAEvC,QAAA,IAAI,gBAAgB,GAAG,kCAAkC,GAAG,QAAQ,CAAC;AACrE,QAAA,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,gBAAgB,CAAC;KACpJ;IAED,aAAa,GAAA;QACT,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;AACpD,QAAA,IAAI,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC;AAC3B,QAAA,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;QAC7B,IAAI,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACvD,QAAA,IAAI,QAAQ,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QAExC,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,UAAU,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;KACzH;AAED,IAAA,cAAc,CAAC,CAAQ,EAAA;QACnB,IAAI,CAAC,IAAI,EAAE,CAAC;KACf;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAC3D,SAAC,CAAC,CAAC;KACN;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,MAAK;gBAC/E,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;KAC3C;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;AAC7C,SAAA;KACJ;IAED,YAAY,GAAA;QACR,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AAEpD,QAAA,IAAI,YAAY,KAAK,OAAO,IAAI,YAAY,KAAK,MAAM,EAAE;AACrD,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACjF,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACjF,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1E,SAAA;AACD,QAAA,IAAI,YAAY,KAAK,OAAO,IAAI,YAAY,KAAK,MAAM,EAAE;AACrD,YAAA,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;YAEjE,IAAI,CAAC,MAAM,EAAE;gBACT,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;AAClD,aAAA;YAED,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACxD,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACzD,SAAA;QACD,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACvC;IAED,MAAM,GAAA;QACF,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;AAChD,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,MAAM;gBAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChF,iBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,QAAQ;gBAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;;AAC/F,gBAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC3E,SAAA;QAED,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,iCAAiC,EAAE,CAAC;QACzC,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC7B;IAED,gBAAgB,GAAA;QACZ,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B,SAAA;KACJ;IAED,gBAAgB,GAAA;QACZ,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/B,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;KAC3B;IAED,WAAW,GAAA;QACP,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,SAAA;KACJ;AA5sBQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kBAuJI,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAvJtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,iQAmCI,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,eAAe,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKf,eAAe,CAKf,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,CAKf,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,eAAe,kDAKf,eAAe,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKf,gBAAgB,CAKhB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,kDAKhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,CAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,uBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FA3E3B,OAAO,EAAA,UAAA,EAAA,CAAA;kBANnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAwJgB,MAAM;2BAAC,WAAW,CAAA;0KAlJtB,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,IAAI,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKE,YAAY,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKnB,OAAO,EAAA,CAAA;sBAAzB,KAAK;uBAAC,UAAU,CAAA;gBAMa,QAAQ,EAAA,CAAA;sBAArC,KAAK;uBAAC,iBAAiB,CAAA;gBAWf,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAuNN,aAAa,EAAA,CAAA;sBADZ,YAAY;uBAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,CAAA;;MA6Z1C,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAptBb,YAAA,EAAA,CAAA,OAAO,CAgtBN,EAAA,OAAA,EAAA,CAAA,YAAY,aAhtBb,OAAO,CAAA,EAAA,CAAA,CAAA;AAotBP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAJZ,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,OAAO,CAAC;oBAClB,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;ACpuBD;;AAEG;;;;"}