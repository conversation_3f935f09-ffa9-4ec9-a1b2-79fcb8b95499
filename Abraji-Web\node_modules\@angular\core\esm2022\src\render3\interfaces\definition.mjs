/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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