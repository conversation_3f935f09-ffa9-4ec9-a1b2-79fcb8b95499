{"version": 3, "file": "primeng-inputgroup.mjs", "sources": ["../../src/app/components/inputgroup/inputgroup.ts", "../../src/app/components/inputgroup/primeng-inputgroup.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { Component, Input, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n/**\n * InputGroup displays text, icon, buttons and other content can be grouped next to an input.\n * @group Components\n */\n@Component({\n    selector: 'p-inputGroup',\n    template: `\n        <div class=\"p-inputgroup\" [attr.data-pc-name]=\"'inputgroup'\" [ngClass]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `,\n    host: {\n        class: 'p-element p-inputgroup'\n    }\n})\nexport class InputGroup {\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [InputGroup, SharedModule],\n    declarations: [InputGroup]\n})\nexport class InputGroupModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAGA;;;AAGG;MAYU,UAAU,CAAA;AACnB;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;uGAV/B,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EATT,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;2FAKQ,UAAU,EAAA,UAAA,EAAA,CAAA;kBAXtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,CAAA;;;;AAIT,IAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,wBAAwB;AAClC,qBAAA;AACJ,iBAAA,CAAA;8BAMY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;;MAQG,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,iBAlBhB,UAAU,CAAA,EAAA,OAAA,EAAA,CAcT,YAAY,CAdb,EAAA,OAAA,EAAA,CAAA,UAAU,EAeG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGzB,gBAAgB,EAAA,OAAA,EAAA,CAJf,YAAY,EACA,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGzB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;oBACnC,YAAY,EAAE,CAAC,UAAU,CAAC;AAC7B,iBAAA,CAAA;;;ACnCD;;AAEG;;;;"}