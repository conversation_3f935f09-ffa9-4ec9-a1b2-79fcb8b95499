/*!
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertDefined } from '../../util/assert';
import { CONTEXT, DECLARATION_COMPONENT_VIEW } from '../interfaces/view';
import { getLView } from '../state';
/**
 * Instruction that returns the component instance in which the current instruction is executing.
 * This is a constant-time version of `nextContent` for the case where we know that we need the
 * component instance specifically, rather than the context of a particular template.
 *
 * @codeGenApi
 */
export function ɵɵcomponentInstance() {
    const instance = getLView()[DECLARATION_COMPONENT_VIEW][CONTEXT];
    ngDevMode && assertDefined(instance, 'Expected component instance to be defined');
    return instance;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29tcG9uZW50X2luc3RhbmNlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29yZS9zcmMvcmVuZGVyMy9pbnN0cnVjdGlvbnMvY29tcG9uZW50X2luc3RhbmNlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sRUFBQyxhQUFhLEVBQUMsTUFBTSxtQkFBbUIsQ0FBQztBQUNoRCxPQUFPLEVBQUMsT0FBTyxFQUFFLDBCQUEwQixFQUFDLE1BQU0sb0JBQW9CLENBQUM7QUFDdkUsT0FBTyxFQUFDLFFBQVEsRUFBQyxNQUFNLFVBQVUsQ0FBQztBQUVsQzs7Ozs7O0dBTUc7QUFDSCxNQUFNLFVBQVUsbUJBQW1CO0lBQ2pDLE1BQU0sUUFBUSxHQUFHLFFBQVEsRUFBRSxDQUFDLDBCQUEwQixDQUFDLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDakUsU0FBUyxJQUFJLGFBQWEsQ0FBQyxRQUFRLEVBQUUsMkNBQTJDLENBQUMsQ0FBQztJQUNsRixPQUFPLFFBQVEsQ0FBQztBQUNsQixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyohXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7YXNzZXJ0RGVmaW5lZH0gZnJvbSAnLi4vLi4vdXRpbC9hc3NlcnQnO1xuaW1wb3J0IHtDT05URVhULCBERUNMQVJBVElPTl9DT01QT05FTlRfVklFV30gZnJvbSAnLi4vaW50ZXJmYWNlcy92aWV3JztcbmltcG9ydCB7Z2V0TFZpZXd9IGZyb20gJy4uL3N0YXRlJztcblxuLyoqXG4gKiBJbnN0cnVjdGlvbiB0aGF0IHJldHVybnMgdGhlIGNvbXBvbmVudCBpbnN0YW5jZSBpbiB3aGljaCB0aGUgY3VycmVudCBpbnN0cnVjdGlvbiBpcyBleGVjdXRpbmcuXG4gKiBUaGlzIGlzIGEgY29uc3RhbnQtdGltZSB2ZXJzaW9uIG9mIGBuZXh0Q29udGVudGAgZm9yIHRoZSBjYXNlIHdoZXJlIHdlIGtub3cgdGhhdCB3ZSBuZWVkIHRoZVxuICogY29tcG9uZW50IGluc3RhbmNlIHNwZWNpZmljYWxseSwgcmF0aGVyIHRoYW4gdGhlIGNvbnRleHQgb2YgYSBwYXJ0aWN1bGFyIHRlbXBsYXRlLlxuICpcbiAqIEBjb2RlR2VuQXBpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiDJtcm1Y29tcG9uZW50SW5zdGFuY2UoKTogdW5rbm93biB7XG4gIGNvbnN0IGluc3RhbmNlID0gZ2V0TFZpZXcoKVtERUNMQVJBVElPTl9DT01QT05FTlRfVklFV11bQ09OVEVYVF07XG4gIG5nRGV2TW9kZSAmJiBhc3NlcnREZWZpbmVkKGluc3RhbmNlLCAnRXhwZWN0ZWQgY29tcG9uZW50IGluc3RhbmNlIHRvIGJlIGRlZmluZWQnKTtcbiAgcmV0dXJuIGluc3RhbmNlO1xufVxuIl19