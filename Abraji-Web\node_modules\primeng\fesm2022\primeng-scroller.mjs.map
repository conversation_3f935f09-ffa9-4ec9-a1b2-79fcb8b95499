{"version": 3, "file": "primeng-scroller.mjs", "sources": ["../../src/app/components/scroller/scroller.ts", "../../src/app/components/scroller/primeng-scroller.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewChecked,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    NgZone,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation\n} from '@angular/core';\nimport { PrimeTemplate, ScrollerOptions, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { ScrollerLazyLoadEvent, ScrollerScrollEvent, ScrollerScrollIndexChangeEvent, ScrollerToType } from './scroller.interface';\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\n@Component({\n    selector: 'p-scroller',\n    template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon pi-spin'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `,\n    changeDetection: ChangeDetectionStrategy.Default,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./scroller.css'],\n    host: {\n        class: 'p-scroller-viewport p-element'\n    }\n})\nexport class Scroller implements OnInit, AfterContentInit, AfterViewChecked, OnDestroy {\n    /**\n     * Unique identifier of the element.\n     * @group Props\n     */\n    @Input() get id(): string | undefined {\n        return this._id;\n    }\n    set id(val: string | undefined) {\n        this._id = val;\n    }\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() get style(): any {\n        return this._style;\n    }\n    set style(val: any) {\n        this._style = val;\n    }\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() get styleClass(): string | undefined {\n        return this._styleClass;\n    }\n    set styleClass(val: string | undefined) {\n        this._styleClass = val;\n    }\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input() get tabindex() {\n        return this._tabindex;\n    }\n    set tabindex(val: number) {\n        this._tabindex = val;\n    }\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    @Input() get items(): any[] | undefined | null {\n        return this._items;\n    }\n    set items(val: any[] | undefined | null) {\n        this._items = val;\n    }\n    /**\n     * The height/width of item according to orientation.\n     * @group Props\n     */\n    @Input() get itemSize(): number[] | number {\n        return this._itemSize;\n    }\n    set itemSize(val: number[] | number) {\n        this._itemSize = val;\n    }\n    /**\n     * Height of the scroll viewport.\n     * @group Props\n     */\n    @Input() get scrollHeight(): string | undefined {\n        return this._scrollHeight;\n    }\n    set scrollHeight(val: string | undefined) {\n        this._scrollHeight = val;\n    }\n    /**\n     * Width of the scroll viewport.\n     * @group Props\n     */\n    @Input() get scrollWidth(): string | undefined {\n        return this._scrollWidth;\n    }\n    set scrollWidth(val: string | undefined) {\n        this._scrollWidth = val;\n    }\n    /**\n     * The orientation of scrollbar.\n     * @group Props\n     */\n    @Input() get orientation(): 'vertical' | 'horizontal' | 'both' {\n        return this._orientation;\n    }\n    set orientation(val: 'vertical' | 'horizontal' | 'both') {\n        this._orientation = val;\n    }\n    /**\n     * Used to specify how many items to load in each load method in lazy mode.\n     * @group Props\n     */\n    @Input() get step(): number {\n        return this._step;\n    }\n    set step(val: number) {\n        this._step = val;\n    }\n    /**\n     * Delay in scroll before new data is loaded.\n     * @group Props\n     */\n    @Input() get delay() {\n        return this._delay;\n    }\n    set delay(val: number) {\n        this._delay = val;\n    }\n    /**\n     * Delay after window's resize finishes.\n     * @group Props\n     */\n    @Input() get resizeDelay() {\n        return this._resizeDelay;\n    }\n    set resizeDelay(val: number) {\n        this._resizeDelay = val;\n    }\n    /**\n     * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n     * @group Props\n     */\n    @Input() get appendOnly(): boolean {\n        return this._appendOnly;\n    }\n    set appendOnly(val: boolean) {\n        this._appendOnly = val;\n    }\n    /**\n     * Specifies whether the scroller should be displayed inline or not.\n     * @group Props\n     */\n    @Input() get inline() {\n        return this._inline;\n    }\n    set inline(val: boolean) {\n        this._inline = val;\n    }\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input() get lazy() {\n        return this._lazy;\n    }\n    set lazy(val: boolean) {\n        this._lazy = val;\n    }\n    /**\n     * If disabled, the scroller feature is eliminated and the content is displayed directly.\n     * @group Props\n     */\n    @Input() get disabled() {\n        return this._disabled;\n    }\n    set disabled(val: boolean) {\n        this._disabled = val;\n    }\n    /**\n     * Used to implement a custom loader instead of using the loader feature in the scroller.\n     * @group Props\n     */\n    @Input() get loaderDisabled() {\n        return this._loaderDisabled;\n    }\n    set loaderDisabled(val: boolean) {\n        this._loaderDisabled = val;\n    }\n    /**\n     * Columns to display.\n     * @group Props\n     */\n    @Input() get columns(): any[] | undefined | null {\n        return this._columns;\n    }\n    set columns(val: any[] | undefined | null) {\n        this._columns = val;\n    }\n    /**\n     * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n     * @group Props\n     */\n    @Input() get showSpacer() {\n        return this._showSpacer;\n    }\n    set showSpacer(val: boolean) {\n        this._showSpacer = val;\n    }\n    /**\n     * Defines whether to show loader.\n     * @group Props\n     */\n    @Input() get showLoader() {\n        return this._showLoader;\n    }\n    set showLoader(val: boolean) {\n        this._showLoader = val;\n    }\n    /**\n     * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n     * @group Props\n     */\n    @Input() get numToleratedItems() {\n        return this._numToleratedItems;\n    }\n    set numToleratedItems(val: number) {\n        this._numToleratedItems = val;\n    }\n    /**\n     * Defines whether the data is loaded.\n     * @group Props\n     */\n    @Input() get loading(): boolean | undefined {\n        return this._loading;\n    }\n    set loading(val: boolean | undefined) {\n        this._loading = val;\n    }\n    /**\n     * Defines whether to dynamically change the height or width of scrollable container.\n     * @group Props\n     */\n    @Input() get autoSize(): boolean {\n        return this._autoSize;\n    }\n    set autoSize(val: boolean) {\n        this._autoSize = val;\n    }\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n     * @group Props\n     */\n    @Input() get trackBy(): Function {\n        return this._trackBy;\n    }\n    set trackBy(val: Function) {\n        this._trackBy = val;\n    }\n    /**\n     * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() get options(): ScrollerOptions | undefined {\n        return this._options;\n    }\n    set options(val: ScrollerOptions | undefined) {\n        this._options = val;\n\n        if (val && typeof val === 'object') {\n            //@ts-ignore\n            Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n        }\n    }\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    @Output() onLazyLoad: EventEmitter<ScrollerLazyLoadEvent> = new EventEmitter<ScrollerLazyLoadEvent>();\n    /**\n     * Callback to invoke when scroll position changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll event.\n     * @group Emits\n     */\n    @Output() onScroll: EventEmitter<ScrollerScrollEvent> = new EventEmitter<ScrollerScrollEvent>();\n    /**\n     * Callback to invoke when scroll position and item's range in view changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n     * @group Emits\n     */\n    @Output() onScrollIndexChange: EventEmitter<ScrollerScrollIndexChangeEvent> = new EventEmitter<ScrollerScrollIndexChangeEvent>();\n\n    @ViewChild('element') elementViewChild: Nullable<ElementRef>;\n\n    @ViewChild('content') contentViewChild: Nullable<ElementRef>;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    _id: string | undefined;\n\n    _style: { [klass: string]: any } | null | undefined;\n\n    _styleClass: string | undefined;\n\n    _tabindex: number = 0;\n\n    _items: any[] | undefined | null;\n\n    _itemSize: number | number[] = 0;\n\n    _scrollHeight: string | undefined;\n\n    _scrollWidth: string | undefined;\n\n    _orientation: 'vertical' | 'horizontal' | 'both' = 'vertical';\n\n    _step: number = 0;\n\n    _delay: number = 0;\n\n    _resizeDelay: number = 10;\n\n    _appendOnly: boolean = false;\n\n    _inline: boolean = false;\n\n    _lazy: boolean = false;\n\n    _disabled: boolean = false;\n\n    _loaderDisabled: boolean = false;\n\n    _columns: any[] | undefined | null;\n\n    _showSpacer: boolean = true;\n\n    _showLoader: boolean = false;\n\n    _numToleratedItems: any;\n\n    _loading: boolean | undefined;\n\n    _autoSize: boolean = false;\n\n    _trackBy: any;\n\n    _options: ScrollerOptions | undefined;\n\n    d_loading: boolean = false;\n\n    d_numToleratedItems: any;\n\n    contentEl: any;\n\n    contentTemplate: Nullable<TemplateRef<any>>;\n\n    itemTemplate: Nullable<TemplateRef<any>>;\n\n    loaderTemplate: Nullable<TemplateRef<any>>;\n\n    loaderIconTemplate: Nullable<TemplateRef<any>>;\n\n    first: any = 0;\n\n    last: any = 0;\n\n    page: number = 0;\n\n    isRangeChanged: boolean = false;\n\n    numItemsInViewport: any = 0;\n\n    lastScrollPos: any = 0;\n\n    lazyLoadState: any = {};\n\n    loaderArr: any[] = [];\n\n    spacerStyle: { [klass: string]: any } | null | undefined = {};\n\n    contentStyle: { [klass: string]: any } | null | undefined = {};\n\n    scrollTimeout: any;\n\n    resizeTimeout: any;\n\n    initialized: boolean = false;\n\n    windowResizeListener: VoidListener;\n\n    defaultWidth: number | undefined;\n\n    defaultHeight: number | undefined;\n\n    defaultContentWidth: number | undefined;\n\n    defaultContentHeight: number | undefined;\n\n    get vertical() {\n        return this._orientation === 'vertical';\n    }\n\n    get horizontal() {\n        return this._orientation === 'horizontal';\n    }\n\n    get both() {\n        return this._orientation === 'both';\n    }\n\n    get loadedItems() {\n        if (this._items && !this.d_loading) {\n            if (this.both) return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map((item) => (this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols)));\n            else if (this.horizontal && this._columns) return this._items;\n            else return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n        }\n\n        return [];\n    }\n\n    get loadedRows() {\n        return this.d_loading ? (this._loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n    }\n\n    get loadedColumns() {\n        if (this._columns && (this.both || this.horizontal)) {\n            return this.d_loading && this._loaderDisabled ? (this.both ? this.loaderArr[0] : this.loaderArr) : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n        }\n\n        return this._columns;\n    }\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, private cd: ChangeDetectorRef, private zone: NgZone) {}\n\n    ngOnInit() {\n        this.setInitialState();\n    }\n\n    ngOnChanges(simpleChanges: SimpleChanges) {\n        let isLoadingChanged = false;\n\n        if (simpleChanges.loading) {\n            const { previousValue, currentValue } = simpleChanges.loading;\n\n            if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n                this.d_loading = currentValue;\n                isLoadingChanged = true;\n            }\n        }\n\n        if (simpleChanges.orientation) {\n            this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        }\n\n        if (simpleChanges.numToleratedItems) {\n            const { previousValue, currentValue } = simpleChanges.numToleratedItems;\n\n            if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue;\n            }\n        }\n\n        if (simpleChanges.options) {\n            const { previousValue, currentValue } = simpleChanges.options;\n\n            if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n                this.d_loading = currentValue.loading;\n                isLoadingChanged = true;\n            }\n\n            if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue.numToleratedItems;\n            }\n        }\n\n        if (this.initialized) {\n            const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n\n            if (isChanged) {\n                this.init();\n                this.calculateAutoSize();\n            }\n        }\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n\n                case 'loadericon':\n                    this.loaderIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngAfterViewInit() {\n        Promise.resolve().then(() => {\n            this.viewInit();\n        });\n    }\n\n    ngAfterViewChecked() {\n        if (!this.initialized) {\n            this.viewInit();\n        }\n    }\n\n    ngOnDestroy() {\n        this.unbindResizeListener();\n\n        this.contentEl = null;\n        this.initialized = false;\n    }\n\n    viewInit() {\n        if (isPlatformBrowser(this.platformId) && !this.initialized) {\n            if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n                this.setInitialState();\n                this.setContentEl(this.contentEl);\n                this.init();\n                this.calculateAutoSize();\n\n                this.defaultWidth = DomHandler.getWidth(this.elementViewChild?.nativeElement);\n                this.defaultHeight = DomHandler.getHeight(this.elementViewChild?.nativeElement);\n                this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n                this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n                this.initialized = true;\n            }\n        }\n    }\n\n    init() {\n        if (!this._disabled) {\n            this.setSize();\n            this.calculateOptions();\n            this.setSpacerSize();\n            this.bindResizeListener();\n\n            this.cd.detectChanges();\n        }\n    }\n\n    setContentEl(el?: HTMLElement) {\n        this.contentEl = el || this.contentViewChild?.nativeElement || DomHandler.findSingle(this.elementViewChild?.nativeElement, '.p-scroller-content');\n    }\n\n    setInitialState() {\n        this.first = this.both ? { rows: 0, cols: 0 } : 0;\n        this.last = this.both ? { rows: 0, cols: 0 } : 0;\n        this.numItemsInViewport = this.both ? { rows: 0, cols: 0 } : 0;\n        this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.d_loading = this._loading || false;\n        this.d_numToleratedItems = this._numToleratedItems;\n        this.loaderArr = [];\n        this.spacerStyle = {};\n        this.contentStyle = {};\n    }\n\n    getElementRef() {\n        return this.elementViewChild;\n    }\n\n    getPageByFirst(first?: any) {\n        return Math.floor(((first ?? this.first) + this.d_numToleratedItems * 4) / (this._step || 1));\n    }\n\n    isPageChanged(first?: any) {\n        return this._step ? this.page !== this.getPageByFirst(first ?? this.first) : true;\n    }\n\n    scrollTo(options: ScrollToOptions) {\n        // this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.elementViewChild?.nativeElement?.scrollTo(options);\n    }\n\n    scrollToIndex(index: number | number[], behavior: ScrollBehavior = 'auto') {\n        const valid = this.both ? (index as number[]).every((i) => i > -1) : (index as number) > -1;\n\n        if (valid) {\n            const first = this.first;\n            const { scrollTop = 0, scrollLeft = 0 } = this.elementViewChild?.nativeElement;\n            const { numToleratedItems } = this.calculateNumItems();\n            const contentPos = this.getContentPosition();\n            const itemSize = this.itemSize;\n            const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n            const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n            const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n            let newFirst = this.both ? { rows: 0, cols: 0 } : 0;\n            let isRangeChanged = false,\n                isScrollChanged = false;\n\n            if (this.both) {\n                newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n                scrollTo(calculateCoord(newFirst.cols, itemSize[1], contentPos.left), calculateCoord(newFirst.rows, itemSize[0], contentPos.top));\n                isScrollChanged = this.lastScrollPos.top !== scrollTop || this.lastScrollPos.left !== scrollLeft;\n                isRangeChanged = newFirst.rows !== first.rows || newFirst.cols !== first.cols;\n            } else {\n                newFirst = calculateFirst(index as number, numToleratedItems);\n                this.horizontal ? scrollTo(calculateCoord(newFirst, itemSize, contentPos.left), scrollTop) : scrollTo(scrollLeft, calculateCoord(newFirst, itemSize, contentPos.top));\n                isScrollChanged = this.lastScrollPos !== (this.horizontal ? scrollLeft : scrollTop);\n                isRangeChanged = newFirst !== first;\n            }\n\n            this.isRangeChanged = isRangeChanged;\n            isScrollChanged && (this.first = newFirst);\n        }\n    }\n\n    scrollInView(index: number, to: ScrollerToType, behavior: ScrollBehavior = 'auto') {\n        if (to) {\n            const { first, viewport } = this.getRenderedRange();\n            const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n            const isToStart = to === 'to-start';\n            const isToEnd = to === 'to-end';\n\n            if (isToStart) {\n                if (this.both) {\n                    if (viewport.first.rows - first.rows > (<any>index)[0]) {\n                        scrollTo(viewport.first.cols * (<number[]>this._itemSize)[1], (viewport.first.rows - 1) * (<number[]>this._itemSize)[0]);\n                    } else if (viewport.first.cols - first.cols > (<any>index)[1]) {\n                        scrollTo((viewport.first.cols - 1) * (<number[]>this._itemSize)[1], viewport.first.rows * (<number[]>this._itemSize)[0]);\n                    }\n                } else {\n                    if (viewport.first - first > index) {\n                        const pos = (viewport.first - 1) * <number>this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            } else if (isToEnd) {\n                if (this.both) {\n                    if (viewport.last.rows - first.rows <= (<any>index)[0] + 1) {\n                        scrollTo(viewport.first.cols * (<number[]>this._itemSize)[1], (viewport.first.rows + 1) * (<number[]>this._itemSize)[0]);\n                    } else if (viewport.last.cols - first.cols <= (<any>index)[1] + 1) {\n                        scrollTo((viewport.first.cols + 1) * (<number[]>this._itemSize)[1], viewport.first.rows * (<number[]>this._itemSize)[0]);\n                    }\n                } else {\n                    if (viewport.last - first <= index + 1) {\n                        const pos = (viewport.first + 1) * <number>this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n        } else {\n            this.scrollToIndex(index, behavior);\n        }\n    }\n\n    getRenderedRange() {\n        const calculateFirstInViewport = (_pos: number, _size: number) => (_size || _pos ? Math.floor(_pos / (_size || _pos)) : 0);\n\n        let firstInViewport = this.first;\n        let lastInViewport: any = 0;\n\n        if (this.elementViewChild?.nativeElement) {\n            const { scrollTop, scrollLeft } = this.elementViewChild.nativeElement;\n\n            if (this.both) {\n                firstInViewport = { rows: calculateFirstInViewport(scrollTop, (<number[]>this._itemSize)[0]), cols: calculateFirstInViewport(scrollLeft, (<number[]>this._itemSize)[1]) };\n                lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n            } else {\n                const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n                firstInViewport = calculateFirstInViewport(scrollPos, <number>this._itemSize);\n                lastInViewport = firstInViewport + this.numItemsInViewport;\n            }\n        }\n\n        return {\n            first: this.first,\n            last: this.last,\n            viewport: {\n                first: firstInViewport,\n                last: lastInViewport\n            }\n        };\n    }\n\n    calculateNumItems() {\n        const contentPos = this.getContentPosition();\n        const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n        const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n        const calculateNumItemsInViewport = (_contentSize: number, _itemSize: number) => (_itemSize || _contentSize ? Math.ceil(_contentSize / (_itemSize || _contentSize)) : 0);\n        const calculateNumToleratedItems = (_numItems: number) => Math.ceil(_numItems / 2);\n        const numItemsInViewport: any = this.both\n            ? { rows: calculateNumItemsInViewport(contentHeight, (<number[]>this._itemSize)[0]), cols: calculateNumItemsInViewport(contentWidth, (<number[]>this._itemSize)[1]) }\n            : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, <number>this._itemSize);\n\n        const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n\n        return { numItemsInViewport, numToleratedItems };\n    }\n\n    calculateOptions() {\n        const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n        const calculateLast = (_first: number, _num: number, _numT: number, _isCols: boolean = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n        const first = this.first;\n        const last = this.both\n            ? { rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true) }\n            : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n\n        this.last = last;\n        this.numItemsInViewport = numItemsInViewport;\n        this.d_numToleratedItems = numToleratedItems;\n\n        if (this.showLoader) {\n            this.loaderArr = this.both ? Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) : Array.from({ length: numItemsInViewport });\n        }\n\n        if (this._lazy) {\n            Promise.resolve().then(() => {\n                this.lazyLoadState = {\n                    first: this._step ? (this.both ? { rows: 0, cols: first.cols } : 0) : first,\n                    last: Math.min(this._step ? this._step : this.last, (<any[]>this.items).length)\n                };\n\n                this.handleEvents('onLazyLoad', this.lazyLoadState);\n            });\n        }\n    }\n\n    calculateAutoSize() {\n        if (this._autoSize && !this.d_loading) {\n            Promise.resolve().then(() => {\n                if (this.contentEl) {\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n                    this.contentEl.style.position = 'relative';\n                    (<ElementRef>this.elementViewChild).nativeElement.style.contain = 'none';\n\n                    const [contentWidth, contentHeight] = [DomHandler.getWidth(this.contentEl), DomHandler.getHeight(this.contentEl)];\n                    contentWidth !== this.defaultContentWidth && ((<ElementRef>this.elementViewChild).nativeElement.style.width = '');\n                    contentHeight !== this.defaultContentHeight && ((<ElementRef>this.elementViewChild).nativeElement.style.height = '');\n\n                    const [width, height] = [DomHandler.getWidth((<ElementRef>this.elementViewChild).nativeElement), DomHandler.getHeight((<ElementRef>this.elementViewChild).nativeElement)];\n                    (this.both || this.horizontal) && ((<ElementRef>this.elementViewChild).nativeElement.style.width = width < <number>this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n                    (this.both || this.vertical) && ((<ElementRef>this.elementViewChild).nativeElement.style.height = height < <number>this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n                    this.contentEl.style.position = '';\n                    (<ElementRef>this.elementViewChild).nativeElement.style.contain = '';\n                }\n            });\n        }\n    }\n\n    getLast(last = 0, isCols = false) {\n        return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n    }\n\n    getContentPosition() {\n        if (this.contentEl) {\n            const style = getComputedStyle(this.contentEl);\n            const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n            const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n            const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n            const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n\n            return { left, right, top, bottom, x: left + right, y: top + bottom };\n        }\n\n        return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n    }\n\n    setSize() {\n        if (this.elementViewChild?.nativeElement) {\n            const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n            const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n            const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n            const setProp = (_name: string, _value: any) => ((<ElementRef>this.elementViewChild).nativeElement.style[_name] = _value);\n\n            if (this.both || this.horizontal) {\n                setProp('height', height);\n                setProp('width', width);\n            } else {\n                setProp('height', height);\n            }\n        }\n    }\n\n    setSpacerSize() {\n        if (this._items) {\n            const contentPos = this.getContentPosition();\n            const setProp = (_name: string, _value: any, _size: number, _cpos: number = 0) => (this.spacerStyle = { ...this.spacerStyle, ...{ [`${_name}`]: (_value || []).length * _size + _cpos + 'px' } });\n\n            if (this.both) {\n                setProp('height', this._items, (<number[]>this._itemSize)[0], contentPos.y);\n                setProp('width', this._columns || this._items[1], (<number[]>this._itemSize)[1], contentPos.x);\n            } else {\n                this.horizontal ? setProp('width', this._columns || this._items, <number>this._itemSize, contentPos.x) : setProp('height', this._items, <number>this._itemSize, contentPos.y);\n            }\n        }\n    }\n\n    setContentPosition(pos: any) {\n        if (this.contentEl && !this._appendOnly) {\n            const first = pos ? pos.first : this.first;\n            const calculateTranslateVal = (_first: number, _size: number) => _first * _size;\n            const setTransform = (_x = 0, _y = 0) => (this.contentStyle = { ...this.contentStyle, ...{ transform: `translate3d(${_x}px, ${_y}px, 0)` } });\n\n            if (this.both) {\n                setTransform(calculateTranslateVal(first.cols, (<number[]>this._itemSize)[1]), calculateTranslateVal(first.rows, (<number[]>this._itemSize)[0]));\n            } else {\n                const translateVal = calculateTranslateVal(first, <number>this._itemSize);\n                this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n            }\n        }\n    }\n\n    onScrollPositionChange(event: Event) {\n        const target = event.target;\n        const contentPos = this.getContentPosition();\n        const calculateScrollPos = (_pos: number, _cpos: number) => (_pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0);\n        const calculateCurrentIndex = (_pos: number, _size: number) => (_size || _pos ? Math.floor(_pos / (_size || _pos)) : 0);\n        const calculateTriggerIndex = (_currentIndex: number, _first: number, _last: number, _num: number, _numT: number, _isScrollDownOrRight: any) => {\n            return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n        };\n        const calculateFirst = (_currentIndex: number, _triggerIndex: number, _first: number, _last: number, _num: number, _numT: number, _isScrollDownOrRight: any) => {\n            if (_currentIndex <= _numT) return 0;\n            else return Math.max(0, _isScrollDownOrRight ? (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n        };\n        const calculateLast = (_currentIndex: number, _first: number, _last: number, _num: number, _numT: number, _isCols = false) => {\n            let lastValue = _first + _num + 2 * _numT;\n\n            if (_currentIndex >= _numT) {\n                lastValue += _numT + 1;\n            }\n\n            return this.getLast(lastValue, _isCols);\n        };\n\n        const scrollTop = calculateScrollPos((<HTMLElement>target).scrollTop, contentPos.top);\n        const scrollLeft = calculateScrollPos((<HTMLElement>target).scrollLeft, contentPos.left);\n\n        let newFirst = this.both ? { rows: 0, cols: 0 } : 0;\n        let newLast = this.last;\n        let isRangeChanged = false;\n        let newScrollPos = this.lastScrollPos;\n\n        if (this.both) {\n            const isScrollDown = this.lastScrollPos.top <= scrollTop;\n            const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n\n            if (!this._appendOnly || (this._appendOnly && (isScrollDown || isScrollRight))) {\n                const currentIndex = { rows: calculateCurrentIndex(scrollTop, (<number[]>this._itemSize)[0]), cols: calculateCurrentIndex(scrollLeft, (<number[]>this._itemSize)[1]) };\n                const triggerIndex = {\n                    rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                    cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                };\n\n                newFirst = {\n                    rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                    cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                };\n                newLast = {\n                    rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                    cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n                };\n\n                isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n                newScrollPos = { top: scrollTop, left: scrollLeft };\n            }\n        } else {\n            const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n            const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n\n            if (!this._appendOnly || (this._appendOnly && isScrollDownOrRight)) {\n                const currentIndex = calculateCurrentIndex(scrollPos, <number>this._itemSize);\n                const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n\n                newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n                isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n                newScrollPos = scrollPos;\n            }\n        }\n\n        return {\n            first: newFirst,\n            last: newLast,\n            isRangeChanged,\n            scrollPos: newScrollPos\n        };\n    }\n\n    onScrollChange(event: Event) {\n        const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n\n        if (isRangeChanged) {\n            const newState = { first, last };\n\n            this.setContentPosition(newState);\n\n            this.first = first;\n            this.last = last;\n            this.lastScrollPos = scrollPos;\n\n            this.handleEvents('onScrollIndexChange', newState);\n\n            if (this._lazy && this.isPageChanged(first)) {\n                const lazyLoadState = {\n                    first: this._step ? Math.min(this.getPageByFirst(first) * this._step, (<any[]>this.items).length - this._step) : first,\n                    last: Math.min(this._step ? (this.getPageByFirst(first) + 1) * this._step : last, (<any[]>this.items).length)\n                };\n                const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n\n                isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n                this.lazyLoadState = lazyLoadState;\n            }\n        }\n    }\n\n    onContainerScroll(event: Event) {\n        this.handleEvents('onScroll', { originalEvent: event });\n\n        if (this._delay && this.isPageChanged()) {\n            if (this.scrollTimeout) {\n                clearTimeout(this.scrollTimeout);\n            }\n\n            if (!this.d_loading && this.showLoader) {\n                const { isRangeChanged } = this.onScrollPositionChange(event);\n                const changed = isRangeChanged || (this._step ? this.isPageChanged() : false);\n\n                if (changed) {\n                    this.d_loading = true;\n\n                    this.cd.detectChanges();\n                }\n            }\n\n            this.scrollTimeout = setTimeout(() => {\n                this.onScrollChange(event);\n\n                if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n                    this.d_loading = false;\n                    this.page = this.getPageByFirst();\n                    this.cd.detectChanges();\n                }\n            }, this._delay);\n        } else {\n            !this.d_loading && this.onScrollChange(event);\n        }\n    }\n\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.windowResizeListener) {\n                this.zone.runOutsideAngular(() => {\n                    const window = this.document.defaultView as Window;\n                    const event = DomHandler.isTouchDevice() ? 'orientationchange' : 'resize';\n                    this.windowResizeListener = this.renderer.listen(window, event, this.onWindowResize.bind(this));\n                });\n            }\n        }\n    }\n\n    unbindResizeListener() {\n        if (this.windowResizeListener) {\n            this.windowResizeListener();\n            this.windowResizeListener = null;\n        }\n    }\n\n    onWindowResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n        }\n\n        this.resizeTimeout = setTimeout(() => {\n            if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n                const [width, height] = [DomHandler.getWidth(this.elementViewChild?.nativeElement), DomHandler.getHeight(this.elementViewChild?.nativeElement)];\n                const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n\n                reinit &&\n                    this.zone.run(() => {\n                        this.d_numToleratedItems = this._numToleratedItems;\n                        this.defaultWidth = width;\n                        this.defaultHeight = height;\n                        this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n                        this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n\n                        this.init();\n                        this.calculateAutoSize();\n                    });\n            }\n        }, this._resizeDelay);\n    }\n\n    handleEvents(name: string, params: any) {\n        //@ts-ignore\n        return this.options && (<any>this.options)[name] ? (<any>this.options)[name](params) : this[name].emit(params);\n    }\n\n    getContentOptions() {\n        return {\n            contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n            items: this.loadedItems,\n            getItemOptions: (index: number) => this.getOptions(index),\n            loading: this.d_loading,\n            getLoaderOptions: (index: number, options?: any) => this.getLoaderOptions(index, options),\n            itemSize: this._itemSize,\n            rows: this.loadedRows,\n            columns: this.loadedColumns,\n            spacerStyle: this.spacerStyle,\n            contentStyle: this.contentStyle,\n            vertical: this.vertical,\n            horizontal: this.horizontal,\n            both: this.both\n        };\n    }\n\n    getOptions(renderedIndex: number) {\n        const count = (this._items || []).length;\n        const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === count - 1,\n            even: index % 2 === 0,\n            odd: index % 2 !== 0\n        };\n    }\n\n    getLoaderOptions(index: number, extOptions: any) {\n        const count = this.loaderArr.length;\n\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === count - 1,\n            even: index % 2 === 0,\n            odd: index % 2 !== 0,\n            ...extOptions\n        };\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, SpinnerIcon],\n    exports: [Scroller, SharedModule],\n    declarations: [Scroller]\n})\nexport class ScrollerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;AA8BA;;;AAGG;MA0DU,QAAQ,CAAA;AA+ZqB,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA6B,IAAA,EAAA,CAAA;AAA+B,IAAA,IAAA,CAAA;AA9Z5K;;;AAGG;AACH,IAAA,IAAa,EAAE,GAAA;QACX,OAAO,IAAI,CAAC,GAAG,CAAC;KACnB;IACD,IAAI,EAAE,CAAC,GAAuB,EAAA;AAC1B,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;KAClB;AACD;;;AAGG;AACH,IAAA,IAAa,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;IACD,IAAI,KAAK,CAAC,GAAQ,EAAA;AACd,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;KACrB;AACD;;;AAGG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAuB,EAAA;AAClC,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KAC1B;AACD;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,GAAW,EAAA;AACpB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KACxB;AACD;;;AAGG;AACH,IAAA,IAAa,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;IACD,IAAI,KAAK,CAAC,GAA6B,EAAA;AACnC,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;KACrB;AACD;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,GAAsB,EAAA;AAC/B,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KACxB;AACD;;;AAGG;AACH,IAAA,IAAa,YAAY,GAAA;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC;KAC7B;IACD,IAAI,YAAY,CAAC,GAAuB,EAAA;AACpC,QAAA,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;KAC5B;AACD;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IACD,IAAI,WAAW,CAAC,GAAuB,EAAA;AACnC,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;KAC3B;AACD;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IACD,IAAI,WAAW,CAAC,GAAuC,EAAA;AACnD,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;KAC3B;AACD;;;AAGG;AACH,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;IACD,IAAI,IAAI,CAAC,GAAW,EAAA;AAChB,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;KACpB;AACD;;;AAGG;AACH,IAAA,IAAa,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;IACD,IAAI,KAAK,CAAC,GAAW,EAAA;AACjB,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;KACrB;AACD;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IACD,IAAI,WAAW,CAAC,GAAW,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;KAC3B;AACD;;;AAGG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAY,EAAA;AACvB,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KAC1B;AACD;;;AAGG;AACH,IAAA,IAAa,MAAM,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;IACD,IAAI,MAAM,CAAC,GAAY,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;KACtB;AACD;;;AAGG;AACH,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;IACD,IAAI,IAAI,CAAC,GAAY,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;KACpB;AACD;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,GAAY,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KACxB;AACD;;;AAGG;AACH,IAAA,IAAa,cAAc,GAAA;QACvB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;IACD,IAAI,cAAc,CAAC,GAAY,EAAA;AAC3B,QAAA,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;KAC9B;AACD;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,GAA6B,EAAA;AACrC,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;KACvB;AACD;;;AAGG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAY,EAAA;AACvB,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KAC1B;AACD;;;AAGG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAY,EAAA;AACvB,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KAC1B;AACD;;;AAGG;AACH,IAAA,IAAa,iBAAiB,GAAA;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAClC;IACD,IAAI,iBAAiB,CAAC,GAAW,EAAA;AAC7B,QAAA,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;KACjC;AACD;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,GAAwB,EAAA;AAChC,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;KACvB;AACD;;;AAGG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,GAAY,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KACxB;AACD;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,GAAa,EAAA;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;KACvB;AACD;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,GAAgC,EAAA;AACxC,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AAEpB,QAAA,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;;AAEhC,YAAA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAA,CAAA,EAAI,CAAC,CAAE,CAAA,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,CAAA,EAAI,CAAC,CAAE,CAAA,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvF,SAAA;KACJ;AACD;;;;AAIG;AACO,IAAA,UAAU,GAAwC,IAAI,YAAY,EAAyB,CAAC;AACtG;;;;AAIG;AACO,IAAA,QAAQ,GAAsC,IAAI,YAAY,EAAuB,CAAC;AAChG;;;;AAIG;AACO,IAAA,mBAAmB,GAAiD,IAAI,YAAY,EAAkC,CAAC;AAE3G,IAAA,gBAAgB,CAAuB;AAEvC,IAAA,gBAAgB,CAAuB;AAE7B,IAAA,SAAS,CAAqC;AAE9E,IAAA,GAAG,CAAqB;AAExB,IAAA,MAAM,CAA8C;AAEpD,IAAA,WAAW,CAAqB;IAEhC,SAAS,GAAW,CAAC,CAAC;AAEtB,IAAA,MAAM,CAA2B;IAEjC,SAAS,GAAsB,CAAC,CAAC;AAEjC,IAAA,aAAa,CAAqB;AAElC,IAAA,YAAY,CAAqB;IAEjC,YAAY,GAAuC,UAAU,CAAC;IAE9D,KAAK,GAAW,CAAC,CAAC;IAElB,MAAM,GAAW,CAAC,CAAC;IAEnB,YAAY,GAAW,EAAE,CAAC;IAE1B,WAAW,GAAY,KAAK,CAAC;IAE7B,OAAO,GAAY,KAAK,CAAC;IAEzB,KAAK,GAAY,KAAK,CAAC;IAEvB,SAAS,GAAY,KAAK,CAAC;IAE3B,eAAe,GAAY,KAAK,CAAC;AAEjC,IAAA,QAAQ,CAA2B;IAEnC,WAAW,GAAY,IAAI,CAAC;IAE5B,WAAW,GAAY,KAAK,CAAC;AAE7B,IAAA,kBAAkB,CAAM;AAExB,IAAA,QAAQ,CAAsB;IAE9B,SAAS,GAAY,KAAK,CAAC;AAE3B,IAAA,QAAQ,CAAM;AAEd,IAAA,QAAQ,CAA8B;IAEtC,SAAS,GAAY,KAAK,CAAC;AAE3B,IAAA,mBAAmB,CAAM;AAEzB,IAAA,SAAS,CAAM;AAEf,IAAA,eAAe,CAA6B;AAE5C,IAAA,YAAY,CAA6B;AAEzC,IAAA,cAAc,CAA6B;AAE3C,IAAA,kBAAkB,CAA6B;IAE/C,KAAK,GAAQ,CAAC,CAAC;IAEf,IAAI,GAAQ,CAAC,CAAC;IAEd,IAAI,GAAW,CAAC,CAAC;IAEjB,cAAc,GAAY,KAAK,CAAC;IAEhC,kBAAkB,GAAQ,CAAC,CAAC;IAE5B,aAAa,GAAQ,CAAC,CAAC;IAEvB,aAAa,GAAQ,EAAE,CAAC;IAExB,SAAS,GAAU,EAAE,CAAC;IAEtB,WAAW,GAAgD,EAAE,CAAC;IAE9D,YAAY,GAAgD,EAAE,CAAC;AAE/D,IAAA,aAAa,CAAM;AAEnB,IAAA,aAAa,CAAM;IAEnB,WAAW,GAAY,KAAK,CAAC;AAE7B,IAAA,oBAAoB,CAAe;AAEnC,IAAA,YAAY,CAAqB;AAEjC,IAAA,aAAa,CAAqB;AAElC,IAAA,mBAAmB,CAAqB;AAExC,IAAA,oBAAoB,CAAqB;AAEzC,IAAA,IAAI,QAAQ,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU,CAAC;KAC3C;AAED,IAAA,IAAI,UAAU,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC;KAC7C;AAED,IAAA,IAAI,IAAI,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,YAAY,KAAK,MAAM,CAAC;KACvC;AAED,IAAA,IAAI,WAAW,GAAA;QACX,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAChC,IAAI,IAAI,CAAC,IAAI;AAAE,gBAAA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtM,iBAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ;gBAAE,OAAO,IAAI,CAAC,MAAM,CAAC;;gBACzD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/E,SAAA;AAED,QAAA,OAAO,EAAE,CAAC;KACb;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,GAAG,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC;KAC3F;AAED,IAAA,IAAI,aAAa,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE;AACjD,YAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5M,SAAA;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IAED,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAU,QAAmB,EAAU,EAAqB,EAAU,IAAY,EAAA;QAAlJ,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;KAAI;IAE5L,QAAQ,GAAA;QACJ,IAAI,CAAC,eAAe,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,aAA4B,EAAA;QACpC,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAE7B,IAAI,aAAa,CAAC,OAAO,EAAE;YACvB,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,OAAO,CAAC;AAE9D,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,aAAa,KAAK,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC,SAAS,EAAE;AAChF,gBAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;gBAC9B,gBAAgB,GAAG,IAAI,CAAC;AAC3B,aAAA;AACJ,SAAA;QAED,IAAI,aAAa,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5D,SAAA;QAED,IAAI,aAAa,CAAC,iBAAiB,EAAE;YACjC,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,iBAAiB,CAAC;YAExE,IAAI,aAAa,KAAK,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC,mBAAmB,EAAE;AAC7E,gBAAA,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC;AAC3C,aAAA;AACJ,SAAA;QAED,IAAI,aAAa,CAAC,OAAO,EAAE;YACvB,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC,OAAO,CAAC;AAE9D,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,aAAa,EAAE,OAAO,KAAK,YAAY,EAAE,OAAO,IAAI,YAAY,EAAE,OAAO,KAAK,IAAI,CAAC,SAAS,EAAE;AAC3G,gBAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC;gBACtC,gBAAgB,GAAG,IAAI,CAAC;AAC3B,aAAA;AAED,YAAA,IAAI,aAAa,EAAE,iBAAiB,KAAK,YAAY,EAAE,iBAAiB,IAAI,YAAY,EAAE,iBAAiB,KAAK,IAAI,CAAC,mBAAmB,EAAE;AACtI,gBAAA,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,iBAAiB,CAAC;AAC7D,aAAA;AACJ,SAAA;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,MAAM,SAAS,GAAG,CAAC,gBAAgB,KAAK,aAAa,CAAC,KAAK,EAAE,aAAa,EAAE,MAAM,KAAK,aAAa,CAAC,KAAK,EAAE,YAAY,EAAE,MAAM,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,WAAW,CAAC,CAAC;AAEvN,YAAA,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5B,aAAA;AACJ,SAAA;KACJ;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;AACX,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;YACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpB,SAAC,CAAC,CAAC;KACN;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAE5B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC5B;IAED,QAAQ,GAAA;QACJ,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACzD,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAE;gBAC5D,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,gBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAClC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAEzB,gBAAA,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;AAC9E,gBAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;gBAChF,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/D,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjE,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC3B,aAAA;AACJ,SAAA;KACJ;IAED,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAE1B,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC3B,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,EAAgB,EAAA;QACzB,IAAI,CAAC,SAAS,GAAG,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE,aAAa,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE,qBAAqB,CAAC,CAAC;KACrJ;IAED,eAAe,GAAA;QACX,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;AACxC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACnD,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;KAC1B;IAED,aAAa,GAAA;QACT,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAChC;AAED,IAAA,cAAc,CAAC,KAAW,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;KACjG;AAED,IAAA,aAAa,CAAC,KAAW,EAAA;QACrB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACrF;AAED,IAAA,QAAQ,CAAC,OAAwB,EAAA;;QAE7B,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;KAC3D;AAED,IAAA,aAAa,CAAC,KAAwB,EAAE,QAAA,GAA2B,MAAM,EAAA;AACrE,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAI,KAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAI,KAAgB,GAAG,CAAC,CAAC,CAAC;AAE5F,QAAA,IAAI,KAAK,EAAE;AACP,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACzB,YAAA,MAAM,EAAE,SAAS,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC;YAC/E,MAAM,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACvD,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7C,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,MAAM,cAAc,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,MAAM,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAC7E,YAAA,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,KAAK,MAAM,GAAG,KAAK,GAAG,KAAK,CAAC;YACxE,MAAM,QAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/E,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AACpD,YAAA,IAAI,cAAc,GAAG,KAAK,EACtB,eAAe,GAAG,KAAK,CAAC;YAE5B,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,gBAAA,QAAQ,GAAG,EAAE,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1H,gBAAA,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AAClI,gBAAA,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,UAAU,CAAC;AACjG,gBAAA,cAAc,GAAG,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;AACjF,aAAA;AAAM,iBAAA;AACH,gBAAA,QAAQ,GAAG,cAAc,CAAC,KAAe,EAAE,iBAAiB,CAAC,CAAC;AAC9D,gBAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACtK,gBAAA,eAAe,GAAG,IAAI,CAAC,aAAa,MAAM,IAAI,CAAC,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC,CAAC;AACpF,gBAAA,cAAc,GAAG,QAAQ,KAAK,KAAK,CAAC;AACvC,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;YACrC,eAAe,KAAK,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;AAC9C,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAa,EAAE,EAAkB,EAAE,WAA2B,MAAM,EAAA;AAC7E,QAAA,IAAI,EAAE,EAAE;YACJ,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,QAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC/E,YAAA,MAAM,SAAS,GAAG,EAAE,KAAK,UAAU,CAAC;AACpC,YAAA,MAAM,OAAO,GAAG,EAAE,KAAK,QAAQ,CAAC;AAEhC,YAAA,IAAI,SAAS,EAAE;gBACX,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,oBAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAS,KAAM,CAAC,CAAC,CAAC,EAAE;AACpD,wBAAA,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAc,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAe,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5H,qBAAA;AAAM,yBAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAS,KAAM,CAAC,CAAC,CAAC,EAAE;AAC3D,wBAAA,QAAQ,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAe,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAc,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5H,qBAAA;AACJ,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,EAAE;AAChC,wBAAA,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAY,IAAI,CAAC,SAAS,CAAC;wBAC1D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACzD,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAAM,iBAAA,IAAI,OAAO,EAAE;gBAChB,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,oBAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAU,KAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACxD,wBAAA,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAc,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAe,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5H,qBAAA;AAAM,yBAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAU,KAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC/D,wBAAA,QAAQ,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAe,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAc,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5H,qBAAA;AACJ,iBAAA;AAAM,qBAAA;oBACH,IAAI,QAAQ,CAAC,IAAI,GAAG,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE;AACpC,wBAAA,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAY,IAAI,CAAC,SAAS,CAAC;wBAC1D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACzD,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACvC,SAAA;KACJ;IAED,gBAAgB,GAAA;AACZ,QAAA,MAAM,wBAAwB,GAAG,CAAC,IAAY,EAAE,KAAa,MAAM,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE3H,QAAA,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC;QACjC,IAAI,cAAc,GAAQ,CAAC,CAAC;AAE5B,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE;YACtC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;YAEtE,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,gBAAA,eAAe,GAAG,EAAE,IAAI,EAAE,wBAAwB,CAAC,SAAS,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,wBAAwB,CAAC,UAAU,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1K,cAAc,GAAG,EAAE,IAAI,EAAE,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;AAC7I,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC;gBAC3D,eAAe,GAAG,wBAAwB,CAAC,SAAS,EAAU,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9E,gBAAA,cAAc,GAAG,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAC9D,aAAA;AACJ,SAAA;QAED,OAAO;YACH,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,YAAA,QAAQ,EAAE;AACN,gBAAA,KAAK,EAAE,eAAe;AACtB,gBAAA,IAAI,EAAE,cAAc;AACvB,aAAA;SACJ,CAAC;KACL;IAED,iBAAiB,GAAA;AACb,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7C,QAAA,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,WAAW,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AACzI,QAAA,MAAM,aAAa,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1I,QAAA,MAAM,2BAA2B,GAAG,CAAC,YAAoB,EAAE,SAAiB,MAAM,SAAS,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzK,QAAA,MAAM,0BAA0B,GAAG,CAAC,SAAiB,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AACnF,QAAA,MAAM,kBAAkB,GAAQ,IAAI,CAAC,IAAI;AACrC,cAAE,EAAE,IAAI,EAAE,2BAA2B,CAAC,aAAa,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,2BAA2B,CAAC,YAAY,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,EAAE;AACrK,cAAE,2BAA2B,CAAC,IAAI,CAAC,UAAU,GAAG,YAAY,GAAG,aAAa,EAAU,IAAI,CAAC,SAAS,CAAC,CAAC;AAE1G,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,0BAA0B,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,GAAG,0BAA0B,CAAC,kBAAkB,CAAC,CAAC,CAAC;AAEhO,QAAA,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,CAAC;KACpD;IAED,gBAAgB,GAAA;QACZ,MAAM,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC3E,QAAA,MAAM,aAAa,GAAG,CAAC,MAAc,EAAE,IAAY,EAAE,KAAa,EAAE,OAAmB,GAAA,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,OAAO,CAAC,CAAC;AACzK,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACzB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;AAClB,cAAE,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,kBAAkB,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,kBAAkB,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;cAClL,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;AAEvE,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAC7C,QAAA,IAAI,CAAC,mBAAmB,GAAG,iBAAiB,CAAC;QAE7C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC,CAAC;AACxL,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBACxB,IAAI,CAAC,aAAa,GAAG;AACjB,oBAAA,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK;oBAC3E,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,EAAU,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC;iBAClF,CAAC;gBAEF,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACxD,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,iBAAiB,GAAA;QACb,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnC,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBACxB,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;oBACxE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;oBAC9B,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;oBAEzE,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAClH,oBAAA,YAAY,KAAK,IAAI,CAAC,mBAAmB,KAAkB,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AAClH,oBAAA,aAAa,KAAK,IAAI,CAAC,oBAAoB,KAAkB,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;AAErH,oBAAA,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAc,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,EAAE,UAAU,CAAC,SAAS,CAAc,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC;oBAC1K,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,MAAmB,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAW,IAAI,CAAC,YAAY,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;oBACrM,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,MAAmB,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAW,IAAI,CAAC,aAAa,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;AAEzM,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACpE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACtB,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AACxE,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,MAAM,GAAG,KAAK,EAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;KACnH;IAED,kBAAkB,GAAA;QACd,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC/C,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACtF,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACzF,MAAM,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACnF,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAE5F,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC;AACzE,SAAA;QAED,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;KAC/D;IAED,OAAO,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC;AACtF,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,WAAW,IAAI,CAAC;AACvH,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,YAAY,IAAI,CAAC;YAC3H,MAAM,OAAO,GAAG,CAAC,KAAa,EAAE,MAAW,MAAmB,IAAI,CAAC,gBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;AAE1H,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;AAC9B,gBAAA,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC1B,gBAAA,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3B,aAAA;AAAM,iBAAA;AACH,gBAAA,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC7B,aAAA;AACJ,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,CAAC,KAAa,EAAE,MAAW,EAAE,KAAa,EAAE,KAAgB,GAAA,CAAC,MAAM,IAAI,CAAC,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,CAAG,EAAA,KAAK,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,EAAE,EAAE,CAAC,CAAC;YAElM,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,gBAAA,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC5E,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AAClG,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAU,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAU,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AACjL,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,kBAAkB,CAAC,GAAQ,EAAA;QACvB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrC,YAAA,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3C,YAAA,MAAM,qBAAqB,GAAG,CAAC,MAAc,EAAE,KAAa,KAAK,MAAM,GAAG,KAAK,CAAC;AAChF,YAAA,MAAM,YAAY,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,SAAS,EAAE,CAAe,YAAA,EAAA,EAAE,OAAO,EAAE,CAAA,MAAA,CAAQ,EAAE,EAAE,CAAC,CAAC;YAE9I,IAAI,IAAI,CAAC,IAAI,EAAE;AACX,gBAAA,YAAY,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,EAAE,qBAAqB,CAAC,KAAK,CAAC,IAAI,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpJ,aAAA;AAAM,iBAAA;gBACH,MAAM,YAAY,GAAG,qBAAqB,CAAC,KAAK,EAAU,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC1E,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AACnF,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,sBAAsB,CAAC,KAAY,EAAA;AAC/B,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5B,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7C,QAAA,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAE,KAAa,MAAM,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;AAC9G,QAAA,MAAM,qBAAqB,GAAG,CAAC,IAAY,EAAE,KAAa,MAAM,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACxH,QAAA,MAAM,qBAAqB,GAAG,CAAC,aAAqB,EAAE,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,oBAAyB,KAAI;YAC3I,OAAO,aAAa,IAAI,KAAK,GAAG,KAAK,GAAG,oBAAoB,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;AAC7G,SAAC,CAAC;AACF,QAAA,MAAM,cAAc,GAAG,CAAC,aAAqB,EAAE,aAAqB,EAAE,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,oBAAyB,KAAI;YAC3J,IAAI,aAAa,IAAI,KAAK;AAAE,gBAAA,OAAO,CAAC,CAAC;;gBAChC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAoB,IAAI,aAAa,GAAG,aAAa,GAAG,MAAM,GAAG,aAAa,GAAG,KAAK,IAAI,aAAa,GAAG,aAAa,GAAG,MAAM,GAAG,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AAC1L,SAAC,CAAC;AACF,QAAA,MAAM,aAAa,GAAG,CAAC,aAAqB,EAAE,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,OAAO,GAAG,KAAK,KAAI;YACzH,IAAI,SAAS,GAAG,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;YAE1C,IAAI,aAAa,IAAI,KAAK,EAAE;AACxB,gBAAA,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC;AAC1B,aAAA;YAED,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC5C,SAAC,CAAC;AAEF,QAAA,MAAM,SAAS,GAAG,kBAAkB,CAAe,MAAO,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AACtF,QAAA,MAAM,UAAU,GAAG,kBAAkB,CAAe,MAAO,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;QAEzF,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AACpD,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QAEtC,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,SAAS,CAAC;YACzD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,UAAU,CAAC;AAE5D,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,KAAK,YAAY,IAAI,aAAa,CAAC,CAAC,EAAE;AAC5E,gBAAA,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC,SAAS,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,qBAAqB,CAAC,UAAU,EAAa,IAAI,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACvK,gBAAA,MAAM,YAAY,GAAG;AACjB,oBAAA,IAAI,EAAE,qBAAqB,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC;AACxJ,oBAAA,IAAI,EAAE,qBAAqB,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC;iBAC5J,CAAC;AAEF,gBAAA,QAAQ,GAAG;AACP,oBAAA,IAAI,EAAE,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC;AACpK,oBAAA,IAAI,EAAE,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC;iBACxK,CAAC;AACF,gBAAA,OAAO,GAAG;AACN,oBAAA,IAAI,EAAE,aAAa,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAChI,oBAAA,IAAI,EAAE,aAAa,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;iBACzI,CAAC;gBAEF,cAAc,GAAG,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC;gBACrL,YAAY,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;AACvD,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC;AAC3D,YAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC;AAE5D,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,IAAI,mBAAmB,CAAC,EAAE;gBAChE,MAAM,YAAY,GAAG,qBAAqB,CAAC,SAAS,EAAU,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC9E,MAAM,YAAY,GAAG,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;gBAExJ,QAAQ,GAAG,cAAc,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;AACrJ,gBAAA,OAAO,GAAG,aAAa,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC9G,gBAAA,cAAc,GAAG,QAAQ,KAAK,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC;gBACzF,YAAY,GAAG,SAAS,CAAC;AAC5B,aAAA;AACJ,SAAA;QAED,OAAO;AACH,YAAA,KAAK,EAAE,QAAQ;AACf,YAAA,IAAI,EAAE,OAAO;YACb,cAAc;AACd,YAAA,SAAS,EAAE,YAAY;SAC1B,CAAC;KACL;AAED,IAAA,cAAc,CAAC,KAAY,EAAA;AACvB,QAAA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAEtF,QAAA,IAAI,cAAc,EAAE;AAChB,YAAA,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAEjC,YAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAElC,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;AAE/B,YAAA,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YAEnD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;AACzC,gBAAA,MAAM,aAAa,GAAG;AAClB,oBAAA,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAU,IAAI,CAAC,KAAM,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK;AACtH,oBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,EAAU,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC;iBAChH,CAAC;gBACF,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC;gBAE9H,kBAAkB,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;AACrE,gBAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACtC,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAY,EAAA;QAC1B,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;QAExD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;YACrC,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,gBAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,aAAA;YAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;gBACpC,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;gBAC9D,MAAM,OAAO,GAAG,cAAc,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,KAAK,CAAC,CAAC;AAE9E,gBAAA,IAAI,OAAO,EAAE;AACT,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAEtB,oBAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC3B,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAE3B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE;AACnF,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,oBAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAClC,oBAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC3B,iBAAA;AACL,aAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACnB,SAAA;AAAM,aAAA;YACH,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACjD,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gBAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;AAC7B,oBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;AACnD,oBAAA,MAAM,KAAK,GAAG,UAAU,CAAC,aAAa,EAAE,GAAG,mBAAmB,GAAG,QAAQ,CAAC;oBAC1E,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACpG,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,cAAc,GAAA;QACV,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;YACjC,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAE;AAC5D,gBAAA,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC;AAChJ,gBAAA,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,CAAC;AACjG,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,YAAY,GAAG,IAAI,CAAC,UAAU,GAAG,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,YAAY,GAAG,KAAK,CAAC;gBAE9H,MAAM;AACF,oBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACf,wBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACnD,wBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,wBAAA,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;wBAC5B,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC/D,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAEjE,IAAI,CAAC,IAAI,EAAE,CAAC;wBACZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,qBAAC,CAAC,CAAC;AACV,aAAA;AACL,SAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;KACzB;IAED,YAAY,CAAC,IAAY,EAAE,MAAW,EAAA;;AAElC,QAAA,OAAO,IAAI,CAAC,OAAO,IAAU,IAAI,CAAC,OAAQ,CAAC,IAAI,CAAC,GAAS,IAAI,CAAC,OAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAClH;IAED,iBAAiB,GAAA;QACb,OAAO;AACH,YAAA,iBAAiB,EAAE,CAAA,mBAAA,EAAsB,IAAI,CAAC,SAAS,GAAG,oBAAoB,GAAG,EAAE,CAAE,CAAA;YACrF,KAAK,EAAE,IAAI,CAAC,WAAW;YACvB,cAAc,EAAE,CAAC,KAAa,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;YACzD,OAAO,EAAE,IAAI,CAAC,SAAS;AACvB,YAAA,gBAAgB,EAAE,CAAC,KAAa,EAAE,OAAa,KAAK,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC;YACzF,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,OAAO,EAAE,IAAI,CAAC,aAAa;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;SAClB,CAAC;KACL;AAED,IAAA,UAAU,CAAC,aAAqB,EAAA;QAC5B,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE,MAAM,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;QAEvF,OAAO;YACH,KAAK;YACL,KAAK;YACL,KAAK,EAAE,KAAK,KAAK,CAAC;AAClB,YAAA,IAAI,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC;AACzB,YAAA,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC;AACrB,YAAA,GAAG,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC;SACvB,CAAC;KACL;IAED,gBAAgB,CAAC,KAAa,EAAE,UAAe,EAAA;AAC3C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAEpC,OAAO;YACH,KAAK;YACL,KAAK;YACL,KAAK,EAAE,KAAK,KAAK,CAAC;AAClB,YAAA,IAAI,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC;AACzB,YAAA,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC;AACrB,YAAA,GAAG,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC;AACpB,YAAA,GAAG,UAAU;SAChB,CAAC;KACL;uGA3gCQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA+ZG,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA/ZpE,QAAQ,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,+BAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAuRA,aAAa,EA9UpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,woBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAuhCqC,WAAW,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA/gCxC,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAzDpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,OAAO,iBACjC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,+BAA+B;AACzC,qBAAA,EAAA,MAAA,EAAA,CAAA,woBAAA,CAAA,EAAA,CAAA;;0BAiaY,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;sHA1ZhE,EAAE,EAAA,CAAA;sBAAd,KAAK;gBAUO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAUO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAUO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAUO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAUO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAUO,YAAY,EAAA,CAAA;sBAAxB,KAAK;gBAUO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAUO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAUO,IAAI,EAAA,CAAA;sBAAhB,KAAK;gBAUO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAUO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAUO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAUO,MAAM,EAAA,CAAA;sBAAlB,KAAK;gBAUO,IAAI,EAAA,CAAA;sBAAhB,KAAK;gBAUO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAUO,cAAc,EAAA,CAAA;sBAA1B,KAAK;gBAUO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAUO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAUO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAUO,iBAAiB,EAAA,CAAA;sBAA7B,KAAK;gBAUO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAUO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAUO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAUO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAgBI,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,mBAAmB,EAAA,CAAA;sBAA5B,MAAM;gBAEe,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEE,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA4vBrB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAd,cAAc,EAAA,YAAA,EAAA,CAnhCd,QAAQ,CAAA,EAAA,OAAA,EAAA,CA+gCP,YAAY,EAAE,YAAY,EAAE,WAAW,CAAA,EAAA,OAAA,EAAA,CA/gCxC,QAAQ,EAghCG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAJb,YAAY,EAAE,YAAY,EAAE,WAAW,EAC7B,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGvB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC;AAClD,oBAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;oBACjC,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;AC7mCD;;AAEG;;;;"}