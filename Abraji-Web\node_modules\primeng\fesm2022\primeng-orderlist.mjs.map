{"version": 3, "file": "primeng-orderlist.mjs", "sources": ["../../src/app/components/orderlist/orderlist.ts", "../../src/app/components/orderlist/primeng-orderlist.ts"], "sourcesContent": ["import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewChecked,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { FilterService, PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler } from 'primeng/dom';\nimport { AngleDoubleDownIcon } from 'primeng/icons/angledoubledown';\nimport { AngleDoubleUpIcon } from 'primeng/icons/angledoubleup';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleUpIcon } from 'primeng/icons/angleup';\nimport { SearchIcon } from 'primeng/icons/search';\nimport { RippleModule } from 'primeng/ripple';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { OrderListFilterEvent, OrderListFilterOptions, OrderListSelectionChangeEvent } from './orderlist.interface';\n/**\n * OrderList is used to managed the order of a collection.\n * @group Components\n */\n@Component({\n    selector: 'p-orderList',\n    template: `\n        <div\n            [ngClass]=\"{ 'p-orderlist p-component': true, 'p-orderlist-striped': stripedRows, 'p-orderlist-controls-left': controlsPosition === 'left', 'p-orderlist-controls-right': controlsPosition === 'right' }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-orderlist-controls\" [attr.data-pc-section]=\"'controls'\">\n                <button type=\"button\" [disabled]=\"moveDisabled()\" pButton pRipple class=\"p-button-icon-only\" (click)=\"moveUp()\" [attr.aria-label]=\"moveUpAriaLabel\" [attr.data-pc-section]=\"'moveUpButton'\">\n                    <AngleUpIcon *ngIf=\"!moveUpIconTemplate\" [attr.data-pc-section]=\"'moveupicon'\" />\n                    <ng-template *ngTemplateOutlet=\"moveUpIconTemplate\"></ng-template>\n                </button>\n                <button type=\"button\" [disabled]=\"moveDisabled()\" pButton pRipple class=\"p-button-icon-only\" (click)=\"moveTop()\" [attr.aria-label]=\"moveTopAriaLabel\" [attr.data-pc-section]=\"'moveTopButton'\">\n                    <AngleDoubleUpIcon *ngIf=\"!moveTopIconTemplate\" [attr.data-pc-section]=\"'movetopicon'\" />\n                    <ng-template *ngTemplateOutlet=\"moveTopIconTemplate\"></ng-template>\n                </button>\n                <button type=\"button\" [disabled]=\"moveDisabled()\" pButton pRipple class=\"p-button-icon-only\" (click)=\"moveDown()\" [attr.aria-label]=\"moveDownAriaLabel\" [attr.data-pc-section]=\"'moveDownButton'\">\n                    <AngleDownIcon *ngIf=\"!moveDownIconTemplate\" [attr.data-pc-section]=\"'movedownicon'\" />\n                    <ng-template *ngTemplateOutlet=\"moveDownIconTemplate\"></ng-template>\n                </button>\n                <button type=\"button\" [disabled]=\"moveDisabled()\" pButton pRipple class=\"p-button-icon-only\" (click)=\"moveBottom()\" [attr.aria-label]=\"moveBottomAriaLabel\" [attr.data-pc-section]=\"'moveBottomButton'\">\n                    <AngleDoubleDownIcon *ngIf=\"!moveBottomIconTemplate\" [attr.data-pc-section]=\"'movebottomicon'\" />\n                    <ng-template *ngTemplateOutlet=\"moveBottomIconTemplate\"></ng-template>\n                </button>\n            </div>\n            <div class=\"p-orderlist-list-container\" [attr.data-pc-section]=\"'container'\">\n                <div class=\"p-orderlist-header\" *ngIf=\"header || headerTemplate\" [attr.data-pc-section]=\"'header'\">\n                    <div class=\"p-orderlist-title\" *ngIf=\"!headerTemplate\">{{ header }}</div>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                </div>\n                <div class=\"p-orderlist-filter-container\" *ngIf=\"filterBy\" [attr.data-pc-section]=\"'filterContainer'\">\n                    <ng-container *ngIf=\"filterTemplate; else builtInFilterElement\">\n                        <ng-container *ngTemplateOutlet=\"filterTemplate; context: { options: filterOptions }\"></ng-container>\n                    </ng-container>\n                    <ng-template #builtInFilterElement>\n                        <div class=\"p-orderlist-filter\" [attr.data-pc-section]=\"'filter'\">\n                            <input\n                                #filter\n                                type=\"text\"\n                                role=\"textbox\"\n                                (keyup)=\"onFilterKeyup($event)\"\n                                [disabled]=\"disabled\"\n                                class=\"p-orderlist-filter-input p-inputtext p-component\"\n                                [attr.placeholder]=\"filterPlaceholder\"\n                                [attr.aria-label]=\"ariaFilterLabel\"\n                            />\n                            <SearchIcon *ngIf=\"!filterIconTemplate\" [styleClass]=\"'p-orderlist-filter-icon'\" [attr.data-pc-section]=\"'filterIcon'\" />\n                            <span class=\"p-orderlist-filter-icon\" *ngIf=\"filterIconTemplate\" [attr.data-pc-section]=\"'filterIcon'\">\n                                <ng-template *ngTemplateOutlet=\"filterIconTemplate\"></ng-template>\n                            </span>\n                        </div>\n                    </ng-template>\n                </div>\n                <ul\n                    #listelement\n                    [id]=\"id + '_list'\"\n                    cdkDropList\n                    (cdkDropListDropped)=\"onDrop($event)\"\n                    class=\"p-orderlist-list\"\n                    [ngStyle]=\"listStyle\"\n                    [attr.data-pc-section]=\"'list'\"\n                    role=\"listbox\"\n                    [tabindex]=\"tabindex\"\n                    aria-multiselectable=\"true\"\n                    [attr.aria-activedescendant]=\"focused ? focusedOptionId() : undefined\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    (focus)=\"onListFocus($event)\"\n                    (blur)=\"onListBlur($event)\"\n                    (keydown)=\"onItemKeydown($event)\"\n                >\n                    <ng-template ngFor [ngForTrackBy]=\"trackBy\" let-item [ngForOf]=\"value\" let-i=\"index\" let-l=\"last\">\n                        <li\n                            [id]=\"id + '_' + i\"\n                            pRipple\n                            cdkDrag\n                            role=\"option\"\n                            class=\"p-orderlist-item\"\n                            [ngClass]=\"{ 'p-highlight': isSelected(item), 'p-disabled': disabled, 'p-focus': id + '_' + i === focusedOptionId() }\"\n                            [cdkDragData]=\"item\"\n                            [cdkDragDisabled]=\"!dragdrop\"\n                            (click)=\"onItemClick($event, item, i, id + '_' + i)\"\n                            (touchend)=\"onItemTouchEnd()\"\n                            (mousedown)=\"onOptionMouseDown(i)\"\n                            *ngIf=\"isItemVisible(item)\"\n                            [attr.aria-selected]=\"isSelected(item)\"\n                            [attr.data-pc-section]=\"'item'\"\n                            [attr.data-p-highlight]=\"isSelected(item)\"\n                            [attr.data-p-focused]=\"id + '_' + i === focusedOptionId()\"\n                        >\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                        </li>\n                    </ng-template>\n                    <ng-container *ngIf=\"isEmpty() && (emptyMessageTemplate || emptyFilterMessageTemplate)\">\n                        <li *ngIf=\"!filterValue || !emptyFilterMessageTemplate\" class=\"p-orderlist-empty-message\" [attr.data-pc-section]=\"'emptyMessage'\">\n                            <ng-container *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n                        </li>\n                        <li *ngIf=\"filterValue\" class=\"p-orderlist-empty-message\" [attr.data-pc-section]=\"'emptyMessage'\">\n                            <ng-container *ngTemplateOutlet=\"emptyFilterMessageTemplate\"></ng-container>\n                        </li>\n                    </ng-container>\n                </ul>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./orderlist.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class OrderList implements AfterViewChecked, AfterContentInit {\n    /**\n     * Text for the caption.\n     * @group Props\n     */\n    @Input() header: string | undefined;\n\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined;\n\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n\n    /**\n     * Inline style of the list element.\n     * @group Props\n     */\n    @Input() listStyle: { [klass: string]: any } | null | undefined;\n\n    /**\n     * A boolean value that indicates whether the component should be responsive.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) responsive: boolean | undefined;\n\n    /**\n     * When specified displays an input field to filter the items on keyup and decides which fields to search against.\n     * @group Props\n     */\n    @Input() filterBy: string | undefined;\n\n    /**\n     * Placeholder of the filter input.\n     * @group Props\n     */\n    @Input() filterPlaceholder: string | undefined;\n\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() filterLocale: string | undefined;\n\n    /**\n     * When true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) metaKeySelection: boolean = false;\n\n    /**\n     * Whether to enable dragdrop based reordering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) dragdrop: boolean = false;\n\n    /**\n     * Defines the location of the buttons with respect to the list.\n     * @group Props\n     */\n    @Input() controlsPosition: 'left' | 'right' = 'left';\n\n    /**\n     * Defines a string that labels the filter input.\n     * @group Props\n     */\n    @Input() ariaFilterLabel: string | undefined;\n\n    /**\n     * Defines how the items are filtered.\n     * @group Props\n     */\n    @Input() filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte' = 'contains';\n\n    /**\n     * Indicates the width of the screen at which the component should change its behavior.\n     * @group Props\n     */\n    @Input() breakpoint: string = '960px';\n\n    /**\n     * Whether to displays rows with alternating colors.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) stripedRows: boolean | undefined;\n\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean = false;\n\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity.\n     * @group Props\n     */\n    @Input() trackBy: Function = (index: number, item: any) => item;\n\n    /**\n     * A list of values that are currently selected.\n     * @group Props\n     */\n    @Input() set selection(val: any[]) {\n        this.d_selection = val;\n    }\n    get selection(): any[] {\n        return this.d_selection;\n    }\n\n    /**\n     * Array of values to be displayed in the component.\n     * It represents the data source for the list of items.\n     * @group Props\n     */\n    @Input() set value(val: any[] | undefined) {\n        this._value = val;\n        if (this.filterValue) {\n            this.filter();\n        }\n    }\n    get value(): any[] | undefined {\n        return this._value;\n    }\n\n    /**\n     * Callback to invoke on selection change.\n     * @param {*} any - selection instance.\n     * @group Emits\n     */\n    @Output() selectionChange: EventEmitter<any> = new EventEmitter();\n\n    /**\n     * Callback to invoke when list is reordered.\n     * @param {*} any - list instance.\n     * @group Emits\n     */\n    @Output() onReorder: EventEmitter<any> = new EventEmitter();\n\n    /**\n     * Callback to invoke when selection changes.\n     * @param {OrderListSelectionChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    @Output() onSelectionChange: EventEmitter<OrderListSelectionChangeEvent> = new EventEmitter<OrderListSelectionChangeEvent>();\n\n    /**\n     * Callback to invoke when filtering occurs.\n     * @param {OrderListFilterEvent} event - Custom filter event.\n     * @group Emits\n     */\n    @Output() onFilterEvent: EventEmitter<OrderListFilterEvent> = new EventEmitter<OrderListFilterEvent>();\n\n    /**\n     * Callback to invoke when the list is focused\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n\n    /**\n     * Callback to invoke when the list is blurred\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n\n    @ViewChild('listelement') listViewChild: Nullable<ElementRef>;\n\n    @ViewChild('filter') filterViewChild: Nullable<ElementRef>;\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<PrimeTemplate>>;\n\n    public itemTemplate: Nullable<TemplateRef<any>>;\n\n    public headerTemplate: Nullable<TemplateRef<any>>;\n\n    public emptyMessageTemplate: Nullable<TemplateRef<any>>;\n\n    public emptyFilterMessageTemplate: Nullable<TemplateRef<any>>;\n\n    public filterTemplate: Nullable<TemplateRef<any>>;\n\n    get moveUpAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.moveUp : undefined;\n    }\n\n    get moveTopAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.moveTop : undefined;\n    }\n\n    get moveDownAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.moveDown : undefined;\n    }\n\n    get moveBottomAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.moveBottom : undefined;\n    }\n\n    moveUpIconTemplate: Nullable<TemplateRef<any>>;\n\n    moveTopIconTemplate: Nullable<TemplateRef<any>>;\n\n    moveDownIconTemplate: Nullable<TemplateRef<any>>;\n\n    moveBottomIconTemplate: Nullable<TemplateRef<any>>;\n\n    filterIconTemplate: Nullable<TemplateRef<any>>;\n\n    filterOptions: Nullable<OrderListFilterOptions>;\n\n    d_selection: any[] = [];\n\n    movedUp: Nullable<boolean>;\n\n    movedDown: Nullable<boolean>;\n\n    itemTouched: Nullable<boolean>;\n\n    styleElement: any;\n\n    id: string = UniqueComponentId();\n\n    focused: boolean = false;\n\n    focusedOptionIndex: any = -1;\n\n    focusedOption: any | undefined;\n\n    public filterValue: Nullable<string>;\n\n    public visibleOptions: Nullable<any[]>;\n\n    public _value: any[] | undefined;\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        private renderer: Renderer2,\n        public el: ElementRef,\n        public cd: ChangeDetectorRef,\n        public filterService: FilterService,\n        public config: PrimeNGConfig\n    ) {}\n\n    ngOnInit() {\n        if (this.responsive) {\n            this.createStyle();\n        }\n\n        if (this.filterBy) {\n            this.filterOptions = {\n                filter: (value) => this.onFilterKeyup(value),\n                reset: () => this.resetFilter()\n            };\n        }\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyMessageTemplate = item.template;\n                    break;\n\n                case 'emptyfilter':\n                    this.emptyFilterMessageTemplate = item.template;\n                    break;\n\n                case 'filter':\n                    this.filterTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'moveupicon':\n                    this.moveUpIconTemplate = item.template;\n                    break;\n\n                case 'movetopicon':\n                    this.moveTopIconTemplate = item.template;\n                    break;\n\n                case 'movedownicon':\n                    this.moveDownIconTemplate = item.template;\n                    break;\n\n                case 'movebottomicon':\n                    this.moveBottomIconTemplate = item.template;\n                    break;\n\n                case 'filtericon':\n                    this.filterIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngAfterViewChecked() {\n        if (this.movedUp || this.movedDown) {\n            let listItems = DomHandler.find(this.listViewChild?.nativeElement, 'li.p-highlight');\n            let listItem;\n\n            if (listItems.length > 0) {\n                if (this.movedUp) listItem = listItems[0];\n                else listItem = listItems[listItems.length - 1];\n\n                DomHandler.scrollInView(this.listViewChild?.nativeElement, listItem);\n            }\n            this.movedUp = false;\n            this.movedDown = false;\n        }\n    }\n\n    onItemClick(event, item: any, index?: number, selectedId?: string) {\n        this.itemTouched = false;\n        let focusedIndex = index ? index : ObjectUtils.findIndexInList(this.focusedOption, this.value);\n        let selectedIndex = ObjectUtils.findIndexInList(item, this.d_selection);\n        let selected = selectedIndex !== -1;\n        let metaSelection = this.itemTouched ? false : this.metaKeySelection;\n\n        if (selectedId) {\n            this.focusedOptionIndex = selectedId;\n        }\n\n        if (metaSelection) {\n            let metaKey = event.metaKey || event.ctrlKey;\n\n            if (selected && metaKey) {\n                this.d_selection = this.d_selection.filter((val, focusedIndex) => focusedIndex !== selectedIndex);\n            } else {\n                this.d_selection = metaKey ? (this.d_selection ? [...this.d_selection] : []) : [];\n                ObjectUtils.insertIntoOrderedArray(item, focusedIndex, this.d_selection, this.value);\n            }\n        } else {\n            if (selected) {\n                this.d_selection = this.d_selection.filter((val, focusedIndex) => focusedIndex !== selectedIndex);\n            } else {\n                this.d_selection = this.d_selection ? [...this.d_selection] : [];\n                ObjectUtils.insertIntoOrderedArray(item, focusedIndex, this.d_selection, this.value);\n            }\n        }\n\n        //binding\n        this.selectionChange.emit(this.d_selection);\n\n        //event\n        this.onSelectionChange.emit({ originalEvent: event, value: this.d_selection });\n    }\n\n    onFilterKeyup(event: KeyboardEvent) {\n        this.filterValue = ((<HTMLInputElement>event.target).value.trim() as any).toLocaleLowerCase(this.filterLocale);\n        this.filter();\n\n        this.onFilterEvent.emit({\n            originalEvent: event,\n            value: this.visibleOptions as any[]\n        });\n    }\n\n    filter() {\n        let searchFields: string[] = (this.filterBy as string).split(',');\n        this.visibleOptions = this.filterService.filter(this.value as any[], searchFields, this.filterValue, this.filterMatchMode, this.filterLocale);\n    }\n\n    /**\n     * Callback to invoke on filter reset.\n     * @group Method\n     */\n    public resetFilter() {\n        this.filterValue = null;\n        this.filterViewChild && ((<HTMLInputElement>this.filterViewChild.nativeElement).value = '');\n    }\n\n    isItemVisible(item: any): boolean | undefined {\n        if (this.filterValue && this.filterValue.trim().length) {\n            for (let i = 0; i < (this.visibleOptions as any[]).length; i++) {\n                if (item == (this.visibleOptions as any[])[i]) {\n                    return true;\n                }\n            }\n        } else {\n            return true;\n        }\n    }\n\n    onItemTouchEnd() {\n        this.itemTouched = true;\n    }\n\n    isSelected(item: any) {\n        return ObjectUtils.findIndexInList(item, this.d_selection) !== -1;\n    }\n\n    isEmpty() {\n        return this.filterValue ? !this.visibleOptions || this.visibleOptions.length === 0 : !this.value || this.value.length === 0;\n    }\n\n    moveUp() {\n        if (this.selection) {\n            for (let i = 0; i < this.selection.length; i++) {\n                let selectedItem = this.selection[i];\n                let selectedItemIndex: number = ObjectUtils.findIndexInList(selectedItem, this.value);\n\n                if (selectedItemIndex != 0 && this.value instanceof Array) {\n                    let movedItem = this.value[selectedItemIndex];\n                    let temp = this.value[selectedItemIndex - 1];\n                    this.value[selectedItemIndex - 1] = movedItem;\n                    this.value[selectedItemIndex] = temp;\n                } else {\n                    break;\n                }\n            }\n\n            if (this.dragdrop && this.filterValue) this.filter();\n\n            this.movedUp = true;\n            this.onReorder.emit(this.selection);\n        }\n    }\n\n    moveTop() {\n        if (this.selection) {\n            for (let i = this.selection.length - 1; i >= 0; i--) {\n                let selectedItem = this.selection[i];\n                let selectedItemIndex: number = ObjectUtils.findIndexInList(selectedItem, this.value);\n\n                if (selectedItemIndex != 0 && this.value instanceof Array) {\n                    let movedItem = this.value.splice(selectedItemIndex, 1)[0];\n                    this.value.unshift(movedItem);\n                } else {\n                    break;\n                }\n            }\n\n            if (this.dragdrop && this.filterValue) this.filter();\n\n            this.onReorder.emit(this.selection);\n            (this.listViewChild as ElementRef).nativeElement.scrollTop = 0;\n        }\n    }\n\n    moveDown() {\n        if (this.selection) {\n            for (let i = this.selection.length - 1; i >= 0; i--) {\n                let selectedItem = this.selection[i];\n                let selectedItemIndex: number = ObjectUtils.findIndexInList(selectedItem, this.value);\n\n                if (this.value instanceof Array && selectedItemIndex != this.value.length - 1) {\n                    let movedItem = this.value[selectedItemIndex];\n                    let temp = this.value[selectedItemIndex + 1];\n                    this.value[selectedItemIndex + 1] = movedItem;\n                    this.value[selectedItemIndex] = temp;\n                } else {\n                    break;\n                }\n            }\n\n            if (this.dragdrop && this.filterValue) this.filter();\n\n            this.movedDown = true;\n            this.onReorder.emit(this.selection);\n        }\n    }\n\n    moveBottom() {\n        if (this.selection) {\n            for (let i = 0; i < this.selection.length; i++) {\n                let selectedItem = this.selection[i];\n                let selectedItemIndex: number = ObjectUtils.findIndexInList(selectedItem, this.value);\n\n                if (this.value instanceof Array && selectedItemIndex != this.value.length - 1) {\n                    let movedItem = this.value.splice(selectedItemIndex, 1)[0];\n                    this.value.push(movedItem);\n                } else {\n                    break;\n                }\n            }\n\n            if (this.dragdrop && this.filterValue) this.filter();\n\n            this.onReorder.emit(this.selection);\n            (this.listViewChild as ElementRef).nativeElement.scrollTop = this.listViewChild?.nativeElement.scrollHeight;\n        }\n    }\n\n    onDrop(event: CdkDragDrop<string[]>) {\n        let previousIndex = event.previousIndex;\n        let currentIndex = event.currentIndex;\n\n        if (previousIndex !== currentIndex) {\n            if (this.visibleOptions) {\n                if (this.filterValue) {\n                    previousIndex = ObjectUtils.findIndexInList(event.item.data, this.value);\n                    currentIndex = ObjectUtils.findIndexInList(this.visibleOptions[currentIndex], this.value);\n                }\n\n                moveItemInArray(this.visibleOptions, event.previousIndex, event.currentIndex);\n            }\n\n            moveItemInArray(this.value as any[], previousIndex, currentIndex);\n            this.changeFocusedOptionIndex(currentIndex);\n            this.onReorder.emit([event.item.data]);\n        }\n    }\n\n    onListFocus(event) {\n        const focusableEl = DomHandler.findSingle(this.listViewChild.nativeElement, '[data-p-highlight=\"true\"]') || DomHandler.findSingle(this.listViewChild.nativeElement, '[data-pc-section=\"item\"]');\n\n        if (focusableEl) {\n            const findIndex = ObjectUtils.findIndexInList(focusableEl, this.listViewChild.nativeElement.children);\n            this.focused = true;\n            const index = this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : focusableEl ? findIndex : -1;\n\n            this.changeFocusedOptionIndex(index);\n        }\n\n        this.onFocus.emit(event);\n    }\n\n    onListBlur(event) {\n        this.focused = false;\n        this.focusedOption = null;\n        this.focusedOptionIndex = -1;\n        this.onBlur.emit(event);\n    }\n\n    onItemKeydown(event: KeyboardEvent) {\n        const targetTagName = (event.target as HTMLElement).tagName.toLowerCase();\n\n        if (targetTagName == 'input') {\n            return;\n        }\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n\n            case 'KeyA':\n                if (event.ctrlKey) {\n                    this.d_selection = [...this.value];\n                    this.selectionChange.emit(this.d_selection);\n                }\n\n            default:\n                break;\n        }\n    }\n\n    onOptionMouseDown(index) {\n        this.focused = true;\n        this.focusedOptionIndex = index;\n    }\n\n    onArrowDownKey(event) {\n        const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex);\n\n        this.changeFocusedOptionIndex(optionIndex);\n\n        if (event.shiftKey) {\n            this.onEnterKey(event);\n        }\n\n        event.preventDefault();\n    }\n    onArrowUpKey(event) {\n        const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex);\n\n        this.changeFocusedOptionIndex(optionIndex);\n\n        if (event.shiftKey) {\n            this.onEnterKey(event);\n        }\n\n        event.preventDefault();\n    }\n\n    onHomeKey(event) {\n        if (event.ctrlKey && event.shiftKey) {\n            let visibleOptions = this.getVisibleOptions();\n            let focusedIndex = ObjectUtils.findIndexInList(this.focusedOption, visibleOptions);\n            this.d_selection = [...this.value].slice(0, focusedIndex + 1);\n            this.selectionChange.emit(this.d_selection);\n        } else {\n            this.changeFocusedOptionIndex(0);\n        }\n\n        event.preventDefault();\n    }\n\n    onEndKey(event) {\n        if (event.ctrlKey && event.shiftKey) {\n            let visibleOptions = this.getVisibleOptions();\n            let focusedIndex = ObjectUtils.findIndexInList(this.focusedOption, visibleOptions);\n            this.d_selection = [...this.value].slice(focusedIndex, visibleOptions.length - 1);\n            this.selectionChange.emit(this.d_selection);\n        } else {\n            this.changeFocusedOptionIndex(DomHandler.find(this.listViewChild.nativeElement, '[data-pc-section=\"item\"]').length - 1);\n        }\n\n        event.preventDefault();\n    }\n\n    onEnterKey(event) {\n        this.onItemClick(event, this.focusedOption);\n\n        event.preventDefault();\n    }\n\n    onSpaceKey(event) {\n        event.preventDefault();\n\n        if (event.shiftKey && this.selection && this.selection.length > 0) {\n            let visibleOptions = this.getVisibleOptions();\n            let lastSelectedIndex = this.getLatestSelectedVisibleOptionIndex(visibleOptions);\n\n            if (lastSelectedIndex !== -1) {\n                let focusedIndex = ObjectUtils.findIndexInList(this.focusedOption, visibleOptions);\n                this.d_selection = [...visibleOptions.slice(Math.min(lastSelectedIndex, focusedIndex), Math.max(lastSelectedIndex, focusedIndex) + 1)];\n                this.selectionChange.emit(this.d_selection);\n                this.onSelectionChange.emit({ originalEvent: event, value: this.d_selection });\n\n                return;\n            }\n        }\n\n        this.onEnterKey(event);\n    }\n\n    findNextOptionIndex(index) {\n        const items = DomHandler.find(this.listViewChild.nativeElement, '[data-pc-section=\"item\"]');\n        const matchedOptionIndex = [...items].findIndex((link) => link.id === index);\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n    }\n\n    findPrevOptionIndex(index) {\n        const items = DomHandler.find(this.listViewChild.nativeElement, '[data-pc-section=\"item\"]');\n        const matchedOptionIndex = [...items].findIndex((link) => link.id === index);\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n    }\n\n    getLatestSelectedVisibleOptionIndex(visibleOptions: any[]): number {\n        const latestSelectedItem = [...this.d_selection].reverse().find((item) => visibleOptions.includes(item));\n\n        return latestSelectedItem !== undefined ? visibleOptions.indexOf(latestSelectedItem) : -1;\n    }\n\n    getVisibleOptions() {\n        return this.visibleOptions && this.visibleOptions.length > 0 ? this.visibleOptions : this.value && this.value.length > 0 ? this.value : null;\n    }\n\n    getFocusedOption(index: number) {\n        if (index === -1) return null;\n\n        return this.visibleOptions && this.visibleOptions.length ? this.visibleOptions[index] : this.value && this.value.length ? this.value[index] : null;\n    }\n\n    changeFocusedOptionIndex(index) {\n        const items = DomHandler.find(this.listViewChild.nativeElement, '[data-pc-section=\"item\"]');\n\n        let order = index >= items.length ? items.length - 1 : index < 0 ? 0 : index;\n\n        this.focusedOptionIndex = items[order] ? items[order].getAttribute('id') : -1;\n        this.focusedOption = this.getFocusedOption(order);\n\n        this.scrollInView(this.focusedOptionIndex);\n    }\n\n    scrollInView(id) {\n        const element = DomHandler.findSingle(this.listViewChild.nativeElement, `[data-pc-section=\"item\"][id=\"${id}\"]`);\n\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n\n    findNextItem(item: any): HTMLElement | null {\n        let nextItem = item.nextElementSibling;\n\n        if (nextItem) return !DomHandler.hasClass(nextItem, 'p-orderlist-item') || DomHandler.isHidden(nextItem) ? this.findNextItem(nextItem) : nextItem;\n        else return null;\n    }\n\n    findPrevItem(item: any): HTMLElement | null {\n        let prevItem = item.previousElementSibling;\n\n        if (prevItem) return !DomHandler.hasClass(prevItem, 'p-orderlist-item') || DomHandler.isHidden(prevItem) ? this.findPrevItem(prevItem) : prevItem;\n        else return null;\n    }\n\n    moveDisabled() {\n        if (this.disabled || !this.selection.length) {\n            return true;\n        }\n    }\n\n    focusedOptionId() {\n        return this.focusedOptionIndex !== -1 ? this.focusedOptionIndex : null;\n    }\n\n    createStyle() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.styleElement) {\n                this.renderer.setAttribute(this.el.nativeElement.children[0], this.id, '');\n                this.styleElement = this.renderer.createElement('style');\n                this.renderer.setAttribute(this.styleElement, 'type', 'text/css');\n                DomHandler.setAttribute(this.styleElement, 'nonce', this.config?.csp()?.nonce);\n                this.renderer.appendChild(this.document.head, this.styleElement);\n\n                let innerHTML = `\n                    @media screen and (max-width: ${this.breakpoint}) {\n                        .p-orderlist[${this.id}] {\n                            flex-direction: column;\n                        }\n\n                        .p-orderlist[${this.id}] .p-orderlist-controls {\n                            padding: var(--content-padding);\n                            flex-direction: row;\n                        }\n\n                        .p-orderlist[${this.id}] .p-orderlist-controls .p-button {\n                            margin-right: var(--inline-spacing);\n                            margin-bottom: 0;\n                        }\n\n                        .p-orderlist[${this.id}] .p-orderlist-controls .p-button:last-child {\n                            margin-right: 0;\n                        }\n                    }\n                `;\n                this.renderer.setProperty(this.styleElement, 'innerHTML', innerHTML);\n            }\n        }\n    }\n\n    destroyStyle() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.styleElement) {\n                this.renderer.removeChild(this.document, this.styleElement);\n                this.styleElement = null;\n                ``;\n            }\n        }\n    }\n\n    ngOnDestroy() {\n        this.destroyStyle();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, ButtonModule, SharedModule, RippleModule, DragDropModule, AngleDoubleDownIcon, AngleDoubleUpIcon, AngleUpIcon, AngleDownIcon, SearchIcon],\n    exports: [OrderList, SharedModule, DragDropModule],\n    declarations: [OrderList]\n})\nexport class OrderListModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoCA;;;AAGG;MAmHU,SAAS,CAAA;AAkQY,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACrB,IAAA,QAAA,CAAA;AACD,IAAA,EAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACA,IAAA,aAAA,CAAA;AACA,IAAA,MAAA,CAAA;AAvQX;;;AAGG;AACM,IAAA,MAAM,CAAqB;AAEpC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAE5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AAExC;;;AAGG;AACoC,IAAA,QAAQ,CAAqB;AAEpE;;;AAGG;AACM,IAAA,SAAS,CAAqB;AAEvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAE5C;;;AAGG;AACM,IAAA,SAAS,CAA8C;AAEhE;;;AAGG;AACqC,IAAA,UAAU,CAAsB;AAExE;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AAEtC;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAE/C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAE1C;;;AAGG;IACqC,gBAAgB,GAAY,KAAK,CAAC;AAE1E;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAElE;;;AAGG;IACM,gBAAgB,GAAqB,MAAM,CAAC;AAErD;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAE7C;;;AAGG;IACM,eAAe,GAAyG,UAAU,CAAC;AAE5I;;;AAGG;IACM,UAAU,GAAW,OAAO,CAAC;AAEtC;;;AAGG;AACqC,IAAA,WAAW,CAAsB;AAEzE;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAElE;;;AAGG;IACM,OAAO,GAAa,CAAC,KAAa,EAAE,IAAS,KAAK,IAAI,CAAC;AAEhE;;;AAGG;IACH,IAAa,SAAS,CAAC,GAAU,EAAA;AAC7B,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KAC1B;AACD,IAAA,IAAI,SAAS,GAAA;QACT,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;AAED;;;;AAIG;IACH,IAAa,KAAK,CAAC,GAAsB,EAAA;AACrC,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;QAClB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB,SAAA;KACJ;AACD,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AAED;;;;AAIG;AACO,IAAA,eAAe,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElE;;;;AAIG;AACO,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAE5D;;;;AAIG;AACO,IAAA,iBAAiB,GAAgD,IAAI,YAAY,EAAiC,CAAC;AAE7H;;;;AAIG;AACO,IAAA,aAAa,GAAuC,IAAI,YAAY,EAAwB,CAAC;AAEvG;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AAEnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAExC,IAAA,aAAa,CAAuB;AAEzC,IAAA,eAAe,CAAuB;AAE3B,IAAA,SAAS,CAAqC;AAEvE,IAAA,YAAY,CAA6B;AAEzC,IAAA,cAAc,CAA6B;AAE3C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,0BAA0B,CAA6B;AAEvD,IAAA,cAAc,CAA6B;AAElD,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;KACzF;AAED,IAAA,IAAI,gBAAgB,GAAA;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;KAC1F;AAED,IAAA,IAAI,iBAAiB,GAAA;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;KAC3F;AAED,IAAA,IAAI,mBAAmB,GAAA;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7F;AAED,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,sBAAsB,CAA6B;AAEnD,IAAA,kBAAkB,CAA6B;AAE/C,IAAA,aAAa,CAAmC;IAEhD,WAAW,GAAU,EAAE,CAAC;AAExB,IAAA,OAAO,CAAoB;AAE3B,IAAA,SAAS,CAAoB;AAE7B,IAAA,WAAW,CAAoB;AAE/B,IAAA,YAAY,CAAM;IAElB,EAAE,GAAW,iBAAiB,EAAE,CAAC;IAEjC,OAAO,GAAY,KAAK,CAAC;IAEzB,kBAAkB,GAAQ,CAAC,CAAC,CAAC;AAE7B,IAAA,aAAa,CAAkB;AAExB,IAAA,WAAW,CAAmB;AAE9B,IAAA,cAAc,CAAkB;AAEhC,IAAA,MAAM,CAAoB;AAEjC,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACpC,QAAmB,EACpB,EAAc,EACd,EAAqB,EACrB,aAA4B,EAC5B,MAAqB,EAAA;QANF,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACpC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACpB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACrB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QAC5B,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAC5B;IAEJ,QAAQ,GAAA;QACJ,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,aAAa,GAAG;gBACjB,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AAC5C,gBAAA,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;aAClC,CAAC;AACL,SAAA;KACJ;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAChD,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,gBAAgB;AACjB,oBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC5C,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE;AAChC,YAAA,IAAI,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACrF,YAAA,IAAI,QAAQ,CAAC;AAEb,YAAA,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,IAAI,IAAI,CAAC,OAAO;AAAE,oBAAA,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;;oBACrC,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAEhD,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;AACxE,aAAA;AACD,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAE,IAAS,EAAE,KAAc,EAAE,UAAmB,EAAA;AAC7D,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,YAAY,GAAG,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/F,QAAA,IAAI,aAAa,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACxE,QAAA,IAAI,QAAQ,GAAG,aAAa,KAAK,CAAC,CAAC,CAAC;AACpC,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAErE,QAAA,IAAI,UAAU,EAAE;AACZ,YAAA,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC;AACxC,SAAA;AAED,QAAA,IAAI,aAAa,EAAE;YACf,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;YAE7C,IAAI,QAAQ,IAAI,OAAO,EAAE;gBACrB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,YAAY,KAAK,YAAY,KAAK,aAAa,CAAC,CAAC;AACrG,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AAClF,gBAAA,WAAW,CAAC,sBAAsB,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACxF,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,QAAQ,EAAE;gBACV,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,YAAY,KAAK,YAAY,KAAK,aAAa,CAAC,CAAC;AACrG,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AACjE,gBAAA,WAAW,CAAC,sBAAsB,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACxF,aAAA;AACJ,SAAA;;QAGD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;;AAG5C,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KAClF;AAED,IAAA,aAAa,CAAC,KAAoB,EAAA;AAC9B,QAAA,IAAI,CAAC,WAAW,GAAuB,KAAK,CAAC,MAAO,CAAC,KAAK,CAAC,IAAI,EAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/G,IAAI,CAAC,MAAM,EAAE,CAAC;AAEd,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;AACpB,YAAA,aAAa,EAAE,KAAK;YACpB,KAAK,EAAE,IAAI,CAAC,cAAuB;AACtC,SAAA,CAAC,CAAC;KACN;IAED,MAAM,GAAA;QACF,IAAI,YAAY,GAAc,IAAI,CAAC,QAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,KAAc,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;KACjJ;AAED;;;AAGG;IACI,WAAW,GAAA;AACd,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,eAAe,KAAwB,IAAI,CAAC,eAAe,CAAC,aAAc,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;KAC/F;AAED,IAAA,aAAa,CAAC,IAAS,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE;AACpD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAI,IAAI,CAAC,cAAwB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5D,IAAI,IAAI,IAAK,IAAI,CAAC,cAAwB,CAAC,CAAC,CAAC,EAAE;AAC3C,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;AACJ,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;IAED,cAAc,GAAA;AACV,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;AAED,IAAA,UAAU,CAAC,IAAS,EAAA;AAChB,QAAA,OAAO,WAAW,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;KACrE;IAED,OAAO,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;KAC/H;IAED,MAAM,GAAA;QACF,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAA,IAAI,iBAAiB,GAAW,WAAW,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEtF,IAAI,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,YAAY,KAAK,EAAE;oBACvD,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;oBAC9C,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;oBAC7C,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;AAC9C,oBAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;AACxC,iBAAA;AAAM,qBAAA;oBACH,MAAM;AACT,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW;gBAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAErD,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACvC,SAAA;KACJ;IAED,OAAO,GAAA;QACH,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBACjD,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAA,IAAI,iBAAiB,GAAW,WAAW,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEtF,IAAI,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,YAAY,KAAK,EAAE;AACvD,oBAAA,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,oBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjC,iBAAA;AAAM,qBAAA;oBACH,MAAM;AACT,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW;gBAAE,IAAI,CAAC,MAAM,EAAE,CAAC;YAErD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACnC,IAAI,CAAC,aAA4B,CAAC,aAAa,CAAC,SAAS,GAAG,CAAC,CAAC;AAClE,SAAA;KACJ;IAED,QAAQ,GAAA;QACJ,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBACjD,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAA,IAAI,iBAAiB,GAAW,WAAW,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAEtF,gBAAA,IAAI,IAAI,CAAC,KAAK,YAAY,KAAK,IAAI,iBAAiB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC3E,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;oBAC9C,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;oBAC7C,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;AAC9C,oBAAA,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;AACxC,iBAAA;AAAM,qBAAA;oBACH,MAAM;AACT,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW;gBAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAErD,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACvC,SAAA;KACJ;IAED,UAAU,GAAA;QACN,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC5C,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAA,IAAI,iBAAiB,GAAW,WAAW,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAEtF,gBAAA,IAAI,IAAI,CAAC,KAAK,YAAY,KAAK,IAAI,iBAAiB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3E,oBAAA,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,oBAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,iBAAA;AAAM,qBAAA;oBACH,MAAM;AACT,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW;gBAAE,IAAI,CAAC,MAAM,EAAE,CAAC;YAErD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACnC,YAAA,IAAI,CAAC,aAA4B,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,YAAY,CAAC;AAC/G,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAA4B,EAAA;AAC/B,QAAA,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;AACxC,QAAA,IAAI,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QAEtC,IAAI,aAAa,KAAK,YAAY,EAAE;YAChC,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,oBAAA,aAAa,GAAG,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACzE,oBAAA,YAAY,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7F,iBAAA;AAED,gBAAA,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;AACjF,aAAA;YAED,eAAe,CAAC,IAAI,CAAC,KAAc,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;AAC5C,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;QACb,MAAM,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,2BAA2B,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAC;AAEhM,QAAA,IAAI,WAAW,EAAE;AACb,YAAA,MAAM,SAAS,GAAG,WAAW,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACtG,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,GAAG,WAAW,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;AAEtG,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACxC,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,aAAa,CAAC,KAAoB,EAAA;QAC9B,MAAM,aAAa,GAAI,KAAK,CAAC,MAAsB,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAE1E,IAAI,aAAa,IAAI,OAAO,EAAE;YAC1B,OAAO;AACV,SAAA;QAED,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,KAAK,CAAC,OAAO,EAAE;oBACf,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;oBACnC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/C,iBAAA;AAEL,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAK,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;KACnC;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAEtE,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,KAAK,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC1B,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AACD,IAAA,YAAY,CAAC,KAAK,EAAA;QACd,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAEtE,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,KAAK,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC1B,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;AACX,QAAA,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE;AACjC,YAAA,IAAI,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC9C,YAAA,IAAI,YAAY,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AACnF,YAAA,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/C,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;AACpC,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;AACV,QAAA,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE;AACjC,YAAA,IAAI,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC9C,YAAA,IAAI,YAAY,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;YACnF,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/C,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3H,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE5C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;QACZ,KAAK,CAAC,cAAc,EAAE,CAAC;AAEvB,QAAA,IAAI,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/D,YAAA,IAAI,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC9C,IAAI,iBAAiB,GAAG,IAAI,CAAC,mCAAmC,CAAC,cAAc,CAAC,CAAC;AAEjF,YAAA,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE;AAC1B,gBAAA,IAAI,YAAY,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AACnF,gBAAA,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACvI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC5C,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBAE/E,OAAO;AACV,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAC;QAC5F,MAAM,kBAAkB,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAE7E,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC;KAC/D;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAC;QAC5F,MAAM,kBAAkB,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAE7E,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC;KAC/D;AAED,IAAA,mCAAmC,CAAC,cAAqB,EAAA;QACrD,MAAM,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAEzG,QAAA,OAAO,kBAAkB,KAAK,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;KAC7F;IAED,iBAAiB,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;KAChJ;AAED,IAAA,gBAAgB,CAAC,KAAa,EAAA;QAC1B,IAAI,KAAK,KAAK,CAAC,CAAC;AAAE,YAAA,OAAO,IAAI,CAAC;AAE9B,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACtJ;AAED,IAAA,wBAAwB,CAAC,KAAK,EAAA;AAC1B,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAC;AAE5F,QAAA,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAE7E,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAElD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;KAC9C;AAED,IAAA,YAAY,CAAC,EAAE,EAAA;AACX,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAA,6BAAA,EAAgC,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAEhH,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;AAClB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAEvC,QAAA,IAAI,QAAQ;AAAE,YAAA,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,kBAAkB,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;;AAC7I,YAAA,OAAO,IAAI,CAAC;KACpB;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;AAClB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAE3C,QAAA,IAAI,QAAQ;AAAE,YAAA,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,kBAAkB,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;;AAC7I,YAAA,OAAO,IAAI,CAAC;KACpB;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzC,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;IAED,eAAe,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,kBAAkB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1E;IAED,WAAW,GAAA;AACP,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC3E,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACzD,gBAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAClE,gBAAA,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;AAC/E,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAEjE,gBAAA,IAAI,SAAS,GAAG,CAAA;AACoB,kDAAA,EAAA,IAAI,CAAC,UAAU,CAAA;AAC5B,qCAAA,EAAA,IAAI,CAAC,EAAE,CAAA;;;;AAIP,qCAAA,EAAA,IAAI,CAAC,EAAE,CAAA;;;;;AAKP,qCAAA,EAAA,IAAI,CAAC,EAAE,CAAA;;;;;AAKP,qCAAA,EAAA,IAAI,CAAC,EAAE,CAAA;;;;iBAI7B,CAAC;AACF,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AACxE,aAAA;AACJ,SAAA;KACJ;IAED,YAAY,GAAA;AACR,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC5D,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,gBAAA,CAAA,CAAE,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,YAAY,EAAE,CAAC;KACvB;uGAlyBQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAkQN,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAnQd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EAuBE,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,CAwBf,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,0JAwBhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAMhB,gBAAgB,CAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EA8BhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAMhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAgFnB,aAAa,EAjTpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwGT,EA8yBiF,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,slCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,aAAA,EAAA,OAAA,EAAA,MAAA,EAAA,SAAA,EAAA,UAAA,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,OAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,8BAAA,EAAA,MAAA,EAAA,CAAA,wBAAA,EAAA,iBAAA,EAAA,wBAAA,EAAA,IAAA,EAAA,qBAAA,EAAA,qBAAA,EAAA,4BAAA,EAAA,2BAAA,EAAA,0BAAA,EAAA,+BAAA,EAAA,2BAAA,CAAA,EAAA,OAAA,EAAA,CAAA,oBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,aAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,yBAAA,EAAA,iBAAA,EAAA,0BAAA,EAAA,qBAAA,EAAA,yBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,cAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,mBAAmB,qFAAE,iBAAiB,CAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,WAAW,CAAE,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,aAAa,+EAAE,UAAU,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAtyBvJ,SAAS,EAAA,UAAA,EAAA,CAAA;kBAlHrB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,aAAa,EACb,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwGT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,slCAAA,CAAA,EAAA,CAAA;;0BAoQI,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;kLA9Pd,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAMG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAMG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAMiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAM5B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAMG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAMkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAMG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAMG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAMkC,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAME,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM7B,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAMG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAMG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAMG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAMkC,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAME,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAMO,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAYO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAeI,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAOG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAOG,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAOG,aAAa,EAAA,CAAA;sBAAtB,MAAM;gBAOG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAOG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEmB,aAAa,EAAA,CAAA;sBAAtC,SAAS;uBAAC,aAAa,CAAA;gBAEH,eAAe,EAAA,CAAA;sBAAnC,SAAS;uBAAC,QAAQ,CAAA;gBAEa,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAymBrB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAf,eAAe,EAAA,YAAA,EAAA,CA1yBf,SAAS,CAAA,EAAA,OAAA,EAAA,CAsyBR,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,CAAA,EAAA,OAAA,EAAA,CAtyBvJ,SAAS,EAuyBG,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;wGAGxC,eAAe,EAAA,OAAA,EAAA,CAJd,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAC3I,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;;2FAGxC,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,CAAC;AACjK,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,cAAc,CAAC;oBAClD,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;ACn8BD;;AAEG;;;;"}