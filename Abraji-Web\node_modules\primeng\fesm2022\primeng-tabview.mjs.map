{"version": 3, "file": "primeng-tabview.mjs", "sources": ["../../src/app/components/tabview/tabview.ts", "../../src/app/components/tabview/primeng-tabview.ts"], "sourcesContent": ["import { CommonModule, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewChecked,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EmbeddedViewRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewContainerRef,\n    ViewEncapsulation,\n    booleanAttribute,\n    forwardRef,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { BlockableUI, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { Subscription } from 'rxjs';\nimport { TabViewChangeEvent, TabViewCloseEvent } from './tabview.interface';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { Nullable } from 'primeng/ts-helpers';\n\n/**\n * TabPanel is a helper component for TabView component.\n * @group Components\n */\n@Component({\n    selector: 'p-tabPanel',\n    template: `\n        <div\n            *ngIf=\"!closed\"\n            class=\"p-tabview-panel\"\n            role=\"tabpanel\"\n            [hidden]=\"!selected\"\n            [attr.id]=\"tabView.getTabContentId(id)\"\n            [attr.aria-hidden]=\"!selected\"\n            [attr.aria-labelledby]=\"tabView.getTabHeaderActionId(id)\"\n            [attr.data-pc-name]=\"'tabpanel'\"\n        >\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </ng-container>\n        </div>\n    `,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TabPanel implements AfterContentInit, OnDestroy {\n    /**\n     * Defines if tab can be removed.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) closable: boolean | undefined = false;\n    /**\n     * Inline style of the tab header.\n     * @group Props\n     */\n    @Input() get headerStyle(): { [klass: string]: any } | null | undefined {\n        return this._headerStyle;\n    }\n    set headerStyle(headerStyle: { [klass: string]: any } | null | undefined) {\n        this._headerStyle = headerStyle;\n        this.tabView.cd.markForCheck();\n    }\n    /**\n     * Style class of the tab header.\n     * @group Props\n     */\n    @Input() get headerStyleClass(): string | undefined {\n        return this._headerStyleClass;\n    }\n    set headerStyleClass(headerStyleClass: string | undefined) {\n        this._headerStyleClass = headerStyleClass;\n        this.tabView.cd.markForCheck();\n    }\n    /**\n     * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) cache: boolean | undefined = true;\n    /**\n     * Advisory information to display in a tooltip on hover.\n     * @group Props\n     */\n    @Input() tooltip: string | undefined;\n    /**\n     * Position of the tooltip.\n     * @group Props\n     */\n    @Input() tooltipPosition: 'top' | 'bottom' | 'left' | 'right' | undefined = 'top';\n    /**\n     * Type of CSS position.\n     * @group Props\n     */\n    @Input() tooltipPositionStyle: string | undefined = 'absolute';\n    /**\n     * Style class of the tooltip.\n     * @group Props\n     */\n    @Input() tooltipStyleClass: string | undefined;\n    /**\n     * Defines if tab is active.\n     * @defaultValue false\n     * @group Props\n     */\n    @Input() get selected(): boolean {\n        return !!this._selected;\n    }\n    set selected(val: boolean) {\n        this._selected = val;\n\n        if (!this.loaded) {\n            this.cd.detectChanges();\n        }\n\n        if (val) this.loaded = true;\n    }\n    /**\n     * When true, tab cannot be activated.\n     * @defaultValue false\n     * @group Props\n     */\n    @Input() get disabled(): boolean {\n        return !!this._disabled;\n    }\n    set disabled(disabled: boolean) {\n        this._disabled = disabled;\n        this.tabView.cd.markForCheck();\n    }\n    /**\n     * Title of the tabPanel.\n     * @group Props\n     */\n    @Input() get header(): string {\n        return this._header;\n    }\n    set header(header: string) {\n        this._header = header;\n\n        // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n        // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n        Promise.resolve().then(() => {\n            this.tabView.updateInkBar();\n            this.tabView.cd.markForCheck();\n        });\n    }\n    /**\n     * Left icon of the tabPanel.\n     * @group Props\n     * @deprecated since v15.4.2, use `lefticon` template instead.\n     */\n    @Input() get leftIcon(): string {\n        return this._leftIcon;\n    }\n    set leftIcon(leftIcon: string) {\n        this._leftIcon = leftIcon;\n        this.tabView.cd.markForCheck();\n    }\n    /**\n     * Left icon of the tabPanel.\n     * @group Props\n     * @deprecated since v15.4.2, use `righticon` template instead.\n     */\n    @Input() get rightIcon(): string | undefined {\n        return this._rightIcon;\n    }\n    set rightIcon(rightIcon: string | undefined) {\n        this._rightIcon = rightIcon;\n        this.tabView.cd.markForCheck();\n    }\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    closed: boolean = false;\n\n    view: EmbeddedViewRef<any> | null = null;\n\n    _headerStyle: { [klass: string]: any } | null | undefined;\n\n    _headerStyleClass: string | undefined;\n\n    _selected: boolean | undefined;\n\n    _disabled: boolean | undefined;\n\n    _header!: string;\n\n    _leftIcon!: string;\n\n    _rightIcon: string | undefined = undefined;\n\n    loaded: boolean = false;\n\n    public id: string | undefined;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    headerTemplate: TemplateRef<any> | undefined;\n\n    leftIconTemplate: TemplateRef<any> | undefined;\n\n    rightIconTemplate: TemplateRef<any> | undefined;\n\n    closeIconTemplate: TemplateRef<any> | undefined;\n\n    tabView: TabView;\n\n    constructor(@Inject(forwardRef(() => TabView)) tabView: TabView, public el: ElementRef, public viewContainer: ViewContainerRef, public cd: ChangeDetectorRef) {\n        this.tabView = tabView as TabView;\n        this.id = UniqueComponentId();\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'righticon':\n                    this.rightIconTemplate = item.template;\n                    break;\n\n                case 'lefticon':\n                    this.leftIconTemplate = item.template;\n                    break;\n\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngOnDestroy() {\n        this.view = null;\n    }\n}\n/**\n * TabView is a container component to group content with tabs.\n * @group Components\n */\n@Component({\n    selector: 'p-tabView',\n    template: `\n        <div [ngClass]=\"{ 'p-tabview p-component': true, 'p-tabview-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\" [attr.data-pc-name]=\"'tabview'\">\n            <div #elementToObserve class=\"p-tabview-nav-container\">\n                <button\n                    *ngIf=\"scrollable && !backwardIsDisabled && autoHideButtons\"\n                    #prevBtn\n                    class=\"p-tabview-nav-prev p-tabview-nav-btn p-link\"\n                    (click)=\"navBackward()\"\n                    [attr.tabindex]=\"tabindex\"\n                    [attr.aria-label]=\"prevButtonAriaLabel\"\n                    type=\"button\"\n                    pRipple\n                >\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabview-nav-content\" (scroll)=\"onScroll($event)\" [attr.data-pc-section]=\"'navcontent'\">\n                    <ul #navbar class=\"p-tabview-nav\" role=\"tablist\" [attr.data-pc-section]=\"'nav'\">\n                        <ng-template ngFor let-tab [ngForOf]=\"tabs\" let-i=\"index\">\n                            <li role=\"presentation\" [ngClass]=\"{ 'p-highlight': tab.selected, 'p-disabled': tab.disabled }\" [attr.data-p-disabled]=\"tab.disabled\" [ngStyle]=\"tab.headerStyle\" [class]=\"tab.headerStyleClass\" *ngIf=\"!tab.closed\">\n                                <a\n                                    role=\"tab\"\n                                    class=\"p-tabview-nav-link\"\n                                    [pTooltip]=\"tab.tooltip\"\n                                    [tooltipPosition]=\"tab.tooltipPosition\"\n                                    [positionStyle]=\"tab.tooltipPositionStyle\"\n                                    [tooltipStyleClass]=\"tab.tooltipStyleClass\"\n                                    [attr.id]=\"getTabHeaderActionId(tab.id)\"\n                                    [attr.aria-controls]=\"getTabContentId(tab.id)\"\n                                    [attr.aria-selected]=\"tab.selected\"\n                                    [attr.tabindex]=\"tab.disabled || !tab.selected ? '-1' : tabindex\"\n                                    [attr.aria-disabled]=\"tab.disabled\"\n                                    [attr.data-pc-index]=\"i\"\n                                    [attr.data-pc-section]=\"'headeraction'\"\n                                    (click)=\"open($event, tab)\"\n                                    (keydown)=\"onTabKeyDown($event, tab)\"\n                                    pRipple\n                                >\n                                    <ng-container *ngIf=\"!tab.headerTemplate\">\n                                        <span class=\"p-tabview-left-icon\" [ngClass]=\"tab.leftIcon\" *ngIf=\"tab.leftIcon && !tab.leftIconTemplate\"></span>\n                                        <span *ngIf=\"tab.leftIconTemplate\" class=\"p-tabview-left-icon\">\n                                            <ng-template *ngTemplateOutlet=\"tab.leftIconTemplate\"></ng-template>\n                                        </span>\n                                        <span class=\"p-tabview-title\">{{ tab.header }}</span>\n                                        <span class=\"p-tabview-right-icon\" [ngClass]=\"tab.rightIcon\" *ngIf=\"tab.rightIcon && !tab.rightIconTemplate\"></span>\n                                        <span *ngIf=\"tab.rightIconTemplate\" class=\"p-tabview-right-icon\">\n                                            <ng-template *ngTemplateOutlet=\"tab.rightIconTemplate\"></ng-template>\n                                        </span>\n                                    </ng-container>\n                                    <ng-container *ngTemplateOutlet=\"tab.headerTemplate\"></ng-container>\n                                    <ng-container *ngIf=\"tab.closable\">\n                                        <TimesIcon *ngIf=\"!tab.closeIconTemplate\" [styleClass]=\"'p-tabview-close'\" (click)=\"close($event, tab)\" />\n                                        <span class=\"tab.closeIconTemplate\" *ngIf=\"tab.closeIconTemplate\"></span>\n                                        <ng-template *ngTemplateOutlet=\"tab.closeIconTemplate\"></ng-template>\n                                    </ng-container>\n                                </a>\n                            </li>\n                        </ng-template>\n                        <li #inkbar class=\"p-tabview-ink-bar\" role=\"presentation\" aria-hidden=\"true\" [attr.data-pc-section]=\"'inkbar'\"></li>\n                    </ul>\n                </div>\n                <button\n                    *ngIf=\"scrollable && !forwardIsDisabled && buttonVisible\"\n                    #nextBtn\n                    [attr.tabindex]=\"tabindex\"\n                    [attr.aria-label]=\"nextButtonAriaLabel\"\n                    class=\"p-tabview-nav-next p-tabview-nav-btn p-link\"\n                    (click)=\"navForward()\"\n                    type=\"button\"\n                    pRipple\n                >\n                    <ChevronRightIcon *ngIf=\"!nextIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n            <div class=\"p-tabview-panels\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./tabview.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TabView implements AfterContentInit, AfterViewChecked, OnDestroy, BlockableUI {\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Whether tab close is controlled at onClose event or not.\n     * @defaultValue false\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) controlClose: boolean | undefined;\n    /**\n     * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n     * @defaultValue false\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) scrollable: boolean | undefined;\n    /**\n     * Index of the active tab to change selected tab programmatically.\n     * @group Props\n     */\n    @Input() get activeIndex(): number {\n        return this._activeIndex;\n    }\n    set activeIndex(val: number) {\n        this._activeIndex = val;\n        if (this.preventActiveIndexPropagation) {\n            this.preventActiveIndexPropagation = false;\n            return;\n        }\n\n        if (this.tabs && this.tabs.length && this._activeIndex != null && this.tabs.length > this._activeIndex) {\n            (this.findSelectedTab() as TabPanel).selected = false;\n            this.tabs[this._activeIndex].selected = true;\n            this.tabChanged = true;\n\n            this.updateScrollBar(val);\n        }\n    }\n    /**\n     * When enabled, the focused tab is activated.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) selectOnFocus: boolean = false;\n    /**\n     * Used to define a string aria label attribute the forward navigation button.\n     * @group Props\n     */\n    @Input() nextButtonAriaLabel: string | undefined;\n    /**\n     * Used to define a string aria label attribute the backward navigation button.\n     * @group Props\n     */\n    @Input() prevButtonAriaLabel: string | undefined;\n    /**\n     * When activated, navigation buttons will automatically hide or show based on the available space within the container.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoHideButtons: boolean = true;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n    /**\n     * Callback to invoke on tab change.\n     * @param {TabViewChangeEvent} event - Custom tab change event\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<TabViewChangeEvent> = new EventEmitter<TabViewChangeEvent>();\n    /**\n     * Callback to invoke on tab close.\n     * @param {TabViewCloseEvent} event - Custom tab close event\n     * @group Emits\n     */\n    @Output() onClose: EventEmitter<TabViewCloseEvent> = new EventEmitter<TabViewCloseEvent>();\n    /**\n     * Callback to invoke on the active tab change.\n     * @param {number} index - New active index\n     * @group Emits\n     */\n    @Output() activeIndexChange: EventEmitter<number> = new EventEmitter<number>();\n\n    @ViewChild('content') content?: ElementRef<HTMLDivElement>;\n\n    @ViewChild('navbar') navbar?: ElementRef<HTMLUListElement>;\n\n    @ViewChild('prevBtn') prevBtn?: ElementRef;\n\n    @ViewChild('nextBtn') nextBtn?: ElementRef;\n\n    @ViewChild('inkbar') inkbar?: ElementRef;\n\n    @ContentChildren(TabPanel) tabPanels: QueryList<TabPanel> | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    initialized: boolean | undefined;\n\n    tabs!: TabPanel[];\n\n    _activeIndex!: number;\n\n    preventActiveIndexPropagation!: boolean;\n\n    tabChanged: boolean | undefined;\n\n    backwardIsDisabled: boolean = true;\n\n    forwardIsDisabled: boolean = false;\n\n    private tabChangesSubscription!: Subscription;\n\n    nextIconTemplate: TemplateRef<any> | undefined;\n\n    previousIconTemplate: TemplateRef<any> | undefined;\n\n    resizeObserver: Nullable<ResizeObserver>;\n\n    container: HTMLDivElement | undefined;\n\n    list: HTMLUListElement | undefined;\n\n    buttonVisible: boolean;\n\n    @ViewChild('elementToObserve') elementToObserve: ElementRef;\n\n    constructor(@Inject(PLATFORM_ID) private platformId: any, public el: ElementRef, public cd: ChangeDetectorRef, private renderer: Renderer2) {}\n\n    ngAfterContentInit() {\n        this.initTabs();\n\n        this.tabChangesSubscription = (this.tabPanels as QueryList<TabPanel>).changes.subscribe((_) => {\n            this.initTabs();\n            this.refreshButtonState();\n            this.callResizeObserver();\n        });\n\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'previousicon':\n                    this.previousIconTemplate = item.template;\n                    break;\n\n                case 'nexticon':\n                    this.nextIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    callResizeObserver() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.autoHideButtons) {\n                this.bindResizeObserver();\n            }\n        }\n    }\n\n    ngAfterViewInit() {\n        this.callResizeObserver();\n    }\n\n    bindResizeObserver() {\n        this.container = DomHandler.findSingle(this.el.nativeElement, '[data-pc-section=\"navcontent\"]');\n        this.list = DomHandler.findSingle(this.el.nativeElement, '[data-pc-section=\"nav\"]');\n\n        this.resizeObserver = new ResizeObserver(() => {\n            if (this.list.offsetWidth >= this.container.offsetWidth) {\n                this.buttonVisible = true;\n            } else {\n                this.buttonVisible = false;\n            }\n            this.updateButtonState();\n            this.cd.detectChanges();\n        });\n        this.resizeObserver.observe(this.container);\n    }\n\n    unbindResizeObserver() {\n        this.resizeObserver.unobserve(this.elementToObserve.nativeElement);\n        this.resizeObserver = null;\n    }\n\n    ngAfterViewChecked() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.tabChanged) {\n                this.updateInkBar();\n                this.tabChanged = false;\n            }\n        }\n    }\n\n    ngOnDestroy(): void {\n        if (this.tabChangesSubscription) {\n            this.tabChangesSubscription.unsubscribe();\n        }\n\n        if (this.resizeObserver) {\n            this.unbindResizeObserver();\n        }\n    }\n\n    getTabHeaderActionId(tabId) {\n        return `${tabId}_header_action`;\n    }\n\n    getTabContentId(tabId) {\n        return `${tabId}_content`;\n    }\n\n    initTabs(): void {\n        this.tabs = (this.tabPanels as QueryList<TabPanel>).toArray();\n        let selectedTab: TabPanel = this.findSelectedTab() as TabPanel;\n        if (!selectedTab && this.tabs.length) {\n            if (this.activeIndex != null && this.tabs.length > this.activeIndex) this.tabs[this.activeIndex].selected = true;\n            else this.tabs[0].selected = true;\n\n            this.tabChanged = true;\n        }\n\n        this.cd.markForCheck();\n    }\n\n    onTabKeyDown(event: KeyboardEvent, tab: TabPanel): void {\n        switch (event.code) {\n            case 'ArrowLeft':\n                this.onTabArrowLeftKey(event);\n                break;\n\n            case 'ArrowRight':\n                this.onTabArrowRightKey(event);\n                break;\n\n            case 'Home':\n                this.onTabHomeKey(event);\n                break;\n\n            case 'End':\n                this.onTabEndKey(event);\n                break;\n\n            case 'PageDown':\n                this.onTabEndKey(event);\n                break;\n\n            case 'PageUp':\n                this.onTabHomeKey(event);\n                break;\n\n            case 'Enter':\n            case 'Space':\n                this.open(event, tab);\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onTabArrowLeftKey(event: KeyboardEvent) {\n        const prevHeaderAction = this.findPrevHeaderAction((<HTMLElement>event.target).parentElement);\n        const index = DomHandler.getAttribute(prevHeaderAction, 'data-pc-index');\n\n        prevHeaderAction ? this.changeFocusedTab(event, prevHeaderAction, index) : this.onTabEndKey(event);\n        event.preventDefault();\n    }\n\n    onTabArrowRightKey(event: KeyboardEvent) {\n        const nextHeaderAction = this.findNextHeaderAction((<HTMLElement>event.target).parentElement);\n        const index = DomHandler.getAttribute(nextHeaderAction, 'data-pc-index');\n\n        nextHeaderAction ? this.changeFocusedTab(event, nextHeaderAction, index) : this.onTabHomeKey(event);\n        event.preventDefault();\n    }\n\n    onTabHomeKey(event: KeyboardEvent) {\n        const firstHeaderAction = this.findFirstHeaderAction();\n        const index = DomHandler.getAttribute(firstHeaderAction, 'data-pc-index');\n\n        this.changeFocusedTab(event, firstHeaderAction, index);\n        event.preventDefault();\n    }\n\n    onTabEndKey(event: KeyboardEvent) {\n        const lastHeaderAction = this.findLastHeaderAction();\n        const index = DomHandler.getAttribute(lastHeaderAction, 'data-pc-index');\n\n        this.changeFocusedTab(event, lastHeaderAction, index);\n        event.preventDefault();\n    }\n\n    changeFocusedTab(event: KeyboardEvent, element: any, index: number) {\n        if (element) {\n            DomHandler.focus(element);\n            element.scrollIntoView({ block: 'nearest' });\n\n            if (this.selectOnFocus) {\n                const tab = this.tabs[index];\n                this.open(event, tab);\n            }\n        }\n    }\n\n    findNextHeaderAction(tabElement: any, selfCheck = false) {\n        const headerElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n        return headerElement\n            ? DomHandler.getAttribute(headerElement, 'data-p-disabled') || DomHandler.getAttribute(headerElement, 'data-pc-section') === 'inkbar'\n                ? this.findNextHeaderAction(headerElement)\n                : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]')\n            : null;\n    }\n\n    findPrevHeaderAction(tabElement: any, selfCheck = false) {\n        const headerElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n\n        return headerElement\n            ? DomHandler.getAttribute(headerElement, 'data-p-disabled') || DomHandler.getAttribute(headerElement, 'data-pc-section') === 'inkbar'\n                ? this.findPrevHeaderAction(headerElement)\n                : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]')\n            : null;\n    }\n\n    findFirstHeaderAction() {\n        const firstEl = this.navbar.nativeElement.firstElementChild;\n        return this.findNextHeaderAction(firstEl, true);\n    }\n\n    findLastHeaderAction() {\n        const lastEl = this.navbar.nativeElement.lastElementChild;\n        const lastHeaderAction = DomHandler.getAttribute(lastEl, 'data-pc-section') === 'inkbar' ? lastEl.previousElementSibling : lastEl;\n        return this.findPrevHeaderAction(lastHeaderAction, true);\n    }\n\n    open(event: Event, tab: TabPanel) {\n        if (tab.disabled) {\n            if (event) {\n                event.preventDefault();\n            }\n            return;\n        }\n\n        if (!tab.selected) {\n            let selectedTab: TabPanel = this.findSelectedTab() as TabPanel;\n            if (selectedTab) {\n                selectedTab.selected = false;\n            }\n\n            this.tabChanged = true;\n            tab.selected = true;\n            let selectedTabIndex = this.findTabIndex(tab);\n            this.preventActiveIndexPropagation = true;\n            this.activeIndexChange.emit(selectedTabIndex);\n            this.onChange.emit({ originalEvent: event, index: selectedTabIndex });\n\n            this.updateScrollBar(selectedTabIndex);\n        }\n\n        if (event) {\n            event.preventDefault();\n        }\n    }\n\n    close(event: Event, tab: TabPanel) {\n        if (this.controlClose) {\n            this.onClose.emit({\n                originalEvent: event,\n                index: this.findTabIndex(tab),\n                close: () => {\n                    this.closeTab(tab);\n                }\n            });\n        } else {\n            this.closeTab(tab);\n            this.onClose.emit({\n                originalEvent: event,\n                index: this.findTabIndex(tab)\n            });\n        }\n    }\n\n    closeTab(tab: TabPanel) {\n        if (tab.disabled) {\n            return;\n        }\n        if (tab.selected) {\n            this.tabChanged = true;\n            tab.selected = false;\n            for (let i = 0; i < this.tabs.length; i++) {\n                let tabPanel = this.tabs[i];\n                if (!tabPanel.closed && !tab.disabled) {\n                    tabPanel.selected = true;\n                    break;\n                }\n            }\n        }\n\n        tab.closed = true;\n    }\n\n    findSelectedTab(): TabPanel | null {\n        for (let i = 0; i < this.tabs.length; i++) {\n            if (this.tabs[i].selected) {\n                return this.tabs[i];\n            }\n        }\n        return null;\n    }\n\n    findTabIndex(tab: TabPanel) {\n        let index = -1;\n        for (let i = 0; i < this.tabs.length; i++) {\n            if (this.tabs[i] == tab) {\n                index = i;\n                break;\n            }\n        }\n        return index;\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    updateInkBar() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.navbar) {\n                const tabHeader: HTMLElement | null = DomHandler.findSingle(this.navbar.nativeElement, 'li.p-highlight');\n\n                if (!tabHeader) {\n                    return;\n                }\n\n                (this.inkbar as ElementRef).nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n                (this.inkbar as ElementRef).nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar.nativeElement).left + 'px';\n            }\n        }\n    }\n\n    updateScrollBar(index: number) {\n        let tabHeader = (this.navbar as ElementRef).nativeElement.children[index];\n\n        if (tabHeader) {\n            tabHeader.scrollIntoView({ block: 'nearest' });\n        }\n    }\n\n    updateButtonState() {\n        const content = (this.content as ElementRef).nativeElement;\n        const { scrollLeft, scrollWidth } = content;\n        const width = DomHandler.getWidth(content);\n\n        this.backwardIsDisabled = scrollLeft === 0;\n        this.forwardIsDisabled = Math.round(scrollLeft) === scrollWidth - width;\n    }\n\n    refreshButtonState() {\n        this.container = DomHandler.findSingle(this.el.nativeElement, '[data-pc-section=\"navcontent\"]');\n        this.list = DomHandler.findSingle(this.el.nativeElement, '[data-pc-section=\"nav\"]');\n        if (this.list.offsetWidth >= this.container.offsetWidth) {\n            if (this.list.offsetWidth >= this.container.offsetWidth) {\n                this.buttonVisible = true;\n            } else {\n                this.buttonVisible = false;\n            }\n            this.updateButtonState();\n            this.cd.markForCheck();\n        }\n    }\n\n    onScroll(event: Event) {\n        this.scrollable && this.updateButtonState();\n\n        event.preventDefault();\n    }\n\n    getVisibleButtonWidths() {\n        return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => (el ? acc + DomHandler.getWidth(el) : acc), 0);\n    }\n\n    navBackward() {\n        const content = (this.content as ElementRef).nativeElement;\n        const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n        const pos = content.scrollLeft - width;\n        content.scrollLeft = pos <= 0 ? 0 : pos;\n    }\n\n    navForward() {\n        const content = (this.content as ElementRef).nativeElement;\n        const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n        const pos = content.scrollLeft + width;\n        const lastPos = content.scrollWidth - width;\n\n        content.scrollLeft = pos >= lastPos ? lastPos : pos;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, TooltipModule, RippleModule, TimesIcon, ChevronLeftIcon, ChevronRightIcon],\n    exports: [TabView, TabPanel, SharedModule],\n    declarations: [TabView, TabPanel]\n})\nexport class TabViewModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAwCA;;;AAGG;MAwBU,QAAQ,CAAA;AAgKuD,IAAA,EAAA,CAAA;AAAuB,IAAA,aAAA,CAAA;AAAwC,IAAA,EAAA,CAAA;AA/JvI;;;AAGG;IACqC,QAAQ,GAAwB,KAAK,CAAC;AAC9E;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IACD,IAAI,WAAW,CAAC,WAAwD,EAAA;AACpE,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;AAChC,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAClC;AACD;;;AAGG;AACH,IAAA,IAAa,gBAAgB,GAAA;QACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC;KACjC;IACD,IAAI,gBAAgB,CAAC,gBAAoC,EAAA;AACrD,QAAA,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;AAC1C,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAClC;AACD;;;AAGG;IACqC,KAAK,GAAwB,IAAI,CAAC;AAC1E;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACM,eAAe,GAAoD,KAAK,CAAC;AAClF;;;AAGG;IACM,oBAAoB,GAAuB,UAAU,CAAC;AAC/D;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;;AAIG;AACH,IAAA,IAAa,QAAQ,GAAA;AACjB,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;KAC3B;IACD,IAAI,QAAQ,CAAC,GAAY,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AAErB,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC3B,SAAA;AAED,QAAA,IAAI,GAAG;AAAE,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KAC/B;AACD;;;;AAIG;AACH,IAAA,IAAa,QAAQ,GAAA;AACjB,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;KAC3B;IACD,IAAI,QAAQ,CAAC,QAAiB,EAAA;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAClC;AACD;;;AAGG;AACH,IAAA,IAAa,MAAM,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;IACD,IAAI,MAAM,CAAC,MAAc,EAAA;AACrB,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;;;AAItB,QAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AACxB,YAAA,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AACnC,SAAC,CAAC,CAAC;KACN;AACD;;;;AAIG;AACH,IAAA,IAAa,QAAQ,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACzB;IACD,IAAI,QAAQ,CAAC,QAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAClC;AACD;;;;AAIG;AACH,IAAA,IAAa,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;IACD,IAAI,SAAS,CAAC,SAA6B,EAAA;AACvC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAClC;AAE+B,IAAA,SAAS,CAAuC;IAEhF,MAAM,GAAY,KAAK,CAAC;IAExB,IAAI,GAAgC,IAAI,CAAC;AAEzC,IAAA,YAAY,CAA8C;AAE1D,IAAA,iBAAiB,CAAqB;AAEtC,IAAA,SAAS,CAAsB;AAE/B,IAAA,SAAS,CAAsB;AAE/B,IAAA,OAAO,CAAU;AAEjB,IAAA,SAAS,CAAU;IAEnB,UAAU,GAAuB,SAAS,CAAC;IAE3C,MAAM,GAAY,KAAK,CAAC;AAEjB,IAAA,EAAE,CAAqB;AAE9B,IAAA,eAAe,CAA+B;AAE9C,IAAA,cAAc,CAA+B;AAE7C,IAAA,gBAAgB,CAA+B;AAE/C,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,OAAO,CAAU;AAEjB,IAAA,WAAA,CAA+C,OAAgB,EAAS,EAAc,EAAS,aAA+B,EAAS,EAAqB,EAAA;QAApF,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAa,CAAA,aAAA,GAAb,aAAa,CAAkB;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;AACxJ,QAAA,IAAI,CAAC,OAAO,GAAG,OAAkB,CAAC;AAClC,QAAA,IAAI,CAAC,EAAE,GAAG,iBAAiB,EAAE,CAAC;KACjC;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB;AArMQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,kBAgKG,UAAU,CAAC,MAAM,OAAO,CAAC,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAhKpC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,uEAKG,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EA2BhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA4FnB,aAAa,EAjJpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;AAgBT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;;2FAKQ,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAvBpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;AAgBT,IAAA,CAAA;AACD,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAiKgB,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,OAAO,CAAC,CAAA;iIA3JL,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAWO,gBAAgB,EAAA,CAAA;sBAA5B,KAAK;gBAWkC,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAMO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAiBO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAWO,MAAM,EAAA,CAAA;sBAAlB,KAAK;gBAkBO,QAAQ,EAAA,CAAA;sBAApB,KAAK;gBAYO,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAQ0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;AA2ElC;;;AAGG;MA0FU,OAAO,CAAA;AAqIyB,IAAA,UAAA,CAAA;AAAwB,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAA+B,IAAA,QAAA,CAAA;AApIvH;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;;AAIG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;;AAIG;AACqC,IAAA,UAAU,CAAsB;AACxE;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;KAC5B;IACD,IAAI,WAAW,CAAC,GAAW,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,IAAI,CAAC,6BAA6B,EAAE;AACpC,YAAA,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;YAC3C,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE;AACnG,YAAA,IAAI,CAAC,eAAe,EAAe,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7C,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAEvB,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AAC7B,SAAA;KACJ;AACD;;;AAGG;IACqC,aAAa,GAAY,KAAK,CAAC;AACvE;;;AAGG;AACM,IAAA,mBAAmB,CAAqB;AACjD;;;AAGG;AACM,IAAA,mBAAmB,CAAqB;AACjD;;;AAGG;IACqC,eAAe,GAAY,IAAI,CAAC;AACxE;;;AAGG;IACoC,QAAQ,GAAW,CAAC,CAAC;AAC5D;;;;AAIG;AACO,IAAA,QAAQ,GAAqC,IAAI,YAAY,EAAsB,CAAC;AAC9F;;;;AAIG;AACO,IAAA,OAAO,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAC3F;;;;AAIG;AACO,IAAA,iBAAiB,GAAyB,IAAI,YAAY,EAAU,CAAC;AAEzD,IAAA,OAAO,CAA8B;AAEtC,IAAA,MAAM,CAAgC;AAErC,IAAA,OAAO,CAAc;AAErB,IAAA,OAAO,CAAc;AAEtB,IAAA,MAAM,CAAc;AAEd,IAAA,SAAS,CAAkC;AAEtC,IAAA,SAAS,CAAuC;AAEhF,IAAA,WAAW,CAAsB;AAEjC,IAAA,IAAI,CAAc;AAElB,IAAA,YAAY,CAAU;AAEtB,IAAA,6BAA6B,CAAW;AAExC,IAAA,UAAU,CAAsB;IAEhC,kBAAkB,GAAY,IAAI,CAAC;IAEnC,iBAAiB,GAAY,KAAK,CAAC;AAE3B,IAAA,sBAAsB,CAAgB;AAE9C,IAAA,gBAAgB,CAA+B;AAE/C,IAAA,oBAAoB,CAA+B;AAEnD,IAAA,cAAc,CAA2B;AAEzC,IAAA,SAAS,CAA6B;AAEtC,IAAA,IAAI,CAA+B;AAEnC,IAAA,aAAa,CAAU;AAEQ,IAAA,gBAAgB,CAAa;AAE5D,IAAA,WAAA,CAAyC,UAAe,EAAS,EAAc,EAAS,EAAqB,EAAU,QAAmB,EAAA;QAAjG,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAAI;IAE9I,kBAAkB,GAAA;QACd,IAAI,CAAC,QAAQ,EAAE,CAAC;AAEhB,QAAA,IAAI,CAAC,sBAAsB,GAAI,IAAI,CAAC,SAAiC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;YAC1F,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC9B,SAAC,CAAC,CAAC;QAEF,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,aAAA;AACJ,SAAA;KACJ;IAED,eAAe,GAAA;QACX,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,gCAAgC,CAAC,CAAC;AAChG,QAAA,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,yBAAyB,CAAC,CAAC;AAEpF,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,MAAK;YAC1C,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AACrD,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC9B,aAAA;YACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC5B,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC/C;IAED,oBAAoB,GAAA;QAChB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;KAC9B;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,YAAY,EAAE,CAAC;AACpB,gBAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC3B,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC7C,SAAA;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,KAAK,EAAA;QACtB,OAAO,CAAA,EAAG,KAAK,CAAA,cAAA,CAAgB,CAAC;KACnC;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,OAAO,CAAA,EAAG,KAAK,CAAA,QAAA,CAAU,CAAC;KAC7B;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC,SAAiC,CAAC,OAAO,EAAE,CAAC;AAC9D,QAAA,IAAI,WAAW,GAAa,IAAI,CAAC,eAAe,EAAc,CAAC;QAC/D,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAClC,YAAA,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW;gBAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;;gBAC5G,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AAElC,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,YAAY,CAAC,KAAoB,EAAE,GAAa,EAAA;QAC5C,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC9B,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBAC/B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,UAAU;AACX,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,iBAAiB,CAAC,KAAoB,EAAA;AAClC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAe,KAAK,CAAC,MAAO,CAAC,aAAa,CAAC,CAAC;QAC9F,MAAM,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAEzE,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,gBAAgB,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACnG,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,kBAAkB,CAAC,KAAoB,EAAA;AACnC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAe,KAAK,CAAC,MAAO,CAAC,aAAa,CAAC,CAAC;QAC9F,MAAM,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAEzE,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,gBAAgB,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACpG,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAoB,EAAA;AAC7B,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACvD,MAAM,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAE1E,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACvD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACrD,MAAM,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;QAEzE,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;QACtD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,KAAoB,EAAE,OAAY,EAAE,KAAa,EAAA;AAC9D,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC1B,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YAE7C,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7B,gBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACzB,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,UAAe,EAAE,SAAS,GAAG,KAAK,EAAA;AACnD,QAAA,MAAM,aAAa,GAAG,SAAS,GAAG,UAAU,GAAG,UAAU,CAAC,kBAAkB,CAAC;AAC7E,QAAA,OAAO,aAAa;AAChB,cAAE,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC,KAAK,QAAQ;AACjI,kBAAE,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC;kBACxC,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,kCAAkC,CAAC;cAC5E,IAAI,CAAC;KACd;AAED,IAAA,oBAAoB,CAAC,UAAe,EAAE,SAAS,GAAG,KAAK,EAAA;AACnD,QAAA,MAAM,aAAa,GAAG,SAAS,GAAG,UAAU,GAAG,UAAU,CAAC,sBAAsB,CAAC;AAEjF,QAAA,OAAO,aAAa;AAChB,cAAE,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC,KAAK,QAAQ;AACjI,kBAAE,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC;kBACxC,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,kCAAkC,CAAC;cAC5E,IAAI,CAAC;KACd;IAED,qBAAqB,GAAA;QACjB,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAC5D,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KACnD;IAED,oBAAoB,GAAA;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC;QAC1D,MAAM,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,iBAAiB,CAAC,KAAK,QAAQ,GAAG,MAAM,CAAC,sBAAsB,GAAG,MAAM,CAAC;QAClI,OAAO,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;KAC5D;IAED,IAAI,CAAC,KAAY,EAAE,GAAa,EAAA;QAC5B,IAAI,GAAG,CAAC,QAAQ,EAAE;AACd,YAAA,IAAI,KAAK,EAAE;gBACP,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,aAAA;YACD,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,WAAW,GAAa,IAAI,CAAC,eAAe,EAAc,CAAC;AAC/D,YAAA,IAAI,WAAW,EAAE;AACb,gBAAA,WAAW,CAAC,QAAQ,GAAG,KAAK,CAAC;AAChC,aAAA;AAED,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,YAAA,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;YACpB,IAAI,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;AAC1C,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;AAEtE,YAAA,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;AAC1C,SAAA;AAED,QAAA,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,KAAK,CAAC,KAAY,EAAE,GAAa,EAAA;QAC7B,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AACd,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;gBAC7B,KAAK,EAAE,MAAK;AACR,oBAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;iBACtB;AACJ,aAAA,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACnB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AACd,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;AAChC,aAAA,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,GAAa,EAAA;QAClB,IAAI,GAAG,CAAC,QAAQ,EAAE;YACd,OAAO;AACV,SAAA;QACD,IAAI,GAAG,CAAC,QAAQ,EAAE;AACd,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,YAAA,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC;AACrB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACvC,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;AACnC,oBAAA,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;oBACzB,MAAM;AACT,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;KACrB;IAED,eAAe,GAAA;AACX,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;AACvB,gBAAA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvB,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED,IAAA,YAAY,CAAC,GAAa,EAAA;AACtB,QAAA,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACf,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;gBACrB,KAAK,GAAG,CAAC,CAAC;gBACV,MAAM;AACT,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,YAAY,GAAA;AACR,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,gBAAA,MAAM,SAAS,GAAuB,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;gBAEzG,IAAI,CAAC,SAAS,EAAE;oBACZ,OAAO;AACV,iBAAA;AAEA,gBAAA,IAAI,CAAC,MAAqB,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AAC7F,gBAAA,IAAI,CAAC,MAAqB,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7J,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,SAAS,GAAI,IAAI,CAAC,MAAqB,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAE1E,QAAA,IAAI,SAAS,EAAE;YACX,SAAS,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AAClD,SAAA;KACJ;IAED,iBAAiB,GAAA;AACb,QAAA,MAAM,OAAO,GAAI,IAAI,CAAC,OAAsB,CAAC,aAAa,CAAC;AAC3D,QAAA,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAC5C,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAE3C,QAAA,IAAI,CAAC,kBAAkB,GAAG,UAAU,KAAK,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,WAAW,GAAG,KAAK,CAAC;KAC3E;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,gCAAgC,CAAC,CAAC;AAChG,QAAA,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,yBAAyB,CAAC,CAAC;QACpF,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACrD,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AACrD,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC9B,aAAA;YACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAY,EAAA;AACjB,QAAA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE5C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,sBAAsB,GAAA;QAClB,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;KACxI;IAED,WAAW,GAAA;AACP,QAAA,MAAM,OAAO,GAAI,IAAI,CAAC,OAAsB,CAAC,aAAa,CAAC;AAC3D,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC3E,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;AACvC,QAAA,OAAO,CAAC,UAAU,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KAC3C;IAED,UAAU,GAAA;AACN,QAAA,MAAM,OAAO,GAAI,IAAI,CAAC,OAAsB,CAAC,aAAa,CAAC;AAC3D,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC3E,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;AACvC,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;AAE5C,QAAA,OAAO,CAAC,UAAU,GAAG,GAAG,IAAI,OAAO,GAAG,OAAO,GAAG,GAAG,CAAC;KACvD;AApfQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kBAqII,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AArItB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,EAgBI,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAMhB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,iFA2BhB,gBAAgB,CAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,eAAA,EAAA,CAAA,iBAAA,EAAA,iBAAA,EAehB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,eAAe,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,OAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA8BlB,QAAQ,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAER,aAAa,EA5LpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+ET,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,i5BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAggBkE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,gBAAgB,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAxftG,OAAO,EAAA,UAAA,EAAA,CAAA;kBAzFnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+ET,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,i5BAAA,CAAA,EAAA,CAAA;;0BAuIY,MAAM;2BAAC,WAAW,CAAA;0HAhItB,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAMkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAME,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAsBkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKkC,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAM3B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAEe,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS,CAAA;gBAEC,MAAM,EAAA,CAAA;sBAA1B,SAAS;uBAAC,QAAQ,CAAA;gBAEG,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS,CAAA;gBAEE,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS,CAAA;gBAEC,MAAM,EAAA,CAAA;sBAA1B,SAAS;uBAAC,QAAQ,CAAA;gBAEQ,SAAS,EAAA,CAAA;sBAAnC,eAAe;uBAAC,QAAQ,CAAA;gBAEO,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBA8BC,gBAAgB,EAAA,CAAA;sBAA9C,SAAS;uBAAC,kBAAkB,CAAA;;MAyXpB,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAb,aAAa,EAAA,YAAA,EAAA,CA5fb,OAAO,EApSP,QAAQ,aA4xBP,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,aAxftG,OAAO,EApSP,QAAQ,EA6xBY,YAAY,CAAA,EAAA,CAAA,CAAA;AAGhC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAJZ,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAClF,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGhC,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,CAAC;AAChH,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC;AAC1C,oBAAA,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;AACpC,iBAAA,CAAA;;;ACl2BD;;AAEG;;;;"}