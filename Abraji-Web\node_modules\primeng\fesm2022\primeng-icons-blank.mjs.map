{"version": 3, "file": "primeng-icons-blank.mjs", "sources": ["../../src/app/components/icons/blank/blank.ts", "../../src/app/components/icons/blank/primeng-icons-blank.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/baseicon';\n\n@Component({\n    selector: 'BlankIcon',\n    standalone: true,\n    imports: [BaseIcon],\n    template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" v-bind=\"pti()\">\n            <rect width=\"1\" height=\"1\" fill=\"currentColor\" fill-opacity=\"0\" />\n        </svg>\n    `\n})\nexport class BlankIcon extends BaseIcon {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;AAaM,MAAO,SAAU,SAAQ,QAAQ,CAAA;uGAA1B,SAAS,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EANR,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;2FAEQ,SAAS,EAAA,UAAA,EAAA,CAAA;kBAVrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,CAAC,QAAQ,CAAC;AACnB,oBAAA,QAAQ,EAAE,CAAA;;;;AAIT,IAAA,CAAA;AACJ,iBAAA,CAAA;;;ACZD;;AAEG;;;;"}