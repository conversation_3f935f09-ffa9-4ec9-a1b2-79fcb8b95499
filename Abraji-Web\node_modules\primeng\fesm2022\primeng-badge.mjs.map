{"version": 3, "file": "primeng-badge.mjs", "sources": ["../../src/app/components/badge/badge.ts", "../../src/app/components/badge/primeng-badge.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT } from '@angular/common';\nimport { AfterViewInit, ChangeDetectionStrategy, Component, Directive, ElementRef, Inject, Input, NgModule, Renderer2, OnChanges, SimpleChanges, ViewEncapsulation, booleanAttribute } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { UniqueComponentId } from 'primeng/utils';\n/**\n * Badge Directive is directive usage of badge component.\n * @group Components\n */\n@Directive({\n    selector: '[pBadge]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class BadgeDirective implements OnChanges, AfterViewInit {\n    /**\n     * When specified, disables the component.\n     * @group Props\n     */\n    @Input('badgeDisabled') public disabled: boolean;\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     */\n    @Input() public badgeSize: 'large' | 'xlarge' | undefined;\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     * @deprecated use badgeSize instead.\n     */\n    @Input() public set size(value: 'large' | 'xlarge') {\n        this._size = value;\n        console.warn('size property is deprecated and will removed in v18, use badgeSize instead.');\n    }\n    get size() {\n        return this._size;\n    }\n    _size: 'large' | 'xlarge';\n    /**\n     * Severity type of the badge.\n     * @group Props\n     */\n    @Input() public severity: 'success' | 'info' | 'warning' | 'danger' | null | undefined;\n    /**\n     * Value to display inside the badge.\n     * @group Props\n     */\n    @Input() public value: string | number;\n\n    private id!: string;\n\n    private get activeElement(): HTMLElement {\n        return this.el.nativeElement.nodeName.indexOf('-') != -1 ? this.el.nativeElement.firstChild : this.el.nativeElement;\n    }\n\n    private get canUpdateBadge(): boolean {\n        return this.id && !this.disabled;\n    }\n\n    constructor(@Inject(DOCUMENT) private document: Document, public el: ElementRef, private renderer: Renderer2) {}\n\n    public ngOnChanges({ value, size, severity, disabled }: SimpleChanges): void {\n        if (disabled) {\n            this.toggleDisableState();\n        }\n\n        if (!this.canUpdateBadge) {\n            return;\n        }\n\n        if (severity) {\n            this.setSeverity(severity.previousValue);\n        }\n\n        if (size) {\n            this.setSizeClasses();\n        }\n\n        if (value) {\n            this.setValue();\n        }\n    }\n\n    public ngAfterViewInit(): void {\n        this.id = UniqueComponentId() + '_badge';\n        this.renderBadgeContent();\n    }\n\n    private setValue(element?: HTMLElement): void {\n        const badge = element ?? this.document.getElementById(this.id);\n\n        if (!badge) {\n            return;\n        }\n\n        if (this.value != null) {\n            if (DomHandler.hasClass(badge, 'p-badge-dot')) {\n                DomHandler.removeClass(badge, 'p-badge-dot');\n            }\n\n            if (this.value && String(this.value).length === 1) {\n                DomHandler.addClass(badge, 'p-badge-no-gutter');\n            } else {\n                DomHandler.removeClass(badge, 'p-badge-no-gutter');\n            }\n        } else {\n            if (!DomHandler.hasClass(badge, 'p-badge-dot')) {\n                DomHandler.addClass(badge, 'p-badge-dot');\n            }\n\n            DomHandler.removeClass(badge, 'p-badge-no-gutter');\n        }\n\n        badge.innerHTML = '';\n        const badgeValue = this.value != null ? String(this.value) : '';\n        this.renderer.appendChild(badge, this.document.createTextNode(badgeValue));\n    }\n\n    private setSizeClasses(element?: HTMLElement): void {\n        const badge = element ?? this.document.getElementById(this.id);\n\n        if (!badge) {\n            return;\n        }\n\n        if (this.badgeSize) {\n            if (this.badgeSize === 'large') {\n                DomHandler.addClass(badge, 'p-badge-lg');\n                DomHandler.removeClass(badge, 'p-badge-xl');\n            }\n\n            if (this.badgeSize === 'xlarge') {\n                DomHandler.addClass(badge, 'p-badge-xl');\n                DomHandler.removeClass(badge, 'p-badge-lg');\n            }\n        } else if (this.size && !this.badgeSize) {\n            if (this.size === 'large') {\n                DomHandler.addClass(badge, 'p-badge-lg');\n                DomHandler.removeClass(badge, 'p-badge-xl');\n            }\n\n            if (this.size === 'xlarge') {\n                DomHandler.addClass(badge, 'p-badge-xl');\n                DomHandler.removeClass(badge, 'p-badge-lg');\n            }\n        } else {\n            DomHandler.removeClass(badge, 'p-badge-lg');\n            DomHandler.removeClass(badge, 'p-badge-xl');\n        }\n    }\n\n    private renderBadgeContent(): void {\n        if (this.disabled) {\n            return null;\n        }\n\n        const el = this.activeElement;\n        const badge = this.document.createElement('span');\n        badge.id = this.id;\n        badge.className = 'p-badge p-component';\n\n        this.setSeverity(null, badge);\n        this.setSizeClasses(badge);\n        this.setValue(badge);\n        DomHandler.addClass(el, 'p-overlay-badge');\n        this.renderer.appendChild(el, badge);\n    }\n\n    private setSeverity(oldSeverity?: 'success' | 'info' | 'warning' | 'danger' | null, element?: HTMLElement): void {\n        const badge = element ?? this.document.getElementById(this.id);\n\n        if (!badge) {\n            return;\n        }\n\n        if (this.severity) {\n            DomHandler.addClass(badge, `p-badge-${this.severity}`);\n        }\n\n        if (oldSeverity) {\n            DomHandler.removeClass(badge, `p-badge-${oldSeverity}`);\n        }\n    }\n\n    private toggleDisableState(): void {\n        if (!this.id) {\n            return;\n        }\n\n        if (this.disabled) {\n            const badge = this.activeElement?.querySelector(`#${this.id}`);\n\n            if (badge) {\n                this.renderer.removeChild(this.activeElement, badge);\n            }\n        } else {\n            this.renderBadgeContent();\n        }\n    }\n}\n/**\n * Badge is a small status indicator for another element.\n * @group Components\n */\n@Component({\n    selector: 'p-badge',\n    template: ` <span *ngIf=\"!badgeDisabled\" [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">{{ value }}</span> `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./badge.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Badge {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     */\n    @Input() badgeSize: 'large' | 'xlarge' | undefined;\n    /**\n     * Severity type of the badge.\n     * @group Props\n     */\n    @Input() severity: 'success' | 'info' | 'warning' | 'danger' | 'help' | 'primary' | 'secondary' | 'contrast' | null | undefined;\n    /**\n     * Value to display inside the badge.\n     * @group Props\n     */\n    @Input() value: string | number | null | undefined;\n    /**\n     * When specified, disables the component.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) badgeDisabled: boolean = false;\n    /**\n     * Size of the badge, valid options are \"large\" and \"xlarge\".\n     * @group Props\n     * @deprecated use badgeSize instead.\n     */\n    @Input() public set size(value: 'large' | 'xlarge') {\n        this._size = value;\n        console.warn('size property is deprecated and will removed in v18, use badgeSize instead.');\n    }\n    get size() {\n        return this._size;\n    }\n    _size: 'large' | 'xlarge';\n\n    containerClass() {\n        return {\n            'p-badge p-component': true,\n            'p-badge-no-gutter': this.value != undefined && String(this.value).length === 1,\n            'p-badge-lg': this.badgeSize === 'large' || this.size === 'large',\n            'p-badge-xl': this.badgeSize === 'xlarge' || this.size === 'xlarge',\n            [`p-badge-${this.severity}`]: this.severity\n        };\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Badge, BadgeDirective, SharedModule],\n    declarations: [Badge, BadgeDirective]\n})\nexport class BadgeModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;AAKA;;;AAGG;MAOU,cAAc,CAAA;AA6Ce,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAAwB,IAAA,QAAA,CAAA;AA5CzF;;;AAGG;AAC4B,IAAA,QAAQ,CAAU;AACjD;;;AAGG;AACa,IAAA,SAAS,CAAiC;AAC1D;;;;AAIG;IACH,IAAoB,IAAI,CAAC,KAAyB,EAAA;AAC9C,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,OAAO,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;KAC/F;AACD,IAAA,IAAI,IAAI,GAAA;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;AACD,IAAA,KAAK,CAAqB;AAC1B;;;AAGG;AACa,IAAA,QAAQ,CAA+D;AACvF;;;AAGG;AACa,IAAA,KAAK,CAAkB;AAE/B,IAAA,EAAE,CAAU;AAEpB,IAAA,IAAY,aAAa,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC;KACvH;AAED,IAAA,IAAY,cAAc,GAAA;QACtB,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;KACpC;AAED,IAAA,WAAA,CAAsC,QAAkB,EAAS,EAAc,EAAU,QAAmB,EAAA;QAAtE,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAAI;IAEzG,WAAW,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAiB,EAAA;AACjE,QAAA,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO;AACV,SAAA;AAED,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC5C,SAAA;AAED,QAAA,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,cAAc,EAAE,CAAC;AACzB,SAAA;AAED,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,QAAQ,EAAE,CAAC;AACnB,SAAA;KACJ;IAEM,eAAe,GAAA;AAClB,QAAA,IAAI,CAAC,EAAE,GAAG,iBAAiB,EAAE,GAAG,QAAQ,CAAC;QACzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B;AAEO,IAAA,QAAQ,CAAC,OAAqB,EAAA;AAClC,QAAA,MAAM,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE/D,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;AACV,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACpB,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE;AAC3C,gBAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AAChD,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/C,gBAAA,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC;AACnD,aAAA;AAAM,iBAAA;AACH,gBAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC;AACtD,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE;AAC5C,gBAAA,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AAC7C,aAAA;AAED,YAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC;AACtD,SAAA;AAED,QAAA,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAChE,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;KAC9E;AAEO,IAAA,cAAc,CAAC,OAAqB,EAAA;AACxC,QAAA,MAAM,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE/D,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO,EAAE;AAC5B,gBAAA,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACzC,gBAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC/C,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;AAC7B,gBAAA,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACzC,gBAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC/C,aAAA;AACJ,SAAA;aAAM,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACrC,YAAA,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvB,gBAAA,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACzC,gBAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC/C,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AACxB,gBAAA,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACzC,gBAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC/C,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC5C,YAAA,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC/C,SAAA;KACJ;IAEO,kBAAkB,GAAA;QACtB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAClD,QAAA,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACnB,QAAA,KAAK,CAAC,SAAS,GAAG,qBAAqB,CAAC;AAExC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC9B,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACrB,QAAA,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;KACxC;IAEO,WAAW,CAAC,WAA8D,EAAE,OAAqB,EAAA;AACrG,QAAA,MAAM,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE/D,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAW,QAAA,EAAA,IAAI,CAAC,QAAQ,CAAE,CAAA,CAAC,CAAC;AAC1D,SAAA;AAED,QAAA,IAAI,WAAW,EAAE;YACb,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,CAAW,QAAA,EAAA,WAAW,CAAE,CAAA,CAAC,CAAC;AAC3D,SAAA;KACJ;IAEO,kBAAkB,GAAA;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACV,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAA,CAAA,EAAI,IAAI,CAAC,EAAE,CAAA,CAAE,CAAC,CAAC;AAE/D,YAAA,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AACxD,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,SAAA;KACJ;AAxLQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,kBA6CH,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FA7CnB,cAAc,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,eAAA,EAAA,UAAA,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAN1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,UAAU;AACpB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BA8CgB,MAAM;2BAAC,QAAQ,CAAA;0FAxCG,QAAQ,EAAA,CAAA;sBAAtC,KAAK;uBAAC,eAAe,CAAA;gBAKN,SAAS,EAAA,CAAA;sBAAxB,KAAK;gBAMc,IAAI,EAAA,CAAA;sBAAvB,KAAK;gBAYU,QAAQ,EAAA,CAAA;sBAAvB,KAAK;gBAKU,KAAK,EAAA,CAAA;sBAApB,KAAK;;AAyJV;;;AAGG;MAWU,KAAK,CAAA;AACd;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,SAAS,CAAiC;AACnD;;;AAGG;AACM,IAAA,QAAQ,CAA+G;AAChI;;;AAGG;AACM,IAAA,KAAK,CAAqC;AACnD;;;AAGG;IACqC,aAAa,GAAY,KAAK,CAAC;AACvE;;;;AAIG;IACH,IAAoB,IAAI,CAAC,KAAyB,EAAA;AAC9C,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,OAAO,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;KAC/F;AACD,IAAA,IAAI,IAAI,GAAA;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;AACD,IAAA,KAAK,CAAqB;IAE1B,cAAc,GAAA;QACV,OAAO;AACH,YAAA,qBAAqB,EAAE,IAAI;AAC3B,YAAA,mBAAmB,EAAE,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC;YAC/E,YAAY,EAAE,IAAI,CAAC,SAAS,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;YACjE,YAAY,EAAE,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ;YACnE,CAAC,CAAA,QAAA,EAAW,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ;SAC9C,CAAC;KACL;uGArDQ,KAAK,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAL,KAAK,EAAA,QAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EA8BM,gBAAgB,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAtC1B,CAAuH,qHAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,oYAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQxH,KAAK,EAAA,UAAA,EAAA,CAAA;kBAVjB,SAAS;+BACI,SAAS,EAAA,QAAA,EACT,CAAuH,qHAAA,CAAA,EAAA,eAAA,EAChH,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,oYAAA,CAAA,EAAA,CAAA;8BAOQ,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAMlB,IAAI,EAAA,CAAA;sBAAvB,KAAK;;MAyBG,WAAW,CAAA;uGAAX,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAX,WAAW,EAAA,YAAA,EAAA,CA7DX,KAAK,EAxML,cAAc,CAAA,EAAA,OAAA,EAAA,CAiQb,YAAY,CAAA,EAAA,OAAA,EAAA,CAzDb,KAAK,EAxML,cAAc,EAkQU,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGpC,WAAW,EAAA,OAAA,EAAA,CAJV,YAAY,EACW,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGpC,WAAW,EAAA,UAAA,EAAA,CAAA;kBALvB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,CAAC;AAC9C,oBAAA,YAAY,EAAE,CAAC,KAAK,EAAE,cAAc,CAAC;AACxC,iBAAA,CAAA;;;ACnRD;;AAEG;;;;"}