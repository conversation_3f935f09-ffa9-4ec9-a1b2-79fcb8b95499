import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HeaderApi } from '../api/header.api';
import { HeaderData, ManagerModel } from '../api/header';
import { LocalStorageService } from '../../auth-services/services/local-storage.service';
import { AuthService } from '../../auth-services/services/auth.service';

@Injectable({
  providedIn: 'root',
})
export class HeaderService implements HeaderData {
  private headerApi = inject(HeaderApi);
  private localStorage = inject(LocalStorageService);
  private authService = inject(AuthService);

  getManager(): Observable<ManagerModel> {
    return this.headerApi.getManager();
  }

  getAllManagers(): Observable<any> {
    return this.headerApi.getAllManagers();
  }

  saveCredentials(credentials: ManagerModel): void {
    this.localStorage.saveObj(credentials, this.localStorage.CREDENTIALS);
  }

  getCredentials(): ManagerModel {
    return this.localStorage.getObj(this.localStorage.CREDENTIALS);
  }

  logout(): void {
    this.authService.logout();
  }


}
