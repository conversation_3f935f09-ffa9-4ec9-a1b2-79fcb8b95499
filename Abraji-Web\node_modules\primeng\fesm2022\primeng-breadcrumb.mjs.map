{"version": 3, "file": "primeng-breadcrumb.mjs", "sources": ["../../src/app/components/breadcrumb/breadcrumb.ts", "../../src/app/components/breadcrumb/primeng-breadcrumb.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewEncapsulation } from '@angular/core';\nimport { Router, RouterModule } from '@angular/router';\nimport { MenuItem, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { HomeIcon } from 'primeng/icons/home';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { BreadcrumbItemClickEvent } from './breadcrumb.interface';\n/**\n * Breadcrumb provides contextual information about page hierarchy.\n * @group Components\n */\n@Component({\n    selector: 'p-breadcrumb',\n    template: `\n        <nav [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-breadcrumb p-component'\" [attr.data-pc-name]=\"'breadcrumb'\" [attr.data-pc-section]=\"'root'\">\n            <ol [attr.data-pc-section]=\"'menu'\" class=\"p-breadcrumb-list\">\n                <li\n                    [class]=\"home.styleClass\"\n                    [attr.id]=\"home.id\"\n                    [ngClass]=\"{ 'p-breadcrumb-home': true, 'p-disabled': home.disabled }\"\n                    [ngStyle]=\"home.style\"\n                    *ngIf=\"home\"\n                    pTooltip\n                    [tooltipOptions]=\"home.tooltipOptions\"\n                    [attr.data-pc-section]=\"'home'\"\n                >\n                    <a\n                        [href]=\"home.url ? home.url : null\"\n                        *ngIf=\"!home.routerLink\"\n                        [attr.aria-label]=\"homeAriaLabel\"\n                        class=\"p-menuitem-link\"\n                        (click)=\"onClick($event, home)\"\n                        [target]=\"home.target\"\n                        [attr.title]=\"home.title\"\n                        [attr.tabindex]=\"home.disabled ? null : '0'\"\n                        [attr.ariaCurrentWhenActive]=\"isCurrentUrl(home)\"\n                    >\n                        <span *ngIf=\"home.icon\" class=\"p-menuitem-icon\" [ngClass]=\"home.icon\" [ngStyle]=\"home.iprivateyle\"></span>\n                        <HomeIcon *ngIf=\"!home.icon\" [styleClass]=\"'p-menuitem-icon'\" />\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeLabel\" class=\"p-menuitem-text\">{{ home.label }}</span>\n                            <ng-template #htmlHomeLabel><span class=\"p-menuitem-text\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                    <a\n                        *ngIf=\"home.routerLink\"\n                        [routerLink]=\"home.routerLink\"\n                        [attr.aria-label]=\"homeAriaLabel\"\n                        [queryParams]=\"home.queryParams\"\n                        [routerLinkActive]=\"'p-menuitem-link-active'\"\n                        [routerLinkActiveOptions]=\"home.routerLinkActiveOptions || { exact: false }\"\n                        class=\"p-menuitem-link\"\n                        (click)=\"onClick($event, home)\"\n                        [target]=\"home.target\"\n                        [attr.title]=\"home.title\"\n                        [attr.tabindex]=\"home.disabled ? '-1' : '0'\"\n                        [attr.ariaCurrentWhenActive]=\"isCurrentUrl(home)\"\n                        [fragment]=\"home.fragment\"\n                        [queryParamsHandling]=\"home.queryParamsHandling\"\n                        [preserveFragment]=\"home.preserveFragment\"\n                        [skipLocationChange]=\"home.skipLocationChange\"\n                        [replaceUrl]=\"home.replaceUrl\"\n                        [state]=\"home.state\"\n                    >\n                        <span *ngIf=\"home.icon\" class=\"p-menuitem-icon\" [ngClass]=\"home.icon\" [ngStyle]=\"home.iconStyle\"></span>\n                        <HomeIcon *ngIf=\"!home.icon\" [styleClass]=\"'p-menuitem-icon'\" />\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeRouteLabel\" class=\"p-menuitem-text\">{{ home.label }}</span>\n                            <ng-template #htmlHomeRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                </li>\n                <li *ngIf=\"model && home\" class=\"p-menuitem-separator\" [attr.data-pc-section]=\"'separator'\">\n                    <ChevronRightIcon *ngIf=\"!separatorTemplate\" />\n                    <ng-template *ngTemplateOutlet=\"separatorTemplate\"></ng-template>\n                </li>\n                <ng-template ngFor let-item let-end=\"last\" [ngForOf]=\"model\">\n                    <li [class]=\"item.styleClass\" [attr.id]=\"item.id\" [ngStyle]=\"item.style\" [ngClass]=\"{ 'p-disabled': item.disabled }\" pTooltip [tooltipOptions]=\"item.tooltipOptions\" [attr.data-pc-section]=\"'menuitem'\">\n                        <a\n                            *ngIf=\"!item.routerLink\"\n                            [attr.href]=\"item.url ? item.url : null\"\n                            class=\"p-menuitem-link\"\n                            (click)=\"onClick($event, item)\"\n                            [target]=\"item.target\"\n                            [attr.title]=\"item.title\"\n                            [attr.tabindex]=\"item.disabled ? null : '0'\"\n                            [attr.ariaCurrentWhenActive]=\"isCurrentUrl(item)\"\n                        >\n                            <ng-container *ngIf=\"!itemTemplate\">\n                                <span *ngIf=\"item.icon\" class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                <ng-container *ngIf=\"item.label\">\n                                    <span *ngIf=\"item.escape !== false; else htmlLabel\" class=\"p-menuitem-text\">{{ item.label }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                </ng-container>\n                            </ng-container>\n                            <ng-container *ngIf=\"itemTemplate\">\n                                <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-template>\n                            </ng-container>\n                        </a>\n                        <a\n                            *ngIf=\"item.routerLink\"\n                            [routerLink]=\"item.routerLink\"\n                            [queryParams]=\"item.queryParams\"\n                            [routerLinkActive]=\"'p-menuitem-link-active'\"\n                            [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                            class=\"p-menuitem-link\"\n                            (click)=\"onClick($event, item)\"\n                            [target]=\"item.target\"\n                            [attr.title]=\"item.title\"\n                            [attr.tabindex]=\"item.disabled ? null : '0'\"\n                            [fragment]=\"item.fragment\"\n                            [queryParamsHandling]=\"item.queryParamsHandling\"\n                            [preserveFragment]=\"item.preserveFragment\"\n                            [skipLocationChange]=\"item.skipLocationChange\"\n                            [replaceUrl]=\"item.replaceUrl\"\n                            [state]=\"item.state\"\n                            [attr.ariaCurrentWhenActive]=\"isCurrentUrl(item)\"\n                        >\n                            <ng-container *ngIf=\"!itemTemplate\">\n                                <span *ngIf=\"item.icon\" class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                <ng-container *ngIf=\"item.label\">\n                                    <span *ngIf=\"item.escape !== false; else htmlRouteLabel\" class=\"p-menuitem-text\">{{ item.label }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                </ng-container>\n                            </ng-container>\n                            <ng-container *ngIf=\"itemTemplate\">\n                                <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-template>\n                            </ng-container>\n                        </a>\n                    </li>\n                    <li *ngIf=\"!end\" class=\"p-menuitem-separator\" [attr.data-pc-section]=\"'separator'\">\n                        <ChevronRightIcon *ngIf=\"!separatorTemplate\" />\n                        <ng-template *ngTemplateOutlet=\"separatorTemplate\"></ng-template>\n                    </li>\n                </ng-template>\n            </ol>\n        </nav>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./breadcrumb.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Breadcrumb implements AfterContentInit {\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    @Input() model: MenuItem[] | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * MenuItem configuration for the home icon.\n     * @group Props\n     */\n    @Input() home: MenuItem | undefined;\n    /**\n     * Defines a string that labels the home icon for accessibility.\n     * @group Props\n     */\n    @Input() homeAriaLabel: string | undefined;\n    /**\n     * Fired when an item is selected.\n     * @param {BreadcrumbItemClickEvent} event - custom click event.\n     * @group Emits\n     */\n    @Output() onItemClick: EventEmitter<BreadcrumbItemClickEvent> = new EventEmitter<BreadcrumbItemClickEvent>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    separatorTemplate: TemplateRef<any> | undefined;\n\n    itemTemplate: TemplateRef<any> | undefined;\n\n    constructor(private router: Router) {}\n\n    onClick(event: MouseEvent, item: MenuItem) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n\n        this.onItemClick.emit({\n            originalEvent: event,\n            item: item\n        });\n    }\n\n    onHomeClick(event: MouseEvent | any) {\n        if (this.home) {\n            this.onClick(event, this.home);\n        }\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'separator':\n                    this.separatorTemplate = item.template;\n                    break;\n\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    isCurrentUrl(item) {\n        const { routerLink } = item;\n        const lastPath = this.router ? this.router.url : '';\n\n        return routerLink === lastPath ? 'page' : undefined;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, TooltipModule, ChevronRightIcon, HomeIcon, SharedModule],\n    exports: [Breadcrumb, RouterModule, TooltipModule, SharedModule],\n    declarations: [Breadcrumb]\n})\nexport class BreadcrumbModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQA;;;AAGG;MAuIU,UAAU,CAAA;AAuCC,IAAA,MAAA,CAAA;AAtCpB;;;AAGG;AACM,IAAA,KAAK,CAAyB;AACvC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,IAAI,CAAuB;AACpC;;;AAGG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;;AAIG;AACO,IAAA,WAAW,GAA2C,IAAI,YAAY,EAA4B,CAAC;AAE7E,IAAA,SAAS,CAAuC;AAEhF,IAAA,iBAAiB,CAA+B;AAEhD,IAAA,YAAY,CAA+B;AAE3C,IAAA,WAAA,CAAoB,MAAc,EAAA;QAAd,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;KAAI;IAEtC,OAAO,CAAC,KAAiB,EAAE,IAAc,EAAA;QACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;AACV,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC/B,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC;AACT,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,IAAI,EAAE,IAAI;AACb,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AAClB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,IAAI,EAAE,IAAI;AACb,SAAA,CAAC,CAAC;KACN;AAED,IAAA,WAAW,CAAC,KAAuB,EAAA;QAC/B,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,YAAY,CAAC,IAAI,EAAA;AACb,QAAA,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;AAC5B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC;QAEpD,OAAO,UAAU,KAAK,QAAQ,GAAG,MAAM,GAAG,SAAS,CAAC;KACvD;uGA7FQ,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAV,UAAU,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,aAAA,EAAA,eAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAiCF,aAAa,EArKpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4HT,EAyGoD,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,+ZAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,gBAAgB,kFAAE,QAAQ,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAjGtE,UAAU,EAAA,UAAA,EAAA,CAAA;kBAtItB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,cAAc,EACd,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4HT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,+ZAAA,CAAA,EAAA,CAAA;2EAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAMI,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAoErB,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAhB,gBAAgB,EAAA,YAAA,EAAA,CArGhB,UAAU,CAiGT,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAE,QAAQ,EAAE,YAAY,CAjGpF,EAAA,OAAA,EAAA,CAAA,UAAU,EAkGG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;AAGtD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAJf,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAE,QAAQ,EAAE,YAAY,EACvE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGtD,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAE,QAAQ,EAAE,YAAY,CAAC;oBAC9F,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;oBAChE,YAAY,EAAE,CAAC,UAAU,CAAC;AAC7B,iBAAA,CAAA;;;ACtPD;;AAEG;;;;"}