import { Injectable } from '@angular/core';
import { WalletApi } from '../api/wallet.api';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class WalletService implements WalletServiceAbstract {
  constructor(private walletApi: WalletApi) { }

  getBalance(): Observable<any> {
    return this.walletApi.getBalance();
  }

  deposit(amount: number): Observable<any> {
    return this.walletApi.deposit(amount);
  }

  withdraw(amount: number): Observable<any> {
    return this.walletApi.withdraw(amount);
  }

  getTransactions(requestForm: any): Observable<any> {
    return this.walletApi.getWalletTransactions(requestForm);
  }

  resetBalance(): Observable<any> {
    return this.walletApi.resetBalance();
  }

  editWallet(amount: number): Observable<any> {
    return this.walletApi.editWallet(amount);
  }
}

export abstract class WalletServiceAbstract {
  abstract getBalance(): Observable<any>;
  abstract deposit(amount: number): Observable<any>;
  abstract withdraw(amount: number): Observable<any>;
  abstract getTransactions(requestForm: any): Observable<any>;
  abstract resetBalance(): Observable<any>;
  abstract editWallet(amount: number): Observable<any>;

}
