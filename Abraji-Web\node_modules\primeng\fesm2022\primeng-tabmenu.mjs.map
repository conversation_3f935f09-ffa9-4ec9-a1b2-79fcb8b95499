{"version": 3, "file": "primeng-tabmenu.mjs", "sources": ["../../src/app/components/tabmenu/tabmenu.ts", "../../src/app/components/tabmenu/primeng-tabmenu.ts"], "sourcesContent": ["import { CommonModule, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    AfterViewChecked,\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewChildren,\n    ViewEncapsulation,\n    booleanAttribute,\n    signal\n} from '@angular/core';\nimport { ActivatedRoute, NavigationEnd, Router, RouterModule } from '@angular/router';\nimport { MenuItem, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { ObjectUtils } from 'primeng/utils';\nimport { filter } from 'rxjs/operators';\n\n/**\n * TabMenu is a navigation component that displays items as tab headers.\n * @group Components\n */\n@Component({\n    selector: 'p-tabMenu',\n    template: `\n        <div [ngClass]=\"{ 'p-tabmenu p-component': true, 'p-tabmenu-scrollable': scrollable }\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-tabmenu-nav-container\">\n                <button *ngIf=\"scrollable && !backwardIsDisabled\" #prevBtn class=\"p-tabmenu-nav-prev p-tabmenu-nav-btn p-link\" (click)=\"navBackward()\" type=\"button\" role=\"navigation\" pRipple>\n                    <ChevronLeftIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                </button>\n                <div #content class=\"p-tabmenu-nav-content\" (scroll)=\"onScroll($event)\">\n                    <ul #navbar class=\"p-tabmenu-nav p-reset\" role=\"menubar\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-label]=\"ariaLabel\">\n                        <li\n                            #tab\n                            *ngFor=\"let item of focusableItems; let i = index\"\n                            role=\"presentation\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            [attr.data-p-disabled]=\"disabled(item)\"\n                            [attr.data-p-highlight]=\"focusedItemInfo() === item\"\n                            (click)=\"itemClick($event, item)\"\n                            (keydown)=\"onKeydownItem($event, i, item)\"\n                            (focus)=\"onMenuItemFocus(item)\"\n                            [ngClass]=\"{ 'p-tabmenuitem': true, 'p-disabled': getItemProp(item, 'disabled'), 'p-highlight': isActive(item), 'p-hidden': item.visible === false }\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                        >\n                            <a\n                                #tabLink\n                                *ngIf=\"!item.routerLink && !itemTemplate\"\n                                class=\"p-menuitem-link\"\n                                role=\"menuitem\"\n                                [attr.href]=\"getItemProp(item, 'url')\"\n                                [attr.id]=\"getItemProp(item, 'id')\"\n                                [attr.aria-disabled]=\"disabled(item)\"\n                                [attr.aria-label]=\"getItemProp(item, 'label')\"\n                                [attr.tabindex]=\"disabled(item) ? -1 : 0\"\n                                [target]=\"getItemProp(item, 'target')\"\n                                pRipple\n                            >\n                                <ng-container>\n                                    <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ getItemProp(item, 'badge') }}</span>\n                                </ng-container>\n                            </a>\n                            <a\n                                #tabLink\n                                *ngIf=\"item.routerLink && !itemTemplate\"\n                                [routerLink]=\"item.routerLink\"\n                                [queryParams]=\"item.queryParams\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                                role=\"menuitem\"\n                                class=\"p-menuitem-link\"\n                                [target]=\"item.target\"\n                                [attr.id]=\"getItemProp(item, 'id')\"\n                                [attr.aria-disabled]=\"disabled(item)\"\n                                [attr.aria-label]=\"getItemProp(item, 'label')\"\n                                [attr.tabindex]=\"disabled(item) ? -1 : 0\"\n                                [fragment]=\"item.fragment\"\n                                [queryParamsHandling]=\"item.queryParamsHandling\"\n                                [preserveFragment]=\"item.preserveFragment\"\n                                [skipLocationChange]=\"item.skipLocationChange\"\n                                [replaceUrl]=\"item.replaceUrl\"\n                                [state]=\"item.state\"\n                                pRipple\n                            >\n                                <ng-container>\n                                    <span class=\"p-menuitem-icon\" [attr.aria-hidden]=\"true\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                    <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{ getItemProp(item, 'label') }}</span>\n                                    <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemProp(item, 'label')\"></span></ng-template>\n                                    <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ getItemProp(item, 'badge') }}</span>\n                                </ng-container>\n                            </a>\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, index: i }\"></ng-container>\n                        </li>\n                        <li #inkbar class=\"p-tabmenu-ink-bar\" role=\"none\"></li>\n                    </ul>\n                </div>\n                <button *ngIf=\"scrollable && !forwardIsDisabled\" #nextBtn class=\"p-tabmenu-nav-next p-tabmenu-nav-btn p-link\" (click)=\"navForward()\" type=\"button\" role=\"navigation\" pRipple>\n                    <ChevronRightIcon *ngIf=\"!previousIconTemplate\" [attr.aria-hidden]=\"true\" />\n                    <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./tabmenu.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class TabMenu implements AfterContentInit, AfterViewInit, AfterViewChecked, OnDestroy {\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    @Input() set model(value: MenuItem[] | undefined) {\n        this._model = value;\n        this._focusableItems = (this._model || []).reduce((result, item) => {\n            result.push(item);\n\n            return result;\n        }, []);\n    }\n    get model(): MenuItem[] | undefined {\n        return this._model;\n    }\n    /**\n     * Defines the default active menuitem\n     * @group Props\n     */\n    @Input() set activeItem(value: MenuItem | undefined) {\n        this._activeItem = value;\n        this.activeItemChange.emit(value);\n        this.tabChanged = true;\n    }\n\n    get activeItem(): MenuItem | undefined {\n        return this._activeItem;\n    }\n    /**\n     * When enabled displays buttons at each side of the tab headers to scroll the tab list.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) scrollable: boolean | undefined;\n    /**\n     * Defines if popup mode enabled.\n     */\n    @Input({ transform: booleanAttribute }) popup: boolean | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Event fired when a tab is selected.\n     * @param {MenuItem} item - Menu item.\n     * @group Emits\n     */\n    @Output() activeItemChange: EventEmitter<MenuItem> = new EventEmitter<MenuItem>();\n\n    @ViewChild('content') content: Nullable<ElementRef>;\n\n    @ViewChild('navbar') navbar: Nullable<ElementRef>;\n\n    @ViewChild('inkbar') inkbar: Nullable<ElementRef>;\n\n    @ViewChild('prevBtn') prevBtn: Nullable<ElementRef>;\n\n    @ViewChild('nextBtn') nextBtn: Nullable<ElementRef>;\n\n    @ViewChildren('tabLink') tabLink: Nullable<QueryList<ElementRef>>;\n\n    @ViewChildren('tab') tab: Nullable<QueryList<ElementRef>>;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    itemTemplate: Nullable<TemplateRef<any>>;\n\n    previousIconTemplate: Nullable<TemplateRef<any>>;\n\n    nextIconTemplate: Nullable<TemplateRef<any>>;\n\n    tabChanged: boolean | undefined;\n\n    backwardIsDisabled: boolean = true;\n\n    forwardIsDisabled: boolean = false;\n\n    private timerIdForAutoScroll: any = null;\n\n    _focusableItems: MenuItem[] | undefined | any;\n\n    _model: MenuItem[] | undefined;\n\n    _activeItem: MenuItem | undefined;\n\n    focusedItemInfo = signal<any>(null);\n\n    get focusableItems() {\n        if (!this._focusableItems || !this._focusableItems.length) {\n            this._focusableItems = (this.model || []).reduce((result, item) => {\n                result.push(item);\n\n                return result;\n            }, []);\n        }\n        return this._focusableItems;\n    }\n\n    constructor(@Inject(PLATFORM_ID) private platformId: any, private router: Router, private route: ActivatedRoute, private cd: ChangeDetectorRef) {\n        this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {\n            this.cd.markForCheck();\n        });\n    }\n\n    ngOnChanges(simpleChange: SimpleChanges) {\n        if (simpleChange.activeItem) {\n            this.autoScrollForActiveItem();\n        }\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'nexticon':\n                    this.nextIconTemplate = item.template;\n                    break;\n\n                case 'previousicon':\n                    this.previousIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngAfterViewInit(): void {\n        if (isPlatformBrowser(this.platformId)) {\n            this.updateInkBar();\n            this.autoScrollForActiveItem();\n            this.initButtonState();\n        }\n    }\n\n    ngAfterViewChecked() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.updateInkBar();\n            this.tabChanged = false;\n        }\n    }\n\n    ngOnDestroy(): void {\n        this.clearAutoScrollHandler();\n    }\n\n    isActive(item: MenuItem) {\n        if (item.routerLink) {\n            const routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n\n            return this.router.isActive(this.router.createUrlTree(routerLink, { relativeTo: this.route }).toString(), item.routerLinkActiveOptions?.exact ?? item.routerLinkActiveOptions ?? false);\n        }\n\n        return item === this.activeItem;\n    }\n\n    getItemProp(item: any, name: string) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n\n    visible(item) {\n        return typeof item.visible === 'function' ? item.visible() : item.visible !== false;\n    }\n\n    disabled(item) {\n        return typeof item.disabled === 'function' ? item.disabled() : item.disabled;\n    }\n\n    onMenuItemFocus(item) {\n        this.focusedItemInfo.set(item);\n    }\n\n    itemClick(event: Event, item: MenuItem) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n\n        this.activeItem = item;\n        this.activeItemChange.emit(item);\n        this.tabChanged = true;\n        this.cd.markForCheck();\n    }\n\n    onKeydownItem(event, index, item) {\n        let i = index;\n\n        let foundElement = {};\n        const tabLinks = this.tabLink.toArray();\n        const tabs = this.tab.toArray();\n\n        switch (event.code) {\n            case 'ArrowRight':\n                foundElement = this.findNextItem(tabs, i);\n                i = foundElement['i'];\n                break;\n\n            case 'ArrowLeft':\n                foundElement = this.findPrevItem(tabs, i);\n                i = foundElement['i'];\n                break;\n\n            case 'End':\n                foundElement = this.findPrevItem(tabs, this.model.length);\n                i = foundElement['i'];\n\n                event.preventDefault();\n                break;\n\n            case 'Home':\n                foundElement = this.findNextItem(tabs, -1);\n                i = foundElement['i'];\n\n                event.preventDefault();\n                break;\n\n            case 'Space':\n            case 'Enter':\n                this.itemClick(event, item);\n                break;\n\n            case 'Tab':\n                this.onTabKeyDown(tabLinks);\n                break;\n\n            default:\n                break;\n        }\n\n        if (tabLinks[i] && tabLinks[index]) {\n            tabLinks[index].nativeElement.tabIndex = '-1';\n            tabLinks[i].nativeElement.tabIndex = '0';\n            tabLinks[i].nativeElement.focus();\n        }\n        this.cd.markForCheck();\n    }\n\n    onTabKeyDown(tabLinks) {\n        tabLinks.forEach((item) => {\n            item.nativeElement.tabIndex = DomHandler.getAttribute(item.nativeElement.parentElement, 'data-p-highlight') ? '0' : '-1';\n        });\n    }\n\n    findNextItem(items, index) {\n        let i = index + 1;\n\n        if (i >= items.length) {\n            return { nextItem: items[items.length], i: items.length };\n        }\n\n        let nextItem = items[i];\n\n        if (nextItem) return DomHandler.getAttribute(nextItem.nativeElement, 'data-p-disabled') ? this.findNextItem(items, i) : { nextItem: nextItem.nativeElement, i };\n        else return null;\n    }\n\n    findPrevItem(items, index) {\n        let i = index - 1;\n\n        if (i < 0) {\n            return { prevItem: items[0], i: 0 };\n        }\n\n        let prevItem = items[i];\n\n        if (prevItem) return DomHandler.getAttribute(prevItem.nativeElement, 'data-p-disabled') ? this.findPrevItem(items, i) : { prevItem: prevItem.nativeElement, i };\n        else return null;\n    }\n\n    updateInkBar() {\n        const tabHeader = DomHandler.findSingle(this.navbar?.nativeElement, 'li.p-highlight');\n        if (tabHeader) {\n            (this.inkbar as ElementRef).nativeElement.style.width = DomHandler.getWidth(tabHeader) + 'px';\n            (this.inkbar as ElementRef).nativeElement.style.left = DomHandler.getOffset(tabHeader).left - DomHandler.getOffset(this.navbar?.nativeElement).left + 'px';\n        }\n    }\n\n    getVisibleButtonWidths() {\n        return [this.prevBtn?.nativeElement, this.nextBtn?.nativeElement].reduce((acc, el) => (el ? acc + DomHandler.getWidth(el) : acc), 0);\n    }\n\n    updateButtonState() {\n        const content = this.content?.nativeElement;\n        const { scrollLeft, scrollWidth } = content;\n        const width = DomHandler.getWidth(content);\n\n        this.backwardIsDisabled = scrollLeft === 0;\n        this.forwardIsDisabled = parseInt(scrollLeft) === scrollWidth - width;\n    }\n\n    updateScrollBar(index: number): void {\n        const tabHeader = this.navbar?.nativeElement.children[index];\n\n        if (!tabHeader) {\n            return;\n        }\n\n        if (tabHeader && typeof tabHeader.scrollIntoView === 'function') {\n            tabHeader.scrollIntoView({ block: 'nearest', inline: 'center' });\n        }\n    }\n\n    onScroll(event: Event) {\n        this.scrollable && this.updateButtonState();\n\n        event.preventDefault();\n    }\n\n    navBackward() {\n        const content = this.content?.nativeElement;\n        const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n        const pos = content.scrollLeft - width;\n        content.scrollLeft = pos <= 0 ? 0 : pos;\n    }\n\n    navForward() {\n        const content = this.content?.nativeElement;\n        const width = DomHandler.getWidth(content) - this.getVisibleButtonWidths();\n        const pos = content.scrollLeft + width;\n        const lastPos = content.scrollWidth - width;\n        content.scrollLeft = pos >= lastPos ? lastPos : pos;\n    }\n\n    private autoScrollForActiveItem(): void {\n        if (!this.scrollable) {\n            return;\n        }\n\n        this.clearAutoScrollHandler();\n        // We have to wait for the rendering and then can scroll to element.\n        this.timerIdForAutoScroll = setTimeout(() => {\n            const activeItem = (this.model as MenuItem[]).findIndex((menuItem) => this.isActive(menuItem));\n\n            if (activeItem !== -1) {\n                this.updateScrollBar(activeItem);\n            }\n        });\n    }\n\n    private clearAutoScrollHandler(): void {\n        if (this.timerIdForAutoScroll) {\n            clearTimeout(this.timerIdForAutoScroll);\n            this.timerIdForAutoScroll = null;\n        }\n    }\n\n    private initButtonState(): void {\n        if (this.scrollable) {\n            // We have to wait for the rendering and then retrieve the actual size element from the DOM.\n            // in future `Promise.resolve` can be changed to `queueMicrotask` (if ie11 support will be dropped)\n            Promise.resolve().then(() => {\n                this.updateButtonState();\n                this.cd.markForCheck();\n            });\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, SharedModule, RippleModule, TooltipModule, ChevronLeftIcon, ChevronRightIcon],\n    exports: [TabMenu, RouterModule, SharedModule, TooltipModule],\n    declarations: [TabMenu]\n})\nexport class TabMenuModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAqCA;;;AAGG;MA+FU,OAAO,CAAA;AAkHyB,IAAA,UAAA,CAAA;AAAyB,IAAA,MAAA,CAAA;AAAwB,IAAA,KAAA,CAAA;AAA+B,IAAA,EAAA,CAAA;AAjHzH;;;AAGG;IACH,IAAa,KAAK,CAAC,KAA6B,EAAA;AAC5C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI,KAAI;AAC/D,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAElB,YAAA,OAAO,MAAM,CAAC;SACjB,EAAE,EAAE,CAAC,CAAC;KACV;AACD,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AACD;;;AAGG;IACH,IAAa,UAAU,CAAC,KAA2B,EAAA;AAC/C,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC1B;AAED,IAAA,IAAI,UAAU,GAAA;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;AACD;;;AAGG;AACqC,IAAA,UAAU,CAAsB;AACxE;;AAEG;AACqC,IAAA,KAAK,CAAsB;AACnE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;;AAIG;AACO,IAAA,gBAAgB,GAA2B,IAAI,YAAY,EAAY,CAAC;AAE5D,IAAA,OAAO,CAAuB;AAE/B,IAAA,MAAM,CAAuB;AAE7B,IAAA,MAAM,CAAuB;AAE5B,IAAA,OAAO,CAAuB;AAE9B,IAAA,OAAO,CAAuB;AAE3B,IAAA,OAAO,CAAkC;AAE7C,IAAA,GAAG,CAAkC;AAE1B,IAAA,SAAS,CAAuC;AAEhF,IAAA,YAAY,CAA6B;AAEzC,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,gBAAgB,CAA6B;AAE7C,IAAA,UAAU,CAAsB;IAEhC,kBAAkB,GAAY,IAAI,CAAC;IAEnC,iBAAiB,GAAY,KAAK,CAAC;IAE3B,oBAAoB,GAAQ,IAAI,CAAC;AAEzC,IAAA,eAAe,CAA+B;AAE9C,IAAA,MAAM,CAAyB;AAE/B,IAAA,WAAW,CAAuB;AAElC,IAAA,eAAe,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAEpC,IAAA,IAAI,cAAc,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AACvD,YAAA,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI,KAAI;AAC9D,gBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAElB,gBAAA,OAAO,MAAM,CAAC;aACjB,EAAE,EAAE,CAAC,CAAC;AACV,SAAA;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;AAED,IAAA,WAAA,CAAyC,UAAe,EAAU,MAAc,EAAU,KAAqB,EAAU,EAAqB,EAAA;QAArG,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QAAU,IAAK,CAAA,KAAA,GAAL,KAAK,CAAgB;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAC1I,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,YAAY,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,KAAoB,KAAI;AAC1G,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;AAED,IAAA,WAAW,CAAC,YAA2B,EAAA;QACnC,IAAI,YAAY,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAClC,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,eAAe,GAAA;AACX,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,YAAY,EAAE,CAAC;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC3B,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,sBAAsB,EAAE,CAAC;KACjC;AAED,IAAA,QAAQ,CAAC,IAAc,EAAA;QACnB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAExF,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,uBAAuB,EAAE,KAAK,IAAI,IAAI,CAAC,uBAAuB,IAAI,KAAK,CAAC,CAAC;AAC3L,SAAA;AAED,QAAA,OAAO,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC;KACnC;IAED,WAAW,CAAC,IAAS,EAAE,IAAY,EAAA;AAC/B,QAAA,OAAO,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;KAClE;AAED,IAAA,OAAO,CAAC,IAAI,EAAA;QACR,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC;KACvF;AAED,IAAA,QAAQ,CAAC,IAAI,EAAA;AACT,QAAA,OAAO,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;KAChF;AAED,IAAA,eAAe,CAAC,IAAI,EAAA;AAChB,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAClC;IAED,SAAS,CAAC,KAAY,EAAE,IAAc,EAAA;QAClC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;AACV,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC/B,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC;AACT,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,IAAI,EAAE,IAAI;AACb,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAA;QAC5B,IAAI,CAAC,GAAG,KAAK,CAAC;QAEd,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAEhC,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,YAAY;gBACb,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1C,gBAAA,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,WAAW;gBACZ,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1C,gBAAA,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC1D,gBAAA,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBAEtB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3C,gBAAA,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBAEtB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;QAED,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;YAChC,QAAQ,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,GAAG,GAAG,CAAC;YACzC,QAAQ,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACrC,SAAA;AACD,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,QAAQ,EAAA;AACjB,QAAA,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;YACtB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,kBAAkB,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;AAC7H,SAAC,CAAC,CAAC;KACN;IAED,YAAY,CAAC,KAAK,EAAE,KAAK,EAAA;AACrB,QAAA,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAElB,QAAA,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE;AACnB,YAAA,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;AAC7D,SAAA;AAED,QAAA,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAExB,QAAA,IAAI,QAAQ;AAAE,YAAA,OAAO,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC;;AAC3J,YAAA,OAAO,IAAI,CAAC;KACpB;IAED,YAAY,CAAC,KAAK,EAAE,KAAK,EAAA;AACrB,QAAA,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;QAElB,IAAI,CAAC,GAAG,CAAC,EAAE;AACP,YAAA,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACvC,SAAA;AAED,QAAA,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAExB,QAAA,IAAI,QAAQ;AAAE,YAAA,OAAO,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC;;AAC3J,YAAA,OAAO,IAAI,CAAC;KACpB;IAED,YAAY,GAAA;AACR,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACtF,QAAA,IAAI,SAAS,EAAE;AACV,YAAA,IAAI,CAAC,MAAqB,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AAC7F,YAAA,IAAI,CAAC,MAAqB,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAC9J,SAAA;KACJ;IAED,sBAAsB,GAAA;QAClB,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;KACxI;IAED,iBAAiB,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC;AAC5C,QAAA,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAC5C,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAE3C,QAAA,IAAI,CAAC,kBAAkB,GAAG,UAAU,KAAK,CAAC,CAAC;QAC3C,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,WAAW,GAAG,KAAK,CAAC;KACzE;AAED,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAE7D,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;AACV,SAAA;QAED,IAAI,SAAS,IAAI,OAAO,SAAS,CAAC,cAAc,KAAK,UAAU,EAAE;AAC7D,YAAA,SAAS,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;AACpE,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAY,EAAA;AACjB,QAAA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE5C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,WAAW,GAAA;AACP,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC;AAC5C,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC3E,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;AACvC,QAAA,OAAO,CAAC,UAAU,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KAC3C;IAED,UAAU,GAAA;AACN,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC;AAC5C,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC3E,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;AACvC,QAAA,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;AAC5C,QAAA,OAAO,CAAC,UAAU,GAAG,GAAG,IAAI,OAAO,GAAG,OAAO,GAAG,GAAG,CAAC;KACvD;IAEO,uBAAuB,GAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;AACV,SAAA;QAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;;AAE9B,QAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,MAAK;YACxC,MAAM,UAAU,GAAI,IAAI,CAAC,KAAoB,CAAC,SAAS,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE/F,YAAA,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;AACnB,gBAAA,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACpC,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAEO,sBAAsB,GAAA;QAC1B,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AACxC,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAEO,eAAe,GAAA;QACnB,IAAI,IAAI,CAAC,UAAU,EAAE;;;AAGjB,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,gBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAnYQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kBAkHI,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAlHtB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,sHAiCI,gBAAgB,CAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAIhB,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA0CnB,aAAa,EA3KpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,KAAA,EAAA,SAAA,EAAA,CAAA,KAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoFT,EA+YgF,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,88BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,iFAAE,gBAAgB,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAvYzG,OAAO,EAAA,UAAA,EAAA,CAAA;kBA9FnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,88BAAA,CAAA,EAAA,CAAA;;0BAoHY,MAAM;2BAAC,WAAW,CAAA;2HA7GlB,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAeO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAakC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAIE,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMI,gBAAgB,EAAA,CAAA;sBAAzB,MAAM;gBAEe,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS,CAAA;gBAEC,MAAM,EAAA,CAAA;sBAA1B,SAAS;uBAAC,QAAQ,CAAA;gBAEE,MAAM,EAAA,CAAA;sBAA1B,SAAS;uBAAC,QAAQ,CAAA;gBAEG,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS,CAAA;gBAEE,OAAO,EAAA,CAAA;sBAA5B,SAAS;uBAAC,SAAS,CAAA;gBAEK,OAAO,EAAA,CAAA;sBAA/B,YAAY;uBAAC,SAAS,CAAA;gBAEF,GAAG,EAAA,CAAA;sBAAvB,YAAY;uBAAC,KAAK,CAAA;gBAEa,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA4TrB,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAb,aAAa,EAAA,YAAA,EAAA,CA3Yb,OAAO,CAuYN,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB,CAvYzG,EAAA,OAAA,EAAA,CAAA,OAAO,EAwYG,YAAY,EAAE,YAAY,EAAE,aAAa,CAAA,EAAA,CAAA,CAAA;AAGnD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAJZ,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB,EAC/F,YAAY,EAAE,YAAY,EAAE,aAAa,CAAA,EAAA,CAAA,CAAA;;2FAGnD,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB,CAAC;oBACnH,OAAO,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;oBAC7D,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;ACjhBD;;AAEG;;;;"}