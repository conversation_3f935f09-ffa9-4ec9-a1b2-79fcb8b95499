/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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