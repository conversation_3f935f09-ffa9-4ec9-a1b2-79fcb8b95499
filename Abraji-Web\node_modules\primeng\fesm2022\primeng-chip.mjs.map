{"version": 3, "file": "primeng-chip.mjs", "sources": ["../../src/app/components/chip/chip.ts", "../../src/app/components/chip/primeng-chip.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, EventEmitter, Input, NgModule, Output, QueryList, TemplateRef, ViewEncapsulation, inject, booleanAttribute } from '@angular/core';\nimport { PrimeNGConfig, PrimeTemplate, SharedModule, TranslationKeys } from 'primeng/api';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\n/**\n * Chip represents people using icons, labels and images.\n * @group Components\n */\n@Component({\n    selector: 'p-chip',\n    template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"visible\" [attr.data-pc-name]=\"'chip'\" [attr.aria-label]=\"label\" [attr.data-pc-section]=\"'root'\">\n            <ng-content></ng-content>\n            <img [src]=\"image\" *ngIf=\"image; else iconTemplate\" (error)=\"imageError($event)\" [alt]=\"alt\" />\n            <ng-template #iconTemplate><span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-chip-icon'\" [attr.data-pc-section]=\"'icon'\"></span></ng-template>\n            <div class=\"p-chip-text\" *ngIf=\"label\" [attr.data-pc-section]=\"'label'\">{{ label }}</div>\n            <ng-container *ngIf=\"removable\">\n                <ng-container *ngIf=\"!removeIconTemplate\">\n                    <span\n                        tabindex=\"0\"\n                        *ngIf=\"removeIcon\"\n                        [class]=\"removeIcon\"\n                        [ngClass]=\"'pi-chip-remove-icon'\"\n                        [attr.data-pc-section]=\"'removeicon'\"\n                        (click)=\"close($event)\"\n                        (keydown)=\"onKeydown($event)\"\n                        [attr.aria-label]=\"removeAriaLabel\"\n                        role=\"button\"\n                    ></span>\n                    <TimesCircleIcon tabindex=\"0\" *ngIf=\"!removeIcon\" [class]=\"'pi-chip-remove-icon'\" [attr.data-pc-section]=\"'removeicon'\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\" />\n                </ng-container>\n                <span *ngIf=\"removeIconTemplate\" tabindex=\"0\" [attr.data-pc-section]=\"'removeicon'\" class=\"pi-chip-remove-icon\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\">\n                    <ng-template *ngTemplateOutlet=\"removeIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./chip.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Chip implements AfterContentInit {\n    /**\n     * Defines the text to display.\n     * @group Props\n     */\n    @Input() label: string | undefined;\n    /**\n     * Defines the icon to display.\n     * @group Props\n     */\n    @Input() icon: string | undefined;\n    /**\n     * Defines the image to display.\n     * @group Props\n     */\n    @Input() image: string | undefined;\n    /**\n     * Alt attribute of the image.\n     * @group Props\n     */\n    @Input() alt: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Whether to display a remove icon.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) removable: boolean | undefined = false;\n    /**\n     * Icon of the remove element.\n     * @group Props\n     */\n    @Input() removeIcon: string | undefined;\n    /**\n     * Callback to invoke when a chip is removed.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    @Output() onRemove: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();\n    /**\n     * This event is triggered if an error occurs while loading an image file.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onImageError: EventEmitter<Event> = new EventEmitter<Event>();\n\n    config = inject(PrimeNGConfig);\n\n    visible: boolean = true;\n\n    removeIconTemplate: TemplateRef<any> | undefined;\n\n    get removeAriaLabel() {\n        return this.config.getTranslation(TranslationKeys.ARIA)['removeLabel'];\n    }\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'removeicon':\n                    this.removeIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.removeIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    containerClass() {\n        return {\n            'p-chip p-component': true,\n            'p-chip-image': this.image != null\n        };\n    }\n\n    close(event: MouseEvent) {\n        this.visible = false;\n        this.onRemove.emit(event);\n    }\n\n    onKeydown(event) {\n        if (event.key === 'Enter' || event.key === 'Backspace') {\n            this.close(event);\n        }\n    }\n\n    imageError(event: Event) {\n        this.onImageError.emit(event);\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, TimesCircleIcon, SharedModule],\n    exports: [Chip, SharedModule],\n    declarations: [Chip]\n})\nexport class ChipModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;AAIA;;;AAGG;MAqCU,IAAI,CAAA;AACb;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACM,IAAA,GAAG,CAAqB;AACjC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACqC,SAAS,GAAwB,KAAK,CAAC;AAC/E;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;;AAIG;AACO,IAAA,QAAQ,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC9E;;;;AAIG;AACO,IAAA,YAAY,GAAwB,IAAI,YAAY,EAAS,CAAC;AAExE,IAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;IAE/B,OAAO,GAAY,IAAI,CAAC;AAExB,IAAA,kBAAkB,CAA+B;AAEjD,IAAA,IAAI,eAAe,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC;KAC1E;AAE+B,IAAA,SAAS,CAAuC;IAEhF,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,cAAc,GAAA;QACV,OAAO;AACH,YAAA,oBAAoB,EAAE,IAAI;AAC1B,YAAA,cAAc,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI;SACrC,CAAC;KACL;AAED,IAAA,KAAK,CAAC,KAAiB,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC7B;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;QACX,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,WAAW,EAAE;AACpD,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrB,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAY,EAAA;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACjC;uGApGQ,IAAI,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAJ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,IAAI,EAmCO,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CA6BnB,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAlGpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,0MAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAgHuB,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAxG9B,IAAI,EAAA,UAAA,EAAA,CAAA;kBApChB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,QAAQ,EACR,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,0MAAA,CAAA,EAAA,CAAA;8BAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAMI,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAYyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MA4CrB,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAV,UAAU,EAAA,YAAA,EAAA,CA5GV,IAAI,CAAA,EAAA,OAAA,EAAA,CAwGH,YAAY,EAAE,eAAe,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CAxG5C,IAAI,EAyGG,YAAY,CAAA,EAAA,CAAA,CAAA;AAGnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,YAJT,YAAY,EAAE,eAAe,EAAE,YAAY,EACrC,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGnB,UAAU,EAAA,UAAA,EAAA,CAAA;kBALtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,CAAC;AACtD,oBAAA,OAAO,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC;oBAC7B,YAAY,EAAE,CAAC,IAAI,CAAC;AACvB,iBAAA,CAAA;;;ACvJD;;AAEG;;;;"}