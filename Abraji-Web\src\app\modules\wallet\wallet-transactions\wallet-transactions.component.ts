import { Component, inject } from '@angular/core';
import { WalletTransaction, WalletTransactionsForm } from '../../../core/wallet-services/api/wallet';
import { TableResponse } from '../../../core/common-services/interfaces/table-response';
import { TableElementsService } from '../../../core/common-services/services/table-elements.service';
import { WalletService } from '../../../core/wallet-services/services/wallet.service';
import { ToastService } from '../../shared/toast/toast.service';

@Component({
  selector: 'app-wallet-transactions',
  templateUrl: './wallet-transactions.component.html',
  styleUrl: './wallet-transactions.component.scss'
})
export class WalletTransactionsComponent {
  transactionsForm!: WalletTransactionsForm;
  isLoading: boolean = false;
  tableResponse: TableResponse<WalletTransaction> = {
    current_page: 1,
    data: [],
    total: 0,
    last_page: 0,
    per_page: 0,
    to: 0,
    from: 0,
  };

  constructor (
    private walletService: WalletService,
    private toastService: ToastService,
    private tableElementsService: TableElementsService,
  ){}

  // Get pages that shown in pagination
  getPagesToDisplay(): (number | string)[] {
    return this.tableElementsService.getPagesToDisplay(this.tableResponse.last_page, this.transactionsForm.page);
  }

  // Bind the change page in pagination to the form
  changePage(page: (string | number)): void {
    const pageNumber = parseInt(page.toString(), 10);

    if (!isNaN(pageNumber)) {
      this.transactionsForm.page = pageNumber;
      this.fetchTransactions();
    }
  }

  ngOnInit(): void {
    this.transactionsForm = {
      page: 1,
      count: 10,
      sortBy: "created_at",
      direction: "desc",
      search: "",
      columns: []
    };
    this.fetchTransactions();
  }

  // fetch transactions
  fetchTransactions(){
    this.isLoading = true;
    this.walletService.getTransactions(this.transactionsForm).subscribe({
      next: (response) => {
        this.tableResponse = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastService.addToast("error", "Error fetching transactions", error.error.message);
        this.isLoading = false;
      }
    });
  }
}
