{"version": 3, "file": "primeng-togglebutton.mjs", "sources": ["../../src/app/components/togglebutton/togglebutton.ts", "../../src/app/components/togglebutton/primeng-togglebutton.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { booleanAttribute, ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChildren, EventEmitter, forwardRef, Input, NgModule, numberAttribute, Output, QueryList, TemplateRef } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { RippleModule } from 'primeng/ripple';\nimport { ToggleButtonChangeEvent } from './togglebutton.interface';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { AutoFocusModule } from 'primeng/autofocus';\n\ntype ToggleButtonIconPosition = 'left' | 'right';\n\nexport const TOGGLEBUTTON_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => ToggleButton),\n    multi: true\n};\n/**\n * ToggleButton is used to select a boolean value using a button.\n * @group Components\n */\n@Component({\n    selector: 'p-toggleButton',\n    template: `\n        <div\n            [ngClass]=\"{ 'p-togglebutton p-button p-component': true, 'p-button-icon-only': onIcon && offIcon && !hasOnLabel && !hasOffLabel, 'p-highlight': checked, 'p-disabled': disabled }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"toggle($event)\"\n            (keydown)=\"onKeyDown($event)\"\n            [attr.tabindex]=\"disabled ? null : tabindex\"\n            role=\"switch\"\n            [attr.aria-checked]=\"checked\"\n            [attr.aria-labelledby]=\"ariaLabelledBy\"\n            [attr.aria-label]=\"ariaLabel\"\n            pRipple\n            [attr.data-pc-name]=\"'togglebutton'\"\n            [attr.data-pc-section]=\"'root'\"\n            pAutoFocus\n            [autofocus]=\"autofocus\"\n        >\n            @if (!iconTemplate) {\n            <span\n                *ngIf=\"onIcon || offIcon\"\n                [class]=\"checked ? this.onIcon : this.offIcon\"\n                [ngClass]=\"{ 'p-button-icon': true, 'p-button-icon-left': iconPos === 'left', 'p-button-icon-right': iconPos === 'right' }\"\n                [attr.data-pc-section]=\"'icon'\"\n            ></span>\n            } @else {\n            <ng-container *ngTemplateOutlet=\"iconTemplate; context: { $implicit: checked }\"></ng-container>\n            }\n            <span class=\"p-button-label\" *ngIf=\"onLabel || offLabel\" [attr.data-pc-section]=\"'label'\">{{ checked ? (hasOnLabel ? onLabel : '') : hasOffLabel ? offLabel : '' }}</span>\n        </div>\n    `,\n    providers: [TOGGLEBUTTON_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    styleUrls: ['../button/button.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ToggleButton implements ControlValueAccessor {\n    /**\n     * Label for the on state.\n     * @group Props\n     */\n    @Input() onLabel: string | undefined;\n    /**\n     * Label for the off state.\n     * @group Props\n     */\n    @Input() offLabel: string | undefined;\n    /**\n     * Icon for the on state.\n     * @group Props\n     */\n    @Input() onIcon: string | undefined;\n    /**\n     * Icon for the off state.\n     * @group Props\n     */\n    @Input() offIcon: string | undefined;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: any;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined = 0;\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    @Input() iconPos: 'left' | 'right' = 'left';\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Callback to invoke on value change.\n     * @param {ToggleButtonChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<ToggleButtonChangeEvent> = new EventEmitter<ToggleButtonChangeEvent>();\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    iconTemplate: Nullable<TemplateRef<any>>;\n\n    checked: boolean = false;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    constructor(public cd: ChangeDetectorRef) {}\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n                default:\n                    this.iconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    toggle(event: Event) {\n        if (!this.disabled) {\n            this.checked = !this.checked;\n            this.onModelChange(this.checked);\n            this.onModelTouched();\n            this.onChange.emit({\n                originalEvent: event,\n                checked: this.checked\n            });\n\n            this.cd.markForCheck();\n        }\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'Enter':\n                this.toggle(event);\n                event.preventDefault();\n                break;\n            case 'Space':\n                this.toggle(event);\n                event.preventDefault();\n                break;\n        }\n    }\n\n    onBlur() {\n        this.onModelTouched();\n    }\n\n    writeValue(value: any): void {\n        this.checked = value;\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    get hasOnLabel(): boolean {\n        return (this.onLabel && this.onLabel.length > 0) as boolean;\n    }\n\n    get hasOffLabel(): boolean {\n        return (this.onLabel && this.onLabel.length > 0) as boolean;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RippleModule, SharedModule, AutoFocusModule],\n    exports: [ToggleButton, SharedModule],\n    declarations: [ToggleButton]\n})\nexport class ToggleButtonModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;AAWa,MAAA,2BAA2B,GAAQ;AAC5C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,YAAY,CAAC;AAC3C,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MAyCU,YAAY,CAAA;AAmFF,IAAA,EAAA,CAAA;AAlFnB;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;AACM,IAAA,MAAM,CAAqB;AACpC;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,KAAK,CAAM;AACpB;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACoC,QAAQ,GAAuB,CAAC,CAAC;AACxE;;;AAGG;IACM,OAAO,GAAqB,MAAM,CAAC;AAC5C;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;;AAIG;AACO,IAAA,QAAQ,GAA0C,IAAI,YAAY,EAA2B,CAAC;AAExE,IAAA,SAAS,CAA4B;AAErE,IAAA,YAAY,CAA6B;IAEzC,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,WAAA,CAAmB,EAAqB,EAAA;QAArB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;IAE5C,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,MAAM,CAAC,KAAY,EAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,gBAAA,aAAa,EAAE,KAAK;gBACpB,OAAO,EAAE,IAAI,CAAC,OAAO;AACxB,aAAA,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnB,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM;AACb,SAAA;KACJ;IAED,MAAM,GAAA;QACF,IAAI,CAAC,cAAc,EAAE,CAAC;KACzB;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,IAAI,UAAU,GAAA;AACV,QAAA,QAAQ,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAa;KAC/D;AAED,IAAA,IAAI,WAAW,GAAA;AACX,QAAA,QAAQ,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAa;KAC/D;uGAzJQ,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAmCD,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAoBhB,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,CAUf,EAAA,OAAA,EAAA,SAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,CAxEzB,EAAA,EAAA,OAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA,CAAC,2BAA2B,CAAC,EAgFvB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA/GpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,ohEAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,SAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA,CAAA;;2FAQQ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAxCxB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,gBAAgB,EAChB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BT,IAAA,CAAA,EAAA,SAAA,EACU,CAAC,2BAA2B,CAAC,mBACvB,uBAAuB,CAAC,MAAM,EAEzC,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,ohEAAA,CAAA,EAAA,CAAA;sFAOQ,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAM5B,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAwFrB,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,EAjKlB,YAAA,EAAA,CAAA,YAAY,CA6JX,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,CA7J1D,EAAA,OAAA,EAAA,CAAA,YAAY,EA8JG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAG3B,kBAAkB,EAAA,OAAA,EAAA,CAJjB,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAC3C,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG3B,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,CAAC;AACpE,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;oBACrC,YAAY,EAAE,CAAC,YAAY,CAAC;AAC/B,iBAAA,CAAA;;;AC5ND;;AAEG;;;;"}