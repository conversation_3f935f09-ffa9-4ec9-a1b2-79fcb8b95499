{"version": 3, "file": "primeng-blockui.mjs", "sources": ["../../src/app/components/blockui/blockui.ts", "../../src/app/components/blockui/primeng-blockui.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { PrimeNGConfig, PrimeTemplate } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ZIndexUtils } from 'primeng/utils';\n/**\n * BlockUI can either block other components or the whole page.\n * @group Components\n */\n@Component({\n    selector: 'p-blockUI',\n    template: `\n        <div\n            #mask\n            [class]=\"styleClass\"\n            [attr.aria-busy]=\"blocked\"\n            [ngClass]=\"{ 'p-blockui-document': !target, 'p-blockui p-component-overlay p-component-overlay-enter': true }\"\n            [ngStyle]=\"{ display: 'none' }\"\n            [attr.data-pc-name]=\"'blockui'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <ng-content></ng-content>\n            <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./blockui.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class BlockUI implements AfterViewInit, OnDestroy {\n    /**\n     * Name of the local ng-template variable referring to another component.\n     * @group Props\n     */\n    @Input() target: any;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Current blocked state as a boolean.\n     * @group Props\n     */\n    @Input() get blocked(): boolean {\n        return this._blocked;\n    }\n    set blocked(val: boolean) {\n        if (this.mask && this.mask.nativeElement) {\n            if (val) this.block();\n            else this.unblock();\n        } else {\n            this._blocked = val;\n        }\n    }\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @ViewChild('mask') mask: ElementRef | undefined;\n\n    _blocked: boolean = false;\n\n    animationEndListener: VoidFunction | null | undefined;\n\n    contentTemplate: TemplateRef<any> | undefined;\n\n    constructor(@Inject(DOCUMENT) private document: Document, public el: ElementRef, public cd: ChangeDetectorRef, public config: PrimeNGConfig, private renderer: Renderer2, @Inject(PLATFORM_ID) public platformId: any) {}\n\n    ngAfterViewInit() {\n        if (this._blocked) this.block();\n\n        if (this.target && !this.target.getBlockableElement) {\n            throw 'Target of BlockUI must implement BlockableUI interface';\n        }\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    block() {\n        if (isPlatformBrowser(this.platformId)) {\n            this._blocked = true;\n            (this.mask as ElementRef).nativeElement.style.display = 'flex';\n\n            if (this.target) {\n                this.target.getBlockableElement().appendChild((this.mask as ElementRef).nativeElement);\n                this.target.getBlockableElement().style.position = 'relative';\n            } else {\n                this.renderer.appendChild(this.document.body, (this.mask as ElementRef).nativeElement);\n                DomHandler.blockBodyScroll();\n            }\n\n            if (this.autoZIndex) {\n                ZIndexUtils.set('modal', (this.mask as ElementRef).nativeElement, this.baseZIndex + this.config.zIndex.modal);\n            }\n        }\n    }\n\n    unblock() {\n        if (isPlatformBrowser(this.platformId) && this.mask && !this.animationEndListener) {\n            this.animationEndListener = this.renderer.listen(this.mask.nativeElement, 'animationend', this.destroyModal.bind(this));\n            DomHandler.addClass(this.mask.nativeElement, 'p-component-overlay-leave');\n        }\n    }\n\n    destroyModal() {\n        this._blocked = false;\n        if (this.mask && isPlatformBrowser(this.platformId)) {\n            ZIndexUtils.clear(this.mask.nativeElement);\n            DomHandler.removeClass(this.mask.nativeElement, 'p-component-overlay-leave');\n            this.renderer.removeChild(this.el.nativeElement, this.mask.nativeElement);\n            DomHandler.unblockBodyScroll();\n        }\n        this.unbindAnimationEndListener();\n        this.cd.markForCheck();\n    }\n\n    unbindAnimationEndListener() {\n        if (this.animationEndListener && this.mask) {\n            this.animationEndListener();\n            this.animationEndListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.unblock();\n        this.destroyModal();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [BlockUI],\n    declarations: [BlockUI]\n})\nexport class BlockUIModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;AAwBA;;;AAGG;MAwBU,OAAO,CAAA;AA+CsB,IAAA,QAAA,CAAA;AAA2B,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAA8B,IAAA,MAAA,CAAA;AAA+B,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AA9CtM;;;AAGG;AACM,IAAA,MAAM,CAAM;AACrB;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACxB;IACD,IAAI,OAAO,CAAC,GAAY,EAAA;QACpB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACtC,YAAA,IAAI,GAAG;gBAAE,IAAI,CAAC,KAAK,EAAE,CAAC;;gBACjB,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACvB,SAAA;KACJ;AAE+B,IAAA,SAAS,CAAuC;AAE7D,IAAA,IAAI,CAAyB;IAEhD,QAAQ,GAAY,KAAK,CAAC;AAE1B,IAAA,oBAAoB,CAAkC;AAEtD,IAAA,eAAe,CAA+B;IAE9C,WAAsC,CAAA,QAAkB,EAAS,EAAc,EAAS,EAAqB,EAAS,MAAqB,EAAU,QAAmB,EAA8B,UAAe,EAAA;QAA/K,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAA8B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;KAAI;IAEzN,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,QAAQ;YAAE,IAAI,CAAC,KAAK,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE;AACjD,YAAA,MAAM,wDAAwD,CAAC;AAClE,SAAA;KACJ;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,KAAK,GAAA;AACD,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,IAAmB,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;YAE/D,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,gBAAA,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,WAAW,CAAE,IAAI,CAAC,IAAmB,CAAC,aAAa,CAAC,CAAC;gBACvF,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACjE,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAG,IAAI,CAAC,IAAmB,CAAC,aAAa,CAAC,CAAC;gBACvF,UAAU,CAAC,eAAe,EAAE,CAAC;AAChC,aAAA;YAED,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,WAAW,CAAC,GAAG,CAAC,OAAO,EAAG,IAAI,CAAC,IAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjH,aAAA;AACJ,SAAA;KACJ;IAED,OAAO,GAAA;AACH,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC/E,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACxH,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,2BAA2B,CAAC,CAAC;AAC7E,SAAA;KACJ;IAED,YAAY,GAAA;AACR,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,IAAI,CAAC,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACjD,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,2BAA2B,CAAC,CAAC;AAC7E,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1E,UAAU,CAAC,iBAAiB,EAAE,CAAC;AAClC,SAAA;QACD,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,IAAI,EAAE;YACxC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,YAAY,EAAE,CAAC;KACvB;uGAvHQ,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA+CI,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAsJ,WAAW,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA/CpL,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,8FAUI,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,eAAe,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAsBlB,aAAa,EA1DpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;AAaT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,wXAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,OAAO,EAAA,UAAA,EAAA,CAAA;kBAvBnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;AAaT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,wXAAA,CAAA,EAAA,CAAA;;0BAiDY,MAAM;2BAAC,QAAQ,CAAA;;0BAA+I,MAAM;2BAAC,WAAW,CAAA;yCA1CpL,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKO,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAY0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEX,IAAI,EAAA,CAAA;sBAAtB,SAAS;uBAAC,MAAM,CAAA;;MAwFR,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EA/Hb,YAAA,EAAA,CAAA,OAAO,CA2HN,EAAA,OAAA,EAAA,CAAA,YAAY,aA3Hb,OAAO,CAAA,EAAA,CAAA,CAAA;AA+HP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAJZ,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,OAAO,CAAC;oBAClB,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;ACjLD;;AAEG;;;;"}