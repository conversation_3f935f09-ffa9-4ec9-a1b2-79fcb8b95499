/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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