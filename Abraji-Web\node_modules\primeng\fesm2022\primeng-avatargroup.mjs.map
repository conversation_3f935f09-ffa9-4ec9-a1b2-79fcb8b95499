{"version": 3, "file": "primeng-avatargroup.mjs", "sources": ["../../src/app/components/avatargroup/avatargroup.ts", "../../src/app/components/avatargroup/primeng-avatargroup.ts"], "sourcesContent": ["import { NgModule, Component, ChangeDetectionStrategy, ViewEncapsulation, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n/**\n * AvatarGroup is a helper component for Avatar.\n * @group Components\n */\n@Component({\n    selector: 'p-avatarGroup',\n    template: `\n        <div [ngClass]=\"'p-avatar-group p-component'\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./avatargroup.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class AvatarGroup {\n    /**\n     * Style class of the component\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [AvatarGroup],\n    declarations: [AvatarGroup]\n})\nexport class AvatarGroupModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAEA;;;AAGG;MAeU,WAAW,CAAA;AACpB;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;uGAVnD,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAZV,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,wHAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,WAAW,EAAA,UAAA,EAAA,CAAA;kBAdvB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,eAAe,EACf,QAAA,EAAA,CAAA;;;;AAIT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,wHAAA,CAAA,EAAA,CAAA;8BAOQ,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;;MAQG,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAlBjB,YAAA,EAAA,CAAA,WAAW,CAcV,EAAA,OAAA,EAAA,CAAA,YAAY,aAdb,WAAW,CAAA,EAAA,CAAA,CAAA;AAkBX,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAJhB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAL7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,WAAW,CAAC;oBACtB,YAAY,EAAE,CAAC,WAAW,CAAC;AAC9B,iBAAA,CAAA;;;ACrCD;;AAEG;;;;"}