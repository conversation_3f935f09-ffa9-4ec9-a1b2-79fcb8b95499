<?php

namespace $NAMESPACE$;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use $MODULE_NAMESPACE$\$MODULE$\Database\Factories\$NAME$Factory;

class $CLASS$ extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = $FILLABLE$;

    protected static function newFactory(): $NAME$Factory
    {
        //return $NAME$Factory::new();
    }
}
