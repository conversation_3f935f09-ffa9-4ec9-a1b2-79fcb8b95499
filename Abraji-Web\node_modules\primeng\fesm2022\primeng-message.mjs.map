{"version": 3, "file": "primeng-message.mjs", "sources": ["../../src/app/components/message/message.ts", "../../src/app/components/message/primeng-message.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, Input, NgModule, ViewEncapsulation, booleanAttribute } from '@angular/core';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ExclamationTriangleIcon } from 'primeng/icons/exclamationtriangle';\nimport { InfoCircleIcon } from 'primeng/icons/infocircle';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\n/**\n * Message groups a collection of contents in tabs.\n * @group Components\n */\n@Component({\n    selector: 'p-message',\n    template: `\n        <div aria-live=\"polite\" class=\"p-inline-message p-component p-inline-message\" [ngStyle]=\"style\" [class]=\"styleClass\" [ngClass]=\"containerClass\">\n            <CheckIcon *ngIf=\"icon === 'success'\" [styleClass]=\"'p-inline-message-icon'\" />\n            <InfoCircleIcon *ngIf=\"icon === 'info'\" [styleClass]=\"'p-inline-message-icon'\" />\n            <TimesCircleIcon *ngIf=\"icon === 'error'\" [styleClass]=\"'p-inline-message-icon'\" />\n            <ExclamationTriangleIcon *ngIf=\"icon === 'warn'\" [styleClass]=\"'p-inline-message-icon'\" />\n            <div *ngIf=\"!escape; else escapeOut\">\n                <span *ngIf=\"!escape\" class=\"p-inline-message-text\" [innerHTML]=\"text\"></span>\n            </div>\n            <ng-template #escapeOut>\n                <span *ngIf=\"escape\" class=\"p-inline-message-text\">{{ text }}</span>\n            </ng-template>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./message.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class UIMessage {\n    /**\n     * Severity level of the message.\n     * @group Props\n     */\n    @Input() severity: 'success' | 'info' | 'warn' | 'error' | 'help' | 'primary' | 'secondary' | 'contrast' | string | null | undefined;\n    /**\n     * Text content.\n     * @group Props\n     */\n    @Input() text: string | undefined;\n    /**\n     * Whether displaying messages would be escaped or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) escape: boolean = true;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n\n    get icon() {\n        if (this.severity) {\n            return this.severity;\n        } else {\n            return 'info';\n        }\n    }\n\n    get containerClass() {\n        return {\n            [`p-inline-message-${this.severity}`]: this.severity,\n            'p-inline-message-icon-only': this.text == null\n        };\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon],\n    exports: [UIMessage],\n    declarations: [UIMessage]\n})\nexport class MessageModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;AAMA;;;AAGG;MAwBU,SAAS,CAAA;AAClB;;;AAGG;AACM,IAAA,QAAQ,CAAoH;AACrI;;;AAGG;AACM,IAAA,IAAI,CAAqB;AAClC;;;AAGG;IACqC,MAAM,GAAY,IAAI,CAAC;AAC/D;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AAExC,IAAA,IAAI,IAAI,GAAA;QACJ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,IAAI,CAAC,QAAQ,CAAC;AACxB,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;KACJ;AAED,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;YACH,CAAC,CAAA,iBAAA,EAAoB,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ;AACpD,YAAA,4BAA4B,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI;SAClD,CAAC;KACL;uGAxCQ,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAT,SAAS,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAeE,gBAAgB,CApC1B,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;AAaT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,4OAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAoDuB,SAAS,CAAE,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,cAAc,CAAE,EAAA,QAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,iFAAE,uBAAuB,CAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA5ClF,SAAS,EAAA,UAAA,EAAA,CAAA;kBAvBrB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;AAaT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,4OAAA,CAAA,EAAA,CAAA;8BAOQ,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKkC,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;;MAuBG,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAhDb,YAAA,EAAA,CAAA,SAAS,CA4CR,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,uBAAuB,aA5ClF,SAAS,CAAA,EAAA,CAAA,CAAA;wGAgDT,aAAa,EAAA,OAAA,EAAA,CAJZ,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,uBAAuB,CAAA,EAAA,CAAA,CAAA;;2FAIlF,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,uBAAuB,CAAC;oBAC5F,OAAO,EAAE,CAAC,SAAS,CAAC;oBACpB,YAAY,EAAE,CAAC,SAAS,CAAC;AAC5B,iBAAA,CAAA;;;AChFD;;AAEG;;;;"}