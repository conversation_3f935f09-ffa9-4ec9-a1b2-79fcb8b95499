{"version": 3, "file": "primeng-carousel.mjs", "sources": ["../../src/app/components/carousel/carousel.ts", "../../src/app/components/carousel/primeng-carousel.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChild,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    NgZone,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { Footer, Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { ChevronUpIcon } from 'primeng/icons/chevronup';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId } from 'primeng/utils';\nimport { CarouselPageEvent, CarouselResponsiveOptions } from './carousel.interface';\nimport { PrimeNGConfig } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\n/**\n * Carousel is a content slider featuring various customization options.\n * @group Components\n */\n@Component({\n    selector: 'p-carousel',\n    template: `\n        <div [attr.id]=\"id\" [ngClass]=\"{ 'p-carousel p-component': true, 'p-carousel-vertical': isVertical(), 'p-carousel-horizontal': !isVertical() }\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"region\">\n            <div class=\"p-carousel-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div [class]=\"contentClass\" [ngClass]=\"'p-carousel-content'\">\n                <div class=\"p-carousel-container\" [attr.aria-live]=\"allowAutoplay ? 'polite' : 'off'\">\n                    <button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-prev p-link': true, 'p-disabled': isBackwardNavDisabled() }\"\n                        [disabled]=\"isBackwardNavDisabled()\"\n                        [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                        (click)=\"navBackward($event)\"\n                        pRipple\n                    >\n                        <ng-container *ngIf=\"!previousIconTemplate\">\n                            <ChevronLeftIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            <ChevronUpIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                        </ng-container>\n                        <span *ngIf=\"previousIconTemplate\" class=\"p-carousel-prev-icon\">\n                            <ng-template *ngTemplateOutlet=\"previousIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                    <div class=\"p-carousel-items-content\" [ngStyle]=\"{ height: isVertical() ? verticalViewPortHeight : 'auto' }\" (touchend)=\"onTouchEnd($event)\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\">\n                        <div #itemsContainer class=\"p-carousel-items-container\" (transitionend)=\"onTransitionEnd()\">\n                            <div\n                                *ngFor=\"let item of clonedItemsForStarting; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-cloned': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === value.length,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForStarting.length - 1 === index\n                                }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of value; let index = index\"\n                                [ngClass]=\"{ 'p-carousel-item': true, 'p-carousel-item-active': firstIndex() <= index && lastIndex() >= index, 'p-carousel-item-start': firstIndex() === index, 'p-carousel-item-end': lastIndex() === index }\"\n                                [attr.aria-hidden]=\"!(totalShiftedItems * -1 === value.length)\"\n                                [attr.aria-label]=\"ariaSlideNumber(index)\"\n                                [attr.aria-roledescription]=\"ariaSlideLabel()\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                            <div\n                                *ngFor=\"let item of clonedItemsForFinishing; let index = index\"\n                                [ngClass]=\"{\n                                    'p-carousel-item p-carousel-item-cloned': true,\n                                    'p-carousel-item-active': totalShiftedItems * -1 === numVisible,\n                                    'p-carousel-item-start': 0 === index,\n                                    'p-carousel-item-end': clonedItemsForFinishing.length - 1 === index\n                                }\"\n                            >\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-container>\n                            </div>\n                        </div>\n                    </div>\n                    <button\n                        type=\"button\"\n                        *ngIf=\"showNavigators\"\n                        [ngClass]=\"{ 'p-carousel-next p-link': true, 'p-disabled': isForwardNavDisabled() }\"\n                        [disabled]=\"isForwardNavDisabled()\"\n                        (click)=\"navForward($event)\"\n                        pRipple\n                        [attr.aria-label]=\"ariaNextButtonLabel()\"\n                    >\n                        <ng-container *ngIf=\"!nextIconTemplate\">\n                            <ChevronRightIcon *ngIf=\"!isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                            <ChevronDownIcon *ngIf=\"isVertical()\" [styleClass]=\"'carousel-prev-icon'\" />\n                        </ng-container>\n                        <span *ngIf=\"nextIconTemplate\" class=\"p-carousel-prev-icon\">\n                            <ng-template *ngTemplateOutlet=\"nextIconTemplate\"></ng-template>\n                        </span>\n                    </button>\n                </div>\n                <ul #indicatorContent [ngClass]=\"'p-carousel-indicators p-reset'\" [class]=\"indicatorsContentClass\" [ngStyle]=\"indicatorsContentStyle\" *ngIf=\"showIndicators\" (keydown)=\"onIndicatorKeydown($event)\">\n                    <li *ngFor=\"let totalDot of totalDotsArray(); let i = index\" [ngClass]=\"{ 'p-carousel-indicator': true, 'p-highlight': _page === i }\" [attr.data-pc-section]=\"'indicator'\">\n                        <button\n                            type=\"button\"\n                            [ngClass]=\"'p-link'\"\n                            (click)=\"onDotClick($event, i)\"\n                            [class]=\"indicatorStyleClass\"\n                            [ngStyle]=\"indicatorStyle\"\n                            [attr.aria-label]=\"ariaPageLabel(i + 1)\"\n                            [attr.aria-current]=\"_page === i ? 'page' : undefined\"\n                            [tabindex]=\"_page === i ? 0 : -1\"\n                        ></button>\n                    </li>\n                </ul>\n            </div>\n            <div class=\"p-carousel-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./carousel.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Carousel implements AfterContentInit {\n    /**\n     * Index of the first item.\n     * @defaultValue 0\n     * @group Props\n     */\n    @Input() get page(): number {\n        return this._page;\n    }\n    set page(val: number) {\n        if (this.isCreated && val !== this._page) {\n            if (this.autoplayInterval) {\n                this.stopAutoplay();\n            }\n\n            if (val > this._page && val <= this.totalDots() - 1) {\n                this.step(-1, val);\n            } else if (val < this._page) {\n                this.step(1, val);\n            }\n        }\n\n        this._page = val;\n    }\n    /**\n     * Number of items per page.\n     * @defaultValue 1\n     * @group Props\n     */\n    @Input() get numVisible(): number {\n        return this._numVisible;\n    }\n    set numVisible(val: number) {\n        this._numVisible = val;\n    }\n    /**\n     * Number of items to scroll.\n     * @defaultValue 1\n     * @group Props\n     */\n    @Input() get numScroll(): number {\n        return this._numVisible;\n    }\n    set numScroll(val: number) {\n        this._numScroll = val;\n    }\n    /**\n     * An array of options for responsive design.\n     * @see {CarouselResponsiveOptions}\n     * @group Props\n     */\n    @Input() responsiveOptions: CarouselResponsiveOptions[] | undefined;\n    /**\n     * Specifies the layout of the component.\n     * @group Props\n     */\n    @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';\n    /**\n     * Height of the viewport in vertical layout.\n     * @group Props\n     */\n    @Input() verticalViewPortHeight: string = '300px';\n    /**\n     * Style class of main content.\n     * @group Props\n     */\n    @Input() contentClass: string = '';\n    /**\n     * Style class of the indicator items.\n     * @group Props\n     */\n    @Input() indicatorsContentClass: string = '';\n    /**\n     * Inline style of the indicator items.\n     * @group Props\n     */\n    @Input() indicatorsContentStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the indicators.\n     * @group Props\n     */\n    @Input() indicatorStyleClass: string = '';\n    /**\n     * Style of the indicators.\n     * @group Props\n     */\n    @Input() indicatorStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * An array of objects to display.\n     * @defaultValue null\n     * @group Props\n     */\n    @Input() get value(): any[] {\n        return this._value as any[];\n    }\n    set value(val) {\n        this._value = val;\n    }\n    /**\n     * Defines if scrolling would be infinite.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) circular: boolean = false;\n    /**\n     * Whether to display indicator container.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showIndicators: boolean = true;\n    /**\n     * Whether to display navigation buttons in container.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showNavigators: boolean = true;\n    /**\n     * Time in milliseconds to scroll items automatically.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) autoplayInterval: number = 0;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the viewport container.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Callback to invoke after scroll.\n     * @param {CarouselPageEvent} event - Custom page event.\n     * @group Emits\n     */\n    @Output() onPage: EventEmitter<CarouselPageEvent> = new EventEmitter<CarouselPageEvent>();\n\n    @ViewChild('itemsContainer') itemsContainer: ElementRef | undefined;\n\n    @ViewChild('indicatorContent') indicatorContent: ElementRef | undefined;\n\n    @ContentChild(Header) headerFacet: QueryList<Header> | undefined;\n\n    @ContentChild(Footer) footerFacet: QueryList<Footer> | undefined;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    _numVisible: number = 1;\n\n    _numScroll: number = 1;\n\n    _oldNumScroll: number = 0;\n\n    prevState: any = {\n        numScroll: 0,\n        numVisible: 0,\n        value: []\n    };\n\n    defaultNumScroll: number = 1;\n\n    defaultNumVisible: number = 1;\n\n    _page: number = 0;\n\n    _value: any[] | null | undefined;\n\n    carouselStyle: any;\n\n    id: string | undefined;\n\n    totalShiftedItems;\n\n    isRemainingItemsAdded: boolean = false;\n\n    animationTimeout: any;\n\n    translateTimeout: any;\n\n    remainingItems: number = 0;\n\n    _items: any[] | undefined;\n\n    startPos: any;\n\n    documentResizeListener: any;\n\n    clonedItemsForStarting: any[] | undefined;\n\n    clonedItemsForFinishing: any[] | undefined;\n\n    allowAutoplay: boolean | undefined;\n\n    interval: any;\n\n    isCreated: boolean | undefined;\n\n    swipeThreshold: number = 20;\n\n    itemTemplate: TemplateRef<any> | undefined;\n\n    headerTemplate: TemplateRef<any> | undefined;\n\n    footerTemplate: TemplateRef<any> | undefined;\n\n    previousIconTemplate: TemplateRef<any> | undefined;\n\n    nextIconTemplate: TemplateRef<any> | undefined;\n\n    window: Window;\n\n    constructor(public el: ElementRef, public zone: NgZone, public cd: ChangeDetectorRef, private renderer: Renderer2, @Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private config: PrimeNGConfig) {\n        this.totalShiftedItems = this.page * this.numScroll * -1;\n        this.window = this.document.defaultView as Window;\n    }\n\n    ngOnChanges(simpleChange: SimpleChanges) {\n        if (isPlatformBrowser(this.platformId)) {\n            if (simpleChange.value) {\n                if (this.circular && this._value) {\n                    this.setCloneItems();\n                }\n            }\n\n            if (this.isCreated) {\n                if (simpleChange.numVisible) {\n                    if (this.responsiveOptions) {\n                        this.defaultNumVisible = this.numVisible;\n                    }\n\n                    if (this.isCircular()) {\n                        this.setCloneItems();\n                    }\n\n                    this.createStyle();\n                    this.calculatePosition();\n                }\n\n                if (simpleChange.numScroll) {\n                    if (this.responsiveOptions) {\n                        this.defaultNumScroll = this.numScroll;\n                    }\n                }\n            }\n        }\n        this.cd.markForCheck();\n    }\n\n    ngAfterContentInit() {\n        this.id = UniqueComponentId();\n        if (isPlatformBrowser(this.platformId)) {\n            this.allowAutoplay = !!this.autoplayInterval;\n\n            if (this.circular) {\n                this.setCloneItems();\n            }\n\n            if (this.responsiveOptions) {\n                this.defaultNumScroll = this._numScroll;\n                this.defaultNumVisible = this._numVisible;\n            }\n\n            this.createStyle();\n            this.calculatePosition();\n\n            if (this.responsiveOptions) {\n                this.bindDocumentListeners();\n            }\n        }\n\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'previousicon':\n                    this.previousIconTemplate = item.template;\n                    break;\n\n                case 'nexticon':\n                    this.nextIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n        this.cd.detectChanges();\n    }\n\n    ngAfterContentChecked() {\n        if (isPlatformBrowser(this.platformId)) {\n            const isCircular = this.isCircular();\n            let totalShiftedItems = this.totalShiftedItems;\n\n            if (this.value && this.itemsContainer && (this.prevState.numScroll !== this._numScroll || this.prevState.numVisible !== this._numVisible || this.prevState.value.length !== this.value.length)) {\n                if (this.autoplayInterval) {\n                    this.stopAutoplay(false);\n                }\n\n                this.remainingItems = (this.value.length - this._numVisible) % this._numScroll;\n\n                let page = this._page;\n                if (this.totalDots() !== 0 && page >= this.totalDots()) {\n                    page = this.totalDots() - 1;\n                    this._page = page;\n                    this.onPage.emit({\n                        page: this.page\n                    });\n                }\n\n                totalShiftedItems = page * this._numScroll * -1;\n                if (isCircular) {\n                    totalShiftedItems -= this._numVisible;\n                }\n\n                if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n                    totalShiftedItems += -1 * this.remainingItems + this._numScroll;\n                    this.isRemainingItemsAdded = true;\n                } else {\n                    this.isRemainingItemsAdded = false;\n                }\n\n                if (totalShiftedItems !== this.totalShiftedItems) {\n                    this.totalShiftedItems = totalShiftedItems;\n                }\n\n                this._oldNumScroll = this._numScroll;\n                this.prevState.numScroll = this._numScroll;\n                this.prevState.numVisible = this._numVisible;\n                this.prevState.value = [...(this._value as any[])];\n\n                if (this.totalDots() > 0 && this.itemsContainer.nativeElement) {\n                    this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n                }\n\n                this.isCreated = true;\n\n                if (this.autoplayInterval && this.isAutoplay()) {\n                    this.startAutoplay();\n                }\n            }\n\n            if (isCircular) {\n                if (this.page === 0) {\n                    totalShiftedItems = -1 * this._numVisible;\n                } else if (totalShiftedItems === 0) {\n                    totalShiftedItems = -1 * this.value.length;\n                    if (this.remainingItems > 0) {\n                        this.isRemainingItemsAdded = true;\n                    }\n                }\n\n                if (totalShiftedItems !== this.totalShiftedItems) {\n                    this.totalShiftedItems = totalShiftedItems;\n                }\n            }\n        }\n    }\n\n    createStyle() {\n        if (!this.carouselStyle) {\n            this.carouselStyle = this.renderer.createElement('style');\n            this.carouselStyle.type = 'text/css';\n            DomHandler.setAttribute(this.carouselStyle, 'nonce', this.config?.csp()?.nonce);\n            this.renderer.appendChild(this.document.head, this.carouselStyle);\n        }\n\n        let innerHTML = `\n            #${this.id} .p-carousel-item {\n\t\t\t\tflex: 1 0 ${100 / this.numVisible}%\n\t\t\t}\n        `;\n\n        if (this.responsiveOptions) {\n            this.responsiveOptions.sort((data1, data2) => {\n                const value1 = data1.breakpoint;\n                const value2 = data2.breakpoint;\n                let result = null;\n\n                if (value1 == null && value2 != null) result = -1;\n                else if (value1 != null && value2 == null) result = 1;\n                else if (value1 == null && value2 == null) result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, { numeric: true });\n                else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n\n                return -1 * result;\n            });\n\n            for (let i = 0; i < this.responsiveOptions.length; i++) {\n                let res = this.responsiveOptions[i];\n\n                innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.id} .p-carousel-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n            }\n        }\n\n        this.carouselStyle.innerHTML = innerHTML;\n    }\n\n    calculatePosition() {\n        if (this.responsiveOptions) {\n            let matchedResponsiveData = {\n                numVisible: this.defaultNumVisible,\n                numScroll: this.defaultNumScroll\n            };\n\n            if (typeof window !== 'undefined') {\n                let windowWidth = window.innerWidth;\n                for (let i = 0; i < this.responsiveOptions.length; i++) {\n                    let res = this.responsiveOptions[i];\n\n                    if (parseInt(res.breakpoint, 10) >= windowWidth) {\n                        matchedResponsiveData = res;\n                    }\n                }\n            }\n\n            if (this._numScroll !== matchedResponsiveData.numScroll) {\n                let page = this._page;\n                page = Math.floor((page * this._numScroll) / matchedResponsiveData.numScroll);\n\n                let totalShiftedItems = matchedResponsiveData.numScroll * this.page * -1;\n\n                if (this.isCircular()) {\n                    totalShiftedItems -= matchedResponsiveData.numVisible;\n                }\n\n                this.totalShiftedItems = totalShiftedItems;\n                this._numScroll = matchedResponsiveData.numScroll;\n\n                this._page = page;\n                this.onPage.emit({\n                    page: this.page\n                });\n            }\n\n            if (this._numVisible !== matchedResponsiveData.numVisible) {\n                this._numVisible = matchedResponsiveData.numVisible;\n                this.setCloneItems();\n            }\n\n            this.cd.markForCheck();\n        }\n    }\n\n    setCloneItems() {\n        this.clonedItemsForStarting = [];\n        this.clonedItemsForFinishing = [];\n        if (this.isCircular()) {\n            this.clonedItemsForStarting.push(...this.value.slice(-1 * this._numVisible));\n            this.clonedItemsForFinishing.push(...this.value.slice(0, this._numVisible));\n        }\n    }\n\n    firstIndex() {\n        return this.isCircular() ? -1 * (this.totalShiftedItems + this.numVisible) : this.totalShiftedItems * -1;\n    }\n\n    lastIndex() {\n        return this.firstIndex() + this.numVisible - 1;\n    }\n\n    totalDots() {\n        return this.value?.length ? Math.ceil((this.value.length - this._numVisible) / this._numScroll) + 1 : 0;\n    }\n\n    totalDotsArray() {\n        const totalDots = this.totalDots();\n        return totalDots <= 0 ? [] : Array(totalDots).fill(0);\n    }\n\n    isVertical() {\n        return this.orientation === 'vertical';\n    }\n\n    isCircular() {\n        return this.circular && this.value && this.value.length >= this.numVisible;\n    }\n\n    isAutoplay() {\n        return this.autoplayInterval && this.allowAutoplay;\n    }\n\n    isForwardNavDisabled() {\n        return this.isEmpty() || (this._page >= this.totalDots() - 1 && !this.isCircular());\n    }\n\n    isBackwardNavDisabled() {\n        return this.isEmpty() || (this._page <= 0 && !this.isCircular());\n    }\n\n    isEmpty() {\n        return !this.value || this.value.length === 0;\n    }\n\n    navForward(e: MouseEvent | TouchEvent, index?: number) {\n        if (this.isCircular() || this._page < this.totalDots() - 1) {\n            this.step(-1, index);\n        }\n\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n\n    navBackward(e: MouseEvent | TouchEvent, index?: number) {\n        if (this.isCircular() || this._page !== 0) {\n            this.step(1, index);\n        }\n\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n\n    onDotClick(e: MouseEvent, index: number) {\n        let page = this._page;\n\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n\n        if (index > page) {\n            this.navForward(e, index);\n        } else if (index < page) {\n            this.navBackward(e, index);\n        }\n    }\n\n    onIndicatorKeydown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'ArrowRight':\n                this.onRightKey();\n                break;\n\n            case 'ArrowLeft':\n                this.onLeftKey();\n                break;\n        }\n    }\n\n    onRightKey() {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n        const activeIndex = this.findFocusedIndicatorIndex();\n\n        this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n    }\n\n    onLeftKey() {\n        const activeIndex = this.findFocusedIndicatorIndex();\n\n        this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n    }\n    onHomeKey() {\n        const activeIndex = this.findFocusedIndicatorIndex();\n\n        this.changedFocusedIndicator(activeIndex, 0);\n    }\n\n    onEndKey() {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]r')];\n        const activeIndex = this.findFocusedIndicatorIndex();\n\n        this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n    }\n\n    onTabKey() {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n        const highlightedIndex = indicators.findIndex((ind) => DomHandler.getAttribute(ind, 'data-p-highlight') === true);\n\n        const activeIndicator = DomHandler.findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n        const activeIndex = indicators.findIndex((ind) => ind === activeIndicator.parentElement);\n\n        indicators[activeIndex].children[0].tabIndex = '-1';\n        indicators[highlightedIndex].children[0].tabIndex = '0';\n    }\n\n    findFocusedIndicatorIndex() {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n        const activeIndicator = DomHandler.findSingle(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"] > button[tabindex=\"0\"]');\n\n        return indicators.findIndex((ind) => ind === activeIndicator.parentElement);\n    }\n\n    changedFocusedIndicator(prevInd, nextInd) {\n        const indicators = [...DomHandler.find(this.indicatorContent.nativeElement, '[data-pc-section=\"indicator\"]')];\n\n        indicators[prevInd].children[0].tabIndex = '-1';\n        indicators[nextInd].children[0].tabIndex = '0';\n        indicators[nextInd].children[0].focus();\n    }\n\n    step(dir: number, page?: number) {\n        let totalShiftedItems = this.totalShiftedItems;\n        const isCircular = this.isCircular();\n\n        if (page != null) {\n            totalShiftedItems = this._numScroll * page * -1;\n\n            if (isCircular) {\n                totalShiftedItems -= this._numVisible;\n            }\n\n            this.isRemainingItemsAdded = false;\n        } else {\n            totalShiftedItems += this._numScroll * dir;\n            if (this.isRemainingItemsAdded) {\n                totalShiftedItems += this.remainingItems - this._numScroll * dir;\n                this.isRemainingItemsAdded = false;\n            }\n\n            let originalShiftedItems = isCircular ? totalShiftedItems + this._numVisible : totalShiftedItems;\n            page = Math.abs(Math.floor(originalShiftedItems / this._numScroll));\n        }\n\n        if (isCircular && this.page === this.totalDots() - 1 && dir === -1) {\n            totalShiftedItems = -1 * (this.value.length + this._numVisible);\n            page = 0;\n        } else if (isCircular && this.page === 0 && dir === 1) {\n            totalShiftedItems = 0;\n            page = this.totalDots() - 1;\n        } else if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n            totalShiftedItems += this.remainingItems * -1 - this._numScroll * dir;\n            this.isRemainingItemsAdded = true;\n        }\n\n        if (this.itemsContainer) {\n            this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n            this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n        }\n\n        this.totalShiftedItems = totalShiftedItems;\n        this._page = page;\n        this.onPage.emit({\n            page: this.page\n        });\n        this.cd.markForCheck();\n    }\n\n    startAutoplay() {\n        this.interval = setInterval(() => {\n            if (this.totalDots() > 0) {\n                if (this.page === this.totalDots() - 1) {\n                    this.step(-1, 0);\n                } else {\n                    this.step(-1, this.page + 1);\n                }\n            }\n        }, this.autoplayInterval);\n        this.allowAutoplay = true;\n        this.cd.markForCheck();\n    }\n\n    stopAutoplay(changeAllow: boolean = true) {\n        if (this.interval) {\n            clearInterval(this.interval);\n            this.interval = undefined;\n            if (changeAllow) {\n                this.allowAutoplay = false;\n            }\n        }\n        this.cd.markForCheck();\n    }\n\n    isPlaying(): boolean {\n        return !!this.interval;\n    }\n\n    onTransitionEnd() {\n        if (this.itemsContainer) {\n            this.itemsContainer.nativeElement.style.transition = '';\n\n            if ((this.page === 0 || this.page === this.totalDots() - 1) && this.isCircular()) {\n                this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${this.totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${this.totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n            }\n        }\n    }\n\n    onTouchStart(e: TouchEvent) {\n        let touchobj = e.changedTouches[0];\n\n        this.startPos = {\n            x: touchobj.pageX,\n            y: touchobj.pageY\n        };\n    }\n\n    onTouchMove(e: TouchEvent | MouseEvent) {\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onTouchEnd(e: TouchEvent) {\n        let touchobj = e.changedTouches[0];\n\n        if (this.isVertical()) {\n            this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n        } else {\n            this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n        }\n    }\n\n    changePageOnTouch(e: TouchEvent | MouseEvent, diff: number) {\n        if (Math.abs(diff) > this.swipeThreshold) {\n            if (diff < 0) {\n                this.navForward(e);\n            } else {\n                this.navBackward(e);\n            }\n        }\n    }\n\n    ariaPrevButtonLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.prevPageLabel : undefined;\n    }\n\n    ariaSlideLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.slide : undefined;\n    }\n\n    ariaNextButtonLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.nextPageLabel : undefined;\n    }\n\n    ariaSlideNumber(value) {\n        return this.config.translation.aria ? this.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n    }\n\n    ariaPageLabel(value) {\n        return this.config.translation.aria ? this.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n\n    bindDocumentListeners() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.documentResizeListener) {\n                this.documentResizeListener = this.renderer.listen(this.window, 'resize', (event) => {\n                    this.calculatePosition();\n                });\n            }\n        }\n    }\n\n    unbindDocumentListeners() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.documentResizeListener) {\n                this.documentResizeListener();\n                this.documentResizeListener = null;\n            }\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.responsiveOptions) {\n            this.unbindDocumentListeners();\n        }\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, RippleModule, ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, ChevronUpIcon],\n    exports: [CommonModule, Carousel, SharedModule],\n    declarations: [Carousel]\n})\nexport class CarouselModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAmCA;;;AAGG;MAgHU,QAAQ,CAAA;AAiNE,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAAqB,IAAA,EAAA,CAAA;AAA+B,IAAA,QAAA,CAAA;AAA+C,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,MAAA,CAAA;AAhNvN;;;;AAIG;AACH,IAAA,IAAa,IAAI,GAAA;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACrB;IACD,IAAI,IAAI,CAAC,GAAW,EAAA;QAChB,IAAI,IAAI,CAAC,SAAS,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,EAAE;YACtC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,aAAA;AAED,YAAA,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;gBACjD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,aAAA;AAAM,iBAAA,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE;AACzB,gBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACrB,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;KACpB;AACD;;;;AAIG;AACH,IAAA,IAAa,UAAU,GAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,UAAU,CAAC,GAAW,EAAA;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KAC1B;AACD;;;;AAIG;AACH,IAAA,IAAa,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;IACD,IAAI,SAAS,CAAC,GAAW,EAAA;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;KACzB;AACD;;;;AAIG;AACM,IAAA,iBAAiB,CAA0C;AACpE;;;AAGG;IACM,WAAW,GAA8B,YAAY,CAAC;AAC/D;;;AAGG;IACM,sBAAsB,GAAW,OAAO,CAAC;AAClD;;;AAGG;IACM,YAAY,GAAW,EAAE,CAAC;AACnC;;;AAGG;IACM,sBAAsB,GAAW,EAAE,CAAC;AAC7C;;;AAGG;AACM,IAAA,sBAAsB,CAA8C;AAC7E;;;AAGG;IACM,mBAAmB,GAAW,EAAE,CAAC;AAC1C;;;AAGG;AACM,IAAA,cAAc,CAA8C;AACrE;;;;AAIG;AACH,IAAA,IAAa,KAAK,GAAA;QACd,OAAO,IAAI,CAAC,MAAe,CAAC;KAC/B;IACD,IAAI,KAAK,CAAC,GAAG,EAAA;AACT,QAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;KACrB;AACD;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACqC,cAAc,GAAY,IAAI,CAAC;AACvE;;;AAGG;IACqC,cAAc,GAAY,IAAI,CAAC;AACvE;;;AAGG;IACoC,gBAAgB,GAAW,CAAC,CAAC;AACpE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;;AAIG;AACO,IAAA,MAAM,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAE7D,IAAA,cAAc,CAAyB;AAErC,IAAA,gBAAgB,CAAyB;AAElD,IAAA,WAAW,CAAgC;AAE3C,IAAA,WAAW,CAAgC;AAEjC,IAAA,SAAS,CAAuC;IAEhF,WAAW,GAAW,CAAC,CAAC;IAExB,UAAU,GAAW,CAAC,CAAC;IAEvB,aAAa,GAAW,CAAC,CAAC;AAE1B,IAAA,SAAS,GAAQ;AACb,QAAA,SAAS,EAAE,CAAC;AACZ,QAAA,UAAU,EAAE,CAAC;AACb,QAAA,KAAK,EAAE,EAAE;KACZ,CAAC;IAEF,gBAAgB,GAAW,CAAC,CAAC;IAE7B,iBAAiB,GAAW,CAAC,CAAC;IAE9B,KAAK,GAAW,CAAC,CAAC;AAElB,IAAA,MAAM,CAA2B;AAEjC,IAAA,aAAa,CAAM;AAEnB,IAAA,EAAE,CAAqB;AAEvB,IAAA,iBAAiB,CAAC;IAElB,qBAAqB,GAAY,KAAK,CAAC;AAEvC,IAAA,gBAAgB,CAAM;AAEtB,IAAA,gBAAgB,CAAM;IAEtB,cAAc,GAAW,CAAC,CAAC;AAE3B,IAAA,MAAM,CAAoB;AAE1B,IAAA,QAAQ,CAAM;AAEd,IAAA,sBAAsB,CAAM;AAE5B,IAAA,sBAAsB,CAAoB;AAE1C,IAAA,uBAAuB,CAAoB;AAE3C,IAAA,aAAa,CAAsB;AAEnC,IAAA,QAAQ,CAAM;AAEd,IAAA,SAAS,CAAsB;IAE/B,cAAc,GAAW,EAAE,CAAC;AAE5B,IAAA,YAAY,CAA+B;AAE3C,IAAA,cAAc,CAA+B;AAE7C,IAAA,cAAc,CAA+B;AAE7C,IAAA,oBAAoB,CAA+B;AAEnD,IAAA,gBAAgB,CAA+B;AAE/C,IAAA,MAAM,CAAS;AAEf,IAAA,WAAA,CAAmB,EAAc,EAAS,IAAY,EAAS,EAAqB,EAAU,QAAmB,EAA4B,QAAkB,EAA+B,UAAe,EAAU,MAAqB,EAAA;QAAzN,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAA4B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;AACxO,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;KACrD;AAED,IAAA,WAAW,CAAC,YAA2B,EAAA;AACnC,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,YAAY,CAAC,KAAK,EAAE;AACpB,gBAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;oBAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,iBAAA;AACJ,aAAA;YAED,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,YAAY,CAAC,UAAU,EAAE;oBACzB,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACxB,wBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC;AAC5C,qBAAA;AAED,oBAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;wBACnB,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,qBAAA;oBAED,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC5B,iBAAA;gBAED,IAAI,YAAY,CAAC,SAAS,EAAE;oBACxB,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACxB,wBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC;AAC1C,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,EAAE,GAAG,iBAAiB,EAAE,CAAC;AAC9B,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAE7C,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,aAAA;YAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACxB,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC;AACxC,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC;AAC7C,aAAA;YAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAChC,aAAA;AACJ,SAAA;QAED,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,qBAAqB,GAAA;AACjB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACrC,YAAA,IAAI,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAE/C,YAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;gBAC5L,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACvB,oBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5B,iBAAA;AAED,gBAAA,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC;AAE/E,gBAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AACtB,gBAAA,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AACpD,oBAAA,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAC5B,oBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,IAAI,CAAC,IAAI;AAClB,qBAAA,CAAC,CAAC;AACN,iBAAA;gBAED,iBAAiB,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAChD,gBAAA,IAAI,UAAU,EAAE;AACZ,oBAAA,iBAAiB,IAAI,IAAI,CAAC,WAAW,CAAC;AACzC,iBAAA;AAED,gBAAA,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;oBAC1D,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC;AAChE,oBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACtC,iBAAA;AAED,gBAAA,IAAI,iBAAiB,KAAK,IAAI,CAAC,iBAAiB,EAAE;AAC9C,oBAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC9C,iBAAA;AAED,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC;gBACrC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;gBAC3C,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;gBAC7C,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,GAAI,IAAI,CAAC,MAAgB,CAAC,CAAC;AAEnD,gBAAA,IAAI,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;AAC3D,oBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,CAAA,eAAA,EAAkB,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAO,KAAA,CAAA,GAAG,CAAA,YAAA,EAAe,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;AACzN,iBAAA;AAED,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBAEtB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;oBAC5C,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,UAAU,EAAE;AACZ,gBAAA,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;AACjB,oBAAA,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AAC7C,iBAAA;qBAAM,IAAI,iBAAiB,KAAK,CAAC,EAAE;oBAChC,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC3C,oBAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;AACzB,wBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,iBAAiB,KAAK,IAAI,CAAC,iBAAiB,EAAE;AAC9C,oBAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC9C,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,UAAU,CAAC;AACrC,YAAA,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;AAChF,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACrE,SAAA;AAED,QAAA,IAAI,SAAS,GAAG,CAAA;AACT,aAAA,EAAA,IAAI,CAAC,EAAE,CAAA;gBACN,GAAG,GAAG,IAAI,CAAC,UAAU,CAAA;;SAE5B,CAAC;QAEF,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AACzC,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;AAChC,gBAAA,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;gBAChC,IAAI,MAAM,GAAG,IAAI,CAAC;AAElB,gBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,qBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC;AACjD,qBAAA,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI;oBAAE,MAAM,GAAG,CAAC,CAAC;qBACjD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ;AAAE,oBAAA,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;oBAClI,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAE7D,gBAAA,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC;AACvB,aAAC,CAAC,CAAC;AAEH,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpD,IAAI,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAEpC,gBAAA,SAAS,IAAI,CAAA;AACuB,kDAAA,EAAA,GAAG,CAAC,UAAU,CAAA;AACvC,yBAAA,EAAA,IAAI,CAAC,EAAE,CAAA;wCACM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAA;;;iBAG3C,CAAC;AACL,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5C;IAED,iBAAiB,GAAA;QACb,IAAI,IAAI,CAAC,iBAAiB,EAAE;AACxB,YAAA,IAAI,qBAAqB,GAAG;gBACxB,UAAU,EAAE,IAAI,CAAC,iBAAiB;gBAClC,SAAS,EAAE,IAAI,CAAC,gBAAgB;aACnC,CAAC;AAEF,YAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,gBAAA,IAAI,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;AACpC,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpD,IAAI,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;oBAEpC,IAAI,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,WAAW,EAAE;wBAC7C,qBAAqB,GAAG,GAAG,CAAC;AAC/B,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,qBAAqB,CAAC,SAAS,EAAE;AACrD,gBAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AACtB,gBAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,IAAI,qBAAqB,CAAC,SAAS,CAAC,CAAC;AAE9E,gBAAA,IAAI,iBAAiB,GAAG,qBAAqB,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAEzE,gBAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;AACnB,oBAAA,iBAAiB,IAAI,qBAAqB,CAAC,UAAU,CAAC;AACzD,iBAAA;AAED,gBAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,gBAAA,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC,SAAS,CAAC;AAElD,gBAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,IAAI;AAClB,iBAAA,CAAC,CAAC;AACN,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,qBAAqB,CAAC,UAAU,EAAE;AACvD,gBAAA,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC,UAAU,CAAC;gBACpD,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,aAAA;AAED,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,aAAa,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;AACjC,QAAA,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;AAClC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACnB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7E,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AAC/E,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;KAC5G;IAED,SAAS,GAAA;QACL,OAAO,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;KAClD;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC3G;IAED,cAAc,GAAA;AACV,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;AACnC,QAAA,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACzD;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC;KAC1C;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC;KAC9E;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,CAAC;KACtD;IAED,oBAAoB,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KACvF;IAED,qBAAqB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KACpE;IAED,OAAO,GAAA;AACH,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;KACjD;IAED,UAAU,CAAC,CAA0B,EAAE,KAAc,EAAA;AACjD,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;YACxD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACxB,SAAA;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE;YACnB,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;IAED,WAAW,CAAC,CAA0B,EAAE,KAAc,EAAA;QAClD,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;AACvC,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACvB,SAAA;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE;YACnB,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;IAED,UAAU,CAAC,CAAa,EAAE,KAAa,EAAA;AACnC,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;QAED,IAAI,KAAK,GAAG,IAAI,EAAE;AACd,YAAA,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC7B,SAAA;aAAM,IAAI,KAAK,GAAG,IAAI,EAAE;AACrB,YAAA,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9B,SAAA;KACJ;AAED,IAAA,kBAAkB,CAAC,KAAoB,EAAA;QACnC,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,YAAY;gBACb,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM;AAEV,YAAA,KAAK,WAAW;gBACZ,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,MAAM;AACb,SAAA;KACJ;IAED,UAAU,GAAA;AACN,QAAA,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,+BAA+B,CAAC,CAAC,CAAC;AAC9G,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAErD,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC,KAAK,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;KAC9H;IAED,SAAS,GAAA;AACL,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAErD,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;KACzF;IACD,SAAS,GAAA;AACL,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAErD,QAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KAChD;IAED,QAAQ,GAAA;AACJ,QAAA,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,gCAAgC,CAAC,CAAC,CAAC;AAC/G,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAErD,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACpE;IAED,QAAQ,GAAA;AACJ,QAAA,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,+BAA+B,CAAC,CAAC,CAAC;QAC9G,MAAM,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,UAAU,CAAC,YAAY,CAAC,GAAG,EAAE,kBAAkB,CAAC,KAAK,IAAI,CAAC,CAAC;AAElH,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,sDAAsD,CAAC,CAAC;AAC3I,QAAA,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,eAAe,CAAC,aAAa,CAAC,CAAC;AAEzF,QAAA,UAAU,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AACpD,QAAA,UAAU,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;KAC3D;IAED,yBAAyB,GAAA;AACrB,QAAA,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,+BAA+B,CAAC,CAAC,CAAC;AAC9G,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,sDAAsD,CAAC,CAAC;AAE3I,QAAA,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,eAAe,CAAC,aAAa,CAAC,CAAC;KAC/E;IAED,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAA;AACpC,QAAA,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,+BAA+B,CAAC,CAAC,CAAC;AAE9G,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AAChD,QAAA,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;QAC/C,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;KAC3C;IAED,IAAI,CAAC,GAAW,EAAE,IAAa,EAAA;AAC3B,QAAA,IAAI,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC/C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAErC,IAAI,IAAI,IAAI,IAAI,EAAE;YACd,iBAAiB,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;AAEhD,YAAA,IAAI,UAAU,EAAE;AACZ,gBAAA,iBAAiB,IAAI,IAAI,CAAC,WAAW,CAAC;AACzC,aAAA;AAED,YAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACtC,SAAA;AAAM,aAAA;AACH,YAAA,iBAAiB,IAAI,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;YAC3C,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,iBAAiB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;AACjE,gBAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACtC,aAAA;AAED,YAAA,IAAI,oBAAoB,GAAG,UAAU,GAAG,iBAAiB,GAAG,IAAI,CAAC,WAAW,GAAG,iBAAiB,CAAC;AACjG,YAAA,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;AACvE,SAAA;AAED,QAAA,IAAI,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;AAChE,YAAA,iBAAiB,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YAChE,IAAI,GAAG,CAAC,CAAC;AACZ,SAAA;aAAM,IAAI,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;YACnD,iBAAiB,GAAG,CAAC,CAAC;AACtB,YAAA,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAC/B,SAAA;AAAM,aAAA,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;AACjE,YAAA,iBAAiB,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;AACtE,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,CAAA,eAAA,EAAkB,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAO,KAAA,CAAA,GAAG,CAAA,YAAA,EAAe,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;YACtN,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,yBAAyB,CAAC;AAClF,SAAA;AAED,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,IAAI,EAAE,IAAI,CAAC,IAAI;AAClB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,aAAa,GAAA;AACT,QAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,MAAK;AAC7B,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;gBACtB,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;oBACpC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAChC,iBAAA;AACJ,aAAA;AACL,SAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,YAAY,CAAC,cAAuB,IAAI,EAAA;QACpC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC7B,YAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;AAC1B,YAAA,IAAI,WAAW,EAAE;AACb,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC9B,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,SAAS,GAAA;AACL,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC1B;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;YAExD,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE,EAAE;gBAC9E,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,kBAAkB,IAAI,CAAC,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,CAAe,YAAA,EAAA,IAAI,CAAC,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAA,QAAA,CAAU,CAAC;AACnO,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,CAAa,EAAA;QACtB,IAAI,QAAQ,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAEnC,IAAI,CAAC,QAAQ,GAAG;YACZ,CAAC,EAAE,QAAQ,CAAC,KAAK;YACjB,CAAC,EAAE,QAAQ,CAAC,KAAK;SACpB,CAAC;KACL;AAED,IAAA,WAAW,CAAC,CAA0B,EAAA;QAClC,IAAI,CAAC,CAAC,UAAU,EAAE;YACd,CAAC,CAAC,cAAc,EAAE,CAAC;AACtB,SAAA;KACJ;AACD,IAAA,UAAU,CAAC,CAAa,EAAA;QACpB,IAAI,QAAQ,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAEnC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;AACnB,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/D,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/D,SAAA;KACJ;IAED,iBAAiB,CAAC,CAA0B,EAAE,IAAY,EAAA;QACtD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE;YACtC,IAAI,IAAI,GAAG,CAAC,EAAE;AACV,gBAAA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACtB,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACvB,aAAA;AACJ,SAAA;KACJ;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;KAChG;IAED,cAAc,GAAA;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;KACxF;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;KAChG;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;AACjB,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC;KAC/H;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;AACf,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC;KACtH;IAED,qBAAqB,GAAA;AACjB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAC9B,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,KAAK,KAAI;oBAChF,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,uBAAuB,GAAA;AACnB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,sBAAsB,EAAE;gBAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAClC,SAAA;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;KACJ;uGA5wBQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAiN0G,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAjN3K,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,EAsGG,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKhB,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAgB,wDAKhB,gBAAgB,CAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAKhB,eAAe,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAsBrB,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAEN,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAEH,aAAa,EA5PpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,y5BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAwxBmD,gBAAgB,CAAE,EAAA,QAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,CAAE,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,eAAe,iFAAE,aAAa,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAhxB5G,QAAQ,EAAA,UAAA,EAAA,CAAA;kBA/GpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,y5BAAA,CAAA,EAAA,CAAA;;0BAmNmH,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;qEA3MvK,IAAI,EAAA,CAAA;sBAAhB,KAAK;gBAuBO,UAAU,EAAA,CAAA;sBAAtB,KAAK;gBAWO,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAWG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,sBAAsB,EAAA,CAAA;sBAA9B,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,sBAAsB,EAAA,CAAA;sBAA9B,KAAK;gBAKG,sBAAsB,EAAA,CAAA;sBAA9B,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMO,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAUkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,gBAAgB,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAMI,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEsB,cAAc,EAAA,CAAA;sBAA1C,SAAS;uBAAC,gBAAgB,CAAA;gBAEI,gBAAgB,EAAA,CAAA;sBAA9C,SAAS;uBAAC,kBAAkB,CAAA;gBAEP,WAAW,EAAA,CAAA;sBAAhC,YAAY;uBAAC,MAAM,CAAA;gBAEE,WAAW,EAAA,CAAA;sBAAhC,YAAY;uBAAC,MAAM,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAqoBrB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAd,cAAc,EAAA,YAAA,EAAA,CApxBd,QAAQ,CAgxBP,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,aAC3G,YAAY,EAjxBb,QAAQ,EAixBiB,YAAY,CAAA,EAAA,CAAA,CAAA;AAGrC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAJb,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,EAC3G,YAAY,EAAY,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGrC,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,CAAC;AACtH,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC;oBAC/C,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;ACz6BD;;AAEG;;;;"}