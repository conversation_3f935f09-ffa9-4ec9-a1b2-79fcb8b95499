{"version": 3, "file": "primeng-dragdrop.mjs", "sources": ["../../src/app/components/dragdrop/dragdrop.ts", "../../src/app/components/dragdrop/primeng-dragdrop.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterViewInit, Directive, ElementRef, EventEmitter, HostListener, Input, NgModule, NgZone, OnDestroy, Output, Renderer2, booleanAttribute } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { VoidListener } from 'primeng/ts-helpers';\n/**\n * pDraggable directive apply draggable behavior to any element.\n * @group Components\n */\n@Directive({\n    selector: '[pDraggable]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Draggable implements AfterViewInit, OnDestroy {\n    @Input('pDraggable') scope: string | undefined;\n    /**\n     * Defines the cursor style.\n     * @group Props\n     */\n    @Input() dragEffect: 'none' | 'copy' | 'copyLink' | 'copyMove' | 'link' | 'linkMove' | 'move' | 'all' | 'uninitialized' | undefined;\n    /**\n     * Selector to define the drag handle, by default anywhere on the target element is a drag handle to start dragging.\n     * @group Props\n     */\n    @Input() dragHandle: string | undefined;\n    /**\n     * Callback to invoke when drag begins.\n     * @param {DragEvent} event - Drag event.\n     * @group Emits\n     */\n    @Output() onDragStart: EventEmitter<DragEvent> = new EventEmitter();\n    /**\n     * Callback to invoke when drag ends.\n     * @param {DragEvent} event - Drag event.\n     * @group Emits\n     */\n    @Output() onDragEnd: EventEmitter<DragEvent> = new EventEmitter();\n    /**\n     * Callback to invoke on dragging.\n     * @param {DragEvent} event - Drag event.\n     * @group Emits\n     */\n    @Output() onDrag: EventEmitter<DragEvent> = new EventEmitter();\n\n    handle: any;\n\n    dragListener: VoidListener;\n\n    mouseDownListener: VoidListener;\n\n    mouseUpListener: VoidListener;\n\n    _pDraggableDisabled: boolean = false;\n\n    constructor(public el: ElementRef, public zone: NgZone, private renderer: Renderer2) {}\n\n    @Input() get pDraggableDisabled(): boolean {\n        return this._pDraggableDisabled;\n    }\n    set pDraggableDisabled(_pDraggableDisabled: boolean) {\n        this._pDraggableDisabled = _pDraggableDisabled;\n\n        if (this._pDraggableDisabled) {\n            this.unbindMouseListeners();\n        } else {\n            this.el.nativeElement.draggable = true;\n            this.bindMouseListeners();\n        }\n    }\n\n    ngAfterViewInit() {\n        if (!this.pDraggableDisabled) {\n            this.el.nativeElement.draggable = true;\n            this.bindMouseListeners();\n        }\n    }\n\n    bindDragListener() {\n        if (!this.dragListener) {\n            this.zone.runOutsideAngular(() => {\n                this.dragListener = this.renderer.listen(this.el.nativeElement, 'drag', this.drag.bind(this));\n            });\n        }\n    }\n\n    unbindDragListener() {\n        if (this.dragListener) {\n            this.zone.runOutsideAngular(() => {\n                this.dragListener && this.dragListener();\n                this.dragListener = null;\n            });\n        }\n    }\n\n    bindMouseListeners() {\n        if (!this.mouseDownListener && !this.mouseUpListener) {\n            this.zone.runOutsideAngular(() => {\n                this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.mousedown.bind(this));\n                this.mouseUpListener = this.renderer.listen(this.el.nativeElement, 'mouseup', this.mouseup.bind(this));\n            });\n        }\n    }\n\n    unbindMouseListeners() {\n        if (this.mouseDownListener && this.mouseUpListener) {\n            this.zone.runOutsideAngular(() => {\n                this.mouseDownListener && this.mouseDownListener();\n                this.mouseUpListener && this.mouseUpListener();\n                this.mouseDownListener = null;\n                this.mouseUpListener = null;\n            });\n        }\n    }\n\n    drag(event: DragEvent) {\n        this.onDrag.emit(event);\n    }\n\n    @HostListener('dragstart', ['$event'])\n    dragStart(event: DragEvent) {\n        if (this.allowDrag() && !this.pDraggableDisabled) {\n            if (this.dragEffect) {\n                (event.dataTransfer as DataTransfer).effectAllowed = this.dragEffect;\n            }\n            (event.dataTransfer as DataTransfer).setData('text', this.scope!);\n\n            this.onDragStart.emit(event);\n\n            this.bindDragListener();\n        } else {\n            event.preventDefault();\n        }\n    }\n\n    @HostListener('dragend', ['$event'])\n    dragEnd(event: DragEvent) {\n        this.onDragEnd.emit(event);\n        this.unbindDragListener();\n    }\n\n    mousedown(event: MouseEvent) {\n        this.handle = event.target;\n    }\n\n    mouseup(event: MouseEvent) {\n        this.handle = null;\n    }\n\n    allowDrag(): boolean {\n        if (this.dragHandle && this.handle) return DomHandler.matches(this.handle, this.dragHandle);\n        else return true;\n    }\n\n    ngOnDestroy() {\n        this.unbindDragListener();\n        this.unbindMouseListeners();\n    }\n}\n/**\n * pDroppable directive apply droppable behavior to any element.\n * @group Components\n */\n@Directive({\n    selector: '[pDroppable]',\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Droppable implements AfterViewInit, OnDestroy {\n    @Input('pDroppable') scope: string | string[] | undefined;\n    /**\n     * Whether the element is droppable, useful for conditional cases.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) pDroppableDisabled: boolean = false;\n    /**\n     * Defines the cursor style, valid values are none, copy, move, link, copyMove, copyLink, linkMove and all.\n     * @group Props\n     */\n    @Input() dropEffect: 'none' | 'copy' | 'link' | 'move' | undefined;\n    /**\n     * Callback to invoke when a draggable enters drop area.\n     * @group Emits\n     */\n    @Output() onDragEnter: EventEmitter<DragEvent> = new EventEmitter();\n    /**\n     * Callback to invoke when a draggable leave drop area.\n     * @group Emits\n     */\n    @Output() onDragLeave: EventEmitter<DragEvent> = new EventEmitter();\n    /**\n     * Callback to invoke when a draggable is dropped onto drop area.\n     * @group Emits\n     */\n    @Output() onDrop: EventEmitter<DragEvent> = new EventEmitter();\n\n    constructor(public el: ElementRef, public zone: NgZone, private renderer: Renderer2) {}\n\n    dragOverListener: VoidListener;\n\n    ngAfterViewInit() {\n        if (!this.pDroppableDisabled) {\n            this.bindDragOverListener();\n        }\n    }\n\n    bindDragOverListener() {\n        if (!this.dragOverListener) {\n            this.zone.runOutsideAngular(() => {\n                this.dragOverListener = this.renderer.listen(this.el.nativeElement, 'dragover', this.dragOver.bind(this));\n            });\n        }\n    }\n\n    unbindDragOverListener() {\n        if (this.dragOverListener) {\n            this.zone.runOutsideAngular(() => {\n                this.dragOverListener && this.dragOverListener();\n                this.dragOverListener = null;\n            });\n        }\n    }\n\n    dragOver(event: DragEvent) {\n        event.preventDefault();\n    }\n\n    @HostListener('drop', ['$event'])\n    drop(event: DragEvent) {\n        if (this.allowDrop(event)) {\n            DomHandler.removeClass(this.el.nativeElement, 'p-draggable-enter');\n            event.preventDefault();\n            this.onDrop.emit(event);\n        }\n    }\n\n    @HostListener('dragenter', ['$event'])\n    dragEnter(event: DragEvent) {\n        event.preventDefault();\n\n        if (this.dropEffect) {\n            (event.dataTransfer as DataTransfer).dropEffect = this.dropEffect;\n        }\n\n        DomHandler.addClass(this.el.nativeElement, 'p-draggable-enter');\n        this.onDragEnter.emit(event);\n    }\n\n    @HostListener('dragleave', ['$event'])\n    dragLeave(event: DragEvent) {\n        event.preventDefault();\n\n        if (!this.el.nativeElement.contains(event.relatedTarget)) {\n            DomHandler.removeClass(this.el.nativeElement, 'p-draggable-enter');\n            this.onDragLeave.emit(event);\n        }\n    }\n\n    allowDrop(event: DragEvent): boolean {\n        let dragScope = (event.dataTransfer as DataTransfer).getData('text');\n        if (typeof this.scope == 'string' && dragScope == this.scope) {\n            return true;\n        } else if (Array.isArray(this.scope)) {\n            for (let j = 0; j < this.scope.length; j++) {\n                if (dragScope == this.scope[j]) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n\n    ngOnDestroy() {\n        this.unbindDragOverListener();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Draggable, Droppable],\n    declarations: [Draggable, Droppable]\n})\nexport class DragDropModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAIA;;;AAGG;MAOU,SAAS,CAAA;AAyCC,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAAsB,IAAA,QAAA,CAAA;AAxC3C,IAAA,KAAK,CAAqB;AAC/C;;;AAGG;AACM,IAAA,UAAU,CAAiH;AACpI;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;;AAIG;AACO,IAAA,WAAW,GAA4B,IAAI,YAAY,EAAE,CAAC;AACpE;;;;AAIG;AACO,IAAA,SAAS,GAA4B,IAAI,YAAY,EAAE,CAAC;AAClE;;;;AAIG;AACO,IAAA,MAAM,GAA4B,IAAI,YAAY,EAAE,CAAC;AAE/D,IAAA,MAAM,CAAM;AAEZ,IAAA,YAAY,CAAe;AAE3B,IAAA,iBAAiB,CAAe;AAEhC,IAAA,eAAe,CAAe;IAE9B,mBAAmB,GAAY,KAAK,CAAC;AAErC,IAAA,WAAA,CAAmB,EAAc,EAAS,IAAY,EAAU,QAAmB,EAAA;QAAhE,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAAI;AAEvF,IAAA,IAAa,kBAAkB,GAAA;QAC3B,OAAO,IAAI,CAAC,mBAAmB,CAAC;KACnC;IACD,IAAI,kBAAkB,CAAC,mBAA4B,EAAA;AAC/C,QAAA,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAE/C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC;YACvC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,SAAA;KACJ;IAED,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC;YACvC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,SAAA;KACJ;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACpB,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAClG,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;AAC7B,gBAAA,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACzC,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AAClD,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC7G,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3G,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,eAAe,EAAE;AAChD,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;AAC7B,gBAAA,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACnD,gBAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/C,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC9B,gBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,IAAI,CAAC,KAAgB,EAAA;AACjB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAGD,IAAA,SAAS,CAAC,KAAgB,EAAA;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC9C,IAAI,IAAI,CAAC,UAAU,EAAE;gBAChB,KAAK,CAAC,YAA6B,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC;AACxE,aAAA;YACA,KAAK,CAAC,YAA6B,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,KAAM,CAAC,CAAC;AAElE,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC3B,SAAA;AAAM,aAAA;YACH,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAGD,IAAA,OAAO,CAAC,KAAgB,EAAA;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;KAC7B;AAED,IAAA,SAAS,CAAC,KAAiB,EAAA;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;KAC9B;AAED,IAAA,OAAO,CAAC,KAAiB,EAAA;AACrB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;KACtB;IAED,SAAS,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM;AAAE,YAAA,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;;AACvF,YAAA,OAAO,IAAI,CAAC;KACpB;IAED,WAAW,GAAA;QACP,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;uGA/IQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAT,SAAS,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,YAAA,EAAA,OAAA,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBANrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;4HAEwB,KAAK,EAAA,CAAA;sBAAzB,KAAK;uBAAC,YAAY,CAAA;gBAKV,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAMI,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAMG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAcM,kBAAkB,EAAA,CAAA;sBAA9B,KAAK;gBA+DN,SAAS,EAAA,CAAA;sBADR,YAAY;uBAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAA;gBAiBrC,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAA;;AAwBvC;;;AAGG;MAOU,SAAS,CAAA;AA4BC,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAAsB,IAAA,QAAA,CAAA;AA3B3C,IAAA,KAAK,CAAgC;AAC1D;;;AAGG;IACqC,kBAAkB,GAAY,KAAK,CAAC;AAC5E;;;AAGG;AACM,IAAA,UAAU,CAAgD;AACnE;;;AAGG;AACO,IAAA,WAAW,GAA4B,IAAI,YAAY,EAAE,CAAC;AACpE;;;AAGG;AACO,IAAA,WAAW,GAA4B,IAAI,YAAY,EAAE,CAAC;AACpE;;;AAGG;AACO,IAAA,MAAM,GAA4B,IAAI,YAAY,EAAE,CAAC;AAE/D,IAAA,WAAA,CAAmB,EAAc,EAAS,IAAY,EAAU,QAAmB,EAAA;QAAhE,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAAI;AAEvF,IAAA,gBAAgB,CAAe;IAE/B,eAAe,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AACxB,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9G,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,sBAAsB,GAAA;QAClB,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACvB,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;AAC7B,gBAAA,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACjD,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAgB,EAAA;QACrB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAGD,IAAA,IAAI,CAAC,KAAgB,EAAA;AACjB,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YACvB,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;YACnE,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;KACJ;AAGD,IAAA,SAAS,CAAC,KAAgB,EAAA;QACtB,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,UAAU,EAAE;YAChB,KAAK,CAAC,YAA6B,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACrE,SAAA;QAED,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;AAChE,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAChC;AAGD,IAAA,SAAS,CAAC,KAAgB,EAAA;QACtB,KAAK,CAAC,cAAc,EAAE,CAAC;AAEvB,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;YACtD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;AACnE,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAgB,EAAA;QACtB,IAAI,SAAS,GAAI,KAAK,CAAC,YAA6B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACrE,QAAA,IAAI,OAAO,IAAI,CAAC,KAAK,IAAI,QAAQ,IAAI,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;AAC1D,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAClC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,IAAI,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AAC5B,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;AACJ,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;IAED,WAAW,GAAA;QACP,IAAI,CAAC,sBAAsB,EAAE,CAAC;KACjC;uGA1GQ,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,uIAME,gBAAgB,CAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,MAAA,EAAA,cAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAN3B,SAAS,EAAA,UAAA,EAAA,CAAA;kBANrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;4HAEwB,KAAK,EAAA,CAAA;sBAAzB,KAAK;uBAAC,YAAY,CAAA;gBAKqB,kBAAkB,EAAA,CAAA;sBAAzD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKI,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAKG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAkCP,IAAI,EAAA,CAAA;sBADH,YAAY;uBAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAA;gBAUhC,SAAS,EAAA,CAAA;sBADR,YAAY;uBAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAA;gBAarC,SAAS,EAAA,CAAA;sBADR,YAAY;uBAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAkC5B,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAd,cAAc,EAAA,YAAA,EAAA,CA7Qd,SAAS,EA2JT,SAAS,aA8GR,YAAY,CAAA,EAAA,OAAA,EAAA,CAzQb,SAAS,EA2JT,SAAS,CAAA,EAAA,CAAA,CAAA;AAkHT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAJb,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AAC/B,oBAAA,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACvC,iBAAA,CAAA;;;AC1RD;;AAEG;;;;"}