{"version": 3, "file": "primeng-overlaypanel.mjs", "sources": ["../../src/app/components/overlaypanel/overlaypanel.ts", "../../src/app/components/overlaypanel/primeng-overlaypanel.ts"], "sourcesContent": ["import { animate, AnimationEvent, state, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    booleanAttribute,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    HostListener,\n    Inject,\n    Input,\n    NgModule,\n    NgZone,\n    numberAttribute,\n    OnDestroy,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewEncapsulation,\n    ViewRef\n} from '@angular/core';\nimport { OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ConnectedOverlayScrollHandler, DomHandler } from 'primeng/dom';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { RippleModule } from 'primeng/ripple';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { Subscription } from 'rxjs';\n/**\n * OverlayPanel is a container component positioned as connected to its target.\n * @group Components\n */\n@Component({\n    selector: 'p-overlayPanel',\n    template: `\n        <div\n            *ngIf=\"render\"\n            [ngClass]=\"'p-overlaypanel p-component'\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{ value: overlayVisible ? 'open' : 'close', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            (@animation.start)=\"onAnimationStart($event)\"\n            (@animation.done)=\"onAnimationEnd($event)\"\n            role=\"dialog\"\n            [attr.aria-modal]=\"overlayVisible\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n        >\n            <div class=\"p-overlaypanel-content\" (click)=\"onContentClick($event)\" (mousedown)=\"onContentClick($event)\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n            <button *ngIf=\"showCloseIcon\" type=\"button\" class=\"p-overlaypanel-close p-link\" (click)=\"onCloseClick($event)\" (keydown.enter)=\"hide()\" [attr.aria-label]=\"ariaCloseLabel\" pRipple>\n                <TimesIcon *ngIf=\"!closeIconTemplate\" [styleClass]=\"'p-overlaypanel-close-icon'\" />\n                <span class=\"p-overlaypanel-close-icon\" *ngIf=\"closeIconTemplate\">\n                    <ng-template *ngTemplateOutlet=\"closeIconTemplate\"></ng-template>\n                </span>\n            </button>\n        </div>\n    `,\n    animations: [\n        trigger('animation', [\n            state(\n                'void',\n                style({\n                    transform: 'scaleY(0.8)',\n                    opacity: 0\n                })\n            ),\n            state(\n                'close',\n                style({\n                    opacity: 0\n                })\n            ),\n            state(\n                'open',\n                style({\n                    transform: 'translateY(0)',\n                    opacity: 1\n                })\n            ),\n            transition('void => open', animate('{{showTransitionParams}}')),\n            transition('open => close', animate('{{hideTransitionParams}}'))\n        ])\n    ],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./overlaypanel.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class OverlayPanel implements AfterContentInit, OnDestroy {\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Enables to hide the overlay when outside is clicked.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) dismissable: boolean = true;\n    /**\n     * When enabled, displays a close icon at top right corner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showCloseIcon: boolean | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     *  Target element to attach the panel, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any = 'body';\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Aria label of the close icon.\n     * @group Props\n     */\n    @Input() ariaCloseLabel: string | undefined;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * When enabled, first button receives focus on show.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) focusOnShow: boolean = true;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.1s linear';\n    /**\n     * Callback to invoke when an overlay becomes visible.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<any> = new EventEmitter();\n    /**\n     * Callback to invoke when an overlay gets hidden.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<any> = new EventEmitter<any>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    container: Nullable<HTMLDivElement>;\n\n    overlayVisible: boolean = false;\n\n    render: boolean = false;\n\n    isOverlayAnimationInProgress: boolean = false;\n\n    selfClick: boolean = false;\n\n    documentClickListener: VoidListener;\n\n    target: any;\n\n    willHide: Nullable<boolean>;\n\n    scrollHandler: Nullable<ConnectedOverlayScrollHandler>;\n\n    documentResizeListener: VoidListener;\n\n    contentTemplate: Nullable<TemplateRef<any>>;\n\n    closeIconTemplate: Nullable<TemplateRef<any>>;\n\n    destroyCallback: Nullable<Function>;\n\n    overlayEventListener: Nullable<(event?: any) => void>;\n\n    overlaySubscription: Subscription | undefined;\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        public el: ElementRef,\n        public renderer: Renderer2,\n        public cd: ChangeDetectorRef,\n        private zone: NgZone,\n        public config: PrimeNGConfig,\n        public overlayService: OverlayService\n    ) {}\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n\n            this.cd.markForCheck();\n        });\n    }\n\n    bindDocumentClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.documentClickListener && this.dismissable) {\n                let documentEvent = DomHandler.isIOS() ? 'touchstart' : 'click';\n                const documentTarget: any = this.el ? this.el.nativeElement.ownerDocument : this.document;\n\n                this.documentClickListener = this.renderer.listen(documentTarget, documentEvent, (event) => {\n                    if (!this.container?.contains(event.target) && this.target !== event.target && !this.target.contains(event.target) && !this.selfClick) {\n                        this.hide();\n                    }\n\n                    this.selfClick = false;\n                    this.cd.markForCheck();\n                });\n            }\n        }\n    }\n\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n            this.selfClick = false;\n        }\n    }\n\n    /**\n     * Toggles the visibility of the panel.\n     * @param {Event} event - Browser event\n     * @param {Target} target - Target element.\n     * @group Method\n     */\n    toggle(event: any, target?: any) {\n        if (this.isOverlayAnimationInProgress) {\n            return;\n        }\n\n        if (this.overlayVisible) {\n            if (this.hasTargetChanged(event, target)) {\n                this.destroyCallback = () => {\n                    this.show(null, target || event.currentTarget || event.target);\n                };\n            }\n\n            this.hide();\n        } else {\n            this.show(event, target);\n        }\n    }\n    /**\n     * Displays the panel.\n     * @param {Event} event - Browser event\n     * @param {Target} target - Target element.\n     * @group Method\n     */\n    show(event: any, target?: any) {\n        target && event && event.stopPropagation();\n        if (this.isOverlayAnimationInProgress) {\n            return;\n        }\n\n        this.target = target || event.currentTarget || event.target;\n        this.overlayVisible = true;\n        this.render = true;\n        this.cd.markForCheck();\n    }\n\n    onOverlayClick(event: MouseEvent) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n\n        this.selfClick = true;\n    }\n\n    onContentClick(event: MouseEvent) {\n        const targetElement = event.target as HTMLElement;\n        this.selfClick = event.offsetX < targetElement.clientWidth && event.offsetY < targetElement.clientHeight;\n    }\n\n    hasTargetChanged(event: any, target: any) {\n        return this.target != null && this.target !== (target || event.currentTarget || event.target);\n    }\n\n    appendContainer() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);\n            else DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n\n    restoreAppend() {\n        if (this.container && this.appendTo) {\n            this.renderer.appendChild(this.el.nativeElement, this.container);\n        }\n    }\n\n    align() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('overlay', this.container, this.baseZIndex + this.config.zIndex.overlay);\n        }\n\n        DomHandler.absolutePosition(this.container, this.target, false);\n\n        const containerOffset = DomHandler.getOffset(this.container);\n        const targetOffset = DomHandler.getOffset(this.target);\n        const borderRadius = this.document.defaultView?.getComputedStyle(this.container!).getPropertyValue('border-radius');\n        let arrowLeft = 0;\n\n        if (containerOffset.left < targetOffset.left) {\n            arrowLeft = targetOffset.left - containerOffset.left - parseFloat(borderRadius!) * 2;\n        }\n        this.container?.style.setProperty('--overlayArrowLeft', `${arrowLeft}px`);\n\n        if (containerOffset.top < targetOffset.top) {\n            DomHandler.addClass(this.container, 'p-overlaypanel-flipped');\n\n            if (this.showCloseIcon) {\n                this.renderer.setStyle(this.container, 'margin-top', '-30px');\n            }\n        }\n    }\n\n    onAnimationStart(event: AnimationEvent) {\n        if (event.toState === 'open') {\n            this.container = event.element;\n            this.appendContainer();\n            this.align();\n            this.bindDocumentClickListener();\n            this.bindDocumentResizeListener();\n            this.bindScrollListener();\n\n            if (this.focusOnShow) {\n                this.focus();\n            }\n\n            this.overlayEventListener = (e) => {\n                if (this.container && this.container.contains(e.target)) {\n                    this.selfClick = true;\n                }\n            };\n\n            this.overlaySubscription = this.overlayService.clickObservable.subscribe(this.overlayEventListener);\n            this.onShow.emit(null);\n        }\n\n        this.isOverlayAnimationInProgress = true;\n    }\n\n    onAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                if (this.destroyCallback) {\n                    this.destroyCallback();\n                    this.destroyCallback = null;\n                }\n\n                if (this.overlaySubscription) {\n                    this.overlaySubscription.unsubscribe();\n                }\n                break;\n\n            case 'close':\n                if (this.autoZIndex) {\n                    ZIndexUtils.clear(this.container);\n                }\n\n                if (this.overlaySubscription) {\n                    this.overlaySubscription.unsubscribe();\n                }\n\n                this.onContainerDestroy();\n                this.onHide.emit({});\n                this.render = false;\n                break;\n        }\n\n        this.isOverlayAnimationInProgress = false;\n    }\n\n    focus() {\n        let focusable = DomHandler.findSingle(this.container, '[autofocus]');\n        if (focusable) {\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => focusable.focus(), 5);\n            });\n        }\n    }\n    /**\n     * Hides the panel.\n     * @group Method\n     */\n    hide() {\n        this.overlayVisible = false;\n        this.cd.markForCheck();\n    }\n\n    onCloseClick(event: MouseEvent) {\n        this.hide();\n        event.preventDefault();\n    }\n\n    @HostListener('document:keydown.escape', ['$event'])\n    onEscapeKeydown(event: KeyboardEvent) {\n        this.hide();\n    }\n\n    onWindowResize() {\n        if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n\n    bindDocumentResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.documentResizeListener) {\n                const window = this.document.defaultView as Window;\n                this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n            }\n        }\n    }\n\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n\n    bindScrollListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                    if (this.overlayVisible) {\n                        this.hide();\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        }\n    }\n\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n\n    onContainerDestroy() {\n        if (!(this.cd as ViewRef).destroyed) {\n            this.target = null;\n        }\n\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n    }\n\n    ngOnDestroy() {\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.container && this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n\n        if (!(this.cd as ViewRef).destroyed) {\n            this.target = null;\n        }\n\n        this.destroyCallback = null;\n        if (this.container) {\n            this.restoreAppend();\n            this.onContainerDestroy();\n        }\n\n        if (this.overlaySubscription) {\n            this.overlaySubscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RippleModule, SharedModule, TimesIcon],\n    exports: [OverlayPanel, SharedModule],\n    declarations: [OverlayPanel]\n})\nexport class OverlayPanelModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAiCA;;;AAGG;MA+DU,YAAY,CAAA;AA8GS,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACtB,IAAA,EAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACC,IAAA,IAAA,CAAA;AACD,IAAA,MAAA,CAAA;AACA,IAAA,cAAA,CAAA;AApHX;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACqC,WAAW,GAAY,IAAI,CAAC;AACpE;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACM,QAAQ,GAAkF,MAAM,CAAC;AAC1G;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;IACqC,WAAW,GAAY,IAAI,CAAC;AACpE;;;AAGG;IACM,qBAAqB,GAAW,iCAAiC,CAAC;AAC3E;;;AAGG;IACM,qBAAqB,GAAW,YAAY,CAAC;AACtD;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAE,CAAC;AACzD;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAE9B,IAAA,SAAS,CAAuC;AAEhF,IAAA,SAAS,CAA2B;IAEpC,cAAc,GAAY,KAAK,CAAC;IAEhC,MAAM,GAAY,KAAK,CAAC;IAExB,4BAA4B,GAAY,KAAK,CAAC;IAE9C,SAAS,GAAY,KAAK,CAAC;AAE3B,IAAA,qBAAqB,CAAe;AAEpC,IAAA,MAAM,CAAM;AAEZ,IAAA,QAAQ,CAAoB;AAE5B,IAAA,aAAa,CAA0C;AAEvD,IAAA,sBAAsB,CAAe;AAErC,IAAA,eAAe,CAA6B;AAE5C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,eAAe,CAAqB;AAEpC,IAAA,oBAAoB,CAAkC;AAEtD,IAAA,mBAAmB,CAA2B;AAE9C,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACrC,EAAc,EACd,QAAmB,EACnB,EAAqB,EACpB,IAAY,EACb,MAAqB,EACrB,cAA8B,EAAA;QAPX,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACrC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACpB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACb,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACrB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;KACrC;IAEJ,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AAED,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAED,yBAAyB,GAAA;AACrB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,WAAW,EAAE;AACjD,gBAAA,IAAI,aAAa,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,GAAG,OAAO,CAAC;gBAChE,MAAM,cAAc,GAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;AAE1F,gBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,aAAa,EAAE,CAAC,KAAK,KAAI;AACvF,oBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;wBACnI,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AAED,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,oBAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,2BAA2B,GAAA;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAClC,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC1B,SAAA;KACJ;AAED;;;;;AAKG;IACH,MAAM,CAAC,KAAU,EAAE,MAAY,EAAA;QAC3B,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,OAAO;AACV,SAAA;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;AACtC,gBAAA,IAAI,CAAC,eAAe,GAAG,MAAK;AACxB,oBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;AACnE,iBAAC,CAAC;AACL,aAAA;YAED,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5B,SAAA;KACJ;AACD;;;;;AAKG;IACH,IAAI,CAAC,KAAU,EAAE,MAAY,EAAA;AACzB,QAAA,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;QAC3C,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC;AAC5D,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa;AAChC,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACzB;AAED,IAAA,cAAc,CAAC,KAAiB,EAAA;AAC5B,QAAA,MAAM,aAAa,GAAG,KAAK,CAAC,MAAqB,CAAC;AAClD,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC;KAC5G;IAED,gBAAgB,CAAC,KAAU,EAAE,MAAW,EAAA;QACpC,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;KACjG;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;AAAE,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;gBACvF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9D,SAAA;KACJ;IAED,aAAa,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjC,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACpE,SAAA;KACJ;IAED,KAAK,GAAA;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5F,SAAA;AAED,QAAA,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAEhE,MAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,gBAAgB,CAAC,IAAI,CAAC,SAAU,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;QACpH,IAAI,SAAS,GAAG,CAAC,CAAC;AAElB,QAAA,IAAI,eAAe,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,EAAE;AAC1C,YAAA,SAAS,GAAG,YAAY,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,GAAG,UAAU,CAAC,YAAa,CAAC,GAAG,CAAC,CAAC;AACxF,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAA,EAAG,SAAS,CAAA,EAAA,CAAI,CAAC,CAAC;AAE1E,QAAA,IAAI,eAAe,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE;YACxC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;YAE9D,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,gBAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AACjE,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAqB,EAAA;AAClC,QAAA,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;AAC1B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,KAAK,EAAE,CAAC;AAChB,aAAA;AAED,YAAA,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,KAAI;AAC9B,gBAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;AACrD,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACzB,iBAAA;AACL,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AACpG,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,SAAA;AAED,QAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;KAC5C;AAED,IAAA,cAAc,CAAC,KAAqB,EAAA;QAChC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;gBACP,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC/B,iBAAA;gBAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC1B,oBAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AAC1C,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,OAAO;gBACR,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,oBAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,iBAAA;gBAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC1B,oBAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AAC1C,iBAAA;gBAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrB,gBAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,MAAM;AACb,SAAA;AAED,QAAA,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;KAC7C;IAED,KAAK,GAAA;AACD,QAAA,IAAI,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AACrE,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,UAAU,CAAC,MAAM,SAAS,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3C,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AACD;;;AAGG;IACH,IAAI,GAAA;AACA,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAiB,EAAA;QAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAGD,IAAA,eAAe,CAAC,KAAoB,EAAA;QAChC,IAAI,CAAC,IAAI,EAAE,CAAC;KACf;IAED,cAAc,GAAA;QACV,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;YACpD,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;KACJ;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAC9B,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;gBACnD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxG,aAAA;AACJ,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,IAAI,CAAC,aAAa,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,MAAK;oBACrE,IAAI,IAAI,CAAC,cAAc,EAAE;wBACrB,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AAED,YAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;AAC3C,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;AAC7C,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAE,IAAI,CAAC,EAAc,CAAC,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;QAED,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAE,IAAI,CAAC,EAAc,CAAC,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,SAAA;AAED,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,SAAA;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC1B,YAAA,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;AAC1C,SAAA;KACJ;uGAvaQ,YAAY,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EA8GT,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA/Gd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAeD,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAKhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAoBhB,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAUhB,EAAA,cAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,eAAe,CAKf,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAsBnB,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,yBAAA,EAAA,yBAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAzIpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,62BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA6cmD,SAAS,CA5cjD,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,UAAA,EAAA;YACR,OAAO,CAAC,WAAW,EAAE;AACjB,gBAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,oBAAA,SAAS,EAAE,aAAa;AACxB,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,OAAO,EACP,KAAK,CAAC;AACF,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA,CAAC,CACL;AACD,gBAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,oBAAA,SAAS,EAAE,eAAe;AAC1B,oBAAA,OAAO,EAAE,CAAC;AACb,iBAAA,CAAC,CACL;AACD,gBAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC/D,gBAAA,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;aACnE,CAAC;AACL,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,YAAY,EAAA,UAAA,EAAA,CAAA;kBA9DxB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,gBAAgB,EAChB,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;KA0BT,EACW,UAAA,EAAA;wBACR,OAAO,CAAC,WAAW,EAAE;AACjB,4BAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,gCAAA,SAAS,EAAE,aAAa;AACxB,gCAAA,OAAO,EAAE,CAAC;AACb,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,OAAO,EACP,KAAK,CAAC;AACF,gCAAA,OAAO,EAAE,CAAC;AACb,6BAAA,CAAC,CACL;AACD,4BAAA,KAAK,CACD,MAAM,EACN,KAAK,CAAC;AACF,gCAAA,SAAS,EAAE,eAAe;AAC1B,gCAAA,OAAO,EAAE,CAAC;AACb,6BAAA,CAAC,CACL;AACD,4BAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC/D,4BAAA,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC;yBACnE,CAAC;AACL,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,62BAAA,CAAA,EAAA,CAAA;;0BAgHI,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;wMA1Gd,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKiC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKI,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBA2Q9B,eAAe,EAAA,CAAA;sBADd,YAAY;uBAAC,yBAAyB,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAwF1C,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,EA/alB,YAAA,EAAA,CAAA,YAAY,CA2aX,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,CA3apD,EAAA,OAAA,EAAA,CAAA,YAAY,EA4aG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAG3B,kBAAkB,EAAA,OAAA,EAAA,CAJjB,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EACrC,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG3B,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,CAAC;AAC9D,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;oBACrC,YAAY,EAAE,CAAC,YAAY,CAAC;AAC/B,iBAAA,CAAA;;;ACjhBD;;AAEG;;;;"}