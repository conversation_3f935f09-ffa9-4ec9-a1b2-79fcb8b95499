{"version": 3, "file": "primeng-password.mjs", "sources": ["../../src/app/components/password/password.ts", "../../src/app/components/password/primeng-password.ts"], "sourcesContent": ["import { AnimationEvent, animate, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    Directive,\n    DoCheck,\n    ElementRef,\n    EventEmitter,\n    HostListener,\n    Inject,\n    Input,\n    NgModule,\n    NgZone,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    Pipe,\n    PipeTransform,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    forwardRef,\n    numberAttribute\n} from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule, TranslationKeys } from 'primeng/api';\nimport { ConnectedOverlayScrollHandler, DomHandler } from 'primeng/dom';\nimport { EyeIcon } from 'primeng/icons/eye';\nimport { EyeSlashIcon } from 'primeng/icons/eyeslash';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { AutoFocusModule } from 'primeng/autofocus';\nimport { Subscription } from 'rxjs';\n\ntype Meter = {\n    strength: string;\n    width: string;\n};\n/**\n * Password directive.\n * @group Components\n */\n@Directive({\n    selector: '[pPassword]',\n    host: {\n        class: 'p-inputtext p-component p-element',\n        '[class.p-filled]': 'filled',\n        '[class.p-variant-filled]': 'variant === \"filled\" || config.inputStyle() === \"filled\"'\n    }\n})\nexport class PasswordDirective implements OnDestroy, DoCheck {\n    /**\n     * Text to prompt password entry. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    @Input() promptLabel: string = 'Enter a password';\n    /**\n     * Text for a weak password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    @Input() weakLabel: string = 'Weak';\n    /**\n     * Text for a medium password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    @Input() mediumLabel: string = 'Medium';\n    /**\n     * Text for a strong password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    @Input() strongLabel: string = 'Strong';\n    /**\n     * Whether to show the strength indicator or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) feedback: boolean = true;\n    /**\n     * Sets the visibility of the password field.\n     * @group Props\n     */\n    @Input() set showPassword(show: boolean) {\n        this.el.nativeElement.type = show ? 'text' : 'password';\n    }\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n\n    panel: Nullable<HTMLDivElement>;\n\n    meter: Nullable<Meter>;\n\n    info: Nullable<HTMLDivElement>;\n\n    filled: Nullable<boolean>;\n\n    scrollHandler: Nullable<ConnectedOverlayScrollHandler>;\n\n    documentResizeListener: VoidListener;\n\n    constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: any, private renderer: Renderer2, public el: ElementRef, public zone: NgZone, public config: PrimeNGConfig) {}\n\n    ngDoCheck() {\n        this.updateFilledState();\n    }\n\n    @HostListener('input', ['$event'])\n    onInput(e: Event) {\n        this.updateFilledState();\n    }\n\n    updateFilledState() {\n        this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n    }\n\n    createPanel() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.panel = this.renderer.createElement('div');\n            this.renderer.addClass(this.panel, 'p-password-panel');\n            this.renderer.addClass(this.panel, 'p-component');\n            this.renderer.addClass(this.panel, 'p-password-panel-overlay');\n            this.renderer.addClass(this.panel, 'p-connected-overlay');\n\n            this.meter = this.renderer.createElement('div');\n            this.renderer.addClass(this.meter, 'p-password-meter');\n            this.renderer.appendChild(this.panel, this.meter);\n\n            this.info = this.renderer.createElement('div');\n            this.renderer.addClass(this.info, 'p-password-info');\n            this.renderer.setProperty(this.info, 'textContent', this.promptLabel);\n            this.renderer.appendChild(this.panel, this.info);\n\n            this.renderer.setStyle(this.panel, 'minWidth', `${this.el.nativeElement.offsetWidth}px`);\n            this.renderer.appendChild(document.body, this.panel);\n        }\n    }\n\n    showOverlay() {\n        if (this.feedback) {\n            if (!this.panel) {\n                this.createPanel();\n            }\n\n            this.renderer.setStyle(this.panel, 'zIndex', String(++DomHandler.zindex));\n            this.renderer.setStyle(this.panel, 'display', 'block');\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    DomHandler.addClass(this.panel, 'p-connected-overlay-visible');\n                    this.bindScrollListener();\n                    this.bindDocumentResizeListener();\n                }, 1);\n            });\n            DomHandler.absolutePosition(this.panel, this.el.nativeElement);\n        }\n    }\n\n    hideOverlay() {\n        if (this.feedback && this.panel) {\n            DomHandler.addClass(this.panel, 'p-connected-overlay-hidden');\n            DomHandler.removeClass(this.panel, 'p-connected-overlay-visible');\n            this.unbindScrollListener();\n            this.unbindDocumentResizeListener();\n\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    this.ngOnDestroy();\n                }, 150);\n            });\n        }\n    }\n\n    @HostListener('focus')\n    onFocus() {\n        this.showOverlay();\n    }\n\n    @HostListener('blur')\n    onBlur() {\n        this.hideOverlay();\n    }\n\n    @HostListener('keyup', ['$event'])\n    onKeyup(e: Event) {\n        if (this.feedback) {\n            let value = (e.target as HTMLInputElement).value,\n                label = null,\n                meterPos = null;\n\n            if (value.length === 0) {\n                label = this.promptLabel;\n                meterPos = '0px 0px';\n            } else {\n                var score = this.testStrength(value);\n\n                if (score < 30) {\n                    label = this.weakLabel;\n                    meterPos = '0px -10px';\n                } else if (score >= 30 && score < 80) {\n                    label = this.mediumLabel;\n                    meterPos = '0px -20px';\n                } else if (score >= 80) {\n                    label = this.strongLabel;\n                    meterPos = '0px -30px';\n                }\n            }\n\n            if (!this.panel || !DomHandler.hasClass(this.panel, 'p-connected-overlay-visible')) {\n                this.showOverlay();\n            }\n\n            this.renderer.setStyle(this.meter, 'backgroundPosition', meterPos);\n            (this.info as HTMLDivElement).textContent = label;\n        }\n    }\n\n    testStrength(str: string) {\n        let grade: number = 0;\n        let val: Nullable<RegExpMatchArray>;\n\n        val = str.match('[0-9]');\n        grade += this.normalize(val ? val.length : 1 / 4, 1) * 25;\n\n        val = str.match('[a-zA-Z]');\n        grade += this.normalize(val ? val.length : 1 / 2, 3) * 10;\n\n        val = str.match('[!@#$%^&*?_~.,;=]');\n        grade += this.normalize(val ? val.length : 1 / 6, 1) * 35;\n\n        val = str.match('[A-Z]');\n        grade += this.normalize(val ? val.length : 1 / 6, 1) * 30;\n\n        grade *= str.length / 8;\n\n        return grade > 100 ? 100 : grade;\n    }\n\n    normalize(x: number, y: number) {\n        let diff = x - y;\n\n        if (diff <= 0) return x / y;\n        else return 1 + 0.5 * (x / (x + y / 4));\n    }\n\n    get disabled(): boolean {\n        return this.el.nativeElement.disabled;\n    }\n\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n                if (DomHandler.hasClass(this.panel, 'p-connected-overlay-visible')) {\n                    this.hideOverlay();\n                }\n            });\n        }\n\n        this.scrollHandler.bindScrollListener();\n    }\n\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n\n    bindDocumentResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.documentResizeListener) {\n                const window = this.document.defaultView as Window;\n                this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n            }\n        }\n    }\n\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n\n    onWindowResize() {\n        if (!DomHandler.isTouchDevice()) {\n            this.hideOverlay();\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.panel) {\n            if (this.scrollHandler) {\n                this.scrollHandler.destroy();\n                this.scrollHandler = null;\n            }\n\n            this.unbindDocumentResizeListener();\n\n            this.renderer.removeChild(this.document.body, this.panel);\n            this.panel = null;\n            this.meter = null;\n            this.info = null;\n        }\n    }\n}\n\ntype Mapper<T, G> = (item: T, ...args: any[]) => G;\n\n@Pipe({\n    name: 'mapper',\n    pure: true\n})\nexport class MapperPipe implements PipeTransform {\n    public transform<T, G>(value: T, mapper: Mapper<T, G>, ...args: unknown[]): G {\n        return mapper(value, ...args);\n    }\n}\n\nexport const Password_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Password),\n    multi: true\n};\n/**\n * Password displays strength indicator for password fields.\n * @group Components\n */\n@Component({\n    selector: 'p-password',\n    template: `\n        <div [ngClass]=\"toggleMask | mapper : containerClass\" [ngStyle]=\"style\" [class]=\"styleClass\" [attr.data-pc-name]=\"'password'\" [attr.data-pc-section]=\"'root'\">\n            <input\n                #input\n                [attr.label]=\"label\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledBy]=\"ariaLabelledBy\"\n                [attr.id]=\"inputId\"\n                pInputText\n                [ngClass]=\"disabled | mapper : inputFieldClass\"\n                [disabled]=\"disabled\"\n                [ngStyle]=\"inputStyle\"\n                [class]=\"inputStyleClass\"\n                [attr.type]=\"unmasked | mapper : inputType\"\n                [attr.placeholder]=\"placeholder\"\n                [attr.autocomplete]=\"autocomplete\"\n                [value]=\"value\"\n                [variant]=\"variant\"\n                (input)=\"onInput($event)\"\n                (focus)=\"onInputFocus($event)\"\n                (blur)=\"onInputBlur($event)\"\n                (keyup)=\"onKeyUp($event)\"\n                [attr.maxlength]=\"maxLength\"\n                [attr.data-pc-section]=\"'input'\"\n                pAutoFocus\n                [autofocus]=\"autofocus\"\n            />\n            <ng-container *ngIf=\"showClear && value != null\">\n                <TimesIcon *ngIf=\"!clearIconTemplate\" [styleClass]=\"'p-password-clear-icon'\" (click)=\"clear()\" [attr.data-pc-section]=\"'clearIcon'\" />\n                <span (click)=\"clear()\" class=\"p-password-clear-icon\" [attr.data-pc-section]=\"'clearIcon'\">\n                    <ng-template *ngTemplateOutlet=\"clearIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n\n            <ng-container *ngIf=\"toggleMask\">\n                <ng-container *ngIf=\"unmasked\">\n                    <EyeSlashIcon *ngIf=\"!hideIconTemplate\" (click)=\"onMaskToggle()\" [attr.data-pc-section]=\"'hideIcon'\" />\n                    <span *ngIf=\"hideIconTemplate\" (click)=\"onMaskToggle()\">\n                        <ng-template *ngTemplateOutlet=\"hideIconTemplate\"></ng-template>\n                    </span>\n                </ng-container>\n                <ng-container *ngIf=\"!unmasked\">\n                    <EyeIcon *ngIf=\"!showIconTemplate\" (click)=\"onMaskToggle()\" [attr.data-pc-section]=\"'showIcon'\" />\n                    <span *ngIf=\"showIconTemplate\" (click)=\"onMaskToggle()\">\n                        <ng-template *ngTemplateOutlet=\"showIconTemplate\"></ng-template>\n                    </span>\n                </ng-container>\n            </ng-container>\n\n            <div\n                #overlay\n                *ngIf=\"overlayVisible\"\n                [ngClass]=\"'p-password-panel p-component'\"\n                (click)=\"onOverlayClick($event)\"\n                [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n                (@overlayAnimation.start)=\"onAnimationStart($event)\"\n                (@overlayAnimation.done)=\"onAnimationEnd($event)\"\n                [attr.data-pc-section]=\"'panel'\"\n            >\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <ng-container *ngIf=\"contentTemplate; else content\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </ng-container>\n                <ng-template #content>\n                    <div class=\"p-password-meter\" [attr.data-pc-section]=\"'meter'\">\n                        <div [ngClass]=\"meter | mapper : strengthClass\" [ngStyle]=\"{ width: meter ? meter.width : '' }\" [attr.data-pc-section]=\"'meterLabel'\"></div>\n                    </div>\n                    <div class=\"p-password-info\" [attr.data-pc-section]=\"'info'\">{{ infoText }}</div>\n                </ng-template>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n    animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0, transform: 'scaleY(0.8)' }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({ opacity: 0 }))])])],\n    host: {\n        class: 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled()',\n        '[class.p-inputwrapper-focus]': 'focused',\n        '[class.p-password-clearable]': 'showClear',\n        '[class.p-password-mask]': 'toggleMask'\n    },\n    providers: [Password_VALUE_ACCESSOR],\n    styleUrls: ['./password.css'],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None\n})\nexport class Password implements AfterContentInit, OnInit {\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Label of the input for accessibility.\n     * @group Props\n     */\n    @Input() label: string | undefined;\n    /**\n     * Indicates whether the component is disabled or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * Text to prompt password entry. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    @Input() promptLabel: string | undefined;\n    /**\n     * Regex value for medium regex.\n     * @group Props\n     */\n    @Input() mediumRegex: string = '^(((?=.*[a-z])(?=.*[A-Z]))|((?=.*[a-z])(?=.*[0-9]))|((?=.*[A-Z])(?=.*[0-9])))(?=.{6,})';\n    /**\n     * Regex value for strong regex.\n     * @group Props\n     */\n    @Input() strongRegex: string = '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{8,})';\n    /**\n     * Text for a weak password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    @Input() weakLabel: string | undefined;\n    /**\n     * Text for a medium password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    @Input() mediumLabel: string | undefined;\n    /**\n     * specifies the maximum number of characters allowed in the input element.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) maxLength: number | undefined;\n    /**\n     * Text for a strong password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    @Input() strongLabel: string | undefined;\n    /**\n     * Identifier of the accessible input element.\n     * @group Props\n     */\n    @Input() inputId: string | undefined;\n    /**\n     * Whether to show the strength indicator or not.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) feedback: boolean = true;\n    /**\n     * Id of the element or \"body\" for document where the overlay should be appended to.\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Whether to show an icon to display the password as plain text.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) toggleMask: boolean | undefined;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    @Input() inputStyleClass: string | undefined;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    @Input() inputStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.1s linear';\n    /**\n     * Specify automated assistance in filling out password by browser.\n     * @group Props\n     */\n    @Input() autocomplete: string | undefined;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    @Input() placeholder: string | undefined;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showClear: boolean = false;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autofocus: boolean | undefined;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    @Input() variant: 'filled' | 'outlined' = 'outlined';\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when clear button is clicked.\n     * @group Emits\n     */\n    @Output() onClear: EventEmitter<any> = new EventEmitter<any>();\n\n    @ViewChild('input') input!: ElementRef;\n\n    contentTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    clearIconTemplate: Nullable<TemplateRef<any>>;\n\n    hideIconTemplate: Nullable<TemplateRef<any>>;\n\n    showIconTemplate: Nullable<TemplateRef<any>>;\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    overlayVisible: boolean = false;\n\n    meter: Nullable<Meter>;\n\n    infoText: Nullable<string>;\n\n    focused: boolean = false;\n\n    unmasked: boolean = false;\n\n    mediumCheckRegExp!: RegExp;\n\n    strongCheckRegExp!: RegExp;\n\n    resizeListener: VoidListener;\n\n    scrollHandler: Nullable<ConnectedOverlayScrollHandler>;\n\n    overlay: HTMLElement | ElementRef | null | undefined;\n\n    value: Nullable<string> = null;\n\n    onModelChange: Function = () => {};\n\n    onModelTouched: Function = () => {};\n\n    translationSubscription: Nullable<Subscription>;\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        private renderer: Renderer2,\n        private cd: ChangeDetectorRef,\n        private config: PrimeNGConfig,\n        public el: ElementRef,\n        public overlayService: OverlayService\n    ) {}\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'clearicon':\n                    this.clearIconTemplate = item.template;\n                    break;\n\n                case 'hideicon':\n                    this.hideIconTemplate = item.template;\n                    break;\n\n                case 'showicon':\n                    this.showIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    ngOnInit() {\n        this.infoText = this.promptText();\n        this.mediumCheckRegExp = new RegExp(this.mediumRegex);\n        this.strongCheckRegExp = new RegExp(this.strongRegex);\n        this.translationSubscription = this.config.translationObserver.subscribe(() => {\n            this.updateUI(this.value || '');\n        });\n    }\n\n    onAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                this.overlay = event.element;\n                ZIndexUtils.set('overlay', this.overlay, this.config.zIndex.overlay);\n                this.appendContainer();\n                this.alignOverlay();\n                this.bindScrollListener();\n                this.bindResizeListener();\n                break;\n\n            case 'void':\n                this.unbindScrollListener();\n                this.unbindResizeListener();\n                this.overlay = null;\n                break;\n        }\n    }\n\n    onAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                ZIndexUtils.clear(event.element);\n                break;\n        }\n    }\n\n    appendContainer() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.overlay);\n            else (this.document as any).getElementById(this.appendTo).appendChild(this.overlay as HTMLElement);\n        }\n    }\n\n    alignOverlay() {\n        if (this.appendTo) {\n            (this.overlay as HTMLElement).style.minWidth = DomHandler.getOuterWidth(this.input.nativeElement) + 'px';\n            DomHandler.absolutePosition(this.overlay, this.input.nativeElement);\n        } else {\n            DomHandler.relativePosition(this.overlay, this.input.nativeElement);\n        }\n    }\n\n    onInput(event: Event) {\n        this.value = (event.target as HTMLInputElement).value;\n        this.onModelChange(this.value);\n    }\n\n    onInputFocus(event: Event) {\n        this.focused = true;\n        if (this.feedback) {\n            this.overlayVisible = true;\n        }\n\n        this.onFocus.emit(event);\n    }\n\n    onInputBlur(event: Event) {\n        this.focused = false;\n        if (this.feedback) {\n            this.overlayVisible = false;\n        }\n\n        this.onModelTouched();\n        this.onBlur.emit(event);\n    }\n\n    onKeyUp(event: KeyboardEvent) {\n        if (this.feedback) {\n            let value = (event.target as HTMLInputElement).value;\n            this.updateUI(value);\n\n            if (event.code === 'Escape') {\n                this.overlayVisible && (this.overlayVisible = false);\n\n                return;\n            }\n\n            if (!this.overlayVisible) {\n                this.overlayVisible = true;\n            }\n        }\n    }\n\n    updateUI(value: string) {\n        let label = null;\n        let meter = null;\n\n        switch (this.testStrength(value)) {\n            case 1:\n                label = this.weakText();\n                meter = {\n                    strength: 'weak',\n                    width: '33.33%'\n                };\n                break;\n\n            case 2:\n                label = this.mediumText();\n                meter = {\n                    strength: 'medium',\n                    width: '66.66%'\n                };\n                break;\n\n            case 3:\n                label = this.strongText();\n                meter = {\n                    strength: 'strong',\n                    width: '100%'\n                };\n                break;\n\n            default:\n                label = this.promptText();\n                meter = null;\n                break;\n        }\n\n        this.meter = meter;\n        this.infoText = label;\n    }\n\n    onMaskToggle() {\n        this.unmasked = !this.unmasked;\n    }\n\n    onOverlayClick(event: Event) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n    }\n\n    testStrength(str: string) {\n        let level = 0;\n\n        if (this.strongCheckRegExp.test(str)) level = 3;\n        else if (this.mediumCheckRegExp.test(str)) level = 2;\n        else if (str.length) level = 1;\n\n        return level;\n    }\n\n    writeValue(value: any): void {\n        if (value === undefined) this.value = null;\n        else this.value = value;\n\n        if (this.feedback) this.updateUI(this.value || '');\n\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    bindScrollListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.input.nativeElement, () => {\n                    if (this.overlayVisible) {\n                        this.overlayVisible = false;\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        }\n    }\n\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                const window = this.document.defaultView as Window;\n                this.resizeListener = this.renderer.listen(window, 'resize', () => {\n                    if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n                        this.overlayVisible = false;\n                    }\n                });\n            }\n        }\n    }\n\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n\n    containerClass(toggleMask: boolean) {\n        return { 'p-password p-component p-inputwrapper': true, 'p-input-icon-right': toggleMask };\n    }\n\n    inputFieldClass(disabled: boolean) {\n        return { 'p-password-input': true, 'p-disabled': disabled };\n    }\n\n    strengthClass(meter: any) {\n        return `p-password-strength ${meter ? meter.strength : ''}`;\n    }\n\n    filled() {\n        return this.value != null && this.value.toString().length > 0;\n    }\n\n    promptText() {\n        return this.promptLabel || this.getTranslation(TranslationKeys.PASSWORD_PROMPT);\n    }\n\n    weakText() {\n        return this.weakLabel || this.getTranslation(TranslationKeys.WEAK);\n    }\n\n    mediumText() {\n        return this.mediumLabel || this.getTranslation(TranslationKeys.MEDIUM);\n    }\n\n    strongText() {\n        return this.strongLabel || this.getTranslation(TranslationKeys.STRONG);\n    }\n\n    restoreAppend() {\n        if (this.overlay && this.appendTo) {\n            if (this.appendTo === 'body') this.renderer.removeChild(this.document.body, this.overlay);\n            else (this.document as any).getElementById(this.appendTo).removeChild(this.overlay);\n        }\n    }\n\n    inputType(unmasked: boolean) {\n        return unmasked ? 'text' : 'password';\n    }\n\n    getTranslation(option: string) {\n        return this.config.getTranslation(option);\n    }\n\n    clear() {\n        this.value = null;\n        this.onModelChange(this.value);\n        this.writeValue(this.value);\n        this.onClear.emit();\n    }\n\n    ngOnDestroy() {\n        if (this.overlay) {\n            ZIndexUtils.clear(this.overlay);\n            this.overlay = null;\n        }\n\n        this.restoreAppend();\n        this.unbindResizeListener();\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        if (this.translationSubscription) {\n            this.translationSubscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, InputTextModule, AutoFocusModule, TimesIcon, EyeSlashIcon, EyeIcon],\n    exports: [PasswordDirective, Password, SharedModule],\n    declarations: [PasswordDirective, Password, MapperPipe]\n})\nexport class PasswordModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAgDA;;;AAGG;MASU,iBAAiB,CAAA;AAmDY,IAAA,QAAA,CAAA;AAAiD,IAAA,UAAA,CAAA;AAAyB,IAAA,QAAA,CAAA;AAA4B,IAAA,EAAA,CAAA;AAAuB,IAAA,IAAA,CAAA;AAAqB,IAAA,MAAA,CAAA;AAlDxL;;;AAGG;IACM,WAAW,GAAW,kBAAkB,CAAC;AAClD;;;AAGG;IACM,SAAS,GAAW,MAAM,CAAC;AACpC;;;AAGG;IACM,WAAW,GAAW,QAAQ,CAAC;AACxC;;;AAGG;IACM,WAAW,GAAW,QAAQ,CAAC;AACxC;;;AAGG;IACqC,QAAQ,GAAY,IAAI,CAAC;AACjE;;;AAGG;IACH,IAAa,YAAY,CAAC,IAAa,EAAA;AACnC,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,GAAG,MAAM,GAAG,UAAU,CAAC;KAC3D;AACD;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AAErD,IAAA,KAAK,CAA2B;AAEhC,IAAA,KAAK,CAAkB;AAEvB,IAAA,IAAI,CAA2B;AAE/B,IAAA,MAAM,CAAoB;AAE1B,IAAA,aAAa,CAA0C;AAEvD,IAAA,sBAAsB,CAAe;IAErC,WAAsC,CAAA,QAAkB,EAA+B,UAAe,EAAU,QAAmB,EAAS,EAAc,EAAS,IAAY,EAAS,MAAqB,EAAA;QAAvK,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAA+B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAAI;IAEjN,SAAS,GAAA;QACL,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC5B;AAGD,IAAA,OAAO,CAAC,CAAQ,EAAA;QACZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC5B;IAED,iBAAiB,GAAA;QACb,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;KACnF;IAED,WAAW,GAAA;AACP,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACpC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAChD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YAClD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,0BAA0B,CAAC,CAAC;YAC/D,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;YAE1D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAChD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAElD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;AACrD,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACtE,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,CAAG,EAAA,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,WAAW,CAAI,EAAA,CAAA,CAAC,CAAC;AACzF,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACxD,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gBACb,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,aAAA;AAED,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,UAAU,CAAC,MAAK;oBACZ,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;oBAC/D,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,IAAI,CAAC,0BAA0B,EAAE,CAAC;iBACrC,EAAE,CAAC,CAAC,CAAC;AACV,aAAC,CAAC,CAAC;AACH,YAAA,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;AAClE,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;YAC7B,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,4BAA4B,CAAC,CAAC;YAC9D,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;YAClE,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,4BAA4B,EAAE,CAAC;AAEpC,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC7B,UAAU,CAAC,MAAK;oBACZ,IAAI,CAAC,WAAW,EAAE,CAAC;iBACtB,EAAE,GAAG,CAAC,CAAC;AACZ,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAGD,OAAO,GAAA;QACH,IAAI,CAAC,WAAW,EAAE,CAAC;KACtB;IAGD,MAAM,GAAA;QACF,IAAI,CAAC,WAAW,EAAE,CAAC;KACtB;AAGD,IAAA,OAAO,CAAC,CAAQ,EAAA;QACZ,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,KAAK,GAAI,CAAC,CAAC,MAA2B,CAAC,KAAK,EAC5C,KAAK,GAAG,IAAI,EACZ,QAAQ,GAAG,IAAI,CAAC;AAEpB,YAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACpB,gBAAA,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;gBACzB,QAAQ,GAAG,SAAS,CAAC;AACxB,aAAA;AAAM,iBAAA;gBACH,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAErC,IAAI,KAAK,GAAG,EAAE,EAAE;AACZ,oBAAA,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;oBACvB,QAAQ,GAAG,WAAW,CAAC;AAC1B,iBAAA;AAAM,qBAAA,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE;AAClC,oBAAA,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;oBACzB,QAAQ,GAAG,WAAW,CAAC;AAC1B,iBAAA;qBAAM,IAAI,KAAK,IAAI,EAAE,EAAE;AACpB,oBAAA,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;oBACzB,QAAQ,GAAG,WAAW,CAAC;AAC1B,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC,EAAE;gBAChF,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,aAAA;AAED,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,oBAAoB,EAAE,QAAQ,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,IAAuB,CAAC,WAAW,GAAG,KAAK,CAAC;AACrD,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,GAAW,EAAA;QACpB,IAAI,KAAK,GAAW,CAAC,CAAC;AACtB,QAAA,IAAI,GAA+B,CAAC;AAEpC,QAAA,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzB,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;AAE1D,QAAA,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC5B,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;AAE1D,QAAA,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACrC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;AAE1D,QAAA,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzB,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;AAE1D,QAAA,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QAExB,OAAO,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC;KACpC;IAED,SAAS,CAAC,CAAS,EAAE,CAAS,EAAA;AAC1B,QAAA,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAEjB,IAAI,IAAI,IAAI,CAAC;YAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;AACvB,YAAA,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C;AAED,IAAA,IAAI,QAAQ,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC;KACzC;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,MAAK;gBAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,6BAA6B,CAAC,EAAE;oBAChE,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;KAC3C;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;AAC7C,SAAA;KACJ;IAED,0BAA0B,GAAA;AACtB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;AAC9B,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;gBACnD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxG,aAAA;AACJ,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,cAAc,GAAA;AACV,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;YAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;AACtB,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,aAAA;YAED,IAAI,CAAC,4BAA4B,EAAE,CAAC;AAEpC,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB,SAAA;KACJ;uGA5PQ,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAmDN,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAsC,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAnDpE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,oLAyBN,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,WAAA,EAAA,MAAA,EAAA,UAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,8DAAA,EAAA,EAAA,cAAA,EAAA,mCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAzB3B,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAR7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,mCAAmC;AAC1C,wBAAA,kBAAkB,EAAE,QAAQ;AAC5B,wBAAA,0BAA0B,EAAE,0DAA0D;AACzF,qBAAA;AACJ,iBAAA,CAAA;;0BAoDgB,MAAM;2BAAC,QAAQ,CAAA;;0BAA+B,MAAM;2BAAC,WAAW,CAAA;2IA9CpE,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,YAAY,EAAA,CAAA;sBAAxB,KAAK;gBAOG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAqBN,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;gBAkEjC,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,CAAA;gBAMrB,MAAM,EAAA,CAAA;sBADL,YAAY;uBAAC,MAAM,CAAA;gBAMpB,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAiIxB,UAAU,CAAA;AACZ,IAAA,SAAS,CAAO,KAAQ,EAAE,MAAoB,EAAE,GAAG,IAAe,EAAA;AACrE,QAAA,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;KACjC;uGAHQ,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA;qGAAV,UAAU,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,CAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBAJtB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACF,oBAAA,IAAI,EAAE,QAAQ;AACd,oBAAA,IAAI,EAAE,IAAI;AACb,iBAAA,CAAA;;AAOY,MAAA,uBAAuB,GAAQ;AACxC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,QAAQ,CAAC;AACvC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MAyFU,QAAQ,CAAA;AAkMa,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACrB,IAAA,QAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACA,IAAA,MAAA,CAAA;AACD,IAAA,EAAA,CAAA;AACA,IAAA,cAAA,CAAA;AAvMX;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,KAAK,CAAqB;AACnC;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;IACM,WAAW,GAAW,wFAAwF,CAAC;AACxH;;;AAGG;IACM,WAAW,GAAW,6CAA6C,CAAC;AAC7E;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACoC,IAAA,SAAS,CAAqB;AACrE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACqC,QAAQ,GAAY,IAAI,CAAC;AACjE;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;AACqC,IAAA,UAAU,CAAsB;AACxE;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAA8C;AACjE;;;AAGG;IACM,qBAAqB,GAAW,iCAAiC,CAAC;AAC3E;;;AAGG;IACM,qBAAqB,GAAW,YAAY,CAAC;AACtD;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;IACqC,SAAS,GAAY,KAAK,CAAC;AACnE;;;AAGG;AACqC,IAAA,SAAS,CAAsB;AACvE;;;AAGG;IACM,OAAO,GAA0B,UAAU,CAAC;AACrD;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AACnE;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;AAGG;AACO,IAAA,OAAO,GAAsB,IAAI,YAAY,EAAO,CAAC;AAE3C,IAAA,KAAK,CAAc;AAEvC,IAAA,eAAe,CAA6B;AAE5C,IAAA,cAAc,CAA6B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,iBAAiB,CAA6B;AAE9C,IAAA,gBAAgB,CAA6B;AAE7C,IAAA,gBAAgB,CAA6B;AAEb,IAAA,SAAS,CAA4B;IAErE,cAAc,GAAY,KAAK,CAAC;AAEhC,IAAA,KAAK,CAAkB;AAEvB,IAAA,QAAQ,CAAmB;IAE3B,OAAO,GAAY,KAAK,CAAC;IAEzB,QAAQ,GAAY,KAAK,CAAC;AAE1B,IAAA,iBAAiB,CAAU;AAE3B,IAAA,iBAAiB,CAAU;AAE3B,IAAA,cAAc,CAAe;AAE7B,IAAA,aAAa,CAA0C;AAEvD,IAAA,OAAO,CAA8C;IAErD,KAAK,GAAqB,IAAI,CAAC;AAE/B,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,uBAAuB,CAAyB;AAEhD,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACpC,QAAmB,EACnB,EAAqB,EACrB,MAAqB,EACtB,EAAc,EACd,cAA8B,EAAA;QANX,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACpC,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACrB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACtB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;KACrC;IAEJ,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAK;YAC1E,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACpC,SAAC,CAAC,CAAC;KACN;AAED,IAAA,gBAAgB,CAAC,KAAqB,EAAA;QAClC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC7B,gBAAA,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACrE,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAqB,EAAA;QAChC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;AACP,gBAAA,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM;AACb,SAAA;KACJ;IAED,eAAe,GAAA;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;AAAE,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;;AACpF,gBAAA,IAAI,CAAC,QAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,OAAsB,CAAC,CAAC;AACtG,SAAA;KACJ;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,QAAQ,EAAE;AACd,YAAA,IAAI,CAAC,OAAuB,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;AACzG,YAAA,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACvE,SAAA;AAAM,aAAA;AACH,YAAA,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACvE,SAAA;KACJ;AAED,IAAA,OAAO,CAAC,KAAY,EAAA;QAChB,IAAI,CAAC,KAAK,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;AACtD,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAClC;AAED,IAAA,YAAY,CAAC,KAAY,EAAA;AACrB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC/B,SAAA;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,OAAO,CAAC,KAAoB,EAAA;QACxB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,KAAK,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;AACrD,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAErB,YAAA,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACzB,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;gBAErD,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACtB,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,QAAQ,CAAC,KAAa,EAAA;QAClB,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,KAAK,GAAG,IAAI,CAAC;AAEjB,QAAA,QAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AAC5B,YAAA,KAAK,CAAC;AACF,gBAAA,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACxB,gBAAA,KAAK,GAAG;AACJ,oBAAA,QAAQ,EAAE,MAAM;AAChB,oBAAA,KAAK,EAAE,QAAQ;iBAClB,CAAC;gBACF,MAAM;AAEV,YAAA,KAAK,CAAC;AACF,gBAAA,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,gBAAA,KAAK,GAAG;AACJ,oBAAA,QAAQ,EAAE,QAAQ;AAClB,oBAAA,KAAK,EAAE,QAAQ;iBAClB,CAAC;gBACF,MAAM;AAEV,YAAA,KAAK,CAAC;AACF,gBAAA,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,gBAAA,KAAK,GAAG;AACJ,oBAAA,QAAQ,EAAE,QAAQ;AAClB,oBAAA,KAAK,EAAE,MAAM;iBAChB,CAAC;gBACF,MAAM;AAEV,YAAA;AACI,gBAAA,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC1B,KAAK,GAAG,IAAI,CAAC;gBACb,MAAM;AACb,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;KACzB;IAED,YAAY,GAAA;AACR,QAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;KAClC;AAED,IAAA,cAAc,CAAC,KAAY,EAAA;AACvB,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa;AAChC,SAAA,CAAC,CAAC;KACN;AAED,IAAA,YAAY,CAAC,GAAW,EAAA;QACpB,IAAI,KAAK,GAAG,CAAC,CAAC;AAEd,QAAA,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC;YAAE,KAAK,GAAG,CAAC,CAAC;AAC3C,aAAA,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC;YAAE,KAAK,GAAG,CAAC,CAAC;aAChD,IAAI,GAAG,CAAC,MAAM;YAAE,KAAK,GAAG,CAAC,CAAC;AAE/B,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;QACjB,IAAI,KAAK,KAAK,SAAS;AAAE,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;;AACtC,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAExB,IAAI,IAAI,CAAC,QAAQ;YAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AAEnD,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,MAAK;oBAClF,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,wBAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC/B,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AAED,YAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;AAC3C,SAAA;KACJ;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACtB,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAqB,CAAC;AACnD,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAK;oBAC9D,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;AACpD,wBAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC/B,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;AAC7C,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,UAAmB,EAAA;QAC9B,OAAO,EAAE,uCAAuC,EAAE,IAAI,EAAE,oBAAoB,EAAE,UAAU,EAAE,CAAC;KAC9F;AAED,IAAA,eAAe,CAAC,QAAiB,EAAA;QAC7B,OAAO,EAAE,kBAAkB,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC;KAC/D;AAED,IAAA,aAAa,CAAC,KAAU,EAAA;AACpB,QAAA,OAAO,CAAuB,oBAAA,EAAA,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC;KAC/D;IAED,MAAM,GAAA;AACF,QAAA,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;KACjE;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;KACnF;IAED,QAAQ,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KACtE;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KAC1E;IAED,UAAU,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KAC1E;IAED,aAAa,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/B,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;AAAE,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;;AACpF,gBAAA,IAAI,CAAC,QAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvF,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,QAAiB,EAAA;QACvB,OAAO,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;KACzC;AAED,IAAA,cAAc,CAAC,MAAc,EAAA;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;KAC7C;IAED,KAAK,GAAA;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;KACvB;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACvB,SAAA;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,SAAA;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAC9B,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;AAC9C,SAAA;KACJ;uGA3gBQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAkML,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAnMd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,iJAoBG,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EA8BhB,eAAe,CAef,EAAA,WAAA,EAAA,aAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,kEAUhB,gBAAgB,CAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EA6ChB,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAKhB,gBAAgB,CAlIzB,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,6BAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,SAAA,EAAA,4BAAA,EAAA,WAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,SAAA,EAAA,CAAC,uBAAuB,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAwKnB,aAAa,EAzPpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwET,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,imBAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA6hByD,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,YAAY,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,OAAO,CAAA,EAAA,QAAA,EAAA,SAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAtnBjF,UAAU,CAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,EAAA,UAAA,EA0FP,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAapO,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAxFpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwET,EACW,UAAA,EAAA,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACvO,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,0BAA0B;AACjC,wBAAA,+BAA+B,EAAE,UAAU;AAC3C,wBAAA,8BAA8B,EAAE,SAAS;AACzC,wBAAA,8BAA8B,EAAE,WAAW;AAC3C,wBAAA,yBAAyB,EAAE,YAAY;qBAC1C,EACU,SAAA,EAAA,CAAC,uBAAuB,CAAC,EAEnB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAAA,MAAA,EAAA,CAAA,imBAAA,CAAA,EAAA,CAAA;;0BAoMhC,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;mLA9Ld,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKiC,SAAS,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKkC,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAMI,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAEa,KAAK,EAAA,CAAA;sBAAxB,SAAS;uBAAC,OAAO,CAAA;gBAcc,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAgXrB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAd,cAAc,EAAA,YAAA,EAAA,CA/3Bd,iBAAiB,EA4WjB,QAAQ,EAvGR,UAAU,CAAA,EAAA,OAAA,EAAA,CAsnBT,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,aA33BjF,iBAAiB,EA4WjB,QAAQ,EAghBsB,YAAY,CAAA,EAAA,CAAA,CAAA;AAG1C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAJb,OAAA,EAAA,CAAA,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EACnD,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAG1C,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC;AAC3F,oBAAA,OAAO,EAAE,CAAC,iBAAiB,EAAE,QAAQ,EAAE,YAAY,CAAC;AACpD,oBAAA,YAAY,EAAE,CAAC,iBAAiB,EAAE,QAAQ,EAAE,UAAU,CAAC;AAC1D,iBAAA,CAAA;;;AC17BD;;AAEG;;;;"}