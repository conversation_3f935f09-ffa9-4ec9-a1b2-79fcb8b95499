import { Injectable } from '@angular/core';
import { ColumnState } from '../../user-services/api/users';
import { ManagerModel } from '../../user-services/api/header';


@Injectable({
  providedIn: 'root'
})
export class LocalStorageService {
  readonly TOKEN = 'token';
  readonly CREDENTIALS = 'credentials';
  readonly UserAccounts = 'UserAccounts';
  readonly UserColumnsState = 'UserColumnsState';
  readonly OnlineUserColumnsState = 'OnlineUserColumnsState';
  readonly CardColumnsState = 'CardColumnsState';
  readonly CardDetailsColumnsState = 'CardDetailsColumnsState';
  readonly DeptColumnsState = 'DeptColumnsState';
  readonly ExpensesColumnsState = 'DeptColumnsState';

  getLanguage(): string {
    return localStorage.getItem('language') || 'en';
  }
  constructor() {}
  setToken(token: string) {
    localStorage.setItem(this.TOKEN, token);
  }

  getToken(): string {
    return <string>localStorage.getItem(this.TOKEN);
  }

  removeToken() {
    localStorage.removeItem(this.TOKEN);
  }

  saveObj(obj: any, key: string): void {
    localStorage.setItem(key, JSON.stringify(obj));
  }


  getObj(key: string) {
    const user = localStorage.getItem(key);
    if (user === null) {
      return null;
    }
    return JSON.parse(String(user));
  }

  saveColumnsState(columns: ColumnState[], key: string): void {
    localStorage.setItem(key, JSON.stringify(columns));
  }

  removeByKey(key: string): void {
    localStorage.removeItem(key);
  }

  loadColumnsState(key: string): ColumnState[] {
    const savedState = localStorage.getItem(key);
    return savedState ? JSON.parse(savedState): undefined;
  }

  getCredentials(): ManagerModel {
    return this.getObj(this.CREDENTIALS);
  }

}
