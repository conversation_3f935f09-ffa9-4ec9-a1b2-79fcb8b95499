import { Observable } from "rxjs";
import { TableResponse } from "../../common-services/interfaces/table-response";
import { Debt } from "../api/debts";


export abstract class IDebtsService {
  abstract getDebtHistory(debtId: string): Observable<any>;
  abstract getDebts(requestForm: any):Observable<TableResponse<Debt>>;
  abstract getDebtsByUser(id: number, requestForm: any):Observable<TableResponse<Debt>>;
  abstract getDebt(): Observable<Debt>;
  abstract createDebt(debt: Debt): Observable<any>;
  abstract updateDebt(debt: any): Observable<any>;
  abstract deleteDebt(id: number): Observable<any>;
  abstract payDebt(id: number): Observable<any>;
  abstract payPartialDebt(id: number, amount: number): Observable<any>;
  abstract getDebtStatisticsByUser(id: number): Observable<any>;
}
