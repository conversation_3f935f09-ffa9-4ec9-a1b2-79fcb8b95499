/*!
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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