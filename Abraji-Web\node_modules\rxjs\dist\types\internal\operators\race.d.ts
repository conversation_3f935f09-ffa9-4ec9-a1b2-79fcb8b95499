import { ObservableInputTuple, OperatorFunction } from '../types';
/** @deprecated Replaced with {@link raceWith}. Will be removed in v8. */
export declare function race<T, A extends readonly unknown[]>(otherSources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;
/** @deprecated Replaced with {@link raceWith}. Will be removed in v8. */
export declare function race<T, A extends readonly unknown[]>(...otherSources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;
//# sourceMappingURL=race.d.ts.map