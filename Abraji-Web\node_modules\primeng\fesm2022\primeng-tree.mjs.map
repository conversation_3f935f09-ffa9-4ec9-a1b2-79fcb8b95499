{"version": 3, "file": "primeng-tree.mjs", "sources": ["../../src/app/components/tree/tree.ts", "../../src/app/components/tree/primeng-tree.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport {\n    AfterContentInit,\n    booleanAttribute,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    forwardRef,\n    Inject,\n    Input,\n    NgModule,\n    numberAttribute,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    Optional,\n    Output,\n    QueryList,\n    SimpleChanges,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation\n} from '@angular/core';\nimport { BlockableUI, PrimeNGConfig, PrimeTemplate, SharedModule, TranslationKeys, TreeDragDropService, TreeNode } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { RippleModule } from 'primeng/ripple';\nimport { Scroller, ScrollerModule } from 'primeng/scroller';\nimport { ScrollerOptions } from 'primeng/api';\nimport { ObjectUtils } from 'primeng/utils';\nimport { Subscription } from 'rxjs';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { MinusIcon } from 'primeng/icons/minus';\nimport { PlusIcon } from 'primeng/icons/plus';\nimport { SearchIcon } from 'primeng/icons/search';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { Nullable } from 'primeng/ts-helpers';\nimport {\n    TreeFilterEvent,\n    TreeLazyLoadEvent,\n    TreeNodeCollapseEvent,\n    TreeNodeContextMenuSelectEvent,\n    TreeNodeDropEvent,\n    TreeNodeExpandEvent,\n    TreeNodeSelectEvent,\n    TreeNodeUnSelectEvent,\n    TreeScrollEvent,\n    TreeScrollIndexChangeEvent\n} from './tree.interface';\n\n@Component({\n    selector: 'p-treeNode',\n    template: `\n        <ng-template [ngIf]=\"node\">\n            <li\n                *ngIf=\"tree.droppableNodes\"\n                class=\"p-treenode-droppoint\"\n                [attr.aria-hidden]=\"true\"\n                [ngClass]=\"{ 'p-treenode-droppoint-active': draghoverPrev }\"\n                (drop)=\"onDropPoint($event, -1)\"\n                (dragover)=\"onDropPointDragOver($event)\"\n                (dragenter)=\"onDropPointDragEnter($event, -1)\"\n                (dragleave)=\"onDropPointDragLeave($event)\"\n            ></li>\n            <li\n                *ngIf=\"!tree.horizontal\"\n                [ngClass]=\"['p-treenode', node.styleClass || '', isLeaf() ? 'p-treenode-leaf' : '']\"\n                [ngStyle]=\"{ height: itemSize + 'px' }\"\n                [style]=\"node.style\"\n                [attr.aria-label]=\"node.label\"\n                [attr.aria-checked]=\"ariaChecked\"\n                [attr.aria-setsize]=\"node.children ? node.children.length : 0\"\n                [attr.aria-selected]=\"ariaSelected\"\n                [attr.aria-expanded]=\"node.expanded\"\n                [attr.aria-posinset]=\"index + 1\"\n                [attr.aria-level]=\"level + 1\"\n                [attr.tabindex]=\"index === 0 ? 0 : -1\"\n                [attr.data-id]=\"node.key\"\n                role=\"treeitem\"\n                (keydown)=\"onKeyDown($event)\"\n            >\n                <div\n                    class=\"p-treenode-content\"\n                    [ngStyle]=\"{\n                        'padding-left': level * indentation + 'rem'\n                    }\"\n                    (click)=\"onNodeClick($event)\"\n                    (contextmenu)=\"onNodeRightClick($event)\"\n                    (touchend)=\"onNodeTouchEnd()\"\n                    (drop)=\"onDropNode($event)\"\n                    (dragover)=\"onDropNodeDragOver($event)\"\n                    (dragenter)=\"onDropNodeDragEnter($event)\"\n                    (dragleave)=\"onDropNodeDragLeave($event)\"\n                    [draggable]=\"tree.draggableNodes\"\n                    (dragstart)=\"onDragStart($event)\"\n                    (dragend)=\"onDragStop($event)\"\n                    [ngClass]=\"{ 'p-treenode-selectable': tree.selectionMode && node.selectable !== false, 'p-treenode-dragover': draghoverNode, 'p-highlight': isSelected() }\"\n                >\n                    <button type=\"button\" [attr.data-pc-section]=\"'toggler'\" class=\"p-tree-toggler p-link\" (click)=\"toggle($event)\" pRipple tabindex=\"-1\" aria-hidden=\"true\">\n                        <ng-container *ngIf=\"!tree.togglerIconTemplate\">\n                            <ng-container *ngIf=\"!node.loading\">\n                                <ChevronRightIcon *ngIf=\"!node.expanded\" [styleClass]=\"'p-tree-toggler-icon'\" />\n                                <ChevronDownIcon *ngIf=\"node.expanded\" [styleClass]=\"'p-tree-toggler-icon'\" />\n                            </ng-container>\n                            <ng-container *ngIf=\"loadingMode === 'icon' && node.loading\">\n                                <SpinnerIcon [spin]=\"true\" [styleClass]=\"'p-tree-node-toggler-icon'\" />\n                            </ng-container>\n                        </ng-container>\n                        <span *ngIf=\"tree.togglerIconTemplate\" class=\"p-tree-toggler-icon\">\n                            <ng-template *ngTemplateOutlet=\"tree.togglerIconTemplate; context: { $implicit: node.expanded }\"></ng-template>\n                        </span>\n                    </button>\n                    <div\n                        class=\"p-checkbox p-component\"\n                        [ngClass]=\"{ 'p-checkbox-disabled p-disabled': node.selectable === false, 'p-variant-filled': tree?.config.inputStyle() === 'filled' }\"\n                        *ngIf=\"tree.selectionMode == 'checkbox'\"\n                        aria-hidden=\"true\"\n                    >\n                        <div class=\"p-checkbox-box\" [ngClass]=\"{ 'p-highlight': isSelected(), 'p-indeterminate': node.partialSelected }\" role=\"checkbox\">\n                            <ng-container *ngIf=\"!tree.checkboxIconTemplate\">\n                                <CheckIcon *ngIf=\"!node.partialSelected && isSelected()\" [styleClass]=\"'p-checkbox-icon'\" />\n                                <MinusIcon *ngIf=\"node.partialSelected\" [styleClass]=\"'p-checkbox-icon'\" />\n                            </ng-container>\n                            <ng-template *ngTemplateOutlet=\"tree.checkboxIconTemplate; context: { $implicit: isSelected(), partialSelected: node.partialSelected }\"></ng-template>\n                        </div>\n                    </div>\n                    <span [class]=\"getIcon()\" *ngIf=\"node.icon || node.expandedIcon || node.collapsedIcon\"></span>\n                    <span class=\"p-treenode-label\">\n                        <span *ngIf=\"!tree.getTemplateForNode(node)\">{{ node.label }}</span>\n                        <span *ngIf=\"tree.getTemplateForNode(node)\">\n                            <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: { $implicit: node }\"></ng-container>\n                        </span>\n                    </span>\n                </div>\n                <ul\n                    class=\"p-treenode-children\"\n                    [ngStyle]=\"{\n                        display: node.expanded ? 'block' : 'none'\n                    }\"\n                    *ngIf=\"!tree.virtualScroll && node.children && node.expanded\"\n                    role=\"group\"\n                >\n                    <p-treeNode\n                        *ngFor=\"let childNode of node.children; let firstChild = first; let lastChild = last; let index = index; trackBy: tree.trackBy\"\n                        [node]=\"childNode\"\n                        [parentNode]=\"node\"\n                        [firstChild]=\"firstChild\"\n                        [lastChild]=\"lastChild\"\n                        [index]=\"index\"\n                        [itemSize]=\"itemSize\"\n                        [level]=\"level + 1\"\n                    ></p-treeNode>\n                </ul>\n            </li>\n\n            <li\n                *ngIf=\"tree.droppableNodes && lastChild\"\n                class=\"p-treenode-droppoint\"\n                [ngClass]=\"{ 'p-treenode-droppoint-active': draghoverNext }\"\n                (drop)=\"onDropPoint($event, 1)\"\n                [attr.aria-hidden]=\"true\"\n                (dragover)=\"onDropPointDragOver($event)\"\n                (dragenter)=\"onDropPointDragEnter($event, 1)\"\n                (dragleave)=\"onDropPointDragLeave($event)\"\n            ></li>\n\n            <table *ngIf=\"tree.horizontal\" [class]=\"node.styleClass\">\n                <tbody>\n                    <tr>\n                        <td class=\"p-treenode-connector\" *ngIf=\"!root\">\n                            <table class=\"p-treenode-connector-table\">\n                                <tbody>\n                                    <tr>\n                                        <td [ngClass]=\"{ 'p-treenode-connector-line': !firstChild }\"></td>\n                                    </tr>\n                                    <tr>\n                                        <td [ngClass]=\"{ 'p-treenode-connector-line': !lastChild }\"></td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </td>\n                        <td class=\"p-treenode\" [ngClass]=\"{ 'p-treenode-collapsed': !node.expanded }\">\n                            <div\n                                class=\"p-treenode-content\"\n                                tabindex=\"0\"\n                                [ngClass]=\"{ 'p-treenode-selectable': tree.selectionMode, 'p-highlight': isSelected() }\"\n                                (click)=\"onNodeClick($event)\"\n                                (contextmenu)=\"onNodeRightClick($event)\"\n                                (touchend)=\"onNodeTouchEnd()\"\n                                (keydown)=\"onNodeKeydown($event)\"\n                            >\n                                <span *ngIf=\"!isLeaf()\" [ngClass]=\"'p-tree-toggler'\" (click)=\"toggle($event)\">\n                                    <ng-container *ngIf=\"!tree.togglerIconTemplate\">\n                                        <PlusIcon *ngIf=\"!node.expanded\" [styleClass]=\"'p-tree-toggler-icon'\" [ariaLabel]=\"tree.togglerAriaLabel\" />\n                                        <MinusIcon *ngIf=\"node.expanded\" [styleClass]=\"'p-tree-toggler-icon'\" [ariaLabel]=\"tree.togglerAriaLabel\" />\n                                    </ng-container>\n                                    <span *ngIf=\"tree.togglerIconTemplate\" class=\"p-tree-toggler-icon\">\n                                        <ng-template *ngTemplateOutlet=\"tree.togglerIconTemplate; context: { $implicit: node.expanded }\"></ng-template>\n                                    </span>\n                                </span>\n                                <span [class]=\"getIcon()\" *ngIf=\"node.icon || node.expandedIcon || node.collapsedIcon\"></span>\n                                <span class=\"p-treenode-label\">\n                                    <span *ngIf=\"!tree.getTemplateForNode(node)\">{{ node.label }}</span>\n                                    <span *ngIf=\"tree.getTemplateForNode(node)\">\n                                        <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: { $implicit: node }\"></ng-container>\n                                    </span>\n                                </span>\n                            </div>\n                        </td>\n                        <td\n                            class=\"p-treenode-children-container\"\n                            *ngIf=\"node.children && node.expanded\"\n                            [ngStyle]=\"{\n                                display: node.expanded ? 'table-cell' : 'none'\n                            }\"\n                        >\n                            <div class=\"p-treenode-children\">\n                                <p-treeNode *ngFor=\"let childNode of node.children; let firstChild = first; let lastChild = last; trackBy: tree.trackBy\" [node]=\"childNode\" [firstChild]=\"firstChild\" [lastChild]=\"lastChild\"></p-treeNode>\n                            </div>\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </ng-template>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class UITreeNode implements OnInit {\n    static ICON_CLASS: string = 'p-treenode-icon ';\n\n    @Input() rowNode: any;\n\n    @Input() node: TreeNode<any> | undefined;\n\n    @Input() parentNode: TreeNode<any> | undefined;\n\n    @Input({ transform: booleanAttribute }) root: boolean | undefined;\n\n    @Input({ transform: numberAttribute }) index: number | undefined;\n\n    @Input({ transform: booleanAttribute }) firstChild: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) lastChild: boolean | undefined;\n\n    @Input({ transform: numberAttribute }) level: number | undefined;\n\n    @Input({ transform: numberAttribute }) indentation: number | undefined;\n\n    @Input({ transform: numberAttribute }) itemSize: number | undefined;\n\n    @Input() loadingMode: string;\n\n    tree: Tree;\n\n    timeout: any;\n\n    draghoverPrev: boolean | undefined;\n\n    draghoverNext: boolean | undefined;\n\n    draghoverNode: boolean | undefined;\n\n    get ariaSelected() {\n        return this.tree.selectionMode === 'single' || this.tree.selectionMode === 'multiple' ? this.isSelected() : undefined;\n    }\n\n    get ariaChecked() {\n        return this.tree.selectionMode === 'checkbox' ? this.isSelected() : undefined;\n    }\n\n    constructor(@Inject(forwardRef(() => Tree)) tree: Tree) {\n        this.tree = tree as Tree;\n    }\n\n    ngOnInit() {\n        (<TreeNode>this.node).parent = this.parentNode;\n        if (this.parentNode) {\n            this.setAllNodesTabIndexes();\n            this.tree.syncNodeOption(<TreeNode>this.node, <TreeNode<any>[]>this.tree.value, 'parent', this.tree.getNodeWithKey(<string>this.parentNode.key, <TreeNode<any>[]>this.tree.value));\n        }\n    }\n\n    getIcon() {\n        let icon: string | undefined;\n\n        if ((<TreeNode>this.node).icon) icon = (<TreeNode>this.node).icon as string;\n        else icon = (<TreeNode>this.node).expanded && (<TreeNode>this.node).children && (<TreeNode>this.node).children?.length ? (<TreeNode>this.node).expandedIcon : (<TreeNode>this.node).collapsedIcon;\n\n        return UITreeNode.ICON_CLASS + ' ' + icon;\n    }\n\n    isLeaf() {\n        return this.tree.isNodeLeaf(<TreeNode>this.node);\n    }\n\n    toggle(event: Event) {\n        if ((<TreeNode>this.node).expanded) this.collapse(event);\n        else this.expand(event);\n\n        event.stopPropagation();\n    }\n\n    expand(event: Event) {\n        (<TreeNode>this.node).expanded = true;\n        if (this.tree.virtualScroll) {\n            this.tree.updateSerializedValue();\n            this.focusVirtualNode();\n        }\n        this.tree.onNodeExpand.emit({ originalEvent: event, node: <TreeNode>this.node });\n    }\n\n    collapse(event: Event) {\n        (<TreeNode>this.node).expanded = false;\n        if (this.tree.virtualScroll) {\n            this.tree.updateSerializedValue();\n            this.focusVirtualNode();\n        }\n        this.tree.onNodeCollapse.emit({ originalEvent: event, node: <TreeNode>this.node });\n    }\n\n    onNodeClick(event: MouseEvent) {\n        this.tree.onNodeClick(event, <TreeNode>this.node);\n    }\n\n    onNodeKeydown(event: KeyboardEvent) {\n        if (event.key === 'Enter') {\n            this.tree.onNodeClick(event, <TreeNode>this.node);\n        }\n    }\n\n    onNodeTouchEnd() {\n        this.tree.onNodeTouchEnd();\n    }\n\n    onNodeRightClick(event: MouseEvent) {\n        this.tree.onNodeRightClick(event, <TreeNode>this.node);\n    }\n\n    isSelected() {\n        return this.tree.isSelected(<TreeNode>this.node);\n    }\n\n    isSameNode(event) {\n        return event.currentTarget && (event.currentTarget.isSameNode(event.target) || event.currentTarget.isSameNode(event.target.closest('[role=\"treeitem\"]')));\n    }\n\n    onDropPoint(event: DragEvent, position: number) {\n        event.preventDefault();\n        let dragNode = this.tree.dragNode;\n        let dragNodeIndex = this.tree.dragNodeIndex;\n        let dragNodeScope = this.tree.dragNodeScope;\n        let isValidDropPointIndex = this.tree.dragNodeTree === this.tree ? position === 1 || dragNodeIndex !== <number>this.index - 1 : true;\n\n        if (this.tree.allowDrop(<TreeNode>dragNode, <TreeNode>this.node, dragNodeScope) && isValidDropPointIndex) {\n            let dropParams = { ...this.createDropPointEventMetadata(<number>position) };\n\n            if (this.tree.validateDrop) {\n                this.tree.onNodeDrop.emit({\n                    originalEvent: event,\n                    dragNode: dragNode,\n                    dropNode: this.node,\n                    index: this.index,\n                    accept: () => {\n                        this.processPointDrop(dropParams);\n                    }\n                });\n            } else {\n                this.processPointDrop(dropParams);\n                this.tree.onNodeDrop.emit({\n                    originalEvent: event,\n                    dragNode: dragNode,\n                    dropNode: this.node,\n                    index: this.index\n                });\n            }\n        }\n\n        this.draghoverPrev = false;\n        this.draghoverNext = false;\n    }\n\n    processPointDrop(event: any) {\n        let newNodeList = event.dropNode.parent ? event.dropNode.parent.children : this.tree.value;\n        event.dragNodeSubNodes.splice(event.dragNodeIndex, 1);\n        let dropIndex = this.index;\n\n        if (event.position < 0) {\n            dropIndex = event.dragNodeSubNodes === newNodeList ? (event.dragNodeIndex > event.index ? event.index : event.index - 1) : event.index;\n            newNodeList.splice(dropIndex, 0, event.dragNode);\n        } else {\n            dropIndex = newNodeList.length;\n            newNodeList.push(event.dragNode);\n        }\n\n        this.tree.dragDropService.stopDrag({\n            node: event.dragNode,\n            subNodes: event.dropNode.parent ? event.dropNode.parent.children : this.tree.value,\n            index: event.dragNodeIndex\n        });\n    }\n\n    createDropPointEventMetadata(position: number) {\n        return {\n            dragNode: this.tree.dragNode,\n            dragNodeIndex: this.tree.dragNodeIndex,\n            dragNodeSubNodes: this.tree.dragNodeSubNodes,\n            dropNode: this.node,\n            index: this.index,\n            position: position\n        };\n    }\n\n    onDropPointDragOver(event: any) {\n        event.dataTransfer.dropEffect = 'move';\n        event.preventDefault();\n    }\n\n    onDropPointDragEnter(event: Event, position: number) {\n        if (this.tree.allowDrop(<TreeNode>this.tree.dragNode, <TreeNode>this.node, this.tree.dragNodeScope)) {\n            if (position < 0) this.draghoverPrev = true;\n            else this.draghoverNext = true;\n        }\n    }\n\n    onDropPointDragLeave(event: Event) {\n        this.draghoverPrev = false;\n        this.draghoverNext = false;\n    }\n\n    onDragStart(event: any) {\n        if (this.tree.draggableNodes && (<TreeNode>this.node).draggable !== false) {\n            event.dataTransfer.setData('text', 'data');\n\n            this.tree.dragDropService.startDrag({\n                tree: this,\n                node: this.node,\n                subNodes: this.node?.parent ? this.node.parent.children : this.tree.value,\n                index: this.index,\n                scope: this.tree.draggableScope\n            });\n        } else {\n            event.preventDefault();\n        }\n    }\n\n    onDragStop(event: any) {\n        this.tree.dragDropService.stopDrag({\n            node: this.node,\n            subNodes: this.node?.parent ? this.node.parent.children : this.tree.value,\n            index: this.index\n        });\n    }\n\n    onDropNodeDragOver(event: any) {\n        event.dataTransfer.dropEffect = 'move';\n        if (this.tree.droppableNodes) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n\n    onDropNode(event: any) {\n        if (this.tree.droppableNodes && this.node?.droppable !== false) {\n            let dragNode = this.tree.dragNode;\n\n            if (this.tree.allowDrop(<TreeNode>dragNode, <TreeNode>this.node, this.tree.dragNodeScope)) {\n                let dropParams = { ...this.createDropNodeEventMetadata() };\n\n                if (this.tree.validateDrop) {\n                    this.tree.onNodeDrop.emit({\n                        originalEvent: event,\n                        dragNode: dragNode,\n                        dropNode: this.node,\n                        index: this.index,\n                        accept: () => {\n                            this.processNodeDrop(dropParams);\n                        }\n                    });\n                } else {\n                    this.processNodeDrop(dropParams);\n                    this.tree.onNodeDrop.emit({\n                        originalEvent: event,\n                        dragNode: dragNode,\n                        dropNode: this.node,\n                        index: this.index\n                    });\n                }\n            }\n        }\n\n        event.preventDefault();\n        event.stopPropagation();\n        this.draghoverNode = false;\n    }\n\n    createDropNodeEventMetadata() {\n        return {\n            dragNode: this.tree.dragNode,\n            dragNodeIndex: this.tree.dragNodeIndex,\n            dragNodeSubNodes: this.tree.dragNodeSubNodes,\n            dropNode: this.node\n        };\n    }\n\n    processNodeDrop(event: any) {\n        let dragNodeIndex = event.dragNodeIndex;\n        event.dragNodeSubNodes.splice(dragNodeIndex, 1);\n\n        if (event.dropNode.children) event.dropNode.children.push(event.dragNode);\n        else event.dropNode.children = [event.dragNode];\n\n        this.tree.dragDropService.stopDrag({\n            node: event.dragNode,\n            subNodes: event.dropNode.parent ? event.dropNode.parent.children : this.tree.value,\n            index: dragNodeIndex\n        });\n    }\n\n    onDropNodeDragEnter(event: any) {\n        if (this.tree.droppableNodes && this.node?.droppable !== false && this.tree.allowDrop(<TreeNode>this.tree.dragNode, <TreeNode>this.node, this.tree.dragNodeScope)) {\n            this.draghoverNode = true;\n        }\n    }\n\n    onDropNodeDragLeave(event: any) {\n        if (this.tree.droppableNodes) {\n            let rect = event.currentTarget.getBoundingClientRect();\n            if (event.x > rect.left + rect.width || event.x < rect.left || event.y >= Math.floor(rect.top + rect.height) || event.y < rect.top) {\n                this.draghoverNode = false;\n            }\n        }\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        if (!this.isSameNode(event) || (this.tree.contextMenu && this.tree.contextMenu.containerViewChild?.nativeElement.style.display === 'block')) {\n            return;\n        }\n\n        switch (event.code) {\n            //down arrow\n            case 'ArrowDown':\n                this.onArrowDown(event);\n                break;\n\n            //up arrow\n            case 'ArrowUp':\n                this.onArrowUp(event);\n                break;\n\n            //right arrow\n            case 'ArrowRight':\n                this.onArrowRight(event);\n                break;\n\n            //left arrow\n            case 'ArrowLeft':\n                this.onArrowLeft(event);\n                break;\n\n            //enter\n            case 'Enter':\n            case 'NumpadEnter':\n                this.onEnter(event);\n                break;\n\n            //space\n            case 'Space':\n                const nodeName = event.target instanceof HTMLElement && event.target.nodeName;\n                if (!['INPUT'].includes(nodeName)) {\n                    this.onEnter(event);\n                }\n                break;\n\n            //tab\n            case 'Tab':\n                this.setAllNodesTabIndexes();\n                break;\n\n            default:\n                //no op\n                break;\n        }\n    }\n\n    onArrowUp(event: KeyboardEvent) {\n        const nodeElement = (<HTMLDivElement>event.target).getAttribute('data-pc-section') === 'toggler' ? (<HTMLDivElement>event.target).closest('[role=\"treeitem\"]') : (<HTMLDivElement>event.target).parentElement;\n\n        if (nodeElement.previousElementSibling) {\n            this.focusRowChange(nodeElement, nodeElement.previousElementSibling, this.findLastVisibleDescendant(nodeElement.previousElementSibling));\n        } else {\n            let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n            if (parentNodeElement) {\n                this.focusRowChange(nodeElement, parentNodeElement);\n            }\n        }\n\n        event.preventDefault();\n    }\n\n    onArrowDown(event: KeyboardEvent) {\n        const nodeElement = (<HTMLDivElement>event.target).getAttribute('data-pc-section') === 'toggler' ? (<HTMLDivElement>event.target).closest('[role=\"treeitem\"]') : <HTMLDivElement>event.target;\n        const listElement = nodeElement.children[1];\n\n        if (listElement && listElement.children.length > 0) {\n            this.focusRowChange(nodeElement, listElement.children[0]);\n        } else {\n            if (nodeElement.parentElement.nextElementSibling) {\n                this.focusRowChange(nodeElement, nodeElement.parentElement.nextElementSibling);\n            } else {\n                let nextSiblingAncestor = this.findNextSiblingOfAncestor(nodeElement.parentElement);\n\n                if (nextSiblingAncestor) {\n                    this.focusRowChange(nodeElement, nextSiblingAncestor);\n                }\n            }\n        }\n        event.preventDefault();\n    }\n\n    onArrowRight(event: KeyboardEvent) {\n        if (!this.node?.expanded && !this.tree.isNodeLeaf(<TreeNode>this.node)) {\n            this.expand(event);\n            (<HTMLDivElement>event.currentTarget).tabIndex = -1;\n\n            setTimeout(() => {\n                this.onArrowDown(event);\n            }, 1);\n        }\n        event.preventDefault();\n    }\n\n    onArrowLeft(event: KeyboardEvent) {\n        const nodeElement = (<HTMLDivElement>event.target).getAttribute('data-pc-section') === 'toggler' ? (<HTMLDivElement>event.target).closest('[role=\"treeitem\"]') : <HTMLDivElement>event.target;\n\n        if (this.level === 0 && !this.node?.expanded) {\n            return false;\n        }\n\n        if (this.node?.expanded) {\n            this.collapse(event);\n            return;\n        }\n\n        let parentNodeElement = this.getParentNodeElement(nodeElement.parentElement);\n\n        if (parentNodeElement) {\n            this.focusRowChange(event.currentTarget, parentNodeElement);\n        }\n\n        event.preventDefault();\n    }\n\n    isActionableElement(event) {\n        const target = event.target;\n\n        const isActionable = target instanceof HTMLElement && (target.nodeName == 'A' || target.nodeName == 'BUTTON');\n\n        return isActionable;\n    }\n\n    onEnter(event: KeyboardEvent) {\n        this.tree.onNodeClick(event, <TreeNode>this.node);\n        this.setTabIndexForSelectionMode(event, this.tree.nodeTouched);\n\n        if (!this.isActionableElement(event)) {\n            event.preventDefault();\n        }\n    }\n\n    setAllNodesTabIndexes() {\n        const nodes = DomHandler.find(this.tree.el.nativeElement, '.p-treenode');\n\n        const hasSelectedNode = [...nodes].some((node) => node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true');\n\n        [...nodes].forEach((node) => {\n            node.tabIndex = -1;\n        });\n\n        if (hasSelectedNode) {\n            const selectedNodes = [...nodes].filter((node) => node.getAttribute('aria-selected') === 'true' || node.getAttribute('aria-checked') === 'true');\n\n            selectedNodes[0].tabIndex = 0;\n\n            return;\n        }\n\n        [...nodes][0].tabIndex = 0;\n    }\n\n    setTabIndexForSelectionMode(event, nodeTouched) {\n        if (this.tree.selectionMode !== null) {\n            const elements = [...DomHandler.find(this.tree.el.nativeElement, '.p-treenode')];\n\n            event.currentTarget.tabIndex = nodeTouched === false ? -1 : 0;\n\n            if (elements.every((element) => element.tabIndex === -1)) {\n                elements[0].tabIndex = 0;\n            }\n        }\n    }\n\n    findNextSiblingOfAncestor(nodeElement: any): any {\n        let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n        if (parentNodeElement) {\n            if (parentNodeElement.nextElementSibling) return parentNodeElement.nextElementSibling;\n            else return this.findNextSiblingOfAncestor(parentNodeElement);\n        } else {\n            return null;\n        }\n    }\n\n    findLastVisibleDescendant(nodeElement: any): any {\n        const listElement = <HTMLElement>Array.from(nodeElement.children).find((el) => DomHandler.hasClass(el, 'p-treenode'));\n        const childrenListElement = listElement.children[1];\n        if (childrenListElement && childrenListElement.children.length > 0) {\n            const lastChildElement = childrenListElement.children[childrenListElement.children.length - 1];\n\n            return this.findLastVisibleDescendant(lastChildElement);\n        } else {\n            return nodeElement;\n        }\n    }\n\n    getParentNodeElement(nodeElement: HTMLElement | Element) {\n        const parentNodeElement = nodeElement.parentElement?.parentElement?.parentElement;\n\n        return parentNodeElement?.tagName === 'P-TREENODE' ? parentNodeElement : null;\n    }\n\n    focusNode(element: any) {\n        if (this.tree.droppableNodes) (element.children[1] as HTMLElement).focus();\n        else (element.children[0] as HTMLElement).focus();\n    }\n\n    focusRowChange(firstFocusableRow, currentFocusedRow, lastVisibleDescendant?) {\n        firstFocusableRow.tabIndex = '-1';\n        currentFocusedRow.children[0].tabIndex = '0';\n\n        this.focusNode(lastVisibleDescendant || currentFocusedRow);\n    }\n\n    focusVirtualNode() {\n        this.timeout = setTimeout(() => {\n            let node = DomHandler.findSingle(document.body, `[data-id=\"${<TreeNode>this.node?.key ?? <TreeNode>this.node?.data}\"]`);\n            DomHandler.focus(node);\n        }, 1);\n    }\n}\n/**\n * Tree is used to display hierarchical data.\n * @group Components\n */\n@Component({\n    selector: 'p-tree',\n    template: `\n        <div\n            [ngClass]=\"{ 'p-tree p-component': true, 'p-tree-selectable': selectionMode, 'p-treenode-dragover': dragHover, 'p-tree-loading': loading, 'p-tree-flex-scrollable': scrollHeight === 'flex' }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            *ngIf=\"!horizontal\"\n            (drop)=\"onDrop($event)\"\n            (dragover)=\"onDragOver($event)\"\n            (dragenter)=\"onDragEnter()\"\n            (dragleave)=\"onDragLeave($event)\"\n        >\n            <div class=\"p-tree-loading-overlay p-component-overlay\" *ngIf=\"loading && loadingMode === 'mask'\">\n                <i *ngIf=\"loadingIcon\" [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n                <ng-container *ngIf=\"!loadingIcon\">\n                    <SpinnerIcon *ngIf=\"!loadingIconTemplate\" [spin]=\"true\" [styleClass]=\"'p-tree-loading-icon'\" />\n                    <span *ngIf=\"loadingIconTemplate\" class=\"p-tree-loading-icon\">\n                        <ng-template *ngTemplateOutlet=\"loadingIconTemplate\"></ng-template>\n                    </span>\n                </ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            <div *ngIf=\"filter\" class=\"p-tree-filter-container\">\n                <input #filter type=\"search\" autocomplete=\"off\" class=\"p-tree-filter p-inputtext p-component\" [attr.placeholder]=\"filterPlaceholder\" (keydown.enter)=\"$event.preventDefault()\" (input)=\"_filter($event.target.value)\" />\n                <SearchIcon *ngIf=\"!filterIconTemplate\" [styleClass]=\"'p-tree-filter-icon'\" />\n                <span *ngIf=\"filterIconTemplate\" class=\"p-tree-filter-icon\">\n                    <ng-template *ngTemplateOutlet=\"filterIconTemplate\"></ng-template>\n                </span>\n            </div>\n            <ng-container *ngIf=\"getRootNode()?.length\">\n                <p-scroller\n                    #scroller\n                    *ngIf=\"virtualScroll\"\n                    [items]=\"serializedValue\"\n                    [tabindex]=\"-1\"\n                    styleClass=\"p-tree-wrapper\"\n                    [style]=\"{ height: scrollHeight !== 'flex' ? scrollHeight : undefined }\"\n                    [scrollHeight]=\"scrollHeight !== 'flex' ? undefined : '100%'\"\n                    [itemSize]=\"virtualScrollItemSize || _virtualNodeHeight\"\n                    [lazy]=\"lazy\"\n                    (onScroll)=\"onScroll.emit($event)\"\n                    (onScrollIndexChange)=\"onScrollIndexChange.emit($event)\"\n                    (onLazyLoad)=\"onLazyLoad.emit($event)\"\n                    [options]=\"virtualScrollOptions\"\n                >\n                    <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                        <ul *ngIf=\"items\" class=\"p-tree-container\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                            <p-treeNode\n                                #treeNode\n                                *ngFor=\"let rowNode of items; let firstChild = first; let lastChild = last; let index = index; trackBy: trackBy\"\n                                [level]=\"rowNode.level\"\n                                [rowNode]=\"rowNode\"\n                                [node]=\"rowNode.node\"\n                                [parentNode]=\"rowNode.parent\"\n                                [firstChild]=\"firstChild\"\n                                [lastChild]=\"lastChild\"\n                                [index]=\"getIndex(scrollerOptions, index)\"\n                                [itemSize]=\"scrollerOptions.itemSize\"\n                                [indentation]=\"indentation\"\n                                [loadingMode]=\"loadingMode\"\n                            ></p-treeNode>\n                        </ul>\n                    </ng-template>\n                    <ng-container *ngIf=\"loaderTemplate\">\n                        <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: scrollerOptions }\"></ng-container>\n                        </ng-template>\n                    </ng-container>\n                </p-scroller>\n                <ng-container *ngIf=\"!virtualScroll\">\n                    <div #wrapper class=\"p-tree-wrapper\" [ngStyle]=\"{ 'max-height': scrollHeight }\">\n                        <ul class=\"p-tree-container\" *ngIf=\"getRootNode()\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                            <p-treeNode\n                                *ngFor=\"let node of getRootNode(); let firstChild = first; let lastChild = last; let index = index; trackBy: trackBy\"\n                                [node]=\"node\"\n                                [firstChild]=\"firstChild\"\n                                [lastChild]=\"lastChild\"\n                                [index]=\"index\"\n                                [level]=\"0\"\n                                [loadingMode]=\"loadingMode\"\n                            ></p-treeNode>\n                        </ul>\n                    </div>\n                </ng-container>\n            </ng-container>\n\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate; else emptyFilter\">\n                    {{ emptyMessageLabel }}\n                </ng-container>\n                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n        </div>\n        <div [ngClass]=\"{ 'p-tree p-tree-horizontal p-component': true, 'p-tree-selectable': selectionMode }\" [ngStyle]=\"style\" [class]=\"styleClass\" *ngIf=\"horizontal\">\n            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            <div class=\"p-tree-loading-mask p-component-overlay\" *ngIf=\"loading\">\n                <i *ngIf=\"loadingIcon\" [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n                <ng-container *ngIf=\"!loadingIcon\">\n                    <SpinnerIcon *ngIf=\"!loadingIconTemplate\" [spin]=\"true\" [styleClass]=\"'p-tree-loading-icon'\" />\n                    <span *ngIf=\"loadingIconTemplate\" class=\"p-tree-loading-icon\">\n                        <ng-template *ngTemplateOutlet=\"loadingIconTemplate\"></ng-template>\n                    </span>\n                </ng-container>\n            </div>\n            <table *ngIf=\"value && value[0]\">\n                <p-treeNode [node]=\"value[0]\" [root]=\"true\"></p-treeNode>\n            </table>\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate; else emptyFilter\">\n                    {{ emptyMessageLabel }}\n                </ng-container>\n                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.Default,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./tree.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Tree implements OnInit, AfterContentInit, OnChanges, OnDestroy, BlockableUI {\n    /**\n     * An array of treenodes.\n     * @group Props\n     */\n    @Input() value: TreeNode<any> | TreeNode<any>[] | any[] | any;\n    /**\n     * Defines the selection mode.\n     * @group Props\n     */\n    @Input() selectionMode: 'single' | 'multiple' | 'checkbox' | null | undefined;\n    /**\n     * Loading mode display.\n     * @group Props\n     */\n    @Input() loadingMode: 'mask' | 'icon' = 'mask';\n    /**\n     * A single treenode instance or an array to refer to the selections.\n     * @group Props\n     */\n    @Input() selection: any;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Context menu instance.\n     * @group Props\n     */\n    @Input() contextMenu: any;\n    /**\n     * Defines the orientation of the tree, valid values are 'vertical' and 'horizontal'.\n     * @group Props\n     */\n    @Input() layout: string = 'vertical';\n    /**\n     * Scope of the draggable nodes to match a droppableScope.\n     * @group Props\n     */\n    @Input() draggableScope: any;\n    /**\n     * Scope of the droppable nodes to match a draggableScope.\n     * @group Props\n     */\n    @Input() droppableScope: any;\n    /**\n     * Whether the nodes are draggable.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) draggableNodes: boolean | undefined;\n    /**\n     * Whether the nodes are droppable.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) droppableNodes: boolean | undefined;\n    /**\n     * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) metaKeySelection: boolean = false;\n    /**\n     * Whether checkbox selections propagate to ancestor nodes.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) propagateSelectionUp: boolean = true;\n    /**\n     * Whether checkbox selections propagate to descendant nodes.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) propagateSelectionDown: boolean = true;\n    /**\n     * Displays a loader to indicate data load is in progress.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) loading: boolean | undefined;\n    /**\n     * The icon to show while indicating data load is in progress.\n     * @group Props\n     */\n    @Input() loadingIcon: string | undefined;\n    /**\n     * Text to display when there is no data.\n     * @group Props\n     */\n    @Input() emptyMessage: string = '';\n    /**\n     * Used to define a string that labels the tree.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Defines a string that labels the toggler icon for accessibility.\n     * @group Props\n     */\n    @Input() togglerAriaLabel: string | undefined;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * When enabled, drop can be accepted or rejected based on condition defined at onNodeDrop.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) validateDrop: boolean | undefined;\n    /**\n     * When specified, displays an input field to filter the items.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) filter: boolean | undefined;\n    /**\n     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n     * @group Props\n     */\n    @Input() filterBy: string = 'label';\n    /**\n     * Mode for filtering valid values are \"lenient\" and \"strict\". Default is lenient.\n     * @group Props\n     */\n    @Input() filterMode: string = 'lenient';\n    /**\n     * Placeholder text to show when filter input is empty.\n     * @group Props\n     */\n    @Input() filterPlaceholder: string | undefined;\n    /**\n     * Values after the tree nodes are filtered.\n     * @group Props\n     */\n    @Input() filteredNodes: TreeNode<any>[] | undefined | null;\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() filterLocale: string | undefined;\n    /**\n     * Height of the scrollable viewport.\n     * @group Props\n     */\n    @Input() scrollHeight: string | undefined;\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazy: boolean = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) virtualScroll: boolean | undefined;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) virtualScrollItemSize: number | undefined;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() virtualScrollOptions: ScrollerOptions | undefined;\n    /**\n     * Indentation factor for spacing of the nested node when virtual scrolling is enabled.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) indentation: number = 1.5;\n    /**\n     * Custom templates of the component.\n     * @group Props\n     */\n    @Input() _templateMap: any;\n    /**\n     * Function to optimize the node list rendering, default algorithm checks for object identity.\n     * @group Props\n     */\n    @Input() trackBy: Function = (index: number, item: any) => item;\n    /**\n     * Height of the node.\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    _virtualNodeHeight: number | undefined;\n    @Input() get virtualNodeHeight(): number | undefined {\n        return this._virtualNodeHeight;\n    }\n    set virtualNodeHeight(val: number | undefined) {\n        this._virtualNodeHeight = val;\n        console.warn('The virtualNodeHeight property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    /**\n     * Callback to invoke on selection change.\n     * @param {(TreeNode<any> | TreeNode<any>[] | null)} event - Custom selection change event.\n     * @group Emits\n     */\n    @Output() selectionChange: EventEmitter<TreeNode<any> | TreeNode<any>[] | null> = new EventEmitter<TreeNode<any> | TreeNode<any>[] | null>();\n    /**\n     * Callback to invoke when a node is selected.\n     * @param {TreeNodeSelectEvent} event - Node select event.\n     * @group Emits\n     */\n    @Output() onNodeSelect: EventEmitter<TreeNodeSelectEvent> = new EventEmitter<TreeNodeSelectEvent>();\n    /**\n     * Callback to invoke when a node is unselected.\n     * @param {TreeNodeUnSelectEvent} event - Node unselect event.\n     * @group Emits\n     */\n    @Output() onNodeUnselect: EventEmitter<TreeNodeUnSelectEvent> = new EventEmitter<TreeNodeUnSelectEvent>();\n    /**\n     * Callback to invoke when a node is expanded.\n     * @param {TreeNodeExpandEvent} event - Node expand event.\n     * @group Emits\n     */\n    @Output() onNodeExpand: EventEmitter<TreeNodeExpandEvent> = new EventEmitter<TreeNodeExpandEvent>();\n    /**\n     * Callback to invoke when a node is collapsed.\n     * @param {TreeNodeCollapseEvent} event - Node collapse event.\n     * @group Emits\n     */\n    @Output() onNodeCollapse: EventEmitter<TreeNodeCollapseEvent> = new EventEmitter<TreeNodeCollapseEvent>();\n    /**\n     * Callback to invoke when a node is selected with right click.\n     * @param {onNodeContextMenuSelect} event - Node context menu select event.\n     * @group Emits\n     */\n    @Output() onNodeContextMenuSelect: EventEmitter<TreeNodeContextMenuSelectEvent> = new EventEmitter<TreeNodeContextMenuSelectEvent>();\n    /**\n     * Callback to invoke when a node is dropped.\n     * @param {TreeNodeDropEvent} event - Node drop event.\n     * @group Emits\n     */\n    @Output() onNodeDrop: EventEmitter<TreeNodeDropEvent> = new EventEmitter<TreeNodeDropEvent>();\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {TreeLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    @Output() onLazyLoad: EventEmitter<TreeLazyLoadEvent> = new EventEmitter<TreeLazyLoadEvent>();\n    /**\n     * Callback to invoke in virtual scroll mode when scroll position changes.\n     * @param {TreeScrollEvent} event - Custom scroll event.\n     * @group Emits\n     */\n    @Output() onScroll: EventEmitter<TreeScrollEvent> = new EventEmitter<TreeScrollEvent>();\n    /**\n     * Callback to invoke in virtual scroll mode when scroll position and item's range in view changes.\n     * @param {TreeScrollIndexChangeEvent} event - Scroll index change event.\n     * @group Emits\n     */\n    @Output() onScrollIndexChange: EventEmitter<TreeScrollIndexChangeEvent> = new EventEmitter<TreeScrollIndexChangeEvent>();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {TreeFilterEvent} event - Custom filter event.\n     * @group Emits\n     */\n    @Output() onFilter: EventEmitter<TreeFilterEvent> = new EventEmitter<TreeFilterEvent>();\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<any>>;\n\n    @ViewChild('filter') filterViewChild: Nullable<ElementRef>;\n\n    @ViewChild('scroller') scroller: Nullable<Scroller>;\n\n    @ViewChild('wrapper') wrapperViewChild: Nullable<ElementRef>;\n\n    serializedValue: Nullable<TreeNode<any>[]>;\n\n    headerTemplate: Nullable<TemplateRef<any>>;\n\n    footerTemplate: Nullable<TemplateRef<any>>;\n\n    loaderTemplate: Nullable<TemplateRef<any>>;\n\n    emptyMessageTemplate: Nullable<TemplateRef<any>>;\n\n    togglerIconTemplate: Nullable<TemplateRef<any>>;\n\n    checkboxIconTemplate: Nullable<TemplateRef<any>>;\n\n    loadingIconTemplate: Nullable<TemplateRef<any>>;\n\n    filterIconTemplate: Nullable<TemplateRef<any>>;\n\n    public nodeTouched: boolean | undefined | null;\n\n    public dragNodeTree: Tree | undefined | null;\n\n    public dragNode: TreeNode<any> | undefined | null;\n\n    public dragNodeSubNodes: TreeNode<any>[] | undefined | null;\n\n    public dragNodeIndex: number | undefined | null;\n\n    public dragNodeScope: any;\n\n    public dragHover: boolean | undefined | null;\n\n    public dragStartSubscription: Subscription | undefined | null;\n\n    public dragStopSubscription: Subscription | undefined | null;\n\n    constructor(public el: ElementRef, @Optional() public dragDropService: TreeDragDropService, public config: PrimeNGConfig, private cd: ChangeDetectorRef) {}\n\n    ngOnInit() {\n        if (this.droppableNodes) {\n            this.dragStartSubscription = this.dragDropService.dragStart$.subscribe((event) => {\n                this.dragNodeTree = event.tree;\n                this.dragNode = event.node;\n                this.dragNodeSubNodes = event.subNodes;\n                this.dragNodeIndex = event.index;\n                this.dragNodeScope = event.scope;\n            });\n\n            this.dragStopSubscription = this.dragDropService.dragStop$.subscribe((event) => {\n                this.dragNodeTree = null;\n                this.dragNode = null;\n                this.dragNodeSubNodes = null;\n                this.dragNodeIndex = null;\n                this.dragNodeScope = null;\n                this.dragHover = false;\n            });\n        }\n    }\n\n    ngOnChanges(simpleChange: SimpleChanges) {\n        if (simpleChange.value) {\n            this.updateSerializedValue();\n            if (this.hasFilterActive()) {\n                this._filter(this.filterViewChild.nativeElement.value);\n            }\n        }\n    }\n\n    get horizontal(): boolean {\n        return this.layout == 'horizontal';\n    }\n\n    get emptyMessageLabel(): string {\n        return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n\n    ngAfterContentInit() {\n        if ((this.templates as QueryList<PrimeTemplate>).length) {\n            this._templateMap = {};\n        }\n\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyMessageTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n\n                case 'togglericon':\n                    this.togglerIconTemplate = item.template;\n                    break;\n\n                case 'checkboxicon':\n                    this.checkboxIconTemplate = item.template;\n                    break;\n\n                case 'loadingicon':\n                    this.loadingIconTemplate = item.template;\n                    break;\n\n                case 'filtericon':\n                    this.filterIconTemplate = item.template;\n                    break;\n\n                default:\n                    this._templateMap[<any>item.name] = item.template;\n                    break;\n            }\n        });\n    }\n\n    updateSerializedValue() {\n        this.serializedValue = [];\n        this.serializeNodes(null, this.getRootNode(), 0, true);\n    }\n\n    serializeNodes(parent: TreeNode<any> | null, nodes: TreeNode<any>[] | any, level: number, visible: boolean) {\n        if (nodes && nodes.length) {\n            for (let node of nodes) {\n                node.parent = parent;\n                const rowNode = {\n                    node: node,\n                    parent: parent,\n                    level: level,\n                    visible: visible && (parent ? parent.expanded : true)\n                };\n                (this.serializedValue as TreeNode<any>[]).push(<TreeNode>rowNode);\n\n                if (rowNode.visible && node.expanded) {\n                    this.serializeNodes(node, node.children, level + 1, rowNode.visible);\n                }\n            }\n        }\n    }\n\n    onNodeClick(event: Event, node: TreeNode) {\n        let eventTarget = <Element>event.target;\n        if (DomHandler.hasClass(eventTarget, 'p-tree-toggler') || DomHandler.hasClass(eventTarget, 'p-tree-toggler-icon')) {\n            return;\n        } else if (this.selectionMode) {\n            if (node.selectable === false) {\n                node.style = '--p-focus-ring-color: none;';\n                return;\n            } else {\n                node.style = '--p-focus-ring-color: var(--primary-color)';\n            }\n\n            if (this.hasFilteredNodes()) {\n                node = this.getNodeWithKey(<string>node.key, <TreeNode<any>[]>this.filteredNodes) as TreeNode;\n                if (!node) {\n                    return;\n                }\n            }\n\n            let index = this.findIndexInSelection(node);\n            let selected = index >= 0;\n\n            if (this.isCheckboxSelectionMode()) {\n                if (selected) {\n                    if (this.propagateSelectionDown) this.propagateDown(node, false);\n                    else this.selection = this.selection.filter((val: TreeNode, i: number) => i != index);\n\n                    if (this.propagateSelectionUp && node.parent) {\n                        this.propagateUp(node.parent, false);\n                    }\n\n                    this.selectionChange.emit(this.selection);\n                    this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                } else {\n                    if (this.propagateSelectionDown) this.propagateDown(node, true);\n                    else this.selection = [...(this.selection || []), node];\n\n                    if (this.propagateSelectionUp && node.parent) {\n                        this.propagateUp(node.parent, true);\n                    }\n\n                    this.selectionChange.emit(this.selection);\n                    this.onNodeSelect.emit({ originalEvent: event, node: node });\n                }\n            } else {\n                let metaSelection = this.nodeTouched ? false : this.metaKeySelection;\n\n                if (metaSelection) {\n                    let metaKey = (<KeyboardEvent>event).metaKey || (<KeyboardEvent>event).ctrlKey;\n\n                    if (selected && metaKey) {\n                        if (this.isSingleSelectionMode()) {\n                            this.selectionChange.emit(null);\n                        } else {\n                            this.selection = this.selection.filter((val: TreeNode, i: number) => i != index);\n                            this.selectionChange.emit(this.selection);\n                        }\n\n                        this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                    } else {\n                        if (this.isSingleSelectionMode()) {\n                            this.selectionChange.emit(<TreeNode>node);\n                        } else if (this.isMultipleSelectionMode()) {\n                            this.selection = !metaKey ? [] : this.selection || [];\n                            this.selection = [...this.selection, node];\n                            this.selectionChange.emit(this.selection);\n                        }\n\n                        this.onNodeSelect.emit({ originalEvent: event, node: node });\n                    }\n                } else {\n                    if (this.isSingleSelectionMode()) {\n                        if (selected) {\n                            this.selection = null;\n                            this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                        } else {\n                            this.selection = node;\n                            setTimeout(() => {\n                                this.onNodeSelect.emit({ originalEvent: event, node: node });\n                            });\n                        }\n                    } else {\n                        if (selected) {\n                            this.selection = this.selection.filter((val: TreeNode, i: number) => i != index);\n                            this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                        } else {\n                            this.selection = [...(this.selection || []), node];\n                            setTimeout(() => {\n                                this.onNodeSelect.emit({ originalEvent: event, node: node });\n                            });\n                        }\n                    }\n\n                    this.selectionChange.emit(this.selection);\n                }\n            }\n        }\n\n        this.nodeTouched = false;\n    }\n\n    onNodeTouchEnd() {\n        this.nodeTouched = true;\n    }\n\n    onNodeRightClick(event: MouseEvent, node: TreeNode<any>) {\n        if (this.contextMenu) {\n            let eventTarget = <Element>event.target;\n            let className = eventTarget.getAttribute('class');\n\n            if (className && className.includes('p-tree-toggler')) {\n                return;\n            } else {\n                let index = this.findIndexInSelection(node);\n                let selected = index >= 0;\n\n                if (!selected) {\n                    if (this.isSingleSelectionMode()) this.selectionChange.emit(node);\n                    else this.selectionChange.emit([node]);\n                }\n\n                this.contextMenu.show(event);\n                this.onNodeContextMenuSelect.emit({ originalEvent: event, node: node });\n            }\n        }\n    }\n\n    findIndexInSelection(node: TreeNode) {\n        let index: number = -1;\n        if (this.selectionMode && this.selection) {\n            if (this.isSingleSelectionMode()) {\n                let areNodesEqual = (this.selection.key && this.selection.key === node.key) || this.selection == node;\n                index = areNodesEqual ? 0 : -1;\n            } else {\n                for (let i = 0; i < this.selection.length; i++) {\n                    let selectedNode = this.selection[i];\n                    let areNodesEqual = (selectedNode.key && selectedNode.key === node.key) || selectedNode == node;\n                    if (areNodesEqual) {\n                        index = i;\n                        break;\n                    }\n                }\n            }\n        }\n\n        return index;\n    }\n\n    syncNodeOption(node: TreeNode, parentNodes: TreeNode<any>[], option: any, value?: any) {\n        // to synchronize the node option between the filtered nodes and the original nodes(this.value)\n        const _node = this.hasFilteredNodes() ? this.getNodeWithKey(<string>node.key, parentNodes) : null;\n        if (_node) {\n            (<any>_node)[option] = value || (<any>node)[option];\n        }\n    }\n\n    hasFilteredNodes() {\n        return this.filter && this.filteredNodes && this.filteredNodes.length;\n    }\n\n    hasFilterActive() {\n        return this.filter && this.filterViewChild?.nativeElement?.value.length > 0;\n    }\n\n    getNodeWithKey(key: string, nodes: TreeNode<any>[]): TreeNode<any> | undefined {\n        for (let node of nodes) {\n            if (node.key === key) {\n                return node;\n            }\n\n            if (node.children) {\n                let matchedNode = this.getNodeWithKey(key, node.children);\n                if (matchedNode) {\n                    return matchedNode;\n                }\n            }\n        }\n    }\n\n    propagateUp(node: TreeNode, select: boolean) {\n        if (node.children && node.children.length) {\n            let selectedCount: number = 0;\n            let childPartialSelected: boolean = false;\n            for (let child of node.children) {\n                if (this.isSelected(child)) {\n                    selectedCount++;\n                } else if (child.partialSelected) {\n                    childPartialSelected = true;\n                }\n            }\n\n            if (select && selectedCount == node.children.length) {\n                this.selection = [...(this.selection || []), node];\n                node.partialSelected = false;\n            } else {\n                if (!select) {\n                    let index = this.findIndexInSelection(node);\n                    if (index >= 0) {\n                        this.selection = this.selection.filter((val: TreeNode, i: number) => i != index);\n                    }\n                }\n\n                if (childPartialSelected || (selectedCount > 0 && selectedCount != node.children.length)) node.partialSelected = true;\n                else node.partialSelected = false;\n            }\n\n            this.syncNodeOption(node, <TreeNode<any>[]>this.filteredNodes, 'partialSelected');\n        }\n\n        let parent = node.parent;\n        if (parent) {\n            this.propagateUp(parent, select);\n        }\n    }\n\n    propagateDown(node: TreeNode, select: boolean) {\n        let index = this.findIndexInSelection(node);\n\n        if (select && index == -1) {\n            this.selection = [...(this.selection || []), node];\n        } else if (!select && index > -1) {\n            this.selection = this.selection.filter((val: TreeNode, i: number) => i != index);\n        }\n\n        node.partialSelected = false;\n\n        this.syncNodeOption(node, <TreeNode<any>[]>this.filteredNodes, 'partialSelected');\n\n        if (node.children && node.children.length) {\n            for (let child of node.children) {\n                this.propagateDown(child, select);\n            }\n        }\n    }\n\n    isSelected(node: TreeNode) {\n        return this.findIndexInSelection(node) != -1;\n    }\n\n    isSingleSelectionMode() {\n        return this.selectionMode && this.selectionMode == 'single';\n    }\n\n    isMultipleSelectionMode() {\n        return this.selectionMode && this.selectionMode == 'multiple';\n    }\n\n    isCheckboxSelectionMode() {\n        return this.selectionMode && this.selectionMode == 'checkbox';\n    }\n\n    isNodeLeaf(node: TreeNode): boolean {\n        return node.leaf == false ? false : !(node.children && node.children.length);\n    }\n\n    getRootNode() {\n        return this.filteredNodes ? this.filteredNodes : this.value;\n    }\n\n    getTemplateForNode(node: TreeNode): TemplateRef<any> | null {\n        if (this._templateMap) return node.type ? this._templateMap[node.type] : this._templateMap['default'];\n        else return null;\n    }\n\n    onDragOver(event: DragEvent) {\n        if (this.droppableNodes && (!this.value || (<any>this.value).length === 0)) {\n            (<any>event).dataTransfer.dropEffect = 'move';\n            event.preventDefault();\n        }\n    }\n\n    onDrop(event: DragEvent) {\n        if (this.droppableNodes && (!this.value || (<any>this.value).length === 0)) {\n            event.preventDefault();\n            let dragNode = this.dragNode as TreeNode;\n\n            if (this.allowDrop(dragNode, null, this.dragNodeScope)) {\n                let dragNodeIndex = <number>this.dragNodeIndex;\n                this.value = this.value || [];\n\n                if (this.validateDrop) {\n                    this.onNodeDrop.emit({\n                        originalEvent: event,\n                        dragNode: dragNode,\n                        dropNode: null,\n                        index: dragNodeIndex,\n                        accept: () => {\n                            this.processTreeDrop(dragNode, dragNodeIndex);\n                        }\n                    });\n                } else {\n                    this.onNodeDrop.emit({\n                        originalEvent: event,\n                        dragNode: dragNode,\n                        dropNode: null,\n                        index: dragNodeIndex\n                    });\n\n                    this.processTreeDrop(dragNode, dragNodeIndex);\n                }\n            }\n        }\n    }\n\n    processTreeDrop(dragNode: TreeNode, dragNodeIndex: number) {\n        (<TreeNode<any>[]>this.dragNodeSubNodes).splice(dragNodeIndex, 1);\n        (this.value as TreeNode<any>[]).push(dragNode);\n        this.dragDropService.stopDrag({\n            node: dragNode\n        });\n    }\n\n    onDragEnter() {\n        if (this.droppableNodes && this.allowDrop(<TreeNode>this.dragNode, null, this.dragNodeScope)) {\n            this.dragHover = true;\n        }\n    }\n\n    onDragLeave(event: DragEvent) {\n        if (this.droppableNodes) {\n            let rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\n            if (event.x > rect.left + rect.width || event.x < rect.left || event.y > rect.top + rect.height || event.y < rect.top) {\n                this.dragHover = false;\n            }\n        }\n    }\n\n    allowDrop(dragNode: TreeNode, dropNode: TreeNode<any> | null, dragNodeScope: any): boolean {\n        if (!dragNode) {\n            //prevent random html elements to be dragged\n            return false;\n        } else if (this.isValidDragScope(dragNodeScope)) {\n            let allow: boolean = true;\n            if (dropNode) {\n                if (dragNode === dropNode) {\n                    allow = false;\n                } else {\n                    let parent = dropNode.parent;\n                    while (parent != null) {\n                        if (parent === dragNode) {\n                            allow = false;\n                            break;\n                        }\n                        parent = parent.parent;\n                    }\n                }\n            }\n\n            return allow;\n        } else {\n            return false;\n        }\n    }\n\n    isValidDragScope(dragScope: any): boolean {\n        let dropScope = this.droppableScope;\n\n        if (dropScope) {\n            if (typeof dropScope === 'string') {\n                if (typeof dragScope === 'string') return dropScope === dragScope;\n                else if (Array.isArray(dragScope)) return (<Array<any>>dragScope).indexOf(dropScope) != -1;\n            } else if (Array.isArray(dropScope)) {\n                if (typeof dragScope === 'string') {\n                    return (<Array<any>>dropScope).indexOf(dragScope) != -1;\n                } else if (Array.isArray(dragScope)) {\n                    for (let s of dropScope) {\n                        for (let ds of dragScope) {\n                            if (s === ds) {\n                                return true;\n                            }\n                        }\n                    }\n                }\n            }\n            return false;\n        } else {\n            return true;\n        }\n    }\n\n    public _filter(value: string) {\n        let filterValue = value;\n        if (filterValue === '') {\n            this.filteredNodes = null;\n        } else {\n            this.filteredNodes = [];\n            const searchFields: string[] = this.filterBy.split(',');\n            const filterText = ObjectUtils.removeAccents(filterValue).toLocaleLowerCase(this.filterLocale);\n            const isStrictMode = this.filterMode === 'strict';\n            for (let node of <TreeNode<any>[]>this.value) {\n                let copyNode = { ...node };\n                let paramsWithoutNode = { searchFields, filterText, isStrictMode };\n                if (\n                    (isStrictMode && (this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode))) ||\n                    (!isStrictMode && (this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode)))\n                ) {\n                    this.filteredNodes.push(copyNode);\n                }\n            }\n        }\n\n        this.updateSerializedValue();\n        this.onFilter.emit({\n            filter: filterValue,\n            filteredValue: this.filteredNodes\n        });\n    }\n\n    /**\n     * Resets filter.\n     * @group Method\n     */\n    public resetFilter() {\n        this.filteredNodes = null;\n\n        if (this.filterViewChild && this.filterViewChild.nativeElement) {\n            this.filterViewChild.nativeElement.value = '';\n        }\n    }\n    /**\n     * Scrolls to virtual index.\n     * @param {number} number - Index to be scrolled.\n     * @group Method\n     */\n    public scrollToVirtualIndex(index: number) {\n        this.virtualScroll && this.scroller?.scrollToIndex(index);\n    }\n    /**\n     * Scrolls to virtual index.\n     * @param {ScrollToOptions} options - Scroll options.\n     * @group Method\n     */\n    public scrollTo(options: any) {\n        if (this.virtualScroll) {\n            this.scroller?.scrollTo(options);\n        } else if (this.wrapperViewChild && this.wrapperViewChild.nativeElement) {\n            if (this.wrapperViewChild.nativeElement.scrollTo) {\n                this.wrapperViewChild.nativeElement.scrollTo(options);\n            } else {\n                this.wrapperViewChild.nativeElement.scrollLeft = options.left;\n                this.wrapperViewChild.nativeElement.scrollTop = options.top;\n            }\n        }\n    }\n\n    findFilteredNodes(node: TreeNode, paramsWithoutNode: any) {\n        if (node) {\n            let matched = false;\n            if (node.children) {\n                let childNodes = [...node.children];\n                node.children = [];\n                for (let childNode of childNodes) {\n                    let copyChildNode = { ...childNode };\n                    if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n                        matched = true;\n                        node.children.push(copyChildNode);\n                    }\n                }\n            }\n\n            if (matched) {\n                node.expanded = true;\n                return true;\n            }\n        }\n    }\n\n    isFilterMatched(node: TreeNode, params: any) {\n        let { searchFields, filterText, isStrictMode } = params;\n        let matched = false;\n        for (let field of searchFields) {\n            let fieldValue = ObjectUtils.removeAccents(String(ObjectUtils.resolveFieldData(node, field))).toLocaleLowerCase(this.filterLocale);\n            if (fieldValue.indexOf(filterText) > -1) {\n                matched = true;\n            }\n        }\n\n        if (!matched || (isStrictMode && !this.isNodeLeaf(node))) {\n            matched = this.findFilteredNodes(node, { searchFields, filterText, isStrictMode }) || matched;\n        }\n\n        return matched;\n    }\n\n    getIndex(options: any, index: number) {\n        const getItemOptions = options['getItemOptions'];\n        return getItemOptions ? getItemOptions(index).index : index;\n    }\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    ngOnDestroy() {\n        if (this.dragStartSubscription) {\n            this.dragStartSubscription.unsubscribe();\n        }\n\n        if (this.dragStopSubscription) {\n            this.dragStopSubscription.unsubscribe();\n        }\n    }\n}\n@NgModule({\n    imports: [CommonModule, SharedModule, RippleModule, ScrollerModule, CheckIcon, ChevronDownIcon, ChevronRightIcon, MinusIcon, SearchIcon, SpinnerIcon, PlusIcon],\n    exports: [Tree, SharedModule, ScrollerModule],\n    declarations: [Tree, UITreeNode]\n})\nexport class TreeModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;MA0Oa,UAAU,CAAA;AACnB,IAAA,OAAO,UAAU,GAAW,kBAAkB,CAAC;AAEtC,IAAA,OAAO,CAAM;AAEb,IAAA,IAAI,CAA4B;AAEhC,IAAA,UAAU,CAA4B;AAEP,IAAA,IAAI,CAAsB;AAE3B,IAAA,KAAK,CAAqB;AAEzB,IAAA,UAAU,CAAsB;AAEhC,IAAA,SAAS,CAAsB;AAEhC,IAAA,KAAK,CAAqB;AAE1B,IAAA,WAAW,CAAqB;AAEhC,IAAA,QAAQ,CAAqB;AAE3D,IAAA,WAAW,CAAS;AAE7B,IAAA,IAAI,CAAO;AAEX,IAAA,OAAO,CAAM;AAEb,IAAA,aAAa,CAAsB;AAEnC,IAAA,aAAa,CAAsB;AAEnC,IAAA,aAAa,CAAsB;AAEnC,IAAA,IAAI,YAAY,GAAA;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,SAAS,CAAC;KACzH;AAED,IAAA,IAAI,WAAW,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,SAAS,CAAC;KACjF;AAED,IAAA,WAAA,CAA4C,IAAU,EAAA;AAClD,QAAA,IAAI,CAAC,IAAI,GAAG,IAAY,CAAC;KAC5B;IAED,QAAQ,GAAA;QACO,IAAI,CAAC,IAAK,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC/C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAW,IAAI,CAAC,IAAI,EAAmB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAS,IAAI,CAAC,UAAU,CAAC,GAAG,EAAmB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACtL,SAAA;KACJ;IAED,OAAO,GAAA;AACH,QAAA,IAAI,IAAwB,CAAC;AAE7B,QAAA,IAAe,IAAI,CAAC,IAAK,CAAC,IAAI;AAAE,YAAA,IAAI,GAAc,IAAI,CAAC,IAAK,CAAC,IAAc,CAAC;;AACvE,YAAA,IAAI,GAAc,IAAI,CAAC,IAAK,CAAC,QAAQ,IAAe,IAAI,CAAC,IAAK,CAAC,QAAQ,IAAe,IAAI,CAAC,IAAK,CAAC,QAAQ,EAAE,MAAM,GAAc,IAAI,CAAC,IAAK,CAAC,YAAY,GAAc,IAAI,CAAC,IAAK,CAAC,aAAa,CAAC;AAElM,QAAA,OAAO,UAAU,CAAC,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC;KAC7C;IAED,MAAM,GAAA;QACF,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAW,IAAI,CAAC,IAAI,CAAC,CAAC;KACpD;AAED,IAAA,MAAM,CAAC,KAAY,EAAA;AACf,QAAA,IAAe,IAAI,CAAC,IAAK,CAAC,QAAQ;AAAE,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;;AACpD,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAExB,KAAK,CAAC,eAAe,EAAE,CAAC;KAC3B;AAED,IAAA,MAAM,CAAC,KAAY,EAAA;AACJ,QAAA,IAAI,CAAC,IAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AACtC,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACzB,YAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC3B,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAY,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;KACpF;AAED,IAAA,QAAQ,CAAC,KAAY,EAAA;AACN,QAAA,IAAI,CAAC,IAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;AACvC,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACzB,YAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAClC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC3B,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAY,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;KACtF;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;QACzB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAY,IAAI,CAAC,IAAI,CAAC,CAAC;KACrD;AAED,IAAA,aAAa,CAAC,KAAoB,EAAA;AAC9B,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAY,IAAI,CAAC,IAAI,CAAC,CAAC;AACrD,SAAA;KACJ;IAED,cAAc,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;KAC9B;AAED,IAAA,gBAAgB,CAAC,KAAiB,EAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAY,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1D;IAED,UAAU,GAAA;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAW,IAAI,CAAC,IAAI,CAAC,CAAC;KACpD;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,OAAO,KAAK,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;KAC7J;IAED,WAAW,CAAC,KAAgB,EAAE,QAAgB,EAAA;QAC1C,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClC,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;AAC5C,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;AAC5C,QAAA,IAAI,qBAAqB,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,GAAG,QAAQ,KAAK,CAAC,IAAI,aAAa,KAAa,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;AAErI,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAW,QAAQ,EAAY,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,qBAAqB,EAAE;YACtG,IAAI,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC,4BAA4B,CAAS,QAAQ,CAAC,EAAE,CAAC;AAE5E,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACxB,gBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACtB,oBAAA,aAAa,EAAE,KAAK;AACpB,oBAAA,QAAQ,EAAE,QAAQ;oBAClB,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,MAAM,EAAE,MAAK;AACT,wBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;qBACrC;AACJ,iBAAA,CAAC,CAAC;AACN,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;AAClC,gBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACtB,oBAAA,aAAa,EAAE,KAAK;AACpB,oBAAA,QAAQ,EAAE,QAAQ;oBAClB,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,iBAAA,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;KAC9B;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;QACvB,IAAI,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;QAC3F,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AACtD,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;AAE3B,QAAA,IAAI,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE;AACpB,YAAA,SAAS,GAAG,KAAK,CAAC,gBAAgB,KAAK,WAAW,IAAI,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC;YACvI,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpD,SAAA;AAAM,aAAA;AACH,YAAA,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC;AAC/B,YAAA,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC/B,IAAI,EAAE,KAAK,CAAC,QAAQ;YACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;YAClF,KAAK,EAAE,KAAK,CAAC,aAAa;AAC7B,SAAA,CAAC,CAAC;KACN;AAED,IAAA,4BAA4B,CAAC,QAAgB,EAAA;QACzC,OAAO;AACH,YAAA,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ;AAC5B,YAAA,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa;AACtC,YAAA,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAC5C,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,YAAA,QAAQ,EAAE,QAAQ;SACrB,CAAC;KACL;AAED,IAAA,mBAAmB,CAAC,KAAU,EAAA;AAC1B,QAAA,KAAK,CAAC,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;QACvC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,oBAAoB,CAAC,KAAY,EAAE,QAAgB,EAAA;QAC/C,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACjG,IAAI,QAAQ,GAAG,CAAC;AAAE,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;;AACvC,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAClC,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,KAAY,EAAA;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;KAC9B;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,IAAe,IAAI,CAAC,IAAK,CAAC,SAAS,KAAK,KAAK,EAAE;YACvE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAE3C,YAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;AAChC,gBAAA,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;gBACzE,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc;AAClC,aAAA,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;YACH,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;YACzE,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,SAAA,CAAC,CAAC;KACN;AAED,IAAA,kBAAkB,CAAC,KAAU,EAAA;AACzB,QAAA,KAAK,CAAC,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;AACvC,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,KAAK,CAAC,eAAe,EAAE,CAAC;AAC3B,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,SAAS,KAAK,KAAK,EAAE;AAC5D,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AAElC,YAAA,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAW,QAAQ,EAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBACvF,IAAI,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC,2BAA2B,EAAE,EAAE,CAAC;AAE3D,gBAAA,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACxB,oBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACtB,wBAAA,aAAa,EAAE,KAAK;AACpB,wBAAA,QAAQ,EAAE,QAAQ;wBAClB,QAAQ,EAAE,IAAI,CAAC,IAAI;wBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,MAAM,EAAE,MAAK;AACT,4BAAA,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;yBACpC;AACJ,qBAAA,CAAC,CAAC;AACN,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACjC,oBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACtB,wBAAA,aAAa,EAAE,KAAK;AACpB,wBAAA,QAAQ,EAAE,QAAQ;wBAClB,QAAQ,EAAE,IAAI,CAAC,IAAI;wBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,qBAAA,CAAC,CAAC;AACN,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,KAAK,CAAC,eAAe,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;KAC9B;IAED,2BAA2B,GAAA;QACvB,OAAO;AACH,YAAA,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ;AAC5B,YAAA,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa;AACtC,YAAA,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAC5C,QAAQ,EAAE,IAAI,CAAC,IAAI;SACtB,CAAC;KACL;AAED,IAAA,eAAe,CAAC,KAAU,EAAA;AACtB,QAAA,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;QACxC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AAEhD,QAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ;YAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;;YACrE,KAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAEhD,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC/B,IAAI,EAAE,KAAK,CAAC,QAAQ;YACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;AAClF,YAAA,KAAK,EAAE,aAAa;AACvB,SAAA,CAAC,CAAC;KACN;AAED,IAAA,mBAAmB,CAAC,KAAU,EAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,SAAS,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAW,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAY,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;AAC/J,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,SAAA;KACJ;AAED,IAAA,mBAAmB,CAAC,KAAU,EAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAC1B,IAAI,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;YACvD,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE;AAChI,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC9B,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC,EAAE;YACzI,OAAO;AACV,SAAA;QAED,QAAQ,KAAK,CAAC,IAAI;;AAEd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;;AAGV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;;AAGV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;;AAGV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;;AAGV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM;;AAGV,YAAA,KAAK,OAAO;AACR,gBAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,YAAY,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAC9E,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC/B,oBAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvB,iBAAA;gBACD,MAAM;;AAGV,YAAA,KAAK,KAAK;gBACN,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,MAAM;AAEV,YAAA;;gBAEI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC1B,QAAA,MAAM,WAAW,GAAoB,KAAK,CAAC,MAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,SAAS,GAAoB,KAAK,CAAC,MAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAoB,KAAK,CAAC,MAAO,CAAC,aAAa,CAAC;QAE9M,IAAI,WAAW,CAAC,sBAAsB,EAAE;AACpC,YAAA,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,sBAAsB,EAAE,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC5I,SAAA;AAAM,aAAA;YACH,IAAI,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;AAE/D,YAAA,IAAI,iBAAiB,EAAE;AACnB,gBAAA,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AACvD,aAAA;AACJ,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,WAAW,GAAoB,KAAK,CAAC,MAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,SAAS,GAAoB,KAAK,CAAC,MAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAmB,KAAK,CAAC,MAAM,CAAC;QAC9L,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5C,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAChD,YAAA,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,WAAW,CAAC,aAAa,CAAC,kBAAkB,EAAE;gBAC9C,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;AAClF,aAAA;AAAM,iBAAA;gBACH,IAAI,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAEpF,gBAAA,IAAI,mBAAmB,EAAE;AACrB,oBAAA,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;AACzD,iBAAA;AACJ,aAAA;AACJ,SAAA;QACD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAoB,EAAA;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAW,IAAI,CAAC,IAAI,CAAC,EAAE;AACpE,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACF,YAAA,KAAK,CAAC,aAAc,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YAEpD,UAAU,CAAC,MAAK;AACZ,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAC3B,EAAE,CAAC,CAAC,CAAC;AACT,SAAA;QACD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,WAAW,GAAoB,KAAK,CAAC,MAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,SAAS,GAAoB,KAAK,CAAC,MAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAmB,KAAK,CAAC,MAAM,CAAC;AAE9L,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC1C,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrB,OAAO;AACV,SAAA;QAED,IAAI,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAE7E,QAAA,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;AAC/D,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAE5B,QAAA,MAAM,YAAY,GAAG,MAAM,YAAY,WAAW,KAAK,MAAM,CAAC,QAAQ,IAAI,GAAG,IAAI,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC,CAAC;AAE9G,QAAA,OAAO,YAAY,CAAC;KACvB;AAED,IAAA,OAAO,CAAC,KAAoB,EAAA;QACxB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAY,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAE/D,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;YAClC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;IAED,qBAAqB,GAAA;AACjB,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAEzE,QAAA,MAAM,eAAe,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,MAAM,CAAC,CAAC;QAEjJ,CAAC,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACxB,YAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACvB,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,eAAe,EAAE;AACjB,YAAA,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,MAAM,CAAC,CAAC;AAEjJ,YAAA,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;YAE9B,OAAO;AACV,SAAA;QAED,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;KAC9B;IAED,2BAA2B,CAAC,KAAK,EAAE,WAAW,EAAA;AAC1C,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;AAClC,YAAA,MAAM,QAAQ,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;AAEjF,YAAA,KAAK,CAAC,aAAa,CAAC,QAAQ,GAAG,WAAW,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAE9D,YAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE;AACtD,gBAAA,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC5B,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,yBAAyB,CAAC,WAAgB,EAAA;QACtC,IAAI,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;AAE/D,QAAA,IAAI,iBAAiB,EAAE;YACnB,IAAI,iBAAiB,CAAC,kBAAkB;gBAAE,OAAO,iBAAiB,CAAC,kBAAkB,CAAC;;AACjF,gBAAA,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;AACjE,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAED,IAAA,yBAAyB,CAAC,WAAgB,EAAA;QACtC,MAAM,WAAW,GAAgB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC;QACtH,MAAM,mBAAmB,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,mBAAmB,IAAI,mBAAmB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAChE,YAAA,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAE/F,YAAA,OAAO,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;AAC3D,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,WAAW,CAAC;AACtB,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,WAAkC,EAAA;QACnD,MAAM,iBAAiB,GAAG,WAAW,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;AAElF,QAAA,OAAO,iBAAiB,EAAE,OAAO,KAAK,YAAY,GAAG,iBAAiB,GAAG,IAAI,CAAC;KACjF;AAED,IAAA,SAAS,CAAC,OAAY,EAAA;AAClB,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc;YAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAiB,CAAC,KAAK,EAAE,CAAC;;YACrE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAiB,CAAC,KAAK,EAAE,CAAC;KACrD;AAED,IAAA,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,qBAAsB,EAAA;AACvE,QAAA,iBAAiB,CAAC,QAAQ,GAAG,IAAI,CAAC;QAClC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC;AAE7C,QAAA,IAAI,CAAC,SAAS,CAAC,qBAAqB,IAAI,iBAAiB,CAAC,CAAC;KAC9D;IAED,gBAAgB,GAAA;AACZ,QAAA,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,MAAK;YAC3B,IAAI,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAuB,UAAA,EAAA,IAAI,CAAC,IAAI,EAAE,GAAG,IAAc,IAAI,CAAC,IAAI,EAAE,IAAI,CAAI,EAAA,CAAA,CAAC,CAAC;AACxH,YAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC1B,EAAE,CAAC,CAAC,CAAC;KACT;AAzgBQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,kBA2CC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA3CjC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EASC,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,CAEhB,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,4CAEf,gBAAgB,CAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAEhB,gBAAgB,CAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAEhB,eAAe,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAEf,eAAe,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAEf,eAAe,CAvMzB,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4KT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAyiDmE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,eAAe,CAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,gBAAgB,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAc,WAAW,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,QAAQ,CAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAniDrJ,UAAU,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,MAAA,EAAA,YAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,WAAA,EAAA,OAAA,EAAA,aAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBApLtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4KT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BA4CgB,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,IAAI,CAAC,CAAA;yCAxCjC,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAEG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAEG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAEkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,SAAS,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEE,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEE,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,WAAW,EAAA,CAAA;sBAAnB,KAAK;;AAofV;;;AAGG;MA8HU,IAAI,CAAA;AAiTM,IAAA,EAAA,CAAA;AAAmC,IAAA,eAAA,CAAA;AAA6C,IAAA,MAAA,CAAA;AAA+B,IAAA,EAAA,CAAA;AAhTlI;;;AAGG;AACM,IAAA,KAAK,CAAgD;AAC9D;;;AAGG;AACM,IAAA,aAAa,CAAwD;AAC9E;;;AAGG;IACM,WAAW,GAAoB,MAAM,CAAC;AAC/C;;;AAGG;AACM,IAAA,SAAS,CAAM;AACxB;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,WAAW,CAAM;AAC1B;;;AAGG;IACM,MAAM,GAAW,UAAU,CAAC;AACrC;;;AAGG;AACM,IAAA,cAAc,CAAM;AAC7B;;;AAGG;AACM,IAAA,cAAc,CAAM;AAC7B;;;AAGG;AACqC,IAAA,cAAc,CAAsB;AAC5E;;;AAGG;AACqC,IAAA,cAAc,CAAsB;AAC5E;;;AAGG;IACqC,gBAAgB,GAAY,KAAK,CAAC;AAC1E;;;AAGG;IACqC,oBAAoB,GAAY,IAAI,CAAC;AAC7E;;;AAGG;IACqC,sBAAsB,GAAY,IAAI,CAAC;AAC/E;;;AAGG;AACqC,IAAA,OAAO,CAAsB;AACrE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;IACM,YAAY,GAAW,EAAE,CAAC;AACnC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;AACqC,IAAA,MAAM,CAAsB;AACpE;;;AAGG;IACM,QAAQ,GAAW,OAAO,CAAC;AACpC;;;AAGG;IACM,UAAU,GAAW,SAAS,CAAC;AACxC;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACM,IAAA,aAAa,CAAqC;AAC3D;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACoC,IAAA,qBAAqB,CAAqB;AACjF;;;AAGG;AACM,IAAA,oBAAoB,CAA8B;AAC3D;;;AAGG;IACoC,WAAW,GAAW,GAAG,CAAC;AACjE;;;AAGG;AACM,IAAA,YAAY,CAAM;AAC3B;;;AAGG;IACM,OAAO,GAAa,CAAC,KAAa,EAAE,IAAS,KAAK,IAAI,CAAC;AAChE;;;;AAIG;AACH,IAAA,kBAAkB,CAAqB;AACvC,IAAA,IAAa,iBAAiB,GAAA;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAClC;IACD,IAAI,iBAAiB,CAAC,GAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;AAC9B,QAAA,OAAO,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;KAC7G;AACD;;;;AAIG;AACO,IAAA,eAAe,GAAyD,IAAI,YAAY,EAA0C,CAAC;AAC7I;;;;AAIG;AACO,IAAA,YAAY,GAAsC,IAAI,YAAY,EAAuB,CAAC;AACpG;;;;AAIG;AACO,IAAA,cAAc,GAAwC,IAAI,YAAY,EAAyB,CAAC;AAC1G;;;;AAIG;AACO,IAAA,YAAY,GAAsC,IAAI,YAAY,EAAuB,CAAC;AACpG;;;;AAIG;AACO,IAAA,cAAc,GAAwC,IAAI,YAAY,EAAyB,CAAC;AAC1G;;;;AAIG;AACO,IAAA,uBAAuB,GAAiD,IAAI,YAAY,EAAkC,CAAC;AACrI;;;;AAIG;AACO,IAAA,UAAU,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAC9F;;;;AAIG;AACO,IAAA,UAAU,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAC9F;;;;AAIG;AACO,IAAA,QAAQ,GAAkC,IAAI,YAAY,EAAmB,CAAC;AACxF;;;;AAIG;AACO,IAAA,mBAAmB,GAA6C,IAAI,YAAY,EAA8B,CAAC;AACzH;;;;AAIG;AACO,IAAA,QAAQ,GAAkC,IAAI,YAAY,EAAmB,CAAC;AAExD,IAAA,SAAS,CAA2B;AAE/C,IAAA,eAAe,CAAuB;AAEpC,IAAA,QAAQ,CAAqB;AAE9B,IAAA,gBAAgB,CAAuB;AAE7D,IAAA,eAAe,CAA4B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,cAAc,CAA6B;AAE3C,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,oBAAoB,CAA6B;AAEjD,IAAA,mBAAmB,CAA6B;AAEhD,IAAA,kBAAkB,CAA6B;AAExC,IAAA,WAAW,CAA6B;AAExC,IAAA,YAAY,CAA0B;AAEtC,IAAA,QAAQ,CAAmC;AAE3C,IAAA,gBAAgB,CAAqC;AAErD,IAAA,aAAa,CAA4B;AAEzC,IAAA,aAAa,CAAM;AAEnB,IAAA,SAAS,CAA6B;AAEtC,IAAA,qBAAqB,CAAkC;AAEvD,IAAA,oBAAoB,CAAkC;AAE7D,IAAA,WAAA,CAAmB,EAAc,EAAqB,eAAoC,EAAS,MAAqB,EAAU,EAAqB,EAAA;QAApI,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAqB,IAAe,CAAA,eAAA,GAAf,eAAe,CAAqB;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;KAAI;IAE3J,QAAQ,GAAA;QACJ,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,KAAK,KAAI;AAC7E,gBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC;AAC/B,gBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;AAC3B,gBAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,QAAQ,CAAC;AACvC,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC;AACjC,gBAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC;AACrC,aAAC,CAAC,CAAC;AAEH,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,KAAI;AAC3E,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC7B,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,YAA2B,EAAA;QACnC,IAAI,YAAY,CAAC,KAAK,EAAE;YACpB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;gBACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1D,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,IAAI,UAAU,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,YAAY,CAAC;KACtC;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;KACzF;IAED,kBAAkB,GAAA;AACd,QAAA,IAAK,IAAI,CAAC,SAAsC,CAAC,MAAM,EAAE;AACrD,YAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC1B,SAAA;QAEA,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC1C,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA;oBACI,IAAI,CAAC,YAAY,CAAM,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClD,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,qBAAqB,GAAA;AACjB,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;KAC1D;AAED,IAAA,cAAc,CAAC,MAA4B,EAAE,KAA4B,EAAE,KAAa,EAAE,OAAgB,EAAA;AACtG,QAAA,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AACvB,YAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACpB,gBAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,gBAAA,MAAM,OAAO,GAAG;AACZ,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,MAAM,EAAE,MAAM;AACd,oBAAA,KAAK,EAAE,KAAK;AACZ,oBAAA,OAAO,EAAE,OAAO,KAAK,MAAM,GAAG,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;iBACxD,CAAC;AACD,gBAAA,IAAI,CAAC,eAAmC,CAAC,IAAI,CAAW,OAAO,CAAC,CAAC;AAElE,gBAAA,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AAClC,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACxE,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,CAAC,KAAY,EAAE,IAAc,EAAA;AACpC,QAAA,IAAI,WAAW,GAAY,KAAK,CAAC,MAAM,CAAC;AACxC,QAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,gBAAgB,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,qBAAqB,CAAC,EAAE;YAC/G,OAAO;AACV,SAAA;aAAM,IAAI,IAAI,CAAC,aAAa,EAAE;AAC3B,YAAA,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;AAC3B,gBAAA,IAAI,CAAC,KAAK,GAAG,6BAA6B,CAAC;gBAC3C,OAAO;AACV,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,KAAK,GAAG,4CAA4C,CAAC;AAC7D,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;AACzB,gBAAA,IAAI,GAAG,IAAI,CAAC,cAAc,CAAS,IAAI,CAAC,GAAG,EAAmB,IAAI,CAAC,aAAa,CAAa,CAAC;gBAC9F,IAAI,CAAC,IAAI,EAAE;oBACP,OAAO;AACV,iBAAA;AACJ,aAAA;YAED,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAC5C,YAAA,IAAI,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC;AAE1B,YAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE;AAChC,gBAAA,IAAI,QAAQ,EAAE;oBACV,IAAI,IAAI,CAAC,sBAAsB;AAAE,wBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;;wBAC5D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AAEtF,oBAAA,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,MAAM,EAAE;wBAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxC,qBAAA;oBAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAClE,iBAAA;AAAM,qBAAA;oBACH,IAAI,IAAI,CAAC,sBAAsB;AAAE,wBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;;AAC3D,wBAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAExD,oBAAA,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,MAAM,EAAE;wBAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACvC,qBAAA;oBAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1C,oBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAChE,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAErE,gBAAA,IAAI,aAAa,EAAE;oBACf,IAAI,OAAO,GAAmB,KAAM,CAAC,OAAO,IAAoB,KAAM,CAAC,OAAO,CAAC;oBAE/E,IAAI,QAAQ,IAAI,OAAO,EAAE;AACrB,wBAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;AAC9B,4BAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,yBAAA;AAAM,6BAAA;4BACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;4BACjF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,yBAAA;AAED,wBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAClE,qBAAA;AAAM,yBAAA;AACH,wBAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;AAC9B,4BAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAW,IAAI,CAAC,CAAC;AAC7C,yBAAA;AAAM,6BAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE,EAAE;AACvC,4BAAA,IAAI,CAAC,SAAS,GAAG,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;4BACtD,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;4BAC3C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,yBAAA;AAED,wBAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAChE,qBAAA;AACJ,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;AAC9B,wBAAA,IAAI,QAAQ,EAAE;AACV,4BAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,4BAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAClE,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;4BACtB,UAAU,CAAC,MAAK;AACZ,gCAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACjE,6BAAC,CAAC,CAAC;AACN,yBAAA;AACJ,qBAAA;AAAM,yBAAA;AACH,wBAAA,IAAI,QAAQ,EAAE;4BACV,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AACjF,4BAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAClE,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;4BACnD,UAAU,CAAC,MAAK;AACZ,gCAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACjE,6BAAC,CAAC,CAAC;AACN,yBAAA;AACJ,qBAAA;oBAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;KAC5B;IAED,cAAc,GAAA;AACV,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;IAED,gBAAgB,CAAC,KAAiB,EAAE,IAAmB,EAAA;QACnD,IAAI,IAAI,CAAC,WAAW,EAAE;AAClB,YAAA,IAAI,WAAW,GAAY,KAAK,CAAC,MAAM,CAAC;YACxC,IAAI,SAAS,GAAG,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAElD,IAAI,SAAS,IAAI,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;gBACnD,OAAO;AACV,aAAA;AAAM,iBAAA;gBACH,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAC5C,gBAAA,IAAI,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC;gBAE1B,IAAI,CAAC,QAAQ,EAAE;oBACX,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAAE,wBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;wBAC7D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,iBAAA;AAED,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7B,gBAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3E,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,oBAAoB,CAAC,IAAc,EAAA;AAC/B,QAAA,IAAI,KAAK,GAAW,CAAC,CAAC,CAAC;AACvB,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE;AACtC,YAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBAC9B,IAAI,aAAa,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;gBACtG,KAAK,GAAG,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC5C,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACrC,oBAAA,IAAI,aAAa,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI,YAAY,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,KAAK,YAAY,IAAI,IAAI,CAAC;AAChG,oBAAA,IAAI,aAAa,EAAE;wBACf,KAAK,GAAG,CAAC,CAAC;wBACV,MAAM;AACT,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,cAAc,CAAC,IAAc,EAAE,WAA4B,EAAE,MAAW,EAAE,KAAW,EAAA;;QAEjF,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAS,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC;AAClG,QAAA,IAAI,KAAK,EAAE;YACD,KAAM,CAAC,MAAM,CAAC,GAAG,KAAK,IAAU,IAAK,CAAC,MAAM,CAAC,CAAC;AACvD,SAAA;KACJ;IAED,gBAAgB,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;KACzE;IAED,eAAe,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KAC/E;IAED,cAAc,CAAC,GAAW,EAAE,KAAsB,EAAA;AAC9C,QAAA,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AACpB,YAAA,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE;AAClB,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;YAED,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,gBAAA,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1D,gBAAA,IAAI,WAAW,EAAE;AACb,oBAAA,OAAO,WAAW,CAAC;AACtB,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,WAAW,CAAC,IAAc,EAAE,MAAe,EAAA;QACvC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACvC,IAAI,aAAa,GAAW,CAAC,CAAC;YAC9B,IAAI,oBAAoB,GAAY,KAAK,CAAC;AAC1C,YAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC7B,gBAAA,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AACxB,oBAAA,aAAa,EAAE,CAAC;AACnB,iBAAA;qBAAM,IAAI,KAAK,CAAC,eAAe,EAAE;oBAC9B,oBAAoB,GAAG,IAAI,CAAC;AAC/B,iBAAA;AACJ,aAAA;YAED,IAAI,MAAM,IAAI,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACjD,gBAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACnD,gBAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAChC,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,MAAM,EAAE;oBACT,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC5C,IAAI,KAAK,IAAI,CAAC,EAAE;wBACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AACpF,qBAAA;AACJ,iBAAA;AAED,gBAAA,IAAI,oBAAoB,KAAK,aAAa,GAAG,CAAC,IAAI,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAAE,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;;AACjH,oBAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AACrC,aAAA;YAED,IAAI,CAAC,cAAc,CAAC,IAAI,EAAmB,IAAI,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;AACrF,SAAA;AAED,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACpC,SAAA;KACJ;IAED,aAAa,CAAC,IAAc,EAAE,MAAe,EAAA;QACzC,IAAI,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAE5C,QAAA,IAAI,MAAM,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE;AACvB,YAAA,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACtD,SAAA;AAAM,aAAA,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,CAAS,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AACpF,SAAA;AAED,QAAA,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,IAAI,CAAC,cAAc,CAAC,IAAI,EAAmB,IAAI,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;QAElF,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACvC,YAAA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC7B,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACrC,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,IAAc,EAAA;QACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KAChD;IAED,qBAAqB,GAAA;QACjB,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC;KAC/D;IAED,uBAAuB,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,CAAC;KACjE;IAED,uBAAuB,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,CAAC;KACjE;AAED,IAAA,UAAU,CAAC,IAAc,EAAA;QACrB,OAAO,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;KAChF;IAED,WAAW,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;KAC/D;AAED,IAAA,kBAAkB,CAAC,IAAc,EAAA;QAC7B,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;;AACjG,YAAA,OAAO,IAAI,CAAC;KACpB;AAED,IAAA,UAAU,CAAC,KAAgB,EAAA;AACvB,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,CAAC,KAAK,IAAU,IAAI,CAAC,KAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;AAClE,YAAA,KAAM,CAAC,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;YAC9C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,MAAM,CAAC,KAAgB,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,CAAC,KAAK,IAAU,IAAI,CAAC,KAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;YACxE,KAAK,CAAC,cAAc,EAAE,CAAC;AACvB,YAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAoB,CAAC;AAEzC,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE;AACpD,gBAAA,IAAI,aAAa,GAAW,IAAI,CAAC,aAAa,CAAC;gBAC/C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBAE9B,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,oBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACjB,wBAAA,aAAa,EAAE,KAAK;AACpB,wBAAA,QAAQ,EAAE,QAAQ;AAClB,wBAAA,QAAQ,EAAE,IAAI;AACd,wBAAA,KAAK,EAAE,aAAa;wBACpB,MAAM,EAAE,MAAK;AACT,4BAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;yBACjD;AACJ,qBAAA,CAAC,CAAC;AACN,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACjB,wBAAA,aAAa,EAAE,KAAK;AACpB,wBAAA,QAAQ,EAAE,QAAQ;AAClB,wBAAA,QAAQ,EAAE,IAAI;AACd,wBAAA,KAAK,EAAE,aAAa;AACvB,qBAAA,CAAC,CAAC;AAEH,oBAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AACjD,iBAAA;AACJ,aAAA;AACJ,SAAA;KACJ;IAED,eAAe,CAAC,QAAkB,EAAE,aAAqB,EAAA;QACnC,IAAI,CAAC,gBAAiB,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AACjE,QAAA,IAAI,CAAC,KAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;AAC1B,YAAA,IAAI,EAAE,QAAQ;AACjB,SAAA,CAAC,CAAC;KACN;IAED,WAAW,GAAA;AACP,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,CAAW,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE;AAC1F,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACzB,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAgB,EAAA;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,IAAI,GAAI,KAAK,CAAC,aAA6B,CAAC,qBAAqB,EAAE,CAAC;AACxE,YAAA,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE;AACnH,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC1B,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,QAAkB,EAAE,QAA8B,EAAE,aAAkB,EAAA;QAC5E,IAAI,CAAC,QAAQ,EAAE;;AAEX,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAAM,aAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE;YAC7C,IAAI,KAAK,GAAY,IAAI,CAAC;AAC1B,YAAA,IAAI,QAAQ,EAAE;gBACV,IAAI,QAAQ,KAAK,QAAQ,EAAE;oBACvB,KAAK,GAAG,KAAK,CAAC;AACjB,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;oBAC7B,OAAO,MAAM,IAAI,IAAI,EAAE;wBACnB,IAAI,MAAM,KAAK,QAAQ,EAAE;4BACrB,KAAK,GAAG,KAAK,CAAC;4BACd,MAAM;AACT,yBAAA;AACD,wBAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC1B,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,SAAc,EAAA;AAC3B,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;AAEpC,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;gBAC/B,IAAI,OAAO,SAAS,KAAK,QAAQ;oBAAE,OAAO,SAAS,KAAK,SAAS,CAAC;AAC7D,qBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;oBAAE,OAAoB,SAAU,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9F,aAAA;AAAM,iBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AACjC,gBAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;oBAC/B,OAAoB,SAAU,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3D,iBAAA;AAAM,qBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AACjC,oBAAA,KAAK,IAAI,CAAC,IAAI,SAAS,EAAE;AACrB,wBAAA,KAAK,IAAI,EAAE,IAAI,SAAS,EAAE;4BACtB,IAAI,CAAC,KAAK,EAAE,EAAE;AACV,gCAAA,OAAO,IAAI,CAAC;AACf,6BAAA;AACJ,yBAAA;AACJ,qBAAA;AACJ,iBAAA;AACJ,aAAA;AACD,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ;AAEM,IAAA,OAAO,CAAC,KAAa,EAAA;QACxB,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,WAAW,KAAK,EAAE,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;YACxB,MAAM,YAAY,GAAa,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACxD,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC/F,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC;AAClD,YAAA,KAAK,IAAI,IAAI,IAAqB,IAAI,CAAC,KAAK,EAAE;AAC1C,gBAAA,IAAI,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;gBAC3B,IAAI,iBAAiB,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;gBACnE,IACI,CAAC,YAAY,KAAK,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;qBAC1H,CAAC,YAAY,KAAK,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC,CAAC,EAC/H;AACE,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrC,iBAAA;AACJ,aAAA;AACJ,SAAA;QAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACf,YAAA,MAAM,EAAE,WAAW;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;AACpC,SAAA,CAAC,CAAC;KACN;AAED;;;AAGG;IACI,WAAW,GAAA;AACd,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;YAC5D,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AACjD,SAAA;KACJ;AACD;;;;AAIG;AACI,IAAA,oBAAoB,CAAC,KAAa,EAAA;QACrC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;KAC7D;AACD;;;;AAIG;AACI,IAAA,QAAQ,CAAC,OAAY,EAAA;QACxB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACpC,SAAA;aAAM,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;AACrE,YAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,QAAQ,EAAE;gBAC9C,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACzD,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;gBAC9D,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;AAC/D,aAAA;AACJ,SAAA;KACJ;IAED,iBAAiB,CAAC,IAAc,EAAE,iBAAsB,EAAA;AACpD,QAAA,IAAI,IAAI,EAAE;YACN,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;AACpC,gBAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACnB,gBAAA,KAAK,IAAI,SAAS,IAAI,UAAU,EAAE;AAC9B,oBAAA,IAAI,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC;oBACrC,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,iBAAiB,CAAC,EAAE;wBACxD,OAAO,GAAG,IAAI,CAAC;AACf,wBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACrC,qBAAA;AACJ,iBAAA;AACJ,aAAA;AAED,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,gBAAA,OAAO,IAAI,CAAC;AACf,aAAA;AACJ,SAAA;KACJ;IAED,eAAe,CAAC,IAAc,EAAE,MAAW,EAAA;QACvC,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC;QACxD,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB,QAAA,KAAK,IAAI,KAAK,IAAI,YAAY,EAAE;YAC5B,IAAI,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnI,IAAI,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;gBACrC,OAAO,GAAG,IAAI,CAAC;AAClB,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;AACtD,YAAA,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC,IAAI,OAAO,CAAC;AACjG,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB;IAED,QAAQ,CAAC,OAAY,EAAE,KAAa,EAAA;AAChC,QAAA,MAAM,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACjD,QAAA,OAAO,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;KAC/D;IAED,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC5B,YAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;AAC5C,SAAA;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC3B,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;AAC3C,SAAA;KACJ;uGAp5BQ,IAAI,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAJ,IAAI,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,eAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,WAAA,EAAA,aAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAuDO,gBAAgB,CAAA,EAAA,cAAA,EAAA,CAAA,gBAAA,EAAA,gBAAA,EAKhB,gBAAgB,CAAA,EAAA,gBAAA,EAAA,CAAA,kBAAA,EAAA,kBAAA,EAKhB,gBAAgB,CAAA,EAAA,oBAAA,EAAA,CAAA,sBAAA,EAAA,sBAAA,EAKhB,gBAAgB,CAAA,EAAA,sBAAA,EAAA,CAAA,wBAAA,EAAA,wBAAA,EAKhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAKhB,gBAAgB,CAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EA8BhB,gBAAgB,CAKhB,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,CAmChB,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,CAKhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,CAKhB,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAAA,eAAe,CAUf,EAAA,oBAAA,EAAA,sBAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,eAAe,CA2FlB,EAAA,YAAA,EAAA,cAAA,EAAA,OAAA,EAAA,SAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EAhYpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,42oCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA+5B4H,UAAU,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,WAAW,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAniD3I,UAAU,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,MAAA,EAAA,YAAA,EAAA,MAAA,EAAA,OAAA,EAAA,YAAA,EAAA,WAAA,EAAA,OAAA,EAAA,aAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA4oBV,IAAI,EAAA,UAAA,EAAA,CAAA;kBA7HhB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,QAAQ,EACR,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,OAAO,iBACjC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,42oCAAA,CAAA,EAAA,CAAA;;0BAmTmC,QAAQ;qGA5SnC,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,cAAc,EAAA,CAAA;sBAArD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,oBAAoB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,sBAAsB,EAAA,CAAA;sBAA7D,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,OAAO,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,qBAAqB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKiC,WAAW,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAOO,iBAAiB,EAAA,CAAA;sBAA7B,KAAK;gBAYI,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,YAAY,EAAA,CAAA;sBAArB,MAAM;gBAMG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAMG,uBAAuB,EAAA,CAAA;sBAAhC,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,mBAAmB,EAAA,CAAA;sBAA5B,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAET,eAAe,EAAA,CAAA;sBAAnC,SAAS;uBAAC,QAAQ,CAAA;gBAEI,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;gBAEC,gBAAgB,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;;MAgpBX,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EA35BV,YAAA,EAAA,CAAA,IAAI,EA5oBJ,UAAU,CAmiDT,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAv5BrJ,EAAA,OAAA,EAAA,CAAA,IAAI,EAw5BG,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;wGAGnC,UAAU,EAAA,OAAA,EAAA,CAJT,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAC9I,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;;2FAGnC,UAAU,EAAA,UAAA,EAAA,CAAA;kBALtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC;AAC/J,oBAAA,OAAO,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,cAAc,CAAC;AAC7C,oBAAA,YAAY,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;AACnC,iBAAA,CAAA;;;AChxDD;;AAEG;;;;"}