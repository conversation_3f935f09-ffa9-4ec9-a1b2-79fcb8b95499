{"version": 3, "file": "primeng-listbox.mjs", "sources": ["../../src/app/components/listbox/listbox.ts", "../../src/app/components/listbox/primeng-listbox.ts"], "sourcesContent": ["import {\n    NgModule,\n    Component,\n    ElementRef,\n    Input,\n    Output,\n    EventEmitter,\n    AfterContentInit,\n    ContentChildren,\n    ContentChild,\n    QueryList,\n    TemplateRef,\n    forwardRef,\n    ChangeDetectorRef,\n    ViewChild,\n    ChangeDetectionStrategy,\n    ViewEncapsulation,\n    OnInit,\n    OnDestroy,\n    computed,\n    signal,\n    Renderer2,\n    booleanAttribute,\n    numberAttribute\n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule, PrimeTemplate, Footer, Header, FilterService, TranslationKeys, PrimeNGConfig, ScrollerOptions } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';\nimport { RippleModule } from 'primeng/ripple';\nimport { Subscription } from 'rxjs';\nimport { SearchIcon } from 'primeng/icons/search';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { Nullable } from 'primeng/ts-helpers';\nimport { ListboxChangeEvent, ListboxClickEvent, ListboxDoubleClickEvent, ListboxFilterEvent, ListboxFilterOptions, ListboxSelectAllChangeEvent } from './listbox.interface';\nimport { Scroller, ScrollerModule } from 'primeng/scroller';\n\nexport const LISTBOX_VALUE_ACCESSOR: any = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Listbox),\n    multi: true\n};\n/**\n * ListBox is used to select one or more values from a list of items.\n * @group Components\n */\n@Component({\n    selector: 'p-listbox',\n    template: `\n        <div [attr.id]=\"id\" [ngClass]=\"containerClass\" [ngStyle]=\"style\" [class]=\"styleClass\" (focusout)=\"onFocusout($event)\">\n            <span\n                #firstHiddenFocusableElement\n                role=\"presentation\"\n                [attr.aria-hidden]=\"true\"\n                class=\"p-hidden-accessible p-hidden-focusable\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                (focus)=\"onFirstHiddenFocus($event)\"\n                [attr.data-p-hidden-focusable]=\"true\"\n            >\n            </span>\n            <div class=\"p-listbox-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate; context: { $implicit: modelValue(), options: visibleOptions() }\"></ng-container>\n            </div>\n            <div class=\"p-listbox-header\" *ngIf=\"(checkbox && multiple && showToggleAll) || filter\">\n                <div *ngIf=\"checkbox && multiple && showToggleAll\" class=\"p-checkbox p-component\" [ngClass]=\"{ 'p-checkbox-disabled': disabled || toggleAllDisabled }\" (click)=\"onToggleAll($event)\" (keydown)=\"onHeaderCheckboxKeyDown($event)\">\n                    <div class=\"p-hidden-accessible\" [attr.data-p-hidden-accessible]=\"true\">\n                        <input\n                            #headerchkbox\n                            type=\"checkbox\"\n                            readonly=\"readonly\"\n                            [attr.checked]=\"allSelected()\"\n                            [disabled]=\"disabled || toggleAllDisabled\"\n                            (focus)=\"onHeaderCheckboxFocus($event)\"\n                            (blur)=\"onHeaderCheckboxBlur($event)\"\n                            [attr.aria-label]=\"toggleAllAriaLabel\"\n                        />\n                    </div>\n                    <div class=\"p-checkbox-box\" role=\"checkbox\" [attr.aria-checked]=\"allSelected()\" [ngClass]=\"{ 'p-highlight': allSelected(), 'p-focus': headerCheckboxFocus, 'p-disabled': disabled || toggleAllDisabled }\">\n                        <ng-container *ngIf=\"allSelected()\">\n                            <CheckIcon [styleClass]=\"'p-checkbox-icon'\" *ngIf=\"!checkIconTemplate\" [attr.aria-hidden]=\"true\" />\n                            <span *ngIf=\"checkIconTemplate\" class=\"p-checkbox-icon\" [attr.aria-hidden]=\"true\">\n                                <ng-template *ngTemplateOutlet=\"checkIconTemplate\"></ng-template>\n                            </span>\n                        </ng-container>\n                    </div>\n                </div>\n                <ng-container *ngIf=\"filterTemplate; else builtInFilterElement\">\n                    <ng-container *ngTemplateOutlet=\"filterTemplate; context: { options: filterOptions }\"></ng-container>\n                </ng-container>\n                <ng-template #builtInFilterElement>\n                    <div class=\"p-listbox-filter-container\" *ngIf=\"filter\">\n                        <input\n                            #filterInput\n                            type=\"text\"\n                            class=\"p-listbox-filter p-inputtext p-component\"\n                            role=\"searchbox\"\n                            [value]=\"_filterValue() || ''\"\n                            [disabled]=\"disabled\"\n                            [attr.aria-owns]=\"id + '_list'\"\n                            [attr.aria-activedescendant]=\"focusedOptionId\"\n                            [attr.placeholder]=\"filterPlaceHolder\"\n                            [attr.aria-label]=\"ariaFilterLabel\"\n                            [tabindex]=\"!disabled && !focused ? tabindex : -1\"\n                            (input)=\"onFilterChange($event)\"\n                            (keydown)=\"onFilterKeyDown($event)\"\n                            (blur)=\"onFilterBlur($event)\"\n                        />\n                        <SearchIcon *ngIf=\"!filterIconTemplate\" [styleClass]=\"'p-listbox-filter-icon'\" [attr.aria-hidden]=\"true\" />\n                        <span *ngIf=\"filterIconTemplate\" class=\"p-listbox-filter-icon\" [attr.aria-hidden]=\"true\">\n                            <ng-template *ngTemplateOutlet=\"filterIconTemplate\"></ng-template>\n                        </span>\n                    </div>\n                    <span role=\"status\" attr.aria-live=\"polite\" class=\"p-hidden-accessible\" [attr.data-p-hidden-accessible]=\"true\">\n                        {{ filterResultMessageText }}\n                    </span>\n                </ng-template>\n            </div>\n            <div [ngClass]=\"'p-listbox-list-wrapper'\" [ngStyle]=\"listStyle\" [class]=\"listStyleClass\" [style.max-height]=\"virtualScroll ? 'auto' : scrollHeight || 'auto'\">\n                <p-scroller\n                    #scroller\n                    *ngIf=\"virtualScroll\"\n                    [items]=\"visibleOptions()\"\n                    [style]=\"{ height: scrollHeight }\"\n                    [itemSize]=\"virtualScrollItemSize\"\n                    [autoSize]=\"true\"\n                    [tabindex]=\"-1\"\n                    [lazy]=\"lazy\"\n                    [options]=\"virtualScrollOptions\"\n                    (onLazyLoad)=\"onLazyLoad.emit($event)\"\n                >\n                    <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                        <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: items, options: scrollerOptions }\"></ng-container>\n                    </ng-template>\n                    <ng-container *ngIf=\"loaderTemplate\">\n                        <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: scrollerOptions }\"></ng-container>\n                        </ng-template>\n                    </ng-container>\n                </p-scroller>\n                <ng-container *ngIf=\"!virtualScroll\">\n                    <ng-container *ngTemplateOutlet=\"buildInItems; context: { $implicit: visibleOptions(), options: {} }\"></ng-container>\n                </ng-container>\n\n                <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                    <ul\n                        #list\n                        class=\"p-listbox-list\"\n                        role=\"listbox\"\n                        [tabindex]=\"-1\"\n                        [attr.aria-multiselectable]=\"true\"\n                        [ngClass]=\"scrollerOptions.contentStyleClass\"\n                        [ngStyle]=\"scrollerOptions.contentStyle\"\n                        [attr.aria-activedescendant]=\"focused ? focusedOptionId : undefined\"\n                        [attr.aria-label]=\"ariaLabel\"\n                        [attr.aria-multiselectable]=\"multiple\"\n                        [attr.aria-disabled]=\"disabled\"\n                        (focus)=\"onListFocus($event)\"\n                        (blur)=\"onListBlur($event)\"\n                        (keydown)=\"onListKeyDown($event)\"\n                    >\n                        <ng-template ngFor let-option [ngForOf]=\"items\" let-i=\"index\">\n                            <ng-container *ngIf=\"isOptionGroup(option)\">\n                                <li [attr.id]=\"id + '_' + getOptionIndex(i, scrollerOptions)\" class=\"p-listbox-item-group\" [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\" role=\"option\">\n                                    <span *ngIf=\"!groupTemplate\">{{ getOptionGroupLabel(option.optionGroup) }}</span>\n                                    <ng-container *ngTemplateOutlet=\"groupTemplate; context: { $implicit: option.optionGroup }\"></ng-container>\n                                </li>\n                            </ng-container>\n                            <ng-container *ngIf=\"!isOptionGroup(option)\">\n                                <li\n                                    pRipple\n                                    class=\"p-listbox-item\"\n                                    role=\"option\"\n                                    [attr.id]=\"id + '_' + getOptionIndex(i, scrollerOptions)\"\n                                    [ngStyle]=\"{ height: scrollerOptions.itemSize + 'px' }\"\n                                    [ngClass]=\"{ 'p-listbox-item': true, 'p-highlight': isSelected(option), 'p-focus': focusedOptionIndex() === getOptionIndex(i, scrollerOptions), 'p-disabled': isOptionDisabled(option) }\"\n                                    [attr.aria-label]=\"getOptionLabel(option)\"\n                                    [attr.aria-selected]=\"isSelected(option)\"\n                                    [attr.aria-disabled]=\"isOptionDisabled(option)\"\n                                    [attr.aria-setsize]=\"ariaSetSize\"\n                                    [attr.ariaPosInset]=\"getAriaPosInset(getOptionIndex(i, scrollerOptions))\"\n                                    (click)=\"onOptionSelect($event, option, getOptionIndex(i, scrollerOptions))\"\n                                    (dblclick)=\"onOptionDoubleClick($event, option)\"\n                                    (mousedown)=\"onOptionMouseDown($event, getOptionIndex(i, scrollerOptions))\"\n                                    (mouseenter)=\"onOptionMouseEnter($event, getOptionIndex(i, scrollerOptions))\"\n                                    (touchend)=\"onOptionTouchEnd()\"\n                                >\n                                    <div class=\"p-checkbox p-component\" *ngIf=\"checkbox && multiple\" [ngClass]=\"{ 'p-checkbox-disabled': disabled || isOptionDisabled(option) }\">\n                                        <div class=\"p-checkbox-box\" [ngClass]=\"{ 'p-highlight': isSelected(option) }\">\n                                            <ng-container *ngIf=\"isSelected(option)\">\n                                                <CheckIcon [styleClass]=\"'p-checkbox-icon'\" *ngIf=\"!checkIconTemplate\" [attr.aria-hidden]=\"true\" />\n                                                <span *ngIf=\"checkIconTemplate\" class=\"p-checkbox-icon\" [attr.aria-hidden]=\"true\">\n                                                    <ng-template *ngTemplateOutlet=\"checkIconTemplate\"></ng-template>\n                                                </span>\n                                            </ng-container>\n                                        </div>\n                                    </div>\n                                    <span *ngIf=\"!itemTemplate\">{{ getOptionLabel(option) }}</span>\n                                    <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: option, index: getOptionIndex(i, scrollerOptions) }\"></ng-container>\n                                </li>\n                            </ng-container>\n                        </ng-template>\n                        <li *ngIf=\"hasFilter() && isEmpty()\" class=\"p-listbox-empty-message\" role=\"option\">\n                            <ng-container *ngIf=\"!emptyFilterTemplate && !emptyTemplate; else emptyFilter\">\n                                {{ emptyFilterMessageText }}\n                            </ng-container>\n                            <ng-container #emptyFilter *ngTemplateOutlet=\"emptyFilterTemplate || emptyTemplate\"></ng-container>\n                        </li>\n                        <li *ngIf=\"!hasFilter() && isEmpty()\" class=\"p-listbox-empty-message\" role=\"option\">\n                            <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                {{ emptyMessage }}\n                            </ng-container>\n                            <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                        </li>\n                    </ul>\n                </ng-template>\n            </div>\n            <div class=\"p-listbox-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate; context: { $implicit: modelValue(), options: visibleOptions() }\"></ng-container>\n            </div>\n            <span *ngIf=\"isEmpty()\" role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\">\n                {{ emptyMessage }}\n            </span>\n            <span role=\"status\" aria-live=\"polite\" class=\"p-hidden-accessible\">\n                {{ selectedMessageText }}\n            </span>\n            <span\n                #lastHiddenFocusableElement\n                role=\"presentation\"\n                [attr.aria-hidden]=\"true\"\n                class=\"p-hidden-accessible p-hidden-focusable\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                (focus)=\"onLastHiddenFocus($event)\"\n                [attr.data-p-hidden-focusable]=\"true\"\n            >\n            </span>\n        </div>\n    `,\n    providers: [LISTBOX_VALUE_ACCESSOR],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./listbox.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Listbox implements AfterContentInit, OnInit, ControlValueAccessor, OnDestroy {\n    /**\n     * Unique identifier of the component.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Text to display when the search is active. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} results are available'\n     */\n    @Input() searchMessage: string | undefined;\n    /**\n     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue 'No selected item'\n     */\n    @Input() emptySelectionMessage: string | undefined;\n    /**\n     * Text to be displayed in hidden accessible field when options are selected. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} items selected'\n     */\n    @Input() selectionMessage: string | undefined;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoOptionFocus: boolean | undefined = true;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * When enabled, the focused option is selected.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) selectOnFocus: boolean | undefined;\n    /**\n     * Locale to use in searching. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) searchLocale: boolean | undefined;\n    /**\n     * When enabled, the hovered option will be focused.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) focusOnHover: boolean | undefined;\n    /**\n     * Text to display when filtering.\n     * @group Props\n     */\n    @Input() filterMessage: string | undefined;\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    @Input() filterFields: any[] | undefined;\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) lazy: boolean = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) virtualScroll: boolean | undefined;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) virtualScrollItemSize: number | undefined;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    @Input() virtualScrollOptions: ScrollerOptions | undefined;\n    /**\n     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    @Input() scrollHeight: string = '200px';\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number | undefined = 0;\n    /**\n     * When specified, allows selecting multiple values.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) multiple: boolean | undefined;\n    /**\n     * Inline style of the container.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the container.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the list element.\n     * @group Props\n     */\n    @Input() listStyle: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the list element.\n     * @group Props\n     */\n    @Input() listStyleClass: string | undefined;\n    /**\n     * When present, it specifies that the element value cannot be changed.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) readonly: boolean | undefined;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) disabled: boolean | undefined;\n    /**\n     * When specified, allows selecting items with checkboxes.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) checkbox: boolean = false;\n    /**\n     * When specified, displays a filter input at header.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) filter: boolean = false;\n    /**\n     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n     * @group Props\n     */\n    @Input() filterBy: string | undefined;\n    /**\n     * Defines how the items are filtered.\n     * @group Props\n     */\n    @Input() filterMatchMode: 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notEquals' | 'in' | 'lt' | 'lte' | 'gt' | 'gte' = 'contains';\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    @Input() filterLocale: string | undefined;\n    /**\n     * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) metaKeySelection: boolean = false;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    @Input() dataKey: string | undefined;\n    /**\n     * Whether header checkbox is shown in multiple mode.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) showToggleAll: boolean = true;\n    /**\n     * Name of the label field of an option.\n     * @group Props\n     */\n    @Input() optionLabel: string | undefined;\n    /**\n     * Name of the value field of an option.\n     * @group Props\n     */\n    @Input() optionValue: string | undefined;\n    /**\n     * Name of the options field of an option group.\n     * @group Props\n     */\n    @Input() optionGroupChildren: string | undefined = 'items';\n    /**\n     * Name of the label field of an option group.\n     * @group Props\n     */\n    @Input() optionGroupLabel: string | undefined = 'label';\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    @Input() optionDisabled: string | undefined;\n    /**\n     * Defines a string that labels the filter input.\n     * @group Props\n     */\n    @Input() ariaFilterLabel: string | undefined;\n    /**\n     * Defines placeholder of the filter input.\n     * @group Props\n     */\n    @Input() filterPlaceHolder: string | undefined;\n    /**\n     * Text to display when filtering does not return any results.\n     * @group Props\n     */\n    @Input() emptyFilterMessage: string | undefined;\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    @Input() emptyMessage: string | undefined;\n    /**\n     * Whether to display options as grouped when nested options are provided.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) group: boolean | undefined;\n    /**\n     * An array of selectitems to display as the available options.\n     * @group Props\n     */\n    @Input() get options(): any[] {\n        return this._options();\n    }\n    set options(val: any[]) {\n        this._options.set(val);\n    }\n    /**\n     * When specified, filter displays with this value.\n     * @group Props\n     */\n    @Input() get filterValue(): string {\n        return this._filterValue();\n    }\n    set filterValue(val: string) {\n        this._filterValue.set(val);\n    }\n    /**\n     * Whether all data is selected.\n     * @group Props\n     */\n    @Input() get selectAll(): boolean | undefined | null {\n        return this._selectAll;\n    }\n    set selectAll(value: boolean | undefined | null) {\n        this._selectAll = value;\n    }\n    /**\n     * Callback to invoke on value change.\n     * @param {ListboxChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    @Output() onChange: EventEmitter<ListboxChangeEvent> = new EventEmitter<ListboxChangeEvent>();\n    /**\n     * Callback to invoke when option is clicked.\n     * @param {ListboxClickEvent} event - Custom click event.\n     * @group Emits\n     */\n    @Output() onClick: EventEmitter<ListboxClickEvent> = new EventEmitter<ListboxClickEvent>();\n    /**\n     * Callback to invoke when option is double clicked.\n     * @param {ListboxDoubleClickEvent} event - Custom double click event.\n     * @group Emits\n     */\n    @Output() onDblClick: EventEmitter<ListboxDoubleClickEvent> = new EventEmitter<ListboxDoubleClickEvent>();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {ListboxFilterEvent} event - Custom filter event.\n     * @group Emits\n     */\n    @Output() onFilter: EventEmitter<ListboxFilterEvent> = new EventEmitter<ListboxFilterEvent>();\n    /**\n     * Callback to invoke when component receives focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n    /**\n     * Callback to invoke when component loses focus.\n     * @param {FocusEvent} event - Blur event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n    /**\n     * Callback to invoke when all data is selected.\n     * @param {ListboxSelectAllChangeEvent} event - Custom select event.\n     * @group Emits\n     */\n    @Output() onSelectAllChange: EventEmitter<ListboxSelectAllChangeEvent> = new EventEmitter<ListboxSelectAllChangeEvent>();\n\n    @ViewChild('headerchkbox') headerCheckboxViewChild: Nullable<ElementRef>;\n\n    @ViewChild('filter') filterViewChild: Nullable<ElementRef>;\n\n    @ViewChild('lastHiddenFocusableElement') lastHiddenFocusableElement: Nullable<ElementRef>;\n\n    @ViewChild('firstHiddenFocusableElement') firstHiddenFocusableElement: Nullable<ElementRef>;\n\n    @ViewChild('scroller') scroller: Nullable<Scroller>;\n\n    @ViewChild('list') listViewChild: Nullable<ElementRef>;\n\n    @ContentChild(Header) headerFacet: Nullable<TemplateRef<any>>;\n\n    @ContentChild(Footer) footerFacet: Nullable<TemplateRef<any>>;\n\n    @ContentChildren(PrimeTemplate) templates!: QueryList<PrimeTemplate>;\n\n    public itemTemplate: TemplateRef<any> | undefined;\n\n    public groupTemplate: TemplateRef<any> | undefined;\n\n    public headerTemplate: TemplateRef<any> | undefined;\n\n    public filterTemplate: TemplateRef<any> | undefined;\n\n    public footerTemplate: TemplateRef<any> | undefined;\n\n    public emptyFilterTemplate: TemplateRef<any> | undefined;\n\n    public emptyTemplate: TemplateRef<any> | undefined;\n\n    filterIconTemplate: TemplateRef<any> | undefined;\n\n    checkIconTemplate: TemplateRef<any> | undefined;\n\n    public _filterValue = signal<string | null | undefined>(null);\n\n    public _filteredOptions: any[] | undefined | null;\n\n    filterOptions: ListboxFilterOptions | undefined;\n\n    public filtered: boolean | undefined | null;\n\n    public value: any | undefined | null;\n\n    public onModelChange: Function = () => {};\n\n    public onModelTouched: Function = () => {};\n\n    public optionTouched: boolean | undefined | null;\n\n    public focus: boolean | undefined | null;\n\n    public headerCheckboxFocus: boolean | undefined | null;\n\n    translationSubscription: Nullable<Subscription>;\n\n    focused: boolean | undefined;\n\n    get containerClass() {\n        return {\n            'p-listbox p-component': true,\n            'p-disabled': this.disabled\n        };\n    }\n\n    get focusedOptionId() {\n        return this.focusedOptionIndex() !== -1 ? `${this.id}_${this.focusedOptionIndex()}` : null;\n    }\n\n    get filterResultMessageText() {\n        return ObjectUtils.isNotEmpty(this.visibleOptions()) ? this.filterMessageText.replaceAll('{0}', this.visibleOptions().length) : this.emptyFilterMessageText;\n    }\n\n    get filterMessageText() {\n        return this.filterMessage || this.config.translation.searchMessage || '';\n    }\n\n    get searchMessageText() {\n        return this.searchMessage || this.config.translation.searchMessage || '';\n    }\n\n    get emptyFilterMessageText() {\n        return this.emptyFilterMessage || this.config.translation.emptySearchMessage || this.config.translation.emptyFilterMessage || '';\n    }\n\n    get selectionMessageText() {\n        return this.selectionMessage || this.config.translation.selectionMessage || '';\n    }\n\n    get emptySelectionMessageText() {\n        return this.emptySelectionMessage || this.config.translation.emptySelectionMessage || '';\n    }\n\n    get selectedMessageText() {\n        return this.hasSelectedOption() ? this.selectionMessageText.replaceAll('{0}', this.multiple ? this.modelValue().length : '1') : this.emptySelectionMessageText;\n    }\n\n    get ariaSetSize() {\n        return this.visibleOptions().filter((option) => !this.isOptionGroup(option)).length;\n    }\n\n    get virtualScrollerDisabled() {\n        return !this.virtualScroll;\n    }\n\n    get searchFields() {\n        return this.filterFields || [this.optionLabel];\n    }\n\n    get toggleAllAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria[this.allSelected() ? 'selectAll' : 'unselectAll'] : undefined;\n    }\n\n    searchValue: string | undefined;\n\n    searchTimeout: any;\n\n    _selectAll: boolean | undefined | null = null;\n\n    _options = signal<any>(null);\n\n    startRangeIndex = signal<number>(-1);\n\n    focusedOptionIndex = signal<number>(-1);\n\n    modelValue = signal<any>(null);\n\n    visibleOptions = computed(() => {\n        const options = this.group ? this.flatOptions(this._options()) : this._options() || [];\n        const filterValue = this._filterValue();\n\n        if (this.searchFields[0] === undefined) {\n            return filterValue ? options.filter((option) => option.toString().toLocaleLowerCase(this.filterLocale).indexOf(filterValue.toLocaleLowerCase(this.filterLocale).trim()) !== -1) : options;\n        } else return filterValue ? this.filterService.filter(options, this.searchFields, filterValue, this.filterMatchMode, this.filterLocale) : options;\n    });\n\n    constructor(public el: ElementRef, public cd: ChangeDetectorRef, public filterService: FilterService, public config: PrimeNGConfig, private renderer: Renderer2) {}\n\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n        this.translationSubscription = this.config.translationObserver.subscribe(() => {\n            this.cd.markForCheck();\n        });\n\n        this.autoUpdateModel();\n\n        if (this.filterBy) {\n            this.filterOptions = {\n                filter: (value) => this.onFilterChange(value),\n                reset: () => this.resetFilter()\n            };\n        }\n    }\n\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'group':\n                    this.groupTemplate = item.template;\n                    break;\n\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n\n                case 'filter':\n                    this.filterTemplate = item.template;\n                    break;\n\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n\n                case 'empty':\n                    this.emptyTemplate = item.template;\n                    break;\n\n                case 'emptyfilter':\n                    this.emptyFilterTemplate = item.template;\n                    break;\n\n                case 'filtericon':\n                    this.filterIconTemplate = item.template;\n                    break;\n\n                case 'checkicon':\n                    this.checkIconTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    writeValue(value: any): void {\n        this.value = value;\n        this.modelValue.set(this.value);\n        this.cd.markForCheck();\n    }\n\n    registerOnChange(fn: Function): void {\n        this.onModelChange = fn;\n    }\n\n    registerOnTouched(fn: Function): void {\n        this.onModelTouched = fn;\n    }\n\n    setDisabledState(val: boolean): void {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n\n    flatOptions(options) {\n        return (options || []).reduce((result, option, index) => {\n            result.push({ optionGroup: option, group: true, index });\n\n            const optionGroupChildren = this.getOptionGroupChildren(option);\n\n            optionGroupChildren && optionGroupChildren.forEach((o) => result.push(o));\n\n            return result;\n        }, []);\n    }\n\n    autoUpdateModel() {\n        if (this.selectOnFocus && this.autoOptionFocus && !this.hasSelectedOption() && !this.multiple) {\n            const focusedOptionIndex = this.findFirstFocusedOptionIndex();\n            this.focusedOptionIndex.set(focusedOptionIndex);\n            this.onOptionSelect(null, this.visibleOptions()[this.focusedOptionIndex()]);\n        }\n    }\n    /**\n     * Updates the model value.\n     * @group Method\n     */\n    public updateModel(value, event?) {\n        this.value = value;\n        this.modelValue.set(value);\n        this.onModelChange(value);\n\n        this.onChange.emit({ originalEvent: event, value: this.value });\n    }\n\n    removeOption(option) {\n        return this.modelValue().filter((val) => !ObjectUtils.equals(val, this.getOptionValue(option), this.equalityKey()));\n    }\n\n    onOptionSelect(event, option, index = -1) {\n        if (this.disabled || this.isOptionDisabled(option) || this.readonly) {\n            return;\n        }\n\n        event && this.onClick.emit({ originalEvent: event, option, value: this.value });\n\n        this.multiple ? this.onOptionSelectMultiple(event, option) : this.onOptionSelectSingle(event, option);\n        this.optionTouched = false;\n        index !== -1 && this.focusedOptionIndex.set(index);\n    }\n\n    onOptionSelectMultiple(event, option) {\n        let selected = this.isSelected(option);\n        let value = null;\n        let metaSelection = this.optionTouched ? false : this.metaKeySelection;\n\n        if (metaSelection) {\n            let metaKey = event.metaKey || event.ctrlKey;\n\n            if (selected) {\n                value = metaKey ? this.removeOption(option) : [this.getOptionValue(option)];\n            } else {\n                value = metaKey ? this.modelValue() || [] : [];\n                value = [...value, this.getOptionValue(option)];\n            }\n        } else {\n            value = selected ? this.removeOption(option) : [...(this.modelValue() || []), this.getOptionValue(option)];\n        }\n\n        this.updateModel(value, event);\n    }\n\n    onOptionSelectSingle(event, option) {\n        let selected = this.isSelected(option);\n        let valueChanged = false;\n        let value = null;\n        let metaSelection = this.optionTouched ? false : this.metaKeySelection;\n\n        if (metaSelection) {\n            let metaKey = event.metaKey || event.ctrlKey;\n\n            if (selected) {\n                if (metaKey) {\n                    value = null;\n                    valueChanged = true;\n                }\n            } else {\n                value = this.getOptionValue(option);\n                valueChanged = true;\n            }\n        } else {\n            value = selected ? null : this.getOptionValue(option);\n            valueChanged = true;\n        }\n\n        if (valueChanged) {\n            this.updateModel(value, event);\n        }\n    }\n\n    onOptionSelectRange(event, start = -1, end = -1) {\n        start === -1 && (start = this.findNearestSelectedOptionIndex(end, true));\n        end === -1 && (end = this.findNearestSelectedOptionIndex(start));\n\n        if (start !== -1 && end !== -1) {\n            const rangeStart = Math.min(start, end);\n            const rangeEnd = Math.max(start, end);\n            const value = this.visibleOptions()\n                .slice(rangeStart, rangeEnd + 1)\n                .filter((option) => this.isValidOption(option))\n                .map((option) => this.getOptionValue(option));\n\n            this.updateModel(value, event);\n        }\n    }\n\n    onToggleAll(event) {\n        if (this.disabled || this.readonly) {\n            return;\n        }\n        DomHandler.focus(this.headerCheckboxViewChild.nativeElement);\n\n        if (this.selectAll !== null) {\n            this.onSelectAllChange.emit({\n                originalEvent: event,\n                checked: !this.allSelected()\n            });\n        } else {\n            const value = this.allSelected()\n                ? []\n                : this.visibleOptions()\n                      .filter((option) => this.isValidOption(option))\n                      .map((option) => this.getOptionValue(option));\n\n            this.updateModel(value, event);\n        }\n\n        event.preventDefault();\n        // event.stopPropagation();\n    }\n\n    allSelected() {\n        return this.selectAll !== null ? this.selectAll : ObjectUtils.isNotEmpty(this.visibleOptions()) && this.visibleOptions().every((option) => this.isOptionGroup(option) || this.isOptionDisabled(option) || this.isSelected(option));\n    }\n\n    onOptionTouchEnd() {\n        if (this.disabled) {\n            return;\n        }\n\n        this.optionTouched = true;\n    }\n\n    onOptionMouseDown(event: MouseEvent, index: number) {\n        this.changeFocusedOptionIndex(event, index);\n    }\n\n    onOptionMouseEnter(event: MouseEvent, index: number) {\n        if (this.focusOnHover) {\n            this.changeFocusedOptionIndex(event, index);\n        }\n    }\n\n    onOptionDoubleClick(event: MouseEvent, option: any) {\n        if (this.disabled || this.isOptionDisabled(option) || this.readonly) {\n            return;\n        }\n\n        this.onDblClick.emit({\n            originalEvent: event,\n            option: option,\n            value: this.value\n        });\n    }\n\n    onFirstHiddenFocus(event: FocusEvent) {\n        DomHandler.focus(this.listViewChild.nativeElement);\n        const firstFocusableEl = DomHandler.getFirstFocusableElement(this.el.nativeElement, ':not([data-p-hidden-focusable=\"true\"])');\n        this.lastHiddenFocusableElement.nativeElement.tabIndex = ObjectUtils.isEmpty(firstFocusableEl) ? '-1' : undefined;\n        this.firstHiddenFocusableElement.nativeElement.tabIndex = -1;\n    }\n\n    onLastHiddenFocus(event: FocusEvent) {\n        const relatedTarget = event.relatedTarget;\n\n        if (relatedTarget === this.listViewChild.nativeElement) {\n            const firstFocusableEl = DomHandler.getFirstFocusableElement(this.el.nativeElement, ':not(.p-hidden-focusable)');\n\n            DomHandler.focus(firstFocusableEl);\n            this.firstHiddenFocusableElement.nativeElement.tabIndex = undefined;\n        } else {\n            DomHandler.focus(this.firstHiddenFocusableElement.nativeElement);\n        }\n        this.lastHiddenFocusableElement.nativeElement.tabIndex = -1;\n    }\n\n    onFocusout(event: FocusEvent) {\n        if (!this.el.nativeElement.contains(event.relatedTarget) && this.lastHiddenFocusableElement && this.firstHiddenFocusableElement) {\n            this.firstHiddenFocusableElement.nativeElement.tabIndex = this.lastHiddenFocusableElement.nativeElement.tabIndex = undefined;\n        }\n    }\n\n    onListFocus(event: FocusEvent) {\n        this.focused = true;\n        const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n        this.focusedOptionIndex.set(focusedOptionIndex);\n        this.onFocus.emit(event);\n    }\n\n    onListBlur(event: FocusEvent) {\n        this.focused = false;\n        this.focusedOptionIndex.set(-1);\n        this.startRangeIndex.set(-1);\n        this.searchValue = '';\n    }\n\n    onHeaderCheckboxFocus(event) {\n        this.headerCheckboxFocus = true;\n    }\n\n    onHeaderCheckboxBlur() {\n        this.headerCheckboxFocus = false;\n    }\n\n    onHeaderCheckboxKeyDown(event) {\n        if (this.disabled) {\n            event.preventDefault();\n\n            return;\n        }\n\n        switch (event.code) {\n            case 'Space':\n                this.onToggleAll(event);\n                break;\n            case 'Enter':\n                this.onToggleAll(event);\n                break;\n            case 'Tab':\n                this.onHeaderCheckboxTabKeyDown(event);\n                break;\n            default:\n                break;\n        }\n    }\n\n    onHeaderCheckboxTabKeyDown(event) {\n        DomHandler.focus(this.listViewChild.nativeElement);\n        event.preventDefault();\n    }\n\n    onFilterChange(event: KeyboardEvent) {\n        let value: string = (event.target as HTMLInputElement).value?.trim();\n        this._filterValue.set(value);\n        this.focusedOptionIndex.set(-1);\n        this.startRangeIndex.set(-1);\n        this.onFilter.emit({ originalEvent: event, filter: this._filterValue() });\n\n        !this.virtualScrollerDisabled && this.scroller.scrollToIndex(0);\n    }\n\n    onFilterBlur(event: FocusEvent) {\n        this.focusedOptionIndex.set(-1);\n        this.startRangeIndex.set(-1);\n    }\n\n    onListKeyDown(event: KeyboardEvent) {\n        const metaKey = event.metaKey || event.ctrlKey;\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'PageDown':\n                this.onPageDownKey(event);\n                break;\n\n            case 'PageUp':\n                this.onPageUpKey(event);\n                break;\n\n            case 'Enter':\n            case 'Space':\n            case 'NumpadEnter':\n                this.onSpaceKey(event);\n                break;\n\n            case 'Tab':\n                //NOOP\n                break;\n\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                this.onShiftKey();\n                break;\n\n            default:\n                if (this.multiple && event.code === 'KeyA' && metaKey) {\n                    const value = this.visibleOptions()\n                        .filter((option) => this.isValidOption(option))\n                        .map((option) => this.getOptionValue(option));\n\n                    this.updateModel(value, event);\n\n                    event.preventDefault();\n                    break;\n                }\n\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchOptions(event, event.key);\n                    event.preventDefault();\n                }\n\n                break;\n        }\n    }\n\n    onFilterKeyDown(event: KeyboardEvent) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'ArrowLeft':\n            case 'ArrowRight':\n                this.onArrowLeftKey(event, true);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event, true);\n                break;\n\n            case 'End':\n                this.onEndKey(event, true);\n                break;\n\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                this.onShiftKey();\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onArrowDownKey(event: KeyboardEvent) {\n        const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.findFirstFocusedOptionIndex();\n\n        if (this.multiple && event.shiftKey) {\n            this.onOptionSelectRange(event, this.startRangeIndex(), optionIndex);\n        }\n\n        this.changeFocusedOptionIndex(event, optionIndex);\n        event.preventDefault();\n    }\n\n    onArrowUpKey(event: KeyboardEvent) {\n        const optionIndex = this.focusedOptionIndex() !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex()) : this.findLastFocusedOptionIndex();\n\n        if (this.multiple && event.shiftKey) {\n            this.onOptionSelectRange(event, optionIndex, this.startRangeIndex());\n        }\n\n        this.changeFocusedOptionIndex(event, optionIndex);\n        event.preventDefault();\n    }\n\n    onArrowLeftKey(event: KeyboardEvent, pressedInInputText = false) {\n        pressedInInputText && this.focusedOptionIndex.set(-1);\n    }\n\n    onHomeKey(event: KeyboardEvent, pressedInInputText: boolean = false) {\n        if (pressedInInputText) {\n            (event.currentTarget as HTMLInputElement).setSelectionRange(0, 0);\n            this.focusedOptionIndex.set(-1);\n        } else {\n            let metaKey = event.metaKey || event.ctrlKey;\n            let optionIndex = this.findFirstOptionIndex();\n\n            if (this.multiple && event.shiftKey && metaKey) {\n                this.onOptionSelectRange(event, optionIndex, this.startRangeIndex());\n            }\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n        }\n\n        event.preventDefault();\n    }\n\n    onEndKey(event: KeyboardEvent, pressedInInputText: boolean = false) {\n        if (pressedInInputText) {\n            const target = event.currentTarget as HTMLInputElement;\n            const len = target.value.length;\n\n            target.setSelectionRange(len, len);\n            this.focusedOptionIndex.set(-1);\n        } else {\n            let metaKey = event.metaKey || event.ctrlKey;\n            let optionIndex = this.findLastOptionIndex();\n\n            if (this.multiple && event.shiftKey && metaKey) {\n                this.onOptionSelectRange(event, this.startRangeIndex(), optionIndex);\n            }\n\n            this.changeFocusedOptionIndex(event, optionIndex);\n        }\n\n        event.preventDefault();\n    }\n\n    onPageDownKey(event: KeyboardEvent) {\n        this.scrollInView(0);\n        event.preventDefault();\n    }\n\n    onPageUpKey(event: KeyboardEvent) {\n        this.scrollInView(this.visibleOptions().length - 1);\n        event.preventDefault();\n    }\n\n    onEnterKey(event) {\n        if (this.focusedOptionIndex() !== -1) {\n            if (this.multiple && event.shiftKey) this.onOptionSelectRange(event, this.focusedOptionIndex());\n            else this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n        }\n\n        event.preventDefault();\n    }\n\n    onSpaceKey(event: KeyboardEvent) {\n        this.onEnterKey(event);\n    }\n\n    onShiftKey() {\n        const focusedOptionIndex = this.focusedOptionIndex();\n        this.startRangeIndex.set(focusedOptionIndex);\n    }\n\n    getOptionGroupChildren(optionGroup) {\n        return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n\n    getOptionGroupLabel(optionGroup: any) {\n        return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label !== undefined ? optionGroup.label : optionGroup;\n    }\n\n    getOptionLabel(option) {\n        return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option.label != undefined ? option.label : option;\n    }\n\n    getOptionIndex(index, scrollerOptions) {\n        return this.virtualScrollerDisabled ? index : scrollerOptions && scrollerOptions.getItemOptions(index)['index'];\n    }\n\n    getOptionValue(option: any) {\n        return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : !this.optionLabel && option && option.value !== undefined ? option.value : option;\n    }\n\n    getAriaPosInset(index: number) {\n        return (\n            (this.optionGroupLabel\n                ? index -\n                  this.visibleOptions()\n                      .slice(0, index)\n                      .filter((option) => this.isOptionGroup(option)).length\n                : index) + 1\n        );\n    }\n\n    hasSelectedOption() {\n        return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n\n    isOptionGroup(option) {\n        return this.optionGroupLabel && option.optionGroup && option.group;\n    }\n\n    changeFocusedOptionIndex(event, index) {\n        if (this.focusedOptionIndex() !== index) {\n            this.focusedOptionIndex.set(index);\n            this.scrollInView();\n\n            if (this.selectOnFocus && !this.multiple) {\n                this.onOptionSelect(event, this.visibleOptions()[index]);\n            }\n        }\n    }\n\n    searchOptions(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let optionIndex = -1;\n        let matched = false;\n\n        if (this.focusedOptionIndex() !== -1) {\n            optionIndex = this.visibleOptions()\n                .slice(this.focusedOptionIndex())\n                .findIndex((option) => this.isOptionMatched(option));\n            optionIndex =\n                optionIndex === -1\n                    ? this.visibleOptions()\n                          .slice(0, this.focusedOptionIndex())\n                          .findIndex((option) => this.isOptionMatched(option))\n                    : optionIndex + this.focusedOptionIndex();\n        } else {\n            optionIndex = this.visibleOptions().findIndex((option) => this.isOptionMatched(option));\n        }\n\n        if (optionIndex !== -1) {\n            matched = true;\n        }\n\n        if (optionIndex === -1 && this.focusedOptionIndex() === -1) {\n            optionIndex = this.findFirstFocusedOptionIndex();\n        }\n\n        if (optionIndex !== -1) {\n            this.changeFocusedOptionIndex(event, optionIndex);\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n\n    isOptionMatched(option) {\n        return this.isValidOption(option) && this.getOptionLabel(option).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale));\n    }\n\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n        const element = DomHandler.findSingle(this.listViewChild.nativeElement, `li[id=\"${id}\"]`);\n\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        } else if (!this.virtualScrollerDisabled) {\n            this.virtualScroll && this.scroller.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex());\n        }\n    }\n\n    findFirstOptionIndex() {\n        return this.visibleOptions().findIndex((option) => this.isValidOption(option));\n    }\n\n    findLastOptionIndex() {\n        return ObjectUtils.findLastIndex(this.visibleOptions(), (option) => this.isValidOption(option));\n    }\n\n    findFirstFocusedOptionIndex() {\n        const selectedIndex = this.findFirstSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n\n    findLastFocusedOptionIndex() {\n        const selectedIndex = this.findLastSelectedOptionIndex();\n\n        return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n\n    findLastSelectedOptionIndex() {\n        return this.hasSelectedOption() ? ObjectUtils.findLastIndex(this.visibleOptions(), (option) => this.isValidSelectedOption(option)) : -1;\n    }\n\n    findNextOptionIndex(index) {\n        const matchedOptionIndex =\n            index < this.visibleOptions().length - 1\n                ? this.visibleOptions()\n                      .slice(index + 1)\n                      .findIndex((option) => this.isValidOption(option))\n                : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n\n    findNextSelectedOptionIndex(index) {\n        const matchedOptionIndex =\n            this.hasSelectedOption() && index < this.visibleOptions().length - 1\n                ? this.visibleOptions()\n                      .slice(index + 1)\n                      .findIndex((option) => this.isValidSelectedOption(option))\n                : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : -1;\n    }\n\n    findPrevSelectedOptionIndex(index) {\n        const matchedOptionIndex = this.hasSelectedOption() && index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), (option) => this.isValidSelectedOption(option)) : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : -1;\n    }\n\n    findFirstSelectedOptionIndex() {\n        return this.hasSelectedOption() ? this.visibleOptions().findIndex((option) => this.isValidSelectedOption(option)) : -1;\n    }\n\n    findPrevOptionIndex(index) {\n        const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), (option) => this.isValidOption(option)) : -1;\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n\n    findNearestSelectedOptionIndex(index, firstCheckUp = false) {\n        let matchedOptionIndex = -1;\n\n        if (this.hasSelectedOption()) {\n            if (firstCheckUp) {\n                matchedOptionIndex = this.findPrevSelectedOptionIndex(index);\n                matchedOptionIndex = matchedOptionIndex === -1 ? this.findNextSelectedOptionIndex(index) : matchedOptionIndex;\n            } else {\n                matchedOptionIndex = this.findNextSelectedOptionIndex(index);\n                matchedOptionIndex = matchedOptionIndex === -1 ? this.findPrevSelectedOptionIndex(index) : matchedOptionIndex;\n            }\n        }\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n\n    equalityKey() {\n        return this.optionValue ? null : this.dataKey;\n    }\n\n    isValidSelectedOption(option) {\n        return this.isValidOption(option) && this.isSelected(option);\n    }\n\n    isOptionDisabled(option: any) {\n        return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : false;\n    }\n\n    isSelected(option) {\n        const optionValue = this.getOptionValue(option);\n\n        if (this.multiple) return (this.modelValue() || []).some((value) => ObjectUtils.equals(value, optionValue, this.equalityKey()));\n        else return ObjectUtils.equals(this.modelValue(), optionValue, this.equalityKey());\n    }\n\n    isValidOption(option) {\n        return option && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n    }\n\n    isEmpty() {\n        return !this._options()?.length || !this.visibleOptions()?.length;\n    }\n\n    hasFilter() {\n        return this._filterValue() && this._filterValue().trim().length > 0;\n    }\n\n    resetFilter() {\n        if (this.filterViewChild && this.filterViewChild.nativeElement) {\n            this.filterViewChild.nativeElement.value = '';\n        }\n\n        this._filterValue.set(null);\n    }\n\n    ngOnDestroy() {\n        if (this.translationSubscription) {\n            this.translationSubscription.unsubscribe();\n        }\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, SharedModule, RippleModule, ScrollerModule, SearchIcon, CheckIcon],\n    exports: [Listbox, SharedModule, ScrollerModule],\n    declarations: [Listbox]\n})\nexport class ListboxModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAsCa,MAAA,sBAAsB,GAAQ;AACvC,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,OAAO,CAAC;AACtC,IAAA,KAAK,EAAE,IAAI;EACb;AACF;;;AAGG;MA0MU,OAAO,CAAA;AAyaG,IAAA,EAAA,CAAA;AAAuB,IAAA,EAAA,CAAA;AAA8B,IAAA,aAAA,CAAA;AAAqC,IAAA,MAAA,CAAA;AAA+B,IAAA,QAAA,CAAA;AAxa5I;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;;AAIG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;;AAIG;AACM,IAAA,qBAAqB,CAAqB;AACnD;;;;AAIG;AACM,IAAA,gBAAgB,CAAqB;AAC9C;;;AAGG;IACqC,eAAe,GAAwB,IAAI,CAAC;AACpF;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;AACqC,IAAA,YAAY,CAAsB;AAC1E;;;AAGG;AACM,IAAA,aAAa,CAAqB;AAC3C;;;AAGG;AACM,IAAA,YAAY,CAAoB;AACzC;;;AAGG;IACqC,IAAI,GAAY,KAAK,CAAC;AAC9D;;;AAGG;AACqC,IAAA,aAAa,CAAsB;AAC3E;;;AAGG;AACoC,IAAA,qBAAqB,CAAqB;AACjF;;;AAGG;AACM,IAAA,oBAAoB,CAA8B;AAC3D;;;AAGG;IACM,YAAY,GAAW,OAAO,CAAC;AACxC;;;AAGG;IACoC,QAAQ,GAAuB,CAAC,CAAC;AACxE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,SAAS,CAA8C;AAChE;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACqC,QAAQ,GAAY,KAAK,CAAC;AAClE;;;AAGG;IACqC,MAAM,GAAY,KAAK,CAAC;AAChE;;;AAGG;AACM,IAAA,QAAQ,CAAqB;AACtC;;;AAGG;IACM,eAAe,GAAyG,UAAU,CAAC;AAC5I;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;IACqC,gBAAgB,GAAY,KAAK,CAAC;AAC1E;;;AAGG;AACM,IAAA,OAAO,CAAqB;AACrC;;;AAGG;IACqC,aAAa,GAAY,IAAI,CAAC;AACtE;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;AACM,IAAA,WAAW,CAAqB;AACzC;;;AAGG;IACM,mBAAmB,GAAuB,OAAO,CAAC;AAC3D;;;AAGG;IACM,gBAAgB,GAAuB,OAAO,CAAC;AACxD;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,eAAe,CAAqB;AAC7C;;;AAGG;AACM,IAAA,iBAAiB,CAAqB;AAC/C;;;AAGG;AACM,IAAA,kBAAkB,CAAqB;AAChD;;;AAGG;AACM,IAAA,YAAY,CAAqB;AAC1C;;;AAGG;AACqC,IAAA,KAAK,CAAsB;AACnE;;;AAGG;AACH,IAAA,IAAa,OAAO,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;KAC1B;IACD,IAAI,OAAO,CAAC,GAAU,EAAA;AAClB,QAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC1B;AACD;;;AAGG;AACH,IAAA,IAAa,WAAW,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;KAC9B;IACD,IAAI,WAAW,CAAC,GAAW,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9B;AACD;;;AAGG;AACH,IAAA,IAAa,SAAS,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;IACD,IAAI,SAAS,CAAC,KAAiC,EAAA;AAC3C,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;KAC3B;AACD;;;;AAIG;AACO,IAAA,QAAQ,GAAqC,IAAI,YAAY,EAAsB,CAAC;AAC9F;;;;AAIG;AACO,IAAA,OAAO,GAAoC,IAAI,YAAY,EAAqB,CAAC;AAC3F;;;;AAIG;AACO,IAAA,UAAU,GAA0C,IAAI,YAAY,EAA2B,CAAC;AAC1G;;;;AAIG;AACO,IAAA,QAAQ,GAAqC,IAAI,YAAY,EAAsB,CAAC;AAC9F;;;;AAIG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC7E;;;;AAIG;AACO,IAAA,MAAM,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC5E;;;;AAIG;AACO,IAAA,iBAAiB,GAA8C,IAAI,YAAY,EAA+B,CAAC;AAE9F,IAAA,uBAAuB,CAAuB;AAEpD,IAAA,eAAe,CAAuB;AAElB,IAAA,0BAA0B,CAAuB;AAEhD,IAAA,2BAA2B,CAAuB;AAErE,IAAA,QAAQ,CAAqB;AAEjC,IAAA,aAAa,CAAuB;AAEjC,IAAA,WAAW,CAA6B;AAExC,IAAA,WAAW,CAA6B;AAE9B,IAAA,SAAS,CAA4B;AAE9D,IAAA,YAAY,CAA+B;AAE3C,IAAA,aAAa,CAA+B;AAE5C,IAAA,cAAc,CAA+B;AAE7C,IAAA,cAAc,CAA+B;AAE7C,IAAA,cAAc,CAA+B;AAE7C,IAAA,mBAAmB,CAA+B;AAElD,IAAA,aAAa,CAA+B;AAEnD,IAAA,kBAAkB,CAA+B;AAEjD,IAAA,iBAAiB,CAA+B;AAEzC,IAAA,YAAY,GAAG,MAAM,CAA4B,IAAI,CAAC,CAAC;AAEvD,IAAA,gBAAgB,CAA2B;AAElD,IAAA,aAAa,CAAmC;AAEzC,IAAA,QAAQ,CAA6B;AAErC,IAAA,KAAK,CAAyB;AAE9B,IAAA,aAAa,GAAa,MAAK,GAAG,CAAC;AAEnC,IAAA,cAAc,GAAa,MAAK,GAAG,CAAC;AAEpC,IAAA,aAAa,CAA6B;AAE1C,IAAA,KAAK,CAA6B;AAElC,IAAA,mBAAmB,CAA6B;AAEvD,IAAA,uBAAuB,CAAyB;AAEhD,IAAA,OAAO,CAAsB;AAE7B,IAAA,IAAI,cAAc,GAAA;QACd,OAAO;AACH,YAAA,uBAAuB,EAAE,IAAI;YAC7B,YAAY,EAAE,IAAI,CAAC,QAAQ;SAC9B,CAAC;KACL;AAED,IAAA,IAAI,eAAe,GAAA;QACf,OAAO,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,GAAG,IAAI,CAAC;KAC9F;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;KAC/J;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,IAAI,EAAE,CAAC;KAC5E;AAED,IAAA,IAAI,iBAAiB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,IAAI,EAAE,CAAC;KAC5E;AAED,IAAA,IAAI,sBAAsB,GAAA;QACtB,OAAO,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC;KACpI;AAED,IAAA,IAAI,oBAAoB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,IAAI,EAAE,CAAC;KAClF;AAED,IAAA,IAAI,yBAAyB,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,qBAAqB,IAAI,EAAE,CAAC;KAC5F;AAED,IAAA,IAAI,mBAAmB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;KAClK;AAED,IAAA,IAAI,WAAW,GAAA;QACX,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;KACvF;AAED,IAAA,IAAI,uBAAuB,GAAA;AACvB,QAAA,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;KAC9B;AAED,IAAA,IAAI,YAAY,GAAA;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KAClD;AAED,IAAA,IAAI,kBAAkB,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,WAAW,GAAG,aAAa,CAAC,GAAG,SAAS,CAAC;KACpI;AAED,IAAA,WAAW,CAAqB;AAEhC,IAAA,aAAa,CAAM;IAEnB,UAAU,GAA+B,IAAI,CAAC;AAE9C,IAAA,QAAQ,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAE7B,IAAA,eAAe,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC,CAAC;AAErC,IAAA,kBAAkB,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC,CAAC;AAExC,IAAA,UAAU,GAAG,MAAM,CAAM,IAAI,CAAC,CAAC;AAE/B,IAAA,cAAc,GAAG,QAAQ,CAAC,MAAK;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;AACvF,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAExC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACpC,OAAO,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAC7L,SAAA;;AAAM,YAAA,OAAO,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC;AACtJ,KAAC,CAAC,CAAC;IAEH,WAAmB,CAAA,EAAc,EAAS,EAAqB,EAAS,aAA4B,EAAS,MAAqB,EAAU,QAAmB,EAAA;QAA5I,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAS,IAAa,CAAA,aAAA,GAAb,aAAa,CAAe;QAAS,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAAI;IAEnK,QAAQ,GAAA;QACJ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAK;AAC1E,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,aAAa,GAAG;gBACjB,MAAM,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AAC7C,gBAAA,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;aAClC,CAAC;AACL,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC5B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AAEV,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,YAAY;AACb,oBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACxC,MAAM;AAEV,gBAAA,KAAK,WAAW;AACZ,oBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACvC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,gBAAgB,CAAC,EAAY,EAAA;AACzB,QAAA,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;KAC3B;AAED,IAAA,iBAAiB,CAAC,EAAY,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;KAC5B;AAED,IAAA,gBAAgB,CAAC,GAAY,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,OAAO,EAAA;AACf,QAAA,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,KAAI;AACpD,YAAA,MAAM,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAEzD,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;AAEhE,YAAA,mBAAmB,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAE1E,YAAA,OAAO,MAAM,CAAC;SACjB,EAAE,EAAE,CAAC,CAAC;KACV;IAED,eAAe,GAAA;AACX,QAAA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC3F,YAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAC9D,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AAC/E,SAAA;KACJ;AACD;;;AAGG;IACI,WAAW,CAAC,KAAK,EAAE,KAAM,EAAA;AAC5B,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAE1B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;KACnE;AAED,IAAA,YAAY,CAAC,MAAM,EAAA;AACf,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KACvH;IAED,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,EAAA;AACpC,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjE,OAAO;AACV,SAAA;QAED,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAEhF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtG,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KACtD;IAED,sBAAsB,CAAC,KAAK,EAAE,MAAM,EAAA;QAChC,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAEvE,QAAA,IAAI,aAAa,EAAE;YACf,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;AAE7C,YAAA,IAAI,QAAQ,EAAE;gBACV,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/E,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAC/C,gBAAA,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AACnD,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9G,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KAClC;IAED,oBAAoB,CAAC,KAAK,EAAE,MAAM,EAAA;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAEvE,QAAA,IAAI,aAAa,EAAE;YACf,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;AAE7C,YAAA,IAAI,QAAQ,EAAE;AACV,gBAAA,IAAI,OAAO,EAAE;oBACT,KAAK,GAAG,IAAI,CAAC;oBACb,YAAY,GAAG,IAAI,CAAC;AACvB,iBAAA;AACJ,aAAA;AAAM,iBAAA;AACH,gBAAA,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACpC,YAAY,GAAG,IAAI,CAAC;AACvB,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,KAAK,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtD,YAAY,GAAG,IAAI,CAAC;AACvB,SAAA;AAED,QAAA,IAAI,YAAY,EAAE;AACd,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;KACJ;IAED,mBAAmB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAA;AAC3C,QAAA,KAAK,KAAK,CAAC,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,8BAA8B,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACzE,QAAA,GAAG,KAAK,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,CAAC;QAEjE,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACtC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE;AAC9B,iBAAA,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG,CAAC,CAAC;AAC/B,iBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC9C,iBAAA,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAElD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAK,EAAA;AACb,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,OAAO;AACV,SAAA;QACD,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;AAE7D,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AACzB,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AACxB,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE;AAC/B,aAAA,CAAC,CAAC;AACN,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE;AAC5B,kBAAE,EAAE;AACJ,kBAAE,IAAI,CAAC,cAAc,EAAE;AAChB,qBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC9C,qBAAA,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAExD,YAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAClC,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;;KAE1B;IAED,WAAW,GAAA;QACP,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;KACtO;IAED,gBAAgB,GAAA;QACZ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC7B;IAED,iBAAiB,CAAC,KAAiB,EAAE,KAAa,EAAA;AAC9C,QAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KAC/C;IAED,kBAAkB,CAAC,KAAiB,EAAE,KAAa,EAAA;QAC/C,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/C,SAAA;KACJ;IAED,mBAAmB,CAAC,KAAiB,EAAE,MAAW,EAAA;AAC9C,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjE,OAAO;AACV,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACjB,YAAA,aAAa,EAAE,KAAK;AACpB,YAAA,MAAM,EAAE,MAAM;YACd,KAAK,EAAE,IAAI,CAAC,KAAK;AACpB,SAAA,CAAC,CAAC;KACN;AAED,IAAA,kBAAkB,CAAC,KAAiB,EAAA;QAChC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AACnD,QAAA,MAAM,gBAAgB,GAAG,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,wCAAwC,CAAC,CAAC;QAC9H,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC;QAClH,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;KAChE;AAED,IAAA,iBAAiB,CAAC,KAAiB,EAAA;AAC/B,QAAA,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;AAE1C,QAAA,IAAI,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;AACpD,YAAA,MAAM,gBAAgB,GAAG,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,2BAA2B,CAAC,CAAC;AAEjH,YAAA,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACnC,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,QAAQ,GAAG,SAAS,CAAC;AACvE,SAAA;AAAM,aAAA;YACH,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;AACpE,SAAA;QACD,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;KAC/D;AAED,IAAA,UAAU,CAAC,KAAiB,EAAA;QACxB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,2BAA2B,EAAE;AAC7H,YAAA,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,QAAQ,GAAG,SAAS,CAAC;AAChI,SAAA;KACJ;AAED,IAAA,WAAW,CAAC,KAAiB,EAAA;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,CAAC;AACzJ,QAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,UAAU,CAAC,KAAiB,EAAA;AACxB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;KACzB;AAED,IAAA,qBAAqB,CAAC,KAAK,EAAA;AACvB,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;KACnC;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;KACpC;AAED,IAAA,uBAAuB,CAAC,KAAK,EAAA;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,OAAO;AACV,SAAA;QAED,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AACV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AACV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM;AACV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,0BAA0B,CAAC,KAAK,EAAA;QAC5B,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QACnD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;QAC/B,IAAI,KAAK,GAAY,KAAK,CAAC,MAA2B,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACrE,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAE1E,QAAA,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;KACnE;AAED,IAAA,YAAY,CAAC,KAAiB,EAAA;QAC1B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAChC;AAED,IAAA,aAAa,CAAC,KAAoB,EAAA;QAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QAE/C,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,UAAU;AACX,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,OAAO,CAAC;AACb,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,KAAK;;gBAEN,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;gBACb,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM;AAEV,YAAA;gBACI,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,OAAO,EAAE;AACnD,oBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE;AAC9B,yBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC9C,yBAAA,GAAG,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAElD,oBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;oBAE/B,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,MAAM;AACT,iBAAA;gBAED,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;oBACrC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;QAChC,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACjC,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;gBACb,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAEhJ,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;AACjC,YAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,WAAW,CAAC,CAAC;AACxE,SAAA;AAED,QAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAClD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAoB,EAAA;AAC7B,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAE/I,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;AACjC,YAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;AACxE,SAAA;AAED,QAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAClD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAoB,EAAE,kBAAkB,GAAG,KAAK,EAAA;QAC3D,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACzD;AAED,IAAA,SAAS,CAAC,KAAoB,EAAE,kBAAA,GAA8B,KAAK,EAAA;AAC/D,QAAA,IAAI,kBAAkB,EAAE;YACnB,KAAK,CAAC,aAAkC,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,SAAA;AAAM,aAAA;YACH,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;AAC7C,YAAA,IAAI,WAAW,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE9C,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,EAAE;AAC5C,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;AACxE,aAAA;AAED,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAE,kBAAA,GAA8B,KAAK,EAAA;AAC9D,QAAA,IAAI,kBAAkB,EAAE;AACpB,YAAA,MAAM,MAAM,GAAG,KAAK,CAAC,aAAiC,CAAC;AACvD,YAAA,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAEhC,YAAA,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,SAAA;AAAM,aAAA;YACH,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;AAC7C,YAAA,IAAI,WAAW,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE7C,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,EAAE;AAC5C,gBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,WAAW,CAAC,CAAC;AACxE,aAAA;AAED,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,aAAa,CAAC,KAAoB,EAAA;AAC9B,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrB,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;AAC5B,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,YAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ;gBAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;;AAC3F,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AACrF,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;IAED,UAAU,GAAA;AACN,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACrD,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;KAChD;AAED,IAAA,sBAAsB,CAAC,WAAW,EAAA;QAC9B,OAAO,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;KAC7H;AAED,IAAA,mBAAmB,CAAC,WAAgB,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,WAAW,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,GAAG,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC;KACtL;AAED,IAAA,cAAc,CAAC,MAAM,EAAA;AACjB,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,KAAK,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;KACxI;IAED,cAAc,CAAC,KAAK,EAAE,eAAe,EAAA;QACjC,OAAO,IAAI,CAAC,uBAAuB,GAAG,KAAK,GAAG,eAAe,IAAI,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;KACnH;AAED,IAAA,cAAc,CAAC,MAAW,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;KACxK;AAED,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,QACI,CAAC,IAAI,CAAC,gBAAgB;AAClB,cAAE,KAAK;gBACL,IAAI,CAAC,cAAc,EAAE;AAChB,qBAAA,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AACf,qBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;AAC5D,cAAE,KAAK,IAAI,CAAC,EAClB;KACL;IAED,iBAAiB,GAAA;QACb,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KACpD;AAED,IAAA,aAAa,CAAC,MAAM,EAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,CAAC;KACtE;IAED,wBAAwB,CAAC,KAAK,EAAE,KAAK,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,KAAK,EAAE;AACrC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;YAEpB,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACtC,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5D,aAAA;AACJ,SAAA;KACJ;IAED,aAAa,CAAC,KAAK,EAAE,IAAI,EAAA;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;AAEnD,QAAA,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;QACrB,IAAI,OAAO,GAAG,KAAK,CAAC;AAEpB,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AAClC,YAAA,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE;AAC9B,iBAAA,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAChC,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;YACzD,WAAW;gBACP,WAAW,KAAK,CAAC,CAAC;AACd,sBAAE,IAAI,CAAC,cAAc,EAAE;AAChB,yBAAA,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACnC,yBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC1D,sBAAE,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACrD,SAAA;AAAM,aAAA;YACH,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3F,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;YACpB,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;AACxD,YAAA,WAAW,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACpD,SAAA;AAED,QAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;AACpB,YAAA,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACrD,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,eAAe,CAAC,MAAM,EAAA;AAClB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KAC3K;AAED,IAAA,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;QACnB,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,eAAe,CAAC;AACvE,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAA,OAAA,EAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAE1F,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,SAAA;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACtC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AACvG,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KAClF;IAED,mBAAmB,GAAA;QACf,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KACnG;IAED,2BAA2B,GAAA;AACvB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;AAE1D,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,GAAG,aAAa,CAAC;KAC1E;IAED,0BAA0B,GAAA;AACtB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAEzD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,aAAa,CAAC;KACzE;IAED,2BAA2B,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC3I;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;QACrB,MAAM,kBAAkB,GACpB,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC;AACpC,cAAE,IAAI,CAAC,cAAc,EAAE;AAChB,iBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChB,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC;AAEb,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KAC3E;AAED,IAAA,2BAA2B,CAAC,KAAK,EAAA;AAC7B,QAAA,MAAM,kBAAkB,GACpB,IAAI,CAAC,iBAAiB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC;AAChE,cAAE,IAAI,CAAC,cAAc,EAAE;AAChB,iBAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAChB,iBAAA,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;cAC9D,CAAC,CAAC,CAAC;AAEb,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KACxE;AAED,IAAA,2BAA2B,CAAC,KAAK,EAAA;QAC7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,IAAI,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEzL,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,CAAC,CAAC;KAC5D;IAED,4BAA4B,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC1H;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,kBAAkB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAErJ,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,CAAC;KAC/D;AAED,IAAA,8BAA8B,CAAC,KAAK,EAAE,YAAY,GAAG,KAAK,EAAA;AACtD,QAAA,IAAI,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAE5B,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC1B,YAAA,IAAI,YAAY,EAAE;AACd,gBAAA,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;AAC7D,gBAAA,kBAAkB,GAAG,kBAAkB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC;AACjH,aAAA;AAAM,iBAAA;AACH,gBAAA,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;AAC7D,gBAAA,kBAAkB,GAAG,kBAAkB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC;AACjH,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,KAAK,CAAC;KAC/D;IAED,WAAW,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;KACjD;AAED,IAAA,qBAAqB,CAAC,MAAM,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;KAChE;AAED,IAAA,gBAAgB,CAAC,MAAW,EAAA;QACxB,OAAO,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC;KAClG;AAED,IAAA,UAAU,CAAC,MAAM,EAAA;QACb,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,QAAQ;AAAE,YAAA,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;;AAC3H,YAAA,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KACtF;AAED,IAAA,aAAa,CAAC,MAAM,EAAA;AAChB,QAAA,OAAO,MAAM,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;KACnF;IAED,OAAO,GAAA;AACH,QAAA,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC;KACrE;IAED,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;KACvE;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;YAC5D,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,CAAC;AACjD,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC/B;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAC9B,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;AAC9C,SAAA;KACJ;uGA1qCQ,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,2NA4BI,gBAAgB,CAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAUhB,gBAAgB,CAKhB,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,kDAKhB,gBAAgB,CAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAehB,gBAAgB,CAKhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,gBAAgB,6EAKhB,eAAe,CAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAef,eAAe,CAKf,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,0IAyBhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKhB,gBAAgB,CAKhB,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,gBAAgB,sJAoBhB,gBAAgB,CAAA,EAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAUhB,gBAAgB,CAkDhB,EAAA,WAAA,EAAA,aAAA,EAAA,WAAA,EAAA,aAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,gBAAgB,oTA7NzB,CAAC,sBAAsB,CAAC,EAmTrB,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,MAAM,8EAEN,MAAM,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAEH,aAAa,EAtfpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,yBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,cAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,QAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,4BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,4BAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,6BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,6BAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8LT,EAurCmE,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,2cAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,IAAA,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,EAAA,OAAA,EAAA,UAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,MAAA,EAAA,OAAA,EAAA,aAAA,EAAA,YAAA,EAAA,QAAA,EAAA,MAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,UAAA,EAAA,SAAA,EAAA,SAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,UAAU,4EAAE,SAAS,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA9qChF,OAAO,EAAA,UAAA,EAAA,CAAA;kBAzMnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8LT,EACU,SAAA,EAAA,CAAC,sBAAsB,CAAC,EAClB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,2cAAA,CAAA,EAAA,CAAA;+LAOQ,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAMG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAMG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAMG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKkC,eAAe,EAAA,CAAA;sBAAtD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,qBAAqB,EAAA,CAAA;sBAA3D,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAKG,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKkC,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,MAAM,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,gBAAgB,EAAA,CAAA;sBAAvD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,OAAO,EAAA,CAAA;sBAAf,KAAK;gBAKkC,aAAa,EAAA,CAAA;sBAApD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAKG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAKG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAKkC,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKzB,OAAO,EAAA,CAAA;sBAAnB,KAAK;gBAUO,WAAW,EAAA,CAAA;sBAAvB,KAAK;gBAUO,SAAS,EAAA,CAAA;sBAArB,KAAK;gBAWI,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,UAAU,EAAA,CAAA;sBAAnB,MAAM;gBAMG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAEoB,uBAAuB,EAAA,CAAA;sBAAjD,SAAS;uBAAC,cAAc,CAAA;gBAEJ,eAAe,EAAA,CAAA;sBAAnC,SAAS;uBAAC,QAAQ,CAAA;gBAEsB,0BAA0B,EAAA,CAAA;sBAAlE,SAAS;uBAAC,4BAA4B,CAAA;gBAEG,2BAA2B,EAAA,CAAA;sBAApE,SAAS;uBAAC,6BAA6B,CAAA;gBAEjB,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;gBAEF,aAAa,EAAA,CAAA;sBAA/B,SAAS;uBAAC,MAAM,CAAA;gBAEK,WAAW,EAAA,CAAA;sBAAhC,YAAY;uBAAC,MAAM,CAAA;gBAEE,WAAW,EAAA,CAAA;sBAAhC,YAAY;uBAAC,MAAM,CAAA;gBAEY,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAm4BrB,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,iBAlrCb,OAAO,CAAA,EAAA,OAAA,EAAA,CA8qCN,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,CAAA,EAAA,OAAA,EAAA,CA9qChF,OAAO,EA+qCG,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;AAGtC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAJZ,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EACtE,YAAY,EAAE,cAAc,CAAA,EAAA,CAAA,CAAA;;2FAGtC,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,CAAC;AAC1F,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,cAAc,CAAC;oBAChD,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;ACz6CD;;AAEG;;;;"}