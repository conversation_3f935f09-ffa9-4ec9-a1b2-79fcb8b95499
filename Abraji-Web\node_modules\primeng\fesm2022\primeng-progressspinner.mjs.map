{"version": 3, "file": "primeng-progressspinner.mjs", "sources": ["../../src/app/components/progressspinner/progressspinner.ts", "../../src/app/components/progressspinner/primeng-progressspinner.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { ChangeDetectionStrategy, Component, Input, NgModule, ViewEncapsulation } from '@angular/core';\n/**\n * ProgressSpinner is a process status indicator.\n * @group Components\n */\n@Component({\n    selector: 'p-progressSpinner',\n    template: `\n        <div class=\"p-progress-spinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\" role=\"progressbar\" [attr.aria-label]=\"ariaLabel\" [attr.aria-busy]=\"true\" [attr.data-pc-name]=\"'progressspinner'\" [attr.data-pc-section]=\"'root'\">\n            <svg class=\"p-progress-spinner-svg\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\" [attr.data-pc-section]=\"'root'\">\n                <circle class=\"p-progress-spinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\" />\n            </svg>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./progressspinner.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class ProgressSpinner {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Width of the circle stroke.\n     * @group Props\n     */\n    @Input() strokeWidth: string = '2';\n    /**\n     * Color for the background of the circle.\n     * @group Props\n     */\n    @Input() fill: string = 'none';\n    /**\n     * Duration of the rotate animation.\n     * @group Props\n     */\n    @Input() animationDuration: string = '2s';\n    /**\n     * Used to define a aria label attribute the current element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [ProgressSpinner],\n    declarations: [ProgressSpinner]\n})\nexport class ProgressSpinnerModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAEA;;;AAGG;MAiBU,eAAe,CAAA;AACxB;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;IACM,WAAW,GAAW,GAAG,CAAC;AACnC;;;AAGG;IACM,IAAI,GAAW,MAAM,CAAC;AAC/B;;;AAGG;IACM,iBAAiB,GAAW,IAAI,CAAC;AAC1C;;;AAGG;AACM,IAAA,SAAS,CAAqB;uGA9B9B,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EAdd,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,IAAA,EAAA,MAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;AAMT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,m7BAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,eAAe,EAAA,UAAA,EAAA,CAAA;kBAhB3B,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,mBAAmB,EACnB,QAAA,EAAA,CAAA;;;;;;AAMT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,m7BAAA,CAAA,EAAA,CAAA;8BAOQ,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBAKG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAKG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;;MAQG,qBAAqB,CAAA;uGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,EAtCrB,YAAA,EAAA,CAAA,eAAe,CAkCd,EAAA,OAAA,EAAA,CAAA,YAAY,aAlCb,eAAe,CAAA,EAAA,CAAA,CAAA;AAsCf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,YAJpB,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBALjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,eAAe,CAAC;oBAC1B,YAAY,EAAE,CAAC,eAAe,CAAC;AAClC,iBAAA,CAAA;;;AC3DD;;AAEG;;;;"}