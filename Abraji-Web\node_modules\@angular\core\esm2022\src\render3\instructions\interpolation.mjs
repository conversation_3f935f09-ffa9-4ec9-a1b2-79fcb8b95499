/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertEqual, assertLessThan } from '../../util/assert';
import { bindingUpdated, bindingUpdated2, bindingUpdated3, bindingUpdated4 } from '../bindings';
import { getBindingIndex, incrementBindingIndex, nextBindingIndex, setBindingIndex } from '../state';
import { NO_CHANGE } from '../tokens';
import { renderStringify } from '../util/stringify_utils';
/**
 * Create interpolation bindings with a variable number of expressions.
 *
 * If there are 1 to 8 expressions `interpolation1()` to `interpolation8()` should be used instead.
 * Those are faster because there is no need to create an array of expressions and iterate over it.
 *
 * `values`:
 * - has static text at even indexes,
 * - has evaluated expressions at odd indexes.
 *
 * Returns the concatenated string when any of the arguments changes, `NO_CHANGE` otherwise.
 */
export function interpolationV(lView, values) {
    ngDevMode && assertLessThan(2, values.length, 'should have at least 3 values');
    ngDevMode && assertEqual(values.length % 2, 1, 'should have an odd number of values');
    let isBindingUpdated = false;
    let bindingIndex = getBindingIndex();
    for (let i = 1; i < values.length; i += 2) {
        // Check if bindings (odd indexes) have changed
        isBindingUpdated = bindingUpdated(lView, bindingIndex++, values[i]) || isBindingUpdated;
    }
    setBindingIndex(bindingIndex);
    if (!isBindingUpdated) {
        return NO_CHANGE;
    }
    // Build the updated content
    let content = values[0];
    for (let i = 1; i < values.length; i += 2) {
        content += renderStringify(values[i]) + values[i + 1];
    }
    return content;
}
/**
 * Creates an interpolation binding with 1 expression.
 *
 * @param prefix static value used for concatenation only.
 * @param v0 value checked for change.
 * @param suffix static value used for concatenation only.
 */
export function interpolation1(lView, prefix, v0, suffix) {
    const different = bindingUpdated(lView, nextBindingIndex(), v0);
    return different ? prefix + renderStringify(v0) + suffix : NO_CHANGE;
}
/**
 * Creates an interpolation binding with 2 expressions.
 */
export function interpolation2(lView, prefix, v0, i0, v1, suffix) {
    const bindingIndex = getBindingIndex();
    const different = bindingUpdated2(lView, bindingIndex, v0, v1);
    incrementBindingIndex(2);
    return different ? prefix + renderStringify(v0) + i0 + renderStringify(v1) + suffix : NO_CHANGE;
}
/**
 * Creates an interpolation binding with 3 expressions.
 */
export function interpolation3(lView, prefix, v0, i0, v1, i1, v2, suffix) {
    const bindingIndex = getBindingIndex();
    const different = bindingUpdated3(lView, bindingIndex, v0, v1, v2);
    incrementBindingIndex(3);
    return different
        ? prefix + renderStringify(v0) + i0 + renderStringify(v1) + i1 + renderStringify(v2) + suffix
        : NO_CHANGE;
}
/**
 * Create an interpolation binding with 4 expressions.
 */
export function interpolation4(lView, prefix, v0, i0, v1, i1, v2, i2, v3, suffix) {
    const bindingIndex = getBindingIndex();
    const different = bindingUpdated4(lView, bindingIndex, v0, v1, v2, v3);
    incrementBindingIndex(4);
    return different
        ? prefix +
            renderStringify(v0) +
            i0 +
            renderStringify(v1) +
            i1 +
            renderStringify(v2) +
            i2 +
            renderStringify(v3) +
            suffix
        : NO_CHANGE;
}
/**
 * Creates an interpolation binding with 5 expressions.
 */
export function interpolation5(lView, prefix, v0, i0, v1, i1, v2, i2, v3, i3, v4, suffix) {
    const bindingIndex = getBindingIndex();
    let different = bindingUpdated4(lView, bindingIndex, v0, v1, v2, v3);
    different = bindingUpdated(lView, bindingIndex + 4, v4) || different;
    incrementBindingIndex(5);
    return different
        ? prefix +
            renderStringify(v0) +
            i0 +
            renderStringify(v1) +
            i1 +
            renderStringify(v2) +
            i2 +
            renderStringify(v3) +
            i3 +
            renderStringify(v4) +
            suffix
        : NO_CHANGE;
}
/**
 * Creates an interpolation binding with 6 expressions.
 */
export function interpolation6(lView, prefix, v0, i0, v1, i1, v2, i2, v3, i3, v4, i4, v5, suffix) {
    const bindingIndex = getBindingIndex();
    let different = bindingUpdated4(lView, bindingIndex, v0, v1, v2, v3);
    different = bindingUpdated2(lView, bindingIndex + 4, v4, v5) || different;
    incrementBindingIndex(6);
    return different
        ? prefix +
            renderStringify(v0) +
            i0 +
            renderStringify(v1) +
            i1 +
            renderStringify(v2) +
            i2 +
            renderStringify(v3) +
            i3 +
            renderStringify(v4) +
            i4 +
            renderStringify(v5) +
            suffix
        : NO_CHANGE;
}
/**
 * Creates an interpolation binding with 7 expressions.
 */
export function interpolation7(lView, prefix, v0, i0, v1, i1, v2, i2, v3, i3, v4, i4, v5, i5, v6, suffix) {
    const bindingIndex = getBindingIndex();
    let different = bindingUpdated4(lView, bindingIndex, v0, v1, v2, v3);
    different = bindingUpdated3(lView, bindingIndex + 4, v4, v5, v6) || different;
    incrementBindingIndex(7);
    return different
        ? prefix +
            renderStringify(v0) +
            i0 +
            renderStringify(v1) +
            i1 +
            renderStringify(v2) +
            i2 +
            renderStringify(v3) +
            i3 +
            renderStringify(v4) +
            i4 +
            renderStringify(v5) +
            i5 +
            renderStringify(v6) +
            suffix
        : NO_CHANGE;
}
/**
 * Creates an interpolation binding with 8 expressions.
 */
export function interpolation8(lView, prefix, v0, i0, v1, i1, v2, i2, v3, i3, v4, i4, v5, i5, v6, i6, v7, suffix) {
    const bindingIndex = getBindingIndex();
    let different = bindingUpdated4(lView, bindingIndex, v0, v1, v2, v3);
    different = bindingUpdated4(lView, bindingIndex + 4, v4, v5, v6, v7) || different;
    incrementBindingIndex(8);
    return different
        ? prefix +
            renderStringify(v0) +
            i0 +
            renderStringify(v1) +
            i1 +
            renderStringify(v2) +
            i2 +
            renderStringify(v3) +
            i3 +
            renderStringify(v4) +
            i4 +
            renderStringify(v5) +
            i5 +
            renderStringify(v6) +
            i6 +
            renderStringify(v7) +
            suffix
        : NO_CHANGE;
}
//# sourceMappingURL=data:application/json;base64,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