{"version": 3, "file": "primeng-timeline.mjs", "sources": ["../../src/app/components/timeline/timeline.ts", "../../src/app/components/timeline/primeng-timeline.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, ElementRef, Input, NgModule, QueryList, TemplateRef, ViewEncapsulation } from '@angular/core';\nimport { BlockableUI, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { Nullable } from 'primeng/ts-helpers';\n/**\n * Timeline visualizes a series of chained events.\n * @group Components\n */\n@Component({\n    selector: 'p-timeline',\n    template: `\n        <div\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            [ngClass]=\"{\n                'p-timeline p-component': true,\n                'p-timeline-left': align === 'left',\n                'p-timeline-right': align === 'right',\n                'p-timeline-top': align === 'top',\n                'p-timeline-bottom': align === 'bottom',\n                'p-timeline-alternate': align === 'alternate',\n                'p-timeline-vertical': layout === 'vertical',\n                'p-timeline-horizontal': layout === 'horizontal'\n            }\"\n            [attr.data-pc-name]=\"'timeline'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div *ngFor=\"let event of value; let last = last\" class=\"p-timeline-event\" [attr.data-pc-section]=\"'event'\">\n                <div class=\"p-timeline-event-opposite\" [attr.data-pc-section]=\"'opposite'\">\n                    <ng-container *ngTemplateOutlet=\"oppositeTemplate; context: { $implicit: event }\"></ng-container>\n                </div>\n                <div class=\"p-timeline-event-separator\" [attr.data-pc-section]=\"'separator'\">\n                    <ng-container *ngIf=\"markerTemplate; else marker\">\n                        <ng-container *ngTemplateOutlet=\"markerTemplate; context: { $implicit: event }\"></ng-container>\n                    </ng-container>\n                    <ng-template #marker>\n                        <div class=\"p-timeline-event-marker\" [attr.data-pc-section]=\"'marker'\"></div>\n                    </ng-template>\n                    <div *ngIf=\"!last\" class=\"p-timeline-event-connector\"></div>\n                </div>\n                <div class=\"p-timeline-event-content\" [attr.data-pc-section]=\"'content'\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: event }\"></ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./timeline.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Timeline implements AfterContentInit, BlockableUI {\n    /**\n     * An array of events to display.\n     * @group Props\n     */\n    @Input() value: any[] | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Position of the timeline bar relative to the content. Valid values are \"left\", \"right\" for vertical layout and \"top\", \"bottom\" for horizontal layout.\n     * @group Props\n     */\n    @Input() align: string = 'left';\n    /**\n     * Orientation of the timeline.\n     * @group Props\n     */\n    @Input() layout: 'vertical' | 'horizontal' = 'vertical';\n\n    @ContentChildren(PrimeTemplate) templates: Nullable<QueryList<any>>;\n\n    contentTemplate: Nullable<TemplateRef<any>>;\n\n    oppositeTemplate: Nullable<TemplateRef<any>>;\n\n    markerTemplate: Nullable<TemplateRef<any>>;\n\n    constructor(private el: ElementRef) {}\n\n    getBlockableElement(): HTMLElement {\n        return this.el.nativeElement.children[0];\n    }\n\n    ngAfterContentInit() {\n        (this.templates as QueryList<PrimeTemplate>).forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n\n                case 'opposite':\n                    this.oppositeTemplate = item.template;\n                    break;\n\n                case 'marker':\n                    this.markerTemplate = item.template;\n                    break;\n            }\n        });\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Timeline, SharedModule],\n    declarations: [Timeline]\n})\nexport class TimelineModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;AAIA;;;AAGG;MA8CU,QAAQ,CAAA;AAmCG,IAAA,EAAA,CAAA;AAlCpB;;;AAGG;AACM,IAAA,KAAK,CAAoB;AAClC;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACM,KAAK,GAAW,MAAM,CAAC;AAChC;;;AAGG;IACM,MAAM,GAA8B,UAAU,CAAC;AAExB,IAAA,SAAS,CAA2B;AAEpE,IAAA,eAAe,CAA6B;AAE5C,IAAA,gBAAgB,CAA6B;AAE7C,IAAA,cAAc,CAA6B;AAE3C,IAAA,WAAA,CAAoB,EAAc,EAAA;QAAd,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;KAAI;IAEtC,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC5C;IAED,kBAAkB,GAAA;QACb,IAAI,CAAC,SAAsC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC1D,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,SAAS;AACV,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACrC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,QAAQ;AACT,oBAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACpC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;uGAzDQ,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAR,QAAQ,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA2BA,aAAa,EAtEpB,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,wxDAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQQ,QAAQ,EAAA,UAAA,EAAA,CAAA;kBA7CpB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EACZ,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,wxDAAA,CAAA,EAAA,CAAA;+EAOQ,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAE0B,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAsCrB,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAd,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,iBAjEd,QAAQ,CAAA,EAAA,OAAA,EAAA,CA6DP,YAAY,CA7Db,EAAA,OAAA,EAAA,CAAA,QAAQ,EA8DG,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGvB,cAAc,EAAA,OAAA,EAAA,CAJb,YAAY,EACF,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGvB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAL1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;AACvB,oBAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;oBACjC,YAAY,EAAE,CAAC,QAAQ,CAAC;AAC3B,iBAAA,CAAA;;;ACrHD;;AAEG;;;;"}