import { Component, inject, OnInit } from '@angular/core';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { HttpService } from '../../../core/common-services/services/http.service';

@Component({
  selector: 'app-dashboard-home',
  templateUrl: './dashboard-home.component.html',
  styleUrls: ['./dashboard-home.component.scss']
})
export class DashboardHomeComponent implements OnInit {

  // Test to see if localStorage is contains values expected
  private localStorageService = inject(LocalStorageService);
  private httpService = inject(HttpService);

  apiTestResult: any = null;
  isLoading = false;

  ngOnInit(): void {
    console.log(localStorage);
    this.testApiConnection();
  }

  testApiConnection(): void {
    this.isLoading = true;
    console.log('Testing API connection...');

    // Test GET endpoint
    this.httpService.get('test').subscribe({
      next: (response) => {
        console.log('✅ API GET Test Success:', response);
        this.apiTestResult = { type: 'GET', success: true, data: response };
        this.testPostEndpoint();
      },
      error: (error) => {
        console.error('❌ API GET Test Failed:', error);
        this.apiTestResult = { type: 'GET', success: false, error: error };
        this.isLoading = false;
      }
    });
  }

  testPostEndpoint(): void {
    // Test POST endpoint
    const testData = { payload: 'test-from-angular' };
    this.httpService.post('test-login', testData).subscribe({
      next: (response) => {
        console.log('✅ API POST Test Success:', response);
        this.apiTestResult = { type: 'POST', success: true, data: response };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ API POST Test Failed:', error);
        this.apiTestResult = { type: 'POST', success: false, error: error };
        this.isLoading = false;
      }
    });
  }

  testLoginEndpoint(): void {
    this.isLoading = true;
    console.log('Testing Login endpoint...');

    const loginData = { username: 'test', password: 'test' };
    this.httpService.post('auth/login', loginData).subscribe({
      next: (response) => {
        console.log('✅ Login Test Success:', response);
        this.apiTestResult = { type: 'LOGIN', success: true, data: response };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ Login Test Failed:', error);
        this.apiTestResult = { type: 'LOGIN', success: false, error: error };
        this.isLoading = false;
      }
    });
  }
}
