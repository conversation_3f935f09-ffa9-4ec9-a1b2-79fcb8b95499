import { Component, inject, OnInit } from '@angular/core';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { HttpService } from '../../../core/common-services/services/http.service';
import { EncryptionService } from '../../../core/common-services/services/encryption.service';

@Component({
  selector: 'app-dashboard-home',
  templateUrl: './dashboard-home.component.html',
  styleUrls: ['./dashboard-home.component.scss']
})
export class DashboardHomeComponent implements OnInit {

  // Test to see if localStorage is contains values expected
  private localStorageService = inject(LocalStorageService);
  private httpService = inject(HttpService);
  private encryptionService = inject(EncryptionService);

  apiTestResult: any = null;
  dashboardData: any = null;
  isLoading = false;

  ngOnInit(): void {
    console.log(localStorage);
    this.testApiConnection();
  }

  testApiConnection(): void {
    this.isLoading = true;
    console.log('Testing API connection...');

    // Test GET endpoint
    this.httpService.get('test').subscribe({
      next: (response) => {
        console.log('✅ API GET Test Success:', response);
        this.apiTestResult = { type: 'GET', success: true, data: response };
        this.testPostEndpoint();
      },
      error: (error) => {
        console.error('❌ API GET Test Failed:', error);
        this.apiTestResult = { type: 'GET', success: false, error: error };
        this.isLoading = false;
      }
    });
  }

  testPostEndpoint(): void {
    // Test POST endpoint
    const testData = { payload: 'test-from-angular' };
    this.httpService.post('test-login', testData).subscribe({
      next: (response) => {
        console.log('✅ API POST Test Success:', response);
        this.apiTestResult = { type: 'POST', success: true, data: response };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ API POST Test Failed:', error);
        this.apiTestResult = { type: 'POST', success: false, error: error };
        this.isLoading = false;
      }
    });
  }

  testLoginEndpoint(): void {
    this.isLoading = true;
    console.log('Testing Real Login endpoint...');

    // Create encrypted payload like the real app
    const loginData = { username: 'admin', password: 'password123' };
    const encryptedPayload = this.encryptionService.generatePayload(loginData);

    this.httpService.post('auth/login-local', { payload: encryptedPayload }).subscribe({
      next: (response) => {
        console.log('✅ Real Login Test Success:', response);
        this.apiTestResult = { type: 'REAL_LOGIN', success: true, data: response };
        this.isLoading = false;

        // Store token if login successful
        if (response && (response as any).token) {
          this.localStorageService.setToken((response as any).token);
          console.log('🔐 Token stored in localStorage');
        }
      },
      error: (error) => {
        console.error('❌ Real Login Test Failed:', error);
        this.apiTestResult = { type: 'REAL_LOGIN', success: false, error: error };
        this.isLoading = false;
      }
    });
  }

  testDashboardEndpoint(): void {
    this.isLoading = true;
    console.log('Testing Dashboard endpoint...');

    this.httpService.get('dashboard-local').subscribe({
      next: (response) => {
        console.log('✅ Dashboard Test Success:', response);
        this.dashboardData = response;
        this.apiTestResult = { type: 'DASHBOARD', success: true, data: response };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ Dashboard Test Failed:', error);
        this.apiTestResult = { type: 'DASHBOARD', success: false, error: error };
        this.isLoading = false;
      }
    });
  }

  testCardsEndpoint(): void {
    this.isLoading = true;
    console.log('Testing Cards endpoint...');

    this.httpService.get('dashboard/cards-local').subscribe({
      next: (response) => {
        console.log('✅ Cards Test Success:', response);
        this.apiTestResult = { type: 'CARDS', success: true, data: response };
        this.isLoading = false;
      },
      error: (error) => {
        console.error('❌ Cards Test Failed:', error);
        this.apiTestResult = { type: 'CARDS', success: false, error: error };
        this.isLoading = false;
      }
    });
  }
}
