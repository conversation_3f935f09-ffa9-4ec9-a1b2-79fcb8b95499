{"version": 3, "file": "primeng-menu.mjs", "sources": ["../../src/app/components/menu/menu.ts", "../../src/app/components/menu/primeng-menu.ts"], "sourcesContent": ["import { AnimationEvent, animate, style, transition, trigger } from '@angular/animations';\nimport { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Input,\n    NgModule,\n    OnDestroy,\n    Output,\n    PLATFORM_ID,\n    Pipe,\n    PipeTransform,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    ViewRef,\n    booleanAttribute,\n    computed,\n    effect,\n    forwardRef,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { RouterModule } from '@angular/router';\nimport { MenuItem, OverlayService, PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ConnectedOverlayScrollHandler, DomHandler } from 'primeng/dom';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { Nullable, VoidListener } from 'primeng/ts-helpers';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\n\n@Pipe({\n    name: 'safeHtml'\n})\nexport class SafeHtmlPipe implements PipeTransform {\n    constructor(@Inject(PLATFORM_ID) private readonly platformId: any, private readonly sanitizer: DomSanitizer) {}\n\n    public transform(value: string): SafeHtml {\n        if (!value || !isPlatformBrowser(this.platformId)) {\n            return value;\n        }\n\n        return this.sanitizer.bypassSecurityTrustHtml(value);\n    }\n}\n\n@Component({\n    selector: '[pMenuItemContent]',\n    template: `\n        <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, item)\">\n            <ng-container *ngIf=\"!itemTemplate\">\n                <a\n                    *ngIf=\"!item?.routerLink\"\n                    [attr.title]=\"item.title\"\n                    [attr.href]=\"item.url || null\"\n                    [attr.data-automationid]=\"item.automationId\"\n                    [attr.tabindex]=\"-1\"\n                    [attr.data-pc-section]=\"'action'\"\n                    [attr.aria-hidden]=\"true\"\n                    class=\"p-menuitem-link\"\n                    [target]=\"item.target\"\n                    [ngClass]=\"{ 'p-disabled': item.disabled }\"\n                    pRipple\n                >\n                    <ng-container *ngTemplateOutlet=\"itemContent; context: { $implicit: item }\"></ng-container>\n                </a>\n                <a\n                    *ngIf=\"item?.routerLink\"\n                    [routerLink]=\"item.routerLink\"\n                    [attr.data-automationid]=\"item.automationId\"\n                    [attr.tabindex]=\"-1\"\n                    [attr.data-pc-section]=\"'action'\"\n                    [attr.aria-hidden]=\"true\"\n                    [attr.title]=\"item.title\"\n                    [queryParams]=\"item.queryParams\"\n                    routerLinkActive=\"p-menuitem-link-active\"\n                    [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                    class=\"p-menuitem-link\"\n                    [target]=\"item.target\"\n                    [ngClass]=\"{ 'p-disabled': item.disabled }\"\n                    [fragment]=\"item.fragment\"\n                    [queryParamsHandling]=\"item.queryParamsHandling\"\n                    [preserveFragment]=\"item.preserveFragment\"\n                    [skipLocationChange]=\"item.skipLocationChange\"\n                    [replaceUrl]=\"item.replaceUrl\"\n                    [state]=\"item.state\"\n                    pRipple\n                >\n                    <ng-container *ngTemplateOutlet=\"itemContent; context: { $implicit: item }\"></ng-container>\n                </a>\n            </ng-container>\n\n            <ng-container *ngIf=\"itemTemplate\">\n                <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-template>\n            </ng-container>\n\n            <ng-template #itemContent>\n                <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [class]=\"item.iconClass\" [ngStyle]=\"item.iconStyle\"></span>\n                <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ item.label }}</span>\n                <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label | safeHtml\"></span></ng-template>\n                <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{ item.badge }}</span>\n            </ng-template>\n        </div>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class MenuItemContent {\n    @Input('pMenuItemContent') item: MenuItem | undefined;\n\n    @Input() itemTemplate: HTMLElement | undefined;\n\n    @Output() onMenuItemClick: EventEmitter<any> = new EventEmitter<any>();\n\n    menu: Menu;\n\n    constructor(@Inject(forwardRef(() => Menu)) menu: Menu) {\n        this.menu = menu as Menu;\n    }\n\n    onItemClick(event, item) {\n        this.onMenuItemClick.emit({ originalEvent: event, item });\n    }\n}\n/**\n * Menu is a navigation / command component that supports dynamic and static positioning.\n * @group Components\n */\n@Component({\n    selector: 'p-menu',\n    template: `\n        <div\n            #container\n            [ngClass]=\"{ 'p-menu p-component': true, 'p-menu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            *ngIf=\"!popup || visible\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            [attr.data-pc-name]=\"'menu'\"\n            [attr.id]=\"id\"\n        >\n            <div *ngIf=\"startTemplate\" class=\"p-menu-start\" [attr.data-pc-section]=\"'start'\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <ul\n                #list\n                class=\"p-menu-list p-reset\"\n                role=\"menu\"\n                [attr.id]=\"id + '_list'\"\n                [attr.tabindex]=\"getTabIndexValue()\"\n                [attr.data-pc-section]=\"'menu'\"\n                [attr.aria-activedescendant]=\"activedescendant()\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledBy]=\"ariaLabelledBy\"\n                (focus)=\"onListFocus($event)\"\n                (blur)=\"onListBlur($event)\"\n                (keydown)=\"onListKeyDown($event)\"\n            >\n                <ng-template ngFor let-submenu let-i=\"index\" [ngForOf]=\"model\" *ngIf=\"hasSubMenu()\">\n                    <li class=\"p-menuitem-separator\" *ngIf=\"submenu.separator\" [ngClass]=\"{ 'p-hidden': submenu.visible === false }\" role=\"separator\"></li>\n                    <li\n                        class=\"p-submenu-header\"\n                        [attr.data-automationid]=\"submenu.automationId\"\n                        *ngIf=\"!submenu.separator\"\n                        [ngClass]=\"{ 'p-hidden': submenu.visible === false, flex: submenu.visible }\"\n                        pTooltip\n                        [tooltipOptions]=\"submenu.tooltipOptions\"\n                        role=\"none\"\n                        [attr.id]=\"menuitemId(submenu, id, i)\"\n                    >\n                        <ng-container *ngIf=\"!submenuHeaderTemplate\">\n                            <span *ngIf=\"submenu.escape !== false; else htmlSubmenuLabel\">{{ submenu.label }}</span>\n                            <ng-template #htmlSubmenuLabel><span [innerHTML]=\"submenu.label | safeHtml\"></span></ng-template>\n                        </ng-container>\n                        <ng-container *ngTemplateOutlet=\"submenuHeaderTemplate; context: { $implicit: submenu }\"></ng-container>\n                    </li>\n                    <ng-template ngFor let-item let-j=\"index\" [ngForOf]=\"submenu.items\">\n                        <li class=\"p-menuitem-separator\" *ngIf=\"item.separator\" [ngClass]=\"{ 'p-hidden': item.visible === false || submenu.visible === false }\" role=\"separator\"></li>\n                        <li\n                            class=\"p-menuitem\"\n                            *ngIf=\"!item.separator\"\n                            [pMenuItemContent]=\"item\"\n                            [itemTemplate]=\"itemTemplate\"\n                            [ngClass]=\"{ 'p-hidden': item.visible === false || submenu.visible === false, 'p-focus': focusedOptionId() && menuitemId(item, id, i, j) === focusedOptionId(), 'p-disabled': disabled(item.disabled) }\"\n                            [ngStyle]=\"item.style\"\n                            [class]=\"item.styleClass\"\n                            (onMenuItemClick)=\"itemClick($event, menuitemId(item, id, i, j))\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                            role=\"menuitem\"\n                            [attr.data-pc-section]=\"'menuitem'\"\n                            [attr.aria-label]=\"label(item.label)\"\n                            [attr.data-p-focused]=\"isItemFocused(menuitemId(item, id, i, j))\"\n                            [attr.data-p-disabled]=\"disabled(item.disabled)\"\n                            [attr.aria-disabled]=\"disabled(item.disabled)\"\n                            [attr.id]=\"menuitemId(item, id, i, j)\"\n                        ></li>\n                    </ng-template>\n                </ng-template>\n                <ng-template ngFor let-item let-i=\"index\" [ngForOf]=\"model\" *ngIf=\"!hasSubMenu()\">\n                    <li class=\"p-menuitem-separator\" *ngIf=\"item.separator\" [ngClass]=\"{ 'p-hidden': item.visible === false }\" role=\"separator\"></li>\n                    <li\n                        class=\"p-menuitem\"\n                        *ngIf=\"!item.separator\"\n                        [pMenuItemContent]=\"item\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [ngClass]=\"{ 'p-hidden': item.visible === false, 'p-focus': focusedOptionId() && menuitemId(item, id, i, j) === focusedOptionId(), 'p-disabled': disabled(item.disabled) }\"\n                        [ngStyle]=\"item.style\"\n                        [class]=\"item.styleClass\"\n                        (onMenuItemClick)=\"itemClick($event, menuitemId(item, id, i))\"\n                        pTooltip\n                        [tooltipOptions]=\"item.tooltipOptions\"\n                        role=\"menuitem\"\n                        [attr.data-pc-section]=\"'menuitem'\"\n                        [attr.aria-label]=\"label(item.label)\"\n                        [attr.data-p-focused]=\"isItemFocused(menuitemId(item, id, i))\"\n                        [attr.data-p-disabled]=\"disabled(item.disabled)\"\n                        [attr.aria-disabled]=\"disabled(item.disabled)\"\n                        [attr.id]=\"menuitemId(item, id, i)\"\n                    ></li>\n                </ng-template>\n            </ul>\n            <div *ngIf=\"endTemplate\" class=\"p-menu-end\" [attr.data-pc-section]=\"'end'\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n    animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0, transform: 'scaleY(0.8)' }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({ opacity: 0 }))])])],\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./menu.css'],\n    host: {\n        class: 'p-element'\n    }\n})\nexport class Menu implements OnDestroy {\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    @Input() model: MenuItem[] | undefined;\n    /**\n     * Defines if menu would displayed as a popup.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) popup: boolean | undefined;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    @Input() appendTo: HTMLElement | ElementRef | TemplateRef<any> | string | null | undefined | any;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    @Input() showTransitionOptions: string = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    @Input() hideTransitionOptions: string = '.1s linear';\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) tabindex: number = 0;\n    /**\n     * Callback to invoke when overlay menu is shown.\n     * @group Emits\n     */\n    @Output() onShow: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when overlay menu is hidden.\n     * @group Emits\n     */\n    @Output() onHide: EventEmitter<any> = new EventEmitter<any>();\n    /**\n     * Callback to invoke when the list loses focus.\n     * @param {Event} event - blur event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<Event> = new EventEmitter<Event>();\n    /**\n     * Callback to invoke when the list receives focus.\n     * @param {Event} event - focus event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<Event> = new EventEmitter<Event>();\n\n    @ViewChild('list') listViewChild: Nullable<ElementRef>;\n\n    @ViewChild('container') containerViewChild: Nullable<ElementRef>;\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    startTemplate: TemplateRef<any> | undefined;\n\n    endTemplate: TemplateRef<any> | undefined;\n\n    itemTemplate: TemplateRef<any> | undefined;\n\n    submenuHeaderTemplate: TemplateRef<any> | undefined;\n\n    container: HTMLDivElement | undefined;\n\n    scrollHandler: ConnectedOverlayScrollHandler | null | undefined;\n\n    documentClickListener: VoidListener;\n\n    documentResizeListener: VoidListener;\n\n    preventDocumentDefault: boolean | undefined;\n\n    target: any;\n\n    visible: boolean | undefined;\n\n    focusedOptionId = computed(() => {\n        return this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : null;\n    });\n\n    public focusedOptionIndex: any = signal<any>(-1);\n\n    public selectedOptionIndex: any = signal<any>(-1);\n\n    public focused: boolean | undefined = false;\n\n    public overlayVisible: boolean | undefined = false;\n\n    relativeAlign: boolean | undefined;\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        public el: ElementRef,\n        public renderer: Renderer2,\n        private cd: ChangeDetectorRef,\n        public config: PrimeNGConfig,\n        public overlayService: OverlayService\n    ) {\n        this.id = this.id || UniqueComponentId();\n    }\n    /**\n     * Toggles the visibility of the popup menu.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    public toggle(event: Event) {\n        if (this.visible) this.hide();\n        else this.show(event);\n\n        this.preventDocumentDefault = true;\n    }\n    /**\n     * Displays the popup menu.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    public show(event: any) {\n        if (this.visible && this.target !== event.currentTarget) {\n            this.hide();\n        }\n\n        this.target = event.currentTarget;\n        this.relativeAlign = event.relativeAlign;\n        this.visible = true;\n        this.preventDocumentDefault = true;\n        this.overlayVisible = true;\n        this.cd.detectChanges();\n    }\n\n    ngOnInit() {\n        if (!this.popup) {\n            this.bindDocumentClickListener();\n        }\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n\n                case 'itemTemplate':\n                    this.itemTemplate = item.template;\n                    break;\n\n                case 'submenuheader':\n                    this.submenuHeaderTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    getTabIndexValue(): string | null {\n        return this.tabindex !== undefined ? this.tabindex.toString() : null;\n    }\n\n    onOverlayAnimationStart(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'visible':\n                if (this.popup) {\n                    this.container = event.element;\n                    this.moveOnTop();\n                    this.onShow.emit({});\n                    this.appendOverlay();\n                    this.alignOverlay();\n                    this.bindDocumentClickListener();\n                    this.bindDocumentResizeListener();\n                    this.bindScrollListener();\n                    DomHandler.focus(this.listViewChild.nativeElement);\n                    this.preventDocumentDefault = true;\n                }\n                break;\n\n            case 'void':\n                this.onOverlayHide();\n                this.onHide.emit({});\n                break;\n        }\n    }\n\n    onOverlayAnimationEnd(event: AnimationEvent) {\n        switch (event.toState) {\n            case 'void':\n                if (this.autoZIndex) {\n                    ZIndexUtils.clear(event.element);\n                }\n                break;\n        }\n    }\n\n    alignOverlay() {\n        if (this.relativeAlign) DomHandler.relativePosition(this.container, this.target);\n        else DomHandler.absolutePosition(this.container, this.target);\n    }\n\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);\n            else DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n\n    restoreOverlayAppend() {\n        if (this.container && this.appendTo) {\n            this.renderer.appendChild(this.el.nativeElement, this.container);\n        }\n    }\n\n    moveOnTop() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n        }\n    }\n    /**\n     * Hides the popup menu.\n     * @group Method\n     */\n    public hide() {\n        this.visible = false;\n        this.relativeAlign = false;\n        this.cd.detectChanges();\n    }\n\n    onWindowResize() {\n        if (this.visible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n\n    menuitemId(item: MenuItem, id: string, index?: string, childIndex?: string) {\n        return item?.id ?? `${id}_${index}${childIndex !== undefined ? '_' + childIndex : ''}`;\n    }\n\n    isItemFocused(id) {\n        return this.focusedOptionId() === id;\n    }\n\n    label(label: any) {\n        return typeof label === 'function' ? label() : label;\n    }\n\n    disabled(disabled: any) {\n        return typeof disabled === 'function' ? disabled() : typeof disabled === 'undefined' ? false : disabled;\n    }\n\n    activedescendant() {\n        return this.focused ? this.focusedOptionId() : undefined;\n    }\n\n    onListFocus(event: Event) {\n        if (!this.focused) {\n            this.focused = true;\n            this.onFocus.emit(event);\n        }\n    }\n\n    onListBlur(event: FocusEvent | MouseEvent) {\n        if (this.focused) {\n            this.focused = false;\n            this.changeFocusedOptionIndex(-1);\n            this.selectedOptionIndex.set(-1);\n            this.focusedOptionIndex.set(-1);\n            this.onBlur.emit(event);\n        }\n    }\n\n    onListKeyDown(event) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n\n            case 'NumpadEnter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n\n            case 'Escape':\n            case 'Tab':\n                if (this.popup) {\n                    DomHandler.focus(this.target);\n                    this.hide();\n                }\n                this.overlayVisible && this.hide();\n                break;\n\n            default:\n                break;\n        }\n    }\n\n    onArrowDownKey(event) {\n        const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex());\n        this.changeFocusedOptionIndex(optionIndex);\n        event.preventDefault();\n    }\n\n    onArrowUpKey(event) {\n        if (event.altKey && this.popup) {\n            DomHandler.focus(this.target);\n            this.hide();\n            event.preventDefault();\n        } else {\n            const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex());\n\n            this.changeFocusedOptionIndex(optionIndex);\n            event.preventDefault();\n        }\n    }\n\n    onHomeKey(event) {\n        this.changeFocusedOptionIndex(0);\n        event.preventDefault();\n    }\n\n    onEndKey(event) {\n        this.changeFocusedOptionIndex(DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]').length - 1);\n        event.preventDefault();\n    }\n\n    onEnterKey(event) {\n        const element = DomHandler.findSingle(this.containerViewChild.nativeElement, `li[id=\"${`${this.focusedOptionIndex()}`}\"]`);\n        const anchorElement = element && DomHandler.findSingle(element, 'a');\n\n        this.popup && DomHandler.focus(this.target);\n        anchorElement ? anchorElement.click() : element && element.click();\n\n        event.preventDefault();\n    }\n\n    onSpaceKey(event) {\n        this.onEnterKey(event);\n    }\n\n    findNextOptionIndex(index) {\n        const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n        const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n    }\n\n    findPrevOptionIndex(index) {\n        const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n        const matchedOptionIndex = [...links].findIndex((link) => link.id === index);\n\n        return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n    }\n\n    changeFocusedOptionIndex(index) {\n        const links = DomHandler.find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n\n        if (links.length > 0) {\n            let order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;\n            order > -1 && this.focusedOptionIndex.set(links[order].getAttribute('id'));\n        }\n    }\n\n    itemClick(event: any, id: string) {\n        const { originalEvent, item } = event;\n\n        if (!this.focused) {\n            this.focused = true;\n            this.onFocus.emit();\n        }\n\n        if (item.disabled) {\n            originalEvent.preventDefault();\n            return;\n        }\n\n        if (!item.url && !item.routerLink) {\n            originalEvent.preventDefault();\n        }\n\n        if (item.command) {\n            item.command({\n                originalEvent: originalEvent,\n                item: item\n            });\n        }\n\n        if (this.popup) {\n            this.hide();\n        }\n\n        if (!this.popup && this.focusedOptionIndex() !== id) {\n            this.focusedOptionIndex.set(id);\n        }\n    }\n\n    onOverlayClick(event: Event) {\n        if (this.popup) {\n            this.overlayService.add({\n                originalEvent: event,\n                target: this.el.nativeElement\n            });\n        }\n\n        this.preventDocumentDefault = true;\n    }\n\n    bindDocumentClickListener() {\n        if (!this.documentClickListener && isPlatformBrowser(this.platformId)) {\n            const documentTarget: any = this.el ? this.el.nativeElement.ownerDocument : 'document';\n\n            this.documentClickListener = this.renderer.listen(documentTarget, 'click', (event) => {\n                const isOutsideContainer = this.containerViewChild.nativeElement && !this.containerViewChild.nativeElement.contains(event.target);\n                const isOutsideTarget = !(this.target && (this.target === event.target || this.target.contains(event.target)));\n                if (!this.popup && isOutsideContainer && isOutsideTarget) {\n                    this.onListBlur(event);\n                }\n                if (this.preventDocumentDefault && this.overlayVisible && isOutsideContainer && isOutsideTarget) {\n                    this.hide();\n                    this.preventDocumentDefault = false;\n                }\n            });\n        }\n    }\n\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n\n    bindDocumentResizeListener() {\n        if (!this.documentResizeListener && isPlatformBrowser(this.platformId)) {\n            const window = this.document.defaultView;\n            this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n        }\n    }\n\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n\n    bindScrollListener() {\n        if (!this.scrollHandler && isPlatformBrowser(this.platformId)) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                if (this.visible) {\n                    this.hide();\n                }\n            });\n        }\n\n        this.scrollHandler?.bindScrollListener();\n    }\n\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n\n    onOverlayHide() {\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.preventDocumentDefault = false;\n    }\n\n    ngOnDestroy() {\n        if (this.popup) {\n            if (this.scrollHandler) {\n                this.scrollHandler.destroy();\n                this.scrollHandler = null;\n            }\n\n            if (this.container && this.autoZIndex) {\n                ZIndexUtils.clear(this.container);\n            }\n\n            this.restoreOverlayAppend();\n            this.onOverlayHide();\n        }\n\n        if (!this.popup) {\n            this.unbindDocumentClickListener();\n        }\n    }\n\n    hasSubMenu(): boolean {\n        if (this.model) {\n            for (var item of this.model) {\n                if (item.items) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n\n    isItemHidden(item: any): boolean {\n        if (item.separator) {\n            return item.visible === false || (item.items && item.items.some((subitem) => subitem.visible !== false));\n        }\n        return item.visible === false;\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule],\n    exports: [Menu, RouterModule, TooltipModule, SharedModule],\n    declarations: [Menu, MenuItemContent, SafeHtmlPipe]\n})\nexport class MenuModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;MA0Ca,YAAY,CAAA;AAC6B,IAAA,UAAA,CAAA;AAAkC,IAAA,SAAA,CAAA;IAApF,WAAkD,CAAA,UAAe,EAAmB,SAAuB,EAAA;QAAzD,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QAAmB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAc;KAAI;AAExG,IAAA,SAAS,CAAC,KAAa,EAAA;QAC1B,IAAI,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAC/C,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;KACxD;AATQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBACD,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,CAAA;qGADtB,YAAY,EAAA,IAAA,EAAA,UAAA,EAAA,CAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAHxB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACF,oBAAA,IAAI,EAAE,UAAU;AACnB,iBAAA,CAAA;;0BAEgB,MAAM;2BAAC,WAAW,CAAA;;MA0EtB,eAAe,CAAA;AACG,IAAA,IAAI,CAAuB;AAE7C,IAAA,YAAY,CAA0B;AAErC,IAAA,eAAe,GAAsB,IAAI,YAAY,EAAO,CAAC;AAEvE,IAAA,IAAI,CAAO;AAEX,IAAA,WAAA,CAA4C,IAAU,EAAA;AAClD,QAAA,IAAI,CAAC,IAAI,GAAG,IAAY,CAAC;KAC5B;IAED,WAAW,CAAC,KAAK,EAAE,IAAI,EAAA;AACnB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;KAC7D;AAfQ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,kBASJ,UAAU,CAAC,MAAM,IAAI,CAAC,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AATjC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,EA7Dd,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,kBAAA,EAAA,MAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EArEQ,YAAY,EAAA,IAAA,EAAA,UAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA2EZ,eAAe,EAAA,UAAA,EAAA,CAAA;kBA/D3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,oBAAoB;AAC9B,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;;0BAUgB,MAAM;AAAC,oBAAA,IAAA,EAAA,CAAA,UAAU,CAAC,MAAM,IAAI,CAAC,CAAA;yCARf,IAAI,EAAA,CAAA;sBAA9B,KAAK;uBAAC,kBAAkB,CAAA;gBAEhB,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEI,eAAe,EAAA,CAAA;sBAAxB,MAAM;;AAYX;;;AAGG;MAgHU,IAAI,CAAA;AAoIiB,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACtB,IAAA,EAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACC,IAAA,EAAA,CAAA;AACD,IAAA,MAAA,CAAA;AACA,IAAA,cAAA,CAAA;AAzIX;;;AAGG;AACM,IAAA,KAAK,CAAyB;AACvC;;;AAGG;AACqC,IAAA,KAAK,CAAsB;AACnE;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,QAAQ,CAAgF;AACjG;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;AAGG;IACM,qBAAqB,GAAW,iCAAiC,CAAC;AAC3E;;;AAGG;IACM,qBAAqB,GAAW,YAAY,CAAC;AACtD;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;IACoC,QAAQ,GAAW,CAAC,CAAC;AAC5D;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;AAGG;AACO,IAAA,MAAM,GAAsB,IAAI,YAAY,EAAO,CAAC;AAC9D;;;;AAIG;AACO,IAAA,MAAM,GAAwB,IAAI,YAAY,EAAS,CAAC;AAClE;;;;AAIG;AACO,IAAA,OAAO,GAAwB,IAAI,YAAY,EAAS,CAAC;AAEhD,IAAA,aAAa,CAAuB;AAE/B,IAAA,kBAAkB,CAAuB;AAEjC,IAAA,SAAS,CAAuC;AAEhF,IAAA,aAAa,CAA+B;AAE5C,IAAA,WAAW,CAA+B;AAE1C,IAAA,YAAY,CAA+B;AAE3C,IAAA,qBAAqB,CAA+B;AAEpD,IAAA,SAAS,CAA6B;AAEtC,IAAA,aAAa,CAAmD;AAEhE,IAAA,qBAAqB,CAAe;AAEpC,IAAA,sBAAsB,CAAe;AAErC,IAAA,sBAAsB,CAAsB;AAE5C,IAAA,MAAM,CAAM;AAEZ,IAAA,OAAO,CAAsB;AAE7B,IAAA,eAAe,GAAG,QAAQ,CAAC,MAAK;AAC5B,QAAA,OAAO,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC;AAC/E,KAAC,CAAC,CAAC;AAEI,IAAA,kBAAkB,GAAQ,MAAM,CAAM,CAAC,CAAC,CAAC,CAAC;AAE1C,IAAA,mBAAmB,GAAQ,MAAM,CAAM,CAAC,CAAC,CAAC,CAAC;IAE3C,OAAO,GAAwB,KAAK,CAAC;IAErC,cAAc,GAAwB,KAAK,CAAC;AAEnD,IAAA,aAAa,CAAsB;AAEnC,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACrC,EAAc,EACd,QAAmB,EAClB,EAAqB,EACtB,MAAqB,EACrB,cAA8B,EAAA;QANX,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACrC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAClB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACtB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACrB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QAErC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;KAC5C;AACD;;;;AAIG;AACI,IAAA,MAAM,CAAC,KAAY,EAAA;QACtB,IAAI,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,IAAI,EAAE,CAAC;;AACzB,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAEtB,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;KACtC;AACD;;;;AAIG;AACI,IAAA,IAAI,CAAC,KAAU,EAAA;QAClB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,aAAa,EAAE;YACrD,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC;AAClC,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;AACzC,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACnC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACpC,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,KAAK;AACN,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,MAAM;AAEV,gBAAA,KAAK,cAAc;AACf,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA,KAAK,eAAe;AAChB,oBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC3C,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,gBAAgB,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC;KACxE;AAED,IAAA,uBAAuB,CAAC,KAAqB,EAAA;QACzC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,SAAS;gBACV,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,oBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC;oBAC/B,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACrB,IAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACjC,IAAI,CAAC,0BAA0B,EAAE,CAAC;oBAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AACnD,oBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,iBAAA;gBACD,MAAM;AAEV,YAAA,KAAK,MAAM;gBACP,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrB,MAAM;AACb,SAAA;KACJ;AAED,IAAA,qBAAqB,CAAC,KAAqB,EAAA;QACvC,QAAQ,KAAK,CAAC,OAAO;AACjB,YAAA,KAAK,MAAM;gBACP,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,oBAAA,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACpC,iBAAA;gBACD,MAAM;AACb,SAAA;KACJ;IAED,YAAY,GAAA;QACR,IAAI,IAAI,CAAC,aAAa;YAAE,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;YAC5E,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACjE;IAED,aAAa,GAAA;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM;AAAE,gBAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;gBACvF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9D,SAAA;KACJ;IAED,oBAAoB,GAAA;AAChB,QAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjC,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACpE,SAAA;KACJ;IAED,SAAS,GAAA;QACL,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtF,SAAA;KACJ;AACD;;;AAGG;IACI,IAAI,GAAA;AACP,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,cAAc,GAAA;QACV,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;YAC7C,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,IAAc,EAAE,EAAU,EAAE,KAAc,EAAE,UAAmB,EAAA;QACtE,OAAO,IAAI,EAAE,EAAE,IAAI,GAAG,EAAE,CAAA,CAAA,EAAI,KAAK,CAAA,EAAG,UAAU,KAAK,SAAS,GAAG,GAAG,GAAG,UAAU,GAAG,EAAE,CAAA,CAAE,CAAC;KAC1F;AAED,IAAA,aAAa,CAAC,EAAE,EAAA;AACZ,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,CAAC;KACxC;AAED,IAAA,KAAK,CAAC,KAAU,EAAA;AACZ,QAAA,OAAO,OAAO,KAAK,KAAK,UAAU,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC;KACxD;AAED,IAAA,QAAQ,CAAC,QAAa,EAAA;QAClB,OAAO,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,EAAE,GAAG,OAAO,QAAQ,KAAK,WAAW,GAAG,KAAK,GAAG,QAAQ,CAAC;KAC3G;IAED,gBAAgB,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,SAAS,CAAC;KAC5D;AAED,IAAA,WAAW,CAAC,KAAY,EAAA;AACpB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACf,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAA;KACJ;AAED,IAAA,UAAU,CAAC,KAA8B,EAAA;QACrC,IAAI,IAAI,CAAC,OAAO,EAAE;AACd,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACrB,YAAA,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;KACJ;AAED,IAAA,aAAa,CAAC,KAAK,EAAA;QACf,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,aAAa;AACd,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,KAAK;gBACN,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,oBAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACD,gBAAA,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACnC,MAAM;AAEV,YAAA;gBACI,MAAM;AACb,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAK,EAAA;QAChB,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AACxE,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAC3C,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,YAAY,CAAC,KAAK,EAAA;AACd,QAAA,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;AAC5B,YAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAExE,YAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;YAC3C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAK,EAAA;AACX,QAAA,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;QACjC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAK,EAAA;QACV,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,yDAAyD,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5J,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;QACZ,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAA,OAAA,EAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA,CAAE,CAAI,EAAA,CAAA,CAAC,CAAC;AAC3H,QAAA,MAAM,aAAa,GAAG,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAErE,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,QAAA,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QAEnE,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAK,EAAA;AACZ,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,yDAAyD,CAAC,CAAC;QAChI,MAAM,kBAAkB,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAE7E,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC;KAC/D;AAED,IAAA,mBAAmB,CAAC,KAAK,EAAA;AACrB,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,yDAAyD,CAAC,CAAC;QAChI,MAAM,kBAAkB,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAE7E,QAAA,OAAO,kBAAkB,GAAG,CAAC,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC;KAC/D;AAED,IAAA,wBAAwB,CAAC,KAAK,EAAA;AAC1B,QAAA,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,yDAAyD,CAAC,CAAC;AAEhI,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAClB,YAAA,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAC7E,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9E,SAAA;KACJ;IAED,SAAS,CAAC,KAAU,EAAE,EAAU,EAAA;AAC5B,QAAA,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AAEtC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACf,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AACvB,SAAA;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,aAAa,CAAC,cAAc,EAAE,CAAC;YAC/B,OAAO;AACV,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC/B,aAAa,CAAC,cAAc,EAAE,CAAC;AAClC,SAAA;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC;AACT,gBAAA,aAAa,EAAE,aAAa;AAC5B,gBAAA,IAAI,EAAE,IAAI;AACb,aAAA,CAAC,CAAC;AACN,SAAA;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE;AACjD,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACnC,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,KAAY,EAAA;QACvB,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,gBAAA,aAAa,EAAE,KAAK;AACpB,gBAAA,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa;AAChC,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;KACtC;IAED,yBAAyB,GAAA;QACrB,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACnE,YAAA,MAAM,cAAc,GAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,aAAa,GAAG,UAAU,CAAC;AAEvF,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;gBACjF,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAClI,gBAAA,MAAM,eAAe,GAAG,EAAE,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/G,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,kBAAkB,IAAI,eAAe,EAAE;AACtD,oBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC1B,iBAAA;gBACD,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,cAAc,IAAI,kBAAkB,IAAI,eAAe,EAAE;oBAC7F,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,oBAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;AACvC,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;KACJ;IAED,2BAA2B,GAAA;QACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACrC,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpE,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACzC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxG,SAAA;KACJ;IAED,4BAA4B,GAAA;QACxB,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtC,SAAA;KACJ;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YAC3D,IAAI,CAAC,aAAa,GAAG,IAAI,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,MAAK;gBACrE,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;AACL,aAAC,CAAC,CAAC;AACN,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,EAAE,kBAAkB,EAAE,CAAC;KAC5C;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;AAC7C,SAAA;KACJ;IAED,aAAa,GAAA;QACT,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;KACvC;IAED,WAAW,GAAA;QACP,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,gBAAA,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC7B,gBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B,aAAA;AAED,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,gBAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,aAAA;YAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;AACxB,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtC,SAAA;KACJ;IAED,UAAU,GAAA;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,YAAA,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;gBACzB,IAAI,IAAI,CAAC,KAAK,EAAE;AACZ,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;AACJ,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE;AAChB,YAAA,OAAO,IAAI,CAAC,OAAO,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC;AAC5G,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC;KACjC;uGA3jBQ,IAAI,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAoID,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FArId,IAAI,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAUO,gBAAgB,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAoBhB,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,eAAe,CA8Bf,EAAA,qBAAA,EAAA,uBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,CA4BlB,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EAAA,aAAa,EA1MpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoGT,EA3HQ,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,oUAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,EA3Ef,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,kBAAA,EAAA,cAAA,CAAA,EAAA,OAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,YAAY,EAuMT,IAAA,EAAA,UAAA,EAAA,CAAA,EAAA,UAAA,EAAA,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAQpO,IAAI,EAAA,UAAA,EAAA,CAAA;kBA/GhB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,QAAQ,EACR,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGT,IAAA,CAAA,EAAA,UAAA,EACW,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAA,eAAA,EAC5N,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA,EAAA,MAAA,EAAA,CAAA,oUAAA,CAAA,EAAA,CAAA;;0BAsII,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;mLAhId,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKkC,KAAK,EAAA,CAAA;sBAA5C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAK7B,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,qBAAqB,EAAA,CAAA;sBAA7B,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAKG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKiC,QAAQ,EAAA,CAAA;sBAA9C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK3B,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAKG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAMG,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAEY,aAAa,EAAA,CAAA;sBAA/B,SAAS;uBAAC,MAAM,CAAA;gBAEO,kBAAkB,EAAA,CAAA;sBAAzC,SAAS;uBAAC,WAAW,CAAA;gBAEU,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;;MAserB,UAAU,CAAA;uGAAV,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAV,UAAU,EAAA,YAAA,EAAA,CAnkBV,IAAI,EApIJ,eAAe,EA3Ef,YAAY,CAAA,EAAA,OAAA,EAAA,CA8wBX,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CA/jBtE,EAAA,OAAA,EAAA,CAAA,IAAI,EAgkBG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;AAGhD,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EAJT,OAAA,EAAA,CAAA,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAC/D,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGhD,UAAU,EAAA,UAAA,EAAA,CAAA;kBALtB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;oBAChF,OAAO,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;AAC1D,oBAAA,YAAY,EAAE,CAAC,IAAI,EAAE,eAAe,EAAE,YAAY,CAAC;AACtD,iBAAA,CAAA;;;AC3zBD;;AAEG;;;;"}