{"version": 3, "file": "primeng-animate.mjs", "sources": ["../../src/app/components/animate/animate.ts", "../../src/app/components/animate/primeng-animate.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { AfterViewInit, Directive, ElementRef, Input, NgModule, OnInit, Renderer2 } from '@angular/core';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\n/**\n * Animate manages PrimeFlex CSS classes declaratively to during enter/leave animations on scroll or on page load.\n * @group Components\n */\n@Directive({\n    selector: '[pAnimate]',\n    host: {\n        '[class.p-animate]': 'true'\n    }\n})\nexport class Animate implements OnInit, AfterViewInit {\n    /**\n     * Selector to define the CSS class for enter animation.\n     * @group Props\n     */\n    @Input() enterClass: string | undefined;\n    /**\n     * Selector to define the CSS class for leave animation.\n     * @group Props\n     */\n    @Input() leaveClass: string | undefined;\n\n    observer: IntersectionObserver | undefined;\n\n    timeout: any;\n\n    constructor(private host: ElementRef, public el: ElementRef, public renderer: Renderer2) {}\n\n    ngOnInit() {\n        console.log('pAnimate directive is deprecated in 16.7.0 and will be removed in the future. Use pAnimateOnScroll directive instead');\n    }\n\n    ngAfterViewInit() {\n        this.bindIntersectionObserver();\n    }\n\n    bindIntersectionObserver() {\n        const options = {\n            root: null,\n            rootMargin: '0px',\n            threshold: 1.0\n        };\n\n        this.observer = new IntersectionObserver((el) => this.isVisible(el), options);\n        this.observer.observe(this.host.nativeElement);\n    }\n\n    isVisible(element: IntersectionObserverEntry[]) {\n        const [intersectionObserverEntry] = element;\n        intersectionObserverEntry.isIntersecting ? this.enter() : this.leave();\n    }\n\n    enter() {\n        this.host.nativeElement.style.visibility = 'visible';\n        DomHandler.addClass(this.host.nativeElement, this.enterClass as string);\n    }\n\n    leave() {\n        DomHandler.removeClass(this.host.nativeElement, this.enterClass as string);\n        if (this.leaveClass) {\n            DomHandler.addClass(this.host.nativeElement, this.leaveClass);\n        }\n\n        const animationDuration = this.host.nativeElement.style.animationDuration || 500;\n\n        this.timeout = setTimeout(() => {\n            this.host.nativeElement.style.visibility = 'hidden';\n        }, animationDuration);\n    }\n\n    unbindIntersectionObserver() {\n        if (this.observer) {\n            this.observer.unobserve(this.host.nativeElement);\n        }\n    }\n\n    ngOnDestroy() {\n        this.unbindIntersectionObserver();\n        clearTimeout(this.timeout);\n    }\n}\n\n@NgModule({\n    imports: [CommonModule],\n    exports: [Animate],\n    declarations: [Animate]\n})\nexport class AnimateModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;AAGA;;;AAGG;MAOU,OAAO,CAAA;AAgBI,IAAA,IAAA,CAAA;AAAyB,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAfpE;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;AACM,IAAA,UAAU,CAAqB;AAExC,IAAA,QAAQ,CAAmC;AAE3C,IAAA,OAAO,CAAM;AAEb,IAAA,WAAA,CAAoB,IAAgB,EAAS,EAAc,EAAS,QAAmB,EAAA;QAAnE,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAY;QAAS,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;KAAI;IAE3F,QAAQ,GAAA;AACJ,QAAA,OAAO,CAAC,GAAG,CAAC,sHAAsH,CAAC,CAAC;KACvI;IAED,eAAe,GAAA;QACX,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACnC;IAED,wBAAwB,GAAA;AACpB,QAAA,MAAM,OAAO,GAAG;AACZ,YAAA,IAAI,EAAE,IAAI;AACV,YAAA,UAAU,EAAE,KAAK;AACjB,YAAA,SAAS,EAAE,GAAG;SACjB,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAoB,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;QAC9E,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAClD;AAED,IAAA,SAAS,CAAC,OAAoC,EAAA;AAC1C,QAAA,MAAM,CAAC,yBAAyB,CAAC,GAAG,OAAO,CAAC;AAC5C,QAAA,yBAAyB,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;KAC1E;IAED,KAAK,GAAA;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;AACrD,QAAA,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAoB,CAAC,CAAC;KAC3E;IAED,KAAK,GAAA;AACD,QAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAoB,CAAC,CAAC;QAC3E,IAAI,IAAI,CAAC,UAAU,EAAE;AACjB,YAAA,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACjE,SAAA;AAED,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,iBAAiB,IAAI,GAAG,CAAC;AAEjF,QAAA,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,MAAK;YAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;SACvD,EAAE,iBAAiB,CAAC,CAAC;KACzB;IAED,0BAA0B,GAAA;QACtB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpD,SAAA;KACJ;IAED,WAAW,GAAA;QACP,IAAI,CAAC,0BAA0B,EAAE,CAAC;AAClC,QAAA,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC9B;uGArEQ,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2FAAP,OAAO,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;;2FAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBANnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACF,wBAAA,mBAAmB,EAAE,MAAM;AAC9B,qBAAA;AACJ,iBAAA,CAAA;gIAMY,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;;MAmEG,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EA7Eb,YAAA,EAAA,CAAA,OAAO,CAyEN,EAAA,OAAA,EAAA,CAAA,YAAY,aAzEb,OAAO,CAAA,EAAA,CAAA,CAAA;AA6EP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,YAJZ,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAIb,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACN,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,OAAO,EAAE,CAAC,OAAO,CAAC;oBAClB,YAAY,EAAE,CAAC,OAAO,CAAC;AAC1B,iBAAA,CAAA;;;ACzFD;;AAEG;;;;"}