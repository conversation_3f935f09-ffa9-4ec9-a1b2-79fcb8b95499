{"version": 3, "file": "primeng-menubar.mjs", "sources": ["../../src/app/components/menubar/menubar.ts", "../../src/app/components/menubar/primeng-menubar.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport {\n    AfterContentInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ContentChildren,\n    ElementRef,\n    EventEmitter,\n    Inject,\n    Injectable,\n    Input,\n    NgModule,\n    OnDestroy,\n    OnInit,\n    Output,\n    PLATFORM_ID,\n    QueryList,\n    Renderer2,\n    TemplateRef,\n    ViewChild,\n    ViewEncapsulation,\n    booleanAttribute,\n    effect,\n    numberAttribute,\n    signal\n} from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { MenuItem, PrimeNGConfig, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { BarsIcon } from 'primeng/icons/bars';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { VoidListener } from 'primeng/ts-helpers';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { Subject, Subscription, interval } from 'rxjs';\nimport { debounce, filter } from 'rxjs/operators';\n\n@Injectable()\nexport class MenubarService {\n    autoHide: boolean | undefined;\n\n    autoHideDelay: number | undefined;\n\n    readonly mouseLeaves = new Subject<boolean>();\n\n    readonly mouseLeft$ = this.mouseLeaves.pipe(\n        debounce(() => interval(this.autoHideDelay)),\n        filter((mouseLeft) => (this.autoHide as boolean) && mouseLeft)\n    );\n}\n\n@Component({\n    selector: 'p-menubarSub',\n    template: `\n        <ul\n            #menubar\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-menubar-root-list': root }\"\n            [attr.data-pc-section]=\"'menu'\"\n            role=\"menubar\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n            [tabindex]=\"0\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            [attr.id]=\"root ? menuId : null\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({ $event, processedItem })\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\" [id]=\"getItemLabelId(processedItem)\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"root\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!root\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" *ngIf=\"root\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" *ngIf=\"!root\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, root: root }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <p-menubarSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [items]=\"processedItem.items\"\n                        [mobileActive]=\"mobileActive\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        [ariaLabelledBy]=\"getItemLabelId(processedItem)\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    >\n                    </p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n    encapsulation: ViewEncapsulation.None,\n    host: {\n        class: 'p-element'\n    }\n})\nexport class MenubarSub implements OnInit, OnDestroy {\n    @Input() items: any[];\n\n    @Input() itemTemplate: HTMLElement | undefined;\n\n    @Input({ transform: booleanAttribute }) root: boolean = false;\n\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n\n    @Input({ transform: booleanAttribute }) mobileActive: boolean | undefined;\n\n    @Input({ transform: booleanAttribute }) autoDisplay: boolean | undefined;\n\n    @Input() menuId: string | undefined;\n\n    @Input() ariaLabel: string | undefined;\n\n    @Input() ariaLabelledBy: string | undefined;\n\n    @Input({ transform: numberAttribute }) level: number = 0;\n\n    @Input() focusedItemId: string | undefined;\n\n    @Input() activeItemPath: any[];\n\n    @Input() submenuIconTemplate: TemplateRef<any> | undefined;\n\n    @Output() itemClick: EventEmitter<any> = new EventEmitter();\n\n    @Output() itemMouseEnter: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuFocus: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuBlur: EventEmitter<any> = new EventEmitter();\n\n    @Output() menuKeydown: EventEmitter<any> = new EventEmitter();\n\n    @ViewChild('menubar', { static: true }) menubarViewChild: ElementRef;\n\n    mouseLeaveSubscriber: Subscription | undefined;\n\n    constructor(public el: ElementRef, public renderer: Renderer2, private cd: ChangeDetectorRef, private menubarService: MenubarService) {}\n\n    ngOnInit() {\n        this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => {\n            this.cd.markForCheck();\n        });\n    }\n\n    onItemClick(event: any, processedItem: any) {\n        this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n        this.itemClick.emit({ originalEvent: event, processedItem, isFocus: true });\n    }\n\n    getItemProp(processedItem: any, name: string, params: any | null = null) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n\n    getItemId(processedItem: any): string {\n        return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n    }\n\n    getItemKey(processedItem: any): string {\n        return this.getItemId(processedItem);\n    }\n\n    getItemLabelId(processedItem: any): string {\n        return `${this.menuId}_${processedItem.key}_label`;\n    }\n\n    getItemClass(processedItem: any) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem': true,\n            'p-highlight': this.isItemActive(processedItem),\n            'p-menuitem-active': this.isItemActive(processedItem),\n            'p-focus': this.isItemFocused(processedItem),\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n\n    getItemLabel(processedItem: any): string {\n        return this.getItemProp(processedItem, 'label');\n    }\n\n    getSeparatorItemClass(processedItem: any) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem-separator': true\n        };\n    }\n\n    isItemVisible(processedItem: any): boolean {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n\n    isItemActive(processedItem: any): boolean {\n        if (this.activeItemPath) {\n            return this.activeItemPath.some((path) => path.key === processedItem.key);\n        }\n    }\n\n    isItemDisabled(processedItem: any): boolean {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n\n    isItemFocused(processedItem: any): boolean {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n\n    isItemGroup(processedItem: any): boolean {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n\n    getAriaPosInset(index: number) {\n        return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n\n    onItemMouseLeave() {\n        this.menubarService.mouseLeaves.next(true);\n    }\n\n    onItemMouseEnter(param: any) {\n        if (this.autoDisplay) {\n            this.menubarService.mouseLeaves.next(false);\n            const { event, processedItem } = param;\n            this.itemMouseEnter.emit({ originalEvent: event, processedItem });\n        }\n    }\n\n    ngOnDestroy() {\n        this.mouseLeaveSubscriber?.unsubscribe();\n    }\n}\n/**\n * Menubar is a horizontal menu component.\n * @group Components\n */\n@Component({\n    selector: 'p-menubar',\n    template: `\n        <div [ngClass]=\"{ 'p-menubar p-component': true, 'p-menubar-mobile-active': mobileActive }\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-section]=\"'root'\" [attr.data-pc-name]=\"'menubar'\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a\n                #menubutton\n                tabindex=\"0\"\n                role=\"button\"\n                [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                [attr.aria-expanded]=\"mobileActive\"\n                [attr.aria-controls]=\"id\"\n                [attr.aria-label]=\"config.translation.aria.navigation\"\n                [attr.data-pc-section]=\"'button'\"\n                *ngIf=\"model && model.length > 0\"\n                class=\"p-menubar-button\"\n                (click)=\"menuButtonClick($event)\"\n                (keydown)=\"menuButtonKeydown($event)\"\n            >\n                <BarsIcon *ngIf=\"!menuIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"menuIconTemplate\"></ng-template>\n            </a>\n            <p-menubarSub\n                #rootmenu\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [mobileActive]=\"mobileActive\"\n                [autoDisplay]=\"autoDisplay\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [submenuIconTemplate]=\"submenuIconTemplate\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    encapsulation: ViewEncapsulation.None,\n    styleUrls: ['./menubar.css'],\n    host: {\n        class: 'p-element'\n    },\n    providers: [MenubarService]\n})\nexport class Menubar implements AfterContentInit, OnDestroy, OnInit {\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    @Input() set model(value: MenuItem[] | undefined) {\n        this._model = value;\n        this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model(): MenuItem[] | undefined {\n        return this._model;\n    }\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    @Input() style: { [klass: string]: any } | null | undefined;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    @Input() styleClass: string | undefined;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoZIndex: boolean = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) baseZIndex: number = 0;\n    /**\n     * Whether to show a root submenu on mouse over.\n     * @defaultValue true\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoDisplay: boolean | undefined = true;\n    /**\n     * Whether to hide a root submenu when mouse leaves.\n     * @group Props\n     */\n    @Input({ transform: booleanAttribute }) autoHide: boolean | undefined;\n    /**\n     * Delay to hide the root submenu in milliseconds when mouse leaves.\n     * @group Props\n     */\n    @Input({ transform: numberAttribute }) autoHideDelay: number = 100;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    @Input() id: string | undefined;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    @Input() ariaLabel: string | undefined;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    @Input() ariaLabelledBy: string | undefined;\n    /**\n     * Callback to execute when button is focused.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onFocus: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n    /**\n     * Callback to execute when button loses focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    @Output() onBlur: EventEmitter<FocusEvent> = new EventEmitter<FocusEvent>();\n\n    @ContentChildren(PrimeTemplate) templates: QueryList<PrimeTemplate> | undefined;\n\n    @ViewChild('menubutton') menubutton: ElementRef | undefined;\n\n    @ViewChild('rootmenu') rootmenu: MenubarSub | undefined;\n\n    startTemplate: TemplateRef<any> | undefined;\n\n    endTemplate: TemplateRef<any> | undefined;\n\n    menuIconTemplate: TemplateRef<any> | undefined;\n\n    submenuIconTemplate: TemplateRef<any> | undefined;\n\n    itemTemplate: TemplateRef<any> | undefined;\n\n    mobileActive: boolean | undefined;\n\n    outsideClickListener: VoidListener;\n\n    resizeListener: VoidListener;\n\n    mouseLeaveSubscriber: Subscription | undefined;\n\n    dirty: boolean = false;\n\n    focused: boolean = false;\n\n    activeItemPath = signal<any>([]);\n\n    number = signal<number>(0);\n\n    focusedItemInfo = signal<any>({ index: -1, level: 0, parentKey: '', item: null });\n\n    searchValue: string = '';\n\n    searchTimeout: any;\n\n    _processedItems: any[];\n\n    _model: MenuItem[] | undefined;\n\n    get visibleItems() {\n        const processedItem = this.activeItemPath().find((p) => p.key === this.focusedItemInfo().parentKey);\n\n        return processedItem ? processedItem.items : this.processedItems;\n    }\n\n    get processedItems() {\n        if (!this._processedItems || !this._processedItems.length) {\n            this._processedItems = this.createProcessedItems(this.model || []);\n        }\n        return this._processedItems;\n    }\n\n    get focusedItemId() {\n        const focusedItem = this.focusedItemInfo();\n        return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n    }\n\n    constructor(\n        @Inject(DOCUMENT) private document: Document,\n        @Inject(PLATFORM_ID) private platformId: any,\n        public el: ElementRef,\n        public renderer: Renderer2,\n        public cd: ChangeDetectorRef,\n        public config: PrimeNGConfig,\n        private menubarService: MenubarService\n    ) {\n        effect(() => {\n            const path = this.activeItemPath();\n\n            if (ObjectUtils.isNotEmpty(path)) {\n                this.bindOutsideClickListener();\n                this.bindResizeListener();\n            } else {\n                this.unbindOutsideClickListener();\n                this.unbindResizeListener();\n            }\n        });\n    }\n\n    ngOnInit(): void {\n        this.menubarService.autoHide = this.autoHide;\n        this.menubarService.autoHideDelay = this.autoHideDelay;\n        this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => this.unbindOutsideClickListener());\n        this.id = this.id || UniqueComponentId();\n    }\n\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n\n                case 'menuicon':\n                    this.menuIconTemplate = item.template;\n                    break;\n\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n\n    createProcessedItems(items: any, level: number = 0, parent: any = {}, parentKey: any = '') {\n        const processedItems = [];\n\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newItem = {\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n\n                newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n\n        return processedItems;\n    }\n\n    getItemProp(item: any, name: string) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n\n    menuButtonClick(event: MouseEvent) {\n        this.toggle(event);\n    }\n\n    menuButtonKeydown(event: any) {\n        (event.code === 'Enter' || event.code === 'Space') && this.menuButtonClick(event);\n    }\n\n    onItemClick(event: any) {\n        const { originalEvent, processedItem } = event;\n        const grouped = this.isProcessedItemGroup(processedItem);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        const selected = this.isSelected(processedItem);\n\n        if (selected) {\n            const { index, key, level, parentKey, item } = processedItem;\n\n            this.activeItemPath.set(this.activeItemPath().filter((p) => key !== p.key && key.startsWith(p.key)));\n            this.focusedItemInfo.set({ index, level, parentKey, item });\n\n            this.dirty = !root;\n            DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n        } else {\n            if (grouped) {\n                this.onItemChange(event);\n            } else {\n                const rootProcessedItem = root ? processedItem : this.activeItemPath().find((p) => p.parentKey === '');\n                this.hide(originalEvent);\n                this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n\n                this.mobileActive = false;\n                DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n            }\n        }\n    }\n\n    onItemMouseEnter(event: any) {\n        if (!DomHandler.isTouchDevice()) {\n            if (!this.mobileActive) {\n                this.onItemChange(event);\n            }\n        }\n    }\n\n    changeFocusedItemIndex(event: any, index: number) {\n        const processedItem = this.findVisibleItem(index);\n        if (this.focusedItemInfo().index !== index) {\n            const focusedItemInfo = this.focusedItemInfo();\n            this.focusedItemInfo.set({ ...focusedItemInfo, item: processedItem.item, index });\n            this.scrollInView();\n        }\n    }\n\n    scrollInView(index: number = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n        const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n\n    onItemChange(event: any) {\n        const { processedItem, isFocus } = event;\n\n        if (ObjectUtils.isEmpty(processedItem)) return;\n\n        const { index, key, level, parentKey, items, item } = processedItem;\n        const grouped = ObjectUtils.isNotEmpty(items);\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n\n        grouped && activeItemPath.push(processedItem);\n        this.focusedItemInfo.set({ index, level, parentKey, item });\n        this.activeItemPath.set(activeItemPath);\n\n        grouped && (this.dirty = true);\n        isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    }\n\n    toggle(event: MouseEvent) {\n        if (this.mobileActive) {\n            this.mobileActive = false;\n            ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n            this.hide();\n        } else {\n            this.mobileActive = true;\n            ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n            setTimeout(() => {\n                this.show();\n            }, 0);\n        }\n        this.cd.markForCheck();\n        this.bindOutsideClickListener();\n        event.preventDefault();\n    }\n\n    hide(event?, isFocus?: boolean) {\n        if (this.mobileActive) {\n            setTimeout(() => {\n                DomHandler.focus(this.menubutton.nativeElement);\n            }, 0);\n        }\n\n        this.activeItemPath.set([]);\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n\n        isFocus && DomHandler.focus(this.rootmenu?.menubarViewChild.nativeElement);\n        this.dirty = false;\n    }\n\n    show() {\n        const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n        this.focusedItemInfo.set({ index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '', item: processedItem?.item });\n        DomHandler.focus(this.rootmenu?.menubarViewChild.nativeElement);\n    }\n\n    onMenuFocus(event: any) {\n        this.focused = true;\n        const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n        const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : { index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '', item: processedItem?.item };\n\n        this.focusedItemInfo.set(focusedItemInfo);\n        this.onFocus.emit(event);\n    }\n\n    onMenuBlur(event: any) {\n        this.focused = false;\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        this.searchValue = '';\n        this.dirty = false;\n        this.onBlur.emit(event);\n    }\n\n    onKeyDown(event: KeyboardEvent) {\n        const metaKey = event.metaKey || event.ctrlKey;\n\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n\n            case 'End':\n                this.onEndKey(event);\n                break;\n\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n\n            case 'PageDown':\n            case 'PageUp':\n            case 'Backspace':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchItems(event, event.key);\n                }\n\n                break;\n        }\n    }\n\n    findVisibleItem(index) {\n        return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n    }\n\n    findFirstFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n\n        return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n\n    findFirstItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n    }\n\n    findSelectedItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n    }\n\n    isProcessedItemGroup(processedItem: any): boolean {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    isSelected(processedItem: any): boolean {\n        return this.activeItemPath().some((p) => p.key === processedItem.key);\n    }\n\n    isValidSelectedItem(processedItem: any): boolean {\n        return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n\n    isValidItem(processedItem: any): boolean {\n        return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n    }\n\n    isItemDisabled(item: any): boolean {\n        return this.getItemProp(item, 'disabled');\n    }\n\n    isItemSeparator(item: any): boolean {\n        return this.getItemProp(item, 'separator');\n    }\n\n    isItemMatched(processedItem: any): boolean {\n        return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n\n    isProccessedItemGroup(processedItem: any): boolean {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n\n    searchItems(event: any, char: string) {\n        this.searchValue = (this.searchValue || '') + char;\n\n        let itemIndex = -1;\n        let matched = false;\n\n        if (this.focusedItemInfo().index !== -1) {\n            itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem));\n            itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n        } else {\n            itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n        }\n\n        if (itemIndex !== -1) {\n            matched = true;\n        }\n\n        if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n            itemIndex = this.findFirstFocusedItemIndex();\n        }\n\n        if (itemIndex !== -1) {\n            this.changeFocusedItemIndex(event, itemIndex);\n        }\n\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n\n        return matched;\n    }\n\n    getProccessedItemLabel(processedItem: any) {\n        return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n\n    getItemLabel(item: any) {\n        return this.getItemProp(item, 'label');\n    }\n\n    onArrowDownKey(event: KeyboardEvent) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const root = processedItem ? ObjectUtils.isEmpty(processedItem.parent) : null;\n\n        if (root) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                this.onArrowRightKey(event);\n            }\n        } else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n\n    onArrowRightKey(event: KeyboardEvent) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = processedItem ? this.activeItemPath().find((p) => p.key === processedItem.parentKey) : null;\n\n        if (parentItem) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                this.onArrowDownKey(event);\n            }\n        } else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n\n    onArrowUpKey(event: KeyboardEvent) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n\n        if (root) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                const itemIndex = this.findLastItemIndex();\n\n                this.changeFocusedItemIndex(event, itemIndex);\n            }\n        } else {\n            const parentItem = this.activeItemPath().find((p) => p.key === processedItem.parentKey);\n            if (this.focusedItemInfo().index === 0) {\n                this.focusedItemInfo.set({ index: -1, parentKey: parentItem ? parentItem.parentKey : '', item: processedItem.item });\n                this.searchValue = '';\n                this.onArrowLeftKey(event);\n                const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n                this.activeItemPath.set(activeItemPath);\n            } else {\n                const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n                this.changeFocusedItemIndex(event, itemIndex);\n            }\n        }\n\n        event.preventDefault();\n    }\n\n    onArrowLeftKey(event: KeyboardEvent) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = processedItem ? this.activeItemPath().find((p) => p.key === processedItem.parentKey) : null;\n\n        if (parentItem) {\n            this.onItemChange({ originalEvent: event, processedItem: parentItem });\n            const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n            this.activeItemPath.set(activeItemPath);\n\n            event.preventDefault();\n        } else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n\n    onHomeKey(event: KeyboardEvent) {\n        this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n        event.preventDefault();\n    }\n\n    onEndKey(event: KeyboardEvent) {\n        this.changeFocusedItemIndex(event, this.findLastItemIndex());\n        event.preventDefault();\n    }\n\n    onSpaceKey(event: KeyboardEvent) {\n        this.onEnterKey(event);\n    }\n\n    onEscapeKey(event: KeyboardEvent) {\n        this.hide(event, true);\n        this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n\n        event.preventDefault();\n    }\n\n    onTabKey(event: KeyboardEvent) {\n        if (this.focusedItemInfo().index !== -1) {\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n\n            !grouped && this.onItemChange({ originalEvent: event, processedItem });\n        }\n\n        this.hide();\n    }\n\n    onEnterKey(event: KeyboardEvent) {\n        if (this.focusedItemInfo().index !== -1) {\n            const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n            const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n\n            anchorElement ? anchorElement.click() : element && element.click();\n        }\n\n        event.preventDefault();\n    }\n\n    findLastFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n\n    findLastItemIndex() {\n        return ObjectUtils.findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n    }\n\n    findPrevItemIndex(index: number) {\n        const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n\n        return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n\n    findNextItemIndex(index: number) {\n        const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n\n        return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    if (!DomHandler.isTouchDevice()) {\n                        this.hide(event, true);\n                    }\n\n                    this.mobileActive = false;\n                });\n            }\n        }\n    }\n\n    bindOutsideClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                    const isOutsideContainer = this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target);\n                    const isOutsideMenuButton = this.mobileActive && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target);\n\n                    if (isOutsideContainer) {\n                        isOutsideMenuButton ? (this.mobileActive = false) : this.hide();\n                    }\n                });\n            }\n        }\n    }\n\n    unbindOutsideClickListener() {\n        if (this.outsideClickListener) {\n            this.outsideClickListener();\n            this.outsideClickListener = null;\n        }\n    }\n\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n\n    ngOnDestroy() {\n        this.mouseLeaveSubscriber?.unsubscribe();\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n    }\n}\n\n@NgModule({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon],\n    exports: [Menubar, RouterModule, TooltipModule, SharedModule],\n    declarations: [Menubar, MenubarSub]\n})\nexport class MenubarModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public_api';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;MAyCa,cAAc,CAAA;AACvB,IAAA,QAAQ,CAAsB;AAE9B,IAAA,aAAa,CAAqB;AAEzB,IAAA,WAAW,GAAG,IAAI,OAAO,EAAW,CAAC;AAErC,IAAA,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CACvC,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAC5C,MAAM,CAAC,CAAC,SAAS,KAAM,IAAI,CAAC,QAAoB,IAAI,SAAS,CAAC,CACjE,CAAC;uGAVO,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;2GAAd,cAAc,EAAA,CAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,UAAU;;MA0KE,UAAU,CAAA;AA2CA,IAAA,EAAA,CAAA;AAAuB,IAAA,QAAA,CAAA;AAA6B,IAAA,EAAA,CAAA;AAA+B,IAAA,cAAA,CAAA;AA1C7F,IAAA,KAAK,CAAQ;AAEb,IAAA,YAAY,CAA0B;IAEP,IAAI,GAAY,KAAK,CAAC;IAEtB,UAAU,GAAY,IAAI,CAAC;IAE5B,UAAU,GAAW,CAAC,CAAC;AAEtB,IAAA,YAAY,CAAsB;AAElC,IAAA,WAAW,CAAsB;AAEhE,IAAA,MAAM,CAAqB;AAE3B,IAAA,SAAS,CAAqB;AAE9B,IAAA,cAAc,CAAqB;IAEL,KAAK,GAAW,CAAC,CAAC;AAEhD,IAAA,aAAa,CAAqB;AAElC,IAAA,cAAc,CAAQ;AAEtB,IAAA,mBAAmB,CAA+B;AAEjD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElD,IAAA,cAAc,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEvD,IAAA,SAAS,GAAsB,IAAI,YAAY,EAAE,CAAC;AAElD,IAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEjD,IAAA,WAAW,GAAsB,IAAI,YAAY,EAAE,CAAC;AAEtB,IAAA,gBAAgB,CAAa;AAErE,IAAA,oBAAoB,CAA2B;AAE/C,IAAA,WAAA,CAAmB,EAAc,EAAS,QAAmB,EAAU,EAAqB,EAAU,cAA8B,EAAA;QAAjH,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QAAU,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QAAU,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;KAAI;IAExI,QAAQ,GAAA;AACJ,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,MAAK;AACtE,YAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;AAC3B,SAAC,CAAC,CAAC;KACN;IAED,WAAW,CAAC,KAAU,EAAE,aAAkB,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/F,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;KAC/E;AAED,IAAA,WAAW,CAAC,aAAkB,EAAE,IAAY,EAAE,SAAqB,IAAI,EAAA;QACnE,OAAO,aAAa,IAAI,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC;KACvH;AAED,IAAA,SAAS,CAAC,aAAkB,EAAA;AACxB,QAAA,OAAO,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAI,CAAA,EAAA,aAAa,CAAC,GAAG,EAAE,CAAC;KACvH;AAED,IAAA,UAAU,CAAC,aAAkB,EAAA;AACzB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KACxC;AAED,IAAA,cAAc,CAAC,aAAkB,EAAA;QAC7B,OAAO,CAAA,EAAG,IAAI,CAAC,MAAM,IAAI,aAAa,CAAC,GAAG,CAAA,MAAA,CAAQ,CAAC;KACtD;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;AAC3C,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AAC/C,YAAA,mBAAmB,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AACrD,YAAA,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;AAC5C,YAAA,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;SACnD,CAAC;KACL;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;KACnD;AAED,IAAA,qBAAqB,CAAC,aAAkB,EAAA;QACpC,OAAO;AACH,YAAA,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC;AAC3C,YAAA,sBAAsB,EAAE,IAAI;SAC/B,CAAC;KACL;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK,CAAC;KAC/D;AAED,IAAA,YAAY,CAAC,aAAkB,EAAA;QAC3B,IAAI,IAAI,CAAC,cAAc,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;AAC7E,SAAA;KACJ;AAED,IAAA,cAAc,CAAC,aAAkB,EAAA;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KACtD;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;KAC/D;AAED,IAAA,WAAW,CAAC,aAAkB,EAAA;QAC1B,OAAO,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACtD;IAED,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC;KAC1I;AAED,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;KACrK;IAED,gBAAgB,GAAA;QACZ,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC9C;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;QACvB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5C,YAAA,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACvC,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AACrE,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,oBAAoB,EAAE,WAAW,EAAE,CAAC;KAC5C;uGA1IQ,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAV,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAU,EAKC,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,gBAAgB,CAEhB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,gBAAgB,CAEhB,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAAA,eAAe,CAEf,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAAA,gBAAgB,CAEhB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CAQhB,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,eAAe,CA/KzB,EAAA,aAAA,EAAA,eAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,QAAA,EAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJT,IAAA,CAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,aAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,uBAAA,EAAA,kBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,eAAA,EAAA,QAAA,EAAA,WAAA,EAAA,WAAA,EAAA,MAAA,EAAA,aAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,gBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MA85B0F,aAAa,CAAA,EAAA,QAAA,EAAA,eAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAE,cAAc,CAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAx5B/G,UAAU,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,cAAA,EAAA,MAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,QAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,qBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBA5JtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJT,IAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,oBAAA,IAAI,EAAE;AACF,wBAAA,KAAK,EAAE,WAAW;AACrB,qBAAA;AACJ,iBAAA,CAAA;iKAEY,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBAEkC,IAAI,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAEG,YAAY,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAEE,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAE7B,MAAM,EAAA,CAAA;sBAAd,KAAK;gBAEG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEiC,KAAK,EAAA,CAAA;sBAA3C,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAE5B,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAEG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAEG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBAEI,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAEG,SAAS,EAAA,CAAA;sBAAlB,MAAM;gBAEG,QAAQ,EAAA,CAAA;sBAAjB,MAAM;gBAEG,WAAW,EAAA,CAAA;sBAApB,MAAM;gBAEiC,gBAAgB,EAAA,CAAA;sBAAvD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAA;;AAqG1C;;;AAGG;MAgEU,OAAO,CAAA;AAyIc,IAAA,QAAA,CAAA;AACG,IAAA,UAAA,CAAA;AACtB,IAAA,EAAA,CAAA;AACA,IAAA,QAAA,CAAA;AACA,IAAA,EAAA,CAAA;AACA,IAAA,MAAA,CAAA;AACC,IAAA,cAAA,CAAA;AA9IZ;;;AAGG;IACH,IAAa,KAAK,CAAC,KAA6B,EAAA;AAC5C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;KACvE;AACD,IAAA,IAAI,KAAK,GAAA;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AACD;;;AAGG;AACM,IAAA,KAAK,CAA8C;AAC5D;;;AAGG;AACM,IAAA,UAAU,CAAqB;AACxC;;;AAGG;IACqC,UAAU,GAAY,IAAI,CAAC;AACnE;;;AAGG;IACoC,UAAU,GAAW,CAAC,CAAC;AAC9D;;;;AAIG;IACqC,WAAW,GAAwB,IAAI,CAAC;AAChF;;;AAGG;AACqC,IAAA,QAAQ,CAAsB;AACtE;;;AAGG;IACoC,aAAa,GAAW,GAAG,CAAC;AACnE;;;AAGG;AACM,IAAA,EAAE,CAAqB;AAChC;;;AAGG;AACM,IAAA,SAAS,CAAqB;AACvC;;;AAGG;AACM,IAAA,cAAc,CAAqB;AAC5C;;;;AAIG;AACO,IAAA,OAAO,GAA6B,IAAI,YAAY,EAAc,CAAC;AAC7E;;;;AAIG;AACO,IAAA,MAAM,GAA6B,IAAI,YAAY,EAAc,CAAC;AAE5C,IAAA,SAAS,CAAuC;AAEvD,IAAA,UAAU,CAAyB;AAErC,IAAA,QAAQ,CAAyB;AAExD,IAAA,aAAa,CAA+B;AAE5C,IAAA,WAAW,CAA+B;AAE1C,IAAA,gBAAgB,CAA+B;AAE/C,IAAA,mBAAmB,CAA+B;AAElD,IAAA,YAAY,CAA+B;AAE3C,IAAA,YAAY,CAAsB;AAElC,IAAA,oBAAoB,CAAe;AAEnC,IAAA,cAAc,CAAe;AAE7B,IAAA,oBAAoB,CAA2B;IAE/C,KAAK,GAAY,KAAK,CAAC;IAEvB,OAAO,GAAY,KAAK,CAAC;AAEzB,IAAA,cAAc,GAAG,MAAM,CAAM,EAAE,CAAC,CAAC;AAEjC,IAAA,MAAM,GAAG,MAAM,CAAS,CAAC,CAAC,CAAC;IAE3B,eAAe,GAAG,MAAM,CAAM,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAElF,WAAW,GAAW,EAAE,CAAC;AAEzB,IAAA,aAAa,CAAM;AAEnB,IAAA,eAAe,CAAQ;AAEvB,IAAA,MAAM,CAAyB;AAE/B,IAAA,IAAI,YAAY,GAAA;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC;AAEpG,QAAA,OAAO,aAAa,GAAG,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;KACpE;AAED,IAAA,IAAI,cAAc,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AACvD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;AACtE,SAAA;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;KAC/B;AAED,IAAA,IAAI,aAAa,GAAA;AACb,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3C,QAAA,OAAO,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAG,EAAA,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC,SAAS,GAAG,EAAE,CAAI,CAAA,EAAA,WAAW,CAAC,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC;KAClO;AAED,IAAA,WAAA,CAC8B,QAAkB,EACf,UAAe,EACrC,EAAc,EACd,QAAmB,EACnB,EAAqB,EACrB,MAAqB,EACpB,cAA8B,EAAA;QANZ,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAK;QACrC,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;QACnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAmB;QACrB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QACpB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;QAEtC,MAAM,CAAC,MAAK;AACR,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAEnC,YAAA,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7B,aAAA;AAAM,iBAAA;gBACH,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC/B,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,QAAQ,GAAA;QACJ,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;AACvD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC,CAAC;QAC9G,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,iBAAiB,EAAE,CAAC;KAC5C;IAED,kBAAkB,GAAA;QACd,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;AAC7B,YAAA,QAAQ,IAAI,CAAC,OAAO,EAAE;AAClB,gBAAA,KAAK,OAAO;AACR,oBAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACnC,MAAM;AAEV,gBAAA,KAAK,KAAK;AACN,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACjC,MAAM;AAEV,gBAAA,KAAK,UAAU;AACX,oBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACtC,MAAM;AAEV,gBAAA,KAAK,aAAa;AACd,oBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC;oBACzC,MAAM;AAEV,gBAAA,KAAK,MAAM;AACP,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AAEV,gBAAA;AACI,oBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,MAAM;AACb,aAAA;AACL,SAAC,CAAC,CAAC;KACN;IAED,oBAAoB,CAAC,KAAU,EAAE,KAAgB,GAAA,CAAC,EAAE,MAAc,GAAA,EAAE,EAAE,SAAA,GAAiB,EAAE,EAAA;QACrF,MAAM,cAAc,GAAG,EAAE,CAAC;QAE1B,KAAK;YACD,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AAC1B,gBAAA,MAAM,GAAG,GAAG,CAAC,SAAS,KAAK,EAAE,GAAG,SAAS,GAAG,GAAG,GAAG,EAAE,IAAI,KAAK,CAAC;AAC9D,gBAAA,MAAM,OAAO,GAAG;oBACZ,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,GAAG;oBACH,MAAM;oBACN,SAAS;iBACZ,CAAC;gBAEF,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AAClF,gBAAA,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;AAEP,QAAA,OAAO,cAAc,CAAC;KACzB;IAED,WAAW,CAAC,IAAS,EAAE,IAAY,EAAA;AAC/B,QAAA,OAAO,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC;KAClE;AAED,IAAA,eAAe,CAAC,KAAiB,EAAA;AAC7B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACtB;AAED,IAAA,iBAAiB,CAAC,KAAU,EAAA;AACxB,QAAA,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;KACrF;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;AAClB,QAAA,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QACzD,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AAEhD,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;AAE7D,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrG,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAE5D,YAAA,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC;YACnB,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAClE,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,OAAO,EAAE;AACT,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5B,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,iBAAiB,GAAG,IAAI,GAAG,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,EAAE,CAAC,CAAC;AACvG,gBAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACzB,gBAAA,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAE7F,gBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;gBAC1B,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAClE,aAAA;AACJ,SAAA;KACJ;AAED,IAAA,gBAAgB,CAAC,KAAU,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;AAC7B,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACpB,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5B,aAAA;AACJ,SAAA;KACJ;IAED,sBAAsB,CAAC,KAAU,EAAE,KAAa,EAAA;QAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,KAAK,EAAE;AACxC,YAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC/C,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAClF,IAAI,CAAC,YAAY,EAAE,CAAC;AACvB,SAAA;KACJ;IAED,YAAY,CAAC,KAAgB,GAAA,CAAC,CAAC,EAAA;QAC3B,MAAM,EAAE,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,CAAG,EAAA,IAAI,CAAC,EAAE,CAAA,CAAA,EAAI,KAAK,CAAE,CAAA,GAAG,IAAI,CAAC,aAAa,CAAC;AACrE,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AAExF,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7F,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAU,EAAA;AACnB,QAAA,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;AAEzC,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC;YAAE,OAAO;AAE/C,QAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC;QACpE,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,CAAC,SAAS,KAAK,GAAG,CAAC,CAAC;AAE7G,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAExC,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAC/B,QAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;KAC7E;AAED,IAAA,MAAM,CAAC,KAAiB,EAAA;QACpB,IAAI,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;YAClD,IAAI,CAAC,IAAI,EAAE,CAAC;AACf,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACjF,UAAU,CAAC,MAAK;gBACZ,IAAI,CAAC,IAAI,EAAE,CAAC;aACf,EAAE,CAAC,CAAC,CAAC;AACT,SAAA;AACD,QAAA,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;QACvB,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,IAAI,CAAC,KAAM,EAAE,OAAiB,EAAA;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,UAAU,CAAC,MAAK;gBACZ,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;aACnD,EAAE,CAAC,CAAC,CAAC;AACT,SAAA;AAED,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAE7E,QAAA,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;IAED,IAAI,GAAA;QACA,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;AAC7E,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;QAC1H,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,aAAa,CAAC,CAAC;KACnE;AAED,IAAA,WAAW,CAAC,KAAU,EAAA;AAClB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;AAC7E,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;AAEvL,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED,IAAA,UAAU,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7E,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QAE/C,QAAQ,KAAK,CAAC,IAAI;AACd,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,SAAS;AACV,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzB,MAAM;AAEV,YAAA,KAAK,WAAW;AACZ,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM;AAEV,YAAA,KAAK,YAAY;AACb,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5B,MAAM;AAEV,YAAA,KAAK,MAAM;AACP,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,OAAO;AACR,gBAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACvB,MAAM;AAEV,YAAA,KAAK,QAAQ;AACT,gBAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACxB,MAAM;AAEV,YAAA,KAAK,KAAK;AACN,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM;AAEV,YAAA,KAAK,UAAU,CAAC;AAChB,YAAA,KAAK,QAAQ,CAAC;AACd,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,WAAW,CAAC;AACjB,YAAA,KAAK,YAAY;;gBAEb,MAAM;AAEV,YAAA;gBACI,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,iBAAA;gBAED,MAAM;AACb,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAK,EAAA;QACjB,OAAO,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KACtF;IAED,yBAAyB,GAAA;AACrB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAEnD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,aAAa,CAAC;KACxE;IAED,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC1F;IAED,qBAAqB,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC;KAClG;AAED,IAAA,oBAAoB,CAAC,aAAkB,EAAA;QACnC,OAAO,aAAa,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvE;AAED,IAAA,UAAU,CAAC,aAAkB,EAAA;QACzB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,CAAC,CAAC;KACzE;AAED,IAAA,mBAAmB,CAAC,aAAkB,EAAA;AAClC,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KAC5E;AAED,IAAA,WAAW,CAAC,aAAkB,EAAA;QAC1B,OAAO,CAAC,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KACnH;AAED,IAAA,cAAc,CAAC,IAAS,EAAA;QACpB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC7C;AAED,IAAA,eAAe,CAAC,IAAS,EAAA;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;KAC9C;AAED,IAAA,aAAa,CAAC,aAAkB,EAAA;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC,iBAAiB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,CAAC;KAC7J;AAED,IAAA,qBAAqB,CAAC,aAAkB,EAAA;QACpC,OAAO,aAAa,IAAI,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACvE;IAED,WAAW,CAAC,KAAU,EAAE,IAAY,EAAA;AAChC,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC;AAEnD,QAAA,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;QACnB,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;YAClI,SAAS,GAAG,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC;AACtM,SAAA;AAAM,aAAA;AACH,YAAA,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;AACjG,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;YAClB,OAAO,GAAG,IAAI,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACzD,YAAA,SAAS,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAChD,SAAA;AAED,QAAA,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;AAClB,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACjD,SAAA;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;AACpB,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACpC,SAAA;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,MAAK;AACjC,YAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B,EAAE,GAAG,CAAC,CAAC;AAER,QAAA,OAAO,OAAO,CAAC;KAClB;AAED,IAAA,sBAAsB,CAAC,aAAkB,EAAA;AACrC,QAAA,OAAO,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;KAC5E;AAED,IAAA,YAAY,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC1C;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;AACtE,QAAA,MAAM,IAAI,GAAG,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAE9E,QAAA,IAAI,IAAI,EAAE;YACN,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,YAAA,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;gBAC3D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAChG,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC/B,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAEhJ,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC9C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,eAAe,CAAC,KAAoB,EAAA;AAChC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;AACtE,QAAA,MAAM,UAAU,GAAG,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AAE/G,QAAA,IAAI,UAAU,EAAE;YACZ,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,YAAA,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;gBAC3D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAChG,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9B,aAAA;AACJ,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAEhJ,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC9C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,YAAY,CAAC,KAAoB,EAAA;AAC7B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;QACtE,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAEvD,QAAA,IAAI,IAAI,EAAE;YACN,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,YAAA,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;gBAC3D,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AAChG,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAE3C,gBAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,CAAC,SAAS,CAAC,CAAC;YACxF,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE;AACpC,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,UAAU,GAAG,UAAU,CAAC,SAAS,GAAG,EAAE,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;AACrH,gBAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC;AAC7G,gBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC3C,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAC/I,gBAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AACjD,aAAA;AACJ,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;AAC/B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;AACtE,QAAA,MAAM,UAAU,GAAG,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,aAAa,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AAE/G,QAAA,IAAI,UAAU,EAAE;AACZ,YAAA,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAC;YACvE,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC;AAC7G,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAExC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAC/I,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC9C,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1B,SAAA;KACJ;AAED,IAAA,SAAS,CAAC,KAAoB,EAAA;QAC1B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC9D,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAC7D,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC1B;AAED,IAAA,WAAW,CAAC,KAAoB,EAAA;AAC5B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEhE,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;AAED,IAAA,QAAQ,CAAC,KAAoB,EAAA;QACzB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;AACrC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAE1D,YAAA,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AAC1E,SAAA;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;KACf;AAED,IAAA,UAAU,CAAC,KAAoB,EAAA;QAC3B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;YACrC,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,CAAG,EAAA,IAAI,CAAC,aAAa,CAAA,CAAE,CAAI,EAAA,CAAA,CAAC,CAAC;AAC7G,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAC;AAE/F,YAAA,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;AACtE,SAAA;QAED,KAAK,CAAC,cAAc,EAAE,CAAC;KAC1B;IAED,wBAAwB,GAAA;AACpB,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AACnD,QAAA,OAAO,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,aAAa,CAAC;KACvE;IAED,iBAAiB,GAAA;QACb,OAAO,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;KAC3G;AAED,IAAA,iBAAiB,CAAC,KAAa,EAAA;AAC3B,QAAA,MAAM,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE3J,QAAA,OAAO,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,GAAG,KAAK,CAAC;KAC3D;AAED,IAAA,iBAAiB,CAAC,KAAa,EAAA;QAC3B,MAAM,gBAAgB,GAAG,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAEtK,QAAA,OAAO,gBAAgB,GAAG,CAAC,CAAC,GAAG,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;KACvE;IAED,kBAAkB,GAAA;AACd,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC,KAAK,KAAI;AACtF,oBAAA,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;AAC7B,wBAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1B,qBAAA;AAED,oBAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC9B,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,wBAAwB,GAAA;AACpB,QAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpC,YAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,gBAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,KAAI;AAC/E,oBAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACrI,oBAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAEzJ,oBAAA,IAAI,kBAAkB,EAAE;AACpB,wBAAA,mBAAmB,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AACnE,qBAAA;AACL,iBAAC,CAAC,CAAC;AACN,aAAA;AACJ,SAAA;KACJ;IAED,0BAA0B,GAAA;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACpC,SAAA;KACJ;IAED,oBAAoB,GAAA;QAChB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC9B,SAAA;KACJ;IAED,WAAW,GAAA;AACP,QAAA,IAAI,CAAC,oBAAoB,EAAE,WAAW,EAAE,CAAC;QACzC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC/B;uGArsBQ,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAyIJ,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,cAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AA1Id,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,sIA0BI,gBAAgB,CAAA,EAAA,UAAA,EAAA,CAAA,YAAA,EAAA,YAAA,EAKhB,eAAe,CAAA,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAMf,gBAAgB,CAKhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKhB,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAAA,eAAe,8KAjDxB,CAAC,cAAc,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,SAAA,EA8EV,aAAa,EAzIpB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,YAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,UAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoDT,EAktBgF,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,gkCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAA,QAAQ,0EAx5BhF,UAAU,CAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,cAAA,EAAA,MAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,QAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,qBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,WAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,UAAA,EAAA,aAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;2FA+MV,OAAO,EAAA,UAAA,EAAA,CAAA;kBA/DnB,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,WAAW,EACX,QAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDT,IAAA,CAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAE/B,IAAA,EAAA;AACF,wBAAA,KAAK,EAAE,WAAW;qBACrB,EACU,SAAA,EAAA,CAAC,cAAc,CAAC,EAAA,MAAA,EAAA,CAAA,gkCAAA,CAAA,EAAA,CAAA;;0BA2ItB,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;gLArIV,KAAK,EAAA,CAAA;sBAAjB,KAAK;gBAWG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAKG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBAKkC,UAAU,EAAA,CAAA;sBAAjD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,UAAU,EAAA,CAAA;sBAAhD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAMG,WAAW,EAAA,CAAA;sBAAlD,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKE,QAAQ,EAAA,CAAA;sBAA/C,KAAK;uBAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAA;gBAKC,aAAa,EAAA,CAAA;sBAAnD,KAAK;uBAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAA;gBAK5B,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAKG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBAKG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBAMI,OAAO,EAAA,CAAA;sBAAhB,MAAM;gBAMG,MAAM,EAAA,CAAA;sBAAf,MAAM;gBAEyB,SAAS,EAAA,CAAA;sBAAxC,eAAe;uBAAC,aAAa,CAAA;gBAEL,UAAU,EAAA,CAAA;sBAAlC,SAAS;uBAAC,YAAY,CAAA;gBAEA,QAAQ,EAAA,CAAA;sBAA9B,SAAS;uBAAC,UAAU,CAAA;;MA6nBZ,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;wGAAb,aAAa,EAAA,YAAA,EAAA,CA7sBb,OAAO,EA/MP,UAAU,CAAA,EAAA,OAAA,EAAA,CAw5BT,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,CAAA,EAAA,OAAA,EAAA,CAzsB/G,OAAO,EA0sBG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;wGAGnD,aAAa,EAAA,OAAA,EAAA,CAJZ,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,EACrG,YAAY,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA,CAAA;;2FAGnD,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACN,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,CAAC;oBACzH,OAAO,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;AAC7D,oBAAA,YAAY,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;AACtC,iBAAA,CAAA;;;AC7mCD;;AAEG;;;;"}