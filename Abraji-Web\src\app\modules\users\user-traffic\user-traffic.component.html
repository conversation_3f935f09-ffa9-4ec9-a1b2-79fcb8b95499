<div class="container mx-auto p-4">
  <h2 class="text-2xl font-semibold mb-4">{{ 'user.traffic.traffic' | transloco }}</h2>
  <div class="bg-white shadow-md rounded-lg p-6">
    <div>
      <div class="flex gap-8 form-group mb-4">
        <select title="Daily" [(ngModel)]="requestForm.report_type" name="type" class="block py-2.5 px-0 w-full text-sm text-gray-500 bg-transparent border-0 border-b-2 border-gray-200 appearance-none dark:text-gray-400 dark:border-gray-700 focus:outline-none focus:ring-0 focus:border-gray-200 peer">
          <option value="daily">{{ 'user.traffic.daily' | transloco }}</option>
          <option value="monthly">{{ 'user.traffic.monthly' | transloco }}</option>
        </select>
        <select title="month" [(ngModel)]="requestForm.month" name="month" class="block py-2.5 px-0 w-full text-sm text-gray-500 bg-transparent border-0 border-b-2 border-gray-200 appearance-none dark:text-gray-400 dark:border-gray-700 focus:outline-none focus:ring-0 focus:border-gray-200 peer">
          <option *ngFor="let month of months" [value]="month.value">{{ ('date.months.' + month.name) | transloco }}</option>
        </select>
        <select title="year" [(ngModel)]="requestForm.year" name="year" class="block py-2.5 px-0 w-full text-sm text-gray-500 bg-transparent border-0 border-b-2 border-gray-200 appearance-none dark:text-gray-400 dark:border-gray-700 focus:outline-none focus:ring-0 focus:border-gray-200 peer">
          <option *ngFor="let year of years" [value]="year">{{ year }}</option>
        </select>
        <button title="refresh" class="btn btn-default" (click)="refreshData()">
          <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.651 7.65a7.131 7.131 0 0 0-12.68 3.15M18.001 4v4h-4m-7.652 8.35a7.13 7.13 0 0 0 12.68-3.15M6 20v-4h4"/>
          </svg>
                  </button>
      </div>
      <div *ngIf="isLoading; else chartContent" class="skeleton-wrapper">
        <div class="skeleton skeleton-text"></div>
        <div class="skeleton skeleton-chart"></div>
      </div>
      <ng-template #chartContent>
        <div *ngIf="!isLoading" [chart]="chart"></div>
      </ng-template>
    </div>
  </div>

  <div class="relative bg-white overflow-x-auto shadow-lg sm:rounded-lg my-10">
    <!-- Data table -->
    <table *ngIf="!isLoading" class="w-full text-sm text-start text-gray-500 dark:text-gray-400">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
          <th scope="col" class="px-6 py-3">{{ ('user.traffic.day') | transloco }}</th>
          <th scope="col" class="px-6 py-3" *ngFor="let column of tableColumns">
            <div class="flex items-center cursor-pointer">
              <span>{{ ('user.traffic.' + column) | transloco }}</span>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let day of getDays(); let i = index" class="hover:bg-gray-100 cursor-pointer">
          <td class="px-6 py-3">{{ day }}</td>
          <td class="px-6 py-3" *ngFor="let column of tableColumns">{{ getColumnData(column, i) | bytesToSize }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
