import { inject, Injectable } from '@angular/core';
import { HttpService } from '../../common-services/services/http.service';
import { catchError, Observable, of, shareReplay } from 'rxjs';
import { Expense } from './expenses';

@Injectable({
  providedIn: 'root',
})
export class ExpensesApi {
  private apiUrl = 'transactions';
  private httpService = inject(HttpService);
  private expenses = new Map<string, Observable<Expense>>();

  getExpenses(requestForm: any) {
    const url = this.apiUrl + '/all';
    return this.httpService.post(url, requestForm);
  }

  createExpense(expense: any) {
    const url = this.apiUrl;
    return this.httpService.post(url, expense);
  }
}
