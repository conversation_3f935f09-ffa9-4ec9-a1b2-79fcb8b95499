import { Component, inject } from '@angular/core';
import { LocalStorageService } from '../../../core/auth-services/services/local-storage.service';
import { TableResponse } from '../../../core/common-services/interfaces/table-response';
import { TableElementsService } from '../../../core/common-services/services/table-elements.service';
import { UserForm, User, UserSession } from '../../../core/user-services/api/users';
import { UsersService } from '../../../core/user-services/services/users.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-user-sessions',
  templateUrl: './user-sessions.component.html',
  styleUrl: './user-sessions.component.scss'
})
export class UserSessionsComponent {
  protected userService = inject(UsersService);
  protected tableElementsService = inject(TableElementsService);
  private route = inject(ActivatedRoute);
  private userId: string | null = null;
  userForm!: UserForm;
  isLoading: boolean = false;
  tableResponse: TableResponse<UserSession> = {
    current_page: 1,
    data: [],
    total: 0,
    last_page: 0,
    per_page: 0,
    to: 0,
    from: 0,
  };
  tableColumns: {key: string, value: string}[] = [
    {key: "acctstarttime", value: "Start Time"},
    {key: "acctstoptime", value: "Stop Time"},
    {key: "framedipaddress", value: "IP Address"},
    {key: "acctinputoctets", value: "Download"},
    {key: "acctoutputoctets", value: "Upload"},
    {key: "callingstationid", value: "MAC Address"},
    {key: 'calledstationid', value: 'Service'},
    {key: 'nasipaddress', value: 'NAS IP'},
    {key: 'acctterminatecause', value: 'Termination Cause'},
  ];

  sortByColumn(key: string): void {
    this.tableElementsService.sortByColumn(key, this.userForm);
    // fetch data
    this.fetchSession();
  }

  // Get pages that shown in pagination
  getPagesToDisplay(): (number | string)[] {
    return this.tableElementsService.getPagesToDisplay(this.tableResponse.last_page, this.userForm.page);
  }

  // Bind the change page in pagination to the form
  changePage(page: (string | number)): void {
    const pageNumber = parseInt(page.toString(), 10);

    if (!isNaN(pageNumber)) {
      this.userForm.page = pageNumber;
      this.fetchSession();
    }
  }

  ngOnInit(): void {
    this.userForm = {
      page: 1,
      count: 10,
      sortBy: "",
      direction: "",
      search: "",
      columns: this.getVisibleColumns(),
    };

       // Access parent route to get the id parameter
        this.route.parent?.paramMap.subscribe(params => {
        this.userId = params.get('id');

        if (this.userId) {
          this.fetchSession();
        }
      });
  }

  getVisibleColumns(): string[] {
    return this.tableColumns.map(column => column.key);
  }

  fetchSession(): void {
    console.log(this.userForm);

    this.isLoading = true;
    this.userService.getUserSessions( this.userId ?? '' ,this.userForm).subscribe({
      next: (response: TableResponse<UserSession>) => {
        this.tableResponse = response;
        console.log(response);

      },
      complete: () => {
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        console.error(error);
      },
    });
  }

  getPropertyValue(user: User, key: string): any {
    return this.tableElementsService.getUserPropertyValue(user, key);
  }

  mapPropertyName(key: string): any {
    return this.tableElementsService.mapUserPropertyName(key);
  }

}
