/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { FactoryTarget, getCompilerFacade, } from '../../compiler/compiler_facade';
import { setClassMetadata, setClassMetadataAsync } from '../metadata';
import { angularCoreEnv } from './environment';
/**
 * Compiles a partial directive declaration object into a full directive definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareDirective(decl) {
    const compiler = getCompilerFacade({
        usage: 1 /* JitCompilerUsage.PartialDeclaration */,
        kind: 'directive',
        type: decl.type,
    });
    return compiler.compileDirectiveDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵfac.js`, decl);
}
/**
 * Evaluates the class metadata declaration.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareClassMetadata(decl) {
    setClassMetadata(decl.type, decl.decorators, decl.ctorParameters ?? null, decl.propDecorators ?? null);
}
/**
 * Evaluates the class metadata of a component that contains deferred blocks.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareClassMetadataAsync(decl) {
    setClassMetadataAsync(decl.type, decl.resolveDeferredDeps, (...types) => {
        const meta = decl.resolveMetadata(...types);
        setClassMetadata(decl.type, meta.decorators, meta.ctorParameters, meta.propDecorators);
    });
}
/**
 * Compiles a partial component declaration object into a full component definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareComponent(decl) {
    const compiler = getCompilerFacade({
        usage: 1 /* JitCompilerUsage.PartialDeclaration */,
        kind: 'component',
        type: decl.type,
    });
    return compiler.compileComponentDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵcmp.js`, decl);
}
/**
 * Compiles a partial pipe declaration object into a full pipe definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareFactory(decl) {
    const compiler = getCompilerFacade({
        usage: 1 /* JitCompilerUsage.PartialDeclaration */,
        kind: getFactoryKind(decl.target),
        type: decl.type,
    });
    return compiler.compileFactoryDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵfac.js`, decl);
}
function getFactoryKind(target) {
    switch (target) {
        case FactoryTarget.Directive:
            return 'directive';
        case FactoryTarget.Component:
            return 'component';
        case FactoryTarget.Injectable:
            return 'injectable';
        case FactoryTarget.Pipe:
            return 'pipe';
        case FactoryTarget.NgModule:
            return 'NgModule';
    }
}
/**
 * Compiles a partial injectable declaration object into a full injectable definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareInjectable(decl) {
    const compiler = getCompilerFacade({
        usage: 1 /* JitCompilerUsage.PartialDeclaration */,
        kind: 'injectable',
        type: decl.type,
    });
    return compiler.compileInjectableDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵprov.js`, decl);
}
/**
 * These enums are used in the partial factory declaration calls.
 */
export { FactoryTarget } from '../../compiler/compiler_facade';
/**
 * Compiles a partial injector declaration object into a full injector definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareInjector(decl) {
    const compiler = getCompilerFacade({
        usage: 1 /* JitCompilerUsage.PartialDeclaration */,
        kind: 'NgModule',
        type: decl.type,
    });
    return compiler.compileInjectorDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵinj.js`, decl);
}
/**
 * Compiles a partial NgModule declaration object into a full NgModule definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareNgModule(decl) {
    const compiler = getCompilerFacade({
        usage: 1 /* JitCompilerUsage.PartialDeclaration */,
        kind: 'NgModule',
        type: decl.type,
    });
    return compiler.compileNgModuleDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵmod.js`, decl);
}
/**
 * Compiles a partial pipe declaration object into a full pipe definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclarePipe(decl) {
    const compiler = getCompilerFacade({
        usage: 1 /* JitCompilerUsage.PartialDeclaration */,
        kind: 'pipe',
        type: decl.type,
    });
    return compiler.compilePipeDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵpipe.js`, decl);
}
//# sourceMappingURL=data:application/json;base64,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